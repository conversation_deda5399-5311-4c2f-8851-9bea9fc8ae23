package com.ideal.service.domain;

import com.ideal.common.utils.Tools;
import com.ideal.ieai.core.JudgeDB;
import com.ideal.ieai.server.domain.DomainManagementManage;
import com.ideal.ieai.server.domain.DomainManagementModel;
import com.ideal.ieai.server.repository.DBUtil;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.idgenerator.IdGenerator;
import com.ideal.ieai.server.util.BeanFormatter;
import net.sf.json.JSONArray;
import org.apache.log4j.Logger;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * @ClassName:  DomainManagementService
 * @Description: 域管理服务层
 * @author: system
 * @date:   2024年12月19日
 *
 * @Copyright: 2024-2027 www.idealinfo.com Inc. All rights reserved.
 *
 */
public class DomainManagementService
{
    private Logger log = Logger.getLogger(DomainManagementService.class);
    private static final String SUCCESS = "success";
    private static final String SAVEDOMAINMANAGEMENT = "saveDomainManagement";
    private static final String ROLLBACKSQLS = "rollbackSqls";
    private static final String MESSAGE = "message";
    private static final String EXESQLS = "exeSqls";
    private static final String DELETEDOMAINMANAGEMENT = "deleteDomainManagement";
    
    public Map getDomainManagementList ( Integer start, Integer limit, String iname, String idesc, int sysType )
    {
        Connection conn = null;
        DomainManagementManage fcm = new DomainManagementManage();
        String sqlWhere = "";
        if (iname != null && !"".equals(iname))
        {
            sqlWhere = sqlWhere + " and a.INAME like '%"+iname+"%'";
        }
        if (idesc != null && !"".equals(idesc))
        {
            sqlWhere = sqlWhere + " and a.IDESC like '%"+idesc+"%'";
        }
        String sqlList = "";
        if (JudgeDB.IEAI_DB_TYPE == 3)
        {
            sqlList = "select a.iid,a.iname,a.idesc,a.icreatorid,a.icreatorname,a.icreatetime from ieai_domain_management a where 1=1 "+sqlWhere+" order by a.IID LIMIT ?,?";
        } else
        {
            sqlList = "select * from (select row_number() over (order by a.iid desc) AS ro,a.iid,a.iname,a.idesc,a.icreatorid,a.icreatorname,a.icreatetime from ieai_domain_management a where 1=1 "+sqlWhere+") where ro>? and ro<=?";
        }
        String sqlCount = "select count(a.iid) as countNum from ieai_domain_management a where 1=1 "+sqlWhere;
        Map map = new HashMap();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            List list = fcm.getDomainManagementList(conn, sqlList, start, limit);
            int count = fcm.getDomainManagementCount(conn, sqlCount);
            map.put("dataList", list);
            map.put("total", count);
        } catch (Exception e)
        {
            log.error("DomainManagementService.getDomainManagementList is error", e);
        } finally
        {
            DBResource.closeConnection(conn, "getDomainManagementList", log);
        }
        return map;
    }
    
    private List jsonTranslateDomainManagement ( String jsonData, String userName, long userId ) throws ParseException {
        List dataList = new ArrayList();
        long  sysDate=  System.currentTimeMillis();
        DomainManagementManage manage = new DomainManagementManage();
        if (!"".equals(jsonData) && !"null".equals(jsonData) && null != jsonData)
        {
            JSONArray jsonArr = JSONArray.fromObject(jsonData);
            for (int i = 0; i < jsonArr.size(); i++)
            {
                DomainManagementModel model = new DomainManagementModel();
                model.setIid(jsonArr.getJSONObject(i).optString("iid")==null||"".equals(jsonArr.getJSONObject(i).optString("iid"))?0l:Long.valueOf(jsonArr.getJSONObject(i).optString("iid")));
                model.setIname(jsonArr.getJSONObject(i).optString("iname")==null||"".equals(jsonArr.getJSONObject(i).optString("iname"))?"":jsonArr.getJSONObject(i).optString("iname"));
                model.setIdesc(jsonArr.getJSONObject(i).optString("idesc")==null||"".equals(jsonArr.getJSONObject(i).optString("idesc"))?"":jsonArr.getJSONObject(i).optString("idesc"));
                if(model.getIid() == 0L)
                {
                    model.setIcreatorid(userId);
                    model.setIcreatorname(userName);
                    model.setIcreatetime(String.valueOf(sysDate));
                }else
                {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    long createtime = sdf.parse(jsonArr.getJSONObject(i).optString("icreatetime")).getTime();
                    model.setIcreatorid(Long.valueOf(jsonArr.getJSONObject(i).optString("icreatorid")==null||"".equals(jsonArr.getJSONObject(i).optString("icreatorid"))?"0":jsonArr.getJSONObject(i).optString("icreatorid")));
                    model.setIcreatorname(jsonArr.getJSONObject(i).optString("icreatorname")==null||"".equals(jsonArr.getJSONObject(i).optString("icreatorname"))?"":jsonArr.getJSONObject(i).optString("icreatorname"));
                    model.setIcreatetime(String.valueOf(createtime));
                }
                dataList.add(model);
            }
        }
        return dataList;
    }
    
    public Map saveDomainManagement ( String jsonData, String userName, long userId ) throws RepositoryException, DBException, ParseException {
        Map res = new HashMap();
        List list = jsonTranslateDomainManagement(jsonData, userName, userId);
        Map<String, Object> connInfo = Tools.getConnectionInfo();
        Connection baseConn = (Connection) connInfo.get("baseConn");
        if (null != baseConn)
        {
            List<Connection> dbConns = (List<Connection>) connInfo.get("dbConns");
            Map orgSql = organizeAddDomainManagementSql(list, baseConn);
            if ((Boolean) orgSql.get(SUCCESS))
            {
                if (DBUtil.hitDatabase(dbConns, (List<String>) orgSql.get(EXESQLS),(List<String>) orgSql.get(ROLLBACKSQLS)))
                {
                    DBResource.closeConnection(baseConn, SAVEDOMAINMANAGEMENT, log);
                    res.put(SUCCESS, true);
                    res.put(MESSAGE, "数据保存成功！");
                    return res;
                } else {
                    DBResource.closeConnection(baseConn, SAVEDOMAINMANAGEMENT, log);
                    res.put(SUCCESS, false);
                    res.put(MESSAGE, "执行信息保存sql时出错");
                    return res;
                }
            } else {
                DBResource.closeConnection(baseConn, SAVEDOMAINMANAGEMENT, log);
                for (Connection conn : dbConns)
                {
                    DBResource.closeConnection(conn, SAVEDOMAINMANAGEMENT, log);
                }
                res.put(SUCCESS, false);
                res.put(MESSAGE, "组织信息sql时出错");
                return res;
            }
        } else {
            res.put(SUCCESS, false);
            res.put(MESSAGE, "没有连接资源, 无法保存！");
            return res;
        }
    }
    
    public Map organizeAddDomainManagementSql ( List<DomainManagementModel> list, Connection baseConn)
    {
        Map res = new HashMap();
        boolean isSuccess = true;
        List<String> exeSqls = new ArrayList<String>();
        List<String> rollbackSqls = new ArrayList<String>();
        DomainManagementManage manage = new DomainManagementManage();
        String insertSqlTemplate = " INSERT INTO ieai_domain_management (iid,iname,idesc,icreatorid,icreatorname,icreatetime) VALUES({iid},'{iname}','{idesc}',{icreatorid},'{icreatorname}','{icreatetime}') ";
        String deleteSqlTemplate = " delete from ieai_domain_management where IID=";
        String updateSqlTemplate = " update ieai_domain_management set iid={iid},iname='{iname}',idesc='{idesc}',icreatorid={icreatorid},icreatorname='{icreatorname}',icreatetime='{icreatetime}' where iid={iid}";
        try
        {
            for (DomainManagementModel model : list)
            {
                if (model.getIid() == 0)
                {
                    long id = IdGenerator.createId("ieai_domain_management", baseConn);
                    model.setIid(id);
                    BeanFormatter<DomainManagementModel> bf = new BeanFormatter<DomainManagementModel>(insertSqlTemplate);
                    String s = bf.format(model);
                    exeSqls.add(s);
                    rollbackSqls.add(deleteSqlTemplate + id);
                } else {
                    BeanFormatter<DomainManagementModel> bf = new BeanFormatter<DomainManagementModel>(updateSqlTemplate);
                    String s = bf.format(model);
                    exeSqls.add(s);
                    DomainManagementModel backModel = manage.getDomainManagementOne(model.getIid(), baseConn);
                    if (null != backModel)
                    {
                        BeanFormatter<DomainManagementModel> rbf = new BeanFormatter<DomainManagementModel>(updateSqlTemplate);
                        String r = rbf.format(backModel);
                        rollbackSqls.add(r);
                    }
                }
            }
        } catch (RepositoryException e) {
            log.error("organizeAddDomainManagementSql error",e);
            isSuccess = false;
        } catch (Exception e) {
            log.error("organizeAddDomainManagementSql error",e);
            isSuccess = false;
        }
        res.put(SUCCESS, isSuccess);
        res.put(EXESQLS, exeSqls);
        res.put(ROLLBACKSQLS, rollbackSqls);
        return res;
    }
    
    public Map deleteDomainManagement ( String deleteIds ) throws RepositoryException, DBException
    {
        Map res = new HashMap();
        Map<String, Object> connInfo = Tools.getConnectionInfo();
        Connection baseConn = (Connection) connInfo.get("baseConn");
        if (null != baseConn)
        {
            List<Connection> dbConns = (List<Connection>) connInfo.get("dbConns");
            Map orgSql = organizeDeleteDomainManagementSql(deleteIds, baseConn);
            if ((Boolean) orgSql.get(SUCCESS))
            {
                if (DBUtil.hitDatabase(dbConns, (List<String>) orgSql.get(EXESQLS),(List<String>) orgSql.get(ROLLBACKSQLS)))
                {
                    DBResource.closeConnection(baseConn, DELETEDOMAINMANAGEMENT, log);
                    res.put(SUCCESS, true);
                    res.put(MESSAGE, "数据删除成功！");
                    return res;
                } else {
                    DBResource.closeConnection(baseConn, DELETEDOMAINMANAGEMENT, log);
                    res.put(SUCCESS, false);
                    res.put(MESSAGE, "执行信息删除sql时出错");
                    return res;
                }
            } else {
                DBResource.closeConnection(baseConn, DELETEDOMAINMANAGEMENT, log);
                for (Connection conn : dbConns)
                {
                    DBResource.closeConnection(conn, DELETEDOMAINMANAGEMENT, log);
                }
                res.put(SUCCESS, false);
                res.put(MESSAGE, "组织信息sql时出错");
                return res;
            }
        } else {
            res.put(SUCCESS, false);
            res.put(MESSAGE, "没有连接资源, 无法删除！");
            return res;
        }
    }
    
    public Map organizeDeleteDomainManagementSql ( String deleteIds, Connection baseConn )
    {
        Map res = new HashMap();
        boolean isSuccess = true;
        List<String> exeSqls = new ArrayList<String>();
        List<String> rollbackSqls = new ArrayList<String>();
        DomainManagementManage manage = new DomainManagementManage();
        String deleteSqlTemplate = " delete from ieai_domain_management where IID=";
        String insertSqlTemplate = " INSERT INTO ieai_domain_management (iid,iname,idesc,icreatorid,icreatorname,icreatetime) VALUES({iid},'{iname}','{idesc}',{icreatorid},'{icreatorname}','{icreatetime}') ";
        try
        {
            String[] deleteId = deleteIds.split(",");
            for(int i=0;i<deleteId.length;i++){
                exeSqls.add(deleteSqlTemplate + deleteId[i]);
                DomainManagementModel model = manage.getDomainManagementOne(Long.valueOf(deleteId[i]), baseConn);
                if (null != model)
                {
                    BeanFormatter<DomainManagementModel> rbf = new BeanFormatter<DomainManagementModel>(insertSqlTemplate);
                    String s = rbf.format(model);
                    rollbackSqls.add(s);
                }
            }
        } catch (RepositoryException e)
        {
            log.error("organizeDeleteDomainManagementSql error",e);
            isSuccess = false;
        } catch (Exception e)
        {
            log.error("organizeDeleteDomainManagementSql error",e);
            isSuccess = false;
        }
        res.put(SUCCESS, isSuccess);
        res.put(EXESQLS, exeSqls);
        res.put(ROLLBACKSQLS, rollbackSqls);
        return res;
    }

    // ==================== 服务器绑定相关方法 ====================
    
    /**
     * 获取域绑定的服务器列表
     */
    public Map getDomainServerList(Integer start, Integer limit, String serverName, long domainId, int sysType) {
        Connection conn = null;
        Map map = new HashMap();
        try {
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            DomainManagementManage manage = new DomainManagementManage();
            
            String sqlWhere = " WHERE s.IDOMAINID = " + domainId;
            if (serverName != null && !"".equals(serverName)) {
                sqlWhere += " AND s.IP LIKE '%" + serverName + "%'";
            }
            
            String sqlList;
            if (JudgeDB.IEAI_DB_TYPE == 3) {
                sqlList = "SELECT s.ID as iid, " + domainId + " as domainId, s.ID as serverId, s.IP as serverIp, s.IP as serverName, '' as serverPort " +
                         "FROM ieai_serverlist s " + sqlWhere + " ORDER BY s.ID LIMIT ?,?";
            } else {
                sqlList = "SELECT * FROM (SELECT row_number() over (order by s.ID desc) AS ro, s.ID as iid, " + domainId + " as domainId, " +
                         "s.ID as serverId, s.IP as serverIp, s.IP as serverName, '' as serverPort " +
                         "FROM ieai_serverlist s " + sqlWhere + ") where ro>? and ro<=?";
            }
            
            String sqlCount = "SELECT count(s.ID) as countNum FROM ieai_serverlist s " + sqlWhere;
            
            List list = manage.getDomainServerList(conn, sqlList, start, limit);
            int count = manage.getDomainServerCount(conn, sqlCount);
            map.put("dataList", list);
            map.put("total", count);
        } catch (Exception e) {
            log.error("DomainManagementService.getDomainServerList is error", e);
        } finally {
            DBResource.closeConnection(conn, "getDomainServerList", log);
        }
        return map;
    }
    
    /**
     * 获取可绑定的服务器列表
     */
    public Map getAvailableServerList(Integer start, Integer limit, String serverName, long domainId, int sysType) {
        Connection conn = null;
        Map map = new HashMap();
        try {
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            DomainManagementManage manage = new DomainManagementManage();
            
            String sqlWhere = " WHERE (s.IDOMAINID IS NULL OR s.IDOMAINID = 0)";
            if (serverName != null && !"".equals(serverName)) {
                sqlWhere += " AND s.IP LIKE '%" + serverName + "%'";
            }
            
            String sqlList;
            if (JudgeDB.IEAI_DB_TYPE == 3) {
                sqlList = "SELECT s.ID as iid, s.IP as serverIp, s.IP as serverName, '' as serverPort " +
                         "FROM ieai_serverlist s " + sqlWhere + " ORDER BY s.ID LIMIT ?,?";
            } else {
                sqlList = "SELECT * FROM (SELECT row_number() over (order by s.ID desc) AS ro, s.ID as iid, " +
                         "s.IP as serverIp, s.IP as serverName, '' as serverPort " +
                         "FROM ieai_serverlist s " + sqlWhere + ") where ro>? and ro<=?";
            }
            
            String sqlCount = "SELECT count(s.ID) as countNum FROM ieai_serverlist s " + sqlWhere;
            
            List list = manage.getAvailableServerList(conn, sqlList, start, limit);
            int count = manage.getAvailableServerCount(conn, sqlCount);
            map.put("dataList", list);
            map.put("total", count);
        } catch (Exception e) {
            log.error("DomainManagementService.getAvailableServerList is error", e);
        } finally {
            DBResource.closeConnection(conn, "getAvailableServerList", log);
        }
        return map;
    }
    
    /**
     * 绑定服务器到域
     */
    public Map bindDomainServer(String jsonData, long domainId) throws RepositoryException, DBException {
        Map res = new HashMap();
        Map<String, Object> connInfo = Tools.getConnectionInfo();
        Connection baseConn = (Connection) connInfo.get("baseConn");
        
        if (null != baseConn) {
            List<Connection> dbConns = (List<Connection>) connInfo.get("dbConns");
            Map orgSql = organizeBindDomainServerSql(jsonData, domainId, baseConn);
            if ((Boolean) orgSql.get(SUCCESS)) {
                if (DBUtil.hitDatabase(dbConns, (List<String>) orgSql.get(EXESQLS), (List<String>) orgSql.get(ROLLBACKSQLS))) {
                    DBResource.closeConnection(baseConn, "bindDomainServer", log);
                    res.put(SUCCESS, true);
                    res.put(MESSAGE, "服务器绑定成功！");
                    return res;
                } else {
                    DBResource.closeConnection(baseConn, "bindDomainServer", log);
                    res.put(SUCCESS, false);
                    res.put(MESSAGE, "执行服务器绑定sql时出错");
                    return res;
                }
            } else {
                DBResource.closeConnection(baseConn, "bindDomainServer", log);
                for (Connection conn : dbConns) {
                    DBResource.closeConnection(conn, "bindDomainServer", log);
                }
                res.put(SUCCESS, false);
                res.put(MESSAGE, "组织服务器绑定sql时出错");
                return res;
            }
        } else {
            res.put(SUCCESS, false);
            res.put(MESSAGE, "没有连接资源, 无法绑定！");
            return res;
        }
    }
    
    /**
     * 组织服务器绑定SQL
     */
    public Map organizeBindDomainServerSql(String jsonData, long domainId, Connection baseConn) {
        Map res = new HashMap();
        boolean isSuccess = true;
        List<String> exeSqls = new ArrayList<String>();
        List<String> rollbackSqls = new ArrayList<String>();
        
        try {
            if (!"".equals(jsonData) && !"null".equals(jsonData) && null != jsonData) {
                JSONArray jsonArr = JSONArray.fromObject(jsonData);
                for (int i = 0; i < jsonArr.size(); i++) {
                    long serverId = Long.valueOf(jsonArr.getJSONObject(i).optString("iid"));
                    String updateSql = "UPDATE ieai_serverlist SET IDOMAINID = " + domainId + " WHERE ID = " + serverId;
                    String rollbackSql = "UPDATE ieai_serverlist SET IDOMAINID = NULL WHERE ID = " + serverId;
                    exeSqls.add(updateSql);
                    rollbackSqls.add(rollbackSql);
                }
            }
        } catch (Exception e) {
            log.error("organizeBindDomainServerSql error", e);
            isSuccess = false;
        }
        
        res.put(SUCCESS, isSuccess);
        res.put(EXESQLS, exeSqls);
        res.put(ROLLBACKSQLS, rollbackSqls);
        return res;
    }
    
    /**
     * 解除服务器绑定
     */
    public Map unbindDomainServer(String deleteIds) throws RepositoryException, DBException {
        Map res = new HashMap();
        Map<String, Object> connInfo = Tools.getConnectionInfo();
        Connection baseConn = (Connection) connInfo.get("baseConn");
        
        if (null != baseConn) {
            List<Connection> dbConns = (List<Connection>) connInfo.get("dbConns");
            Map orgSql = organizeUnbindDomainServerSql(deleteIds, baseConn);
            if ((Boolean) orgSql.get(SUCCESS)) {
                if (DBUtil.hitDatabase(dbConns, (List<String>) orgSql.get(EXESQLS), (List<String>) orgSql.get(ROLLBACKSQLS))) {
                    DBResource.closeConnection(baseConn, "unbindDomainServer", log);
                    res.put(SUCCESS, true);
                    res.put(MESSAGE, "服务器解绑成功！");
                    return res;
                } else {
                    DBResource.closeConnection(baseConn, "unbindDomainServer", log);
                    res.put(SUCCESS, false);
                    res.put(MESSAGE, "执行服务器解绑sql时出错");
                    return res;
                }
            } else {
                DBResource.closeConnection(baseConn, "unbindDomainServer", log);
                for (Connection conn : dbConns) {
                    DBResource.closeConnection(conn, "unbindDomainServer", log);
                }
                res.put(SUCCESS, false);
                res.put(MESSAGE, "组织服务器解绑sql时出错");
                return res;
            }
        } else {
            res.put(SUCCESS, false);
            res.put(MESSAGE, "没有连接资源, 无法解绑！");
            return res;
        }
    }
    
    /**
     * 组织服务器解绑SQL
     */
    public Map organizeUnbindDomainServerSql(String deleteIds, Connection baseConn) {
        Map res = new HashMap();
        boolean isSuccess = true;
        List<String> exeSqls = new ArrayList<String>();
        List<String> rollbackSqls = new ArrayList<String>();
        
        try {
            String[] deleteId = deleteIds.split(",");
            for (int i = 0; i < deleteId.length; i++) {
                String updateSql = "UPDATE ieai_serverlist SET IDOMAINID = NULL WHERE ID = " + deleteId[i];
                String rollbackSql = "UPDATE ieai_serverlist SET IDOMAINID = (SELECT IDOMAINID FROM ieai_domain_management WHERE IID = " + deleteId[i] + ") WHERE ID = " + deleteId[i];
                exeSqls.add(updateSql);
                rollbackSqls.add(rollbackSql);
            }
        } catch (Exception e) {
            log.error("organizeUnbindDomainServerSql error", e);
            isSuccess = false;
        }
        
        res.put(SUCCESS, isSuccess);
        res.put(EXESQLS, exeSqls);
        res.put(ROLLBACKSQLS, rollbackSqls);
        return res;
    }
    
    // ==================== 业务系统绑定相关方法 ====================
    
    /**
     * 获取域绑定的业务系统列表（只显示最新版本）
     */
    public Map getDomainBusinessList(Integer start, Integer limit, String businessName, long domainId, int sysType) {
        Connection conn = null;
        Map map = new HashMap();
        try {
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            DomainManagementManage manage = new DomainManagementManage();
            
            String sqlWhere = " WHERE p.IDOMAINID = " + domainId + " AND p.IID = p.ILATESTID";
            if (businessName != null && !"".equals(businessName)) {
                sqlWhere += " AND p.INAME LIKE '%" + businessName + "%'";
            }
            
            String sqlList;
            if (JudgeDB.IEAI_DB_TYPE == 3) {
                sqlList = "SELECT p.IID as iid, " + domainId + " as domainId, p.IID as businessId, p.INAME as businessName, p.ISYSTEMCODE as businessCode " +
                         "FROM ieai_project p " + sqlWhere + " ORDER BY p.IID LIMIT ?,?";
            } else {
                sqlList = "SELECT * FROM (SELECT row_number() over (order by p.IID desc) AS ro, p.IID as iid, " + domainId + " as domainId, " +
                         "p.IID as businessId, p.INAME as businessName, p.ISYSTEMCODE as businessCode " +
                         "FROM ieai_project p " + sqlWhere + ") where ro>? and ro<=?";
            }
            
            String sqlCount = "SELECT count(p.IID) as countNum FROM ieai_project p " + sqlWhere;
            
            List list = manage.getDomainBusinessList(conn, sqlList, start, limit);
            int count = manage.getDomainBusinessCount(conn, sqlCount);
            map.put("dataList", list);
            map.put("total", count);
        } catch (Exception e) {
            log.error("DomainManagementService.getDomainBusinessList is error", e);
        } finally {
            DBResource.closeConnection(conn, "getDomainBusinessList", log);
        }
        return map;
    }
    
    /**
     * 获取可绑定的业务系统列表（只显示最新版本且未绑定域）
     */
    public Map getAvailableBusinessList(Integer start, Integer limit, String businessName, long domainId, int sysType) {
        Connection conn = null;
        Map map = new HashMap();
        try {
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            DomainManagementManage manage = new DomainManagementManage();
            
            String sqlWhere = " WHERE (p.IDOMAINID IS NULL OR p.IDOMAINID = 0) AND p.IID = p.ILATESTID AND  IID>0 AND ( P.PROTYPE=1 OR P.PROTYPE=7 OR P.PROTYPE=8 OR IPKGCONTENTID=0 )";
            if (businessName != null && !"".equals(businessName)) {
                sqlWhere += " AND p.INAME LIKE '%" + businessName + "%'";
            }
            
            String sqlList;
            if (JudgeDB.IEAI_DB_TYPE == 3) {
                sqlList = "SELECT p.IID as iid, p.INAME as businessName, p.ISYSTEMCODE as businessCode,p.PROTYPE " +
                         "FROM ieai_project p " + sqlWhere + " order by PROTYPE desc,INAME asc LIMIT ?,?";
            } else {
                sqlList = "SELECT * FROM (SELECT row_number() over (order by p.IID desc) AS ro, p.IID as iid, " +
                         "p.INAME as businessName, p.ISYSTEMCODE as businessCode,p.PROTYPE " +
                         "FROM ieai_project p " + sqlWhere + ") where ro>? and ro<=?";
            }
            
            String sqlCount = "SELECT count(p.IID) as countNum FROM ieai_project p " + sqlWhere;
            
            List list = manage.getAvailableBusinessList(conn, sqlList, start, limit);
            int count = manage.getAvailableBusinessCount(conn, sqlCount);
            map.put("dataList", list);
            map.put("total", count);
        } catch (Exception e) {
            log.error("DomainManagementService.getAvailableBusinessList is error", e);
        } finally {
            DBResource.closeConnection(conn, "getAvailableBusinessList", log);
        }
        return map;
    }
    
    /**
     * 绑定业务系统到域（处理所有版本）
     */
    public Map bindDomainBusiness(String jsonData, long domainId) throws RepositoryException, DBException {
        Map res = new HashMap();
        Map<String, Object> connInfo = Tools.getConnectionInfo();
        Connection baseConn = (Connection) connInfo.get("baseConn");
        
        if (null != baseConn) {
            List<Connection> dbConns = (List<Connection>) connInfo.get("dbConns");
            Map orgSql = organizeBindDomainBusinessSql(jsonData, domainId, baseConn);
            if ((Boolean) orgSql.get(SUCCESS)) {
                if (DBUtil.hitDatabase(dbConns, (List<String>) orgSql.get(EXESQLS), (List<String>) orgSql.get(ROLLBACKSQLS))) {
                    DBResource.closeConnection(baseConn, "bindDomainBusiness", log);
                    res.put(SUCCESS, true);
                    res.put(MESSAGE, "业务系统绑定成功！");
                    return res;
                } else {
                    DBResource.closeConnection(baseConn, "bindDomainBusiness", log);
                    res.put(SUCCESS, false);
                    res.put(MESSAGE, "执行业务系统绑定sql时出错");
                    return res;
                }
            } else {
                DBResource.closeConnection(baseConn, "bindDomainBusiness", log);
                for (Connection conn : dbConns) {
                    DBResource.closeConnection(conn, "bindDomainBusiness", log);
                }
                res.put(SUCCESS, false);
                res.put(MESSAGE, "组织业务系统绑定sql时出错");
                return res;
            }
        } else {
            res.put(SUCCESS, false);
            res.put(MESSAGE, "没有连接资源, 无法绑定！");
            return res;
        }
    }
    
    /**
     * 组织业务系统绑定SQL（处理所有版本）
     */
    public Map organizeBindDomainBusinessSql(String jsonData, long domainId, Connection baseConn) {
        Map res = new HashMap();
        boolean isSuccess = true;
        List<String> exeSqls = new ArrayList<String>();
        List<String> rollbackSqls = new ArrayList<String>();
        
        try {
            if (!"".equals(jsonData) && !"null".equals(jsonData) && null != jsonData) {
                JSONArray jsonArr = JSONArray.fromObject(jsonData);
                for (int i = 0; i < jsonArr.size(); i++) {
                    long businessId = Long.valueOf(jsonArr.getJSONObject(i).optString("iid"));
                    // 获取业务编码，用于查找所有版本
                    String businessCode = jsonArr.getJSONObject(i).optString("businessCode");
                    String businessName = jsonArr.getJSONObject(i).optString("businessName");
                    int businessType = jsonArr.getJSONObject(i).optInt("type");
                    
                    // 更新所有版本的业务系统
                    String updateSql = "UPDATE ieai_project SET IDOMAINID = " + domainId + " WHERE ISYSTEMCODE = '" + businessCode + "' AND INAME = '"+businessName+"' AND PROTYPE = "+businessType;
                    String rollbackSql = "UPDATE ieai_project SET IDOMAINID = NULL WHERE ISYSTEMCODE = '" + businessCode + "' AND INAME = '"+businessName+"' AND PROTYPE = "+businessType;
                    exeSqls.add(updateSql);
                    rollbackSqls.add(rollbackSql);
                }
            }
        } catch (Exception e) {
            log.error("organizeBindDomainBusinessSql error", e);
            isSuccess = false;
        }
        
        res.put(SUCCESS, isSuccess);
        res.put(EXESQLS, exeSqls);
        res.put(ROLLBACKSQLS, rollbackSqls);
        return res;
    }
    
    /**
     * 解除业务系统绑定（处理所有版本）
     */
    public Map unbindDomainBusiness(String deleteIds) throws RepositoryException, DBException {
        Map res = new HashMap();
        Map<String, Object> connInfo = Tools.getConnectionInfo();
        Connection baseConn = (Connection) connInfo.get("baseConn");
        
        if (null != baseConn) {
            List<Connection> dbConns = (List<Connection>) connInfo.get("dbConns");
            Map orgSql = organizeUnbindDomainBusinessSql(deleteIds, baseConn);
            if ((Boolean) orgSql.get(SUCCESS)) {
                if (DBUtil.hitDatabase(dbConns, (List<String>) orgSql.get(EXESQLS), (List<String>) orgSql.get(ROLLBACKSQLS))) {
                    DBResource.closeConnection(baseConn, "unbindDomainBusiness", log);
                    res.put(SUCCESS, true);
                    res.put(MESSAGE, "业务系统解绑成功！");
                    return res;
                } else {
                    DBResource.closeConnection(baseConn, "unbindDomainBusiness", log);
                    res.put(SUCCESS, false);
                    res.put(MESSAGE, "执行业务系统解绑sql时出错");
                    return res;
                }
            } else {
                DBResource.closeConnection(baseConn, "unbindDomainBusiness", log);
                for (Connection conn : dbConns) {
                    DBResource.closeConnection(conn, "unbindDomainBusiness", log);
                }
                res.put(SUCCESS, false);
                res.put(MESSAGE, "组织业务系统解绑sql时出错");
                return res;
            }
        } else {
            res.put(SUCCESS, false);
            res.put(MESSAGE, "没有连接资源, 无法解绑！");
            return res;
        }
    }
    
    /**
     * 组织业务系统解绑SQL（处理所有版本）
     */
    public Map organizeUnbindDomainBusinessSql(String deleteIds, Connection baseConn) {
        Map res = new HashMap();
        boolean isSuccess = true;
        List<String> exeSqls = new ArrayList<String>();
        List<String> rollbackSqls = new ArrayList<String>();
        
        try {
            String[] deleteId = deleteIds.split(",");
            for (int i = 0; i < deleteId.length; i++) {
                // 根据业务系统ID获取业务编码
                String getLastIdSql = "SELECT ILATESTID FROM ieai_project WHERE IID = " + deleteId[i];
                PreparedStatement ps = baseConn.prepareStatement(getLastIdSql);
                ResultSet rs = ps.executeQuery();
                Long lastId = 0L;
                if (rs.next()) {
                    lastId = rs.getLong("ILATESTID");
                }
                rs.close();
                ps.close();

                if (lastId !=-0L) {
                    // 更新所有版本的业务系统
                    String updateSql = "UPDATE ieai_project SET IDOMAINID = NULL WHERE ILATESTID = " + lastId;
                    String rollbackSql = "UPDATE ieai_project SET IDOMAINID = (SELECT IDOMAINID FROM ieai_domain_management WHERE IID = " + deleteId[i] + ") WHERE ILATESTID = " + lastId;
                    exeSqls.add(updateSql);
                    rollbackSqls.add(rollbackSql);
                }
            }
        } catch (Exception e) {
            log.error("organizeUnbindDomainBusinessSql error", e);
            isSuccess = false;
        }
        
        res.put(SUCCESS, isSuccess);
        res.put(EXESQLS, exeSqls);
        res.put(ROLLBACKSQLS, rollbackSqls);
        return res;
    }
}
