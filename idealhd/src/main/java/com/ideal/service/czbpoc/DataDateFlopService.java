package com.ideal.service.czbpoc;

import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.core.JudgeDB;
import com.ideal.ieai.server.eswitch.common.SwitchException;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.czbpoc.DataDateFlopManager;
import com.ideal.ieai.server.repository.czbpoc.DataDateModel;
import com.ideal.ieai.server.repository.czbpoc.CutTimeAlarmScheduler;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import net.sf.json.JSONArray;
import org.apache.log4j.Logger;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.text.SimpleDateFormat;
import java.util.*;

public class DataDateFlopService {

    private static final Logger LOGGER = Logger.getLogger(DataDateFlopService.class);

    public Map getDataDateFlopList ( Integer start, Integer limit, String queryString,int sysType )
    {
        Connection conn = null;
        DataDateFlopManager manage = new DataDateFlopManager();
        String sqlWhere = "";
        if (queryString != null && !"".equals(queryString))
        {
            sqlWhere = sqlWhere + " and (a.isystem_name like '%"+queryString+"%' or a.isystem_name like '%"+queryString+"%') ";
        }
        String sqlList = "";
        if (JudgeDB.IEAI_DB_TYPE == 3)
        {
            sqlList = "select a.iid,a.isystem_name,a.idata_date,a.iupdate_time,a.icall_time,a.ibusiness_date,a.ioffset,a.icut_time from IEAI_DATA_DATE_FLOP a  where 1=1 "+ sqlWhere + " order by a.IID LIMIT ?,?";
        } else
        {
            sqlList = "select * from (select row_number() over (order by a.iid desc) AS ro,a.iid,a.isystem_name,a.idata_date,a.iupdate_time,a.icall_time,a.ibusiness_date,a.ioffset,a.icut_time from IEAI_DATA_DATE_FLOP a where 1=1 "+sqlWhere+" ) where ro>? and ro<=?";
        }
        String sqlCount = "select count(a.iid) as countNum from IEAI_DATA_DATE_FLOP a where 1=1 " + sqlWhere;
        Map map = new HashMap();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            List list = manage.getDataDateFlopList(conn, sqlList, start, limit);
            int count = manage.getDataDateFlopCount(conn, sqlCount);
            map.put("dataList", list);
            map.put("total", count);
        } catch (Exception e)
        {
            LOGGER.error("DataDateFlopService.getDataDateFlopList is error", e);
        } finally
        {
            DBResource.closeConnection(conn, "getDataDateFlopList", LOGGER);
        }
        return map;
    }


    public String saveDataDate ( String jsonData, int sysType ) throws RepositoryException
    {
        Connection conn = null;
        DataDateFlopManager manage = new DataDateFlopManager();
        String message = "";
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            List list = jsonTranslateSwitchConfig(jsonData);
            if (list != null && !list.isEmpty())
            {
                // 检查是否存在重复的业务系统名称
                Map<String, Integer> systemNameCount = new HashMap<String, Integer>();
                for (int i = 0; i < list.size(); i++)
                {
                    DataDateModel model = (DataDateModel) list.get(i);
                    String systemName = model.getIsystemName();
                    if (systemName != null && !systemName.trim().isEmpty())
                    {
                        Integer count = systemNameCount.get(systemName);
                        systemNameCount.put(systemName, count == null ? 1 : count + 1);
                    }
                }

                // 检查是否有重复的业务系统名称
                boolean hasDuplicate = false;
                for (Map.Entry<String, Integer> entry : systemNameCount.entrySet())
                {
                    if (entry.getValue() > 1)
                    {
                        hasDuplicate = true;
                        message = "不允许保存多条业务系统名称相同的数据，请检查后重新提交";
                        break;
                    }
                }

                // 检查数据库中是否已存在相同业务系统名称的记录（对于新增记录和修改记录）
                if (!hasDuplicate)
                {
                    for (int i = 0; i < list.size(); i++)
                    {
                        DataDateModel model = (DataDateModel) list.get(i);
                        String systemName = model.getIsystemName();
                        if (systemName != null && !systemName.trim().isEmpty())
                        {
                            String checkSql;
                            PreparedStatement ps = null;
                            ResultSet rs = null;

                            try {
                                if (model.getIid() == 0) // 新增记录
                                {
                                    // 查询数据库中是否已存在该业务系统
                                    checkSql = "select count(1) as cnt from IEAI_DATA_DATE_FLOP where isystem_name = ?";
                                    ps = conn.prepareStatement(checkSql);
                                    ps.setString(1, systemName);
                                }
                                else // 修改记录
                                {
                                    // 查询数据库中是否已存在该业务系统（排除当前记录本身）
                                    checkSql = "select count(1) as cnt from IEAI_DATA_DATE_FLOP where isystem_name = ? and iid <> ?";
                                    ps = conn.prepareStatement(checkSql);
                                    ps.setString(1, systemName);
                                    ps.setLong(2, model.getIid());
                                }

                                rs = ps.executeQuery();
                                if (rs.next() && rs.getInt("cnt") > 0)
                                {
                                    hasDuplicate = true;
                                    message = "业务系统 '" + systemName + "' 已存在，不允许重复添加";
                                    break;
                                }
                            } finally {
                                DBResource.closePSRS(rs, ps, "checkDuplicate", LOGGER);
                            }
                        }
                    }
                }

                // 如果没有重复，则进行保存操作
                if (!hasDuplicate)
                {
                    // 记录保存前的日切时间配置，用于判断是否需要重新加载调度器
                    boolean cutTimeChanged = false;
                    for (int i = 0; i < list.size(); i++)
                    {
                        DataDateModel model = (DataDateModel) list.get(i);

                        // 验证日切时间格式
                        if (model.getIcutTime() != null && !model.getIcutTime().trim().isEmpty()) {
                            if (!isValidCutTimeFormat(model.getIcutTime())) {
                                hasDuplicate = true;
                                message = "日切时间格式不正确，请使用HH:mm格式（如：23:30）";
                                break;
                            }
                        }

                        // 计算业务日期
                        String dataDate = model.getIdataDate();
                        String offset = model.getIoffset();

                        if (dataDate != null && !dataDate.trim().isEmpty() &&
                                offset != null && !offset.trim().isEmpty()) {
                            try {
                                // 解析数据日期
                                SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
                                Date date = sdf.parse(dataDate);

                                // 解析偏移量
                                int offsetDays = 0;
                                if (offset.startsWith("T+")) {
                                    offsetDays = -Integer.parseInt(offset.substring(2));
                                } else if (offset.startsWith("T-")) {
                                    offsetDays = Integer.parseInt(offset.substring(2));
                                }

                                // 计算业务日期
                                Calendar cal = Calendar.getInstance();
                                cal.setTime(date);
                                cal.add(Calendar.DAY_OF_MONTH, offsetDays);

                                // 设置业务日期
                                String businessDate = sdf.format(cal.getTime());
                                model.setIbusinessDate(businessDate);
                            } catch (Exception e) {
                                LOGGER.error("计算业务日期失败", e);
                            }
                        }

                        if (model.getIid() == 0)
                        {
                            // 新增记录，如果有日切时间则添加对应的调度任务
                            manage.addDataDate(conn, model);
                            if (model.getIcutTime() != null && !model.getIcutTime().trim().isEmpty()) {
                                cutTimeChanged = true;
                                // 添加新的日切时间任务
                                addCutTimeAlarmJobIfNeeded(model.getIcutTime().trim());
                            }
                        } else
                        {
                            // 修改记录，检查日切时间是否发生变化
                            String oldCutTime = getOldCutTime(conn, model.getIid());
                            String newCutTime = model.getIcutTime();

                            manage.updateDataDate(conn, model);

                            // 更新调度任务
                            if (hasCutTimeChangedCompare(oldCutTime, newCutTime)) {
                                cutTimeChanged = true;
                                updateCutTimeAlarmJob(oldCutTime, newCutTime);
                            }
                        }
                    }
                    conn.commit();
                    message = "保存成功";

                    // 日志记录
                    if (cutTimeChanged) {
                        LOGGER.info("日切时间配置已更新，相关调度任务已同步更新");
                    }
                }
            }
        } catch (Exception e)
        {
            LOGGER.error("DataDateFlopService.saveDataDate is error", e);
            DBResource.rollback(conn, ServerError.ERR_DB_INSERT, e, "saveDataDate", LOGGER);
            message = "保存失败：" + e.getMessage();
        } finally
        {
            DBResource.closeConnection(conn, "saveDataDate", LOGGER);
        }
        return message;
    }

    private List jsonTranslateSwitchConfig ( String jsonData )
    {
        List dataList = new ArrayList();
        if ("".equals(jsonData) || "null".equals(jsonData) || null == jsonData)
        {
            return dataList;
        }
        JSONArray jsonArr = JSONArray.fromObject(jsonData);
        for (int i = 0; i < jsonArr.size(); i++)
        {
            Calendar calendar = Calendar.getInstance();
            DataDateModel model = new DataDateModel();
            model.setIid(jsonArr.getJSONObject(i).optString("iid") == null
                    || "".equals(jsonArr.getJSONObject(i).optString("iid")) ? 0l
                    : Long.valueOf(jsonArr.getJSONObject(i).optString("iid")));
            model.setIsystemName(jsonArr.getJSONObject(i).optString("isystemName"));
            model.setIdataDate(jsonArr.getJSONObject(i).optString("idataDate"));
            model.setIupdateTime(String.valueOf(calendar.getTimeInMillis()));
            model.setIoffset(jsonArr.getJSONObject(i).optString("ioffset"));
            model.setIbusinessDate(jsonArr.getJSONObject(i).optString("ibusinessDate"));
            model.setIcutTime(jsonArr.getJSONObject(i).optString("icutTime"));
            dataList.add(model);
        }
        return dataList;
    }

    public void deleteDataDate ( String iids, int sysType ) throws SwitchException, RepositoryException
    {
        Connection conn = null;
        DataDateFlopManager manage = new DataDateFlopManager();
        Set<String> deletedCutTimes = new HashSet<>();

        try
        {
            String[] iid = iids.split(",");
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            if (iid != null && iid.length > 0)
            {
                for (int i = 0; i < iid.length; i++)
                {
                    // 删除前先获取日切时间
                    String cutTime = getOldCutTime(conn, Long.valueOf(iid[i]));
                    if (cutTime != null && !cutTime.trim().isEmpty()) {
                        deletedCutTimes.add(cutTime.trim());
                    }

                    manage.deleteDataDate(conn, Long.valueOf(iid[i]));
                }
            }
            conn.commit();

            // 检查删除的日切时间是否还有其他记录在使用，如果没有则移除对应的调度任务
            for (String cutTime : deletedCutTimes) {
                if (!isCutTimeStillInUse(conn, cutTime)) {
                    removeCutTimeAlarmJobIfNeeded(cutTime);
                    LOGGER.info("日切时间 " + cutTime + " 已无记录使用，移除对应调度任务");
                }
            }
        } catch (Exception e)
        {
            DBResource.rollback(conn, ServerError.ERR_DB_INSERT, e, "deleteDataDate", LOGGER);
            LOGGER.error("DataDateFlopService.deleteDataDate is error", e);
            throw new SwitchException(e.getMessage());
        } finally
        {
            DBResource.closeConnection(conn, "deleteDataDate", LOGGER);
        }
    }

    public List<Map<String,String>> getIeaiProject(int sysType) {

        Connection conn = null;
        DataDateFlopManager manage = new DataDateFlopManager();

        String sqlList = "select distinct iname from ieai_project where protype = 1";
        List<Map<String,String>> list = new ArrayList<>();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            list = manage.getIeaiProject(conn, sqlList);
        } catch (Exception e)
        {
            LOGGER.error("DataDateFlopService.getIeaiProject is error", e);
        } finally
        {
            DBResource.closeConnection(conn, "getIeaiProject", LOGGER);
        }
        return list;

    }

    /**
     * 更新所有记录的数据日期和业务日期
     * @param dataDate 新的数据日期
     * @param standardCodeName 贯标名称，用于过滤系统名称（系统名称规则：贯标名称_xxx）
     * @param sysType 系统类型
     * @return 更新的记录数
     * @throws RepositoryException 数据库操作异常
     */
    public int updateAllDataDates(String dataDate, String standardCodeName, int sysType) throws RepositoryException {
        Connection conn = null;
        PreparedStatement selectPs = null;
        PreparedStatement updatePs = null;
        ResultSet rs = null;
        int updatedCount = 0;

        try {
            conn = DBManager.getInstance().getJdbcConnection(sysType);

            // 查询符合贯标名称条件的记录（系统名称以"贯标名称_"开头）
            String selectSql = "SELECT iid, ioffset, isystem_name FROM IEAI_DATA_DATE_FLOP WHERE isystem_name LIKE ?";
            selectPs = conn.prepareStatement(selectSql);
            selectPs.setString(1, standardCodeName + "_%");
            rs = selectPs.executeQuery();

            // 准备更新语句，只更新数据日期、业务日期和icalltime
            String updateSql = "UPDATE IEAI_DATA_DATE_FLOP SET idata_date = ?, ibusiness_date = ?, icall_time = ? WHERE iid = ?";
            updatePs = conn.prepareStatement(updateSql);

            // 获取当前时间戳
            long currentTime = System.currentTimeMillis();

            // 处理每条记录
            while (rs.next()) {
                long id = rs.getLong("iid");
                String offset = rs.getString("ioffset");
                String systemName = rs.getString("isystem_name");

                LOGGER.info("正在更新系统: " + systemName + ", ID: " + id + ", 偏移量: " + offset);

                // 计算业务日期
                String businessDate = dataDate; // 默认与数据日期相同

                if (offset != null && !offset.trim().isEmpty()) {
                    try {
                        // 解析数据日期
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
                        Date date = sdf.parse(dataDate);

                        // 解析偏移量
                        int offsetDays = 0;
                        if (offset.startsWith("T+")) {
                            offsetDays = -Integer.parseInt(offset.substring(2));
                        } else if (offset.startsWith("T-")) {
                            offsetDays = Integer.parseInt(offset.substring(2));
                        }

                        // 计算业务日期
                        Calendar cal = Calendar.getInstance();
                        cal.setTime(date);
                        cal.add(Calendar.DAY_OF_MONTH, offsetDays);

                        businessDate = sdf.format(cal.getTime());
                    } catch (Exception e) {
                        LOGGER.error("计算业务日期失败: " + e.getMessage());
                        // 如果计算失败，使用数据日期作为业务日期
                        businessDate = dataDate;
                    }
                }

                // 更新记录
                updatePs.setString(1, dataDate);
                updatePs.setString(2, businessDate);
                updatePs.setString(3, String.valueOf(currentTime));
                updatePs.setLong(4, id);
                updatedCount += updatePs.executeUpdate();
            }

            conn.commit();
            LOGGER.info("成功更新 " + updatedCount + " 条记录，贯标名称: " + standardCodeName + ", 数据日期: " + dataDate);
            return updatedCount;
        } catch (Exception e) {
            LOGGER.error("DataDateFlopService.updateAllDataDates is error", e);
            if (conn != null) {
                try {
                    conn.rollback();
                } catch (Exception ex) {
                    LOGGER.error("Failed to rollback transaction", ex);
                }
            }
            throw new RepositoryException(ServerError.ERR_DB_UPDATE);
        } finally {
            DBResource.closePSRS(rs, selectPs, "updateAllDataDates-select", LOGGER);
            if (updatePs != null) {
                try {
                    updatePs.close();
                } catch (Exception e) {
                    LOGGER.error("Failed to close PreparedStatement", e);
                }
            }
            DBResource.closeConnection(conn, "updateAllDataDates", LOGGER);
        }
    }

    /**
     * 验证日切时间格式是否正确
     * @param cutTime 日切时间字符串
     * @return true表示格式正确，false表示格式错误
     */
    private boolean isValidCutTimeFormat(String cutTime) {
        if (cutTime == null || cutTime.trim().isEmpty()) {
            return true; // 空值允许
        }

        try {
            // 验证格式：HH:mm
            String regex = "^([01]?[0-9]|2[0-3]):[0-5][0-9]$";
            if (!cutTime.matches(regex)) {
                return false;
            }

            // 进一步验证时间的有效性
            String[] parts = cutTime.split(":");
            int hour = Integer.parseInt(parts[0]);
            int minute = Integer.parseInt(parts[1]);

            return hour >= 0 && hour <= 23 && minute >= 0 && minute <= 59;
        } catch (Exception e) {
            LOGGER.error("验证日切时间格式失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 检查日切时间是否发生变化
     * @param conn 数据库连接
     * @param model 要更新的数据模型
     * @return true表示日切时间发生了变化，false表示没有变化
     */
    private boolean hasCutTimeChanged(Connection conn, DataDateModel model) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            String sql = "SELECT icut_time FROM IEAI_DATA_DATE_FLOP WHERE iid = ?";
            ps = conn.prepareStatement(sql);
            ps.setLong(1, model.getIid());
            rs = ps.executeQuery();

            if (rs.next()) {
                String oldCutTime = rs.getString("icut_time");
                String newCutTime = model.getIcutTime();

                // 处理null值的比较
                if (oldCutTime == null && newCutTime == null) {
                    return false;
                }
                if (oldCutTime == null || newCutTime == null) {
                    return true;
                }

                // 去除空格后比较
                return !oldCutTime.trim().equals(newCutTime.trim());
            }
        } catch (Exception e) {
            LOGGER.error("检查日切时间变化失败: " + e.getMessage(), e);
        } finally {
            DBResource.closePSRS(rs, ps, "hasCutTimeChanged", LOGGER);
        }
        return false; // 出错时默认不重新加载
    }

    /**
     * 获取指定记录的原日切时间
     * @param conn 数据库连接
     * @param iid 记录ID
     * @return 原日切时间，可能为null
     */
    private String getOldCutTime(Connection conn, long iid) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            String sql = "SELECT icut_time FROM IEAI_DATA_DATE_FLOP WHERE iid = ?";
            ps = conn.prepareStatement(sql);
            ps.setLong(1, iid);
            rs = ps.executeQuery();

            if (rs.next()) {
                return rs.getString("icut_time");
            }
        } catch (Exception e) {
            LOGGER.error("获取原日切时间失败: " + e.getMessage(), e);
        } finally {
            DBResource.closePSRS(rs, ps, "getOldCutTime", LOGGER);
        }
        return null;
    }

    /**
     * 比较两个日切时间是否发生变化
     * @param oldCutTime 原日切时间
     * @param newCutTime 新日切时间
     * @return true表示发生了变化，false表示没有变化
     */
    private boolean hasCutTimeChangedCompare(String oldCutTime, String newCutTime) {
        // 处理null值的比较
        if (oldCutTime == null && newCutTime == null) {
            return false;
        }
        if (oldCutTime == null || newCutTime == null) {
            return true;
        }

        // 去除空格后比较
        return !oldCutTime.trim().equals(newCutTime.trim());
    }

    /**
     * 检查指定日切时间是否还有其他记录在使用
     * @param conn 数据库连接
     * @param cutTime 日切时间
     * @return true表示还有记录在使用，false表示没有记录使用
     */
    private boolean isCutTimeStillInUse(Connection conn, String cutTime) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            String sql = "SELECT COUNT(1) as cnt FROM IEAI_DATA_DATE_FLOP WHERE icut_time = ?";
            ps = conn.prepareStatement(sql);
            ps.setString(1, cutTime);
            rs = ps.executeQuery();

            if (rs.next()) {
                return rs.getInt("cnt") > 0;
            }
        } catch (Exception e) {
            LOGGER.error("检查日切时间使用情况失败: " + e.getMessage(), e);
        } finally {
            DBResource.closePSRS(rs, ps, "isCutTimeStillInUse", LOGGER);
        }
        return true; // 出错时默认认为还在使用，避免误删调度任务
    }

    /**
     * 如果需要则添加日切时间调度任务
     * @param cutTime 日切时间
     */
    private void addCutTimeAlarmJobIfNeeded(String cutTime) {
        try {
            CutTimeAlarmScheduler scheduler = CutTimeAlarmScheduler.getInstance();
            if (!scheduler.isCutTimeAlarmJobExists(cutTime)) {
                scheduler.addCutTimeAlarmJob(cutTime);
                LOGGER.info("添加新的日切时间调度任务: " + cutTime);
            }
        } catch (Exception e) {
            LOGGER.error("添加日切时间调度任务失败: " + e.getMessage(), e);
        }
    }

    /**
     * 如果需要则移除日切时间调度任务
     * @param cutTime 日切时间
     */
    private void removeCutTimeAlarmJobIfNeeded(String cutTime) {
        try {
            CutTimeAlarmScheduler scheduler = CutTimeAlarmScheduler.getInstance();
            if (scheduler.isCutTimeAlarmJobExists(cutTime)) {
                scheduler.removeCutTimeAlarmJob(cutTime);
                LOGGER.info("移除日切时间调度任务: " + cutTime);
            }
        } catch (Exception e) {
            LOGGER.error("移除日切时间调度任务失败: " + e.getMessage(), e);
        }
    }

    /**
     * 更新日切时间调度任务
     * @param oldCutTime 原日切时间
     * @param newCutTime 新日切时间
     */
    private void updateCutTimeAlarmJob(String oldCutTime, String newCutTime) {
        try {
            CutTimeAlarmScheduler scheduler = CutTimeAlarmScheduler.getInstance();
            scheduler.updateCutTimeAlarmJob(oldCutTime, newCutTime);
            LOGGER.info("更新日切时间调度任务: " + oldCutTime + " -> " + newCutTime);
        } catch (Exception e) {
            LOGGER.error("更新日切时间调度任务失败: " + e.getMessage(), e);
        }
    }
}
