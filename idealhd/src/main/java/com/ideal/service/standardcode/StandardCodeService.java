package com.ideal.service.standardcode;

import com.alibaba.fastjson.JSON;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.standardcode.StandardCodeManager;
import com.ideal.ieai.server.repository.standardcode.model.StandardCodeBean;
import com.ideal.common.utils.SessionData;
import org.apache.log4j.Logger;

import javax.servlet.http.HttpServletRequest;
import java.sql.Connection;
import java.util.*;

/**
 * 贯标管理Service
 */
public class StandardCodeService {

    private static final Logger log = Logger.getLogger(StandardCodeService.class);

    /**
     * 获取贯标列表
     */
    public Map<String, Object> getStandardCodeList(Integer start, Integer limit, String queryString) {
        Connection conn = null;
        StandardCodeManager manager = new StandardCodeManager();
        String sqlWhere = "";

        if (queryString != null && !"".equals(queryString)) {
            sqlWhere = " and (istandardcode like '%" + queryString + "%' or business_system_name like '%" + queryString + "%')";
        }

        String sqlList = "select iid, istandardcode, business_system_name, icreatetime, icreateuser from ieai_standard_code where 1=1 " + sqlWhere + " order by iid desc limit ?, ?";
        String sqlCount = "select count(iid) as countNum from ieai_standard_code where 1=1 " + sqlWhere;

        Map<String, Object> map = new HashMap<>();
        try {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);
            List<StandardCodeBean> list = manager.getStandardCodeList(conn, sqlList, start, limit);
            int count = manager.getStandardCodeCount(conn, sqlCount);
            map.put("dataList", list);
            map.put("total", count);
        } catch (Exception e) {
            log.error("StandardCodeService.getStandardCodeList is error", e);
            map.put("dataList", new java.util.ArrayList<>());
            map.put("total", 0);
        } finally {
            DBResource.closeConnection(conn, "getStandardCodeList", log);
        }
        return map;
    }

    /**
     * 保存贯标
     */
    public String saveStandardCode(String jsonData, HttpServletRequest request) throws RepositoryException {
        Connection conn = null;
        String message = "";
        StandardCodeManager manager = new StandardCodeManager();

        try {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);
            List<StandardCodeBean> list = jsonTranslateStandardCode(jsonData);
            message = checkStandardCode(list, manager, conn);

            if (!"".equals(message)) {
                return message;
            }

            // 获取当前登录用户
            String currentUser = "System";
            try {
                SessionData sessionData = SessionData.getSessionData(request.getSession());
                String userName = sessionData.getUserName();
                if (userName != null && !userName.trim().isEmpty()) {
                    currentUser = userName;
                }
            } catch (Exception e) {
                log.warn("获取当前用户失败，使用默认用户System", e);
            }

            if (list != null && !list.isEmpty()) {
                for (int i = 0; i < list.size(); i++) {
                    StandardCodeBean model = list.get(i);
                    if (model.getIid() == null || model.getIid() == 0) {
                        // 新增时设置创建用户为当前登录用户
                        model.setIcreateuser(currentUser);
                        manager.addStandardCode(conn, model);
                    } else {
                        manager.updateStandardCode(conn, model);
                    }
                }
            }
            conn.commit();
        } catch (Exception e) {
            DBResource.rollback(conn, ServerError.ERR_DB_INSERT, e, "saveStandardCode", log);
            log.error("StandardCodeService.saveStandardCode is error", e);
            throw new RepositoryException(ServerError.ERR_DB_INSERT);
        } finally {
            DBResource.closeConnection(conn, "saveStandardCode", log);
        }
        return message;
    }

    /**
     * 删除贯标
     */
    public void deleteStandardCode(String iids) throws RepositoryException {
        Connection conn = null;
        StandardCodeManager manager = new StandardCodeManager();

        try {
            String[] iid = iids.split(",");
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);

            if (iid != null && iid.length > 0) {
                for (int i = 0; i < iid.length; i++) {
                    manager.deleteStandardCode(conn, Long.valueOf(iid[i]));
                }
            }
            conn.commit();
        } catch (Exception e) {
            DBResource.rollback(conn, ServerError.ERR_DB_DELETE, e, "deleteStandardCode", log);
            log.error("StandardCodeService.deleteStandardCode is error", e);
            throw new RepositoryException(ServerError.ERR_DB_DELETE);
        } finally {
            DBResource.closeConnection(conn, "deleteStandardCode", log);
        }
    }

    /**
     * JSON转换为StandardCodeBean列表
     */
    private List<StandardCodeBean> jsonTranslateStandardCode(String jsonData) {
        List<StandardCodeBean> list = new ArrayList<>();
        try {
            List<Map> dataList = JSON.parseArray(jsonData, Map.class);
            for (Map<String, Object> map : dataList) {
                StandardCodeBean bean = new StandardCodeBean();

                // 处理ID
                Object iidObj = map.get("iid");
                if (iidObj != null && !"".equals(iidObj.toString())) {
                    bean.setIid(Long.valueOf(iidObj.toString()));
                }

                // 处理贯标代码
                Object istandardcodeObj = map.get("istandardcode");
                if (istandardcodeObj != null) {
                    bean.setIstandardcode(istandardcodeObj.toString());
                }

                // 处理业务系统中文名
                Object businessSystemNameObj = map.get("businessSystemName");
                if (businessSystemNameObj != null) {
                    bean.setBusinessSystemName(businessSystemNameObj.toString());
                }

                // 处理创建用户
                Object icreateUserObj = map.get("icreateuser");
                if (icreateUserObj != null) {
                    bean.setIcreateuser(icreateUserObj.toString());
                }

                list.add(bean);
            }
        } catch (Exception e) {
            log.error("JSON转换StandardCodeBean失败", e);
        }
        return list;
    }

    /**
     * 检查贯标数据
     */
    private String checkStandardCode(List<StandardCodeBean> list, StandardCodeManager manager, Connection conn) {
        String message = "";

        if (list != null && !list.isEmpty()) {
            // 1. 先检查同一批次中的重复
            Set<String> codeSet = new HashSet<>();
            Set<String> businessNameSet = new HashSet<>();
            for (StandardCodeBean bean : list) {
                // 检查贯标代码重复
                if (bean.getIstandardcode() != null && !bean.getIstandardcode().trim().isEmpty()) {
                    String code = bean.getIstandardcode().trim().toUpperCase();
                    if (codeSet.contains(code)) {
                        message = "贯标代码重复：" + code;
                        return message;
                    }
                    codeSet.add(code);
                }

                // 检查业务系统中文名重复
                if (bean.getBusinessSystemName() != null && !bean.getBusinessSystemName().trim().isEmpty()) {
                    String businessName = bean.getBusinessSystemName().trim();
                    if (businessNameSet.contains(businessName)) {
                        message = "业务系统中文名重复：" + businessName;
                        return message;
                    }
                    businessNameSet.add(businessName);
                }
            }

            // 2. 再检查其他验证
            for (StandardCodeBean bean : list) {
                // 检查必填字段
                if (bean.getIstandardcode() == null || bean.getIstandardcode().trim().isEmpty()) {
                    message = "贯标代码不能为空";
                    break;
                }

                // 检查业务系统中文名必填
                if (bean.getBusinessSystemName() == null || bean.getBusinessSystemName().trim().isEmpty()) {
                    message = "业务系统中文名不能为空";
                    break;
                }

                // 检查格式
                if (!bean.getIstandardcode().matches("^[a-zA-Z]+$")) {
                    message = "贯标代码只能包含英文字母";
                    break;
                }

                // 检查与数据库中现有记录的重复
                try {
                    if (bean.getIid() == null || bean.getIid() == 0) {
                        // 新增时检查重复
                        boolean codeExists = manager.isStandardCodeExists(conn, bean.getIstandardcode());
                        if (codeExists) {
                            message = "贯标代码已存在：" + bean.getIstandardcode();
                            break;
                        }

                        boolean businessNameExists = manager.isBusinessSystemNameExists(conn, bean.getBusinessSystemName());
                        if (businessNameExists) {
                            message = "业务系统中文名已存在：" + bean.getBusinessSystemName();
                            break;
                        }
                    } else {
                        // 更新时检查重复（排除自己）
                        boolean codeExists = manager.isStandardCodeExistsExcludeSelf(conn, bean.getIstandardcode(), bean.getIid());
                        if (codeExists) {
                            message = "贯标代码已存在：" + bean.getIstandardcode();
                            break;
                        }

                        boolean businessNameExists = manager.isBusinessSystemNameExistsExcludeSelf(conn, bean.getBusinessSystemName(), bean.getIid());
                        if (businessNameExists) {
                            message = "业务系统中文名已存在：" + bean.getBusinessSystemName();
                            break;
                        }
                    }
                } catch (Exception e) {
                    log.error("检查重复异常", e);
                    message = "检查重复异常";
                    break;
                }
            }
        }

        return message;
    }
}
