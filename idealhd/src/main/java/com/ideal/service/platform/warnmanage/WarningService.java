package com.ideal.service.platform.warnmanage;
import java.sql.Connection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.core.JudgeDB;
import com.ideal.ieai.server.platform.warnmanage.IeaiWarnModel;
import com.ideal.ieai.server.platform.warnmanage.IeaiWarnQueryModel;
import com.ideal.ieai.server.platform.warnmanage.WarningManage;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
public class WarningService
{
    private Logger log = Logger.getLogger(WarningService.class);
    
    
    /**
     * 
     * <li>Description: 记录告警信息，并通知相关联系人</li> 
     * <AUTHOR>
     * 2019年12月13日 
     * @param moduleCode 业务模块编码 必填
     * @param typeCode   业务类型编码 必填
     * @param levelCode  告警级别编码 必填
     * @param ip         IP地址           选填
     * @param message    告警通知内容 必填
     * @param happenTime 发生时间         选填
     * @return
     * return Long  告警信息主键
     */
    public Long warning(String moduleCode,String typeCode,String levelCode,String ip,String message,Date happenTime) {
        IeaiWarnModel model = new IeaiWarnModel();
        model.setIip(ip);
        model.setIlevelcode(levelCode);
        model.setImodulecode(moduleCode);
        model.setItypecode(typeCode);
        model.setIwarnmsg(message);
        model.setIhappentime(happenTime);
        log.info("业务编码："+moduleCode);
        log.info("类型编码："+typeCode);
        log.info("告警级别编码："+levelCode);
        log.info("IP地址："+ip);
        log.info("告警消息内容："+message);
        log.info("发生时间："+happenTime);
        Long warnId = 0L;
        
        try
        {
            warnId=this.createWarningAndNotice(model, Constants.IEAI_OPM);
            log.info("告警记录主键："+warnId);
        } catch (RepositoryException e)
        {
            log.error("WarningService.warning is error", e);
        }
        return warnId;
    }
    
    /**
     * 
     * <li>Description:保存告警信息并根据配置进行通知</li> 
     * <AUTHOR>
     * 2019年12月13日 
     * @param model
     * @param sysType
     * @throws RepositoryException
     * return void
     */
    private Long createWarningAndNotice(IeaiWarnModel model,int sysType) throws RepositoryException{
        Connection conn = null;
        WarningManage manage = new WarningManage();
        Long warnId = 0L;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            warnId = manage.addIeaiWarn(conn, model);
            conn.commit();
        } catch (Exception e)
        {
            DBResource.rollback(conn, ServerError.ERR_DB_ROLLBACK, e,"createWarning", log);
            log.error("WarningService.createWarning is error", e);
        } finally
        {
            DBResource.closeConnection(conn, "createWarning", log);
        }
        return warnId;
    }

    /**
     * 
     * <li>Description:查询分页</li> 
     * <AUTHOR>
     * 2019年12月30日 
     * @param start
     * @param limit
     * @param queryString
     * @param sysType
     * @return
     * return Map<String,Object>
     */
    public Map<String,Object> getWarnInfoList (IeaiWarnQueryModel model, int sysType)
    {
        Connection conn = null;
        WarningManage manage = new WarningManage();
        String sqlWhere = this.appendConditions(model);
        String sqlList = "";
        if (JudgeDB.IEAI_DB_TYPE == 3)
        {
            sqlList = "select w.iwarnid,\n" +
                            "       w.iip,\n" + 
                            "       date_format(w.ihappentime, '%Y-%m-%d %H:%i:%s') ihappentime,\n" + 
                            "       w.iwarnmsg,\n" + 
                            "       m.imodulename,\n" + 
                            "       t.itypename,\n" + 
                            "       l.ilevelname,\n" + 
                            "       w.ihostname,\n" + 
                            "       w.icomparerule,\n" + 
                            "       w.imodulecode,\n" + 
                            "       w.itypecode,\n" + 
                            "       w.ilevelcode,\n" + 
                            "       w.isystemname\n" + 
                            "  from IEAI_WARN w, IEAI_BUSI_MODULE m, IEAI_BUSI_TYPE t, IEAI_BUSI_LEVEL l\n" + 
                            " where w.imodulecode = m.imodulecode\n" + 
                            "   and w.itypecode = t.itypecode\n" + 
                            "   and w.ilevelcode = l.ilevelcode \n" + sqlWhere +
                            " order by w.iwarnid desc  LIMIT ?,?";

        } else
        {
            sqlList = "select *\n" +
                            "  from (select row_number() over(order by w.iwarnid desc) AS ro,\n" + 
                            "               w.iwarnid,\n" + 
                            "               w.iip,\n" + 
                            "               to_char(w.ihappentime, 'yyyy-mm-dd hh24:mi:ss') ihappentime,\n" + 
                            "               w.isystemname,\n" + 
                            "               w.iwarnmsg,\n" + 
                            "               w.imodulecode,\n" + 
                            "               w.itypecode,\n" + 
                            "               w.ilevelcode,\n" + 
                            "               m.imodulename,\n" + 
                            "               t.itypename,\n" + 
                            "               l.ilevelname\n" + 
                            "          from IEAI_WARN        w,\n" + 
                            "               IEAI_BUSI_MODULE m,\n" + 
                            "               IEAI_BUSI_TYPE   t,\n" + 
                            "               IEAI_BUSI_LEVEL  l\n" + 
                            "         where w.imodulecode = m.imodulecode\n" + 
                            "           and w.itypecode = t.itypecode\n" + 
                            "           and w.ilevelcode = l.ilevelcode \n" + sqlWhere +
                            "         order by w.iwarnid desc)\n" + 
                            " where ro > ?\n" + 
                            "   and ro <= ?";
        }
        String sqlCount = "select count(w.iwarnid) as countNum from IEAI_WARN w, IEAI_BUSI_MODULE m, IEAI_BUSI_TYPE t, IEAI_BUSI_LEVEL l where w.imodulecode = m.imodulecode and w.itypecode = t.itypecode and w.ilevelcode = l.ilevelcode "+ sqlWhere;
        Map<String,Object> map = new HashMap<String,Object>();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            List<IeaiWarnModel> list = manage.getIeaiWarnInfoList(conn, sqlList, model.getStart(), model.getLimit());
            int count = manage.getWarnInfoCount(conn, sqlCount);
            
            map.put("dataList", list);
            map.put("total", count);
        } catch (Exception e)
        {
            log.error("WarningService.getWarnInfoList is error", e);
        } finally
        {
            DBResource.closeConnection(conn, "getWarnInfoList", log);
        }
        return map;
    }
    
    private String appendConditions(IeaiWarnQueryModel model) {
        String sqlWhere = "";
        if (model.getIwarnmsg() != null && !"".equals(model.getIwarnmsg()))
        {
            sqlWhere = sqlWhere + " and (w.iwarnmsg like '%"+model.getIwarnmsg()+"%')";
        }
        if (model.getImodulecode() != null && !"".equals(model.getImodulecode()))
        {
            sqlWhere = sqlWhere + " and (m.imodulecode = '"+model.getImodulecode()+"')";
        }
        if (model.getItypecode() != null && !"".equals(model.getItypecode()))
        {
            sqlWhere = sqlWhere + " and (t.itypecode = '"+model.getItypecode()+"')";
        }
        if (model.getIlevelcode() != null && !"".equals(model.getIlevelcode()))
        {
            sqlWhere = sqlWhere + " and (l.ilevelcode = '"+model.getIlevelcode()+"')";
        }
        if (model.getQuerysystem() != null && !"".equals(model.getQuerysystem()))
        {
            sqlWhere = sqlWhere + " and (w.isystemname like '%"+model.getQuerysystem()+"%')";
        }
        if (model.getQueryiip() != null && !"".equals(model.getQueryiip()))
        {
            sqlWhere = sqlWhere + " and (w.iip like '%"+model.getQueryiip()+"%')";
        }
        if (JudgeDB.IEAI_DB_TYPE == 3)
        {
            if (model.getIstarttime() != null && !"".equals(model.getIstarttime()))
            {
                String starttime = model.getIstarttime();
                sqlWhere = sqlWhere + " and (w.ihappentime > date_format('"+starttime+"','%Y-%m-%d %H:%i:%s'))";
            }
            if (model.getIendtime() != null && !"".equals(model.getIendtime()))
            {
                String endtime = model.getIendtime();
                sqlWhere = sqlWhere + " and (w.ihappentime < date_format('"+endtime+"','%Y-%m-%d %H:%i:%s'))";
            }
            
        }else {
            if (model.getIstarttime() != null && !"".equals(model.getIstarttime()))
            {
                String starttime = model.getIstarttime();
                sqlWhere = sqlWhere + " and (w.ihappentime > to_date('"+starttime+"','yyyy-mm-dd hh24:mi:ss'))";
            }
            if (model.getIendtime() != null && !"".equals(model.getIendtime()))
            {
                String endtime = model.getIendtime();
                sqlWhere = sqlWhere + " and (w.ihappentime < to_date('"+endtime+"','yyyy-mm-dd hh24:mi:ss'))";
            }
        }
       
        
        return sqlWhere;
    }
    
}