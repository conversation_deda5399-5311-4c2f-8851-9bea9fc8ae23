package com.ideal.service.jobscheduling.taskdownload;

import com.alibaba.fastjson.JSON;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.core.JudgeDB;
import com.ideal.ieai.core.ProjectAdaptorCache;
import com.ideal.ieai.core.element.Project;
import com.ideal.ieai.core.element.Workflow;
import com.ideal.ieai.core.element.workflow.BasicActElement;
import com.ideal.ieai.core.element.workflow.FlowChartInfo;
import com.ideal.ieai.core.element.workflow.FlowEdgeInfo;
import com.ideal.ieai.core.io.UnMarshallingException;
import com.ideal.ieai.server.dm.bean.DmOprationResultMessage;
import com.ideal.ieai.server.ieaikernel.ServerEnv;
import com.ideal.ieai.server.jobscheduling.repository.taskdownload.TaskDownloadManager;
import com.ideal.ieai.server.jobscheduling.repository.timeDelayConfigManager.TimeDelayConfigManager;
import com.ideal.ieai.server.jobscheduling.util.taskdownloadbean.ActRelationInfo;
import com.ideal.ieai.server.jobscheduling.util.taskdownloadbean.FlowInfoData;
import com.ideal.ieai.server.jobscheduling.util.taskuploadbean.ActInfoVersionBean;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.project.ProjectManager;
import com.ideal.service.jobscheduling.taskupload.TaskUploadService;
import com.ideal.util.UUID;
import org.apache.log4j.Logger;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;

import javax.servlet.http.HttpServletResponse;
import javax.servlet.jsp.JspException;
import java.io.*;
import java.sql.Connection;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static org.apache.poi.ss.usermodel.FillPatternType.SOLID_FOREGROUND;

/**   
 * @ClassName:  TaskDownloadService   
 * @Description:作业依赖关系导出  
 * @author: yue_sun 
 * @date:   2018年2月26日 上午8:54:02   
 *     
 * @Copyright: 2018-2027 www.idealinfo.com Inc. All rights reserved. 
 * 
 */
public class TaskDownloadService
{
    private static final Logger              _log         = Logger.getLogger(TaskDownloadService.class);
    private static final TaskDownloadService _instProcess = new TaskDownloadService();

    public static TaskDownloadService getInstance ()
    {
        return _instProcess;
    }

    private TaskDownloadService()
    {
    }

    /**   
     * @Title: getTaskProject   
     * @Description: 获取任务工程列表   
     * @throws RepositoryException      
     * @author: yue_sun 
     * @date:   2018年2月26日 上午8:54:27   
     */
    public List getTaskProject ( Map map, long userId, String projectname, String start, String page, String limit,
            String sort ) throws RepositoryException
    {
        int dbType = Constants.IEAI_IEAI_BASIC;
        List result = ProjectManager.getInstance().getRelatedProject(userId);
        Connection conn = null;
        try
        {
            conn = DBResource.getConnection("getTaskProject", _log, dbType);
            return TaskDownloadManager.getInstance().getTaskProject(map, projectname, page, limit, sort, conn,
                result);
        } catch (Exception e)
        {
            _log.error("getTaskProject method of  SQLException:" + e.getMessage());
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closeConnection(conn, "TaskDownloadService.getTaskProject", _log);
        }

    }

    /**   
     * @Title: exportTaskExcel   
     * @Description: 导出依赖关系
     * @param response
     * @param projectname      
     * @author: Administrator 
     * @date:   2018年2月26日 上午8:54:55   
     */
    public void exportTaskExcel ( HttpServletResponse response, String projectname )
    {
        Map<String, FlowInfoData> map = new HashMap<String, FlowInfoData>();
        Map returnMap = new HashMap();
        boolean flag = false;
        try
        {
            flag = TaskDownloadManager.getInstance().isRelation(projectname, Constants.IEAI_IEAI);
            projectname =TaskDownloadManager.getInstance().isChildName(projectname, Constants.IEAI_IEAI);
        } catch (RepositoryException e1)
        {
            e1.printStackTrace();
            _log.error("isRelation is error，" + e1.getMessage());
        }
        if (flag)
        {
            returnMap= this.exportXLS(projectname, map);
            List acts = ( List)  returnMap.get("ActRelationInfo");
            try
            {
                doExportXLS(response.getOutputStream(), acts,returnMap, projectname, map,response);
            } catch (IOException e)
            {
                _log.error("doExportXLS is error，", e);
            }
        } else
        {
            List<FlowInfoData> acts = this.exportPrjoect(projectname, map);

            try
            {
                doExport(response.getOutputStream(), acts, projectname, map);
            } catch (IOException e)
            {
                e.printStackTrace();
                _log.error("doExport is error，" + e.getMessage());
            }
        }
    }

    /**   
     * @Title: exportXLS   
     * @Description: 获取依赖关系的活动信息)  
     * @param prjName
     * @param map
     * @return      
     * @author: yue_sun 
     * @date:   2018年2月26日 上午8:55:31   
     */
    public static Map exportXLS(String prjName, Map map)
    {
        Map returnMap = new HashMap();
        List actlist = null;
        try
        {
            if (DBManager.Orcl_Faimily() || JudgeDB.IEAI_DB_TYPE == JudgeDB.MYSQL)
            {
                returnMap = TaskDownloadManager.getInstance().getRelationInfoForOracleOrMysql(prjName,
                    Constants.IEAI_IEAI);
            } else if (JudgeDB.IEAI_DB_TYPE == JudgeDB.DB2)
            {
                actlist = TaskDownloadManager.getInstance().getRelationInfoForDb2(prjName, Constants.IEAI_IEAI);
                returnMap.put("ActRelationInfo", actlist);
            }

        } catch (RepositoryException e1)
        {
            _log.error("exportXLS method of FlowInfoAction.class to export excel !", e1);
        }

        return returnMap;
    }

    /**
     * @Title: doExportXLS
     * @Description: 导出操作方法
     * @param out
     * @param list
     * @param prjName
     * @param map
     * @throws JspException
     * @author: yue_sun
     * @date:   2018年2月26日 上午8:56:05
     */
    public void doExportXLS ( OutputStream out, List<ActRelationInfo> list, Map returnMap , String prjName,
            Map<String, FlowInfoData> map )
    {

        try
        {
            String shellH = "";
            String shellEndTag = "";
            for (int i = 0; i < list.size(); i++)
            {
                ActRelationInfo flowInfo = list.get(i);
                if (flowInfo==null){
                    continue;
                }
                shellH = flowInfo.getShellHouse();
                shellEndTag = flowInfo.getEndTag();
                i = list.size();
            }
            Map<String, String> indexMap = new HashMap<String, String>();
            HSSFSheet sheet = null;
            HSSFWorkbook wb = new HSSFWorkbook();
            sheet = wb.createSheet();
            wb.setSheetName(0, "作业基本关系");
            int rowNum = 4;
            sheet.setColumnWidth((short) 0, (short) 3000);
            sheet.setColumnWidth((short) 1, (short) 4000);
            sheet.setColumnWidth((short) 2, (short) 4000);
            sheet.setColumnWidth((short) 3, (short) 4000);
            sheet.setColumnWidth((short) 4, (short) 4000);
            sheet.setColumnWidth((short) 5, (short) 4000);
            sheet.setColumnWidth((short) 6, (short) 3000);
            sheet.setColumnWidth((short) 7, (short) 4000);
            sheet.setColumnWidth((short) 8, (short) 3000);
            sheet.setColumnWidth((short) 9, (short) 3000);
            sheet.setColumnWidth((short) 10, (short) 4000);
            sheet.setColumnWidth((short) 11, (short) 3000);
            sheet.setColumnWidth((short) 12, (short) 4000);
            sheet.setColumnWidth((short) 13, (short) 4000);
            sheet.setColumnWidth((short) 14, (short) 4000);
            // add
            sheet.setColumnWidth((short) 15, (short) 4000);
            sheet.setColumnWidth((short) 16, (short) 4000);
            sheet.setColumnWidth((short) 17, (short) 4000);
            sheet.setColumnWidth((short) 18, (short) 4000);
            sheet.setColumnWidth((short) 19, (short) 4000);
            // update by liyang, To add apt interface info
            sheet.setColumnWidth((short) 20, (short) 4000);// 优先级
            sheet.setColumnWidth((short) 21, (short) 4000);// 权重
            sheet.setColumnWidth((short) 22, (short) 4000);
            sheet.setColumnWidth((short) 23, (short) 4000);
            sheet.setColumnWidth((short) 24, (short) 4000);
            sheet.setColumnWidth((short) 25, (short) 4000);
            sheet.setColumnWidth((short) 26, (short) 4000);
            sheet.setColumnWidth((short) 27, (short) 4000);
            sheet.setColumnWidth((short) 28, (short) 4000);
            sheet.setColumnWidth((short) 29, (short) 4000);
            if (Environment.getInstance().getDgProjectParamSwitch()){
                sheet.setColumnHidden((short) 26, true);
                sheet.setColumnHidden((short) 27, true);
                sheet.setColumnHidden((short) 28, true);
                sheet.setColumnHidden((short) 29, true);
            }
            sheet.setColumnWidth((short) 30, (short) 4000);
            sheet.setColumnWidth((short) 31, (short) 4000);
            sheet.setColumnWidth((short) 32, (short) 4000);
            if(ServerEnv.getServerEnv().isJHBankSwitch()){
                sheet.setColumnWidth((short) 33, (short) 4000);
                sheet.setColumnWidth((short) 34, (short) 4000);
                sheet.setColumnWidth((short) 35, (short) 4000);
            }else if(Environment.getInstance().isBhODSUserSwitch()){
                sheet.setColumnWidth((short) 33, (short) 4000);
                sheet.setColumnWidth((short) 34, (short) 4000);
                sheet.setColumnWidth((short) 35, (short) 4000);
         /*       sheet.setColumnWidth((short) 36, (short) 4000);*/
            }else {
                sheet.setColumnWidth((short) 33, (short) 4000);
   /*             sheet.setColumnWidth((short) 34, (short) 4000);*/
            }

            // end update
            HSSFCellStyle headerStyle = null;
            HSSFCellStyle headerStyle1 = null;
            HSSFCellStyle headerStyle2 = null;
            HSSFCellStyle headerStyle3 = null;
            HSSFCellStyle headerStyle4 = null;
            HSSFCellStyle headerStyleTitle = null;
            if (true)
            {
                // Create an header row
                sheet.addMergedRegion(new CellRangeAddress(0, 0, (short) 0, (short) 28));
                HSSFFont font = wb.createFont();
                font.setFontHeightInPoints((short) 10); // 字体大小
                font.setBold(true); // 粗体
                font.setColor(IndexedColors.BLACK.getIndex()); // 绿字
                headerStyleTitle = wb.createCellStyle();
                headerStyleTitle.setAlignment(HorizontalAlignment.LEFT);
                headerStyleTitle.setVerticalAlignment(VerticalAlignment.CENTER);
                headerStyleTitle.setFont(font);
                headerStyleTitle.setWrapText(true);

                HSSFRow xlsRowTitle = sheet.createRow(0);
                HSSFCell cellTitle = xlsRowTitle.createCell((short) 0);
                // cellTitle.setEncoding(HSSFCell.ENCODING_UTF_16);
                cellTitle.setCellValue("说明:\n" + "        填充色为紫色代表开发中心项目组必填项；\n" + "        填充色为蓝色代表开发中心项目组选填项；\n"
                        + "        填充色为黄色代表开发中心项目组必填项。");
                cellTitle.setCellStyle(headerStyleTitle);
                xlsRowTitle.setHeightInPoints(60); // 设置行高

                headerStyle = wb.createCellStyle();
                headerStyle.setFillPattern(SOLID_FOREGROUND);
                headerStyle.setFillForegroundColor(IndexedColors.LAVENDER.getIndex());
                headerStyle.setFont(font);
                headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                headerStyle.setWrapText(true);

                headerStyle2 = wb.createCellStyle();
                headerStyle2.setFillPattern(SOLID_FOREGROUND);
                headerStyle2.setFillForegroundColor(IndexedColors.AQUA.getIndex());
                headerStyle2.setVerticalAlignment(VerticalAlignment.CENTER);
                headerStyle2.setFont(font);
                headerStyle2.setWrapText(true);
                // headerStyle2.setFillPattern(SOLID_FOREGROUND);

                headerStyle3 = wb.createCellStyle();
                headerStyle3.setFillPattern(SOLID_FOREGROUND);
                headerStyle3.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
                headerStyle3.setVerticalAlignment(VerticalAlignment.CENTER);
                headerStyle3.setFont(font);
                headerStyle3.setWrapText(true);

                headerStyle4 = wb.createCellStyle();
                headerStyle4.setFillPattern(SOLID_FOREGROUND);
                headerStyle4.setFillForegroundColor(IndexedColors.LIGHT_GREEN.getIndex());
                headerStyle4.setVerticalAlignment(VerticalAlignment.CENTER);
                headerStyle4.setFont(font);
                headerStyle4.setWrapText(true);

                headerStyle1 = wb.createCellStyle();
                headerStyle1.setWrapText(true);

                HSSFRow xlsRow1 = sheet.createRow(2);
                xlsRow1.setHeightInPoints(30); // 设置行高
                HSSFCell cellTitle0 = xlsRow1.createCell((short) 0);
                // cellTitle0.setEncoding(HSSFCell.ENCODING_UTF_16);
                cellTitle0.setCellValue("脚本外壳");
                cellTitle0.setCellStyle(headerStyle);

             //   sheet.addMergedRegion(new CellRangeAddress(2, 2, (short) 1, (short) 3));
                HSSFCell cellTitle1 = xlsRow1.createCell((short) 1);
                // cellTitle1.setEncoding(HSSFCell.ENCODING_UTF_16);
                cellTitle1.setCellValue(shellH);
                cellTitle1.setCellStyle(headerStyle1);

                HSSFCell cellTitle2 = xlsRow1.createCell((short) 2);
                // cellTitle1.setEncoding(HSSFCell.ENCODING_UTF_16);
                cellTitle2.setCellValue("结束标识");
                cellTitle2.setCellStyle(headerStyle);

              //  sheet.addMergedRegion(new CellRangeAddress(2, 2, (short) 5, (short) 7));
                HSSFCell cellTitle3 = xlsRow1.createCell((short) 3);
                // cellTitle1.setEncoding(HSSFCell.ENCODING_UTF_16);
                cellTitle3.setCellValue(shellEndTag);
                cellTitle3.setCellStyle(headerStyle1);

                HSSFRow xlsRow2 = sheet.createRow(1);
                xlsRow2.setHeightInPoints(30); // 设置行高
                HSSFCell cellTitleProjectName = xlsRow2.createCell((short) 0);
                // cellTitleProjectName.setEncoding(HSSFCell.ENCODING_UTF_16);
                cellTitleProjectName.setCellValue("工程名称");
                cellTitleProjectName.setCellStyle(headerStyle);

                sheet.addMergedRegion(new CellRangeAddress(1, 1, (short) 1, (short) 28));
                HSSFCell cellTitleProjectName1 = xlsRow2.createCell((short) 1);
                // cellTitleProjectName1.setEncoding(HSSFCell.ENCODING_UTF_16);
                cellTitleProjectName1.setCellValue(prjName);
                cellTitleProjectName1.setCellStyle(headerStyleTitle);

                // HSSFFont bold = wb.createFont();
                // bold.setBold(true);
                // bold.setColor(IndexedColors.WHITE.getIndex());
                // headerStyle.setFont(bold);

                HSSFRow xlsRow = sheet.createRow(3);
                xlsRow.setHeightInPoints(25); // 设置行高

                HSSFCell cell = xlsRow.createCell((short) 0);
                // cell.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell.setCellValue("操作步骤序号");
                cell.setCellStyle(headerStyle);

                HSSFCell cell1 = xlsRow.createCell((short) 1);
                // cell1.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell1.setCellValue("所属系统");
                cell1.setCellStyle(headerStyle);

                HSSFCell cell2 = xlsRow.createCell((short) 2);
                // cell2.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell2.setCellValue("子系统名称");
                cell2.setCellStyle(headerStyle);

                HSSFCell cell3 = xlsRow.createCell((short) 3);
                // cell3.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell3.setCellValue("作业名称");
                cell3.setCellStyle(headerStyle);

                HSSFCell cell4 = xlsRow.createCell((short) 4);
                // cell4.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell4.setCellValue("作业描述");
                cell4.setCellStyle(headerStyle);

                HSSFCell cell5 = xlsRow.createCell((short) 5);
                // cell5.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell5.setCellValue("触发作业");
                cell5.setCellStyle(headerStyle);

                HSSFCell cell6 = xlsRow.createCell((short) 6);
                // cell6.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell6.setCellValue("依赖作业");
                cell6.setCellStyle(headerStyle);

                HSSFCell cell7 = xlsRow.createCell((short) 7);
                // cell7.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell7.setCellValue("是否删除");
                cell7.setCellStyle(headerStyle);

                HSSFCell cell8 = xlsRow.createCell((short) 8);
                // cell8.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell8.setCellValue("OK文件绝对路径");
                cell8.setCellStyle(headerStyle);

                HSSFCell cell9 = xlsRow.createCell((short) 9);
                // cell9.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell9.setCellValue("OK文件检测周期");
                cell9.setCellStyle(headerStyle);
                // HSSFCell cell9 = xlsRow.createCell((short) 9);
                // cell9.setEncoding(HSSFCell.ENCODING_UTF_16);
                // cell9.setCellValue("脚本外壳");
                // cell9.setCellStyle(headerStyle);

                HSSFCell cell10 = xlsRow.createCell((short) 10);
                // cell10.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell10.setCellValue("脚本名称");
                cell10.setCellStyle(headerStyle);

                HSSFCell cell11 = xlsRow.createCell((short) 11);
                // cell11.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell11.setCellValue("输出参数");
                cell11.setCellStyle(headerStyle);
                // HSSFCell cell12 = xlsRow.createCell((short) 12);
                // cell12.setEncoding(HSSFCell.ENCODING_UTF_16);
                // cell12.setCellValue("脚本执行成功最后一行打印值");
                // cell12.setCellStyle(headerStyle);

                HSSFCell cell12 = xlsRow.createCell((short) 12);
                // cell12.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell12.setCellValue("业务异常自动重试");
                cell12.setCellStyle(headerStyle);

                HSSFCell cell29 = xlsRow.createCell((short) 13);
                // cell12.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell29.setCellValue("业务异常自动忽略");
                cell29.setCellStyle(headerStyle);

                HSSFCell cell13 = xlsRow.createCell((short) 14);
                // cell13.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell13.setCellValue("Agent资源组");
                cell13.setCellStyle(headerStyle);

                HSSFCell cell14 = xlsRow.createCell((short) 15);
                // cell14.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell14.setCellValue("Agent文件检测资源组");
                cell14.setCellStyle(headerStyle);

                // add
                HSSFCell cell15 = xlsRow.createCell((short) 16);// AgentIP:端口
                cell15.setCellValue("AgentIP:端口");
                cell15.setCellStyle(headerStyle);

                HSSFCell cell16 = xlsRow.createCell((short) 17);// 权重
                cell16.setCellValue("延迟运行");
                cell16.setCellStyle(headerStyle);

                HSSFCell cell17 = xlsRow.createCell((short) 18);
                cell17.setCellValue("执行条件");
                cell17.setCellStyle(headerStyle);

                HSSFCell cell18 = xlsRow.createCell((short) 19);
                cell18.setCellValue("重试次数");
                cell18.setCellStyle(headerStyle);

                HSSFCell cell30 = xlsRow.createCell((short) 20);
                cell30.setCellValue("重试间隔");
                cell30.setCellStyle(headerStyle);

                HSSFCell cell31 = xlsRow.createCell((short) 21);
                cell31.setCellValue("重试结束时间");
                cell31.setCellStyle(headerStyle);

                HSSFCell cell32 = xlsRow.createCell((short) 22);
                cell32.setCellValue("延时报警");
                cell32.setCellStyle(headerStyle);

                HSSFCell cell19 = xlsRow.createCell((short) 23);
                cell19.setCellValue("日历名称");
                cell19.setCellStyle(headerStyle);

                HSSFCell cell20 = xlsRow.createCell((short) 24);// 优先级
                cell20.setCellValue("优先级");
                cell20.setCellStyle(headerStyle);

                HSSFCell cell21 = xlsRow.createCell((short) 25);// 权重
                cell21.setCellValue("权重");
                cell21.setCellStyle(headerStyle);

                HSSFCell cell22 = xlsRow.createCell((short) 26);
                cell22.setCellValue("APT组名称");
                cell22.setCellStyle(headerStyle3);

                HSSFCell cell23 = xlsRow.createCell((short) 27);
                cell23.setCellValue("APT文件名称");
                cell23.setCellStyle(headerStyle3);

                HSSFCell cell24 = xlsRow.createCell((short) 28);
                cell24.setCellValue("ISDB2");
                cell24.setCellStyle(headerStyle3);

                HSSFCell cell25 = xlsRow.createCell((short) 29);
                cell25.setCellValue("DB2IP");
                cell25.setCellStyle(headerStyle3);

                HSSFCell cell26 = xlsRow.createCell((short) 30);
                cell26.setCellValue("主线名称");
                cell26.setCellStyle(headerStyle);

                HSSFCell cell27 = xlsRow.createCell((short) 31);
                cell27.setCellValue("头尾标记");
                cell27.setCellStyle(headerStyle2);

                HSSFCell cell28 = xlsRow.createCell((short) 32);
                cell28.setCellValue("是否禁用");
                cell28.setCellStyle(headerStyle);

                if(ServerEnv.getServerEnv().isJHBankSwitch()){

                    //交行新增功能
                    HSSFCell cell33 = xlsRow.createCell((short) 33);
                    cell33.setCellValue("DAYS");
                    cell33.setCellStyle(headerStyle);

                    HSSFCell cell34 = xlsRow.createCell((short) 34);
                    cell34.setCellValue("LOGIC");
                    cell34.setCellStyle(headerStyle);

                    HSSFCell cell35 = xlsRow.createCell((short) 35);
                    cell35.setCellValue("WDAYS");
                    cell35.setCellStyle(headerStyle);
                }else if(Environment.getInstance().isBhODSUserSwitch()){
                    HSSFCell cell33 = xlsRow.createCell((short) 33);
                    cell33.setCellValue("执行用户");
                    cell33.setCellStyle(headerStyle);
                    HSSFCell cell34 = xlsRow.createCell((short) 34);
                    cell34.setCellValue("虚拟组名称");
                    cell34.setCellStyle(headerStyle);
/*                    HSSFCell cell35 = xlsRow.createCell((short) 35);
                    cell35.setCellValue("ok文件名称");
                    cell35.setCellStyle(headerStyle);*/
                    HSSFCell cell36 = xlsRow.createCell((short) 35);
                    cell36.setCellValue("人工提醒");
                    cell36.setCellStyle(headerStyle);
                    //法时
                    //DAYS:指定批量日期执行1-31
                    HSSFCell cell37 = xlsRow.createCell((short) 36);
                    cell37.setCellValue("DAYS");
                    cell37.setCellStyle(headerStyle);
                    //LOGIC:条件逻辑符AND/OR，AND多个条件均满足，OR多个条件满足一个即可
                    HSSFCell cell38 = xlsRow.createCell((short) 37);
                    cell38.setCellValue("LOGIC");
                    cell38.setCellStyle(headerStyle);
                    //WDAYS:指定星期执行0-6，批量对应的星期一至星期日
                    HSSFCell cell39 = xlsRow.createCell((short) 38);
                    cell39.setCellValue("WDAYS");
                    cell39.setCellStyle(headerStyle);
                    //MONTH:指定月份执行1-12
                    HSSFCell cell40 = xlsRow.createCell((short) 39);
                    cell40.setCellValue("MONTH");
                    cell40.setCellStyle(headerStyle);
                }else if( Environment.getInstance().getXanxSwitch()) //西安
                {
                    HSSFCell cell34 = xlsRow.createCell((short) 33);
                    cell34.setCellValue("人工提醒");
                    cell34.setCellStyle(headerStyle);
                    //交行新增功能
                    HSSFCell cell35 = xlsRow.createCell((short) 34);
                    cell35.setCellValue("DAYS");
                    cell35.setCellStyle(headerStyle);

                    HSSFCell cell36 = xlsRow.createCell((short) 35);
                    cell36.setCellValue("LOGIC");
                    cell36.setCellStyle(headerStyle);

                    HSSFCell cell37 = xlsRow.createCell((short) 36);
                    cell37.setCellValue("WDAYS");
                    cell37.setCellStyle(headerStyle);
                    HSSFCell cell38 = xlsRow.createCell((short) 37);
                    cell38.setCellValue("MONTH");
                    cell38.setCellStyle(headerStyle);
                }else {
/*                    HSSFCell cell33 = xlsRow.createCell((short) 33);
                    cell33.setCellValue("ok文件名称");
                    cell33.setCellStyle(headerStyle);*/
                    HSSFCell cell34 = xlsRow.createCell((short) 33);
                    cell34.setCellValue("人工提醒");
                    cell34.setCellStyle(headerStyle);
                    //法时
                    //DAYS:指定批量日期执行1-31
                    HSSFCell cell35 = xlsRow.createCell((short) 34);
                    cell35.setCellValue("DAYS");
                    cell35.setCellStyle(headerStyle);
                    //LOGIC:条件逻辑符AND/OR，AND多个条件均满足，OR多个条件满足一个即可
                    HSSFCell cell36 = xlsRow.createCell((short) 35);
                    cell36.setCellValue("LOGIC");
                    cell36.setCellStyle(headerStyle);
                    //WDAYS:指定星期执行0-6，批量对应的星期一至星期日
                    HSSFCell cell37 = xlsRow.createCell((short) 36);
                    cell37.setCellValue("WDAYS");
                    cell37.setCellStyle(headerStyle);
                    //MONTH:指定月份执行1-12
                    HSSFCell cell48 = xlsRow.createCell((short) 37);
                    cell48.setCellValue("MONTH");
                    cell48.setCellStyle(headerStyle);
                    if( Environment.getInstance().getGYBankSwitch()){
                        //MONTH:指定月份执行1-12
                        HSSFCell cell49 = xlsRow.createCell((short) 38);
                        cell49.setCellValue("延迟方式");
                        cell49.setCellStyle(headerStyle);
                    }

                }
            }
            String act = "";
            int num = 0;
            // int offset = 1;
            for (int i = 0; i < list.size(); i++)
            {
                ActRelationInfo flowInfo = list.get(i);
                // if(!flowInfo.getFlowName().equals(tempFlowName))
                // {
                // tempFlowName = flowInfo.getFlowName();
                // offset = i+1;
                // }
                if (flowInfo==null){
                    continue;
                }
                indexMap.put(flowInfo.getActID(), String.valueOf(i + 1));
                HSSFRow xlsRow = sheet.createRow(rowNum++);
                xlsRow.setHeightInPoints(25); // 设置行高

                if(Environment.getInstance().getGYBankSwitch()){
                    HSSFCell cell0 = xlsRow.createCell((short) 0);
                    // cell0.setEncoding(HSSFCell.ENCODING_UTF_16);
                    cell0.setCellValue(flowInfo.getActNo());
                    cell0.setCellStyle(headerStyle1);
                }else{
                    HSSFCell cell0 = xlsRow.createCell((short) 0);
                    // cell0.setEncoding(HSSFCell.ENCODING_UTF_16);
                    cell0.setCellValue(flowInfo.getId());
                    cell0.setCellStyle(headerStyle1);
                }

                HSSFCell cell1 = xlsRow.createCell((short) 1);
                // cell1.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell1.setCellValue(flowInfo.getSystem());
                cell1.setCellStyle(headerStyle1);

                HSSFCell cell2 = xlsRow.createCell((short) 2);
                // cell2.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell2.setCellValue(flowInfo.getChildproName());
                cell2.setCellStyle(headerStyle1);

                HSSFCell cell3 = xlsRow.createCell((short) 3);
                // cell3.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell3.setCellValue(flowInfo.getActName());
                cell3.setCellStyle(headerStyle1);

                HSSFCell cell4 = xlsRow.createCell((short) 4);
                // cell4.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell4.setCellValue(flowInfo.getActDesc());
                cell4.setCellStyle(headerStyle1);

                HSSFCell cell5 = xlsRow.createCell((short) 5);
                if(!Environment.getInstance().isBhODSEXPORTRELYONNAME()) {
                cell5.setCellValue(flowInfo.getPreAct());
                cell5.setCellStyle(headerStyle1);
                }
                HSSFCell cell6 = xlsRow.createCell((short) 6);
                // cell6.setEncoding(HSSFCell.ENCODING_UTF_16);
                if(Environment.getInstance().isBhODSUserSwitch()){
                    boolean cz=false;
                    if(!"".equals(flowInfo.getJoblist())&&null!=flowInfo.getJoblist()){
                        for (String s : flowInfo.getJoblist().split(",")) {
                            Map<String, String> VirtualkeyMap = (Map<String, String>) returnMap.get("VirtualkeyMap");
                            if(VirtualkeyMap.containsKey(s)){//判断是否有虚拟组存在
                                cz=true;
                            }else {

                            }
                        }
                    }
                    if(cz){
                        String Virtualname="";
                        if(Environment.getInstance().isBhODSEXPORTRELYONNAME()){
                            for (int j = 0; j < list.size(); j++)
                            {
                                for (String s : flowInfo.getJoblist().split(",")) {
                                    ActRelationInfo flowInfoj = list.get(j);
                                    if(s.equals(flowInfoj.getSequenceNumber())){
                                        boolean status = Virtualname.contains(flowInfoj.getActName());
                                        if(status){

                                        }else {
                                            Virtualname=   flowInfoj.getActName() + "," + Virtualname;
                                        }

                                    }else if(s.equals(flowInfoj.getId())){
                                        boolean status = Virtualname.contains(flowInfoj.getActName());
                                        if(status){

                                        }else {
                                            Virtualname=   flowInfoj.getActName() + "," + Virtualname;
                                        }
                                    }else if(s.equals(flowInfoj.getActName())){
                                        boolean status = Virtualname.contains(flowInfoj.getActName());
                                        if(status){

                                        }else {
                                            Virtualname=   flowInfoj.getActName() + "," + Virtualname;
                                        }
                                    }else {
                                        boolean status = Virtualname.contains(s);
                                        if(status){

                                        }else {
                                            Map<String, String> VirtualkeyMap = (Map<String, String>) returnMap.get("VirtualkeyMap");
                                            if(VirtualkeyMap.containsKey(s)){//判断是否有虚拟组存在
                                            Virtualname=  s + "," + Virtualname;
                                            }
                                        }
                                    }

                                }
                            }
                            if(Virtualname.contains(",")){
                                Virtualname = Virtualname.substring(0,Virtualname.length() - 1);
                            }
                            cell6.setCellValue(Virtualname);
                            cell6.setCellStyle(headerStyle1);
                        }else {

                                for (String s : flowInfo.getJoblist().split(",")) {
                                    for (int j = 0; j < list.size(); j++)
                                    {
                                    ActRelationInfo flowInfoj = list.get(j);
                                    if(s.contains(flowInfoj.getSequenceNumber())){
                                        boolean status = Virtualname.contains(flowInfoj.getId());
                                        if(status){

                                        }else {
                                        Virtualname=   flowInfoj.getId() + "," + Virtualname;
                                        }
                                    }else if(s.equals(flowInfoj.getActName())){
                                        boolean status = Virtualname.contains(flowInfoj.getId());
                                        if(status){

                                        }else {
                                            Virtualname = flowInfoj.getId() + "," + Virtualname;
                                        }
                                    }else if(s.equals(flowInfoj.getId())){
                                        boolean status = Virtualname.contains(flowInfoj.getId());
                                        if(status){

                                        }else {
                                            Virtualname=   flowInfoj.getId() + "," + Virtualname;
                                        }
                                    }else {
                                        boolean status = Virtualname.contains(s);
                                        if (status) {

                                        } else {
                                            Map<String, String> VirtualkeyMap = (Map<String, String>) returnMap.get("VirtualkeyMap");
                                            if(VirtualkeyMap.containsKey(s)){//判断是否有虚拟组存在
                                            Virtualname = s + "," + Virtualname;
                                            }
                                        }
                                    }
                                }
                            }
                                if(Virtualname.contains(",")){
                                    Virtualname = Virtualname.substring(0,Virtualname.length() - 1);
                                }

                            cell6.setCellValue(Virtualname);
                            cell6.setCellStyle(headerStyle1);
                        }
                    }else {
                        cell6.setCellValue(flowInfo.getSuccAct());
                        cell6.setCellStyle(headerStyle1);
                    }
                }else {
                    cell6.setCellValue(flowInfo.getSuccAct());
                    cell6.setCellStyle(headerStyle1);
                }

                HSSFCell cell7 = xlsRow.createCell((short) 7);
                // cell7.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell7.setCellValue("否");

                // RangeAddress regions = new RangeAddress(0,0,0,0);

                // //生成下拉框内容
                // String [] list1={"11","34"};
                //
                // DVConstraint constraint = DVConstraint.createExplicitListConstraint(list);
                //
                // //绑定下拉框和作用区域
                //
                // //HSSFDataValidation data_validation = new
                // HSSFDataValidation((short)(1),(short)1,(short)(1),(short)1);
                //
                // HSSFDataValidation data_validation = new HSSFDataValidation(regions,constraint);
                //
                // //对sheet页生效
                //
                // sheet.addValidationData(data_validation);
                //
                HSSFCell cell8 = xlsRow.createCell((short) 8);
                // cell8.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell8.setCellValue(flowInfo.getOkFilePath());
                cell8.setCellStyle(headerStyle1);

                HSSFCell cell9 = xlsRow.createCell((short) 9);
                // cell9.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell9.setCellValue("0".equals(flowInfo.getOkFileWeek()) ? "" : flowInfo.getOkFileWeek());
                cell9.setCellStyle(headerStyle1);
                // HSSFCell cell9 = xlsRow.createCell((short) 9);
                // cell9.setEncoding(HSSFCell.ENCODING_UTF_16);
                // cell9.setCellValue(flowInfo.getShellHouse());
                HSSFCell cell10 = xlsRow.createCell((short) 10);
                // cell10.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell10.setCellValue(flowInfo.getShellName());
                cell10.setCellStyle(headerStyle1);

                HSSFCell cell11 = xlsRow.createCell((short) 11);
                // cell11.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell11.setCellValue(flowInfo.getOutPara());
                cell11.setCellStyle(headerStyle1);
                // HSSFCell cell12 = xlsRow.createCell((short) 12);
                // cell12.setEncoding(HSSFCell.ENCODING_UTF_16);
                // cell12.setCellValue(flowInfo.getLastLine());

                HSSFCell cell12 = xlsRow.createCell((short) 12);
                // cell12.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell12.setCellValue("0".equals(flowInfo.getRedo()) ? "否" : "是");
                cell12.setCellStyle(headerStyle1);

                HSSFCell cell29 = xlsRow.createCell((short) 13);
                // cell12.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell29.setCellValue("0".equals(flowInfo.getSkip()) ? "否" : "是");
                cell29.setCellStyle(headerStyle1);

                HSSFCell cell13 = xlsRow.createCell((short) 14);
                // cell13.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell13.setCellValue(flowInfo.isAgentGroup() ? flowInfo.getAgentGroup() : "");
                cell13.setCellStyle(headerStyle1);

                HSSFCell cell14 = xlsRow.createCell((short) 15);
                // cell14.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell14.setCellValue(flowInfo.getAgentCheckGroup());
                cell14.setCellStyle(headerStyle1);

                // AgentIP:端口 延迟运行 分支条件 重试次数 日历名称

                HSSFCell cell15 = xlsRow.createCell((short) 16);
                cell15.setCellValue(flowInfo.isAgentGroup() ? "" : flowInfo.getAgentGroup());
                cell15.setCellStyle(headerStyle1);

                HSSFCell cell16 = xlsRow.createCell((short) 17);
                // cellWeight.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell16.setCellValue(flowInfo.getDelayTime());
                cell16.setCellStyle(headerStyle1);

                HSSFCell cell17 = xlsRow.createCell((short) 18);
                // cell15.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell17.setCellValue(flowInfo.getBranchCondition());
                cell17.setCellStyle(headerStyle1);

                HSSFCell cell18 = xlsRow.createCell((short) 19);
                // cell16.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell18.setCellValue("0".equals(flowInfo.getReTryCount()) ? "" : flowInfo.getReTryCount());
                cell18.setCellStyle(headerStyle1);

                HSSFCell cell30 = xlsRow.createCell((short) 20);
                // cell16.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell30.setCellValue(flowInfo.getReTryTime());
                cell30.setCellStyle(headerStyle1);

                HSSFCell cell31 = xlsRow.createCell((short) 21);
                // cell16.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell31.setCellValue(flowInfo.getReTryEndTime());
                cell31.setCellStyle(headerStyle1);

                HSSFCell cell32 = xlsRow.createCell((short) 22);
                // cell16.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell32.setCellValue(flowInfo.getDelayWarnning());
                cell32.setCellStyle(headerStyle1);

                HSSFCell cell19 = xlsRow.createCell((short) 23);
                // cellPriority.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell19.setCellValue(flowInfo.getCalendName());
                cell19.setCellStyle(headerStyle1);

                HSSFCell cell20 = xlsRow.createCell((short) 24);
                // cellPriority.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell20.setCellValue(flowInfo.getPriority());
                cell20.setCellStyle(headerStyle1);

                HSSFCell cell21 = xlsRow.createCell((short) 25);
                // cell15.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell21.setCellValue(flowInfo.getWeights());
                cell21.setCellStyle(headerStyle1);

                HSSFCell cell22 = xlsRow.createCell((short) 26);
                // cell15.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell22.setCellValue(flowInfo.getAptGroupName());
                cell22.setCellStyle(headerStyle1);

                HSSFCell cell23 = xlsRow.createCell((short) 27);
                // cell16.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell23.setCellValue(flowInfo.getAptFileName());
                cell23.setCellStyle(headerStyle1);

                HSSFCell cell24 = xlsRow.createCell((short) 28);
                // cell17.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell24.setCellValue(flowInfo.getIsdb2());
                cell24.setCellStyle(headerStyle1);

                HSSFCell cell25 = xlsRow.createCell((short) 29);
                // cell18.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell25.setCellValue(flowInfo.getDb2ip());
                cell25.setCellStyle(headerStyle1);

                HSSFCell cell26 = xlsRow.createCell((short) 30);
                // cell20.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell26.setCellValue(flowInfo.getMainLineName());
                cell26.setCellStyle(headerStyle1);

                int hf = -1;
                if (!"".equals(flowInfo.getHeadFlag()))
                {
                    hf = Integer.parseInt(flowInfo.getHeadFlag());
                }
                String headflag = "";
                switch (hf)
                {
                    case 1:
                        headflag = "总头";
                        break;
                    case 2:
                        headflag = "主线头";
                        break;
                    case 6:
                        headflag = "总尾";
                        break;
                    case 7:
                        headflag = "主线尾";
                        break;
                }
                HSSFCell cell27 = xlsRow.createCell((short) 31);
                cell27.setCellValue(headflag);
                cell27.setCellStyle(headerStyle1);

                if (null != flowInfo.getActName())
                {
                    if (i == list.size() - 1)
                    {
                        act = act + flowInfo.getActName();
                    } else
                    {
                        act = act + flowInfo.getActName() + "、";
                    }
                    num = num + 1;
                }
                HSSFCell cell28 = xlsRow.createCell((short) 32);
                cell28.setCellValue(flowInfo.getIsDisabled());
                cell28.setCellStyle(headerStyle1);

                if(ServerEnv.getServerEnv().isJHBankSwitch()){

                    HSSFCell cell33 = xlsRow.createCell((short) 33);
                    cell33.setCellValue(flowInfo.getDays());
                    cell33.setCellStyle(headerStyle1);

                    HSSFCell cell34 = xlsRow.createCell((short) 34);
                    cell34.setCellValue(flowInfo.getLogic());
                    cell34.setCellStyle(headerStyle1);
                    HSSFCell cell35 = xlsRow.createCell((short) 35);
                    cell35.setCellValue(flowInfo.getWdays());
                    cell35.setCellStyle(headerStyle1);
                }else if(Environment.getInstance().isBhODSUserSwitch()){
                    HSSFCell cell33 = xlsRow.createCell((short) 33);
                    cell33.setCellValue(flowInfo.getPerformUser());
                    cell33.setCellStyle(headerStyle1);
                    HSSFCell cell34 = xlsRow.createCell((short) 34);
                    cell34.setCellValue(flowInfo.getVirtualName());
                    cell34.setCellStyle(headerStyle1);
/*                    HSSFCell cell35 = xlsRow.createCell((short) 35);
                    cell35.setCellValue(flowInfo.getOkFileName());
                    cell35.setCellStyle(headerStyle1);*/
                    HSSFCell cell35 = xlsRow.createCell((short) 35);
                    cell35.setCellValue(flowInfo.getUserTask());
                    cell35.setCellStyle(headerStyle1);
                    HSSFCell cell36 = xlsRow.createCell((short) 36);
                    cell36.setCellValue(flowInfo.getDays());
                    cell36.setCellStyle(headerStyle1);
                    HSSFCell cell37 = xlsRow.createCell((short) 37);
                    cell37.setCellValue(flowInfo.getLogic());
                    cell37.setCellStyle(headerStyle1);
                    HSSFCell cell38 = xlsRow.createCell((short) 38);
                    cell38.setCellValue(flowInfo.getWdays());
                    cell38.setCellStyle(headerStyle1);
                    HSSFCell cell39 = xlsRow.createCell((short) 39);
                    cell39.setCellValue(flowInfo.getMonth());
                    cell39.setCellStyle(headerStyle1);
                }else if( Environment.getInstance().getXanxSwitch()) {//西安导入
                    HSSFCell cell34 = xlsRow.createCell((short) 33);
                    cell34.setCellValue(flowInfo.getUserTask());
                    cell34.setCellStyle(headerStyle1);
                    HSSFCell cell35 = xlsRow.createCell((short) 34);
                    cell35.setCellValue(flowInfo.getDays());
                    cell35.setCellStyle(headerStyle1);
                    HSSFCell cell36 = xlsRow.createCell((short) 35);
                    cell36.setCellValue(flowInfo.getLogic());
                    cell36.setCellStyle(headerStyle1);
                    HSSFCell cell37 = xlsRow.createCell((short) 36);
                    cell37.setCellValue(flowInfo.getWdays());
                    cell37.setCellStyle(headerStyle1);
                    HSSFCell cell38 = xlsRow.createCell((short) 37);
                    cell38.setCellValue(flowInfo.getMonth());
                    cell38.setCellStyle(headerStyle1);
                }else {
/*                    HSSFCell cell33 = xlsRow.createCell((short) 33);
                    cell33.setCellValue(flowInfo.getOkFileName());
                    cell33.setCellStyle(headerStyle1);*/
                    HSSFCell cell34 = xlsRow.createCell((short) 33);
                    cell34.setCellValue(flowInfo.getUserTask());
                    cell34.setCellStyle(headerStyle1);
                    HSSFCell cell35 = xlsRow.createCell((short) 34);//DAYS:指定批量日期执行1-31
                    cell35.setCellValue(flowInfo.getDays());
                    cell35.setCellStyle(headerStyle1);
                    HSSFCell cell36 = xlsRow.createCell((short) 35);//LOGIC:条件逻辑符AND/OR，AND多个条件均满足，OR多个条件满足一个即可
                    cell36.setCellValue(flowInfo.getLogic());
                    cell36.setCellStyle(headerStyle1);
                    HSSFCell cell37 = xlsRow.createCell((short) 36);//WDAYS:指定星期执行0-6，批量对应的星期一至星期日
                    cell37.setCellValue(flowInfo.getWdays());
                    cell37.setCellStyle(headerStyle1);
                    HSSFCell cell38 = xlsRow.createCell((short) 37);//MONTH:指定月份执行1-12
                    cell38.setCellValue(flowInfo.getMonth());
                    cell38.setCellStyle(headerStyle1);
                    if( Environment.getInstance().getGYBankSwitch()){
                        HSSFCell cell39 = xlsRow.createCell((short) 38);//延时方式
                        String delayType= TimeDelayConfigManager.getInstance().queryDelayerTimeConfigType(flowInfo.getSystem(),flowInfo.getChildproName(),flowInfo.getActName());
                        cell39.setCellValue(delayType);
                        cell39.setCellStyle(headerStyle1);
                    }
                }
            }

            // offsetIndex(sheet, indexMap);

            // int j = 0;
            // j = rowNum++;
            // HSSFCellStyle headerStyleTitle1 = wb.createCellStyle();
            // headerStyleTitle1.setVerticalAlignment(VerticalAlignment.TOP);
            // sheet.addMergedRegion(new CellRangeAddress((short) j, (short) 0, (short) j + 1, (short) 13));
            // HSSFRow xlsRowTitle1 = sheet.createRow(j);
            // HSSFCell cellTitle1 = xlsRowTitle1.createCell((short) 0);
            // cellTitle1.setEncoding(HSSFCell.ENCODING_UTF_16);
            // cellTitle1.setCellValue("说明: 工作流中共有" + num + "个活动，如下：" + act);
            // cellTitle1.setCellStyle(headerStyleTitle1);
            wb.write(out);
            _log.info("Export file:" + prjName);
        } catch (Exception e)
        {
            _log.error("doExport method of FlowInfoAction.class to export excel for all result!", e);
        } finally
        {
            try
            {
                if (out != null)
                {
                    out.close();
                }
            } catch (IOException e)
            {
                _log.error("doExport method of FlowInfoAction.class to export excelout.close !", e);
            }
        }
    }

    /**   
     * @Title: doExportXLS   
     * @Description: 导出操作方法   
     * @param out
     * @param list
     * @param prjName
     * @param map
     * @throws JspException      
     * @author: yue_sun 
     * @date:   2018年2月26日 上午8:56:05   
     */
    public static String doExportXLS(OutputStream out, List<ActRelationInfo> list, Map returnMap, String prjName,
                                   Map<String, FlowInfoData> map, HttpServletResponse response)
    {
        FileOutputStream outer = null;
        String uuid = "";
        try
        {
            String shellH = "";
            String shellEndTag = "";

            for (int i = 0; i < list.size(); i++)
            {
                ActRelationInfo flowInfo = list.get(i);
                if (flowInfo==null){
                    continue;
                }
                shellH = flowInfo.getShellHouse();
                shellEndTag = flowInfo.getEndTag();
                i = list.size();
            }
            Map<String, String> indexMap = new HashMap<String, String>();
            HSSFSheet sheet = null;
            HSSFWorkbook wb = new HSSFWorkbook();
            sheet = wb.createSheet();
            wb.setSheetName(0, "作业基本关系");
            int rowNum = 4;
            sheet.setColumnWidth((short) 0, (short) 3000);
            sheet.setColumnWidth((short) 1, (short) 4000);
            sheet.setColumnWidth((short) 2, (short) 4000);
            sheet.setColumnWidth((short) 3, (short) 4000);
            sheet.setColumnWidth((short) 4, (short) 4000);
            sheet.setColumnWidth((short) 5, (short) 4000);
            sheet.setColumnWidth((short) 6, (short) 3000);
            sheet.setColumnWidth((short) 7, (short) 4000);
            sheet.setColumnWidth((short) 8, (short) 3000);
            sheet.setColumnWidth((short) 9, (short) 3000);
            sheet.setColumnWidth((short) 10, (short) 4000);
            sheet.setColumnWidth((short) 11, (short) 3000);
            sheet.setColumnWidth((short) 12, (short) 4000);
            sheet.setColumnWidth((short) 13, (short) 4000);
            sheet.setColumnWidth((short) 14, (short) 4000);
            // add
            sheet.setColumnWidth((short) 15, (short) 4000);
            sheet.setColumnWidth((short) 16, (short) 4000);
            sheet.setColumnWidth((short) 17, (short) 4000);
            sheet.setColumnWidth((short) 18, (short) 4000);
            sheet.setColumnWidth((short) 19, (short) 4000);
            // update by liyang, To add apt interface info
            sheet.setColumnWidth((short) 20, (short) 4000);// 优先级
            sheet.setColumnWidth((short) 21, (short) 4000);// 权重
            sheet.setColumnWidth((short) 22, (short) 4000);
            sheet.setColumnWidth((short) 23, (short) 4000);
            sheet.setColumnWidth((short) 24, (short) 4000);
            sheet.setColumnWidth((short) 25, (short) 4000);
            sheet.setColumnWidth((short) 26, (short) 4000);
            sheet.setColumnWidth((short) 27, (short) 4000);
            sheet.setColumnWidth((short) 28, (short) 4000);
            sheet.setColumnWidth((short) 29, (short) 4000);
            if (Environment.getInstance().getDgProjectParamSwitch()){
                sheet.setColumnHidden((short) 26, true);
                sheet.setColumnHidden((short) 27, true);
                sheet.setColumnHidden((short) 28, true);
                sheet.setColumnHidden((short) 29, true);
            }
            sheet.setColumnWidth((short) 30, (short) 4000);
            sheet.setColumnWidth((short) 31, (short) 4000);
            sheet.setColumnWidth((short) 32, (short) 4000);
            if(ServerEnv.getServerEnv().isJHBankSwitch()){
                sheet.setColumnWidth((short) 33, (short) 4000);
                sheet.setColumnWidth((short) 34, (short) 4000);
                sheet.setColumnWidth((short) 35, (short) 4000);
            }else if(Environment.getInstance().isBhODSUserSwitch()){
                sheet.setColumnWidth((short) 33, (short) 4000);
                sheet.setColumnWidth((short) 34, (short) 4000);
                sheet.setColumnWidth((short) 35, (short) 4000);
         /*       sheet.setColumnWidth((short) 36, (short) 4000);*/
            }else {
                sheet.setColumnWidth((short) 33, (short) 4000);
   /*             sheet.setColumnWidth((short) 34, (short) 4000);*/
            }
            
            // end update
            HSSFCellStyle headerStyle = null;
            HSSFCellStyle headerStyle1 = null;
            HSSFCellStyle headerStyle2 = null;
            HSSFCellStyle headerStyle3 = null;
            HSSFCellStyle headerStyle4 = null;
            HSSFCellStyle headerStyleTitle = null;
            if (true)
            {
                // Create an header row
                sheet.addMergedRegion(new CellRangeAddress(0, 0, (short) 0, (short) 28));
                HSSFFont font = wb.createFont();
                font.setFontHeightInPoints((short) 10); // 字体大小
                font.setBold(true); // 粗体
                font.setColor(IndexedColors.BLACK.getIndex()); // 绿字
                headerStyleTitle = wb.createCellStyle();
                headerStyleTitle.setAlignment(HorizontalAlignment.LEFT);
                headerStyleTitle.setVerticalAlignment(VerticalAlignment.CENTER);
                headerStyleTitle.setFont(font);
                headerStyleTitle.setWrapText(true);

                HSSFRow xlsRowTitle = sheet.createRow(0);
                HSSFCell cellTitle = xlsRowTitle.createCell((short) 0);
                // cellTitle.setEncoding(HSSFCell.ENCODING_UTF_16);
                cellTitle.setCellValue("说明:\n" + "        填充色为紫色代表开发中心项目组必填项；\n" + "        填充色为蓝色代表开发中心项目组选填项；\n"
                        + "        填充色为黄色代表开发中心项目组必填项。");
                cellTitle.setCellStyle(headerStyleTitle);
                xlsRowTitle.setHeightInPoints(60); // 设置行高

                headerStyle = wb.createCellStyle();
                headerStyle.setFillPattern(SOLID_FOREGROUND);
                headerStyle.setFillForegroundColor(IndexedColors.LAVENDER.getIndex());
                headerStyle.setFont(font);
                headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                headerStyle.setWrapText(true);

                headerStyle2 = wb.createCellStyle();
                headerStyle2.setFillPattern(SOLID_FOREGROUND);
                headerStyle2.setFillForegroundColor(IndexedColors.AQUA.getIndex());
                headerStyle2.setVerticalAlignment(VerticalAlignment.CENTER);
                headerStyle2.setFont(font);
                headerStyle2.setWrapText(true);
                // headerStyle2.setFillPattern(SOLID_FOREGROUND);

                headerStyle3 = wb.createCellStyle();
                headerStyle3.setFillPattern(SOLID_FOREGROUND);
                headerStyle3.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
                headerStyle3.setVerticalAlignment(VerticalAlignment.CENTER);
                headerStyle3.setFont(font);
                headerStyle3.setWrapText(true);

                headerStyle4 = wb.createCellStyle();
                headerStyle4.setFillPattern(SOLID_FOREGROUND);
                headerStyle4.setFillForegroundColor(IndexedColors.LIGHT_GREEN.getIndex());
                headerStyle4.setVerticalAlignment(VerticalAlignment.CENTER);
                headerStyle4.setFont(font);
                headerStyle4.setWrapText(true);

                headerStyle1 = wb.createCellStyle();
                headerStyle1.setWrapText(true);

                HSSFRow xlsRow1 = sheet.createRow(2);
                xlsRow1.setHeightInPoints(30); // 设置行高
                HSSFCell cellTitle0 = xlsRow1.createCell((short) 0);
                // cellTitle0.setEncoding(HSSFCell.ENCODING_UTF_16);
                cellTitle0.setCellValue("脚本外壳");
                cellTitle0.setCellStyle(headerStyle);

             //   sheet.addMergedRegion(new CellRangeAddress(2, 2, (short) 1, (short) 3));
                HSSFCell cellTitle1 = xlsRow1.createCell((short) 1);
                // cellTitle1.setEncoding(HSSFCell.ENCODING_UTF_16);
                cellTitle1.setCellValue(shellH);
                cellTitle1.setCellStyle(headerStyle1);

                HSSFCell cellTitle2 = xlsRow1.createCell((short) 2);
                // cellTitle1.setEncoding(HSSFCell.ENCODING_UTF_16);
                cellTitle2.setCellValue("结束标识");
                cellTitle2.setCellStyle(headerStyle);

              //  sheet.addMergedRegion(new CellRangeAddress(2, 2, (short) 5, (short) 7));
                HSSFCell cellTitle3 = xlsRow1.createCell((short) 3);
                // cellTitle1.setEncoding(HSSFCell.ENCODING_UTF_16);
                cellTitle3.setCellValue(shellEndTag);
                cellTitle3.setCellStyle(headerStyle1);

                HSSFRow xlsRow2 = sheet.createRow(1);
                xlsRow2.setHeightInPoints(30); // 设置行高
                HSSFCell cellTitleProjectName = xlsRow2.createCell((short) 0);
                // cellTitleProjectName.setEncoding(HSSFCell.ENCODING_UTF_16);
                cellTitleProjectName.setCellValue("工程名称");
                cellTitleProjectName.setCellStyle(headerStyle);

                sheet.addMergedRegion(new CellRangeAddress(1, 1, (short) 1, (short) 28));
                HSSFCell cellTitleProjectName1 = xlsRow2.createCell((short) 1);
                // cellTitleProjectName1.setEncoding(HSSFCell.ENCODING_UTF_16);
                cellTitleProjectName1.setCellValue(prjName);
                cellTitleProjectName1.setCellStyle(headerStyleTitle);

                // HSSFFont bold = wb.createFont();
                // bold.setBold(true);
                // bold.setColor(IndexedColors.WHITE.getIndex());
                // headerStyle.setFont(bold);

                HSSFRow xlsRow = sheet.createRow(3);
                xlsRow.setHeightInPoints(25); // 设置行高

                HSSFCell cell = xlsRow.createCell((short) 0);
                // cell.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell.setCellValue("操作步骤序号");
                cell.setCellStyle(headerStyle);

                HSSFCell cell1 = xlsRow.createCell((short) 1);
                // cell1.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell1.setCellValue("所属系统");
                cell1.setCellStyle(headerStyle);

                HSSFCell cell2 = xlsRow.createCell((short) 2);
                // cell2.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell2.setCellValue("子系统名称");
                cell2.setCellStyle(headerStyle);

                HSSFCell cell3 = xlsRow.createCell((short) 3);
                // cell3.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell3.setCellValue("作业名称");
                cell3.setCellStyle(headerStyle);

                HSSFCell cell4 = xlsRow.createCell((short) 4);
                // cell4.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell4.setCellValue("作业描述");
                cell4.setCellStyle(headerStyle);

                HSSFCell cell5 = xlsRow.createCell((short) 5);
                // cell5.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell5.setCellValue("触发作业");
                cell5.setCellStyle(headerStyle);

                HSSFCell cell6 = xlsRow.createCell((short) 6);
                // cell6.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell6.setCellValue("依赖作业");
                cell6.setCellStyle(headerStyle);

                HSSFCell cell7 = xlsRow.createCell((short) 7);
                // cell7.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell7.setCellValue("是否删除");
                cell7.setCellStyle(headerStyle);

                HSSFCell cell8 = xlsRow.createCell((short) 8);
                // cell8.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell8.setCellValue("OK文件绝对路径");
                cell8.setCellStyle(headerStyle);

                HSSFCell cell9 = xlsRow.createCell((short) 9);
                // cell9.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell9.setCellValue("OK文件检测周期");
                cell9.setCellStyle(headerStyle);
                // HSSFCell cell9 = xlsRow.createCell((short) 9);
                // cell9.setEncoding(HSSFCell.ENCODING_UTF_16);
                // cell9.setCellValue("脚本外壳");
                // cell9.setCellStyle(headerStyle);

                HSSFCell cell10 = xlsRow.createCell((short) 10);
                // cell10.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell10.setCellValue("脚本名称");
                cell10.setCellStyle(headerStyle);

                HSSFCell cell11 = xlsRow.createCell((short) 11);
                // cell11.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell11.setCellValue("输出参数");
                cell11.setCellStyle(headerStyle);
                // HSSFCell cell12 = xlsRow.createCell((short) 12);
                // cell12.setEncoding(HSSFCell.ENCODING_UTF_16);
                // cell12.setCellValue("脚本执行成功最后一行打印值");
                // cell12.setCellStyle(headerStyle);

                HSSFCell cell12 = xlsRow.createCell((short) 12);
                // cell12.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell12.setCellValue("业务异常自动重试");
                cell12.setCellStyle(headerStyle);

                HSSFCell cell29 = xlsRow.createCell((short) 13);
                // cell12.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell29.setCellValue("业务异常自动忽略");
                cell29.setCellStyle(headerStyle);

                HSSFCell cell13 = xlsRow.createCell((short) 14);
                // cell13.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell13.setCellValue("Agent资源组");
                cell13.setCellStyle(headerStyle);

                HSSFCell cell14 = xlsRow.createCell((short) 15);
                // cell14.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell14.setCellValue("Agent文件检测资源组");
                cell14.setCellStyle(headerStyle);

                // add
                HSSFCell cell15 = xlsRow.createCell((short) 16);// AgentIP:端口
                cell15.setCellValue("AgentIP:端口");
                cell15.setCellStyle(headerStyle);

                HSSFCell cell16 = xlsRow.createCell((short) 17);// 权重
                cell16.setCellValue("延迟运行");
                cell16.setCellStyle(headerStyle);

                HSSFCell cell17 = xlsRow.createCell((short) 18);
                cell17.setCellValue("执行条件");
                cell17.setCellStyle(headerStyle);

                HSSFCell cell18 = xlsRow.createCell((short) 19);
                cell18.setCellValue("重试次数");
                cell18.setCellStyle(headerStyle);

                HSSFCell cell30 = xlsRow.createCell((short) 20);
                cell30.setCellValue("重试间隔");
                cell30.setCellStyle(headerStyle);

                HSSFCell cell31 = xlsRow.createCell((short) 21);
                cell31.setCellValue("重试结束时间");
                cell31.setCellStyle(headerStyle);

                HSSFCell cell32 = xlsRow.createCell((short) 22);
                cell32.setCellValue("延时报警");
                cell32.setCellStyle(headerStyle);

                HSSFCell cell19 = xlsRow.createCell((short) 23);
                cell19.setCellValue("日历名称");
                cell19.setCellStyle(headerStyle);

                HSSFCell cell20 = xlsRow.createCell((short) 24);// 优先级
                cell20.setCellValue("优先级");
                cell20.setCellStyle(headerStyle);

                HSSFCell cell21 = xlsRow.createCell((short) 25);// 权重
                cell21.setCellValue("权重");
                cell21.setCellStyle(headerStyle);

                HSSFCell cell22 = xlsRow.createCell((short) 26);
                cell22.setCellValue("APT组名称");
                cell22.setCellStyle(headerStyle3);

                HSSFCell cell23 = xlsRow.createCell((short) 27);
                cell23.setCellValue("APT文件名称");
                cell23.setCellStyle(headerStyle3);

                HSSFCell cell24 = xlsRow.createCell((short) 28);
                cell24.setCellValue("ISDB2");
                cell24.setCellStyle(headerStyle3);

                HSSFCell cell25 = xlsRow.createCell((short) 29);
                cell25.setCellValue("DB2IP");
                cell25.setCellStyle(headerStyle3);

                HSSFCell cell26 = xlsRow.createCell((short) 30);
                cell26.setCellValue("主线名称");
                cell26.setCellStyle(headerStyle);

                HSSFCell cell27 = xlsRow.createCell((short) 31);
                cell27.setCellValue("头尾标记");
                cell27.setCellStyle(headerStyle2);

                HSSFCell cell28 = xlsRow.createCell((short) 32);
                cell28.setCellValue("是否禁用");
                cell28.setCellStyle(headerStyle);

                if(ServerEnv.getServerEnv().isJHBankSwitch()){
                    
                    //交行新增功能
                    HSSFCell cell33 = xlsRow.createCell((short) 33);
                    cell33.setCellValue("DAYS");
                    cell33.setCellStyle(headerStyle);
                    
                    HSSFCell cell34 = xlsRow.createCell((short) 34);
                    cell34.setCellValue("LOGIC");
                    cell34.setCellStyle(headerStyle);
                    
                    HSSFCell cell35 = xlsRow.createCell((short) 35);
                    cell35.setCellValue("WDAYS");
                    cell35.setCellStyle(headerStyle);
                }else if(Environment.getInstance().isBhODSUserSwitch()){
                    HSSFCell cell33 = xlsRow.createCell((short) 33);
                    cell33.setCellValue("执行用户");
                    cell33.setCellStyle(headerStyle);
                    HSSFCell cell34 = xlsRow.createCell((short) 34);
                    cell34.setCellValue("虚拟组名称");
                    cell34.setCellStyle(headerStyle);
/*                    HSSFCell cell35 = xlsRow.createCell((short) 35);
                    cell35.setCellValue("ok文件名称");
                    cell35.setCellStyle(headerStyle);*/
                    HSSFCell cell36 = xlsRow.createCell((short) 35);
                    cell36.setCellValue("人工提醒");
                    cell36.setCellStyle(headerStyle);
                    //法时
                    //DAYS:指定批量日期执行1-31
                    HSSFCell cell37 = xlsRow.createCell((short) 36);
                    cell37.setCellValue("DAYS");
                    cell37.setCellStyle(headerStyle);
                    //LOGIC:条件逻辑符AND/OR，AND多个条件均满足，OR多个条件满足一个即可
                    HSSFCell cell38 = xlsRow.createCell((short) 37);
                    cell38.setCellValue("LOGIC");
                    cell38.setCellStyle(headerStyle);
                    //WDAYS:指定星期执行0-6，批量对应的星期一至星期日
                    HSSFCell cell39 = xlsRow.createCell((short) 38);
                    cell39.setCellValue("WDAYS");
                    cell39.setCellStyle(headerStyle);
                    //MONTH:指定月份执行1-12
                    HSSFCell cell40 = xlsRow.createCell((short) 39);
                    cell40.setCellValue("MONTH");
                    cell40.setCellStyle(headerStyle);
                }else if( Environment.getInstance().getXanxSwitch()) //西安
                {
                    HSSFCell cell34 = xlsRow.createCell((short) 33);
                    cell34.setCellValue("人工提醒");
                    cell34.setCellStyle(headerStyle);
                    //交行新增功能
                    HSSFCell cell35 = xlsRow.createCell((short) 34);
                    cell35.setCellValue("DAYS");
                    cell35.setCellStyle(headerStyle);

                    HSSFCell cell36 = xlsRow.createCell((short) 35);
                    cell36.setCellValue("LOGIC");
                    cell36.setCellStyle(headerStyle);

                    HSSFCell cell37 = xlsRow.createCell((short) 36);
                    cell37.setCellValue("WDAYS");
                    cell37.setCellStyle(headerStyle);
                    HSSFCell cell38 = xlsRow.createCell((short) 37);
                    cell38.setCellValue("MONTH");
                    cell38.setCellStyle(headerStyle);
                }else {
/*                    HSSFCell cell33 = xlsRow.createCell((short) 33);
                    cell33.setCellValue("ok文件名称");
                    cell33.setCellStyle(headerStyle);*/
                    HSSFCell cell34 = xlsRow.createCell((short) 33);
                    cell34.setCellValue("人工提醒");
                    cell34.setCellStyle(headerStyle);
                    //法时
                    //DAYS:指定批量日期执行1-31
                    HSSFCell cell35 = xlsRow.createCell((short) 34);
                    cell35.setCellValue("DAYS");
                    cell35.setCellStyle(headerStyle);
                    //LOGIC:条件逻辑符AND/OR，AND多个条件均满足，OR多个条件满足一个即可
                    HSSFCell cell36 = xlsRow.createCell((short) 35);
                    cell36.setCellValue("LOGIC");
                    cell36.setCellStyle(headerStyle);
                    //WDAYS:指定星期执行0-6，批量对应的星期一至星期日
                    HSSFCell cell37 = xlsRow.createCell((short) 36);
                    cell37.setCellValue("WDAYS");
                    cell37.setCellStyle(headerStyle);
                    //MONTH:指定月份执行1-12
                    HSSFCell cell48 = xlsRow.createCell((short) 37);
                    cell48.setCellValue("MONTH");
                    cell48.setCellStyle(headerStyle);
                    HSSFCell cell49 = xlsRow.createCell((short) 38);
                    cell49.setCellValue("作业类型");
                    cell49.setCellStyle(headerStyle);
                    HSSFCell cell50 = xlsRow.createCell((short) 39);
                    cell50.setCellValue("执行用户");
                    cell50.setCellStyle(headerStyle);
                    if( Environment.getInstance().getGYBankSwitch()){
                        //MONTH:指定月份执行1-12
                        HSSFCell cell51 = xlsRow.createCell((short) 40);
                        cell51.setCellValue("延迟方式");
                        cell51.setCellStyle(headerStyle);
                    }else if(Environment.getInstance().getDGBankSwitch()){
                        HSSFCell cell52 = xlsRow.createCell((short) 41);
                        cell52.setCellValue("作业参数");
                        cell52.setCellStyle(headerStyle);
                    }

                }
            }
            String act = "";
            int num = 0;
            // int offset = 1;
            for (int i = 0; i < list.size(); i++)
            {
                ActRelationInfo flowInfo = list.get(i);
                // if(!flowInfo.getFlowName().equals(tempFlowName))
                // {
                // tempFlowName = flowInfo.getFlowName();
                // offset = i+1;
                // }
                if (flowInfo==null){
                    continue;
                }
                indexMap.put(flowInfo.getActID(), String.valueOf(i + 1));
                HSSFRow xlsRow = sheet.createRow(rowNum++);
                xlsRow.setHeightInPoints(25); // 设置行高

                if(Environment.getInstance().getGYBankSwitch()){
                    HSSFCell cell0 = xlsRow.createCell((short) 0);
                    // cell0.setEncoding(HSSFCell.ENCODING_UTF_16);
                    cell0.setCellValue(flowInfo.getActNo());
                    cell0.setCellStyle(headerStyle1);
                }else{
                    HSSFCell cell0 = xlsRow.createCell((short) 0);
                    // cell0.setEncoding(HSSFCell.ENCODING_UTF_16);
                    cell0.setCellValue(flowInfo.getId());
                    cell0.setCellStyle(headerStyle1);
                }

                HSSFCell cell1 = xlsRow.createCell((short) 1);
                // cell1.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell1.setCellValue(flowInfo.getSystem());
                cell1.setCellStyle(headerStyle1);

                HSSFCell cell2 = xlsRow.createCell((short) 2);
                // cell2.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell2.setCellValue(flowInfo.getChildproName());
                cell2.setCellStyle(headerStyle1);

                HSSFCell cell3 = xlsRow.createCell((short) 3);
                // cell3.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell3.setCellValue(flowInfo.getActName());
                cell3.setCellStyle(headerStyle1);

                HSSFCell cell4 = xlsRow.createCell((short) 4);
                // cell4.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell4.setCellValue(flowInfo.getActDesc());
                cell4.setCellStyle(headerStyle1);

                HSSFCell cell5 = xlsRow.createCell((short) 5);
                if(!Environment.getInstance().isBhODSEXPORTRELYONNAME()) {
                cell5.setCellValue(flowInfo.getPreAct());
                cell5.setCellStyle(headerStyle1);
                }
                HSSFCell cell6 = xlsRow.createCell((short) 6);
                // cell6.setEncoding(HSSFCell.ENCODING_UTF_16);
                if(Environment.getInstance().isBhODSUserSwitch()){
                    boolean cz=false;
                    if(!"".equals(flowInfo.getJoblist())&&null!=flowInfo.getJoblist()){
                        for (String s : flowInfo.getJoblist().split(",")) {
                            Map<String, String> VirtualkeyMap = (Map<String, String>) returnMap.get("VirtualkeyMap");
                            if(VirtualkeyMap.containsKey(s)){//判断是否有虚拟组存在
                                cz=true;
                            }else {

                            }
                        }
                    }
                    if(cz){
                        String Virtualname="";
                        if(Environment.getInstance().isBhODSEXPORTRELYONNAME()){
                            for (int j = 0; j < list.size(); j++)
                            {
                                for (String s : flowInfo.getJoblist().split(",")) {
                                    ActRelationInfo flowInfoj = list.get(j);
                                    if(s.equals(flowInfoj.getSequenceNumber())){
                                        boolean status = Virtualname.contains(flowInfoj.getActName());
                                        if(status){

                                        }else {
                                            Virtualname=   flowInfoj.getActName() + "," + Virtualname;
                                        }

                                    }else if(s.equals(flowInfoj.getId())){
                                        boolean status = Virtualname.contains(flowInfoj.getActName());
                                        if(status){

                                        }else {
                                            Virtualname=   flowInfoj.getActName() + "," + Virtualname;
                                        }
                                    }else if(s.equals(flowInfoj.getActName())){
                                        boolean status = Virtualname.contains(flowInfoj.getActName());
                                        if(status){

                                        }else {
                                            Virtualname=   flowInfoj.getActName() + "," + Virtualname;
                                        }
                                    }else {
                                        boolean status = Virtualname.contains(s);
                                        if(status){

                                        }else {
                                            Map<String, String> VirtualkeyMap = (Map<String, String>) returnMap.get("VirtualkeyMap");
                                            if(VirtualkeyMap.containsKey(s)){//判断是否有虚拟组存在
                                            Virtualname=  s + "," + Virtualname;
                                            }
                                        }
                                    }

                                }
                            }
                            if(Virtualname.contains(",")){
                                Virtualname = Virtualname.substring(0,Virtualname.length() - 1);
                            }
                            cell6.setCellValue(Virtualname);
                            cell6.setCellStyle(headerStyle1);
                        }else {

                                for (String s : flowInfo.getJoblist().split(",")) {
                                    for (int j = 0; j < list.size(); j++)
                                    {
                                    ActRelationInfo flowInfoj = list.get(j);
                                    if(s.contains(flowInfoj.getSequenceNumber())){
                                        boolean status = Virtualname.contains(flowInfoj.getId());
                                        if(status){

                                        }else {
                                        Virtualname=   flowInfoj.getId() + "," + Virtualname;
                                        }
                                    }else if(s.equals(flowInfoj.getActName())){
                                        boolean status = Virtualname.contains(flowInfoj.getId());
                                        if(status){

                                        }else {
                                            Virtualname = flowInfoj.getId() + "," + Virtualname;
                                        }
                                    }else if(s.equals(flowInfoj.getId())){
                                        boolean status = Virtualname.contains(flowInfoj.getId());
                                        if(status){

                                        }else {
                                            Virtualname=   flowInfoj.getId() + "," + Virtualname;
                                        }
                                    }else {
                                        boolean status = Virtualname.contains(s);
                                        if (status) {

                                        } else {
                                            Map<String, String> VirtualkeyMap = (Map<String, String>) returnMap.get("VirtualkeyMap");
                                            if(VirtualkeyMap.containsKey(s)){//判断是否有虚拟组存在
                                            Virtualname = s + "," + Virtualname;
                                            }
                                        }
                                    }
                                }
                            }
                                if(Virtualname.contains(",")){
                                    Virtualname = Virtualname.substring(0,Virtualname.length() - 1);
                                }

                            cell6.setCellValue(Virtualname);
                            cell6.setCellStyle(headerStyle1);
                        }
                    }else {
                        cell6.setCellValue(flowInfo.getSuccAct());
                        cell6.setCellStyle(headerStyle1);
                    }
                }else {
                    cell6.setCellValue(flowInfo.getSuccAct());
                    cell6.setCellStyle(headerStyle1);
                }

                HSSFCell cell7 = xlsRow.createCell((short) 7);
                // cell7.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell7.setCellValue("否");

                // RangeAddress regions = new RangeAddress(0,0,0,0);

                // //生成下拉框内容
                // String [] list1={"11","34"};
                //
                // DVConstraint constraint = DVConstraint.createExplicitListConstraint(list);
                //
                // //绑定下拉框和作用区域
                //
                // //HSSFDataValidation data_validation = new
                // HSSFDataValidation((short)(1),(short)1,(short)(1),(short)1);
                //
                // HSSFDataValidation data_validation = new HSSFDataValidation(regions,constraint);
                //
                // //对sheet页生效
                //
                // sheet.addValidationData(data_validation);
                //
                HSSFCell cell8 = xlsRow.createCell((short) 8);
                // cell8.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell8.setCellValue(flowInfo.getOkFilePath());
                cell8.setCellStyle(headerStyle1);

                HSSFCell cell9 = xlsRow.createCell((short) 9);
                // cell9.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell9.setCellValue("0".equals(flowInfo.getOkFileWeek()) ? "" : flowInfo.getOkFileWeek());
                cell9.setCellStyle(headerStyle1);
                // HSSFCell cell9 = xlsRow.createCell((short) 9);
                // cell9.setEncoding(HSSFCell.ENCODING_UTF_16);
                // cell9.setCellValue(flowInfo.getShellHouse());
                HSSFCell cell10 = xlsRow.createCell((short) 10);
                // cell10.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell10.setCellValue(flowInfo.getShellName());
                cell10.setCellStyle(headerStyle1);

                HSSFCell cell11 = xlsRow.createCell((short) 11);
                // cell11.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell11.setCellValue(flowInfo.getOutPara());
                cell11.setCellStyle(headerStyle1);
                // HSSFCell cell12 = xlsRow.createCell((short) 12);
                // cell12.setEncoding(HSSFCell.ENCODING_UTF_16);
                // cell12.setCellValue(flowInfo.getLastLine());

                HSSFCell cell12 = xlsRow.createCell((short) 12);
                // cell12.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell12.setCellValue("0".equals(flowInfo.getRedo()) ? "否" : "是");
                cell12.setCellStyle(headerStyle1);

                HSSFCell cell29 = xlsRow.createCell((short) 13);
                // cell12.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell29.setCellValue("0".equals(flowInfo.getSkip()) ? "否" : "是");
                cell29.setCellStyle(headerStyle1);

                HSSFCell cell13 = xlsRow.createCell((short) 14);
                // cell13.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell13.setCellValue(flowInfo.isAgentGroup() ? flowInfo.getAgentGroup() : "");
                cell13.setCellStyle(headerStyle1);

                HSSFCell cell14 = xlsRow.createCell((short) 15);
                // cell14.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell14.setCellValue(flowInfo.getAgentCheckGroup());
                cell14.setCellStyle(headerStyle1);

                // AgentIP:端口 延迟运行 分支条件 重试次数 日历名称

                HSSFCell cell15 = xlsRow.createCell((short) 16);
                cell15.setCellValue(flowInfo.isAgentGroup() ? "" : flowInfo.getAgentGroup());
                cell15.setCellStyle(headerStyle1);

                HSSFCell cell16 = xlsRow.createCell((short) 17);
                // cellWeight.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell16.setCellValue(flowInfo.getDelayTime());
                cell16.setCellStyle(headerStyle1);

                HSSFCell cell17 = xlsRow.createCell((short) 18);
                // cell15.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell17.setCellValue(flowInfo.getBranchCondition());
                cell17.setCellStyle(headerStyle1);

                HSSFCell cell18 = xlsRow.createCell((short) 19);
                // cell16.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell18.setCellValue("0".equals(flowInfo.getReTryCount()) ? "" : flowInfo.getReTryCount());
                cell18.setCellStyle(headerStyle1);

                HSSFCell cell30 = xlsRow.createCell((short) 20);
                // cell16.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell30.setCellValue(flowInfo.getReTryTime());
                cell30.setCellStyle(headerStyle1);

                HSSFCell cell31 = xlsRow.createCell((short) 21);
                // cell16.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell31.setCellValue(flowInfo.getReTryEndTime());
                cell31.setCellStyle(headerStyle1);

                HSSFCell cell32 = xlsRow.createCell((short) 22);
                // cell16.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell32.setCellValue(flowInfo.getDelayWarnning());
                cell32.setCellStyle(headerStyle1);

                HSSFCell cell19 = xlsRow.createCell((short) 23);
                // cellPriority.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell19.setCellValue(flowInfo.getCalendName());
                cell19.setCellStyle(headerStyle1);

                HSSFCell cell20 = xlsRow.createCell((short) 24);
                // cellPriority.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell20.setCellValue(flowInfo.getPriority());
                cell20.setCellStyle(headerStyle1);

                HSSFCell cell21 = xlsRow.createCell((short) 25);
                // cell15.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell21.setCellValue(flowInfo.getWeights());
                cell21.setCellStyle(headerStyle1);

                HSSFCell cell22 = xlsRow.createCell((short) 26);
                // cell15.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell22.setCellValue(flowInfo.getAptGroupName());
                cell22.setCellStyle(headerStyle1);

                HSSFCell cell23 = xlsRow.createCell((short) 27);
                // cell16.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell23.setCellValue(flowInfo.getAptFileName());
                cell23.setCellStyle(headerStyle1);

                HSSFCell cell24 = xlsRow.createCell((short) 28);
                // cell17.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell24.setCellValue(flowInfo.getIsdb2());
                cell24.setCellStyle(headerStyle1);

                HSSFCell cell25 = xlsRow.createCell((short) 29);
                // cell18.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell25.setCellValue(flowInfo.getDb2ip());
                cell25.setCellStyle(headerStyle1);

                HSSFCell cell26 = xlsRow.createCell((short) 30);
                // cell20.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell26.setCellValue(flowInfo.getMainLineName());
                cell26.setCellStyle(headerStyle1);

                int hf = -1;
                if (!"".equals(flowInfo.getHeadFlag()))
                {
                    hf = Integer.parseInt(flowInfo.getHeadFlag());
                }
                String headflag = "";
                switch (hf)
                {
                    case 1:
                        headflag = "总头";
                        break;
                    case 2:
                        headflag = "主线头";
                        break;
                    case 6:
                        headflag = "总尾";
                        break;
                    case 7:
                        headflag = "主线尾";
                        break;
                }
                HSSFCell cell27 = xlsRow.createCell((short) 31);
                cell27.setCellValue(headflag);
                cell27.setCellStyle(headerStyle1);

                if (null != flowInfo.getActName())
                {
                    if (i == list.size() - 1)
                    {
                        act = act + flowInfo.getActName();
                    } else
                    {
                        act = act + flowInfo.getActName() + "、";
                    }
                    num = num + 1;
                }
                HSSFCell cell28 = xlsRow.createCell((short) 32);
                if ("无".equals(flowInfo.getIsDisabled())){
                    cell28.setCellValue("否");
                }else {
                    cell28.setCellValue(flowInfo.getIsDisabled());
                }
                cell28.setCellStyle(headerStyle1);

                if(ServerEnv.getServerEnv().isJHBankSwitch()){
                    
                    HSSFCell cell33 = xlsRow.createCell((short) 33);
                    cell33.setCellValue(flowInfo.getDays());
                    cell33.setCellStyle(headerStyle1);
                    
                    HSSFCell cell34 = xlsRow.createCell((short) 34);
                    cell34.setCellValue(flowInfo.getLogic());
                    cell34.setCellStyle(headerStyle1);
                    HSSFCell cell35 = xlsRow.createCell((short) 35);
                    cell35.setCellValue(flowInfo.getWdays());
                    cell35.setCellStyle(headerStyle1);
                }else if(Environment.getInstance().isBhODSUserSwitch()){
                    HSSFCell cell33 = xlsRow.createCell((short) 33);
                    cell33.setCellValue(flowInfo.getPerformUser());
                    cell33.setCellStyle(headerStyle1);
                    HSSFCell cell34 = xlsRow.createCell((short) 34);
                    cell34.setCellValue(flowInfo.getVirtualName());
                    cell34.setCellStyle(headerStyle1);
/*                    HSSFCell cell35 = xlsRow.createCell((short) 35);
                    cell35.setCellValue(flowInfo.getOkFileName());
                    cell35.setCellStyle(headerStyle1);*/
                    HSSFCell cell35 = xlsRow.createCell((short) 35);
                    cell35.setCellValue(flowInfo.getUserTask());
                    cell35.setCellStyle(headerStyle1);
                    HSSFCell cell36 = xlsRow.createCell((short) 36);
                    cell36.setCellValue(flowInfo.getDays());
                    cell36.setCellStyle(headerStyle1);
                    HSSFCell cell37 = xlsRow.createCell((short) 37);
                    cell37.setCellValue(flowInfo.getLogic());
                    cell37.setCellStyle(headerStyle1);
                    HSSFCell cell38 = xlsRow.createCell((short) 38);
                    cell38.setCellValue(flowInfo.getWdays());
                    cell38.setCellStyle(headerStyle1);
                    HSSFCell cell39 = xlsRow.createCell((short) 39);
                    cell39.setCellValue(flowInfo.getMonth());
                    cell39.setCellStyle(headerStyle1);
                }else if( Environment.getInstance().getXanxSwitch()) {//西安导入
                    HSSFCell cell34 = xlsRow.createCell((short) 33);
                    cell34.setCellValue(flowInfo.getUserTask());
                    cell34.setCellStyle(headerStyle1);
                    HSSFCell cell35 = xlsRow.createCell((short) 34);
                    cell35.setCellValue(flowInfo.getDays());
                    cell35.setCellStyle(headerStyle1);
                    HSSFCell cell36 = xlsRow.createCell((short) 35);
                    cell36.setCellValue(flowInfo.getLogic());
                    cell36.setCellStyle(headerStyle1);
                    HSSFCell cell37 = xlsRow.createCell((short) 36);
                    cell37.setCellValue(flowInfo.getWdays());
                    cell37.setCellStyle(headerStyle1);
                    HSSFCell cell38 = xlsRow.createCell((short) 37);
                    cell38.setCellValue(flowInfo.getMonth());
                    cell38.setCellStyle(headerStyle1);
                }else {
/*                    HSSFCell cell33 = xlsRow.createCell((short) 33);
                    cell33.setCellValue(flowInfo.getOkFileName());
                    cell33.setCellStyle(headerStyle1);*/
                    HSSFCell cell34 = xlsRow.createCell((short) 33);
                    cell34.setCellValue(flowInfo.getUserTask());
                    cell34.setCellStyle(headerStyle1);
                    HSSFCell cell35 = xlsRow.createCell((short) 34);//DAYS:指定批量日期执行1-31
                    cell35.setCellValue(flowInfo.getDays());
                    cell35.setCellStyle(headerStyle1);
                    HSSFCell cell36 = xlsRow.createCell((short) 35);//LOGIC:条件逻辑符AND/OR，AND多个条件均满足，OR多个条件满足一个即可
                    cell36.setCellValue(flowInfo.getLogic());
                    cell36.setCellStyle(headerStyle1);
                    HSSFCell cell37 = xlsRow.createCell((short) 36);//WDAYS:指定星期执行0-6，批量对应的星期一至星期日
                    cell37.setCellValue(flowInfo.getWdays());
                    cell37.setCellStyle(headerStyle1);
                    HSSFCell cell38 = xlsRow.createCell((short) 37);//MONTH:指定月份执行1-12
                    cell38.setCellValue(flowInfo.getMonth());
                    cell38.setCellStyle(headerStyle1);
                    HSSFCell cell39 = xlsRow.createCell((short) 38);
                    cell39.setCellValue(flowInfo.getJobtype());
                    cell39.setCellStyle(headerStyle1);
                    HSSFCell cell40 = xlsRow.createCell((short) 39);
                    cell40.setCellValue(flowInfo.getPerformUser());
                    cell40.setCellStyle(headerStyle1);
                    if( Environment.getInstance().getGYBankSwitch()){
                        HSSFCell cell41 = xlsRow.createCell((short) 40);//延时方式
                        String delayType= TimeDelayConfigManager.getInstance().queryDelayerTimeConfigType(flowInfo.getSystem(),flowInfo.getChildproName(),flowInfo.getActName());
                        cell41.setCellValue(delayType);
                        cell41.setCellStyle(headerStyle1);
                    }else if(Environment.getInstance().getDGBankSwitch()){
                        HSSFCell cell42 = xlsRow.createCell((short) 41);//作业参数
                        cell42.setCellValue(flowInfo.getActParams());
                        cell42.setCellStyle(headerStyle1);
                    }
                }
            }

            // offsetIndex(sheet, indexMap);

            // int j = 0;
            // j = rowNum++;
            // HSSFCellStyle headerStyleTitle1 = wb.createCellStyle();
            // headerStyleTitle1.setVerticalAlignment(VerticalAlignment.TOP);
            // sheet.addMergedRegion(new CellRangeAddress((short) j, (short) 0, (short) j + 1, (short) 13));
            // HSSFRow xlsRowTitle1 = sheet.createRow(j);
            // HSSFCell cellTitle1 = xlsRowTitle1.createCell((short) 0);
            // cellTitle1.setEncoding(HSSFCell.ENCODING_UTF_16);
            // cellTitle1.setCellValue("说明: 工作流中共有" + num + "个活动，如下：" + act);
            // cellTitle1.setCellStyle(headerStyleTitle1);
//            wb.write(out);
            //增加写入本地
            DmOprationResultMessage result = new DmOprationResultMessage();
            uuid = UUID.uuid();
            result.setMessage(uuid);
            result.setIsOk(true);
            File fileNew  = new File(Environment.getInstance().getIEAIHome() + File.separator +"downTemp" + File.separator + uuid);
            if (!fileNew.exists())
            {
                fileNew.mkdirs();
            }
            // 上传文件
            outer  = new FileOutputStream(fileNew.getAbsolutePath() + File.separator + prjName+".xls");
            wb.write(outer);
            outer .flush();
            responseData(response, JSON.toJSON(result).toString());
            _log.info("Export file:" + prjName);
        } catch (Exception e)
        {
            _log.error("doExport method of FlowInfoAction.class to export excel for all result!", e);
        } finally
        {
            try
            {
                if (out != null)
                {
                    out.close();
                }
                if (outer != null)
                {
                    outer.close();
                }
            } catch (IOException e)
            {
                _log.error("doExport method of FlowInfoAction.class to export excelout.close !", e);
            }
        }
        return uuid;
    }

    private List<FlowInfoData> exportPrjoect ( String prjName, Map<String, FlowInfoData> map )
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Comparator<BasicActElement> comparator = new Comparator<BasicActElement>()
        {
            @Override
            public int compare ( BasicActElement s1, BasicActElement s2 )
            {
                if (s1.getID() < s2.getID())
                    return 0;
                else
                    return 1;
            }
        };
        Project prj = null;
        List<FlowInfoData> ret = new ArrayList<FlowInfoData>();
        try
        {
            prj = ProjectAdaptorCache.getInstance().loadProject(prjName);
            Collection c = prj.getWorkflows();
            Iterator it = c.iterator();
            while (it.hasNext())
            {
                Workflow flow = (Workflow) it.next();
                List<BasicActElement> abc = flow.getActivities();
                Collections.sort(abc, comparator);
                FlowChartInfo fci = flow.getFlowChartInfo();
                for (BasicActElement ae : abc)
                {
                    FlowInfoData flowInfoObject = new FlowInfoData();
                    List<FlowEdgeInfo> edges = fci.getEdgeInfos();
                    Iterator<FlowEdgeInfo> ite = edges.iterator();
                    while (ite.hasNext())
                    {
                        FlowEdgeInfo fei = ite.next();
                        if (fei.getSourceID() == Long.valueOf(ae.getID()))
                        {
                            flowInfoObject.addNextActID(Long.parseLong(String.valueOf(fei.getTargetID())));
                        }
                        if (fei.getTargetID() == Long.valueOf(ae.getID()))
                        {
                            flowInfoObject.addPreActID(Long.parseLong(String.valueOf(fei.getSourceID())));
                        }
                    }
                    flowInfoObject.setActID(flow.getName() + "||" + ae.getID());
                    flowInfoObject.setFlowName(flow.getName());
                    flowInfoObject.setActName(ae.getName());
                    flowInfoObject.setFlowDes(ae.getDescription());
                    flowInfoObject.setFlowNote(firstMatchBracket(ae.getRemarkBodyText()).trim());
                    map.put(flowInfoObject.getActID(), flowInfoObject);
                    ret.add(flowInfoObject);
                }
            }
        } catch (UnMarshallingException e)
        {
            _log.error(method, e);
        }
        return ret;
    }

    public void doExport ( OutputStream out, List list, String prjName, Map<String, FlowInfoData> map )
    {

        try
        {
            Map<String, String> indexMap = new HashMap<String, String>();
            HSSFSheet sheet = null;
            HSSFWorkbook wb = new HSSFWorkbook();
            sheet = wb.createSheet("new Sheet");
            int rowNum = 3;
            sheet.setColumnWidth((short) 0, (short) 1000);
            sheet.setColumnWidth((short) 1, (short) 7000);
            sheet.setColumnWidth((short) 2, (short) 7000);
            sheet.setColumnWidth((short) 3, (short) 7000);
            sheet.setColumnWidth((short) 4, (short) 9000);
            sheet.setColumnWidth((short) 5, (short) 9000);
            sheet.setColumnWidth((short) 6, (short) 9000);
            sheet.setColumnWidth((short) 7, (short) 0);
            if (true)
            {
                // Create an header row
                sheet.addMergedRegion(new CellRangeAddress(0, 1, (short) 0, (short) 5));
                sheet.addMergedRegion(new CellRangeAddress(0, 1, (short) 0, (short) 5));
                HSSFCellStyle headerStyleTitle = wb.createCellStyle();
                headerStyleTitle.setAlignment(HorizontalAlignment.CENTER);
                headerStyleTitle.setVerticalAlignment(VerticalAlignment.CENTER);
                HSSFRow xlsRowTitle = sheet.createRow(0);
                HSSFCell cellTitle = xlsRowTitle.createCell((short) 0);
                // cellTitle.setEncoding(HSSFCell.ENCODING_UTF_16);
                cellTitle.setCellValue(prjName + " 工程作业依赖关系");
                cellTitle.setCellStyle(headerStyleTitle);

                HSSFRow xlsRow = sheet.createRow(2);
                HSSFCellStyle headerStyle = wb.createCellStyle();
                headerStyle.setFillPattern(FillPatternType.FINE_DOTS);
                headerStyle.setFillForegroundColor(IndexedColors.DARK_TEAL.getIndex());
                // headerStyle.setFillBackgroundColor(IndexedColors.BLUE_GREY.getIndex());
                HSSFFont bold = wb.createFont();
                bold.setBold(true);
                bold.setColor(IndexedColors.WHITE.getIndex());
                headerStyle.setFont(bold);
                HSSFCell cell = xlsRow.createCell((short) 0);
                // cell.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell.setCellValue("序号");
                cell.setCellStyle(headerStyle);
                HSSFCell cell1 = xlsRow.createCell((short) 1);
                // cell1.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell1.setCellValue("工作流名称");
                cell1.setCellStyle(headerStyle);
                HSSFCell cell2 = xlsRow.createCell((short) 2);
                // cell2.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell2.setCellValue("活动名称");
                cell2.setCellStyle(headerStyle);
                HSSFCell cell3 = xlsRow.createCell((short) 3);
                // cell3.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell3.setCellValue("依赖活动序号");
                cell3.setCellStyle(headerStyle);
                HSSFCell cell4 = xlsRow.createCell((short) 4);
                // cell4.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell4.setCellValue("触发活动序号");
                cell4.setCellStyle(headerStyle);
                HSSFCell cell5 = xlsRow.createCell((short) 5);
                // cell5.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell5.setCellValue("活动信息描述");
                cell5.setCellStyle(headerStyle);
                HSSFCell cell6 = xlsRow.createCell((short) 6);
                // cell6.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell6.setCellValue("活动信息备注");
                cell6.setCellStyle(headerStyle);
            }
            String act = "";
            int num = 0;
            // int offset = 1;
            for (int i = 0; i < list.size(); i++)
            {
                FlowInfoData flowInfo = (FlowInfoData) list.get(i);
                // if(!flowInfo.getFlowName().equals(tempFlowName))
                // {
                // tempFlowName = flowInfo.getFlowName();
                // offset = i+1;
                // }
                indexMap.put(flowInfo.getActID(), String.valueOf(i + 1));
                HSSFRow xlsRow = sheet.createRow(rowNum++);

                HSSFCell cell0 = xlsRow.createCell((short) 0);
                // cell0.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell0.setCellValue(i + 1D);
                HSSFCell cell1 = xlsRow.createCell((short) 1);
                // cell1.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell1.setCellValue(flowInfo.getFlowName());
                HSSFCell cell2 = xlsRow.createCell((short) 2);
                // cell2.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell2.setCellValue(flowInfo.getActName());
                HSSFCell cell3 = xlsRow.createCell((short) 3);
                // cell3.setEncoding(HSSFCell.ENCODING_UTF_16);
                String preIDs = flowInfo.getPreActID().toString();
                // String preIDs = dmOrderID(offset,flowInfo.getPreActID());
                cell3.setCellValue(preIDs.substring(1, preIDs.length() - 1));
                HSSFCell cell4 = xlsRow.createCell((short) 4);
                // cell4.setEncoding(HSSFCell.ENCODING_UTF_16);
                String nextIDs = flowInfo.getNextActID().toString();
                // String nextIDs = dmOrderID(offset,flowInfo.getNextActID());
                cell4.setCellValue(nextIDs.substring(1, nextIDs.length() - 1));
                HSSFCell cell5 = xlsRow.createCell((short) 5);
                // cell5.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell5.setCellValue(flowInfo.getFlowDes());
                HSSFCell cell6 = xlsRow.createCell((short) 6);
                // cell6.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell6.setCellValue(flowInfo.getFlowNote());
                HSSFCell cell7 = xlsRow.createCell((short) 7);
                // cell7.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell7.setCellValue(flowInfo.getActID());
                if (null != flowInfo.getActName())
                {
                    if (i == list.size() - 1)
                    {
                        act = act + flowInfo.getActName();
                    } else
                    {
                        act = act + flowInfo.getActName() + "、";
                    }
                    num = num + 1;
                }
            }

            offsetIndex(sheet, indexMap);

            int j = 0;
            j = rowNum++;
            HSSFCellStyle headerStyleTitle1 = wb.createCellStyle();
            headerStyleTitle1.setVerticalAlignment(VerticalAlignment.TOP);
            sheet.addMergedRegion(new CellRangeAddress((short) j, (short) j + 1, (short) 0, (short) 5));
            HSSFRow xlsRowTitle1 = sheet.createRow(j);
            HSSFCell cellTitle1 = xlsRowTitle1.createCell((short) 0);
            // cellTitle1.setEncoding(HSSFCell.ENCODING_UTF_16);
            cellTitle1.setCellValue("说明: 工作流中共有" + num + "个活动，如下：" + act);
            cellTitle1.setCellStyle(headerStyleTitle1);
            wb.write(out);
            _log.info("Export file:" + prjName);
        } catch (Exception e)
        {
            _log.error("doExport method of FlowInfoAction.class to export excel for all result!", e);
        } finally
        {
            try
            {
                if (out != null)
                {
                    out.close();
                }
            } catch (IOException e)
            {
                _log.error("doExport method of FlowInfoAction.class to export excelout.close !", e);
            }
        }
    }

    private void offsetIndex ( HSSFSheet sheet, Map<String, String> map )
    {
        for (int i = 3; i <= sheet.getLastRowNum(); i++)
        {
            HSSFRow xlsRow = sheet.getRow(i);
            String flowName = xlsRow.getCell((short) 1).getStringCellValue();
            String preids = offset(xlsRow.getCell((short) 3).getStringCellValue(), map, flowName);
            String nextids = offset(xlsRow.getCell((short) 4).getStringCellValue(), map, flowName);
            xlsRow.getCell((short) 3).setCellValue(preids);
            xlsRow.getCell((short) 4).setCellValue(nextids);
        }
    }

    private String offset ( String idstr, Map<String, String> map, String flowName )
    {
        String[] ids = idstr.split(",");
        String idsret = "";
        for (int j = 0; j < ids.length; j++)
        {
            String value = map.get(flowName + "||" + ids[j].trim());
            if (value == null)
                value = "";
            idsret = idsret + value + ",";
        }
        return idsret.substring(0, idsret.length() - 1);
    }

    private String firstMatchBracket ( String target )
    {
        if (target == null || target.trim().length() == 0)
            return target;
        Pattern p = Pattern.compile(">(.*?)</p>", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
        Matcher m = p.matcher(target);
        if (m.find())
            return m.group(1);
        else
            return target;
    }





    /**
     * @title: getTaskVersionDownloadExcel
     * @description: TODO(【作业依赖关系】查询历史版本列表)
     * @author: lili_xing
     * @createDate: 2022/8/18
     * @param
     * @param prjName
     * @param page
     * @param limit
     * @throws
     */
    public Map<String, Object> getTaskVersionDownloadExcel(String prjName,
                                                           String page,
                                                           String limit) {
        return TaskDownloadManager.getInstance().getTaskVersionDownloadExcel(prjName,page,limit);
    }

    /**
     * @Title: exportTaskExcel
     * @Description: 导出依赖关系
     * @param response
     * @param projectname
     * @author: Administrator
     * @date:   2018年2月26日 上午8:54:55
     */
    public void exportTaskVersionExcel ( HttpServletResponse response, String projectname,
                                         String version,
                                         long iid )
    {
        Map<String, FlowInfoData> map = new HashMap<String, FlowInfoData>();
        Map returnMap = new HashMap();
        boolean flag = false;
        try
        {
            flag = TaskDownloadManager.getInstance().isRelation(projectname, Constants.IEAI_IEAI);
        } catch (RepositoryException e1)
        {
            e1.printStackTrace();
            _log.error("isRelation is error，" + e1.getMessage());
        }
        if (flag)
        {

            returnMap = this.exportXLS(projectname, map);
            try
            {
                try {
                    byte[] hisByte = TaskDownloadManager.getInstance().getTaskVersionBinary(iid);
                    List<ActInfoVersionBean> acts = bytesToList(hisByte); // List<ActRelationInfo> list
                    _log.info("获取的listB："+acts.toString());
                    doExportXLSVersion(response.getOutputStream(), acts,returnMap, projectname, map);
                } catch (RepositoryException e) {
                    e.printStackTrace();
                }
            } catch (IOException e)
            {
                _log.error("doExportXLS is error，", e);
            }
        } else {
            try
            {
                returnMap = this.exportXLS(projectname, map);
                try
                {
                    byte[] hisByte = TaskDownloadManager.getInstance().getTaskVersionBinary(iid);
                    List<ActInfoVersionBean> acts2 = bytesToList(hisByte); // List<ActRelationInfo> list
                    _log.info("获取的listB："+acts2.toString());
                    doExportXLSVersion(response.getOutputStream(), acts2,returnMap, projectname, map);
                } catch (RepositoryException e) {
                    e.printStackTrace();
                }
            } catch (IOException e) {
                _log.error("doExportXLS is error，", e);
            }
        }
    }

    /**
     * @title: bytesToList
     * @description: 字节组转list
     * @author: lili_xing
     * @createDate: 2022/8/22
     * @param b
     * @return List
     * @return java.util.List<java.lang.Byte>
     */
    public List<ActInfoVersionBean> bytesToList(byte[] b){

        List<ActInfoVersionBean> list = new ArrayList<ActInfoVersionBean>();
        ObjectInputStream in = null;
        try {
            in = new ObjectInputStream(new ByteArrayInputStream(b));
            list= (List<ActInfoVersionBean>)in.readObject();
            in.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return list;
    } // ActRelationInfo

    public void doExportXLSVersion ( OutputStream out, List<ActInfoVersionBean> list, Map returnMap , String prjName,
                                     Map<String, FlowInfoData> map )
    {

        try
        {
            String shellH = "";
            String shellEndTag = "";
            for (int i = 0; i < list.size(); i++)
            {
                ActInfoVersionBean flowInfo = list.get(i);
                shellH = flowInfo.getShellCrust();
                shellEndTag = flowInfo.getEndTag();
                i = list.size();
            }
            Map<String, String> indexMap = new HashMap<String, String>();
            HSSFSheet sheet = null;
            HSSFWorkbook wb = new HSSFWorkbook();
            sheet = wb.createSheet();
            wb.setSheetName(0, "作业基本关系");
            int rowNum = 4;
            sheet.setColumnWidth((short) 0, (short) 3000);
            sheet.setColumnWidth((short) 1, (short) 4000);
            sheet.setColumnWidth((short) 2, (short) 4000);
            sheet.setColumnWidth((short) 3, (short) 4000);
            sheet.setColumnWidth((short) 4, (short) 4000);
            sheet.setColumnWidth((short) 5, (short) 4000);
            sheet.setColumnWidth((short) 6, (short) 3000);
            sheet.setColumnWidth((short) 7, (short) 4000);
            sheet.setColumnWidth((short) 8, (short) 3000);
            sheet.setColumnWidth((short) 9, (short) 3000);
            sheet.setColumnWidth((short) 10, (short) 4000);
            sheet.setColumnWidth((short) 11, (short) 3000);
            sheet.setColumnWidth((short) 12, (short) 4000);
            sheet.setColumnWidth((short) 13, (short) 4000);
            sheet.setColumnWidth((short) 14, (short) 4000);
            // add
            sheet.setColumnWidth((short) 15, (short) 4000);
            sheet.setColumnWidth((short) 16, (short) 4000);
            sheet.setColumnWidth((short) 17, (short) 4000);
            sheet.setColumnWidth((short) 18, (short) 4000);
            sheet.setColumnWidth((short) 19, (short) 4000);
            // update by liyang, To add apt interface info
            sheet.setColumnWidth((short) 20, (short) 4000);// 优先级
            sheet.setColumnWidth((short) 21, (short) 4000);// 权重
            sheet.setColumnWidth((short) 22, (short) 4000);
            sheet.setColumnWidth((short) 23, (short) 4000);
            sheet.setColumnWidth((short) 24, (short) 4000);
            sheet.setColumnWidth((short) 25, (short) 4000);
            sheet.setColumnWidth((short) 26, (short) 4000);
            sheet.setColumnWidth((short) 27, (short) 4000);
            sheet.setColumnWidth((short) 28, (short) 4000);
            sheet.setColumnWidth((short) 29, (short) 4000);
            sheet.setColumnWidth((short) 30, (short) 4000);
            sheet.setColumnWidth((short) 31, (short) 4000);
            sheet.setColumnWidth((short) 32, (short) 4000);
            if(ServerEnv.getServerEnv().isJHBankSwitch()){
                sheet.setColumnWidth((short) 33, (short) 4000);
                sheet.setColumnWidth((short) 34, (short) 4000);
                sheet.setColumnWidth((short) 35, (short) 4000);
            }else if(Environment.getInstance().isBhODSUserSwitch()){
                sheet.setColumnWidth((short) 33, (short) 4000);
                sheet.setColumnWidth((short) 34, (short) 4000);
                sheet.setColumnWidth((short) 35, (short) 4000);
                /*       sheet.setColumnWidth((short) 36, (short) 4000);*/
            }else {
                sheet.setColumnWidth((short) 33, (short) 4000);
                /*             sheet.setColumnWidth((short) 34, (short) 4000);*/
            }

            // end update
            HSSFCellStyle headerStyle = null;
            HSSFCellStyle headerStyle1 = null;
            HSSFCellStyle headerStyle2 = null;
            HSSFCellStyle headerStyle3 = null;
            HSSFCellStyle headerStyle4 = null;
            HSSFCellStyle headerStyleTitle = null;
            if (true)
            {
                // Create an header row
                sheet.addMergedRegion(new CellRangeAddress(0, 0, (short) 0, (short) 28));
                HSSFFont font = wb.createFont();
                font.setFontHeightInPoints((short) 10); // 字体大小
                font.setBold(true); // 粗体
                font.setColor(IndexedColors.BLACK.getIndex()); // 绿字
                headerStyleTitle = wb.createCellStyle();
                headerStyleTitle.setAlignment(HorizontalAlignment.LEFT);
                headerStyleTitle.setVerticalAlignment(VerticalAlignment.CENTER);
                headerStyleTitle.setFont(font);
                headerStyleTitle.setWrapText(true);

                HSSFRow xlsRowTitle = sheet.createRow(0);
                HSSFCell cellTitle = xlsRowTitle.createCell((short) 0);
                // cellTitle.setEncoding(HSSFCell.ENCODING_UTF_16);
                cellTitle.setCellValue("说明:\n" + "        填充色为紫色代表开发中心项目组必填项；\n" + "        填充色为蓝色代表开发中心项目组选填项；\n"
                        + "        填充色为黄色代表开发中心项目组必填项。");
                cellTitle.setCellStyle(headerStyleTitle);
                xlsRowTitle.setHeightInPoints(60); // 设置行高

                headerStyle = wb.createCellStyle();
                headerStyle.setFillPattern(SOLID_FOREGROUND);
                headerStyle.setFillForegroundColor(IndexedColors.LAVENDER.getIndex());
                headerStyle.setFont(font);
                headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                headerStyle.setWrapText(true);

                headerStyle2 = wb.createCellStyle();
                headerStyle2.setFillPattern(SOLID_FOREGROUND);
                headerStyle2.setFillForegroundColor(IndexedColors.AQUA.getIndex());
                headerStyle2.setVerticalAlignment(VerticalAlignment.CENTER);
                headerStyle2.setFont(font);
                headerStyle2.setWrapText(true);
                // headerStyle2.setFillPattern(SOLID_FOREGROUND);

                headerStyle3 = wb.createCellStyle();
                headerStyle3.setFillPattern(SOLID_FOREGROUND);
                headerStyle3.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
                headerStyle3.setVerticalAlignment(VerticalAlignment.CENTER);
                headerStyle3.setFont(font);
                headerStyle3.setWrapText(true);

                headerStyle4 = wb.createCellStyle();
                headerStyle4.setFillPattern(SOLID_FOREGROUND);
                headerStyle4.setFillForegroundColor(IndexedColors.LIGHT_GREEN.getIndex());
                headerStyle4.setVerticalAlignment(VerticalAlignment.CENTER);
                headerStyle4.setFont(font);
                headerStyle4.setWrapText(true);

                headerStyle1 = wb.createCellStyle();
                headerStyle1.setWrapText(true);

                HSSFRow xlsRow1 = sheet.createRow(2);
                xlsRow1.setHeightInPoints(30); // 设置行高
                HSSFCell cellTitle0 = xlsRow1.createCell((short) 0);
                // cellTitle0.setEncoding(HSSFCell.ENCODING_UTF_16);
                cellTitle0.setCellValue("脚本外壳");
                cellTitle0.setCellStyle(headerStyle);

                //   sheet.addMergedRegion(new CellRangeAddress(2, 2, (short) 1, (short) 3));
                HSSFCell cellTitle1 = xlsRow1.createCell((short) 1);
                // cellTitle1.setEncoding(HSSFCell.ENCODING_UTF_16);
                cellTitle1.setCellValue(shellH);
                cellTitle1.setCellStyle(headerStyle1);

                //HSSFCell cellTitle2 = xlsRow1.createCell((short) 2);
                // cellTitle1.setEncoding(HSSFCell.ENCODING_UTF_16);
                //cellTitle2.setCellValue("结束标识");
                //cellTitle2.setCellStyle(headerStyle);

                //  sheet.addMergedRegion(new CellRangeAddress(2, 2, (short) 5, (short) 7));
                HSSFCell cellTitle3 = xlsRow1.createCell((short) 3);
                // cellTitle1.setEncoding(HSSFCell.ENCODING_UTF_16);
                cellTitle3.setCellValue(shellEndTag);
                cellTitle3.setCellStyle(headerStyle1);

                HSSFRow xlsRow2 = sheet.createRow(1);
                xlsRow2.setHeightInPoints(30); // 设置行高
                HSSFCell cellTitleProjectName = xlsRow2.createCell((short) 0);
                // cellTitleProjectName.setEncoding(HSSFCell.ENCODING_UTF_16);
                cellTitleProjectName.setCellValue("工程名称");
                cellTitleProjectName.setCellStyle(headerStyle);

                sheet.addMergedRegion(new CellRangeAddress(1, 1, (short) 1, (short) 28));
                HSSFCell cellTitleProjectName1 = xlsRow2.createCell((short) 1);
                // cellTitleProjectName1.setEncoding(HSSFCell.ENCODING_UTF_16);
                cellTitleProjectName1.setCellValue(prjName);
                cellTitleProjectName1.setCellStyle(headerStyleTitle);

                // HSSFFont bold = wb.createFont();
                // bold.setBold(true);
                // bold.setColor(IndexedColors.WHITE.getIndex());
                // headerStyle.setFont(bold);

                HSSFRow xlsRow = sheet.createRow(3);
                xlsRow.setHeightInPoints(25); // 设置行高

                HSSFCell cell = xlsRow.createCell((short) 0);
                // cell.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell.setCellValue("操作步骤序号");
                cell.setCellStyle(headerStyle);

                HSSFCell cell1 = xlsRow.createCell((short) 1);
                // cell1.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell1.setCellValue("所属系统");
                cell1.setCellStyle(headerStyle);

                HSSFCell cell2 = xlsRow.createCell((short) 2);
                // cell2.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell2.setCellValue("子系统名称");
                cell2.setCellStyle(headerStyle);

                HSSFCell cell3 = xlsRow.createCell((short) 3);
                // cell3.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell3.setCellValue("作业名称");
                cell3.setCellStyle(headerStyle);

                HSSFCell cell4 = xlsRow.createCell((short) 4);
                // cell4.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell4.setCellValue("作业描述");
                cell4.setCellStyle(headerStyle);

                HSSFCell cell5 = xlsRow.createCell((short) 5);
                // cell5.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell5.setCellValue("触发作业");
                cell5.setCellStyle(headerStyle);

                HSSFCell cell6 = xlsRow.createCell((short) 6);
                // cell6.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell6.setCellValue("依赖作业");
                cell6.setCellStyle(headerStyle);

                HSSFCell cell7 = xlsRow.createCell((short) 7);
                // cell7.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell7.setCellValue("是否删除");
                cell7.setCellStyle(headerStyle);

                HSSFCell cell8 = xlsRow.createCell((short) 8);
                // cell8.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell8.setCellValue("OK文件绝对路径");
                cell8.setCellStyle(headerStyle);

                HSSFCell cell9 = xlsRow.createCell((short) 9);
                // cell9.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell9.setCellValue("OK文件检测周期");
                cell9.setCellStyle(headerStyle);
                // HSSFCell cell9 = xlsRow.createCell((short) 9);
                // cell9.setEncoding(HSSFCell.ENCODING_UTF_16);
                // cell9.setCellValue("脚本外壳");
                // cell9.setCellStyle(headerStyle);

                HSSFCell cell10 = xlsRow.createCell((short) 10);
                // cell10.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell10.setCellValue("脚本名称");
                cell10.setCellStyle(headerStyle);

                HSSFCell cell11 = xlsRow.createCell((short) 11);
                // cell11.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell11.setCellValue("输出参数");
                cell11.setCellStyle(headerStyle);
                // HSSFCell cell12 = xlsRow.createCell((short) 12);
                // cell12.setEncoding(HSSFCell.ENCODING_UTF_16);
                // cell12.setCellValue("脚本执行成功最后一行打印值");
                // cell12.setCellStyle(headerStyle);

                HSSFCell cell12 = xlsRow.createCell((short) 12);
                // cell12.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell12.setCellValue("业务异常自动重试");
                cell12.setCellStyle(headerStyle);

                HSSFCell cell13 = xlsRow.createCell((short) 13);
                // cell12.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell13.setCellValue("业务异常自动忽略");
                cell13.setCellStyle(headerStyle);

                HSSFCell cell14 = xlsRow.createCell((short) 14);
                // cell13.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell14.setCellValue("Agent资源组");
                cell14.setCellStyle(headerStyle);

                HSSFCell cell15 = xlsRow.createCell((short) 15);
                // cell14.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell15.setCellValue("Agent文件检测资源组");
                cell15.setCellStyle(headerStyle);

                // add
                HSSFCell cell16 = xlsRow.createCell((short) 16);// AgentIP:端口
                cell16.setCellValue("AgentIP:端口");
                cell16.setCellStyle(headerStyle);

                HSSFCell cell17 = xlsRow.createCell((short) 17);// 权重
                cell17.setCellValue("延迟运行");
                cell17.setCellStyle(headerStyle);

                HSSFCell cell18 = xlsRow.createCell((short) 18);
                cell18.setCellValue("执行条件");
                cell18.setCellStyle(headerStyle);

                HSSFCell cell19 = xlsRow.createCell((short) 19);
                cell19.setCellValue("重试次数");
                cell19.setCellStyle(headerStyle);

                HSSFCell cell20 = xlsRow.createCell((short) 20);
                cell20.setCellValue("重试间隔");
                cell20.setCellStyle(headerStyle);

                HSSFCell cell21 = xlsRow.createCell((short) 21);
                cell21.setCellValue("重试结束时间");
                cell21.setCellStyle(headerStyle);

                HSSFCell cell22 = xlsRow.createCell((short) 22);
                cell22.setCellValue("延时报警");
                cell22.setCellStyle(headerStyle);

                HSSFCell cell23 = xlsRow.createCell((short) 23);
                cell23.setCellValue("日历名称");
                cell23.setCellStyle(headerStyle);

                HSSFCell cell24 = xlsRow.createCell((short) 24);// 优先级
                cell24.setCellValue("优先级");
                cell24.setCellStyle(headerStyle);

                HSSFCell cell25 = xlsRow.createCell((short) 25);// 权重
                cell25.setCellValue("权重");
                cell25.setCellStyle(headerStyle);

                HSSFCell cell26 = xlsRow.createCell((short) 26);
                cell26.setCellValue("APT组名称");
                cell26.setCellStyle(headerStyle3);

                HSSFCell cell27 = xlsRow.createCell((short) 27);
                cell27.setCellValue("APT文件名称");
                cell27.setCellStyle(headerStyle3);

                HSSFCell cell28 = xlsRow.createCell((short) 28);
                cell28.setCellValue("ISDB2");
                cell28.setCellStyle(headerStyle3);

                HSSFCell cell29 = xlsRow.createCell((short) 29);
                cell29.setCellValue("DB2IP");
                cell29.setCellStyle(headerStyle3);

                HSSFCell cell30 = xlsRow.createCell((short) 30);
                cell30.setCellValue("主线名称");
                cell30.setCellStyle(headerStyle);

                HSSFCell cell31 = xlsRow.createCell((short) 31);
                cell31.setCellValue("头尾标记");
                cell31.setCellStyle(headerStyle2);

                HSSFCell cell32 = xlsRow.createCell((short) 32);
                cell32.setCellValue("是否禁用");
                cell32.setCellStyle(headerStyle);

                /*HSSFCell cell33 = xlsRow.createCell((short) 33);
                cell33.setCellValue("人工提醒");
                cell33.setCellStyle(headerStyle);*/

                if(ServerEnv.getServerEnv().isJHBankSwitch()){

                    //交行新增功能
                    HSSFCell cell33 = xlsRow.createCell((short) 33);
                    cell33.setCellValue("DAYS");
                    cell33.setCellStyle(headerStyle);

                    HSSFCell cell34 = xlsRow.createCell((short) 34);
                    cell34.setCellValue("LOGIC");
                    cell34.setCellStyle(headerStyle);

                    HSSFCell cell35 = xlsRow.createCell((short) 35);
                    cell35.setCellValue("WDAYS");
                    cell35.setCellStyle(headerStyle);
                }else if(Environment.getInstance().isBhODSUserSwitch()){
                    HSSFCell cell33 = xlsRow.createCell((short) 33);
                    cell33.setCellValue("执行用户");
                    cell33.setCellStyle(headerStyle);
                    HSSFCell cell34 = xlsRow.createCell((short) 34);
                    cell34.setCellValue("虚拟组名称");
                    cell34.setCellStyle(headerStyle);
/*                    HSSFCell cell35 = xlsRow.createCell((short) 35);
                    cell35.setCellValue("ok文件名称");
                    cell35.setCellStyle(headerStyle);*/
                    HSSFCell cell36 = xlsRow.createCell((short) 35);
                    cell36.setCellValue("人工提醒");
                    cell36.setCellStyle(headerStyle);
                }else if(ServerEnv.getServerEnv().isJHBankSwitch()){
                    //西安
                    HSSFCell cell33 = xlsRow.createCell((short) 33);
                    cell33.setCellValue("人工提醒");
                    cell33.setCellStyle(headerStyle);
                    HSSFCell cell34 = xlsRow.createCell((short) 34);
                    cell34.setCellValue("DAYS");
                    cell34.setCellStyle(headerStyle);

                    HSSFCell cell35 = xlsRow.createCell((short) 35);
                    cell35.setCellValue("LOGIC");
                    cell35.setCellStyle(headerStyle);

                    HSSFCell cell36 = xlsRow.createCell((short) 36);
                    cell36.setCellValue("WDAYS");
                    cell36.setCellStyle(headerStyle);
                    HSSFCell cell37 = xlsRow.createCell((short) 37);
                    cell37.setCellValue("MONTH");
                    cell37.setCellStyle(headerStyle);
                }else {
/*                    HSSFCell cell33 = xlsRow.createCell((short) 33);
                    cell33.setCellValue("ok文件名称");
                    cell33.setCellStyle(headerStyle);*/
                    HSSFCell cell34 = xlsRow.createCell((short) 33);
                    cell34.setCellValue("人工提醒");
                    cell34.setCellStyle(headerStyle);
                    HSSFCell cell35 = xlsRow.createCell((short) 34);//DAYS:指定批量日期执行1-31
                    cell35.setCellValue("DAYS");
                    cell35.setCellStyle(headerStyle);

                    HSSFCell cell36 = xlsRow.createCell((short) 35);//LOGIC:条件逻辑符AND/OR，AND多个条件均满足，OR多个条件满足一个即可
                    cell36.setCellValue("LOGIC");
                    cell36.setCellStyle(headerStyle);

                    HSSFCell cell37 = xlsRow.createCell((short) 36);  //WDAYS:指定星期执行0-6，批量对应的星期一至星期日
                    cell37.setCellValue("WDAYS");
                    cell37.setCellStyle(headerStyle);
                    HSSFCell cell38 = xlsRow.createCell((short) 37);//MONTH:指定月份执行1-12
                    cell38.setCellValue("MONTH");
                    cell38.setCellStyle(headerStyle);
                }
            }
            String act = "";
            int num = 0;
            // int offset = 1;
            for (int i = 0; i < list.size(); i++)
            {
                ActInfoVersionBean flowInfo = list.get(i);
                // if(!flowInfo.getFlowName().equals(tempFlowName))
                // {
                // tempFlowName = flowInfo.getFlowName();
                // offset = i+1;
                // }
                indexMap.put(flowInfo.getActNo(), String.valueOf(i + 1));
                HSSFRow xlsRow = sheet.createRow(rowNum++);
                xlsRow.setHeightInPoints(25); // 设置行高

                HSSFCell cell0 = xlsRow.createCell((short) 0);
                // cell0.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell0.setCellValue(flowInfo.getActNo());
                cell0.setCellStyle(headerStyle1);

                HSSFCell cell1 = xlsRow.createCell((short) 1);
                // cell1.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell1.setCellValue(flowInfo.getSystem());
                cell1.setCellStyle(headerStyle1);

                HSSFCell cell2 = xlsRow.createCell((short) 2);
                // cell2.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell2.setCellValue(flowInfo.getChildProjectName());
                cell2.setCellStyle(headerStyle1);

                HSSFCell cell3 = xlsRow.createCell((short) 3);
                // cell3.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell3.setCellValue(flowInfo.getActName());
                cell3.setCellStyle(headerStyle1);

                HSSFCell cell4 = xlsRow.createCell((short) 4);
                // cell4.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell4.setCellValue(flowInfo.getDescribe());
                cell4.setCellStyle(headerStyle1);

                HSSFCell cell5 = xlsRow.createCell((short) 5);
                cell5.setCellValue(flowInfo.getAfterActList()); // 依赖作业
                cell5.setCellStyle(headerStyle1);

                HSSFCell cell6 = xlsRow.createCell((short) 6);
                // cell6.setEncoding(HSSFCell.ENCODING_UTF_16);
                if(Environment.getInstance().isBhODSUserSwitch()){
                    boolean cz=false;
                    if(!"".equals(flowInfo.getJoblist())&&null!=flowInfo.getJoblist()){
                        for (String s : flowInfo.getJoblist().split(",")) {
                            Map<String, String> VirtualkeyMap = (Map<String, String>) returnMap.get("VirtualkeyMap");
                            if(VirtualkeyMap.containsKey(s)){//判断是否有虚拟组存在
                                cz=true;
                            }else {

                            }
                        }
                    }
                    if(cz){
                        String Virtualname="";
                        if(Environment.getInstance().isBhODSEXPORTRELYONNAME()){
                            for (int j = 0; j < list.size(); j++)
                            {
                                for (String s : flowInfo.getJoblist().split(",")) {
                                    ActInfoVersionBean flowInfoj = list.get(j);
                                    if(s.equals(flowInfoj.getActNo())){
                                        boolean status = Virtualname.contains(flowInfoj.getActName());
                                        if(status){

                                        }else {
                                            Virtualname=   flowInfoj.getActName() + "," + Virtualname;
                                        }

                                    }else if(s.equals(flowInfoj.getActID())){
                                        boolean status = Virtualname.contains(flowInfoj.getActName());
                                        if(status){

                                        }else {
                                            Virtualname=   flowInfoj.getActName() + "," + Virtualname;
                                        }
                                    }else if(s.equals(flowInfoj.getActName())){
                                        boolean status = Virtualname.contains(flowInfoj.getActName());
                                        if(status){

                                        }else {
                                            Virtualname=   flowInfoj.getActName() + "," + Virtualname;
                                        }
                                    }else {
                                        boolean status = Virtualname.contains(s);
                                        if(status){

                                        }else {
                                            Map<String, String> VirtualkeyMap = (Map<String, String>) returnMap.get("VirtualkeyMap");
                                            if(VirtualkeyMap.containsKey(s)){//判断是否有虚拟组存在
                                                Virtualname=  s + "," + Virtualname;
                                            }
                                        }
                                    }

                                }
                            }
                            if(Virtualname.contains(",")){
                                Virtualname = Virtualname.substring(0,Virtualname.length() - 1);
                            }
                            cell6.setCellValue(Virtualname);
                            cell6.setCellStyle(headerStyle1);
                        }else {

                            for (String s : flowInfo.getJoblist().split(",")) {
                                for (int j = 0; j < list.size(); j++)
                                {
                                    ActInfoVersionBean flowInfoj = list.get(j);
                                    if(s.contains(flowInfoj.getActNo())){
                                        boolean status = Virtualname.contains(flowInfoj.getActID());
                                        if(status){

                                        }else {
                                            Virtualname=   flowInfoj.getActID() + "," + Virtualname;
                                        }
                                    }else if(s.equals(flowInfoj.getActName())){
                                        boolean status = Virtualname.contains(flowInfoj.getActID());
                                        if(status){

                                        }else {
                                            Virtualname = flowInfoj.getActID() + "," + Virtualname;
                                        }
                                    }else if(s.equals(flowInfoj.getActID())){
                                        boolean status = Virtualname.contains(flowInfoj.getActID());
                                        if(status){

                                        }else {
                                            Virtualname=   flowInfoj.getActID() + "," + Virtualname;
                                        }
                                    }else {
                                        boolean status = Virtualname.contains(s);
                                        if (status) {

                                        } else {
                                            Map<String, String> VirtualkeyMap = (Map<String, String>) returnMap.get("VirtualkeyMap");
                                            if(VirtualkeyMap.containsKey(s)){//判断是否有虚拟组存在
                                                Virtualname = s + "," + Virtualname;
                                            }
                                        }
                                    }
                                }
                            }
                            if(Virtualname.contains(",")){
                                Virtualname = Virtualname.substring(0,Virtualname.length() - 1);
                            }

                            cell6.setCellValue(Virtualname);
                            cell6.setCellStyle(headerStyle1);
                        }
                    }else {
                        cell6.setCellValue(flowInfo.getBeforeActList()); // getSuccAct 触发作业
                        cell6.setCellStyle(headerStyle1);
                    }
                }else {
                    cell6.setCellValue(flowInfo.getBeforeActList()); // getSuccAct 触发作业
                    cell6.setCellStyle(headerStyle1);
                }

                HSSFCell cell7C = xlsRow.createCell((short) 7);
                // cell7.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell7C.setCellValue("否");
                cell7C.setCellStyle(headerStyle1);

                // RangeAddress regions = new RangeAddress(0,0,0,0);

                // //生成下拉框内容
                // String [] list1={"11","34"};
                //
                // DVConstraint constraint = DVConstraint.createExplicitListConstraint(list);
                //
                // //绑定下拉框和作用区域
                //
                // //HSSFDataValidation data_validation = new
                // HSSFDataValidation((short)(1),(short)1,(short)(1),(short)1);
                //
                // HSSFDataValidation data_validation = new HSSFDataValidation(regions,constraint);
                //
                // //对sheet页生效
                //
                // sheet.addValidationData(data_validation);
                //
                HSSFCell cell8C = xlsRow.createCell((short) 8);
                // cell8.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell8C.setCellValue(flowInfo.getOKFileABPath());// OK文件绝对路径
                cell8C.setCellStyle(headerStyle1);

                HSSFCell cell9C = xlsRow.createCell((short) 9);
                // cell9.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell9C.setCellValue("0".equals(flowInfo.getOKFileFindWeek()) ? "" : flowInfo.getOKFileFindWeek()); //OK文件检测周期
                cell9C.setCellStyle(headerStyle1);
                // HSSFCell cell9 = xlsRow.createCell((short) 9);
                // cell9.setEncoding(HSSFCell.ENCODING_UTF_16);
                // cell9.setCellValue(flowInfo.getShellHouse());
                HSSFCell cell10C = xlsRow.createCell((short) 10);
                // cell10.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell10C.setCellValue(flowInfo.getShellABPath()); // 脚本名称
                cell10C.setCellStyle(headerStyle1);

                HSSFCell cell11C = xlsRow.createCell((short) 11);
                // cell11.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell11C.setCellValue(flowInfo.getOutputPamaeter()); // 输出参数
                cell11C.setCellStyle(headerStyle1);
                // HSSFCell cell12 = xlsRow.createCell((short) 12);
                // cell12.setEncoding(HSSFCell.ENCODING_UTF_16);
                // cell12.setCellValue(flowInfo.getLastLine());

                HSSFCell cell12C = xlsRow.createCell((short) 12);
                // cell12.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell12C.setCellValue(flowInfo.getRedo()); // 业务异常自动重试
                cell12C.setCellStyle(headerStyle1);

                HSSFCell cell13C = xlsRow.createCell((short) 13);
                // cell12.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell13C.setCellValue(flowInfo.getSkip());// 业务异常是否忽略
                cell13C.setCellStyle(headerStyle1);

                HSSFCell cell14C = xlsRow.createCell((short) 14);
                // cell13.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell14C.setCellValue(flowInfo.isAgentGroup() ? flowInfo.getAgentGropName() : ""); // Agent资源组
                cell14C.setCellStyle(headerStyle1);

                HSSFCell cell15C = xlsRow.createCell((short) 15);
                // cell14.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell15C.setCellValue(flowInfo.getCheckAgentGropName()); // Agent资源组
                cell15C.setCellStyle(headerStyle1);

                // AgentIP:端口 延迟运行 分支条件 重试次数 日历名称

                HSSFCell cell16C = xlsRow.createCell((short) 16);
                cell16C.setCellValue(flowInfo.getAgentInfo());
                cell16C.setCellStyle(headerStyle1);

                HSSFCell cell17C = xlsRow.createCell((short) 17);
                // cellWeight.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell17C.setCellValue(flowInfo.getDelayTime());
                cell17C.setCellStyle(headerStyle1);

                HSSFCell cell18C = xlsRow.createCell((short) 18);
                // cell15.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell18C.setCellValue(flowInfo.getBranchCondition());
                cell18C.setCellStyle(headerStyle1);

                HSSFCell cell19C = xlsRow.createCell((short) 19);
                // cell16.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell19C.setCellValue("0".equals(flowInfo.getReTryCount()) ? "" : flowInfo.getReTryCount());
                cell19C.setCellStyle(headerStyle1);

                HSSFCell cell20C = xlsRow.createCell((short) 20);
                // cell16.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell20C.setCellValue(flowInfo.getReTryTime());
                cell20C.setCellStyle(headerStyle1);

                HSSFCell cell21C = xlsRow.createCell((short) 21);
                // cell16.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell21C.setCellValue(flowInfo.getReTryEndTime());
                cell21C.setCellStyle(headerStyle1);

                HSSFCell cell22C = xlsRow.createCell((short) 22);
                // cell16.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell22C.setCellValue(flowInfo.getDelayWarnning());
                cell22C.setCellStyle(headerStyle1);

                HSSFCell cell23C = xlsRow.createCell((short) 23);
                // cellPriority.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell23C.setCellValue(flowInfo.getCalendName());
                cell23C.setCellStyle(headerStyle1);

                HSSFCell cell24C = xlsRow.createCell((short) 24);
                // cellPriority.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell24C.setCellValue(flowInfo.getPriority());
                cell24C.setCellStyle(headerStyle1);

                HSSFCell cell25C = xlsRow.createCell((short) 25);
                // cell15.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell25C.setCellValue(flowInfo.getWeights());
                cell25C.setCellStyle(headerStyle1);

                HSSFCell cell26C = xlsRow.createCell((short) 26);
                // cell15.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell26C.setCellValue(flowInfo.getAptGroupName());
                cell26C.setCellStyle(headerStyle1);

                HSSFCell cell27C = xlsRow.createCell((short) 27);
                // cell16.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell27C.setCellValue(flowInfo.getAptFileName());
                cell27C.setCellStyle(headerStyle1);

                HSSFCell cell28C = xlsRow.createCell((short) 28);
                // cell17.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell28C.setCellValue(flowInfo.getIsDB2()); // 是否是DB2
                cell28C.setCellStyle(headerStyle1);

                HSSFCell cell29C = xlsRow.createCell((short) 29);
                // cell18.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell29C.setCellValue(flowInfo.getDb2IP());  // DB2IP
                cell29C.setCellStyle(headerStyle1);

                HSSFCell cell30C = xlsRow.createCell((short) 30);
                // cell20.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell30C.setCellValue(flowInfo.getMainline()); // 主线名称
                cell30C.setCellStyle(headerStyle1);

                HSSFCell cell31C = xlsRow.createCell((short) 31);
                cell31C.setCellValue(flowInfo.getSEFlag());
                cell31C.setCellStyle(headerStyle1);

                if (null != flowInfo.getActName())
                {
                    if (i == list.size() - 1)
                    {
                        act = act + flowInfo.getActName();
                    } else
                    {
                        act = act + flowInfo.getActName() + "、";
                    }
                    num = num + 1;
                }
                HSSFCell cell32C = xlsRow.createCell((short) 32);
                cell32C.setCellValue(flowInfo.getDisableFlag()); // 是否禁用
                cell32C.setCellStyle(headerStyle1);

                /*HSSFCell cell33C = xlsRow.createCell((short) 33);
                cell33C.setCellValue(flowInfo.getDisableFlag()); // 人工提醒
                cell33C.setCellStyle(headerStyle1);*/

                if(ServerEnv.getServerEnv().isJHBankSwitch()){

                    HSSFCell cell33 = xlsRow.createCell((short) 33);
                    cell33.setCellValue(flowInfo.getDays());
                    cell33.setCellStyle(headerStyle1);

                    HSSFCell cell34 = xlsRow.createCell((short) 34);
                    cell34.setCellValue(flowInfo.getLogic());
                    cell34.setCellStyle(headerStyle1);
                    HSSFCell cell35 = xlsRow.createCell((short) 35);
                    cell35.setCellValue(flowInfo.getWdays());
                    cell35.setCellStyle(headerStyle1);
                }else if(Environment.getInstance().isBhODSUserSwitch()){
                    HSSFCell cell33 = xlsRow.createCell((short) 33);
                    cell33.setCellValue(flowInfo.getPerformUser());
                    cell33.setCellStyle(headerStyle1);
                    HSSFCell cell34 = xlsRow.createCell((short) 34);
                    cell34.setCellValue(flowInfo.getVirtualName());
                    cell34.setCellStyle(headerStyle1);
/*                    HSSFCell cell35 = xlsRow.createCell((short) 35);
                    cell35.setCellValue(flowInfo.getOkFileName());
                    cell35.setCellStyle(headerStyle1);*/
                    HSSFCell cell36 = xlsRow.createCell((short) 35);
                    cell36.setCellValue(flowInfo.getDisableFlag()); // 人工提醒
                    cell36.setCellStyle(headerStyle1);
                }else {
/*                    HSSFCell cell33 = xlsRow.createCell((short) 33);
                    cell33.setCellValue(flowInfo.getOkFileName());
                    cell33.setCellStyle(headerStyle1);*/
                    HSSFCell cell34 = xlsRow.createCell((short) 33);
                    cell34.setCellValue(flowInfo.getDisableFlag()); // 人工提醒
                    cell34.setCellStyle(headerStyle1);
                    HSSFCell cell35 = xlsRow.createCell((short) 34);//DAYS:指定批量日期执行1-31
                    cell35.setCellValue(flowInfo.getDays());
                    cell35.setCellStyle(headerStyle1);
                    HSSFCell cell36 = xlsRow.createCell((short) 35);//LOGIC:条件逻辑符AND/OR，AND多个条件均满足，OR多个条件满足一个即可
                    cell36.setCellValue(flowInfo.getLogic());
                    cell36.setCellStyle(headerStyle1);
                    HSSFCell cell37 = xlsRow.createCell((short) 36);//WDAYS:指定星期执行0-6，批量对应的星期一至星期日
                    cell37.setCellValue(flowInfo.getWdays());
                    cell37.setCellStyle(headerStyle1);
                    HSSFCell cell38 = xlsRow.createCell((short) 37);//MONTH:指定月份执行1-12
                    cell38.setCellValue(flowInfo.getMonth());
                    cell38.setCellStyle(headerStyle1);
                }
            }

            // offsetIndex(sheet, indexMap);

            // int j = 0;
            // j = rowNum++;
            // HSSFCellStyle headerStyleTitle1 = wb.createCellStyle();
            // headerStyleTitle1.setVerticalAlignment(VerticalAlignment.TOP);
            // sheet.addMergedRegion(new CellRangeAddress((short) j, (short) 0, (short) j + 1, (short) 13));
            // HSSFRow xlsRowTitle1 = sheet.createRow(j);
            // HSSFCell cellTitle1 = xlsRowTitle1.createCell((short) 0);
            // cellTitle1.setEncoding(HSSFCell.ENCODING_UTF_16);
            // cellTitle1.setCellValue("说明: 工作流中共有" + num + "个活动，如下：" + act);
            // cellTitle1.setCellStyle(headerStyleTitle1);
            wb.write(out);
            _log.info("Export file:" + prjName);
        } catch (Exception e)
        {
            _log.error("doExportVersion method of FlowInfoAction.class to export excel for all result!", e);
        } finally
        {
            try
            {
                if (out != null)
                {
                    out.close();
                }
            } catch (IOException e)
            {
                _log.error("doExportVersion method of FlowInfoAction.class to export excelout.close !", e);
            }
        }
    }


    /**
     *
     * @Title: responseData
     * @Description: 将信息返回给调用端
     * @param response
     * @param jsonStr
     * @author: yue_sun
     * @date:   2018年7月31日
     */
    private static void responseData(HttpServletResponse response, String jsonStr)
    {
        try
        {
            response.getOutputStream().write(jsonStr.getBytes("UTF-8"));
            response.setContentType("text/json; charset=UTF-8");
            response.addHeader("Content-type", "application/json");
            response.addHeader("Accept", "application/json");
        } catch (UnsupportedEncodingException e)
        {
            _log.error("responseData is error ! " + e.getMessage(), e);
        } catch (IOException e)
        {
            _log.error("responseData is error ! " + e.getMessage(), e);
        }
    }



    /**
     * @Title: exportTaskExcel
     * @Description: 导出依赖关系
     * @param response
     * @param projectname
     * @author: Administrator
     * @date:   2018年2月26日 上午8:54:55
     */
    public void exportTaskExcelTemp ( HttpServletResponse response, String projectname ,String uuid)
    {
        FileInputStream in = null;
        OutputStream out = null;
        File directory = null;
        try {
            projectname =TaskDownloadManager.getInstance().isChildName(projectname, Constants.IEAI_IEAI);
            String directoryPath =  Environment.getInstance().getIEAIHome() + File.separator +"downTemp" + File.separator + uuid;
            directory =new File(directoryPath);
            _log.info("下载文件路径："+directory);
            String filePath = Environment.getInstance().getIEAIHome() + File.separator +"downTemp" + File.separator + uuid +File.separator+projectname+".xls";
            _log.info("下载文件路径："+filePath);
            File file = new File(filePath);
            if (!file.exists()) {
                _log.error("文件不存在!");
                throw  new Exception("文件不存在!");
            }
            in = new FileInputStream(file);
            out = response.getOutputStream();

            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = in.read(buffer)) != -1) {
                out.write(buffer, 0, bytesRead);
            }

            in.close();
            out.flush();
        }  catch (Exception e) {
           _log.info("exportTaskExcelTemp is error!",e);
        } finally {
            try {
                if (in != null)
                {
                    in.close();
                }
                if (out != null)
                {
                    out.close();
                }
                if (null != directory)
                {
                    // 删除临时文件
                    TaskUploadService.getInstance().deleteDir(directory);
                }

            } catch (IOException e) {
                _log.info("exportTaskExcelTemp is error!",e);
            }
        }
    }


    /**
     * @Title: doExportXLS
     * @Description: 导出操作方法，生成Excel到固定路径
     * @param list
     * @param prjName
     * @return 生成的Excel文件路径
     * @throws Exception
     * @author: yue_sun
     * @date:   2025年07月04日
     */
    public File doRollBackExportXLS( List<ActRelationInfo> list, String prjName,Map<String, Object> returnMap,String filePath) throws Exception
    {
        FileOutputStream out = null;
        File file = null;
        try {
            // 获取系统临时目录或指定的固定路径
            file = new File(filePath);
            out = new FileOutputStream(file);

            String shellH = "";
            String shellEndTag = "";
            for (int i = 0; i < list.size(); i++)
            {
                ActRelationInfo flowInfo = list.get(i);
                shellH = flowInfo.getShellHouse();
                shellEndTag = flowInfo.getEndTag();
                i = list.size();
            }
            Map<String, String> indexMap = new HashMap<String, String>();
            HSSFSheet sheet = null;
            HSSFWorkbook wb = new HSSFWorkbook();
            sheet = wb.createSheet();
            wb.setSheetName(0, "作业基本关系");
            int rowNum = 4;
            sheet.setColumnWidth((short) 0, (short) 3000);
            sheet.setColumnWidth((short) 1, (short) 4000);
            sheet.setColumnWidth((short) 2, (short) 4000);
            sheet.setColumnWidth((short) 3, (short) 4000);
            sheet.setColumnWidth((short) 4, (short) 4000);
            sheet.setColumnWidth((short) 5, (short) 4000);
            sheet.setColumnWidth((short) 6, (short) 3000);
            sheet.setColumnWidth((short) 7, (short) 4000);
            sheet.setColumnWidth((short) 8, (short) 3000);
            sheet.setColumnWidth((short) 9, (short) 3000);
            sheet.setColumnWidth((short) 10, (short) 4000);
            sheet.setColumnWidth((short) 11, (short) 3000);
            sheet.setColumnWidth((short) 12, (short) 4000);
            sheet.setColumnWidth((short) 13, (short) 4000);
            sheet.setColumnWidth((short) 14, (short) 4000);
            // add
            sheet.setColumnWidth((short) 15, (short) 4000);
            sheet.setColumnWidth((short) 16, (short) 4000);
            sheet.setColumnWidth((short) 17, (short) 4000);
            sheet.setColumnWidth((short) 18, (short) 4000);
            sheet.setColumnWidth((short) 19, (short) 4000);
            // update by liyang, To add apt interface info
            sheet.setColumnWidth((short) 20, (short) 4000);// 优先级
            sheet.setColumnWidth((short) 21, (short) 4000);// 权重
            sheet.setColumnWidth((short) 22, (short) 4000);
            sheet.setColumnWidth((short) 23, (short) 4000);
            sheet.setColumnWidth((short) 24, (short) 4000);
            sheet.setColumnWidth((short) 25, (short) 4000);
            sheet.setColumnWidth((short) 26, (short) 4000);
            sheet.setColumnWidth((short) 27, (short) 4000);
            sheet.setColumnWidth((short) 28, (short) 4000);
            sheet.setColumnWidth((short) 29, (short) 4000);
            sheet.setColumnWidth((short) 30, (short) 4000);
            sheet.setColumnWidth((short) 31, (short) 4000);
            sheet.setColumnWidth((short) 32, (short) 4000);
            if(ServerEnv.getServerEnv().isJHBankSwitch()){
                sheet.setColumnWidth((short) 33, (short) 4000);
                sheet.setColumnWidth((short) 34, (short) 4000);
                sheet.setColumnWidth((short) 35, (short) 4000);
            }else if(Environment.getInstance().isBhODSUserSwitch()){
                sheet.setColumnWidth((short) 33, (short) 4000);
                sheet.setColumnWidth((short) 34, (short) 4000);
                sheet.setColumnWidth((short) 35, (short) 4000);
                /*       sheet.setColumnWidth((short) 36, (short) 4000);*/
            }else if(Environment.getInstance().getXanxSwitch()){
                sheet.setColumnWidth((short) 33, (short) 4000);
                sheet.setColumnWidth((short) 34, (short) 4000);
                sheet.setColumnWidth((short) 35, (short) 4000);
                sheet.setColumnWidth((short) 36, (short) 4000);
                sheet.setColumnWidth((short) 37, (short) 4000);
                sheet.setColumnWidth((short) 38, (short) 4000);
            }else {
                sheet.setColumnWidth((short) 33, (short) 4000);
                /*             sheet.setColumnWidth((short) 34, (short) 4000);*/
            }

            // end update
            HSSFCellStyle headerStyle = null;
            HSSFCellStyle headerStyle1 = null;
            HSSFCellStyle headerStyle2 = null;
            HSSFCellStyle headerStyle3 = null;
            HSSFCellStyle headerStyle4 = null;
            HSSFCellStyle headerStyleTitle = null;
            if (true)
            {
                // Create an header row
                sheet.addMergedRegion(new CellRangeAddress(0, 0, (short) 0, (short) 28));
                HSSFFont font = wb.createFont();
                font.setFontHeightInPoints((short) 10); // 字体大小
                font.setBold(true); // 粗体
                font.setColor(IndexedColors.BLACK.getIndex()); // 绿字
                headerStyleTitle = wb.createCellStyle();
                headerStyleTitle.setAlignment(HorizontalAlignment.LEFT);
                headerStyleTitle.setVerticalAlignment(VerticalAlignment.CENTER);
                headerStyleTitle.setFont(font);
                headerStyleTitle.setWrapText(true);

                HSSFRow xlsRowTitle = sheet.createRow(0);
                HSSFCell cellTitle = xlsRowTitle.createCell((short) 0);
                // cellTitle.setEncoding(HSSFCell.ENCODING_UTF_16);
                cellTitle.setCellValue("说明:\n" + "        填充色为紫色代表开发中心项目组必填项；\n" + "        填充色为蓝色代表开发中心项目组选填项；\n"
                        + "        填充色为黄色代表开发中心项目组必填项。");
                cellTitle.setCellStyle(headerStyleTitle);
                xlsRowTitle.setHeightInPoints(60); // 设置行高

                headerStyle = wb.createCellStyle();
                headerStyle.setFillPattern(SOLID_FOREGROUND);
                headerStyle.setFillForegroundColor(IndexedColors.LAVENDER.getIndex());
                headerStyle.setFont(font);
                headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                headerStyle.setWrapText(true);

                headerStyle2 = wb.createCellStyle();
                headerStyle2.setFillPattern(SOLID_FOREGROUND);
                headerStyle2.setFillForegroundColor(IndexedColors.AQUA.getIndex());
                headerStyle2.setVerticalAlignment(VerticalAlignment.CENTER);
                headerStyle2.setFont(font);
                headerStyle2.setWrapText(true);
                // headerStyle2.setFillPattern(SOLID_FOREGROUND);

                headerStyle3 = wb.createCellStyle();
                headerStyle3.setFillPattern(SOLID_FOREGROUND);
                headerStyle3.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
                headerStyle3.setVerticalAlignment(VerticalAlignment.CENTER);
                headerStyle3.setFont(font);
                headerStyle3.setWrapText(true);

                headerStyle4 = wb.createCellStyle();
                headerStyle4.setFillPattern(SOLID_FOREGROUND);
                headerStyle4.setFillForegroundColor(IndexedColors.LIGHT_GREEN.getIndex());
                headerStyle4.setVerticalAlignment(VerticalAlignment.CENTER);
                headerStyle4.setFont(font);
                headerStyle4.setWrapText(true);

                headerStyle1 = wb.createCellStyle();
                headerStyle1.setWrapText(true);

                HSSFRow xlsRow1 = sheet.createRow(2);
                xlsRow1.setHeightInPoints(30); // 设置行高
                HSSFCell cellTitle0 = xlsRow1.createCell((short) 0);
                // cellTitle0.setEncoding(HSSFCell.ENCODING_UTF_16);
                cellTitle0.setCellValue("脚本外壳");
                cellTitle0.setCellStyle(headerStyle);

                //   sheet.addMergedRegion(new CellRangeAddress(2, 2, (short) 1, (short) 3));
                HSSFCell cellTitle1 = xlsRow1.createCell((short) 1);
                // cellTitle1.setEncoding(HSSFCell.ENCODING_UTF_16);
                cellTitle1.setCellValue(shellH);
                cellTitle1.setCellStyle(headerStyle1);

                HSSFCell cellTitle2 = xlsRow1.createCell((short) 2);
                // cellTitle1.setEncoding(HSSFCell.ENCODING_UTF_16);
                cellTitle2.setCellValue("结束标识");
                cellTitle2.setCellStyle(headerStyle);

                //  sheet.addMergedRegion(new CellRangeAddress(2, 2, (short) 5, (short) 7));
                HSSFCell cellTitle3 = xlsRow1.createCell((short) 3);
                // cellTitle1.setEncoding(HSSFCell.ENCODING_UTF_16);
                cellTitle3.setCellValue(shellEndTag);
                cellTitle3.setCellStyle(headerStyle1);

                HSSFRow xlsRow2 = sheet.createRow(1);
                xlsRow2.setHeightInPoints(30); // 设置行高
                HSSFCell cellTitleProjectName = xlsRow2.createCell((short) 0);
                // cellTitleProjectName.setEncoding(HSSFCell.ENCODING_UTF_16);
                cellTitleProjectName.setCellValue("工程名称");
                cellTitleProjectName.setCellStyle(headerStyle);

                sheet.addMergedRegion(new CellRangeAddress(1, 1, (short) 1, (short) 28));
                HSSFCell cellTitleProjectName1 = xlsRow2.createCell((short) 1);
                // cellTitleProjectName1.setEncoding(HSSFCell.ENCODING_UTF_16);
                cellTitleProjectName1.setCellValue(prjName);
                cellTitleProjectName1.setCellStyle(headerStyleTitle);

                // HSSFFont bold = wb.createFont();
                // bold.setBold(true);
                // bold.setColor(IndexedColors.WHITE.getIndex());
                // headerStyle.setFont(bold);

                HSSFRow xlsRow = sheet.createRow(3);
                xlsRow.setHeightInPoints(25); // 设置行高

                HSSFCell cell = xlsRow.createCell((short) 0);
                // cell.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell.setCellValue("操作步骤序号");
                cell.setCellStyle(headerStyle);

                HSSFCell cell1 = xlsRow.createCell((short) 1);
                // cell1.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell1.setCellValue("所属系统");
                cell1.setCellStyle(headerStyle);

                HSSFCell cell2 = xlsRow.createCell((short) 2);
                // cell2.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell2.setCellValue("子系统名称");
                cell2.setCellStyle(headerStyle);

                HSSFCell cell3 = xlsRow.createCell((short) 3);
                // cell3.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell3.setCellValue("作业名称");
                cell3.setCellStyle(headerStyle);

                HSSFCell cell4 = xlsRow.createCell((short) 4);
                // cell4.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell4.setCellValue("作业描述");
                cell4.setCellStyle(headerStyle);

                HSSFCell cell5 = xlsRow.createCell((short) 5);
                // cell5.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell5.setCellValue("触发作业");
                cell5.setCellStyle(headerStyle);

                HSSFCell cell6 = xlsRow.createCell((short) 6);
                // cell6.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell6.setCellValue("依赖作业");
                cell6.setCellStyle(headerStyle);

                HSSFCell cell7 = xlsRow.createCell((short) 7);
                // cell7.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell7.setCellValue("是否删除");
                cell7.setCellStyle(headerStyle);

                HSSFCell cell8 = xlsRow.createCell((short) 8);
                // cell8.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell8.setCellValue("OK文件绝对路径");
                cell8.setCellStyle(headerStyle);

                HSSFCell cell9 = xlsRow.createCell((short) 9);
                // cell9.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell9.setCellValue("OK文件检测周期");
                cell9.setCellStyle(headerStyle);
                // HSSFCell cell9 = xlsRow.createCell((short) 9);
                // cell9.setEncoding(HSSFCell.ENCODING_UTF_16);
                // cell9.setCellValue("脚本外壳");
                // cell9.setCellStyle(headerStyle);

                HSSFCell cell10 = xlsRow.createCell((short) 10);
                // cell10.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell10.setCellValue("脚本名称");
                cell10.setCellStyle(headerStyle);

                HSSFCell cell11 = xlsRow.createCell((short) 11);
                // cell11.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell11.setCellValue("输出参数");
                cell11.setCellStyle(headerStyle);
                // HSSFCell cell12 = xlsRow.createCell((short) 12);
                // cell12.setEncoding(HSSFCell.ENCODING_UTF_16);
                // cell12.setCellValue("脚本执行成功最后一行打印值");
                // cell12.setCellStyle(headerStyle);

                HSSFCell cell12 = xlsRow.createCell((short) 12);
                // cell12.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell12.setCellValue("业务异常自动重试");
                cell12.setCellStyle(headerStyle);

                HSSFCell cell29 = xlsRow.createCell((short) 13);
                // cell12.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell29.setCellValue("业务异常自动忽略");
                cell29.setCellStyle(headerStyle);

                HSSFCell cell13 = xlsRow.createCell((short) 14);
                // cell13.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell13.setCellValue("Agent资源组");
                cell13.setCellStyle(headerStyle);

                HSSFCell cell14 = xlsRow.createCell((short) 15);
                // cell14.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell14.setCellValue("Agent文件检测资源组");
                cell14.setCellStyle(headerStyle);

                // add
                HSSFCell cell15 = xlsRow.createCell((short) 16);// AgentIP:端口
                cell15.setCellValue("AgentIP:端口");
                cell15.setCellStyle(headerStyle);

                HSSFCell cell16 = xlsRow.createCell((short) 17);// 权重
                cell16.setCellValue("延迟运行");
                cell16.setCellStyle(headerStyle);

                HSSFCell cell17 = xlsRow.createCell((short) 18);
                cell17.setCellValue("执行条件");
                cell17.setCellStyle(headerStyle);

                HSSFCell cell18 = xlsRow.createCell((short) 19);
                cell18.setCellValue("重试次数");
                cell18.setCellStyle(headerStyle);

                HSSFCell cell30 = xlsRow.createCell((short) 20);
                cell30.setCellValue("重试间隔");
                cell30.setCellStyle(headerStyle);

                HSSFCell cell31 = xlsRow.createCell((short) 21);
                cell31.setCellValue("重试结束时间");
                cell31.setCellStyle(headerStyle);

                HSSFCell cell32 = xlsRow.createCell((short) 22);
                cell32.setCellValue("延时报警");
                cell32.setCellStyle(headerStyle);

                HSSFCell cell19 = xlsRow.createCell((short) 23);
                cell19.setCellValue("日历名称");
                cell19.setCellStyle(headerStyle);

                HSSFCell cell20 = xlsRow.createCell((short) 24);// 优先级
                cell20.setCellValue("优先级");
                cell20.setCellStyle(headerStyle);

                HSSFCell cell21 = xlsRow.createCell((short) 25);// 权重
                cell21.setCellValue("权重");
                cell21.setCellStyle(headerStyle);

                HSSFCell cell22 = xlsRow.createCell((short) 26);
                cell22.setCellValue("APT组名称");
                cell22.setCellStyle(headerStyle3);

                HSSFCell cell23 = xlsRow.createCell((short) 27);
                cell23.setCellValue("APT文件名称");
                cell23.setCellStyle(headerStyle3);

                HSSFCell cell24 = xlsRow.createCell((short) 28);
                cell24.setCellValue("ISDB2");
                cell24.setCellStyle(headerStyle3);

                HSSFCell cell25 = xlsRow.createCell((short) 29);
                cell25.setCellValue("DB2IP");
                cell25.setCellStyle(headerStyle3);

                HSSFCell cell26 = xlsRow.createCell((short) 30);
                cell26.setCellValue("主线名称");
                cell26.setCellStyle(headerStyle);

                HSSFCell cell27 = xlsRow.createCell((short) 31);
                cell27.setCellValue("头尾标记");
                cell27.setCellStyle(headerStyle2);

                HSSFCell cell28 = xlsRow.createCell((short) 32);
                cell28.setCellValue("是否禁用");
                cell28.setCellStyle(headerStyle);

                if(ServerEnv.getServerEnv().isJHBankSwitch()){

                    //交行新增功能
                    HSSFCell cell33 = xlsRow.createCell((short) 33);
                    cell33.setCellValue("DAYS");
                    cell33.setCellStyle(headerStyle);

                    HSSFCell cell34 = xlsRow.createCell((short) 34);
                    cell34.setCellValue("LOGIC");
                    cell34.setCellStyle(headerStyle);

                    HSSFCell cell35 = xlsRow.createCell((short) 35);
                    cell35.setCellValue("WDAYS");
                    cell35.setCellStyle(headerStyle);
                }else if(Environment.getInstance().isBhODSUserSwitch()){
                    HSSFCell cell33 = xlsRow.createCell((short) 33);
                    cell33.setCellValue("执行用户");
                    cell33.setCellStyle(headerStyle);
                    HSSFCell cell34 = xlsRow.createCell((short) 34);
                    cell34.setCellValue("虚拟组名称");
                    cell34.setCellStyle(headerStyle);
/*                    HSSFCell cell35 = xlsRow.createCell((short) 35);
                    cell35.setCellValue("ok文件名称");
                    cell35.setCellStyle(headerStyle);*/
                    HSSFCell cell36 = xlsRow.createCell((short) 35);
                    cell36.setCellValue("人工提醒");
                    cell36.setCellStyle(headerStyle);
                }else if( Environment.getInstance().getXanxSwitch()) //西安
                {
                    HSSFCell cell34 = xlsRow.createCell((short) 33);
                    cell34.setCellValue("人工提醒");
                    cell34.setCellStyle(headerStyle);
                    //交行新增功能
                    HSSFCell cell35 = xlsRow.createCell((short) 34);
                    cell35.setCellValue("DAYS");
                    cell35.setCellStyle(headerStyle);

                    HSSFCell cell36 = xlsRow.createCell((short) 35);
                    cell36.setCellValue("LOGIC");
                    cell36.setCellStyle(headerStyle);

                    HSSFCell cell37 = xlsRow.createCell((short) 36);
                    cell37.setCellValue("WDAYS");
                    cell37.setCellStyle(headerStyle);
                    HSSFCell cell38 = xlsRow.createCell((short) 37);
                    cell38.setCellValue("MONTH");
                    cell38.setCellStyle(headerStyle);
                    HSSFCell cell39 = xlsRow.createCell((short) 38);
                    cell39.setCellValue("变更人");
                    cell39.setCellStyle(headerStyle);
                    HSSFCell cell40 = xlsRow.createCell((short) 39);
                    cell40.setCellValue("作业类型");
                    cell40.setCellStyle(headerStyle);

                }else {
/*                    HSSFCell cell33 = xlsRow.createCell((short) 33);
                    cell33.setCellValue("ok文件名称");
                    cell33.setCellStyle(headerStyle);*/
                    HSSFCell cell34 = xlsRow.createCell((short) 33);
                    cell34.setCellValue("人工提醒");
                    cell34.setCellStyle(headerStyle);
                }
            }
            String act = "";
            int num = 0;
            // int offset = 1;
            for (int i = 0; i < list.size(); i++)
            {
                ActRelationInfo flowInfo = list.get(i);
                // if(!flowInfo.getFlowName().equals(tempFlowName))
                // {
                // tempFlowName = flowInfo.getFlowName();
                // offset = i+1;
                // }
                indexMap.put(flowInfo.getActID(), String.valueOf(i + 1));
                HSSFRow xlsRow = sheet.createRow(rowNum++);
                xlsRow.setHeightInPoints(25); // 设置行高

                HSSFCell cell0 = xlsRow.createCell((short) 0);
                // cell0.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell0.setCellValue(flowInfo.getId());
                cell0.setCellStyle(headerStyle1);

                HSSFCell cell1 = xlsRow.createCell((short) 1);
                // cell1.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell1.setCellValue(flowInfo.getSystem());
                cell1.setCellStyle(headerStyle1);

                HSSFCell cell2 = xlsRow.createCell((short) 2);
                // cell2.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell2.setCellValue(flowInfo.getChildproName());
                cell2.setCellStyle(headerStyle1);

                HSSFCell cell3 = xlsRow.createCell((short) 3);
                // cell3.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell3.setCellValue(flowInfo.getActName());
                cell3.setCellStyle(headerStyle1);

                HSSFCell cell4 = xlsRow.createCell((short) 4);
                // cell4.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell4.setCellValue(flowInfo.getActDesc());
                cell4.setCellStyle(headerStyle1);

                HSSFCell cell5 = xlsRow.createCell((short) 5);
                cell5.setCellValue(flowInfo.getPreAct());
                cell5.setCellStyle(headerStyle1);

                HSSFCell cell6 = xlsRow.createCell((short) 6);
                // cell6.setEncoding(HSSFCell.ENCODING_UTF_16);
                if(Environment.getInstance().isBhODSUserSwitch()){
                    boolean cz=false;
                    if(!"".equals(flowInfo.getJoblist())&&null!=flowInfo.getJoblist()){
                        for (String s : flowInfo.getJoblist().split(",")) {
                            Map<String, String> VirtualkeyMap = (Map<String, String>) returnMap.get("VirtualkeyMap");
                            if(VirtualkeyMap.containsKey(s)){//判断是否有虚拟组存在
                                cz=true;
                            }else {

                            }
                        }
                    }
                    if(cz){
                        String Virtualname="";
                        if(Environment.getInstance().isBhODSEXPORTRELYONNAME()){
                            for (int j = 0; j < list.size(); j++)
                            {
                                for (String s : flowInfo.getJoblist().split(",")) {
                                    ActRelationInfo flowInfoj = list.get(j);
                                    if(s.equals(flowInfoj.getActNo())){
                                        boolean status = Virtualname.contains(flowInfoj.getActName());
                                        if(status){

                                        }else {
                                            Virtualname=   flowInfoj.getActName() + "," + Virtualname;
                                        }

                                    }else if(s.equals(flowInfoj.getId())){
                                        boolean status = Virtualname.contains(flowInfoj.getActName());
                                        if(status){

                                        }else {
                                            Virtualname=   flowInfoj.getActName() + "," + Virtualname;
                                        }
                                    }else if(s.equals(flowInfoj.getActName())){
                                        boolean status = Virtualname.contains(flowInfoj.getActName());
                                        if(status){

                                        }else {
                                            Virtualname=   flowInfoj.getActName() + "," + Virtualname;
                                        }
                                    }else {
                                        boolean status = Virtualname.contains(s);
                                        if(status){

                                        }else {
                                            Map<String, String> VirtualkeyMap = (Map<String, String>) returnMap.get("VirtualkeyMap");
                                            if(VirtualkeyMap.containsKey(s)){//判断是否有虚拟组存在
                                                Virtualname=  s + "," + Virtualname;
                                            }
                                        }
                                    }

                                }
                            }
                            if(Virtualname.contains(",")){
                                Virtualname = Virtualname.substring(0,Virtualname.length() - 1);
                            }
                            cell6.setCellValue(Virtualname);
                            cell6.setCellStyle(headerStyle1);
                        }else {

                            for (String s : flowInfo.getJoblist().split(",")) {
                                for (int j = 0; j < list.size(); j++)
                                {
                                    ActRelationInfo flowInfoj = list.get(j);
                                    if(s.contains(flowInfoj.getActNo())){
                                        boolean status = Virtualname.contains(flowInfoj.getId());
                                        if(status){

                                        }else {
                                            Virtualname=   flowInfoj.getId() + "," + Virtualname;
                                        }
                                    }else if(s.equals(flowInfoj.getActName())){
                                        boolean status = Virtualname.contains(flowInfoj.getId());
                                        if(status){

                                        }else {
                                            Virtualname = flowInfoj.getId() + "," + Virtualname;
                                        }
                                    }else if(s.equals(flowInfoj.getId())){
                                        boolean status = Virtualname.contains(flowInfoj.getId());
                                        if(status){

                                        }else {
                                            Virtualname=   flowInfoj.getId() + "," + Virtualname;
                                        }
                                    }else {
                                        boolean status = Virtualname.contains(s);
                                        if (status) {

                                        } else {
                                            Map<String, String> VirtualkeyMap = (Map<String, String>) returnMap.get("VirtualkeyMap");
                                            if(VirtualkeyMap.containsKey(s)){//判断是否有虚拟组存在
                                                Virtualname = s + "," + Virtualname;
                                            }
                                        }
                                    }
                                }
                            }
                            if(Virtualname.contains(",")){
                                Virtualname = Virtualname.substring(0,Virtualname.length() - 1);
                            }

                            cell6.setCellValue(Virtualname);
                            cell6.setCellStyle(headerStyle1);
                        }
                    }else {
                        cell6.setCellValue(flowInfo.getSuccAct());
                        cell6.setCellStyle(headerStyle1);
                    }
                }else {
                    cell6.setCellValue(flowInfo.getSuccAct());
                    cell6.setCellStyle(headerStyle1);
                }

                HSSFCell cell7 = xlsRow.createCell((short) 7);
                // cell7.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell7.setCellValue("否");

                // RangeAddress regions = new RangeAddress(0,0,0,0);

                // //生成下拉框内容
                // String [] list1={"11","34"};
                //
                // DVConstraint constraint = DVConstraint.createExplicitListConstraint(list);
                //
                // //绑定下拉框和作用区域
                //
                // //HSSFDataValidation data_validation = new
                // HSSFDataValidation((short)(1),(short)1,(short)(1),(short)1);
                //
                // HSSFDataValidation data_validation = new HSSFDataValidation(regions,constraint);
                //
                // //对sheet页生效
                //
                // sheet.addValidationData(data_validation);
                //
                HSSFCell cell8 = xlsRow.createCell((short) 8);
                // cell8.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell8.setCellValue(flowInfo.getOkFilePath());
                cell8.setCellStyle(headerStyle1);

                HSSFCell cell9 = xlsRow.createCell((short) 9);
                // cell9.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell9.setCellValue("0".equals(flowInfo.getOkFileWeek()) ? "" : flowInfo.getOkFileWeek());
                cell9.setCellStyle(headerStyle1);
                // HSSFCell cell9 = xlsRow.createCell((short) 9);
                // cell9.setEncoding(HSSFCell.ENCODING_UTF_16);
                // cell9.setCellValue(flowInfo.getShellHouse());
                HSSFCell cell10 = xlsRow.createCell((short) 10);
                // cell10.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell10.setCellValue(flowInfo.getShellName());
                cell10.setCellStyle(headerStyle1);

                HSSFCell cell11 = xlsRow.createCell((short) 11);
                // cell11.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell11.setCellValue(flowInfo.getOutPara());
                cell11.setCellStyle(headerStyle1);
                // HSSFCell cell12 = xlsRow.createCell((short) 12);
                // cell12.setEncoding(HSSFCell.ENCODING_UTF_16);
                // cell12.setCellValue(flowInfo.getLastLine());

                HSSFCell cell12 = xlsRow.createCell((short) 12);
                // cell12.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell12.setCellValue("0".equals(flowInfo.getRedo()) ? "否" : "是");
                cell12.setCellStyle(headerStyle1);

                HSSFCell cell29 = xlsRow.createCell((short) 13);
                // cell12.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell29.setCellValue("0".equals(flowInfo.getSkip()) ? "否" : "是");
                cell29.setCellStyle(headerStyle1);

                HSSFCell cell13 = xlsRow.createCell((short) 14);
                // cell13.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell13.setCellValue(flowInfo.isAgentGroup() ? flowInfo.getAgentGroup() : "");
                cell13.setCellStyle(headerStyle1);

                HSSFCell cell14 = xlsRow.createCell((short) 15);
                // cell14.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell14.setCellValue(flowInfo.getAgentCheckGroup());
                cell14.setCellStyle(headerStyle1);

                // AgentIP:端口 延迟运行 分支条件 重试次数 日历名称

                HSSFCell cell15 = xlsRow.createCell((short) 16);
                cell15.setCellValue(flowInfo.isAgentGroup() ? "" : flowInfo.getAgentGroup());
                cell15.setCellStyle(headerStyle1);

                HSSFCell cell16 = xlsRow.createCell((short) 17);
                // cellWeight.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell16.setCellValue(flowInfo.getDelayTime());
                cell16.setCellStyle(headerStyle1);

                HSSFCell cell17 = xlsRow.createCell((short) 18);
                // cell15.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell17.setCellValue(flowInfo.getBranchCondition());
                cell17.setCellStyle(headerStyle1);

                HSSFCell cell18 = xlsRow.createCell((short) 19);
                // cell16.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell18.setCellValue("0".equals(flowInfo.getReTryCount()) ? "" : flowInfo.getReTryCount());
                cell18.setCellStyle(headerStyle1);

                HSSFCell cell30 = xlsRow.createCell((short) 20);
                // cell16.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell30.setCellValue(flowInfo.getReTryTime());
                cell30.setCellStyle(headerStyle1);

                HSSFCell cell31 = xlsRow.createCell((short) 21);
                // cell16.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell31.setCellValue(flowInfo.getReTryEndTime());
                cell31.setCellStyle(headerStyle1);

                HSSFCell cell32 = xlsRow.createCell((short) 22);
                // cell16.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell32.setCellValue(flowInfo.getDelayWarnning());
                cell32.setCellStyle(headerStyle1);

                HSSFCell cell19 = xlsRow.createCell((short) 23);
                // cellPriority.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell19.setCellValue(flowInfo.getCalendName());
                cell19.setCellStyle(headerStyle1);

                HSSFCell cell20 = xlsRow.createCell((short) 24);
                // cellPriority.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell20.setCellValue(flowInfo.getPriority());
                cell20.setCellStyle(headerStyle1);

                HSSFCell cell21 = xlsRow.createCell((short) 25);
                // cell15.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell21.setCellValue(flowInfo.getWeights());
                cell21.setCellStyle(headerStyle1);

                HSSFCell cell22 = xlsRow.createCell((short) 26);
                // cell15.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell22.setCellValue(flowInfo.getAptGroupName());
                cell22.setCellStyle(headerStyle1);

                HSSFCell cell23 = xlsRow.createCell((short) 27);
                // cell16.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell23.setCellValue(flowInfo.getAptFileName());
                cell23.setCellStyle(headerStyle1);

                HSSFCell cell24 = xlsRow.createCell((short) 28);
                // cell17.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell24.setCellValue(flowInfo.getIsdb2());
                cell24.setCellStyle(headerStyle1);

                HSSFCell cell25 = xlsRow.createCell((short) 29);
                // cell18.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell25.setCellValue(flowInfo.getDb2ip());
                cell25.setCellStyle(headerStyle1);

                HSSFCell cell26 = xlsRow.createCell((short) 30);
                // cell20.setEncoding(HSSFCell.ENCODING_UTF_16);
                cell26.setCellValue(flowInfo.getMainLineName());
                cell26.setCellStyle(headerStyle1);

                int hf = -1;
                if (!"".equals(flowInfo.getHeadFlag()))
                {
                    hf = Integer.parseInt(flowInfo.getHeadFlag());
                }
                String headflag = "";
                switch (hf)
                {
                    case 1:
                        headflag = "总头";
                        break;
                    case 2:
                        headflag = "主线头";
                        break;
                    case 6:
                        headflag = "总尾";
                        break;
                    case 7:
                        headflag = "主线尾";
                        break;
                }
                HSSFCell cell27 = xlsRow.createCell((short) 31);
                cell27.setCellValue(headflag);
                cell27.setCellStyle(headerStyle1);

                if (null != flowInfo.getActName())
                {
                    if (i == list.size() - 1)
                    {
                        act = act + flowInfo.getActName();
                    } else
                    {
                        act = act + flowInfo.getActName() + "、";
                    }
                    num = num + 1;
                }
                HSSFCell cell28 = xlsRow.createCell((short) 32);
                cell28.setCellValue(flowInfo.getIsDisabled());
                cell28.setCellStyle(headerStyle1);

                if(ServerEnv.getServerEnv().isJHBankSwitch()){

                    HSSFCell cell33 = xlsRow.createCell((short) 33);
                    cell33.setCellValue(flowInfo.getDays());
                    cell33.setCellStyle(headerStyle1);

                    HSSFCell cell34 = xlsRow.createCell((short) 34);
                    cell34.setCellValue(flowInfo.getLogic());
                    cell34.setCellStyle(headerStyle1);
                    HSSFCell cell35 = xlsRow.createCell((short) 35);
                    cell35.setCellValue(flowInfo.getWdays());
                    cell35.setCellStyle(headerStyle1);
                }else if(Environment.getInstance().isBhODSUserSwitch()){
                    HSSFCell cell33 = xlsRow.createCell((short) 33);
                    cell33.setCellValue(flowInfo.getPerformUser());
                    cell33.setCellStyle(headerStyle1);
                    HSSFCell cell34 = xlsRow.createCell((short) 34);
                    cell34.setCellValue(flowInfo.getVirtualName());
                    cell34.setCellStyle(headerStyle1);
/*                    HSSFCell cell35 = xlsRow.createCell((short) 35);
                    cell35.setCellValue(flowInfo.getOkFileName());
                    cell35.setCellStyle(headerStyle1);*/
                    HSSFCell cell36 = xlsRow.createCell((short) 35);
                    cell36.setCellValue(flowInfo.getUserTask());
                    cell36.setCellStyle(headerStyle1);
                }else if( Environment.getInstance().getXanxSwitch()) {//西安导入
                    HSSFCell cell34 = xlsRow.createCell((short) 33);
                    cell34.setCellValue(flowInfo.getUserTask());
                    cell34.setCellStyle(headerStyle1);
                    HSSFCell cell35 = xlsRow.createCell((short) 34);
                    cell35.setCellValue(flowInfo.getDays());
                    cell35.setCellStyle(headerStyle1);
                    HSSFCell cell36 = xlsRow.createCell((short) 35);
                    cell36.setCellValue(flowInfo.getLogic());
                    cell36.setCellStyle(headerStyle1);
                    HSSFCell cell37 = xlsRow.createCell((short) 36);
                    cell37.setCellValue(flowInfo.getWdays());
                    cell37.setCellStyle(headerStyle1);
                    HSSFCell cell38 = xlsRow.createCell((short) 37);
                    cell38.setCellValue(flowInfo.getMonth());
                    cell38.setCellStyle(headerStyle1);
                    HSSFCell cell39 = xlsRow.createCell((short) 38);
                    cell39.setCellValue(flowInfo.getChangePerson());
                    cell39.setCellStyle(headerStyle1);
                    HSSFCell cell40 = xlsRow.createCell((short) 39);
                    cell40.setCellValue(flowInfo.getTaskType());
                    cell40.setCellStyle(headerStyle1);

                }else {
/*                    HSSFCell cell33 = xlsRow.createCell((short) 33);
                    cell33.setCellValue(flowInfo.getOkFileName());
                    cell33.setCellStyle(headerStyle1);*/
                    HSSFCell cell34 = xlsRow.createCell((short) 33);
                    cell34.setCellValue(flowInfo.getUserTask());
                    cell34.setCellStyle(headerStyle1);
                }
            }

            // offsetIndex(sheet, indexMap);

            // int j = 0;
            // j = rowNum++;
            // HSSFCellStyle headerStyleTitle1 = wb.createCellStyle();
            // headerStyleTitle1.setVerticalAlignment(VerticalAlignment.TOP);
            // sheet.addMergedRegion(new CellRangeAddress((short) j, (short) 0, (short) j + 1, (short) 13));
            // HSSFRow xlsRowTitle1 = sheet.createRow(j);
            // HSSFCell cellTitle1 = xlsRowTitle1.createCell((short) 0);
            // cellTitle1.setEncoding(HSSFCell.ENCODING_UTF_16);
            // cellTitle1.setCellValue("说明: 工作流中共有" + num + "个活动，如下：" + act);
            // cellTitle1.setCellStyle(headerStyleTitle1);
            wb.write(out);
            _log.info("Export file:" + prjName);
            return file;
        } catch (Exception e)
        {
            _log.error("doRollBackExportXLS method of FlowInfoAction.class to export excel for all result!", e);
            return null;
        } finally
        {
            try
            {
                if (out != null)
                {
                    out.close();
                }
            } catch (IOException e)
            {
                _log.error("doRollBackExportXLS method of FlowInfoAction.class to export excelout.close !", e);
            }
        }
    }



}
