package com.ideal.service.jobscheduling.projects;

import com.ideal.ieai.server.jobscheduling.repository.projects.ProjectsManager;
import com.ideal.ieai.server.jobscheduling.util.taskdownloadbean.ActRelationInfo;
import com.ideal.ieai.server.jobscheduling.util.taskdownloadbean.FlowInfoData;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.service.jobscheduling.taskdownload.TaskDownloadService;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 
 * @ClassName:  ProjectsService   
 * @Description:工程查詢service 
 * @author: Administrator 
 * @date:   2017年7月25日 下午5:07:31   
 *     
 * @Copyright: 2017-2027 www.idealinfo.com Inc. All rights reserved. 
 *
 */
public class ProjectsService
{
    private static ProjectsService intance = new ProjectsService();

    public static ProjectsService getInstance ()
    {
        if (intance == null)
        {
            intance = new ProjectsService();
        }
        return intance;
    }

    /**
     * 
     * @Title: listProjects   
     * @Description: 获取列表数据 
     * @param: @param userId
     * @param: @param page
     * @param: @param limit
     * @param: @param proName
     * @param: @return
     * @param: @throws RepositoryException      
     * @return: Map      
     * @throws   
     * @author: Administrator 
     * @date:   2017年7月25日 下午5:09:46
     */
    public Map listProjects ( String userId, String page, String limit, String proName ) throws RepositoryException
    {
        return ProjectsManager.getInstance().listProjects(userId, page, limit, proName);
    }

    /**
     * 
     * @Title: getProjectDetail   
     * @Description: 获取详细信息   
     * @param: @param IID
     * @param: @param sessionId
     * @param: @param type
     * @param: @return
     * @param: @throws RepositoryException      
     * @return: Map      
     * @throws   
     * @author: Administrator 
     * @date:   2017年7月25日 下午5:09:55
     */
    public Map getProjectDetail ( long iid ) throws RepositoryException
    {
        return ProjectsManager.getInstance().getProjectDetail(iid);

    }

    /**
     * 
     * @Title: getProjectHistory   
     * @Description: 获取历史版本
     * @param: @param iid
     * @param: @param userId
     * @param: @param page
     * @param: @param limit
     * @param: @param proName
     * @param: @return
     * @param: @throws RepositoryException      
     * @return: Map      
     * @throws   
     * @author: Administrator 
     * @date:   2017年7月25日 下午5:09:59
     */
    public Map getProjectHistory ( long iid, int isExcel ,String proName) throws RepositoryException
    {
        return ProjectsManager.getInstance().getProjectHistory(iid, isExcel, proName);
    }

    public String exportTaskExcel(String prjName, HttpServletResponse response) {
        Map<String, FlowInfoData> map = new HashMap<String, FlowInfoData>();
        Map<String, Object> returnMap = TaskDownloadService.exportXLS(prjName, map);
        List<ActRelationInfo> acts = (List<ActRelationInfo>) returnMap.get("ActRelationInfo");
        for (ActRelationInfo act : acts) {
            String projectName = act.getProjectname();   // 假设属性访问方法
            String childProName = act.getChildproName(); // 假设属性访问方法

            // 检查条件：prjName等于ChildproName 且 不等于Projectname
            if (prjName.equals(childProName) && !prjName.equals(projectName)) {
                return "-1";  // 条件满足立即返回-1
            }
        }
        // 创建字节数组输出流，用于在内存中存储Excel数据
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        // 调用生成Excel的方法，将Excel写入字节数组输出流
        String uuid = TaskDownloadService.doExportXLS(outputStream, acts, returnMap, prjName, map, response);

        // 返回字节数组
        return uuid;
    }
}
