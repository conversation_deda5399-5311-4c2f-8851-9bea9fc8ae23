package com.ideal.service.jobscheduling.changeWorkPreview;

import com.ideal.common.utils.SessionData;
import com.ideal.ieai.commons.ClientSession;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.UserInfo;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.server.cluster.manager.ClusterManager;
import com.ideal.ieai.server.ieaikernel.kernel.registry.helper.ClientSessionHelper;
import com.ideal.ieai.server.jobscheduling.repository.changeWorkPreview.ChangeWorkModel;
import com.ideal.ieai.server.jobscheduling.repository.changeWorkPreview.ChangeWorkPreviewManager;
import com.ideal.ieai.server.jobscheduling.repository.changeWorkPreview.WorkPreviewBean;
import com.ideal.ieai.server.jobscheduling.repository.taskCrossMainline.TaskCrossMainlineBean;
import com.ideal.ieai.server.jobscheduling.repository.taskupload.DbUtilUpLoadExcel;
import com.ideal.ieai.server.jobscheduling.util.taskdownloadbean.ActRelationInfo;
import com.ideal.ieai.server.jobscheduling.util.taskuploadbean.ActInfoBean;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.service.jobscheduling.taskdownload.TaskDownloadService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.apache.poi.hssf.usermodel.HSSFDateUtil;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.InputStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class ChangeWorkPreviewService {
    private static final Logger log = Logger.getLogger(ChangeWorkPreviewService.class);

    private static ChangeWorkPreviewService intance = new ChangeWorkPreviewService();

    public static final String             DESEXCELCHECKISOK    ="检查Excel版本是否正确及内容是否正确";

    public static final String             DATALIST    ="dataList";

    public static final String   SUCCESS    ="success";
    public static final String   MESSAGE    ="message";

    private static final int STATUS_NORMAL = 1;
    private static final int STATUS_DELETED = 2;
    private static final int STATUS_ADDED = 3;

    /** 西安标准模板规范列名集合 **/
    private  static final String[] CELL_STR = { "操作步骤序号", "所属系统", "子系统名称", "作业名称", "作业描述", "触发作业", "依赖作业", "是否删除",
            "OK文件绝对路径", "OK文件检测周期", "脚本名称", "输出参数", "业务异常自动重试", "Agent资源组", "Agent文件检测资源组", "优先级", "权重", "APT组名称",
            "APT文件名称", "ISDB2", "DB2IP", "主线名称", "头尾标记", "是否禁用", "AgentIP:端口", "延迟运行", "执行条件", "重试次数", "日历名称", "重试间隔",
            "重试结束时间", "业务异常自动忽略", "延时报警", "人工提醒", "DAYS", "LOGIC", "WDAYS", "MONTH", "变更人", "作业类型", "执行用户"};


    private static final String MX_GRAPH_HEADER = "<mxGraphModel grid=\"1\" guides=\"1\" tooltips=\"1\" connect=\"1\" fold=\"1\" page=\"1\" pageScale=\"1\">";
    private static final String MX_ROOT_START = "<root>";
    private static final String MX_ROOT_END = "</root>";
    private static final String MX_CELL_ID_FORMAT = "<mxCell id=\"%s\"/>";
    private static final String MX_CELL_VERTEX_FORMAT = "<mxCell id=\"%s\" value=\"%s\" realvalue=\"%s\" status=\"%d\" vertex=\"1\" parent=\"0\">";
    private static final String MX_CELL_EDGE_FORMAT = "<mxCell id=\"E%d\" edge=\"1\" parent=\"0\" source=\"%s\" target=\"%s\" status=\"%d\">";
    private static final String MX_GEOMETRY_FORMAT = "<mxGeometry width=\"175\" height=\"30\" as=\"geometry\"/>";
    private static final String MX_GEOMETRY_RELATIVE_FORMAT = "<mxGeometry relative=\"1\" as=\"geometry\"/>";

    public static ChangeWorkPreviewService getInstance ()
    {
        if (intance == null)
        {
            intance = new ChangeWorkPreviewService();
        }
        return intance;
    }



    public Map<String, Object> queryChangeWorkPreview(ChangeWorkModel changeWorkModel ) {
      return  ChangeWorkPreviewManager.getInstance().queryChangeWorkPreview(changeWorkModel);
    }

    public Map<String, Object> checkWorkPreviewExcel(InputStream inputStream, int i, String userName, String incrementalImport) {
        Map returnMap = new HashMap();
        List<WorkPreviewBean> workPreviewBeanList = new ArrayList<>();
        try {
            Map infoCompatible = getActInfoCompatible(inputStream, i, userName);
            String mess = String.valueOf(infoCompatible.get("mess"));
            if (!"success".equals(mess)) {
                returnMap.put(MESSAGE, mess);
                returnMap.put(SUCCESS, false);
                return returnMap;
            }
            String projectName = (String) infoCompatible.get("projectName");
            String childName = (String) infoCompatible.get("childName");
            if (StringUtils.isBlank(projectName) || StringUtils.isBlank(childName)) {
                returnMap.put(MESSAGE, "工程名称或子系统名称不能为空！");
                returnMap.put(SUCCESS, false);
                return returnMap;
            } else {
                //判断是否是第一次上传
                //根据工程名称和子系统名称查询数据库是否有相同的记录
                int exist = ChangeWorkPreviewManager.getInstance().isExistProjectName(projectName, childName);
                if (exist == 0) {
                    //第一次上传
                    returnMap.put(MESSAGE, "工程名称:" + projectName + "子系统名称:" + childName + "第一次上传,不进行预览！");
                    returnMap.put(DATALIST, new ArrayList<>());
                    returnMap.put(SUCCESS, true);
                    return returnMap;
                }
            }
            //获取活动集合  序号和实例
            Map<String, ActInfoBean> mapList = (Map<String, ActInfoBean>) infoCompatible.get("mapList");
            //获取活动集合  活动名和序号
            Map<String, String> keyMap = (Map<String, String>) infoCompatible.get("keyMap");
            //判断是不是第一次上传 根据工程名称和子系统名称
            List<ActInfoBean> upLoadActList = mapList.values().stream().collect(Collectors.toList());
            //获取历史版本
            Map map = DbUtilUpLoadExcel.getInstance().getExcelActInfo(projectName, Constants.IEAI_IEAI);
            List<ActRelationInfo> historyList = (List<ActRelationInfo>) map.get("ActRelationInfo");
            if (historyList.size()>0){
                for (ActRelationInfo actRelationInfo : historyList) {
                    if (!actRelationInfo.isAgentGroup()){
                        actRelationInfo.setAgentInfo(actRelationInfo.getAgentGroup());
                        actRelationInfo.setAgentGroup("");
                    }
                }
            }

            if (StringUtils.isBlank(incrementalImport) || "false".equals(incrementalImport)) {
                //全量导入 数据转换和存储 变更检验
                workPreviewBeanList = getUpLoadActList(upLoadActList, historyList, true, keyMap);
            } else {
                //增量导入 数据转换和存储 变更检验
                workPreviewBeanList = getUpLoadActList(upLoadActList, historyList, false, keyMap);
            }
            returnMap.put(SUCCESS, true);
            returnMap.put(MESSAGE, "预览成功");
            returnMap.put(DATALIST, workPreviewBeanList);

        } catch (Exception e) {
            log.error("checkWorkPreviewExcel is error", e);
            returnMap.put(MESSAGE, "预览失败");
            if (e.getMessage().contains("依赖作业[")) {
                returnMap.put(MESSAGE, e.getMessage());
            }
            returnMap.put(SUCCESS, false);
        }
        return returnMap;
    }


    private List<WorkPreviewBean> getUpLoadActList(List<ActInfoBean> upLoadActList, List<ActRelationInfo> historyList, boolean type, Map<String, String> keyMap) {
        List<WorkPreviewBean> workPreviewBeanList = new ArrayList<>();
        Map<String, ActRelationInfo> mapList = new HashMap<>();
        Map<String, ActRelationInfo> historyActMap = historyList.stream().collect(
                Collectors.toMap(ActRelationInfo::getActName,
                        Function.identity(),
                        (existing, replacement) -> existing
                ));
        Map<String, List<String>> preMap = new HashMap<String, List<String>>();
        Map<String, List<String>> sucMap = new HashMap<String, List<String>>();
        //全量导入,增量导入
        //1.保留作业依赖前后继 依赖作业
        //2.脚本名称,脚本路径,输出参数,
        //3.AgentIP:端口,
        //4.执行频率(DAYS，LOGIC，WDAYS，MONTH)
        //5.作业执行的参数，包括参数个数，参数的顺序
        //6.OK文件绝对路径
        //7.是否禁用
        //8.是否删除
        // 处理历史数据前后继显示
        Map<String, String> history = historyList.stream().collect(Collectors.toMap(ActRelationInfo::getId,
                ActRelationInfo::getActName, // 提取 value
                (oldValue, newValue) -> newValue // 如果 key 重复，保留新的 value
        ));
        //下载 pre 是触发作业
        for (ActRelationInfo actInfo : historyList) {
            String preAct = actInfo.getPreAct();
            if (StringUtils.isNotBlank(preAct)) {
                List<String> preHisList = new ArrayList<>();
                for (String pre : preAct.split(",")) {
                    String hisName = history.get(pre);
                    if (StringUtils.isNotBlank(hisName)) {
                        preHisList.add(hisName);
                    }
                }
                actInfo.setSuccActList(preHisList);
            }
            String sucAct = actInfo.getSuccAct();
            if (StringUtils.isNotBlank(sucAct)) {
                List<String> sucHisList = new ArrayList<>();
                for (String suc : sucAct.split(",")) {
                    String hisName = history.get(suc);
                    if (StringUtils.isNotBlank(hisName)) {
                        sucHisList.add(hisName);
                    }
                }

                actInfo.setPreActList(sucHisList);
            }
        }
        mapList = getActInfo(upLoadActList, keyMap, preMap, sucMap);
        if (mapList.size() > 0) {
            // 跨主线作业依赖关系处理
            List<TaskCrossMainlineBean> taskCrossMainlineBeanList = ChangeWorkPreviewManager.getInstance().getTaskCrossMainlineBeanList();
            if (type) {
                //全量导入
                //1.比较数据库中历史版本和上传版本的作业是否一致
                //2.如果一致，则不做任何操作
                //3.如果不一致，则更新数据库中的作业信息
                workPreviewBeanList = organizeALLWorkPreviewBean(mapList, historyActMap, preMap, sucMap,taskCrossMainlineBeanList);
            } else {
                //增量导入
                workPreviewBeanList = organizeUpdateWorkPreviewBean(mapList, historyActMap, preMap, sucMap,taskCrossMainlineBeanList);
            }
        }
        return workPreviewBeanList;
    }

    /**
     * 全量导入 处理比较数据
     *
     * @param mapList
     * @param historyActMap
     * @param preMap
     * @param sucMap
     * @return
     */
    private List<WorkPreviewBean> organizeALLWorkPreviewBean(Map<String, ActRelationInfo> mapList, Map<String, ActRelationInfo> historyActMap, Map<String, List<String>> preMap, Map<String, List<String>> sucMap,List<TaskCrossMainlineBean> taskCrossMainlineBeanList) {
        List<WorkPreviewBean> workPreviewBeanList = new ArrayList<>();
        List<String> deleteAct = new ArrayList<>();
        List<TaskCrossMainlineBean> crossSuccList = new ArrayList<>();
        List<TaskCrossMainlineBean> crossPreList = new ArrayList<>();
        // 全量导入 处理删除作业
        // 全量导入 处理删除作业
        // 1.获取上传版本中不存在的作业  历史版本中存在的作业 (删除或改名的)
        // 2.全量上传时,如果删除作业存在依赖 则无法删除活动
        if (mapList.size() > 0 && historyActMap.size() > 0) {
            Set<String> keys = mapList.keySet();
            List<String> keyList = new ArrayList<>(keys);
            Set<String> historyKeys = historyActMap.keySet();
            List<String> historyKeyList = new ArrayList<>(historyKeys);
            deleteAct = historyKeyList.stream().filter(key -> !keyList.contains(key)).collect(Collectors.toList());
        }
        // 全量导入 处理作业 是否是新增和删除
        Map<String, Integer> actNameStatus = generateWorkPreviewStatus(mapList, historyActMap);
        // 获取跨主线作业依赖关系
        for (ActRelationInfo actInfo : mapList.values()) {
            // 获取跨主线作业依赖关系

             crossPreList = taskCrossMainlineBeanList.stream()
                    .filter(t -> t != null
                            && actInfo != null
                            && Objects.equals(t.getActName(), actInfo.getActName())
                            && Objects.equals(t.getMainLineName(), actInfo.getMainLineName())
                            && Objects.equals(t.getProjectName(), actInfo.getProjectname()))
                    .collect(Collectors.toList());

             crossSuccList = taskCrossMainlineBeanList.stream()
                    .filter(t -> t != null
                            && actInfo != null
                            && Objects.equals(t.getDependActName(), actInfo.getActName())
                            && Objects.equals(t.getDependMainLineName(), actInfo.getMainLineName())
                            && Objects.equals(t.getDependProjectName(), actInfo.getProjectname()))
                    .collect(Collectors.toList());
            //比较数据库中历史版本和上传版本的作业是否一致
            ActRelationInfo historyActInfo = historyActMap.get(actInfo.getActName());
            WorkPreviewBean workPreviewBean = new WorkPreviewBean();
            String isDisabled = actInfo.getIsDisabled();
            if (isDisabled != null && "1".equals(isDisabled)) {
                isDisabled = "是";
            } else {
                isDisabled = "无";
            }
            createWorkPreviewBean(workPreviewBean, actInfo, historyActInfo);
            //获取前继作业
            //获取前继作业
            List<String> preList = preMap.get(actInfo.getActName());
            String pre = "";
            String suc = "";
            if (preList != null && preList.size() > 0) {
                pre = String.join(",", preList);
                workPreviewBean.setPreActList(preList);
            }
            //获取后继作业
            List<String> sucList = sucMap.get(actInfo.getActName());
            if (sucList != null && sucList.size() > 0) {
                suc = String.join(",", sucList);
                workPreviewBean.setSuccActList(sucList);
            }
            //获取前继作业
            workPreviewBean.setPreActName(pre);
            //获取后继作业
            workPreviewBean.setSuccActName(suc);
            if (historyActInfo == null && "1".equals(actInfo.getFlag())) {
                // actInfo 新增作业
                //获取前继作业
                workPreviewBean.setPreActName(pre);
                //获取后继作业
                workPreviewBean.setSuccActName(suc);
                workPreviewBean.setOpType("新增作业");
            }
            if (historyActInfo != null && "2".equals(actInfo.getFlag())) {
                if (StringUtils.isBlank(pre) && StringUtils.isBlank(suc)) {
                    workPreviewBean.setOpType("删除作业");
                }
            }
            if (historyActInfo != null) {
                String hisPre = "";
                String hisSuc = "";
                if (null != historyActInfo.getPreActList()) {
                    hisPre = String.join(",", historyActInfo.getPreActList());
                }
                if (null != historyActInfo.getSuccActList()) {
                    hisSuc = String.join(",", historyActInfo.getSuccActList());
                }
                boolean p = areStringsEqualIgnoreOrder(pre, hisPre);
                boolean s = areStringsEqualIgnoreOrder(suc, hisSuc);
                // 判断是否为修改作业
                if (!p || !s) {
                    workPreviewBean.setOpType("修改依赖");
                } else if (isExecutionInfoModified(actInfo, historyActInfo, isDisabled)) {
                    workPreviewBean.setOpType("修改作业执行信息");
                }
            }
            //获取 mxGraph 格式的作业依赖关系
            String beforeMxGraph = "";
            if (historyActInfo != null ) {
                beforeMxGraph = getMxGraph(actInfo.getActName(), preList, sucList, historyActInfo.getPreActList(), historyActInfo.getSuccActList(),crossPreList,crossSuccList,actNameStatus,new ArrayList<>());
            }else {
                beforeMxGraph = getMxGraph(actInfo.getActName(), preList, sucList,null,null,crossPreList,crossSuccList,actNameStatus,new ArrayList<>());
            }
            workPreviewBean.setBeforeContext(beforeMxGraph);
            String hisMxGraph = "";
            if (historyActInfo != null && !"新增作业".equals(workPreviewBean.getOpType())) {
                hisMxGraph = getMxGraphHis(actInfo.getActName(), historyActInfo.getPreActList(), historyActInfo.getSuccActList(),crossPreList,crossSuccList,actNameStatus);
            }
            workPreviewBean.setAfterContext(hisMxGraph);
            if (!"".equals(workPreviewBean.getOpType())) {
                workPreviewBeanList.add(workPreviewBean);
            }
        }
        // 全量导入 删除作业
        for (String delAct : deleteAct) {
            ActRelationInfo historyActInfo = historyActMap.get(delAct);
            if (historyActInfo != null) {
                WorkPreviewBean workPreviewBean = new WorkPreviewBean();
                workPreviewBean.setOpType("删除作业");
                workPreviewBean.setActName(delAct);
                String hisDisable = historyActInfo.getIsDisabled();
                if (hisDisable != null && "无".equals(hisDisable)) {
                    hisDisable = "0";
                } else {
                    hisDisable = "1";
                }
                workPreviewBean.setSystemName(historyActInfo.getSystem());
                workPreviewBean.setProjectName(historyActInfo.getProjectname());
                workPreviewBean.setChildName(historyActInfo.getChildproName());
                workPreviewBean.setMainName(historyActInfo.getMainLineName());
                workPreviewBean.setHistoryIsDisable("0".equals(hisDisable) ? "否" : "是");
                workPreviewBean.setHistoryIsDelete("2".equals(historyActInfo.getFlag()) ? "是" : "否");
                workPreviewBean.setHistoryShellName(StringUtils.isBlank(historyActInfo.getShellName()) ? "" : historyActInfo.getShellName());
                workPreviewBean.setHistoryShellPath(StringUtils.isBlank(historyActInfo.getShellHouse()) ? "" : historyActInfo.getShellHouse());
                workPreviewBean.setHistoryAgentInfo(StringUtils.isBlank(historyActInfo.getAgentInfo()) ? "" : historyActInfo.getAgentInfo());
                workPreviewBean.setHistoryAgentGroup(StringUtils.isBlank(historyActInfo.getAgentGroup()) ? "" : historyActInfo.getAgentGroup());
                workPreviewBean.setHistoryDays(StringUtils.isBlank(historyActInfo.getDays()) ? "" : historyActInfo.getDays());
                workPreviewBean.setHistoryLogic(StringUtils.isBlank(historyActInfo.getLogic()) ? "" : historyActInfo.getLogic());
                workPreviewBean.setHistoryWdays(StringUtils.isBlank(historyActInfo.getWdays()) ? "" : historyActInfo.getWdays());
                workPreviewBean.setHistoryMonth(StringUtils.isBlank(historyActInfo.getMonth()) ? "" : historyActInfo.getMonth());
                String hisMxGraph = "";
                hisMxGraph = getMxGraphHis(delAct, historyActInfo.getPreActList(), historyActInfo.getSuccActList(),crossPreList,crossSuccList,actNameStatus);
                workPreviewBean.setAfterContext(hisMxGraph);
                workPreviewBeanList.add(workPreviewBean);
            }
        }
        return workPreviewBeanList;
    }


    /**
     * 判断作业执行信息是否被修改
     */
    private boolean isExecutionInfoModified(ActRelationInfo  actInfo, ActRelationInfo historyActInfo, String isDisabled) {
        return !isFieldEqual(actInfo.getShellName(), historyActInfo.getShellName())
                || !isFieldEqual(actInfo.getShellHouse(), historyActInfo.getShellHouse())
                || !isFieldEqual(actInfo.getAgentInfo(), historyActInfo.getAgentInfo())
                || !isFieldEqual(actInfo.getAgentGroup(), historyActInfo.getAgentGroup())
                || !isFieldEqual(actInfo.getDays(), historyActInfo.getDays())
                || !isFieldEqual(actInfo.getLogic(), historyActInfo.getLogic())
                || !isFieldEqual(actInfo.getWdays(), historyActInfo.getWdays())
                || !isFieldEqual(actInfo.getMonth(), historyActInfo.getMonth())
                || !isFieldEqual(isDisabled, historyActInfo.getIsDisabled());
    }

    /**
     * 判断两个字段是否相等（处理 null 值）
     */
    private boolean isFieldEqual(String field1, String field2) {
        if (field1 == null && field2 == null) {
            return true;
        }
        if (StringUtils.isBlank(field1) && StringUtils.isBlank(field2)) {
            return true;
        }
        if (field1 == null && field2 != null){
            return false;
        }
        return field1.equals(field2);
    }

    public static boolean areStringsEqualIgnoreOrder(String str1, String str2) {

        // 检查空字符串
        if (str1.isEmpty() || str2.isEmpty()) {
            return str1.isEmpty() && str2.isEmpty();
        }

//        // 拆分、去除空格、统一大小写并转换为 Set
//        Set<String> set1 = Arrays.stream(str1.split(","))
//                .map(String::trim)
//                .map(String::toLowerCase)
//                .collect(Collectors.toSet());
//        Set<String> set2 = Arrays.stream(str2.split(","))
//                .map(String::trim)
//                .map(String::toLowerCase)
//                .collect(Collectors.toSet());
        // 将字符串拆分为 Set
        Set<String> set1 = Arrays.stream(str1.split(",")).collect(Collectors.toSet());
        Set<String> set2 = Arrays.stream(str2.split(",")).collect(Collectors.toSet());

        // 比较两个 Set
        return set1.equals(set2);
    }

    private void createWorkPreviewBean(WorkPreviewBean workPreviewBean, ActRelationInfo actInfo, ActRelationInfo historyActInfo) {

        workPreviewBean.setOpType("");
        workPreviewBean.setActName(actInfo.getActName());
        workPreviewBean.setSystemName(actInfo.getSystem());
        workPreviewBean.setProjectName(actInfo.getProjectname());
        workPreviewBean.setChildName(actInfo.getChildproName());
        workPreviewBean.setShellName(StringUtils.isBlank(actInfo.getShellName()) ? "" : actInfo.getShellName());
        workPreviewBean.setShellPath(StringUtils.isBlank(actInfo.getShellHouse()) ? "" : actInfo.getShellHouse());
        workPreviewBean.setAgentInfo(StringUtils.isBlank(actInfo.getAgentInfo()) ? "" : actInfo.getAgentInfo());
        workPreviewBean.setAgentGroup(StringUtils.isBlank(actInfo.getAgentGroup()) ? "" : actInfo.getAgentGroup());
        workPreviewBean.setMainName(actInfo.getMainLineName());
        workPreviewBean.setDays(StringUtils.isBlank(actInfo.getDays()) ? "" : actInfo.getDays());
        workPreviewBean.setLogic(StringUtils.isBlank(actInfo.getLogic()) ? "" : actInfo.getLogic());
        workPreviewBean.setWdays(StringUtils.isBlank(actInfo.getWdays()) ? "" : actInfo.getWdays());
        workPreviewBean.setMonth(StringUtils.isBlank(actInfo.getMonth()) ? "" : actInfo.getMonth());
        workPreviewBean.setIsDelete("2".equals(actInfo.getFlag()) ? "是" : "否");
        workPreviewBean.setIsDisable("0".equals(actInfo.getIsDisabled()) ? "否" : "是");

        if (historyActInfo != null) {
            String hisDisable = historyActInfo.getIsDisabled();
            if (hisDisable != null && "无".equals(hisDisable)) {
                hisDisable = "0";
            } else {
                hisDisable = "1";
            }
            workPreviewBean.setSystemName(historyActInfo.getSystem());
            workPreviewBean.setProjectName(historyActInfo.getProjectname());
            workPreviewBean.setChildName(historyActInfo.getChildproName());
            workPreviewBean.setMainName(historyActInfo.getMainLineName());
            workPreviewBean.setHistoryIsDisable("0".equals(hisDisable) ? "否" : "是");
            workPreviewBean.setHistoryIsDelete("2".equals(historyActInfo.getFlag()) ? "是" : "否");
            workPreviewBean.setHistoryShellName(StringUtils.isBlank(historyActInfo.getShellName()) ? "" : historyActInfo.getShellName());
            workPreviewBean.setHistoryShellPath(StringUtils.isBlank(historyActInfo.getShellHouse()) ? "" : historyActInfo.getShellHouse());
            workPreviewBean.setHistoryAgentInfo(StringUtils.isBlank(historyActInfo.getAgentInfo()) ? "" : historyActInfo.getAgentInfo());
            workPreviewBean.setHistoryAgentGroup(StringUtils.isBlank(historyActInfo.getAgentGroup()) ? "" : historyActInfo.getAgentGroup());
            workPreviewBean.setHistoryDays(StringUtils.isBlank(historyActInfo.getDays()) ? "" : historyActInfo.getDays());
            workPreviewBean.setHistoryLogic(StringUtils.isBlank(historyActInfo.getLogic()) ? "" : historyActInfo.getLogic());
            workPreviewBean.setHistoryWdays(StringUtils.isBlank(historyActInfo.getWdays()) ? "" : historyActInfo.getWdays());
            workPreviewBean.setHistoryMonth(StringUtils.isBlank(historyActInfo.getMonth()) ? "" : historyActInfo.getMonth());
        }

    }


    /**
     * //增量导入  保留作业依赖前后继 依赖作业
     *             //OK文件绝对路径
     *             //脚本名称
     *             //AgentIP:端口
     *             //执行频率(DAYS，LOGIC，WDAYS，MONTH)
     * @param upLoadActList
     * @return
     */
    private Map<String, ActRelationInfo> getActInfo(List<ActInfoBean> upLoadActList,Map<String, String> keyMap,Map<String, List<String>> preMap,Map<String, List<String>> sucMap) {
        Map<String, ActRelationInfo> mapList = new HashMap<>();
        for (ActInfoBean aib : upLoadActList) {
            // 西安需求确认 只比较
            ActRelationInfo relationInfo = new ActRelationInfo();
            //序号

            relationInfo.setId(aib.getActNo());
            //作业名称
            relationInfo.setActName(aib.getActName());
            //所属系统
            relationInfo.setSystem(aib.getSystem());
            //工程名称
            relationInfo.setProjectname(aib.getProjectName());
            //子系统名称
            relationInfo.setChildproName(aib.getChildProjectName());
            // 依赖作业 重点
            List<String> beforeActList = aib.getBeforeActList();
            List<String> preActList = getPreAndSuccList(beforeActList, keyMap);
            for (String preAct : preActList){
                if (sucMap.containsKey(preAct)){
                    List<String> sucList = sucMap.get(preAct);
                    if (!sucList.contains(aib.getActName())) {
                        sucList.add(aib.getActName());
                        sucMap.put(preAct, sucList);
                    }
                }else {
                    List<String> sucList = new ArrayList<>();
                    sucList.add(aib.getActName());
                    sucMap.put(preAct, sucList);
                }
                if (preMap.containsKey(aib.getActName())){
                    List<String> preList = preMap.get(aib.getActName());
                    if (!preList.contains(preAct)) {
                        preList.add(preAct);
                        preMap.put(aib.getActName(), preList);
                    }
                }else {
                    List<String> preList = new ArrayList<>();
                    preList.add(preAct);
                    preMap.put(aib.getActName(), preList);
                }
            }
            relationInfo.setPreActList(preActList);

            // 作业依赖 重点
            List<String> afterActList = aib.getAfterActList();
            List<String> afterList = getPreAndSuccList(afterActList, keyMap);
            for (String sucAct : afterList){
                if (sucMap.containsKey(aib.getActName())){
                    List<String> sucList = sucMap.get(aib.getActName());
                    if (!sucList.contains(sucAct)) {
                        sucList.add(sucAct);
                        sucMap.put(aib.getActName(), sucList);
                    }
                }else {
                    List<String> sucList = new ArrayList<>();
                    sucList.add(sucAct);
                    sucMap.put(aib.getActName(), sucList);
                }
                if (preMap.containsKey(sucAct)){
                    List<String> preList = preMap.get(sucAct);
                    if (!preList.contains(aib.getActName())) {
                        preList.add(aib.getActName());
                        preMap.put(sucAct, preList);
                    }
                }else {
                    List<String> preList = new ArrayList<>();
                    preList.add(aib.getActName());
                    preMap.put(sucAct, preList);
                }
            }

            relationInfo.setSuccActList(afterList);
            //脚本名称
            relationInfo.setShellName(aib.getShellABPath());
            //脚本路径
            relationInfo.setShellHouse(aib.getShellCrust());
            //输出参数
            relationInfo.setOutPara(aib.getOutputPamaeter());
            //AgentIP:端口
            relationInfo.setAgentInfo(aib.getAgentInfo());
            //AptGroupName
            relationInfo.setAgentGroup(aib.getAgentGropName());
            //主线名称
            relationInfo.setMainLineName(aib.getMainline());
            // 5 执行频率(DAYS，LOGIC，WDAYS，MONTH)
            relationInfo.setDays(aib.getDays());
            relationInfo.setLogic(aib.getLogic());
            relationInfo.setWdays(aib.getWdays());
            relationInfo.setMonth(aib.getMonth());

            // 是否禁用
            relationInfo.setIsDisabled(aib.getDisableFlag());
            //是否删除
            relationInfo.setFlag(aib.getDeleteFlag());
            // 5 执行频率(DAYS，LOGIC，WDAYS，MONTH)
            mapList.put(aib.getActName(), relationInfo);
        }
        return mapList;
    }

    /**
     * 获取变更预览信息
     *
     * @return
     */
    private List<WorkPreviewBean> organizeUpdateWorkPreviewBean(Map<String, ActRelationInfo> mapList, Map<String, ActRelationInfo> historyActMap, Map<String, List<String>> preMap, Map<String, List<String>> sucMap,List<TaskCrossMainlineBean> taskCrossMainlineBeanList) {
        //比较数据库中历史版本和上传版本的作业是否一致
        List<WorkPreviewBean> workPreviewBeanList = new ArrayList<>();
        List<TaskCrossMainlineBean> crossSuccList = new ArrayList<>();
        List<TaskCrossMainlineBean> crossPreList = new ArrayList<>();
        //增量导入获取作业状态
        Map<String, Integer> actNameStatus = new HashMap<>();
        List<String> deleteAct = new ArrayList<>();
        generateUpdateWorkPreviewStatus(mapList, historyActMap, preMap, sucMap,actNameStatus,deleteAct);
        for (ActRelationInfo actInfo : mapList.values()) {

            crossPreList = taskCrossMainlineBeanList.stream()
                    .filter(t -> t != null
                            && actInfo != null
                            && Objects.equals(t.getActName(), actInfo.getActName())
                            && Objects.equals(t.getMainLineName(), actInfo.getMainLineName())
                            && Objects.equals(t.getProjectName(), actInfo.getProjectname()))
                    .collect(Collectors.toList());

            crossSuccList = taskCrossMainlineBeanList.stream()
                    .filter(t -> t != null
                            && actInfo != null
                            && Objects.equals(t.getDependActName(), actInfo.getActName())
                            && Objects.equals(t.getDependMainLineName(), actInfo.getMainLineName())
                            && Objects.equals(t.getDependProjectName(), actInfo.getProjectname()))
                    .collect(Collectors.toList());
            ActRelationInfo historyActInfo = historyActMap.get(actInfo.getActName());
            WorkPreviewBean workPreviewBean = new WorkPreviewBean();
            String isDisabled = actInfo.getIsDisabled();
            if (isDisabled != null && "1".equals(isDisabled)) {
                isDisabled = "是";
            } else {
                isDisabled = "无";
            }
            createWorkPreviewBean(workPreviewBean, actInfo, historyActInfo);


            //获取前继作业
            //获取前继作业
            List<String> preList = preMap.get(actInfo.getActName());
            String pre = "";
            String suc = "";
            if (preList != null && preList.size() > 0) {
                pre = String.join(",", preList);
                workPreviewBean.setPreActList(preList);
            }
            //获取后继作业
            List<String> sucList = sucMap.get(actInfo.getActName());
            if (sucList != null && sucList.size() > 0) {
                suc = String.join(",", sucList);
                workPreviewBean.setSuccActList(sucList);
            }

            if (historyActInfo == null && "1".equals(actInfo.getFlag())) {
                // actInfo 新增作业
                workPreviewBean.setOpType("新增作业");
            }
            if (historyActInfo != null && "1".equals(actInfo.getFlag())) {
                String hisPre = "";
                String hisSuc = "";
                 // 增量上传 没有删除依赖的情况

                if (null != historyActInfo.getPreActList()) {
                    hisPre = String.join(",", historyActInfo.getPreActList());
                    if (preList != null && preList.size() > 0) {
                        preList.addAll(historyActInfo.getPreActList());
                        preList = preList.stream().distinct().collect(Collectors.toList());
                        pre = String.join(",", preList);
                    }else {
                        preList = historyActInfo.getPreActList();
                        pre = hisPre;
                    }
                }
                if (null != historyActInfo.getSuccActList()) {
                    hisSuc = String.join(",", historyActInfo.getSuccActList());
                    if (sucList != null && sucList.size() > 0) {
                        sucList.addAll(historyActInfo.getSuccActList());
                        sucList = sucList.stream().distinct().collect(Collectors.toList());
                        suc = String.join(",", sucList);
                    }else {
                        sucList = historyActInfo.getSuccActList();
                        suc = hisSuc;
                    }
                }
                for (String name : deleteAct) {
                    if (pre.contains(name)){
                        String[] parts = pre.split(",");
                        List<String> strings = new ArrayList<>(Arrays.asList(parts));
                        strings.removeIf(s -> s.equals(name));
                        pre = String.join(",", strings);
                    }
                    if (suc.contains(name)) {
                        String[] parts = suc.split(",");
                        List<String> strings = new ArrayList<>(Arrays.asList(parts)); // 转换为ArrayList
                        strings.removeIf(s -> s.equals(name)); // 现在可以安全地调用removeIf
                        suc = String.join(",", strings);
                    }
                }
                boolean p = areStringsEqualIgnoreOrder(pre, hisPre);
                boolean s = areStringsEqualIgnoreOrder(suc, hisSuc);
                // 判断是否为修改作业
                if (!p || !s) {
                    workPreviewBean.setOpType("修改依赖");
                } else if (isExecutionInfoModified(actInfo, historyActInfo, isDisabled)) {
                    workPreviewBean.setOpType("修改作业执行信息");
                }
            }
            if (historyActInfo != null && "2".equals(actInfo.getFlag())) {
                if (StringUtils.isNotBlank(pre) || StringUtils.isNotBlank(suc)) {
                    workPreviewBean.setOpType("删除依赖");
                    preList = new ArrayList<>();
                    sucList = new ArrayList<>();
                } else {
                    workPreviewBean.setOpType("删除作业");
                }
            }
            workPreviewBean.setPreActName(pre);
            workPreviewBean.setPreActList(preList);
            //获取后继作业
            workPreviewBean.setSuccActName(suc);
            workPreviewBean.setSuccActList(sucList);
            //获取 mxGraph 格式的作业依赖关系
            String beforeMxGraph ="";
            if (historyActInfo != null ) {
                beforeMxGraph = getMxGraph(actInfo.getActName(), preList, sucList, historyActInfo.getPreActList(), historyActInfo.getSuccActList(), crossPreList, crossSuccList,actNameStatus,deleteAct);
            }else {
                beforeMxGraph = getMxGraph(actInfo.getActName(), preList, sucList,null,null,crossPreList, crossSuccList,actNameStatus,deleteAct);
            }
            workPreviewBean.setBeforeContext(beforeMxGraph);
            String hisMxGraph = "";
            if (historyActInfo != null && !"新增作业".equals(workPreviewBean.getOpType())) {
                hisMxGraph = getMxGraphHis(actInfo.getActName(), historyActInfo.getPreActList(), historyActInfo.getSuccActList(), crossPreList, crossSuccList,actNameStatus);
            }
            workPreviewBean.setAfterContext(hisMxGraph);
            if (!"".equals(workPreviewBean.getOpType())){
                workPreviewBeanList.add(workPreviewBean);
            }
        }
        return workPreviewBeanList;
    }


    /**
     * 获取历史版本的作业依赖关系
     * @param actName
     * @param preList
     * @param sucList
     * @return
     */
    private String getMxGraphHis(String actName, List<String> preList, List<String> sucList,
                                 List<TaskCrossMainlineBean> crossPreList,List<TaskCrossMainlineBean> crossSucList,Map<String, Integer> actNameStatus    ) {
        StringBuilder sb = new StringBuilder();
        try {

            // 构建 mxGraph 头部
            sb.append(MX_GRAPH_HEADER).append(MX_ROOT_START)
                    .append(String.format(MX_CELL_ID_FORMAT, "-1"))
                    .append("<mxCell id=\"0\" parent=\"-1\"/>")
                    .append("<mxCell id=\"-2\" value=\"\" projectName=\"\" flowName=\"\" actDes=\"\" parent=\"0\">")
                    .append(MX_GEOMETRY_FORMAT).append("</mxCell>");

            // 添加当前作业节点
            appendVertexMain(sb, actName, actName, actNameStatus,1,new ArrayList<>());

            // 添加前继节点
            appendVertices(sb, preList, actNameStatus, 1, new ArrayList<>());

            // 添加后继节点
            appendVertices(sb, sucList, actNameStatus,1, new ArrayList<>());

            // 添加跨主线前继节点
            appendCrossVertices(sb, crossPreList, 1);

            // 添加跨主线后继节点
            appendCrossVertices(sb, crossSucList, 3);

            // 添加边
            int edgeCounter = 0;
            edgeCounter = appendPreEdgesHis(sb, preList, actName, edgeCounter, 1);
            edgeCounter = appendSucEdgesHis(sb, sucList, actName, edgeCounter, 1);
            edgeCounter = appendCrossEdges(sb, crossPreList, actName, edgeCounter, 4,1);
            edgeCounter = appendCrossEdges(sb, crossSucList, actName, edgeCounter, 4,2);

            // 结束 mxGraph
            sb.append(MX_ROOT_END).append("</mxGraphModel>");

        } catch (Exception e) {
            log.error("getMxGraph is error", e);
        }
        return sb.toString();
    }

    /**
     * 添加边
     *
     * @param sb          StringBuilder
     * @param nodes       节点列表
     * @param target      目标节点
     * @param edgeCounter 边计数器
     * @param status      边状态
     * @return 更新后的边计数器
     */
    private int appendPreEdgesHis(StringBuilder sb, List<String> nodes, String target, int edgeCounter, int status) {
        if (nodes != null && !nodes.isEmpty()) {
            for (String node : nodes) {
                edgeCounter++;
                sb.append(String.format(MX_CELL_EDGE_FORMAT, edgeCounter, node, target, status))
                        .append(MX_GEOMETRY_RELATIVE_FORMAT).append("</mxCell>");
            }
        } else {
            edgeCounter++;
            sb.append(String.format(MX_CELL_EDGE_FORMAT, edgeCounter, 0, target, status))
                    .append(MX_GEOMETRY_RELATIVE_FORMAT).append("</mxCell>");
        }
        return edgeCounter;
    }

    /**
     * 添加边
     *
     * @param sb          StringBuilder
     * @param nodes       节点列表
     * @param target      目标节点
     * @param edgeCounter 边计数器
     * @param status      边状态
     * @return 更新后的边计数器
     */
    private int appendSucEdgesHis(StringBuilder sb, List<String> nodes, String target, int edgeCounter, int status) {
        if (nodes != null && !nodes.isEmpty()) {
            for (String node : nodes) {
                edgeCounter++;
                sb.append(String.format(MX_CELL_EDGE_FORMAT, edgeCounter, target,node, status))
                        .append(MX_GEOMETRY_RELATIVE_FORMAT).append("</mxCell>");
            }
        }
        return edgeCounter;
    }

    /**
     * 添加跨主线边
     *
     * @param sb          StringBuilder
     * @param crossNodes  跨主线节点列表
     * @param target      目标节点
     * @param edgeCounter 边计数器
     * @param status      边状态
     * @return 更新后的边计数器
     */
    private int appendCrossEdges(StringBuilder sb, List<TaskCrossMainlineBean> crossNodes, String target, int edgeCounter, int status, int type) {
        if (crossNodes != null && !crossNodes.isEmpty()) {
            for (TaskCrossMainlineBean cross : crossNodes) {
                edgeCounter++;
                if (1 == type) {
                    sb.append(String.format(MX_CELL_EDGE_FORMAT, edgeCounter, cross.getDependActName(), target, status))
                            .append(MX_GEOMETRY_RELATIVE_FORMAT).append("</mxCell>");
                } else {
                    sb.append(String.format(MX_CELL_EDGE_FORMAT, edgeCounter, target, cross.getActName(), status))
                            .append(MX_GEOMETRY_RELATIVE_FORMAT).append("</mxCell>");
                }
            }
        }
        return edgeCounter;
    }

    /**
     * 添加节点
     *
     * @param sb      StringBuilder
     * @param id      节点 ID
     * @param value   节点值
     * @param status  节点状态
     */
    private void appendVertex(StringBuilder sb, String id, String value, int status) {
        sb.append(String.format(MX_CELL_VERTEX_FORMAT, id, value, value, status))
                .append(MX_GEOMETRY_FORMAT).append("</mxCell>");
    }

    /**
     * 添加主节点
     *
     * @param sb      StringBuilder
     * @param id      节点 ID
     * @param value   节点值
     * @param actNameStatus  节点状态
     */
    private void appendVertexMain(StringBuilder sb, String id, String value, Map<String, Integer> actNameStatus, int type, List<String> deleteAct) {
        int status = actNameStatus.get(value);
        if (1 == type) {
            status = 1;
        }
        if (!deleteAct.contains(value)) {
            appendVertex(sb, id, value, status);
        }

    }

    /**
     * 添加多个节点
     *
     * @param sb      StringBuilder
     * @param nodes   节点列表
     * @param actNameStatus  节点状态
     */
    private void appendVertices(StringBuilder sb, List<String> nodes, Map<String, Integer> actNameStatus, int type, List<String> deleteAct) {
        if (nodes != null && !nodes.isEmpty()) {
            for (String node : nodes) {
                int status = actNameStatus.get(node);
                if (1 == type) {
                    status = 1;
                }
                if (!deleteAct.contains(node)) {
                    appendVertex(sb, node, node, status);
                }
            }
        }
    }

    /**
     * 添加跨主线节点
     *
     * @param sb      StringBuilder
     * @param crossNodes 跨主线节点列表
     * @param status  节点状态
     */
    private void appendCrossVertices(StringBuilder sb, List<TaskCrossMainlineBean> crossNodes, int status) {
        if (crossNodes != null && !crossNodes.isEmpty()) {
            for (TaskCrossMainlineBean cross : crossNodes) {
                if (1== status){
                    appendVertex(sb, cross.getDependActName(), cross.getDependActName(), 4);
                }else {
                    appendVertex(sb, cross.getActName(), cross.getActName(), 4);
                }

            }
        }
    }


    /**
     * 添加历史节点（仅当不在当前节点列表中）
     *
     * @param sb          StringBuilder
     * @param historicalNodes 历史节点列表
     * @param currentNodes    当前节点列表
     * @param actNameStatus      节点状态
     */
    private void appendHistoricalVertices(StringBuilder sb, List<String> historicalNodes, List<String> currentNodes, Map<String, Integer> actNameStatus) {
        if (historicalNodes != null && !historicalNodes.isEmpty()) {
            for (String node : historicalNodes) {
                if (currentNodes == null || currentNodes.isEmpty() || !currentNodes.contains(node)) {
                    int status = actNameStatus.get(node);
                    appendVertex(sb, node, node, status);
                }
            }
        }
    }

    /**
     * 添加边
     *
     * @param sb          StringBuilder
     * @param nodes       节点列表
     * @param target      目标节点
     * @param edgeCounter 边计数器
     * @param historicalNodes 历史节点列表
     * @param newStatus   新增边状态
     * @param oldStatus   已有边状态
     * @return 更新后的边计数器
     */
    private int appendPreEdges(StringBuilder sb, List<String> nodes, String target, int edgeCounter,
                            List<String> historicalNodes, int newStatus, int oldStatus,List<String> deleteAct) {
        if (deleteAct.contains(target)){
            return edgeCounter;
        }
        if (nodes != null && !nodes.isEmpty()) {
            for (String node : nodes) {
                if (deleteAct.contains(node)){
                    continue;
                }
                edgeCounter++;
                if (historicalNodes == null || historicalNodes.isEmpty() || !historicalNodes.contains(node)) {
                    sb.append(String.format(MX_CELL_EDGE_FORMAT, edgeCounter, node, target, newStatus))
                            .append(MX_GEOMETRY_RELATIVE_FORMAT).append("</mxCell>");
                } else {
                    sb.append(String.format(MX_CELL_EDGE_FORMAT, edgeCounter, node, target, oldStatus))
                            .append(MX_GEOMETRY_RELATIVE_FORMAT).append("</mxCell>");
                }
            }
        }else {
            edgeCounter++;
            sb.append(String.format(MX_CELL_EDGE_FORMAT, edgeCounter , 0, target, newStatus))
                    .append(MX_GEOMETRY_RELATIVE_FORMAT).append("</mxCell>");
        }
        return edgeCounter;
    }

    /**
     * 添加边
     *
     * @param sb          StringBuilder
     * @param nodes       节点列表
     * @param target      目标节点
     * @param edgeCounter 边计数器
     * @param historicalNodes 历史节点列表
     * @param newStatus   新增边状态
     * @param oldStatus   已有边状态
     * @return 更新后的边计数器
     */
    private int appendSucEdges(StringBuilder sb, List<String> nodes, String target, int edgeCounter,
                               List<String> historicalNodes, int newStatus, int oldStatus,List<String> deleteAct) {
        if (deleteAct.contains(target)){
            return edgeCounter;
        }
        if (nodes != null && !nodes.isEmpty()) {
            for (String node : nodes) {
                if (deleteAct.contains(node)){
                    continue;
                }
                edgeCounter++;
                if (historicalNodes == null || historicalNodes.isEmpty() || !historicalNodes.contains(node)) {
                    sb.append(String.format(MX_CELL_EDGE_FORMAT, edgeCounter, target, node, newStatus))
                            .append(MX_GEOMETRY_RELATIVE_FORMAT).append("</mxCell>");
                } else {
                    sb.append(String.format(MX_CELL_EDGE_FORMAT, edgeCounter, target, node, oldStatus))
                            .append(MX_GEOMETRY_RELATIVE_FORMAT).append("</mxCell>");
                }
            }
        }
        return edgeCounter;
    }

    /**
     * 添加历史边（仅当不在当前节点列表中）
     *
     * @param sb          StringBuilder
     * @param historicalNodes 历史节点列表
     * @param currentNodes    当前节点列表
     * @param target      目标节点
     * @param edgeCounter 边计数器
     * @param status      边状态
     * @return 更新后的边计数器
     */
    private int appendHistoricalEdges(StringBuilder sb, List<String> historicalNodes, List<String> currentNodes,
                                      String target, int edgeCounter, int status, int type) {
        if (historicalNodes != null && !historicalNodes.isEmpty()) {
            for (String node : historicalNodes) {
                if (currentNodes == null || currentNodes.isEmpty() || !currentNodes.contains(node)) {
                    edgeCounter++;
                    if (1 == type) {
                        sb.append(String.format(MX_CELL_EDGE_FORMAT, edgeCounter, node, target, status))
                                .append(MX_GEOMETRY_RELATIVE_FORMAT).append("</mxCell>");
                    } else {
                        sb.append(String.format(MX_CELL_EDGE_FORMAT, edgeCounter, target, node, status))
                                .append(MX_GEOMETRY_RELATIVE_FORMAT).append("</mxCell>");
                    }

                }
            }
        }
        return edgeCounter;
    }



    /**
     * 获取 mxGraph 格式的作业依赖关系
     *
     * @param actName       作业名称
     * @param preList       前继作业列表
     * @param sucList       后继作业列表
     * @param preHisList    历史前继作业列表
     * @param sucHisList    历史后继作业列表
     * @param crossPreList  跨主线前继作业列表
     * @param crossSucList  跨主线后继作业列表
     * @return 返回 mxGraph 格式的 XML 字符串
     */
    private String getMxGraph(String actName, List<String> preList, List<String> sucList,
                              List<String> preHisList, List<String> sucHisList,
                              List<TaskCrossMainlineBean> crossPreList, List<TaskCrossMainlineBean> crossSucList,Map<String, Integer> actNameStatus,List<String> deleteAct) {
        StringBuilder sb = new StringBuilder();
        try {
            // 构建 mxGraph 头部
            sb.append(MX_GRAPH_HEADER).append(MX_ROOT_START)
                    .append(String.format(MX_CELL_ID_FORMAT, "-1"))
                    .append("<mxCell id=\"0\" parent=\"-1\"/>")
                    .append("<mxCell id=\"-2\" value=\"\" projectName=\"\" flowName=\"\" actDes=\"\" parent=\"0\">")
                    .append(MX_GEOMETRY_FORMAT).append("</mxCell>");

            // 添加当前作业节点
            if (deleteAct.contains(actName)){
                sb = new StringBuilder();
                return sb.toString();
            }
            appendVertexMain(sb, actName, actName,actNameStatus,0,deleteAct);

            // 添加前继节点
            appendVertices(sb, preList, actNameStatus,0,deleteAct);

            // 添加后继节点
            appendVertices(sb, sucList, actNameStatus,0,deleteAct);

            // 添加历史前继节点（仅当不在当前前继列表中）
            appendHistoricalVertices(sb, preHisList, preList, actNameStatus);

            // 添加历史后继节点（仅当不在当前后继列表中）
            appendHistoricalVertices(sb, sucHisList, sucList, actNameStatus);

            // 添加跨主线前继节点
            appendCrossVertices(sb, crossPreList, 1);

            // 添加跨主线后继节点
            appendCrossVertices(sb, crossSucList, 3);

            // 添加边
            int edgeCounter = 0;
            edgeCounter = appendPreEdges(sb, preList, actName, edgeCounter, preHisList, 3, 1,deleteAct);
            edgeCounter = appendSucEdges(sb, sucList, actName, edgeCounter, sucHisList, 3, 1,deleteAct);
            edgeCounter = appendHistoricalEdges(sb, preHisList, preList, actName, edgeCounter, 2,1);
            edgeCounter = appendHistoricalEdges(sb, sucHisList, sucList, actName, edgeCounter, 2,2);
            edgeCounter = appendCrossEdges(sb, crossPreList, actName, edgeCounter, 4,1);
            edgeCounter = appendCrossEdges(sb, crossSucList, actName, edgeCounter, 4,2);

            // 结束 mxGraph
            sb.append(MX_ROOT_END).append("</mxGraphModel>");
        } catch (Exception e) {
            log.error("getMxGraph is error", e);
        }
        return sb.toString();
    }

//    private String getMxGraph(String actName, List<String> preList, List<String> sucList , List<String> preHisList, List<String> sucHisList,
//                              List<TaskCrossMainlineBean> crossPreList,List<TaskCrossMainlineBean> crossSucList) {
//        StringBuilder sb = null;
//        try {
//            sb = new StringBuilder();
//            sb.append("<mxGraphModel grid=\"1\" guides=\"1\" tooltips=\"1\" connect=\"1\" fold=\"1\" page=\"1\" pageScale=\"1\">");
//            sb.append("<root>");
//            sb.append("<mxCell id=\"-1\"/>");
//            sb.append("<mxCell id=\"0\" parent=\"-1\"/>");
//            sb.append("<mxCell id=\"-2\" value=\"\" projectName=\"\" flowName=\"\" actDes=\"\"  "
//                    + "parent=\"0\">");
//            sb.append("<mxGeometry width=\"175\" height=\"30\" as=\"geometry\"/>");
//            sb.append("</mxCell>");
//            // 节点本身
//            sb.append("<mxCell id=\"" + actName + "\" value=\"" + actName
//                    + "\" realvalue=\"" + actName + "\" status=\"" + 1
//                    + "\" vertex=\"1\" parent=\"0\">");
//            sb.append("<mxGeometry  width=\"175\" height=\"30\" as=\"geometry\"/>");
//            sb.append("</mxCell>");
//            //前继节点
//            if (preList != null && preList.size() > 0) {
//                for (String pre : preList) {
//                    sb.append("<mxCell id=\"" + pre + "\" value=\"" + pre
//                            + "\" realvalue=\"" + pre + "\" status=\"" + 2 +"\" vertex=\"1\" parent=\"0\">");
//                    sb.append("<mxGeometry  width=\"175\" height=\"30\" as=\"geometry\"/>");
//                    sb.append("</mxCell>");
//                }
//            }
//            //后继节点
//            if (sucList != null && sucList.size() > 0) {
//                for (String suc : sucList) {
//                    sb.append("<mxCell id=\"" + suc + "\" value=\"" + suc
//                            + "\" realvalue=\"" + suc + "\" status=\"" +3
//                            + "\" vertex=\"1\" parent=\"0\">");
//                    sb.append("<mxGeometry  width=\"175\" height=\"30\" as=\"geometry\"/>");
//                    sb.append("</mxCell>");
//                }
//            }
//            //历史的前继节点
//            if (preHisList != null && preHisList.size() > 0) {
//                for (String pre : preHisList) {
//                    if (preList == null || preList.size() == 0 || !preList.contains(pre)) {
//                        sb.append("<mxCell id=\"" + pre + "\" value=\"" + pre
//                                + "\" realvalue=\"" + pre + "\" status=\"" + 2 +"\" vertex=\"1\" parent=\"0\">");
//                        sb.append("<mxGeometry  width=\"175\" height=\"30\" as=\"geometry\"/>");
//                        sb.append("</mxCell>");
//                    }
//                }
//            }
//            //历史的后继节点
//            if (sucHisList != null && sucHisList.size() > 0) {
//                for (String suc : sucHisList) {
//                    if (sucList == null || sucList.size() == 0 || !sucList.contains(suc)) {
//                        sb.append("<mxCell id=\"" + suc + "\" value=\"" + suc
//                                + "\" realvalue=\"" + suc + "\" status=\"" +3
//                                + "\" vertex=\"1\" parent=\"0\">");
//                        sb.append("<mxGeometry  width=\"175\" height=\"30\" as=\"geometry\"/>");
//                        sb.append("</mxCell>");
//                    }
//                }
//            }
//            //跨线的前继节点
//            if (crossPreList != null && crossPreList.size() > 0){
//                for (TaskCrossMainlineBean cross : crossPreList) {
//                    sb.append("<mxCell id=\"" + cross.getDependActName() + "\" value=\"" + cross.getDependActName()
//                            + "\" realvalue=\"" + cross.getDependActName() + "\" status=\"" + 1 +"\" vertex=\"1\" parent=\"0\">");
//                    sb.append("<mxGeometry  width=\"175\" height=\"30\" as=\"geometry\"/>");
//                    sb.append("</mxCell>");
//                }
//            }
//            //跨线的后继节点
//            if (crossSucList != null && crossSucList.size() > 0){
//                for (TaskCrossMainlineBean cross : crossSucList) {
//                    sb.append("<mxCell id=\"" + cross.getActName() + "\" value=\"" + cross.getActName()
//                            + "\" realvalue=\"" + cross.getActName() + "\" status=\"" + 3
//                            + "\" vertex=\"1\" parent=\"0\">");
//                    sb.append("<mxGeometry  width=\"175\" height=\"30\" as=\"geometry\"/>");
//                    sb.append("</mxCell>");
//                }
//            }
//            int i = 0;
//            //边状态 1 正常 2 删除 3 新增
//            if (preList != null && preList.size() > 0) {
//                for (String pre : preList) {
//                    i++;
//                    if (preHisList == null || preHisList.size() == 0 || !preHisList.contains(pre)){
//                        sb.append("<mxCell id=\"E" + i + "\" edge=\"1\" parent=\"0\" source=\""
//                                + pre + "\" target=\"" + actName + "\" status=\"" +3
//                                + "\">");
//                        sb.append("<mxGeometry relative=\"1\" as=\"geometry\">");
//                        sb.append("</mxGeometry>");
//                        sb.append("</mxCell>");
//                    }
//                    if (preHisList != null && preHisList.contains(pre)) {
//                        sb.append("<mxCell id=\"E" + i + "\" edge=\"1\" parent=\"0\" source=\""
//                                + pre + "\" target=\"" + actName + "\" status=\"" +1
//                                + "\">");
//                        sb.append("<mxGeometry relative=\"1\" as=\"geometry\">");
//                        sb.append("</mxGeometry>");
//                        sb.append("</mxCell>");
//                    }
//                }
//                if (preHisList != null && preHisList.size() > 0 ) {
//                    for (String preHis : preHisList) {
//                        if ( !preList.contains(preHis)) {
//                            i++;
//                            sb.append("<mxCell id=\"E" + i + "\" edge=\"1\" parent=\"0\" source=\""
//                                    + preHis + "\" target=\"" + actName + "\" status=\"" +2+ "\">");
//                            sb.append("<mxGeometry relative=\"1\" as=\"geometry\">");
//                            sb.append("</mxGeometry>");
//                            sb.append("</mxCell>");
//                        }
//                    }
//                }
//            }else {
//                // 历史删除的节点
//                if (preHisList != null && preHisList.size() > 0 ) {
//                    for (String pre : preHisList) {
//                        i++;
//                        sb.append("<mxCell id=\"E" + i + "\" edge=\"1\" parent=\"0\" source=\""
//                                + pre + "\" target=\"" + actName + "\" status=\"" +2+ "\">");
//                        sb.append("<mxGeometry relative=\"1\" as=\"geometry\">");
//                        sb.append("</mxGeometry>");
//                        sb.append("</mxCell>");
//                    }
//                }else {
//                    i++;
//                    sb.append("<mxCell id=\"E" + i + "\" edge=\"1\" parent=\"0\" source=\""
//                            + 0 + "\" target=\"" + actName + "\" status=\"" + 1 + "\">");
//                    sb.append("<mxGeometry relative=\"1\" as=\"geometry\">");
//                    sb.append("</mxGeometry>");
//                    sb.append("</mxCell>");
//                }
//
//            }
//
//            if (sucList != null && sucList.size() > 0) {
//                for (String suc : sucList) {
//                    i++;
//                    if (sucHisList == null || sucHisList.size() == 0 || !sucHisList.contains(suc)){
//                        sb.append("<mxCell id=\"E" + i + "\" edge=\"1\" parent=\"0\" source=\""
//                                + actName + "\" target=\"" + suc + "\" status=\"" +3
//                                + "\">");
//                        sb.append("<mxGeometry relative=\"1\" as=\"geometry\">");
//                        sb.append("</mxGeometry>");
//                        sb.append("</mxCell>");
//                    }
//                    if (sucHisList != null && sucHisList.contains(suc)) {
//                        sb.append("<mxCell id=\"E" + i + "\" edge=\"1\" parent=\"0\" source=\""
//                                + actName + "\" target=\"" + suc + "\" status=\"" +1
//                                + "\">");
//                        sb.append("<mxGeometry relative=\"1\" as=\"geometry\">");
//                        sb.append("</mxGeometry>");
//                        sb.append("</mxCell>");
//                    }
//                }
//                //获取删除的历史节点
//                if (sucHisList != null && sucHisList.size() > 0 ) {
//                    for (String sucHis : sucHisList) {
//                        if ( !sucList.contains(sucHis)) {
//                            i++;
//                            sb.append("<mxCell id=\"E" + i + "\" edge=\"1\" parent=\"0\" source=\""
//                                    + actName + "\" target=\"" + sucHis + "\" status=\"" +2+ "\">");
//                            sb.append("<mxGeometry relative=\"1\" as=\"geometry\">");
//                            sb.append("</mxGeometry>");
//                            sb.append("</mxCell>");
//                        }
//                    }
//                }
//
//            }else {
//                if (sucHisList != null && sucHisList.size() > 0) {
//                    for (String suc : sucHisList) {
//                        i++;
//                        sb.append("<mxCell id=\"E" + i + "\" edge=\"1\" parent=\"0\" source=\""
//                                + actName + "\" target=\"" + suc + "\" status=\"" + 2 + "\">");
//                        sb.append("<mxGeometry relative=\"1\" as=\"geometry\">");
//                        sb.append("</mxGeometry>");
//                        sb.append("</mxCell>");
//                    }
//                }else {
//                    i++;
//                    sb.append("<mxCell id=\"E" + i + "\" edge=\"1\" parent=\"0\" source=\""
//                            + actName + "\" target=\"" + 0 + "\" status=\"" + 1 + "\">");
//                    sb.append("<mxGeometry relative=\"1\" as=\"geometry\">");
//                    sb.append("</mxGeometry>");
//                    sb.append("</mxCell>");
//                }
//            }
//            //跨线的前继节点
//            if (crossPreList != null && crossPreList.size() > 0){
//                for (TaskCrossMainlineBean cross : crossPreList) {
//                    i++;
//                    sb.append("<mxCell id=\"E" + i + "\" edge=\"1\" parent=\"0\" source=\""
//                            + cross.getDependActName() + "\" target=\"" + actName + "\" status=\"" +4
//                            + "\">");
//                    sb.append("<mxGeometry relative=\"1\" as=\"geometry\">");
//                    sb.append("</mxGeometry>");
//                    sb.append("</mxCell>");
//                }
//            }
//            //跨线的后继节点
//            if (crossSucList != null && crossSucList.size() > 0){
//                for (TaskCrossMainlineBean cross : crossSucList) {
//                    i++;
//                    sb.append("<mxCell id=\"E" + i + "\" edge=\"1\" parent=\"0\" source=\""
//                            + actName + "\" target=\"" + cross.getActName() + "\" status=\"" +4
//                            + "\">");
//                    sb.append("<mxGeometry relative=\"1\" as=\"geometry\">");
//                    sb.append("</mxGeometry>");
//                    sb.append("</mxCell>");
//                }
//            }
//            sb.append(" </root></mxGraphModel>");
//        } catch (Exception e) {
//            log.error("getMxGraph is error", e);
//        }
//        return sb.toString();
//    }

    public List<String> getPreAndSuccList(List<String> actList, Map<String, String> keyMap) {
        List<String> list = new ArrayList<>();

        for (String actNo : actList) {
            boolean num = isNumeric(actNo);
            if (!num) {
                Collection<String> values = keyMap.values();
                List<String> valueList = new ArrayList<>(values);
                if (valueList.contains(actNo)) {
                    list.add(actNo);
                }else {
                    throw new RuntimeException("依赖作业[" + actNo + "]不存在！");
                }

            } else {
                String actName = keyMap.get(actNo);
                if (StringUtils.isNotBlank(actName)) {
                    list.add(actName);
                }else {
                    throw new RuntimeException("依赖作业[" + actNo + "]不存在！");
                }
            }
        }
        return list;
    }

    public static boolean isNumeric(String str) {
        if (str == null || str.isEmpty()) {
            return false; // 如果字符串为空或 null，返回 false
        }
        return str.chars().allMatch(Character::isDigit); // 使用流操作检查
    }

    public Map getActInfoCompatible(InputStream stream, int version, String userName) {
        Map returnMap = new HashMap();
        String deleteFlaginside = "";
        String messinside = "success";
        Map<String, ActInfoBean> mapList = new HashMap<String, ActInfoBean>();
        Map<String, String> keyMap = new HashMap<String, String>();
        Workbook wb = null;
        Sheet sheet1 = null;
        String shellCrustTemp = "";
        String projectName = "";
        String childName = "";

        Map cellNumMap = new HashMap();// 上传模板中的所有列名Map
        try {
            // Excel2003
            if (version == 2003) {
                wb = new HSSFWorkbook(stream);
            }
            // Excel2007
            else if (version == 2007) {
                wb = new XSSFWorkbook(stream);
            } else {
                messinside = "文件格式错误，请检查！";
                returnMap.put("mess", messinside);
                return returnMap;
            }
            sheet1 = wb.getSheetAt(0);
            if (sheet1 == null) {
                messinside = "数据为空，请检查！";
                returnMap.put("mess", messinside);
                return returnMap;
            }
            int rows = sheet1.getLastRowNum() + 1 - sheet1.getFirstRowNum();
            Row rowName = sheet1.getRow(1);
            if (rowName != null) {
                Cell col = rowName.getCell((short) 0);

                if ("工程名称".equals(getStringCellValue(col))) {
                    Cell col1 = rowName.getCell((short) 1);
                    projectName = getStringCellValue(col1);

                } else {
                    messinside = "请在Excel模板的第二行正确填写工程名称";
                    returnMap.put("mess", messinside);
                    return returnMap;
                }
            } else {
                messinside = "请在Excel模板的第二行正确填写工程名称";
                returnMap.put("mess", messinside);
                return returnMap;
            }
            //判断工程是否是第一次上传 是的话 则返回不做比较

            // 获得脚本外壳
            Row rowTemp = sheet1.getRow(2);
            if (rowTemp != null) {
                Cell colTempCroust = rowTemp.getCell((short) 0);
                if ("脚本外壳".equals(getStringCellValue(colTempCroust))) {
                    Cell colTemp = rowTemp.getCell((short) 1);
                    shellCrustTemp = getStringCellValue(colTemp);
                    returnMap.put("shellCust", shellCrustTemp);
                } else {
                    messinside = "缺少脚本外壳列，请检查！";
                    returnMap.put("mess", messinside);
                    return returnMap;
                }
            }

            // 从第四行开始读取
            for (int i = 3; i < rows; i++) {

                Row row = sheet1.getRow(i);
                ActInfoBean aibtmp = new ActInfoBean();
                if (row != null) {
                    int cols = row.getLastCellNum(); // + 1 -
                    for (int j = 0; j < cols; j++) {
                        Cell col = row.getCell((short) j);
                        // 根据Excel第四行的列名判断模板格式是否正确
                        if (i == 3) {
                            if ((cols != 34 && cols != 40 ) || null == col) {
                                // 西安 xanx.switch 40
                                messinside = "模板列数错误，请检查！";
                                returnMap.put("mess", messinside);
                                return returnMap;
                            }
                            if (col != null) {
                                // 获得模板中所有列名
                                cellNumMap.put(j, getStringCellValue(col));
                            }
                        } else {
                            // 读取Excel文件内容
                            // 根据列号获取当前列的名称
                            String cellNumName = (String) cellNumMap.get(j);
                            //西安导入
                            if (!ArrayUtils.contains(CELL_STR, cellNumName)) {
                                messinside = "模板列名错误，请检查列名---" + cellNumName;
                                returnMap.put("mess", messinside);
                                return returnMap;
                            }


                            // excel内容转换后的字符串
                            String cellValueString = withDateFormatStringCellValue(col);
                            aibtmp.setUserName(userName);
                            // 将模板中数据写入ActInfoBean对象中
                            // "操作步骤序号"
                            if (CELL_STR[0].equals(cellNumName)) {
                                aibtmp.setActNo(cellValueString);
                            }
                            // "所属系统"
                            else if (CELL_STR[1].equals(cellNumName)) {
                                aibtmp.setSystem(cellValueString);
                            }
                            // "子系统名称"
                            else if (CELL_STR[2].equals(cellNumName)) {
                                aibtmp.setChildProjectName(cellValueString);
                            }
                            // "作业名称"
                            else if (CELL_STR[3].equals(cellNumName)) {
                                aibtmp.setActName(cellValueString);
                            }
                            // "作业描述"
                            else if (CELL_STR[4].equals(cellNumName)) {
                                aibtmp.setDescribe(cellValueString);
                            }
                            // "触发作业"
                            else if (CELL_STR[5].equals(cellNumName)) {
                                aibtmp.setAfterActList(changeStringtoList(cellValueString, ","));
                            }
                            // "依赖作业"
                            else if (CELL_STR[6].equals(cellNumName)) {
                                aibtmp.setBeforeActList(changeStringtoList(cellValueString, ","));
                                aibtmp.setJoblist(cellValueString);//备份依赖作业
                            }
                            // "是否删除"
                            else if (CELL_STR[7].equals(cellNumName)) {
                                if (cellValueString.equals("是")) {
                                    aibtmp.setDeleteFlag("2");
                                    deleteFlaginside = "2";
                                } else if (cellValueString.equals("否")) {
                                    aibtmp.setDeleteFlag("1");
                                } else {
                                    aibtmp.setDeleteFlag(cellValueString);
                                }
                            }
                            // "OK文件绝对路径"
                            else if (CELL_STR[8].equals(cellNumName)) {
                                aibtmp.setOKFileABPath(cellValueString);
                            }
                            // "OK文件检测周期"
                            else if (CELL_STR[9].equals(cellNumName)) {
                                aibtmp.setOKFileFindWeek(cellValueString);
                            }
                            // "脚本名称"
                            else if (CELL_STR[10].equals(cellNumName)) {
                                aibtmp.setShellABPath(cellValueString);
                            }
                            // "输出参数"
                            else if (CELL_STR[11].equals(cellNumName)) {
                                aibtmp.setOutputPamaeter(cellValueString);
                            }
                            // "业务异常自动重试"
                            else if (CELL_STR[12].equals(cellNumName)) {
                                if (cellValueString.equals("是")) {
                                    aibtmp.setRedo("1");
                                } else if (cellValueString.equals("否")) {
                                    aibtmp.setRedo("0");
                                } else {
                                    aibtmp.setRedo(cellValueString);
                                }
                            }
                            // "Agent资源组"
                            else if (CELL_STR[13].equals(cellNumName)) {
                                aibtmp.setAgentGropName(cellValueString);
                            }
                            // "Agent文件检测资源组"
                            else if (CELL_STR[14].equals(cellNumName)) {
                                aibtmp.setCheckAgentGropName(cellValueString);
                            }
                            // "优先级"
                            else if (CELL_STR[15].equals(cellNumName)) {
                                aibtmp.setPriority(cellValueString);
                            }
                            // "权重"
                            else if (CELL_STR[16].equals(cellNumName)) {
                                aibtmp.setWeights(cellValueString);
                            }
                            // "APT组名称"
                            else if (CELL_STR[17].equals(cellNumName)) {
                                aibtmp.setAptGroupName(cellValueString);
                            }
                            // "APT文件名称"
                            else if (CELL_STR[18].equals(cellNumName)) {
                                aibtmp.setAptFileName(cellValueString);
                            }
                            // "ISDB2"
                            else if (CELL_STR[19].equals(cellNumName)) {
                                aibtmp.setIsDB2(cellValueString);
                            }
                            // "DB2IP"
                            else if (CELL_STR[20].equals(cellNumName)) {
                                aibtmp.setDb2IP(cellValueString);
                            }
                            // "主线名称"
                            else if (CELL_STR[21].equals(cellNumName)) {
                                aibtmp.setMainline(cellValueString);
                            }
                            // "头尾标记"
                            else if (CELL_STR[22].equals(cellNumName)) {
                                aibtmp.setSEFlag(cellValueString);
                            }
                            // "是否禁用"
                            else if (CELL_STR[23].equals(cellNumName)) {
                                if (cellValueString.equals("是")) {
                                    aibtmp.setDisableFlag("1");
                                } else if (cellValueString.equals("否")) {
                                    aibtmp.setDisableFlag("0");
                                } else {
                                    aibtmp.setDisableFlag("0");
                                }
                            }
                            // agentIp 端口
                            else if (CELL_STR[24].equals(cellNumName)) {
                                aibtmp.setAgentInfo(cellValueString);
                            }
                            // "延迟运行"
                            else if (CELL_STR[25].equals(cellNumName)) {
                                aibtmp.setDelayTime(cellValueString.trim());
                            }
                            // "执行条件"
                            else if (CELL_STR[26].equals(cellNumName)) {
                                aibtmp.setBranchCondition(cellValueString.trim());
                            }
                            // "重试次数"
                            else if (CELL_STR[27].equals(cellNumName)) {
                                aibtmp.setReTryCount(cellValueString.trim());
                            }
                            // "日历名称"
                            else if (CELL_STR[28].equals(cellNumName)) {
                                aibtmp.setCalendName(cellValueString.trim());
                            }
                            // "重试间隔"
                            else if (CELL_STR[29].equals(cellNumName)) {
                                aibtmp.setReTryTime(cellValueString.trim());
                            }
                            // "重试结束时间"
                            else if (CELL_STR[30].equals(cellNumName)) {
                                aibtmp.setReTryEndTime(cellValueString.trim());
                            }
                            //"业务异常自动忽略"
                            else if (CELL_STR[31].equals(cellNumName)) {
                                if (cellValueString.equals("是")) {
                                    aibtmp.setSkip("1");
                                } else if (cellValueString.equals("否")) {
                                    aibtmp.setSkip("0");
                                } else {
                                    aibtmp.setSkip(cellValueString);
                                }
                            }
                            //"延时报警"
                            else if (CELL_STR[32].equals(cellNumName)) {
                                aibtmp.setDelayWarnning(cellValueString.trim());
                            }


                            //西安导入
                            if (CELL_STR[34].equals(cellNumName)) {
                                aibtmp.setDays(cellValueString.trim());
                            } else if (CELL_STR[35].equals(cellNumName)) {
                                aibtmp.setLogic(cellValueString.trim());
                            } else if (CELL_STR[36].equals(cellNumName)) {
                                aibtmp.setWdays(cellValueString.trim());
                            } else if (CELL_STR[37].equals(cellNumName)) {
                                aibtmp.setMonth(cellValueString.trim());
                            }else if (CELL_STR[38].equals(cellNumName)) {
                                aibtmp.setChangePerson(cellValueString.trim());
                            }else if (CELL_STR[39].equals(cellNumName)) {
                                aibtmp.setJobType(cellValueString.trim());
                            } else if (CELL_STR[40].equals(cellNumName)) {
                                //执行用户
                                aibtmp.setPerformUser(cellValueString.trim());
                            }

                        }

                    }
                    // 校验模板列名是否正确
                    if (i == 3) {
                        if (Environment.getInstance().getXanxSwitch()) {
                            //西安导入
                            for (int ii = 0; ii < CELL_STR.length; ii++) {
                                // 判断在模板中解析的列名是否为规范的列名
                                if (!cellNumMap.values().contains(CELL_STR[ii])) {
                                    messinside = "模板列名错误，请检查列名---" + CELL_STR[ii];
                                    returnMap.put("mess", messinside);
                                    return returnMap;
                                }
                            }

                        }
                    } else {
                        if (null != projectName || !"".equals(projectName)) {
                            aibtmp.setProjectName(projectName);
                        }
                        if (!"".equals(aibtmp.getActNo())) {
                            ActInfoBean rep = mapList.get(aibtmp.getActNo());
                            if (rep != null) {
                                messinside = "操作序号重复---" + aibtmp.getActNo();
                            }
                        }

                        if (!"".equals(aibtmp.getAgentIp()) && null != aibtmp.getAgentIp()
                                && !"null".equals(aibtmp.getAgentIp())) {
                            aibtmp.setAgentGroup(true);
                        }

                        if (!"".equals(aibtmp.getActNo()) || !"".equals(aibtmp.getChildProjectName())
                                || !"".equals(aibtmp.getActName()) || !"".equals(aibtmp.getDescribe())
                                || (null == aibtmp.getBeforeActList() || !aibtmp.getBeforeActList().isEmpty())
                                || (null == aibtmp.getAfterActList() || !aibtmp.getAfterActList().isEmpty())
                                || !"".equals(aibtmp.getOKFileABPath()) || !"".equals(aibtmp.getOKFileFindWeek())
                                || !"".equals(aibtmp.getShellABPath()) || !"".equals(aibtmp.getOutputPamaeter())
                                || !"".equals(aibtmp.getAgentGropName()) || !"".equals(aibtmp.getMainline())
                                || !"".equals(aibtmp.getSEFlag()) || !"".equals(aibtmp.getAgentIp())) {


                            // "优先级"
                            String priorityValue = aibtmp.getPriority();
                            aibtmp.setPriority(priorityValue);

                            // "权重"
                            String weightsValue = aibtmp.getWeights();
                            aibtmp.setWeights(weightsValue);
                            // 脚本外壳
                            aibtmp.setShellCrust(shellCrustTemp);

                            if (this.checkEmptyObject(aibtmp)) {
                                mapList.put(aibtmp.getActNo(), aibtmp);
                                keyMap.put(aibtmp.getActNo(),aibtmp.getActName());
                                if (StringUtils.isNotBlank(childName) && !childName.equals(aibtmp.getChildProjectName())) {
                                    messinside = "子系统名称重复---" + childName;
                                    returnMap.put("mess", messinside);
                                    return returnMap;
                                } else {
                                    childName = aibtmp.getChildProjectName();
                                }
                            }

                        }
                    }

                } else if (i == 3) {
                    messinside = DESEXCELCHECKISOK;
                    returnMap.put("mess", messinside);
                    return returnMap;
                }
            }

        } catch (Exception ex) {
            log.error("getActInfoCompatible is error:", ex);
            messinside = "系统异常，请联系管理员！";
            returnMap.put("mess", messinside);
            return returnMap;
        } finally {
            if (null != stream) {
                try {
                    stream.close();
                } catch (Exception e) {
                    stream = null;
                }
            }
        }
        returnMap.put("deleteFlag", deleteFlaginside);
        returnMap.put("mess", messinside);
        returnMap.put("mapList", mapList);
        returnMap.put("keyMap", keyMap);
        returnMap.put("projectName", projectName);
        returnMap.put("childName", childName);
        returnMap.put("mess", messinside);
        return returnMap;
    }

    /**
     *
     * @Title: getStringCellValue
     * @Description: (获取单元格数据内容为字符串类型的数据)
     * @param cell Excel单元格
     * @return 单元格数据内容
     * @return String 返回类型
     * @throws @变更记录 2015-8-4 yunpeng_zhang
     */
    private String getStringCellValue(Cell cell) {
        String strCell = "";
        if (cell != null) {
            switch (cell.getCellType()) {
                case STRING:
                    strCell = cell.getStringCellValue();
                    break;
                case NUMERIC:
                    java.text.DecimalFormat formatter = new java.text.DecimalFormat("########");
                    strCell = formatter.format(cell.getNumericCellValue());
                    break;
                case BOOLEAN:
                    strCell = String.valueOf(cell.getBooleanCellValue());
                    break;
                case BLANK:
                    strCell = "";
                    break;
                default:
                    strCell = "";
                    break;
            }
        }
        if (strCell.equals("") || cell == null) {
            return "";
        }
        return strCell.trim();
    }

    private String withDateFormatStringCellValue(Cell cell) {
        String strCell = "";
        if (cell != null) {
            switch (cell.getCellType()) {
                case STRING:
                    strCell = cell.getStringCellValue();
                    break;
                case NUMERIC:
                    if (HSSFDateUtil.isCellDateFormatted(cell)) {
                        SimpleDateFormat formatter = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy", Locale.US);
                        SimpleDateFormat formatter1 = new SimpleDateFormat("HH:mm");
                        Date parseDate = null;
                        String dateCellValue = cell.getDateCellValue() + "";
                        try {
                            parseDate = formatter.parse(dateCellValue);
                        } catch (ParseException e) {
                            e.printStackTrace();
                        }
                        strCell = formatter1.format(parseDate);
                    } else {
                        java.text.DecimalFormat formatter = new java.text.DecimalFormat("########");
                        strCell = formatter.format(cell.getNumericCellValue());
                    }
                    break;
                case BOOLEAN:
                    strCell = String.valueOf(cell.getBooleanCellValue());
                    break;
                case BLANK:
                    strCell = "";
                    break;
                default:
                    strCell = "";
                    break;
            }
        }
        if (strCell.equals("") || cell == null) {
            return "";
        }
        return strCell.trim();
    }
    /**
     * 将以指定分隔符切割的字符串转换为无重复值的List
     *
     * @param str
     * @param delimiter 分隔符
     * @return
     */
    private static List<String> changeStringtoList (String str, String delimiter )
    {
        List<String> resaultList = new ArrayList<String>();
        if (str != null && (!str.equals("")))
        {
            String[] strlist = str.split(delimiter);
            HashSet<String> stringset = new HashSet<String>();
            for (int i = 0; i < strlist.length; i++)
            {
                stringset.add(strlist[i]);
            }
            Iterator<String> in = stringset.iterator();
            while (in.hasNext())
            {
                resaultList.add(in.next());
            }
        }
        return resaultList;
    }

    /**
     * 判断对象是否为空
     *
     * @param aibtmp
     * @return
     */
    private boolean checkEmptyObject ( ActInfoBean aibtmp )
    {

        boolean returnValue = false;
        if (aibtmp == null)
        {
            return returnValue;
        }
        // 操作步骤号
        if (null != aibtmp.getActNo() && !"".equals(aibtmp.getActNo()))
        {
            returnValue = true;
        }
        // "所属系统"
        if (null != aibtmp.getSystem() && !"".equals(aibtmp.getSystem()))
        {
            returnValue = true;
        }
        // "子系统名称"

        if (null != aibtmp.getChildProjectName() && !"".equals(aibtmp.getChildProjectName()))
        {
            returnValue = true;
        }

        // "作业名称"

        if (null != aibtmp.getActName() && !"".equals(aibtmp.getActName()))
        {
            returnValue = true;
        }

        // "作业描述"

        if (null != aibtmp.getDescribe() && !"".equals(aibtmp.getDescribe()))
        {
            returnValue = true;
        }
        // "触发作业"

        if (null != aibtmp.getAfterActList())
        {
            returnValue = true;
        }
        // "依赖作业"

        if (null != aibtmp.getBeforeActList())
        {
            returnValue = true;
        }
        // "是否删除"

        if (null != aibtmp.getDeleteFlag() && !"".equals(aibtmp.getDeleteFlag()))
        {
            returnValue = true;
        }
        // "OK文件绝对路径"

        if (null != aibtmp.getOKFileABPath() && !"".equals(aibtmp.getOKFileABPath()))
        {
            returnValue = true;
        }
        // "OK文件检测周期"

        if (null != aibtmp.getOKFileFindWeek() && !"".equals(aibtmp.getOKFileFindWeek()))
        {
            returnValue = true;
        }
        // "脚本名称"

        if (null != aibtmp.getShellABPath() && !"".equals(aibtmp.getShellABPath()))
        {
            returnValue = true;
        }
        // "输出参数"

        if (null != aibtmp.getOutputPamaeter() && !"".equals(aibtmp.getOutputPamaeter()))
        {
            returnValue = true;
        }
        // "业务异常自动重试"

        if (null != aibtmp.getRedo() && !"".equals(aibtmp.getRedo()))
        {
            returnValue = true;
        }
        // "Agent资源组"

        if (null != aibtmp.getAgentGropName() && !"".equals(aibtmp.getAgentGropName()))
        {
            returnValue = true;
        }
        // "Agent文件检测资源组"

        if (null != aibtmp.getCheckAgentGropName() && !"".equals(aibtmp.getCheckAgentGropName()))
        {
            returnValue = true;
        }
        // "APT组名称"

        if (null != aibtmp.getAptGroupName() && !"".equals(aibtmp.getAptGroupName()))
        {
            returnValue = true;
        }
        // "APT文件名称"

        if (null != aibtmp.getAptFileName() && !"".equals(aibtmp.getAptFileName()))
        {
            returnValue = true;
        }
        // "ISDB2"

        if (null != aibtmp.getIsDB2() && !"".equals(aibtmp.getIsDB2()))
        {
            returnValue = true;
        }
        // "DB2IP"

        if (null != aibtmp.getDb2IP() && !"".equals(aibtmp.getDb2IP()))
        {
            returnValue = true;
        }
        // "主线名称"

        if (null != aibtmp.getMainline() && !"".equals(aibtmp.getMainline()))
        {
            returnValue = true;
        }
        // "头尾标记"

        if (null != aibtmp.getSEFlag() && !"".equals(aibtmp.getSEFlag()))
        {
            returnValue = true;
        }
        // "是否禁用"
        if (null != aibtmp.getDisableFlag() && !"".equals(aibtmp.getDisableFlag()))
        {
            returnValue = true;
        }

        return returnValue;
    }


    public Map<String, Object> showWorkPreviewDetail(String iid,String projectName,String version) {
        Map<String, Object> returnMap = new HashMap<String, Object>();
        List<WorkPreviewBean> actList = new ArrayList<WorkPreviewBean>();
        try {
            actList = ChangeWorkPreviewManager.getInstance().getWorkPreviewList(iid, projectName, version);
        } catch (RepositoryException e) {
            log.error("showWorkPreviewDetail is error:", e);
            returnMap.put("success", false);
            returnMap.put("message", "系统异常，请联系管理员！");
            return returnMap;
        }
        returnMap.put("success", true);
        returnMap.put("message", "success");
        returnMap.put("dataList", actList);
        return returnMap;
    }

    /**
     * 生成判断作业活动状态 方法 1. 原有作业 2. 新增作业 3. 删除作业
     */
    public Map<String, Integer> generateWorkPreviewStatus(Map<String, ActRelationInfo> mapList, Map<String, ActRelationInfo> historyActMap) {
        Map<String, Integer> resultMap = new HashMap<>();
        List<ActRelationInfo> nowList = mapList.values().stream().collect(Collectors.toList());
        List<ActRelationInfo> historyList = historyActMap.values().stream().collect(Collectors.toList());
        // 获取作业名集合
        Set<String> nowActNameSet = nowList.stream().map(ActRelationInfo::getActName).collect(Collectors.toSet());
        Set<String> historyActNameSet = historyList.stream().map(ActRelationInfo::getActName).collect(Collectors.toSet());
        // 1. 全部作业
        Set<String> allActNameSet = new HashSet<>(nowActNameSet);
        allActNameSet.addAll(historyActNameSet);

        for (String actName : allActNameSet) {
            boolean inNowSet = nowActNameSet.contains(actName);
            boolean inHistorySet = historyActNameSet.contains(actName);

            if (inNowSet && inHistorySet) {
                resultMap.put(actName, 1);
            } else if (inNowSet) {
                resultMap.put(actName, 2);
            } else {
                resultMap.put(actName, 3);
            }
        }
        return resultMap;
    }

    /**
     * 增量生成判断作业活动状态 方法 1. 原有作业 2. 新增作业 3. 删除作业
     */
    public void generateUpdateWorkPreviewStatus(Map<String, ActRelationInfo> mapList, Map<String, ActRelationInfo> historyActMap,Map<String, List<String>>  preMap, Map<String, List<String>> sucMap,Map<String, Integer> resultMap,List<String> deleteAct) {
        List<String> deleteList = new ArrayList<>();
        List<ActRelationInfo> nowList = mapList.values().stream().collect(Collectors.toList());
        List<ActRelationInfo> historyList = historyActMap.values().stream().collect(Collectors.toList());
        // 获取作业名集合
        Set<String> nowActNameSet = nowList.stream().map(ActRelationInfo::getActName).collect(Collectors.toSet());
        Set<String> historyActNameSet = historyList.stream().map(ActRelationInfo::getActName).collect(Collectors.toSet());
        // 1. 全部作业
        Set<String> allActNameSet = new HashSet<>(nowActNameSet);
        allActNameSet.addAll(historyActNameSet);

        for (ActRelationInfo actInfo : nowList) {
            ActRelationInfo historyActInfo = historyActMap.get(actInfo.getActName());
            if (historyActInfo != null && "2".equals(actInfo.getFlag())) {
                //获取前继作业
                //获取前继作业
                List<String> preList = preMap.get(actInfo.getActName());
                String pre = "";
                String suc = "";
                if (preList != null && preList.size() > 0) {
                    pre = String.join(",", preList);
                }
                //获取后继作业
                List<String> sucList = sucMap.get(actInfo.getActName());
                if (sucList != null && sucList.size() > 0) {
                    suc = String.join(",", sucList);
                }
                if (StringUtils.isBlank(pre) && StringUtils.isBlank(suc)) {
                    deleteList.add(actInfo.getActName());
                }
            }
            if (historyActInfo == null && "2".equals(actInfo.getFlag())){
                //删除的作业
                List<String> succActList = actInfo.getSuccActList();
                List<String> preActList = actInfo.getPreActList();
                if (CollectionUtils.isEmpty(succActList) && CollectionUtils.isEmpty(preActList)) {
                    deleteAct.add(actInfo.getActName());
                }
            }
        }

        for (String actName : allActNameSet) {
            boolean inNowSet = nowActNameSet.contains(actName);
            boolean inHistorySet = historyActNameSet.contains(actName);
            if (deleteList.contains(actName)) {
                resultMap.put(actName, 3);
                continue;
            }
            if (inNowSet && !inHistorySet){
                resultMap.put(actName, 2);
            }else {
                resultMap.put(actName, 1);
            }
        }
    }

    public Map<String, Object> rollbackTaskVersion(HttpServletRequest request, String iid, String projectName, String version) {
        Map<String, Object> returnMap = new HashMap<String, Object>();
        String filePath = "";
        String message = "";
        try {
            // 获取当前用户
            ClientSession session = ClusterManager.getInstance().getSession(
                    SessionData.getSessionData(request).getSessionId());
            String username = session.getUserLoginName();
            UserInfo user = ClientSessionHelper.getUser(SessionData.getSessionData(request).getSessionId());
            // 回滚任务版本
            List<ActRelationInfo> actRelationInfos = ChangeWorkPreviewManager.getInstance().rollbackTaskVersion(iid);
            String fileName = projectName + "_" + version + "_"  + iid + "_"+ System.currentTimeMillis() + ".xls";
            String tempDir = Environment.getInstance().getIEAIHome() + File.separator + "rollback";
            File dir = new File(tempDir);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            // 生成唯一的文件名，避免文件覆盖
            filePath = tempDir + File.separator + fileName;
            // 在固定目录下生成文件
            // 生成excel文件
            File file = TaskDownloadService.getInstance().doRollBackExportXLS(actRelationInfos, projectName, returnMap, filePath);
            if (file == null) {
                returnMap.put("success", false);
                returnMap.put("message", "生成历史版本Excel失败！");
                return returnMap;
            }
            // 版本回退 Excel上传
            message = DbUtilUpLoadExcel.getInstance().taskUpload(dir, null, fileName, user, username, "false", null, Long.parseLong(iid));
            if (message.contains("成功")) {
                returnMap.put("message", "版本回退成功！");
                returnMap.put("success", true);
            }else {
                returnMap.put("message", "版本回退失败！"+ message);
                returnMap.put("success", false);
            }

        } catch (Exception e) {
            log.error("rollbackTaskVersion is error:", e);
            returnMap.put("success", false);
            returnMap.put("message", "系统异常，请联系管理员！");
        }
        return returnMap;
    }


    public Map<String, Object> getLatestVersion(String projectName) {
        return ChangeWorkPreviewManager.getInstance().getLatestVersion(projectName);
    }
}
