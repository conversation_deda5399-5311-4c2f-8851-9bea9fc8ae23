package com.ideal.controller.domain;

import com.ideal.common.utils.SessionData;
import com.ideal.ieai.commons.Constants;
import com.ideal.service.domain.DomainManagementService;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 *
 * @ClassName:  DomainManagementController
 * @Description: 域管理控制器
 * @author: system
 * @date:   2024年12月19日
 *
 * @Copyright: 2024-2027 www.idealinfo.com Inc. All rights reserved.
 *
 */
@Controller
public class DomainManagementController
{
    private Logger log = Logger.getLogger(DomainManagementController.class);
    
    @RequestMapping("initDomainManagementPage.do")
    public String initDomainManagementPage ( )
    {
        return "domain/DomainManagement";
    }
    
    @RequestMapping("getDomainManagementList.do")
    @ResponseBody
    public Map getDomainManagementList ( Integer start, Integer limit, String iname, String idesc, String queryString )
    {
        DomainManagementService service = new DomainManagementService();
        return service.getDomainManagementList( start, limit, iname, idesc, Constants.IEAI_IEAI_BASIC);
    }
    
    @RequestMapping("saveDomainManagement.do")
    @ResponseBody
    public Map saveDomainManagement (HttpServletRequest request ,String jsonData )
    {
        DomainManagementService service = new DomainManagementService();
        String userName = SessionData.getSessionData(request).getUserName();
        long userId = Long.parseLong(SessionData.getSessionData(request).getUserInnerCode());
        Map map = new HashMap();
        try
        {
            map = service.saveDomainManagement(jsonData, userName, userId);
        } catch (Exception e)
        {
            log.error("保存失败",e);
            map.put("success", false);
            map.put("message", "保存失败");
        }
        return map;
    }
    
    @RequestMapping("deleteDomainManagement.do")
    @ResponseBody
    public Map deleteDomainManagement ( String iids )
    {
        DomainManagementService service = new DomainManagementService();
        Map map = new HashMap();
        try
        {
            map = service.deleteDomainManagement(iids);
        } catch (Exception e)
        {
            log.error("删除失败",e);
            map.put("success", false);
            map.put("message", "删除失败");
        }
        return map;
    }
    
    // ==================== 服务器绑定相关接口 ====================
    
    @RequestMapping("getDomainServerList.do")
    @ResponseBody
    public Map getDomainServerList(Integer start, Integer limit, String serverName, Long domainId) {
        DomainManagementService service = new DomainManagementService();
        return service.getDomainServerList(start, limit, serverName, domainId, Constants.IEAI_IEAI_BASIC);
    }
    
    @RequestMapping("getAvailableServerList.do")
    @ResponseBody
    public Map getAvailableServerList(Integer start, Integer limit, String serverName, Long domainId) {
        DomainManagementService service = new DomainManagementService();
        return service.getAvailableServerList(start, limit, serverName, domainId, Constants.IEAI_IEAI_BASIC);
    }
    
    @RequestMapping("bindDomainServer.do")
    @ResponseBody
    public Map bindDomainServer(String jsonData, Long domainId) {
        DomainManagementService service = new DomainManagementService();
        Map map = new HashMap();
        try {
            map = service.bindDomainServer(jsonData, domainId);
        } catch (Exception e) {
            log.error("服务器绑定失败", e);
            map.put("success", false);
            map.put("message", "服务器绑定失败");
        }
        return map;
    }
    
    @RequestMapping("unbindDomainServer.do")
    @ResponseBody
    public Map unbindDomainServer(String deleteIds) {
        DomainManagementService service = new DomainManagementService();
        Map map = new HashMap();
        try {
            map = service.unbindDomainServer(deleteIds);
        } catch (Exception e) {
            log.error("服务器解绑失败", e);
            map.put("success", false);
            map.put("message", "服务器解绑失败");
        }
        return map;
    }
    
    // ==================== 业务系统绑定相关接口 ====================
    
    @RequestMapping("getDomainBusinessList.do")
    @ResponseBody
    public Map getDomainBusinessList(Integer start, Integer limit, String businessName, Long domainId) {
        DomainManagementService service = new DomainManagementService();
        return service.getDomainBusinessList(start, limit, businessName, domainId, Constants.IEAI_IEAI_BASIC);
    }
    
    @RequestMapping("getAvailableBusinessList.do")
    @ResponseBody
    public Map getAvailableBusinessList(Integer start, Integer limit, String businessName, Long domainId) {
        DomainManagementService service = new DomainManagementService();
        return service.getAvailableBusinessList(start, limit, businessName, domainId, Constants.IEAI_IEAI_BASIC);
    }
    
    @RequestMapping("bindDomainBusiness.do")
    @ResponseBody
    public Map bindDomainBusiness(String jsonData, Long domainId) {
        DomainManagementService service = new DomainManagementService();
        Map map = new HashMap();
        try {
            map = service.bindDomainBusiness(jsonData, domainId);
        } catch (Exception e) {
            log.error("业务系统绑定失败", e);
            map.put("success", false);
            map.put("message", "业务系统绑定失败");
        }
        return map;
    }
    
    @RequestMapping("unbindDomainBusiness.do")
    @ResponseBody
    public Map unbindDomainBusiness(String deleteIds) {
        DomainManagementService service = new DomainManagementService();
        Map map = new HashMap();
        try {
            map = service.unbindDomainBusiness(deleteIds);
        } catch (Exception e) {
            log.error("业务系统解绑失败", e);
            map.put("success", false);
            map.put("message", "业务系统解绑失败");
        }
        return map;
    }
}
