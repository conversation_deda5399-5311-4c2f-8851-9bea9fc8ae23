package com.ideal.controller.jobscheduling.projects;

import com.ideal.common.utils.SessionData;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.service.jobscheduling.projects.ProjectsService;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 
 * @ClassName:  ProjectsController   
 * @Description:工程查询controller   
 * @author: lichao_liu 
 * @date:   2017年7月25日 下午4:58:35   
 *     
 * @Copyright: 2017-2027 www.idealinfo.com Inc. All rights reserved. 
 *
 */
@Controller
public class ProjectsController
{
    
    private static final String METHODSTR = "method :"; 
    private static final Logger  log       = Logger.getLogger(ProjectsController.class);
    /**
     * 
     * @Title: initProjects   
     * @Description: 列表页初始化
     * @param: @return      
     * @return: String      
     * @throws   
     * @author: lichao_liu 
     * @date:   2017年7月25日 下午4:59:17
     */
    @RequestMapping("initProjects.do")
    public String initProjects ()
    {
        return "jobScheduling/projects/projects";
    }

    /**
     * 
     * @Title: initProjectDetail   
     * @Description: 工程详细信息初始化
     * @param: @param request
     * @param: @return      
     * @return: String      
     * @throws   
     * @author: Administrator 
     * @date:   2017年7月25日 下午5:00:01
     */
    @RequestMapping("initProjectDetail.do")
    public String initProjectDetail ( HttpServletRequest request )
    {
        return "jobScheduling/projects/projectDetailInfo";
    }

    /**
     * 
     * @Title: listProjects   
     * @Description: 获取列表页数据  
     * @param: @param request
     * @param: @param page
     * @param: @param limit
     * @param: @param proName
     * @param: @return
     * @param: @throws RepositoryException      
     * @return: Map      
     * @throws   
     * @author: Administrator 
     * @date:   2017年7月25日 下午5:00:25
     */
    @RequestMapping("listProjects.do")
    @ResponseBody
    public Map listProjects ( HttpServletRequest request, String page, String limit, String proName )
            throws RepositoryException
    {

        return ProjectsService.getInstance().listProjects(SessionData.getSessionData(request).getUserInnerCode(), page,
            limit, proName);

    }

    /**
     * 
     * @Title: getProjectDetail   
     * @Description: 获取工程详细信息
     * @param: @param request
     * @param: @param IID
     * @param: @return      
     * @return: Object      
     * @throws   
     * @author: Administrator 
     * @date:   2017年7月25日 下午5:00:35
     */
    @RequestMapping("getProjectDetail.do")
    @ResponseBody
    public Object getProjectDetail ( HttpServletRequest request, long iid )
    {
        try
        {
            return ProjectsService.getInstance().getProjectDetail(iid);

        } catch (Exception e)
        {
            log.error(METHODSTR+Thread.currentThread().getStackTrace()[1].getMethodName(),e);
        }
        return null;

    }

    /**
     * 
     * @Title: getProjectHistory   
     * @Description: 获取工程历史版本 
     * @param: @param request
     * @param: @param IID
     * @param: @return      
     * @return: Object      
     * @throws   
     * @author: Administrator 
     * @date:   2017年7月25日 下午5:00:54
     */
    @RequestMapping("getProjectHistory.do")
    @ResponseBody
    public Object getProjectHistory ( HttpServletRequest request, long iid , String isExcel, String proName,  String prjId1)
    {
        try
        {
            if(prjId1!=null){
                if(!"".equals(prjId1)){
                    iid= Long.parseLong(prjId1);
                }
            }
            return ProjectsService.getInstance().getProjectHistory(iid, Integer.parseInt(isExcel), proName);

        } catch (Exception e)
        {
            log.error(METHODSTR+Thread.currentThread().getStackTrace()[1].getMethodName(),e);
        }
        return null;

    }

}
