package com.ideal.controller.ProjectDrivingMode;

import com.ideal.common.utils.SessionData;
import com.ideal.controller.platform.user.PageSplitBean;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.server.jobscheduling.repository.projectdrivingmode.ProjectDrivingModeBean;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.service.ProjectDrivingModeService.ProjectDrivingModeService;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 
 * @ClassName:  ProjectDrivingModeController   
 * @Description:工程并发模式及分支判断模式处理Controller   
 * @author: licheng_zhao 
 * @date:   2019年1月6日 上午10:54:20   
 *     
 * @Copyright: 2019-2027 www.idealinfo.com Inc. All rights reserved. 
 *
 */
@Controller
@RequestMapping("projectDrivingMode")
public class ProjectDrivingModeController
{
    private static final Logger       _log    = Logger.getLogger(ProjectDrivingModeController.class);

    private ProjectDrivingModeService service = ProjectDrivingModeService.getInstance();

    /**
     * 
     * @Title: page   
     * @Description: 加载工程并发模式及分支判断模式页面   
     * @return      
     * @author: licheng_zhao 
     * @date:   2019年1月6日 上午10:55:35
     */
    @RequestMapping("page.do")
    public String page() {
        return "projectDrivingMode/projectDrivingModeConfig";
    }
    
    /**
     * 
     * @Title: getSystemNameList   
     * @Description: 获取加载工程并发模式及分支判断模式所属系统名称   
     * @param request
     * @return      
     * @author: zlc52 
     * @date:   2019年1月6日 上午10:56:31
     */
    @RequestMapping("getSystemNameList.do")
    @ResponseBody
    public Object getSystemNameList ( HttpServletRequest request ) 
    {
        JSONObject result = new JSONObject();
        JSONArray jsonArr = new JSONArray();
        List<ProjectDrivingModeBean> list=service.getSystemNameList();
        jsonArr.addAll(list);
        result.put("dataList", jsonArr);
        return result;
    }
    
    /**
     * 
     * @Title: getProjectNameList   
     * @Description: 获取工程并发模式及分支判断模式 工程名称  
     * @param request
     * @return      
     * @author: licheng_zhao 
     * @date:   2019年1月6日 上午10:58:23
     */
    @RequestMapping("getDrivingProjectNameList.do")
    @ResponseBody
    public Object getProjectNameList ( HttpServletRequest request ) 
    {
        JSONObject result = new JSONObject();
        JSONArray jsonArr = new JSONArray();
        String sysName = request.getParameter("sysName");
        List<ProjectDrivingModeBean> list = service.getProjectNameList(sysName);
        jsonArr.addAll(list);
        result.put("dataList", jsonArr);
        return result;
    }
    
    /**
     * 
     * @Title: getFlowNameList   
     * @Description: 获取工程并发模式及分支判断模式工作流名称   
     * @param request
     * @return      
     * @author: licheng_zhao 
     * @date:   2019年1月6日 上午10:58:45
     */
    @RequestMapping("getFlowNameList.do")
    @ResponseBody
    public Object getFlowNameList ( HttpServletRequest request ) 
    {
        JSONObject result = new JSONObject();
        JSONArray jsonArr = new JSONArray();
        String projectName=request.getParameter("projectName");
        List<ProjectDrivingModeBean> list=service.getFlowName(projectName,Constants.IEAI_IEAI);
        jsonArr.addAll(list);
        result.put("dataList", jsonArr);
        return result;
    }
    
    /**
     * 
     * @Title: getDrivingModeList   
     * @Description: 获取工程并发模式及分支判断模式列表   
     * @param request
     * @param system
     * @param projectName
     * @param flowName
     * @return      
     * @author: licheng_zhao 
     * @date:   2019年1月6日 上午10:59:09
     */
    @RequestMapping("getDrivingModeList.do")
    @ResponseBody
    public Object getDrivingModeList ( HttpServletRequest request, ProjectDrivingModeBean bean )
    {
        Object resp = null;
        try
        {
            PageSplitBean beans = new PageSplitBean(request, "limit", "start");
            resp = service.getList(bean, beans.getStartRow(), beans.getEndRow());
        } catch (RepositoryException e)
        {
            _log.info("getDrivingModeList is get error:" + e);
        }
        return resp;
    }
    
    /**
     * 
     * @Title: batchUpdate   
     * @Description: 修改工程并发模式及分支判断模式  
     * @param request
     * @param projectList
     * @param projectName
     * @param flowName
     * @param BatchType
     * @return      
     * @author: licheng_zhao 
     * @date:   2019年1月6日 上午10:59:33
     */
    @RequestMapping("batchOdsUpdate.do")
    @ResponseBody
    public Map batchUpdate ( HttpServletRequest request,String projectList,String projectName,String flowName,String BatchType )
    {
        Map map = new HashMap();
        SessionData _sessData = SessionData.getSessionData(request);
        boolean result = service.batchUpdateProjectModel(projectList, projectName,flowName,BatchType);
        if (result)
        {
            map.put("success", true);
            map.put("message", "批量保存成功");
        } else
        {
            map.put("success", false);
            map.put("message", "批量保存失败");
        }
        return map;
    }
    

}
