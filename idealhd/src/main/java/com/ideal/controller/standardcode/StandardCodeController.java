package com.ideal.controller.standardcode;

import com.ideal.service.standardcode.StandardCodeService;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 贯标管理控制器
 */
@Controller
public class StandardCodeController {

    private static final Logger log = Logger.getLogger(StandardCodeController.class);
    private static final String SUCCESS = "success";
    private static final String MESSAGE = "message";

    /**
     * 跳转到贯标管理页面
     */
    @RequestMapping("standardCodeManage.do")
    public String standardCodeManage() {
        return "standardcode/standardCodeManage";
    }
    
    /**
     * 获取贯标列表
     */
    @RequestMapping("standardcode/getStandardCodeList.do")
    @ResponseBody
    public Map<String, Object> getStandardCodeList(Integer start, Integer limit, String queryString) {
        StandardCodeService service = new StandardCodeService();
        Map<String, Object> map = new HashMap<>();
        try {
            map = service.getStandardCodeList(start, limit, queryString);
        } catch (Exception e) {
            log.error("查询失败", e);
            map.put("dataList", new java.util.ArrayList<>());
            map.put("total", 0);
        }
        return map;
    }

    /**
     * 保存贯标
     */
    @RequestMapping("standardcode/saveStandardCode.do")
    @ResponseBody
    public Map<String, Object> saveStandardCode(HttpServletRequest request, String jsonData) {
        StandardCodeService service = new StandardCodeService();
        Map<String, Object> map = new HashMap<>();
        try {
            String message = service.saveStandardCode(jsonData,request);
            if (!"".equals(message)) {
                map.put(SUCCESS, false);
                map.put(MESSAGE, message);
            } else {
                map.put(SUCCESS, true);
                map.put(MESSAGE, "保存成功");
            }
        } catch (Exception e) {
            log.error("保存失败", e);
            map.put(SUCCESS, false);
            map.put(MESSAGE, "保存失败");
        }
        return map;
    }

    /**
     * 删除贯标
     */
    @RequestMapping("standardcode/deleteStandardCode.do")
    @ResponseBody
    public Map<String, Object> deleteStandardCode(String deleteIds) {
        StandardCodeService service = new StandardCodeService();
        Map<String, Object> map = new HashMap<>();
        try {
            service.deleteStandardCode(deleteIds);
            map.put(SUCCESS, true);
            map.put(MESSAGE, "删除成功");
        } catch (Exception e) {
            log.error("删除失败", e);
            map.put(SUCCESS, false);
            map.put(MESSAGE, "删除失败");
        }
        return map;
    }
}
