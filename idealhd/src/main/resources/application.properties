spring.profiles.active=dev
server.ignore.servlet.context=/aoms
logging.config=${IEAI_HOME}/config/serverLog4j2.xml
server.bes.basedir=./work/tempwork
server.tomcat.basedir=./work/tempwork
server.max-http-header-size=2MB
server.tomcat.max-http-form-post-size=10MB


server.servlet.encoding.force=true
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.enabled=true

#???????????
#start.type.outside.switch=false

pf.unified.filter=false
sp.saml.auth.filter=false
xy.single.cas.filter=false
xy.single.cas.ext.filter=false
xy.single.cas.refresh.filter=false



#######hx config begin ##################

#hx.ycas.listener.switch=true
#hx.ycas.session.filter.switch=true
#hx.ycas.httpwrapper.filter.switch=true
#hx.ycas.assertion.filter.switch=true
#hx.ycas.authentication.filter.switch=true
#hx.ycas.authentication.filter.casServerLoginUrl=http://*************:8888/aoms
#hx.ycas.authentication.filter.service=http://localhost:9999/logonUa.do
#hx.ycas.authentication.filter.exceptUrl=
#hx.ycas.validation.filter.switch=true
#hx.ycas.validation.filter.casServerUrlPrefix=http://*************:8888/
#######hx config end ##################





#server.bes.basedir=classpath:/

https.server.ssl.key-store-type=JKS
https.server.ssl.key-store=classpath:itomcat.keystore
https.server.ssl.key-store-password=idealinfo
https.server.ssl.enabled=true
https.server.ssl.protocol=TLS
https.server.ssl.maxThreads=1500
https.server.ssl.secure=true
https.server.port=8887
https.server.type=tomcat
https.server.start=false
spring.server.entegor.xss.allow.urls=/acceptEmPortState.do,/getuserGroupForRoleList.do,/accessCI_CONFIGFILE.do,/productmanage/initStore.do,/accessCICDDeployExecStart.do,/productmanage/initStoreWork.do,/accessCD_CONFIGFILE.do,/CDDeployExecStart.do,/hisCiCdConDelTaskMon.do,/productmanage/initStore.do,/accessCICDExecStart.do,/accessCICDDeployExecStart.do,/accessCICDBuildExecStart.do,/execStrategy.do,/deployStrategy.do,/buildStrategy.do,/accessConDelTaskMon.do,/accessConDelTaskMon_yw.do,/accessHisCiCdMonitor.do,/accessHisCiCdMonitor_yw.do,/accessCICDTaskDetail.do,/CDDeployExecStart.do,/saveScriptEdit.do,/updateScriptEdit.do,/queryCICD_DeployReport.do,/leftPanelQuery.do,/queryDeploymentTimeForEcharts.do,/queryDeploymentTime.do,/getAutomationUsageRate.do,/topFiveWorkOrderQuantities.do,/workOrderQuantities.do,/sxTokenLogin.do,/switch/getSwitchResult.do,/switch/startSwitchFlow.do,/switch/getSwitchFlow.do,/captchaImage.do,/oneClickOfflineInterface.do,/uninstallInterface.do,/wfyh/*,/execShell/query.do,/execShell/exec.do,/execShell/token.do,/scriptdownload.do,/login2.do,/updataMethodRunTimeLogStatus.do,/startselfHealing.do,/getSelfhealing.do,/addAlarm.do,/login.do,/changePasswordPage.do,/updatePassword.do,/scriptplatform.do,/scriptUploadFiles.do,/dmoperation/V1/token.do,/dmoperation/stardard/getAgentInstallResult.do,/emerplan/V1/token.do,/emerplan/V1/emerPlanInfo.do,/dmtimetask/V1/token.do,/dmtimetask/V1/getToken.do,/getHcToken.do,/sendNumber.do,/about/equipments/equipment.do,/CollectResult/cpid.do,/infoCollection/token.do,/getScriptToken.do,/saveCheckResult.do,/getComputerStatus.do,/integratePushApi.do,/agencytasksendexcel.do,/saveordeluser.do,/initButtonAndMenu.do,/cibinitscreen.do,/restfullDbaasGetDbpwd.do,/speakerAlarm.do,/hcCheckUser.do,/toHcLogin.do,/susfinishAndOut.do,/azfinishAndOut.do,/getProcData.do,/saveCibAgentInfo.do,/api_gfcheckhc/V1/getToken.do,/api_gfcheckhc/V1/token.do,/updateIthreshold.do,/updateCibItsmMessage.do,/api_suppercheckhc/V1/token.do,/pubUmsRecordJson.do,/pubAomsRecordJson.do,/pubAomsOpmJson.do,/getEmToken.do,/getEmPlanNames.do,/emStartPlan.do,/getEmMonitorInfo.do,/itilopration/token.do,/itilopration/itilApproved.do,/deleteEntegorConfigFromOther.do,/reloadConfigFromOther.do,/opm/agentapi/getAgentInfo.do,/opm/agentapi/bindAgentSystem.do,/opm/agentapi/agentOffLine.do,/alarmInfo/getRemoteAlarmInfoData.do,/ciblogin.do,/rest/openapi/notification/common/alarm.do,/getTcAlarmToken.do,/verificationalarm.do,/api/users.do,/token/getToken.do,/token/verify.do,/getOnlineUsersData.do,/kikoutUsersBuSession.do,/monitorDirPage.do,/opm/agentproxyapi/getAgentInfo.do,/opm/agentapi/agentproxyapi/getAgentInfo.do,/opm/agentapi/agentproxyapi/getProxyInfo.do,/opm/agentproxyapi/getProxyInfo.do,/opm/agentproxyapi/getAgentProxyRelation.do,/updateOldCodeData.do,/artifacts/pushGenericFileAPI.do,/artifacts/queryProjectListPage.do,/artifacts/queryStoreListPage.do,/artifacts/platform.do,/getswitchruninsInterface.do,/cmdb/V1/getCmdbListByTypeRelation.do,/cmdb/V1/queryCmdbInfoListPage.do,/cmdb/V1/queryCmdbTypeAttributeList.do,/cmdb/V1/queryCMDBTypeList.do,/dmoperation/stardard/evertoken.do,/opm/agentapi/startLogCenterDataCollect.do,/dmoperation/stardard/queryDayAndEXecutionCount.do,/dmoperation/stardard/queryDayAndEXecutionDetail.do,/dmoperation/stardard/queryDayAndEXecutionPrj.do,/performShutdownPlan.do,/updateRecoverActStatus.do,/order/createOrder.do,/order/createOrderNew.do,/order/getOrderStatus.do,/sus/api/saveProjSys.do,/sus/api/saveSequential.do,/riseExport.do,/riseImport.do,/riseChange.do,/approveNotify.do,/productmanage/uploadFile.do,/productmanage/pullFile.do,/sus/api/queryRiseTask.do,/sus/api/flowTaskApproved.do,/sus/api/execFJRCStart.do,/accessCI_CONFIGFILEKg.do,/productmanage/initProductKg.do,/V1/updateConfigStatusByItemId.do,/V1/updateArtifactsStatus.do,/V1/updateSequentState.do,/V1/updateCHGState.do,/productmanage/pushGenericFile.do,/productmanage/pullGenericFile.do,/productmanage/updateIseorderByid.do,/accessCICD_CodeList.do,/queryCICD_CodeList.do,/queryCICD_CodeListHis.do,/queryCICD_ListType.do,/queryCICDPersonInfoSyscode.do,/queryCICD_SystemName.do,/getmodulebySystem.do,/saveCICD_CodeList.do,/checkCodeListClob.do,/checkCodeListModelType.do,/editCICD_CodeList.do,/CodeListProject.do,/codeListClob.do,/deleteCICD_CodeList.do,/deleteCICD_CodeOne.do,/copyCodeList.do,/exportCodelist.do,/ExportConfig.do,/codelistfile.do,/queryOldProjectINFO.do,/checkCodeListProject.do,/importCodelistfile.do,/productmanage/sendNotifyforCD.do,/productmanage/pushGenericFileServer.do,/saveCICDFlowTask.do,/getPromotionLog.do,/editCI_CONFIGFILE.do,/editConfigFileData.do,/getPreviousVersionContext.do,/CI_pushItem.do,/editConfigFileData_CD.do,/productmanage/pushGeneric.do,/getAgentForStateList.do,/getAgentStatusList.do,/checkBlacklistCommand.do,/validataShellScript.do,/smsApprovalReply.do,/drcbUnifiedInterface.do,/flopDataDate.do,/positionBegin.do,/lookMonitorCreatData.do,/getJobByPrjId_jobDetailShow.do,/lookMonitorCreatDataByMobile.do,/promotionExcelSystem.do

server.tongweb.license.type = file 
server.tongweb.license.path = classpath:license.dat


spring.datasource.url=****************************************
spring.datasource.username=root
spring.datasource.password=ideal123
spring.datasource.driver-class-name=com.p6spy.engine.spy.P6SpyDriver


modulelist=com.p6spy.engine.logging.P6LogFactory
logMessageFormat=com.p6spy.engine.spy.appender.MultiLineFormat
appender=com.p6spy.engine.spy.appender.StdoutLogger