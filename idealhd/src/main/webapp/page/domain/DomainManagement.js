Ext.onReady(function() {

    destroyRubbish();
    Ext.tip.QuickTipManager.init();// QuickTips的作用是读取标签中的ext:qtip属性，并为它赋予显示提示的动作。
    
    // 定义按钮
    var queryButton = Ext.create("Ext.Button", {
        text: '查询',
        cls: 'Common_Btn',// 确保样式统一
        handler: function () {
            queryBtnFun();
        }
    });

    /** 重置按钮* */
    var resetButton = Ext.create("Ext.Button", {
        cls: 'Common_Btn',
        textAlign: 'center',
        text: '重置',
        handler: resetWhere
    });

    var addButton = Ext.create("Ext.Button", {
        text: '增加',
        cls: 'Common_Btn',
        handler: function () {
            addCfg();
        }
    });

    var saveButton = Ext.create("Ext.Button", {
        text: '保存',
        cls: 'Common_Btn',
        handler: function () {
            saveCfg();
        }
    });

    var delButton = Ext.create("Ext.Button", {
        text: '删除',
        cls: 'Common_Btn',
        handler: function () {
            delCfgs();
        }
    });

    // ** 查询条件* *//*
    var iname = Ext.create('Ext.form.TextField', {
        id:'iname',
        emptyText: '请输入执行域名称',
        width: 200,
        name: 'iname',
        xtype: 'textfield',
        listeners: {
            specialkey: function (field, e) { // 按下与导航相关的任何键（箭头，制表符，输入，esc等）时触发。您可以检查Ext.EventObject.getKey以确定按下了哪个键
                if (e.getKey() == Ext.EventObject.ENTER) { // e.getKey()相当于Ext.EventObject.getKey()其中ENTER的ASCLL的编码为13,即按下ENTER出发的事件
                    var checkModelName = field.getValue() == null ? "" : field.getValue();// field为输入框对象
                    grid.ipage.moveFirst();
                    store.load({
                        params: {iname: checkModelName.trim()}
                    });
                }
            }
        }
    });
    
    // ** 查询条件* *//*
    var idesc = Ext.create('Ext.form.TextField', {
        id:'idesc',
        emptyText: '请输入执行域描述',
        width: 200,
        name: 'idesc',
        xtype: 'textfield',
        listeners: {
            specialkey: function (field, e) { // 按下与导航相关的任何键（箭头，制表符，输入，esc等）时触发。您可以检查Ext.EventObject.getKey以确定按下了哪个键
                if (e.getKey() == Ext.EventObject.ENTER) { // e.getKey()相当于Ext.EventObject.getKey()其中ENTER的ASCLL的编码为13,即按下ENTER出发的事件
                    var checkModelName = field.getValue() == null ? "" : field.getValue();// field为输入框对象
                    grid.ipage.moveFirst();
                    store.load({
                        params: {idesc: checkModelName.trim()}
                    });
                }
            }
        }
    });

    // 创建form以及其他组件
    var form = Ext.create('Ext.form.FormPanel', {
        region: 'north',
        border: false,
        bodyCls: 'x-docked-noborder-top',
        baseCls: 'customize_gray_back',
        dockedItems: [{
            baseCls: 'customize_gray_back',
            xtype: 'toolbar',
            border: false,
            dock: 'top',
            // 定义按钮
            items: [iname, idesc, queryButton, resetButton, '->', addButton, saveButton, delButton
            ]
        }]
    });

    // 定义方案名称
    var dataModel  =  Ext.define('domainManagement', {
        extend : 'Ext.data.Model',
        fields : [ {
            name : 'iid',
            type : 'string'
        },{
            name : 'iname',
            type : 'string'
        }, {
            name : 'idesc',
            type : 'string'
        }, {
            name : 'icreatorid',
            type : 'string'
        }, {
            name : 'icreatorname',
            type : 'string'
        }, {
            name : 'icreatetime',
            type : 'string',
        }]
    });

    // 定义store  获取列表
    var store = Ext.create('Ext.data.Store', {
        autoLoad : true,
        autoDestroy : true,
        model : 'domainManagement',
        pageSize : 30,// 每页显示数据的条数
        proxy : {// 用于从某个路径读取数据
            type : 'ajax',// 代理类型
            url : 'getDomainManagementList.do',// 查询方法的路径
            reader : {// 从后台读入数据
                type : 'json',
                root : 'dataList',
                totalProterty : 'total'
            }
        }
    });

    // 定义列
    var columns = [ {
        text : '序号',
        width : 60,
        xtype : 'rownumberer'
    }, {
        text : 'id',
        dataIndex : 'iid',
        hidden : true
    }, {
        text : '执行域名称',
        flex : 1,
        dataIndex : 'iname',
        editor : new Ext.form.TextField({
            allowBlank : false
        })
    }, {
        text : '执行域描述',
        flex : 2,
        dataIndex : 'idesc',
        editor : new Ext.form.TextField({
            allowBlank : true
        })
    }, {
        text : '创建人',
        flex : 1,
        dataIndex : 'icreatorname',
        remoteSort : true,
    }, {
        text : '创建时间',
        flex : 1,
        dataIndex : 'icreatetime',
        remoteSort : true,
    }, {
        text : '操作',
        xtype : 'actiontextcolumn',
        align : 'left',
        flex : 1,
        items : [{
            text : '配置',
            iconCls : 'role_permission',
            handler : function (grid, rowIndex) {
                var domainId = grid.getStore().data.items[rowIndex].data.iid;
                var domainName = grid.getStore().data.items[rowIndex].data.iname;
                domainConfig(grid, rowIndex, domainId, domainName);
            }
        }]
    }];

    // 定义grid
    var grid = Ext.create('Ext.ux.ideal.grid.Panel', {
        cls:'customize_panel_back',
        ipageBaseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar',
        region : 'center',
        padding : panel_margin,
        store : store,
        autoScroll : true,
        border : true,
        columnLines : true,
        selModel : Ext.create('Ext.selection.CheckboxModel'),
        loadMask : {
            msg : " 数据加载中，请稍等 "
        },
        columns : columns,
        viewConfig : {
            enableTextSelection : true
        }
    });

    // 定义面板 设置renderTo
    var mainPanel = Ext.create('Ext.panel.Panel', {
        layout : 'border',// 东南西北布局方式
        renderTo : "domainManagement_area",// 指定要填充的位置
        bodyPadding : grid_margin,
        bodyCls: 'service_platform_bodybg',
        border : true,
        height : contentPanel.getHeight() - modelHeigth,
        width : contentPanel.getWidth,
        items : [ form, grid ]// 定义包含什么组件
    });

    store.on('beforeload', function(store, options) {
        var new_params = {
            iname : iname.getValue().trim(),
            idesc : idesc.getValue().trim()
        };
        Ext.apply(store.proxy.extraParams, new_params);
    });

    // 窗口自适应
    contentPanel.on('resize', function() {
        mainPanel.setHeight(contentPanel.getHeight() - modelHeigth);
        mainPanel.setWidth(contentPanel.getWidth);
    });

    // 查询的方法
    function queryBtnFun() {
        if (Ext.isIE) {
            CollectGarbage();
        }
        grid.ipage.moveFirst();
    }

    function resetWhere() {
        iname.setValue('');
        idesc.setValue('');
    }

    function addCfg() {
        store.insert(0, new dataModel());
    }

    // 保存的方法
    function saveCfg() {
        var records = store.getModifiedRecords();// 获取改变的记录
        if (records.length == 0) {
            Ext.Msg.show({
                title : '提示',
                msg : '域信息没有变更，请选择需要保存的记录',
                buttons : Ext.Msg.OK,// 单个确认按钮
                icon : Ext.Msg.INFO// 叹号图标
            });
        } else {
            var lstAddRecord = new Array();
            var flag = true;
            Ext.each(records, function(record) {
                lstAddRecord.push(record.data); // record.data 中的数据是依据store.insert(0,p)其中p所定义的属性名
                var inameValue =  record.data.iname.trim();
                console.log(inameValue)
                if ("" == inameValue || null == inameValue) {
                    setMessage('执行域名称不能为空！');
                    flag = false;
                    return;
                }
            });
            if(flag){
                Ext.Ajax.request({
                    url : 'saveDomainManagement.do',// 保存域的路径
                    params : {
                        jsonData : Ext.encode(lstAddRecord)// 将参数封装为jsonData形式
                    },
                    success : function(response, opts) {
                        // var success = Ext.decode(response.responseText).success;
                        var message = Ext.decode(response.responseText).message;// 获取响应数据
                        Ext.MessageBox.show({
                            title : "提示",
                            msg : message,
                            buttonText : {
                                yes : '确定'
                            },
                            buttons : Ext.Msg.YES
                        });
                        store.reload();
                    },
                    failure : function(result, opts) {
                        secureFilterRs(result, "请求返回失败！");
                    }
                });
            }
        }
    }

    function delCfgs() {
        if (Ext.isIE) {
            CollectGarbage();// 清理内存
        }
        var seleCount = grid.getSelectionModel().getSelection();
        if (seleCount.length == 0) {
            Ext.MessageBox.alert("提示", "请选择要删除的数据");
            return;
        }

        Ext.MessageBox.buttonText.yes = "确定";
        Ext.MessageBox.buttonText.no = "取消";
        Ext.Msg.confirm("确认删除", "确定删除选中的域记录", function(id) {
            if (id == 'yes')
                delCfg();
        });
    }

    // 删除的方法
    function delCfg() {
        var records = grid.getSelectionModel().getSelection();
        var jsonArray = [];
        Ext.each(records, function(item) {// 遍历
            jsonArray.push(item.data.iid);
        });
        Ext.Ajax.request({
            url : 'deleteDomainManagement.do',// 删除域信息的方法
            method : 'post',
            params : {
                iids : jsonArray.join(',')
            },
            success : function(response, request) {
                var success = Ext.decode(response.responseText).success;
                var message = Ext.decode(response.responseText).message;
                if (success) {
                    grid.ipage.moveFirst();
                    Ext.Msg.alert('提示', message);
                } else {
                    Ext.Msg.alert('提示', message);
                }
            },
            failure : function(result, request) {
                secureFilterRs(result, "请求返回失败！", request);
            }
        });
    }

    function setMessage(msg) {
        Ext.Msg.alert('提示', msg);
    }

    //执行域配置功能
    function domainConfig(grid, rowIndex, domainId, domainName) {
        
        //服务器绑定数据模型
        Ext.define('domainServerModel', {
            extend: 'Ext.data.Model',
            fields: [
                {
                    name: 'iid',
                    type: 'long'
                }, {
                    name: 'domainId',
                    type: 'long'
                }, {
                    name: 'serverId',
                    type: 'long'
                }, {
                    name: 'serverName',
                    type: 'string'
                }, {
                    name: 'serverIp',
                    type: 'string'
                }, {
                    name: 'serverPort',
                    type: 'string'
                }
            ]
        });

        //服务器数据源
        var serverStore = Ext.create('Ext.data.Store', {
            autoLoad: true,
            autoDestroy: true,
            remoteSort: true,
            model: 'domainServerModel',
            proxy: {
                type: 'ajax',
                url: 'getDomainServerList.do',
                reader: {
                    type: 'json',
                    root: 'dataList',
                    totalProperty: 'total'
                }
            }
        });

        serverStore.on('beforeload', function (store, options) {
            var new_params = {
                serverName: serverForm.getForm().findField('serverName').getValue(),
                domainId: domainId
            };
            Ext.apply(serverStore.proxy.extraParams, new_params);
        });

        //服务器配置表单
        var serverForm = Ext.create('Ext.ux.ideal.form.Panel', {
            region: 'north',
            layout: 'anchor',
            buttonAlign: 'center',
            iqueryFun: queryWhere_server,
            iselect: false,
            border: false,
            dockedItems: [{
                xtype: 'toolbar',
                items: [{
                    fieldLabel: '服务器名称',
                    labelAlign: 'right',
                    labelWidth: 80,
                    emptyText: '--请输入服务器名称--',
                    name: 'serverName',
                    width: 300,
                    xtype: 'textfield',
                    listeners: {
                        specialkey: function (field, e) {
                            if (e.getKey() == Ext.EventObject.ENTER) {
                                var serverName = field.getValue() == null ? "" : field.getValue();
                                serverStore.load({
                                    params: {
                                        serverName: serverName.trim(),
                                        domainId: domainId
                                    }
                                });
                            }
                        }
                    }
                },
                    '->', Ext.create("Ext.Button", {
                    cls: 'Common_Btn',
                    text: "查询",
                    handler: function () {
                        serverStore.load({
                            params: {
                                serverName: serverForm.getForm().findField('serverName').getValue(),
                                domainId: domainId
                            }
                        });
                    }
                }), Ext.create("Ext.Button", {
                    cls: 'Common_Btn',
                    text: "增加",
                    handler: function () {
                        // 查询可用服务器列表
                        Ext.define('availableServerModel', {
                            extend: 'Ext.data.Model',
                            fields: [{
                                name: 'iid',
                                type: 'string'
                            }, {
                                name: 'serverName',
                                type: 'string'
                            }, {
                                name: 'serverIp',
                                type: 'string'
                            }, {
                                name: 'serverPort',
                                type: 'string'
                            }]
                        });

                        var availableServerStore = Ext.create('Ext.data.Store', {
                            autoLoad: true,
                            autoDestroy: true,
                            model: 'availableServerModel',
                            proxy: {
                                type: 'ajax',
                                url: 'getAvailableServerList.do',
                                reader: {
                                    type: 'json',
                                    root: 'dataList'
                                }
                            }
                        });

                        availableServerStore.on('beforeload', function (store, options) {
                            var new_params = {
                                serverName: serverForQuery.getValue().trim(),
                                domainId: domainId
                            };
                            Ext.apply(availableServerStore.proxy.extraParams, new_params);
                        });

                        // 查询按钮
                        var queryButtonForServer = Ext.create("Ext.Button", {
                            cls: 'Common_Btn',
                            textAlign: 'center',
                            text: '查询',
                            handler: function () {
                                var serverName = serverForQuery.getValue() == null ? "" : serverForQuery.getValue();
                                availableServerStore.load({
                                    params: {serverName: serverName.trim()}
                                });
                            }
                        });

                        // 重置按钮
                        var resetButtonForServer = Ext.create("Ext.Button", {
                            cls: 'Common_Btn',
                            textAlign: 'center',
                            text: '重置',
                            handler: function () {
                                serverForQuery.setValue('');
                            }
                        });

                        // 绑定按钮
                        var bindButtonForServer = Ext.create("Ext.Button", {
                            cls: 'Common_Btn',
                            textAlign: 'center',
                            text: '绑定',
                            handler: function () {
                                var jsonData = "[";
                                var m = addServerGrid.getSelectionModel().getSelection();
                                if (m.length < 1) {
                                    Ext.Msg.alert('提示', '无需要绑定的服务器！');
                                    return;
                                }
                                for (var i = 0, len = m.length; i < len; i++) {
                                    var ss = Ext.JSON.encode(m[i].data);
                                    if (i == 0)
                                        jsonData = jsonData + ss;
                                    else
                                        jsonData = jsonData + "," + ss;
                                }
                                jsonData = jsonData + "]";
                                Ext.MessageBox.wait("数据处理中...", "进度条");
                                Ext.Ajax.request({
                                    url: 'bindDomainServer.do',
                                    timeout: 30000,
                                    params: {
                                        jsonData: jsonData,
                                        domainId: domainId
                                    },
                                    method: 'POST',
                                    success: function (response, opts) {
                                        var success = Ext.decode(response.responseText).success;
                                        var message = Ext.decode(response.responseText).message;
                                        if (success) {
                                            Ext.Msg.alert('提示', message, function() {
                                                // 关闭绑定窗口
                                                addServer.close();
                                                // 刷新服务器绑定列表
                                                queryWhere_server();
                                                // 确保切换到服务器绑定tab
                                                tab.setActiveTab(0);
                                            });
                                        } else {
                                            Ext.Msg.alert('提示', message);
                                        }
                                    },
                                    failure: function (response, ooptions) {
                                        Ext.MessageBox.hide();
                                        Ext.Msg.alert('提示', '请求超时！');
                                    }
                                });
                            }
                        });

                        var serverForQuery = Ext.create('Ext.form.TextField', {
                            emptyText: '--请输入服务器名称--',
                            labelWidth: 80,
                            width: 300,
                            xtype: 'textfield',
                            listeners: {
                                specialkey: function (field, e) {
                                    if (e.getKey() == Ext.EventObject.ENTER) {
                                        var serverName = field.getValue() == null ? "" : field.getValue();
                                        availableServerStore.load({
                                            params: {serverName: serverName.trim()}
                                        });
                                    }
                                }
                            }
                        });

                        var selModel = Ext.create('Ext.selection.CheckboxModel');
                        // 列表Columns
                        var gridColumns = [{
                            text: '序号',
                            width: 50,
                            xtype: 'rownumberer'
                        }, {
                            text: 'iid',
                            dataIndex: 'iid',
                            flex: 1,
                            hidden: true
                        }, {
                            text: '服务器名称',
                            dataIndex: 'serverName',
                            flex: 1
                        }, {
                            text: '服务器IP',
                            dataIndex: 'serverIp',
                            flex: 1
                        }];

                        // 可用服务器列表panel
                        var addServerGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
                            height: contentPanel.getHeight() - 200,
                            store: availableServerStore,
                            selModel: selModel,
                            region: 'center',
                            padding: panel_margin,
                            cls: 'customize_panel_back',
                            border: true,
                            ipageBaseCls: Ext.baseCSSPrefix + 'toolbar customize_toolbar',
                            columnLines: true,
                            columns: gridColumns,
                            collapsible: false,
                            viewConfig: {
                                enableTextSelection: true
                            }
                        });

                        var Form1 = Ext.create('Ext.form.FormPanel', {
                            region: 'north',
                            border: false,
                            dockedItems: [{
                                xtype: 'toolbar',
                                border: false,
                                dock: 'top',
                                items: [serverForQuery, queryButtonForServer, resetButtonForServer, '->', bindButtonForServer]
                            }]
                        });

                        var addServerPanel = Ext.create('Ext.panel.Panel', {
                            border: false,
                            anchor: '0,0',
                            padding: grid_space,
                            width: contentPanel.getWidth() - 650,
                            height: contentPanel.getHeight() - 100,
                            items: [Form1, addServerGrid]
                        });

                        var addServer = Ext.create('Ext.window.Window', {
                            id: 'addServerWin',
                            closeAction: 'destroy',
                            modal: true,
                            region: 'right',
                            items: [addServerPanel],
                            width: contentPanel.getWidth() - 600,
                            height: contentPanel.getHeight(),
                            draggable: false,
                            resizable: true,
                            listeners: {
                                'close': function () {
                                }
                            },
                            layout: 'fit',
                        }).show();
                        addServer.center();
                    }
                }), Ext.create("Ext.Button", {
                    cls: 'Common_Btn',
                    text: "删除",
                    handler: function () {
                        var records = serverGrid.getSelectionModel().getSelection();
                        var id;
                        if (records.length == 0) {
                            Ext.Msg.alert('提示', "请选择要删除的行");
                            return;
                        }

                        for (var i = 0; i < records.length; i++) {
                            if (i == 0) {
                                id = records[i].get("iid");
                            } else {
                                id = id + "," + records[i].get("iid");
                            }
                        }

                        Ext.Ajax.request({
                            url: 'unbindDomainServer.do',
                            method: 'POST',
                            params: {
                                deleteIds: id
                            },
                            success: function (response, request) {
                                var success = Ext.decode(response.responseText).success;
                                var message = Ext.decode(response.responseText).message;
                                if (success) {
                                    Ext.Msg.alert('提示', '删除成功');
                                    queryWhere_server();
                                    serverGrid.getSelectionModel().deselectAll();
                                } else {
                                    Ext.Msg.alert('提示', message);
                                }
                            },
                            failure: function (result, request) {
                                secureFilterRs(result, '删除失败！');
                            }
                        });
                    }
                })]
            }]
        });

        //服务器绑定列表
        var serverGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
            store: serverStore,
            height: contentPanel.getHeight() - 230,
            ipageBaseCls: Ext.baseCSSPrefix + 'toolbar customize_toolbar',
            padding: grid_space,
            cls: 'customize_panel_back',
            selModel: Ext.create('Ext.selection.CheckboxModel'),
            region: 'center',
            border: true,
            cellTip: true,
            columns: [
                {text: '序号', width: 40, align: 'left', xtype: 'rownumberer'},
                {text: 'iid', dataIndex: 'iid', hidden: true},
                {text: '服务器名称', width: 160, dataIndex: 'serverName', flex: 1},
                {text: '服务器IP', dataIndex: 'serverIp', flex: 1},
            ],
            animCollapse: false,
            selModel: Ext.create('Ext.selection.CheckboxModel', {
                checkOnly: true
            }),
        });

        var serverPanel = Ext.create('Ext.panel.Panel', {
            border: false,
            anchor: '0,0',
            padding: grid_space,
            height: contentPanel.getHeight() - 100,
            width: contentPanel.getWidth() - 650,
            items: [serverForm, serverGrid]
        });

        //业务系统绑定数据模型
        Ext.define('domainBusinessModel', {
            extend: 'Ext.data.Model',
            fields: [
                {
                    name: 'iid',
                    type: 'long'
                }, {
                    name: 'domainId',
                    type: 'long'
                }, {
                    name: 'businessId',
                    type: 'long'
                }, {
                    name: 'businessName',
                    type: 'string'
                }, {
                    name: 'businessCode',
                    type: 'string'
                }
            ]
        });

        //业务系统数据源
        var businessStore = Ext.create('Ext.data.Store', {
            autoLoad: true,
            autoDestroy: true,
            remoteSort: true,
            model: 'domainBusinessModel',
            proxy: {
                type: 'ajax',
                url: 'getDomainBusinessList.do',
                reader: {
                    type: 'json',
                    root: 'dataList',
                    totalProperty: 'total'
                }
            }
        });

        businessStore.on('beforeload', function (store, options) {
            var new_params = {
                businessName: businessForm.getForm().findField('businessName').getValue(),
                domainId: domainId
            };
            Ext.apply(businessStore.proxy.extraParams, new_params);
        });

        //业务系统配置表单
        var businessForm = Ext.create('Ext.ux.ideal.form.Panel', {
            region: 'north',
            layout: 'anchor',
            buttonAlign: 'center',
            iqueryFun: queryWhere_business,
            iselect: false,
            border: false,
            dockedItems: [{
                xtype: 'toolbar',
                items: [{
                    fieldLabel: '业务系统名称',
                    labelAlign: 'right',
                    labelWidth: 90,
                    emptyText: '--请输入业务系统名称--',
                    name: 'businessName',
                    width: 300,
                    xtype: 'textfield',
                    listeners: {
                        specialkey: function (field, e) {
                            if (e.getKey() == Ext.EventObject.ENTER) {
                                var businessName = field.getValue() == null ? "" : field.getValue();
                                businessStore.load({
                                    params: {
                                        businessName: businessName.trim(),
                                        domainId: domainId
                                    }
                                });
                            }
                        }
                    }
                },
                    '->', Ext.create("Ext.Button", {
                    cls: 'Common_Btn',
                    text: "查询",
                    handler: function () {
                        businessStore.load({
                            params: {
                                businessName: businessForm.getForm().findField('businessName').getValue(),
                                domainId: domainId
                            }
                        });
                    }
                }), Ext.create("Ext.Button", {
                    cls: 'Common_Btn',
                    text: "增加",
                    handler: function () {
                        // 查询可用业务系统列表
                        Ext.define('availableBusinessModel', {
                            extend: 'Ext.data.Model',
                            fields: [{
                                name: 'iid',
                                type: 'string'
                            }, {
                                name: 'businessName',
                                type: 'string'
                            }, {
                                name: 'businessCode',
                                type: 'string'
                            }, {
                                name: 'type',
                                type: 'int'
                            }]
                        });

                        var availableBusinessStore = Ext.create('Ext.data.Store', {
                            autoLoad: true,
                            autoDestroy: true,
                            model: 'availableBusinessModel',
                            proxy: {
                                type: 'ajax',
                                url: 'getAvailableBusinessList.do',
                                reader: {
                                    type: 'json',
                                    root: 'dataList'
                                }
                            }
                        });

                        availableBusinessStore.on('beforeload', function (store, options) {
                            var new_params = {
                                businessName: businessForQuery.getValue().trim(),
                                domainId: domainId
                            };
                            Ext.apply(availableBusinessStore.proxy.extraParams, new_params);
                        });

                        // 查询按钮
                        var queryButtonForBusiness = Ext.create("Ext.Button", {
                            cls: 'Common_Btn',
                            textAlign: 'center',
                            text: '查询',
                            handler: function () {
                                var businessName = businessForQuery.getValue() == null ? "" : businessForQuery.getValue();
                                availableBusinessStore.load({
                                    params: {businessName: businessName.trim()}
                                });
                            }
                        });

                        // 重置按钮
                        var resetButtonForBusiness = Ext.create("Ext.Button", {
                            cls: 'Common_Btn',
                            textAlign: 'center',
                            text: '重置',
                            handler: function () {
                                businessForQuery.setValue('');
                            }
                        });

                        // 绑定按钮
                        var bindButtonForBusiness = Ext.create("Ext.Button", {
                            cls: 'Common_Btn',
                            textAlign: 'center',
                            text: '绑定',
                            handler: function () {
                                var jsonData = "[";
                                var m = addBusinessGrid.getSelectionModel().getSelection();
                                if (m.length < 1) {
                                    Ext.Msg.alert('提示', '无需要绑定的业务系统！');
                                    return;
                                }
                                for (var i = 0, len = m.length; i < len; i++) {
                                    var ss = Ext.JSON.encode(m[i].data);
                                    if (i == 0)
                                        jsonData = jsonData + ss;
                                    else
                                        jsonData = jsonData + "," + ss;
                                }
                                jsonData = jsonData + "]";
                                Ext.MessageBox.wait("数据处理中...", "进度条");
                                Ext.Ajax.request({
                                    url: 'bindDomainBusiness.do',
                                    timeout: 30000,
                                    params: {
                                        jsonData: jsonData,
                                        domainId: domainId
                                    },
                                    method: 'POST',
                                    success: function (response, opts) {
                                        var success = Ext.decode(response.responseText).success;
                                        var message = Ext.decode(response.responseText).message;
                                        if (success) {
                                            Ext.Msg.alert('提示', message, function() {
                                                // 关闭绑定窗口
                                                addBusiness.close();
                                                // 刷新业务系统绑定列表
                                                queryWhere_business();
                                                // 确保切换到业务系统绑定tab
                                                tab.setActiveTab(1);
                                            });
                                        } else {
                                            Ext.Msg.alert('提示', message);
                                        }
                                    },
                                    failure: function (response, ooptions) {
                                        Ext.MessageBox.hide();
                                        Ext.Msg.alert('提示', '请求超时！');
                                    }
                                });
                            }
                        });

                        var businessForQuery = Ext.create('Ext.form.TextField', {
                            emptyText: '--请输入业务系统名称--',
                            labelWidth: 80,
                            width: 300,
                            xtype: 'textfield',
                            listeners: {
                                specialkey: function (field, e) {
                                    if (e.getKey() == Ext.EventObject.ENTER) {
                                        var businessName = field.getValue() == null ? "" : field.getValue();
                                        availableBusinessStore.load({
                                            params: {businessName: businessName.trim()}
                                        });
                                    }
                                }
                            }
                        });

                        var selModel = Ext.create('Ext.selection.CheckboxModel');
                        // 列表Columns
                        var gridColumns = [{
                            text: '序号',
                            width: 50,
                            xtype: 'rownumberer'
                        }, {
                            text: 'iid',
                            dataIndex: 'iid',
                            flex: 1,
                            hidden: true
                        }, {
                            text: 'type',
                            dataIndex: 'type',
                            flex: 1,
                            hidden: true
                        }, {
                            text: '业务系统名称',
                            dataIndex: 'businessName',
                            flex: 1
                        }, {
                            text: '业务系统编码',
                            dataIndex: 'businessCode',
                            flex: 1
                        }];

                        // 可用业务系统列表panel
                        var addBusinessGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
                            height: contentPanel.getHeight() - 200,
                            store: availableBusinessStore,
                            selModel: selModel,
                            region: 'center',
                            padding: panel_margin,
                            cls: 'customize_panel_back',
                            border: true,
                            ipageBaseCls: Ext.baseCSSPrefix + 'toolbar customize_toolbar',
                            columnLines: true,
                            columns: gridColumns,
                            collapsible: false,
                            viewConfig: {
                                enableTextSelection: true
                            }
                        });

                        var Form2 = Ext.create('Ext.form.FormPanel', {
                            region: 'north',
                            border: false,
                            dockedItems: [{
                                xtype: 'toolbar',
                                border: false,
                                dock: 'top',
                                items: [businessForQuery, queryButtonForBusiness, resetButtonForBusiness, '->', bindButtonForBusiness]
                            }]
                        });

                        var addBusinessPanel = Ext.create('Ext.panel.Panel', {
                            border: false,
                            anchor: '0,0',
                            padding: grid_space,
                            width: contentPanel.getWidth() - 650,
                            height: contentPanel.getHeight() - 100,
                            items: [Form2, addBusinessGrid]
                        });

                        var addBusiness = Ext.create('Ext.window.Window', {
                            id: 'addBusinessWin',
                            closeAction: 'destroy',
                            modal: true,
                            region: 'right',
                            items: [addBusinessPanel],
                            width: contentPanel.getWidth() - 600,
                            height: contentPanel.getHeight(),
                            draggable: false,
                            resizable: true,
                            listeners: {
                                'close': function () {
                                }
                            },
                            layout: 'fit',
                        }).show();
                        addBusiness.center();
                    }
                }), Ext.create("Ext.Button", {
                    cls: 'Common_Btn',
                    text: "删除",
                    handler: function () {
                        var records = businessGrid.getSelectionModel().getSelection();
                        var id;
                        if (records.length == 0) {
                            Ext.Msg.alert('提示', "请选择要删除的行");
                            return;
                        }

                        for (var i = 0; i < records.length; i++) {
                            if (i == 0) {
                                id = records[i].get("iid");
                            } else {
                                id = id + "," + records[i].get("iid");
                            }
                        }

                        Ext.Ajax.request({
                            url: 'unbindDomainBusiness.do',
                            method: 'POST',
                            params: {
                                deleteIds: id
                            },
                            success: function (response, request) {
                                var success = Ext.decode(response.responseText).success;
                                var message = Ext.decode(response.responseText).message;
                                if (success) {
                                    Ext.Msg.alert('提示', '删除成功');
                                    queryWhere_business();
                                    businessGrid.getSelectionModel().deselectAll();
                                } else {
                                    Ext.Msg.alert('提示', message);
                                }
                            },
                            failure: function (result, request) {
                                secureFilterRs(result, '删除失败！');
                            }
                        });
                    }
                })]
            }]
        });

        //业务系统绑定列表
        var businessGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
            store: businessStore,
            height: contentPanel.getHeight() - 230,
            ipageBaseCls: Ext.baseCSSPrefix + 'toolbar customize_toolbar',
            padding: grid_space,
            cls: 'customize_panel_back',
            selModel: Ext.create('Ext.selection.CheckboxModel'),
            region: 'center',
            border: true,
            cellTip: true,
            columns: [
                {text: '序号', width: 40, align: 'left', xtype: 'rownumberer'},
                {text: 'iid', dataIndex: 'iid', hidden: true},
                {text: '业务系统名称', width: 160, dataIndex: 'businessName', flex: 1},
                {text: '业务系统编码', dataIndex: 'businessCode', flex: 1}
            ],
            animCollapse: false,
            selModel: Ext.create('Ext.selection.CheckboxModel', {
                checkOnly: true
            }),
        });

        var businessPanel = Ext.create('Ext.panel.Panel', {
            border: false,
            anchor: '0,0',
            padding: grid_space,
            height: contentPanel.getHeight() - 100,
            width: contentPanel.getWidth() - 650,
            items: [businessForm, businessGrid]
        });

        //创建Tab页面
        var tab = Ext.create('Ext.tab.Panel', {
            cls: 'customize_panel_back',
            tabPosition: 'top',
            border: false,
            region: 'center',
            items: [{
                xtype: 'panel',
                title: '服务器绑定',
                listeners: {
                    'activate': function () {
                        serverGrid.ipage.moveFirst();
                    },
                },
                items: [serverPanel]
            }, {
                xtype: 'panel',
                title: '业务系统绑定',
                listeners: {
                    'activate': function () {
                        businessGrid.ipage.moveFirst();
                    }
                },
                items: [businessPanel]
            }]
        });

        //创建配置窗口
        var showDomainConfigWin = Ext.create('Ext.window.Window', {
            id: 'showDomainConfigWin',
            title: '执行域配置 - ' + domainName,
            closeAction: 'destroy',
            modal: true,
            region: 'center',
            items: [tab],
            width: contentPanel.getWidth() - 600,
            height: contentPanel.getHeight(),
            draggable: false,
            resizable: true,
            listeners: {
                'close': function () {
                }
            },
            layout: 'fit',
        }).show();
        showDomainConfigWin.center();

        //查询服务器信息
        function queryWhere_server() {
            serverStore.load({
                params: {
                    serverName: "",
                    domainId: domainId
                }
            });
        }

        //查询业务系统信息
        function queryWhere_business() {
            businessStore.load({
                params: {
                    businessName: "",
                    domainId: domainId
                }
            });
        }
    }
});
