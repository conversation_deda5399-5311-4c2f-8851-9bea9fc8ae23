/**
 * 贯标管理页面
 * <AUTHOR>
 * @date 2025-09-23
 */

Ext.onReady(function() {

    // 获取内容面板
    var contentPanel = Ext.getCmp('main-content-panel');
    var modelHeigth = 30;
    
    // 查询条件
    var standardCodeField = Ext.create('Ext.form.field.Text', {
        fieldLabel: '查询条件',
        name: 'standardCode',
        labelWidth: 80,
        width: 270,
        emptyText: '---请输入查询关键字---'
    });
    
    // 查询按钮
    var queryButton = Ext.create('Ext.Button', {
        text: '查询',
        cls: 'Common_Btn',
        handler: function() {
            standardCodeGrid.getStore().load({
                params: {
                    queryString: standardCodeField.getValue(),
                    start: 0,
                    limit: 20
                }
            });
        }
    });

    // 重置按钮
    var resetButton = Ext.create('Ext.Button', {
        text: '重置',
        cls: 'Common_Btn',
        handler: function() {
            standardCodeField.setValue('');
            standardCodeGrid.getStore().load({
                params: {
                    queryString: '',
                    start: 0,
                    limit: 20
                }
            });
        }
    });

    // 新增按钮
    var addButton = Ext.create('Ext.Button', {
        text: '新增',
        cls: 'Common_Btn',
        handler: function() {
            addNewRecord();
        }
    });

    // 保存按钮
    var saveButton = Ext.create('Ext.Button', {
        text: '保存',
        cls: 'Common_Btn',
        handler: function() {
            saveChanges();
        }
    });

    // 删除按钮
    var deleteButton = Ext.create('Ext.Button', {
        text: '删除',
        cls: 'Common_Btn',
        handler: function() {
            var selection = standardCodeGrid.getSelectionModel().getSelection();
            if (selection.length === 0) {
                Ext.Msg.alert('提示', '请选择要删除的记录');
                return;
            }

            var message = selection.length === 1 ?
                '确定要删除选中的贯标吗？' :
                '确定要删除选中的 ' + selection.length + ' 条贯标吗？';

            Ext.Msg.confirm('确认', message, function(btn) {
                if (btn === 'yes') {
                    // 支持单条和多条删除
                    var ids = [];
                    Ext.Array.each(selection, function(record) {
                        var iid = record.get('iid');
                        if (iid && iid !== '') {
                            ids.push(iid);
                        }
                    });

                    if (ids.length > 0) {
                        deleteStandardCodes(ids.join(','));
                    } else {
                        Ext.Msg.alert('提示', '选中的记录中没有有效的ID');
                    }
                }
            });
        }
    });
    
    // 数据模型
    Ext.define('StandardCodeModel', {
        extend: 'Ext.data.Model',
        fields: [
            {name: 'iid', type: 'string'},
            {name: 'istandardcode', type: 'string'},
            {name: 'businessSystemName', type: 'string'},
            {name: 'icreatetime', type: 'string'},  // 改为string类型，就像datadateflop.js一样
            {name: 'icreateuser', type: 'string'}
        ]
    });
    
    // 数据存储
    var standardCodeStore = Ext.create('Ext.data.Store', {
        model: 'StandardCodeModel',
        pageSize: 20,
        proxy: {
            type: 'ajax',
            url: 'standardcode/getStandardCodeList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        },
        autoLoad: true
    });
    
    // 数据网格
    var standardCodeGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
        region: 'center',
        cls: 'customize_panel_back',
        ipageBaseCls: Ext.baseCSSPrefix + 'toolbar customize_toolbar',
        padding: grid_space,
        store: standardCodeStore,
        selModel: Ext.create('Ext.selection.CheckboxModel', {
            mode: 'MULTI'
        }),
        plugins: [
            Ext.create('Ext.grid.plugin.CellEditing', {
                clicksToEdit: 2,
                listeners: {
                    beforeedit: function(editor, context) {
                        // 可以在这里添加编辑前的逻辑
                        return true;
                    },
                    edit: function(editor, context) {
                        // 在编辑完成后进行验证
                        if (context.field === 'istandardcode') {
                            var value = context.value;
                            var regex = /^[a-zA-Z]+$/;
                            if (value && !regex.test(value)) {
                                Ext.Msg.alert('验证失败', '贯标代码只能包含英文字母');
                                context.record.set(context.field, context.originalValue);
                                return false;
                            }
                        } else if (context.field === 'businessSystemName') {
                            var value = context.value;
                            if (!value || value.trim() === '') {
                                Ext.Msg.alert('验证失败', '业务系统中文名不能为空');
                                context.record.set(context.field, context.originalValue);
                                return false;
                            }
                        }
                    }
                }
            })
        ],
        columns: [
            {
                xtype: 'rownumberer',
                width: 60,
                text: '序号'
            },
            {
                xtype: 'gridcolumn',
                hidden: true,
                dataIndex: 'iid',
                text: '主键'
            },
            {
                xtype: 'gridcolumn',
                text: '贯标代码',
                dataIndex: 'istandardcode',
                flex: 1,
                editor: {
                    xtype: 'textfield',
                    allowBlank: false,
                    blankText: '贯标代码不能为空',
                    regex: /^[a-zA-Z]+$/,
                    regexText: '贯标代码只能包含英文字母',
                    maxLength: 50,
                    emptyText: '请输入贯标代码',
                    maskRe: /[a-zA-Z]/,  // 只允许输入英文字母
                    listeners: {
                        change: function(field, newValue, oldValue) {
                            if (newValue && !/^[a-zA-Z]+$/.test(newValue)) {
                                field.markInvalid('贯标代码只能包含英文字母');
                            } else {
                                field.clearInvalid();
                            }
                        }
                    }
                }
            },
            {
                xtype: 'gridcolumn',
                text: '业务系统中文名',
                dataIndex: 'businessSystemName',
                flex: 1,
                editor: {
                    xtype: 'textfield',
                    allowBlank: false,
                    blankText: '业务系统中文名不能为空',
                    maxLength: 100,
                    emptyText: '请输入业务系统中文名'
                }
            },
            {
                xtype: 'gridcolumn',
                text: '创建时间',
                dataIndex: 'icreatetime',
                flex: 1,
                renderer: function(value) {
                    if (value && value !== '' && value !== null && value !== undefined) {
                        try {
                            // 处理ISO格式的时间字符串，如：2025-09-23T06:40:22.000+00:00
                            var date = new Date(value);
                            if (!isNaN(date.getTime())) {
                                // 格式化为：2025-09-17 09:56:12
                                return Ext.util.Format.date(date, 'Y-m-d H:i:s');
                            }
                        } catch (e) {
                            console.error('时间格式化错误:', e, value);
                        }
                        return value; // 如果格式化失败，返回原值
                    }
                    return '';
                }
            },
            {
                xtype: 'gridcolumn',
                text: '创建用户',
                dataIndex: 'icreateuser',
                flex: 1
            }
        ]
    });
    
    // 查询表单
    var queryForm = Ext.create('Ext.form.Panel', {
        region: 'north',
        border: false,
        bodyCls: 'x-docked-noborder-top',
        baseCls: 'customize_gray_back',
        layout: 'form',
        height: 40,
        dockedItems: [{
            xtype: 'toolbar',
            baseCls: 'customize_gray_back',
            dock: 'top',
            items: [
                standardCodeField,
                {xtype: 'tbspacer', width: 10},
                queryButton,
                {xtype: 'tbspacer', width: 5},
                resetButton,
                '->',
                addButton,
                {xtype: 'tbspacer', width: 5},
                saveButton,
                {xtype: 'tbspacer', width: 5},
                deleteButton
            ]
        }]
    });
    
    // 主面板
    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: 'standardcode_area',
        layout: 'border',
        height: contentPanel ? (contentPanel.getHeight() - modelHeigth) : 600,
        width: contentPanel ? contentPanel.getWidth() : 800,
        items: [queryForm, standardCodeGrid]
    });
    
    // 窗口大小调整
    if (contentPanel) {
        contentPanel.on('resize', function() {
            mainPanel.setWidth(contentPanel.getWidth());
            mainPanel.setHeight(contentPanel.getHeight() - modelHeigth);
        });

        // 页面销毁时清理
        contentPanel.getLoader().on("beforeload", function(obj, options, eOpts) {
            Ext.destroy(mainPanel);
            if (Ext.isIE) {
                CollectGarbage();
            }
        });
    }
    
    // 新增记录
    function addNewRecord() {
        var newRecord = Ext.create('StandardCodeModel', {
            iid: '',
            istandardcode: '',
            businessSystemName: '',
            icreatetime: '',
            icreateuser: '' // 后端会自动设置当前登录用户
        });

        standardCodeStore.insert(0, newRecord);

        // 添加后自动开始编辑贯标名称字段
        setTimeout(function() {
            try {
                // 贯标名称列索引，包含序号列和隐藏的主键列
                var cellEditing = standardCodeGrid.getPlugin('cellediting');
                if (cellEditing) {
                    cellEditing.startEdit(0, 2); // 第0行，第2列（贯标名称列）
                }
            } catch(e) {
                console.log(e);
            }
        }, 100);
    }

    // 保存修改（完全按照jobConfig模式）
    function saveChanges() {
        var m = standardCodeStore.getModifiedRecords();
        if (m.length < 1) {
            Ext.Msg.alert('提示', '您没有进行任何修改，无需保存');
            return;
        }

        // 验证修改的记录
        var regex = /^[a-zA-Z]+$/;
        var standardCodes = [];
        var businessNames = [];

        for (var i = 0; i < m.length; i++) {
            var record = m[i];
            var standardCode = record.get('istandardcode');
            var businessName = record.get('businessSystemName');

            // 检查贯标代码
            if (!standardCode || standardCode.trim() === '') {
                Ext.Msg.alert('验证失败', '贯标代码不能为空，请检查第' + (i + 1) + '条记录');
                return;
            }
            if (!regex.test(standardCode)) {
                Ext.Msg.alert('验证失败', '贯标代码只能包含英文字母，请检查第' + (i + 1) + '条记录');
                return;
            }

            // 检查业务系统中文名
            if (!businessName || businessName.trim() === '') {
                Ext.Msg.alert('验证失败', '业务系统中文名不能为空，请检查第' + (i + 1) + '条记录');
                return;
            }

            // 检查同一批次中的重复
            if (standardCodes.indexOf(standardCode.toUpperCase()) !== -1) {
                Ext.Msg.alert('验证失败', '贯标代码重复：' + standardCode + '，请检查第' + (i + 1) + '条记录');
                return;
            }
            standardCodes.push(standardCode.toUpperCase());

            if (businessNames.indexOf(businessName) !== -1) {
                Ext.Msg.alert('验证失败', '业务系统中文名重复：' + businessName + '，请检查第' + (i + 1) + '条记录');
                return;
            }
            businessNames.push(businessName);
        }
        var jsonData = "[";
        for (var i = 0, len = m.length; i < len; i++) {
            var ss = Ext.JSON.encode(m[i].data);
            if (i == 0)
                jsonData = jsonData + ss;
            else
                jsonData = jsonData + "," + ss;
        }
        jsonData = jsonData + "]";
        Ext.Ajax.request({
            url: 'standardcode/saveStandardCode.do',
            method: 'POST',
            params: {
                jsonData: jsonData
            },
            success: function(response, request) {
                var success = Ext.decode(response.responseText).success;
                var message = Ext.decode(response.responseText).message;
                if (success) {
                    standardCodeStore.reload();
                }
                Ext.Msg.alert('提示', message);
            },
            failure: function(result, request) {
                Ext.Msg.alert('错误', '保存失败！');
            }
        });
    }



    // 添加贯标
    function addStandardCode(standardCode, window, record) {
        Ext.Ajax.request({
            url: 'standardcode/addStandardCode.do',
            method: 'POST',
            params: {
                standardCode: standardCode
            },
            success: function(response) {
                var result = Ext.decode(response.responseText);
                if (result.success) {
                    Ext.Msg.alert('成功', result.message, function() {
                        if (window) {
                            window.close();
                        }
                        // 保存成功后直接重新加载数据，不需要手动更新记录
                        standardCodeGrid.getStore().reload();
                    });
                } else {
                    Ext.Msg.alert('失败', result.message);
                    if (record) {
                        // 如果保存失败，从store中移除这条记录
                        standardCodeStore.remove(record);
                    }
                }
            },
            failure: function() {
                Ext.Msg.alert('错误', '网络请求失败');
                if (record) {
                    // 如果请求失败，从store中移除这条记录
                    standardCodeStore.remove(record);
                }
            }
        });
    }

    // 更新贯标
    function updateStandardCode(iid, standardCode, window, record) {
        Ext.Ajax.request({
            url: 'standardcode/updateStandardCode.do',
            method: 'POST',
            params: {
                iid: iid,
                standardCode: standardCode
            },
            success: function(response) {
                var result = Ext.decode(response.responseText);
                if (result.success) {
                    Ext.Msg.alert('成功', result.message, function() {
                        if (window) {
                            window.close();
                        }
                        // 保存成功后直接重新加载数据
                        standardCodeGrid.getStore().reload();
                    });
                } else {
                    Ext.Msg.alert('失败', result.message);
                    if (record) {
                        record.reject(); // 恢复原始值
                    }
                }
            },
            failure: function() {
                Ext.Msg.alert('错误', '网络请求失败');
                if (record) {
                    record.reject(); // 恢复原始值
                }
            }
        });
    }

    // 删除贯标（使用jobConfig模式）
    function deleteStandardCodes(deleteIds) {
        Ext.Ajax.request({
            url: 'standardcode/deleteStandardCode.do',
            method: 'POST',
            params: {
                deleteIds: deleteIds
            },
            success: function(response, opts) {
                var success = Ext.decode(response.responseText).success;
                var message = Ext.decode(response.responseText).message;
                if (success) {
                    standardCodeGrid.getStore().reload();
                }
                Ext.Msg.alert('提示', message);
            },
            failure: function(result, request) {
                Ext.Msg.alert('错误', '删除失败！');
            }
        });
    }

    // 解决IE下trim问题
    String.prototype.trim = function() {
        return this.replace(/(^\s*)|(\s*$)/g, "");
    };

});
