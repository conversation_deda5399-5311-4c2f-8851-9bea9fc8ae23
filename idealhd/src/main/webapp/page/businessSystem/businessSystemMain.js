/*******************************************************************************
 * 业务系统维护
 ******************************************************************************/
var proInfoFrom=null;
var tongBuButton =null;
var businessSystemStore = null;
var businessSystem_mainPanel ="";
var bsPageBar="";
var proInfoMap= new Map();
function trim(str)
{
    return RTrim(LTrim(str));
}
Ext.onReady (function ()
{
	// 清理主面板的各种监听时间
	destroyRubbish();
	var gobleSysId = 0;// 全局业务系统id
	var gobleSysName = "";// 全局业务系统名称
	var opersystype=0; // 全局 系统 类型操作
	// 浦发名称要求与其它不同
	var systemName='业务系统名称';
	var systemNameCom=systemName;
	var win;
	if(pfFlag)
	{
		systemName='工程名称'
	}
	if(jobSchedulingQuerySystemSwitch)
	{
		systemName='工程名称';
		systemNameCom='工程名或业务系统名称';
	}
	
	 /** 周类型Model* */
	 Ext.define("commonModel", {
	       extend: "Ext.data.Model",
	       fields: [
	    	   {name: "id", type: "string"}, 
	    	   {name: "name", type: "string"}
    	   ]
	  });
	 
	/** *********************Model********************* */
	/** 系统名下拉框数据Model* */
	Ext.define ('businessSystemModel',
	{
	    extend : 'Ext.data.Model',
	    remoteSort : true,
	    fields : [
	            {
	                name : 'sysName',
	                type : 'string'
	            },
	            {
	                name : 'prjVersion',
	                type : 'string'
	            },
	            {
	                name : 'sysType',
	                type : 'string'
	            },
	            {
	                name : 'sysDesc',
	                type : 'string'
	            },
	            {
	                name : 'systemId',
	                type : 'long'
	            },
	            {
	                name : 'sysState',
	                type : 'int'
	            },
	            {
	                name : 'priority',
	                type : 'int'
	            },
	            {
	                name : 'prjType',
	                type : 'int'
	            },
	            {
	                name : 'sysParam',
	                type : 'string'
	            },
	            {
	            	name : 'sendCycle',
	            	type : 'string'
	            },
	            {
	            	name : 'mails',
	            	type : 'string'
	            },
	            {
	            	name : 'mailSendStatus',
	            	type : 'boolean'
	            },
	            {
	            	name : 'pkgid',
	            	type : 'int'
	            },
		    	// add systemCode by yue_sun 2017-06-20
	            {
	            	name : 'systemCode',
	            	type : 'String'
	            },
	        	// add systemNumber by qiang_li 2021-10-09
	            {
	            	name : 'systemNumber',
	            	type : 'String'
	            }
	            // begin.added by manxi_zhao.2017-05-02.2017-06-15迁移
	            ,{
	            	name : 'upid',
	            	type : 'long'
	            },
	            {
	            	name : 'freeze',
	            	type : 'int'
	            },
	            {
	            	name : 'runPrj',
	            	type : 'int'
	            },
	            {
	            	name : 'excelPrj',
	            	type : 'int'
	            },
	            {
	            	name : 'icooment',
	            	type : 'string'
	            },
	            {
	            	name : 'iuploadNumber',
	            	type : 'string'
	            },
	            {
	            	name : 'iuploadTime',
	            	type : 'string'
	            },
	            {
	            	name : 'iuploadUser',
	            	type : 'string'
	            },{
	            	name : 'ipmpSysName',
	            	type : 'string'
	            },{
	            	name : 'sysCode',
	            	type : 'string'
	            },{
	            	name : 'icansync',
	            	type : 'boolean'
	            },{
	            	name : 'developer',
	            	type : 'string'
	            },{
	            	name : 'department',
	            	type : 'string'
	            },{
	            	name : 'systemNumberCicd',
	            	type : 'string'
	            },{
	            	name : 'systemAbbreviation',
	            	type : 'string'
	            },{
	            	name : 'asyToUse',
	            	type : 'string'
	            },{
	            	name : 'systemStatus',
	            	type : 'string'
	            },{
	            	name : 'ifContinuousIntegration',
	            	type : 'string'
	            },{
	            	name : 'ifAutoDeploy',
	            	type : 'string'
	            },{
					name : 'isysbingagent',
					type : 'string'
				},{
					name : 'asytouseManager',
					type : 'string'
				},{
					name : 'asytodetOffice',
					type : 'string'
				},{
					name : 'systemOwnerTel',
					type : 'string'
				},{
					name : 'domainName',
					type : 'string'
				}
	            
	            // end.added by manxi_zhao.2017-05-02.
	    ]
	});
	/** 业务系统级别Model* */
	Ext.define ('syslvModel',
	{
	    extend : 'Ext.data.Model',
	    fields : [
	            {
	                name : 'applvlId',
	                type : 'long'
	            },
	            {
	                name : 'applvl',
	                type : 'string'
	            }
	    ]
	});
	/** *********************Store********************* */
	/** 系统名下拉框数据源* */
	var businessSystemNameStore = Ext.create ('Ext.data.Store',
	{
	    autoLoad : true,
	    autoDestroy : true,
	    model : 'businessSystemModel',
	    proxy :
	    {
	        type : 'ajax',
	        url : 'businessSystemNameList.do',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList'
	        }
	    }
	});

	businessSystemNameStore.on ('beforeload', function (store, options)
	{
		var prjTypeInit = 0;
		var appStringInit = true;
		if (urlType != '')
		{
			prjTypeInit = urlType;
			appStringInit = false;
		}
		if (null != prjTypeForQueryNew.getValue ())
		{
			prjTypeInit = prjTypeForQueryNew.getValue ();
		}
		var new_params =
		{
		    prjType : prjTypeInit,
		    appString : appStringInit
		};
		Ext.apply (businessSystemNameStore.proxy.extraParams, new_params);
	});
	/** 业务系统级别下拉框数据源* */
	var syslvStore = Ext.create ('Ext.data.Store',
	{
	    autoLoad : true,
	    autoDestroy : true,
	    model : 'syslvModel',
	    proxy :
	    {
	        type : 'ajax',
	        url : 'sysLvList.do',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList'
	        }
	    },
	    listeners:{
	          'load': function(store, records, options) {
	        	  store.insert(0, Ext.create('syslvModel',{applvlId:'',applvl:'无'}));
	          }
	     }
	});
	/** 业务系统列表数据源* */
	businessSystemStore = Ext.create ('Ext.data.Store',
	{
	    autoLoad : true,
	    autoDestroy : true,
	    remoteSort : true,
	    model : 'businessSystemModel',
	    pageSize : 30,
	    proxy :
	    {
	        type : 'ajax',
	        url : 'businessSystemList.do',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList',
	            totalProperty : 'total'
	        }
	    }
	});
	
	

	
	businessSystemStore.on ('beforeload', function (store, options)
	{
		var prjTypeInit = 0;
		if (null != prjTypeForQuery.getValue () && '' != prjTypeForQuery.getValue ())
		{
			prjTypeInit = prjTypeForQuery.getValue ();
		}
		if (fjnxSyncSystemSwitch)
		{
			prjTypeInit = 3;
		}
		var new_params =
		{
		    sysName : sysNameForQuery.getValue ().trim (),
		    prjType : prjTypeInit
		};
		Ext.apply (businessSystemStore.proxy.extraParams, new_params);
	});
	
	/** *********************组件********************* */
	/** 查询业务系统* */
	var queryButtonForBSM = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
	    text : '查询',
	    handler : queryWhere
	});
	/** 重置查询业务系统条件* */
	var resetButtonForBSM = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
	    text : '重置',
	    handler : resetWhere
	});
	
	/** 同步按钮* */
	if(isGDDownloadLocalPath){
		tongBuButton = Ext.create ("Ext.Button",
		{
		    cls : 'Common_Btn',
		    text : '同步',
		    handler : synchronButtion,
			hidden : fjnxSyncSystemSwitch
		});
	}
	
	
	/** 增加业务系统* */
	var addButtonForBSM = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
	    text : '增加',
	    hidden : jobSchedulingQuerySystemSwitch?true:false,
	    handler : onAddListener
	});
	/** 业务系统维护-历史信息查看功能 一体化4.7.9 孙悦 start * */
	/** 冻结业务系统* */
	var freezeButtonForIEAI = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
	    text : '冻结',
	    hidden:jobSchedulingQuerySystemSwitch?true:false|| fjnxSyncSystemSwitch,
	    handler : onFreezeListener
	});
	/** 解冻业务系统* */
	var unFreezeButtonForIEAI = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
	    text : '解冻',
	    hidden:jobSchedulingQuerySystemSwitch?true:false|| fjnxSyncSystemSwitch,
	    handler : onUnFreezeListener
	});
	/** 业务系统维护-历史信息查看功能 一体化4.7.9 孙悦 end * */
	/** 删除业务系统* */
	var deleteButtonForBSM = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
	    text : '删除',
	    hidden : jobSchedulingQuerySystemSwitch?true:false,
	    disabled : true,
	    handler : onDeleteListener
	});
	
	/** 导出工程* */
	var exportAllButton = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
	    text : '导出工程',
	    hidden : jobSchedulingQuerySystemSwitch?false:true,
	    handler : onExportListener
	});
	/** 保存业务系统* */
	var saveButtonForBSM = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
	    text : '保存',
	    hidden : jobSchedulingQuerySystemSwitch?true:false,
	    handler : onSaveListener
	});
	var promotion = Ext.create ("Ext.Button",
		{
			cls : 'Common_Btn',
			text : '晋级',
			hidden : jobSchedulingQuerySystemSwitch?true:false,
			handler : promotionFun
		});


	var syncButton = Ext.create ("Ext.Button",
		{
			cls : 'Common_Btn',
			text : '同步全部系统',
			hidden : !hsBankSyncSwitch,
			handler : syncAll
		});
	/** 设备变更窗口弹出按钮* */
	var changeButtonForBSM = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
	    text : '设备变更',
	    handler : function ()
	    {
		    onShowSysRelation (0, '', 0);
	    }
	});
	/** 导入导出按钮* */
	var importOrExportButtonForBSM = Ext.create("Ext.Button",{
            text: '导入/导出',
            cls:'Common_Btn',
            hidden : jobSchedulingQuerySystemSwitch?true:false|| fjnxSyncSystemSwitch,
            menu: {
                xtype: 'menu',
                plain: true,
                items: {
                    xtype: 'buttongroup',
                    columns: 2,
                    defaults: {
                        xtype: 'button'
                    },
                    items: [ {
						text : '导入',
						iconCls : 'server_import',
						cls:'Common_Btn',
						handler : openImportWindows_buSM
					},
					{
						text : '导出',
						iconCls : 'server_export',
						cls:'Common_Btn',
						handler : exportOperationSystem
					}]
                }
            }
	});
	/** 同步业务系统按钮* */
	var syncButtonForBSM = Ext.create("Ext.Button",{
		text:'同步',
		itemId : 'sync',
		cls : 'Common_Btn',
		hidden : jobSchedulingQuerySystemSwitch?true:false|| fjnxSyncSystemSwitch,
		handler:syncOperationSystem
	});
	var syncButtonForFJNX = Ext.create("Ext.Button",{
		text:'同步',
		cls : 'Common_Btn',
		hidden : !fjnxSyncSystemSwitch,
		handler:syncSystem
	});
	/** 工程类型下拉框* */
	var prjTypeForQuerystore = Ext.create('Ext.data.Store',{
		fields:['id','name'],
		data:[{"id":"0","name":"未选择"},{"id":"1","name":"作业调度"},{"id":"2","name":"信息采集"},{"id":"3","name":"应用变更"},{"id":"4","name":"灾备切换"},{"id":"5","name":"定时任务"},{"id":"6","name":"应急操作"},{"id":"7","name":"健康巡检"},{"id":"8","name":"日常操作"},{"id":"16","name":"应用维护"},{"id":"40","name":"AZ切换"}]
	});
	var prjTypeForQuery = Ext.create ('Ext.form.field.ComboBox',
	{
	    margin : '5',
	    labelWidth : 80,
	    width : 150,
	    queryMode : 'local',
	    // padding : '0 10 0 0',
	    editable : false,
	    emptyText : '--请选择工程类型--',
	    store : prjTypeForQuerystore,
	    displayField: 'name',
	    valueField: 'id',
	    hidden: fjnxSyncSystemSwitch,
	    listConfig :
	    {
		    minHeight : 80
	    },listeners:{
	    	select:function(){
	    		businessSystemStore.load();
	    		bsPageBar.moveFirst ();
	    	}
	    }
	});
	/** 业务系统名查询输入框* */
	var sysNameForQuery = Ext.create ('Ext.form.TextField',
	{
	    margin : '5',
	    // fieldLabel : '业务系统名称',
	    emptyText : '--请输入'+systemNameCom+'--',
	    labelWidth : 80,
	    width : 330,
	    xtype : 'textfield',
	    listeners: {  
			specialkey: function(field,e){
				if (e.getKey()==Ext.EventObject.ENTER){
					bsPageBar.moveFirst ();
				}  
			}  
		}
	});
	
// /** 非sa用户不可增加删除业务系统* */
// if ('sa' != loginName)
// {
// addButtonForBSM.hide ();
// deleteButtonForBSM.hide ();
// }
	/** 设备变更窗口>业务系统名称下拉框* */
	var sysNameForQueryRelation = Ext.create ('Ext.form.field.ComboBox',
	{
	    labelWidth : 80,
	    width : 600,
	    // fieldLabel : '业务系统名称',
	    emptyText : '--请选择业务系统--',
	    queryMode : 'local',
	    padding : '0 10 0 0',
	    store : businessSystemNameStore,
	    displayField : 'sysName',
	    valueField : 'systemId',
	    listConfig :
	    {
		    maxHeight : 280
	    },
	    listeners :
	    {
		    select : function ()
		    {
			    gobleSysId = this.getValue ();
			    gobleSysName = this.getRawValue ();
			    tabPanelForBSM.getActiveTab ().loader.load (
			    {
				    params :
				    {
					    sysIdForQuery : gobleSysId
				    }
			    });
			    win.setTitle ("设备变更-业务系统名称：" + gobleSysName);
		    }
	    }
	});
	/** 设备变更窗口>工程类型下拉框* */
	var prjTypeForQueryNew = Ext.create ('Ext.form.field.ComboBox',
	{
	    
	    labelWidth : 80,
	    emptyText : '--请选择工程类型--',
	    queryMode : 'local',
	    padding : '0 10 0 0',
	    editable : false,
	    store : [
	            [
	                    0, '--未选择--'
	            ], [
	                    4, '灾备切换'
	            ], [
	                    5, '健康巡检'
	            ]
	    ],
	    listConfig :
	    {
		    minHeight : 80
	    },
	    listeners :
	    {
		    select : function ()
		    {
			    sysNameForQueryRelation.clearValue ();
			    sysNameForQueryRelation.applyEmptyText ();
			    sysNameForQueryRelation.getPicker ().getSelectionModel ().doMultiSelect ([], false);
			    businessSystemNameStore.load ();
		    }
	    }
	});
	/** 业务系统列表分页工具栏* */
 bsPageBar = Ext.create ('Ext.PagingToolbar',
	{
	    store : businessSystemStore,
	    dock: 'bottom',
        baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        displayInfo: true,
        hidden:jobSchedulingQuerySystemSwitch?true:false,
        border:false
	});
	var cellEditing = Ext.create ('Ext.grid.plugin.CellEditing',
	{
		clicksToEdit : 2
	});
	
	var bussSysUserGrant = {
	    	text : '用户赋权',
			xtype : 'actioncolumn',
			width : 100,
			 align : 'left',
			sortable : false,
			hidden:user_sys,
			menuDisabled : true,
			items : [ {
				iconCls:'role_permission',
				tooltip : '用户赋权',
				handler : function (a,b,c,d,e,record) {
					onShowUserRelation (record.get ('systemId'), record.get ('sysName'), record.get ('prjType'));
				}
			} ]
		}
		var bussSysUserGrantNew = {
	    	text : '用户赋权',
			xtype : 'actioncolumn',
			width : 100,
			align : 'left',
			sortable : false,
			menuDisabled : true,
			items : [ {
				iconCls:'role_permission',
				tooltip : '用户赋权',
				handler : function (a,b,c,d,e,record) {
					onShowUserRelationNew (record.get ('systemId'), record.get ('sysName'), record.get ('prjType'));
				}
			} ]
		}
		
	if(jobSchedulingQuerySystemSwitch)
	{
	bussSysUserGrant.hidden=true;
	}
	
	if(fjnxSyncSystemSwitch){
		bussSysUserGrant.hidden=true;
	}else{
		bussSysUserGrantNew.hidden=true;
	}
	
	var systemStatusComboBox = Ext.create('Ext.form.field.ComboBox', { 
		editable : false,
	    store: { 
		    　　fields: ['key','value'], 
		    　　data: [ 
		          　　  	{key : '01', value: '规划'}, 
		        {key : '02', value: '在建'},
				{key : '03', value: '运行'},
				{key : '04', value: '改造'},
				{key : '05', value: '下线'},
				{key : '06', value: '停用'}
			] 
	    } ,
	    displayField : 'value',
		valueField : 'key'
	});
	
	var ifContinuousIntegrationComboBox = Ext.create('Ext.form.field.ComboBox', { 
		editable : false,
	    store: { 
		    　　fields: ['key','value'], 
		    　　data: [ 
		          　　  	{key : '1', value: '是'}, 
		        {key : '0', value: '否'}
			] 
	    } ,
	    displayField : 'value',
		valueField : 'key'
	});
	
	var ifAutoDeployComboBox = Ext.create('Ext.form.field.ComboBox', { 
		editable : false,
	    store: { 
		    　　fields: ['key','value'], 
		    　　data: [ 
		          　　  	{key : '1', value: '是'}, 
		        {key : '0', value: '否'}
			] 
	    } ,
	    displayField : 'value',
		valueField : 'key'
	});
	
	/** 业务系统列表Columns* */
	var gridColumns = null;
	if(jobSchedulingQuerySystemSwitch)
	{
		gridColumns = [
		   	        {
		   	            text : '序号',
		   	            width : 45,
		   	            align : 'left',
		   	            xtype : 'rownumberer'
		   	        },
// {
// text : 'systemId',
// dataIndex : 'systemId',
// width : 100
// },
// {
// text : 'upperId',
// dataIndex : 'upid',
// width : 100
// },
		   	        {
		   	            text : systemName,
		   	            sortable : true,
		   	            dataIndex : 'sysName',
		   	            flex : 2,
		   	            editor :
		   	            {
		   		            allowBlank : false
		   	            },
		   	            renderer : projectOpen
		   	        }, {
		   	            text : '说明',
		   	            dataIndex : 'icooment',
		   	            hidden : jobSchedulingQuerySystemSwitch?false:true,
		   	            flex : 2
		   	        }, {
		   	            text : '上载次数',
		   	            dataIndex : 'iuploadNumber',
		   	            align : 'left',
		   	            hidden : jobSchedulingQuerySystemSwitch?false:true,
		   	            width: 100,
		   	        }, {
		   	            text : '上载时间',
		   	            dataIndex : 'iuploadTime',
		   	            width: 150,
		   	            hidden : jobSchedulingQuerySystemSwitch?false:true,
		   	            renderer: Ext.util.Format.dateRenderer('Y-m-d H:i:s')
		   	        }, {
		   	            text : '上载者',
		   	            dataIndex : 'iuploadUser',
		   	            hidden : jobSchedulingQuerySystemSwitch?false:true,
		   	            flex : 1
		   	        },
		   	        {
		   	            text : '系统名称', // 下载作业调度的PKG专用
		   	            sortable : true,
		   	            dataIndex : 'prjName',
		   	            flex : 2,
		   	            hidden : true,
		   	            editor :
		   	            {
		   		            allowBlank : false
		   	            }
		   	        },{
		   	            text : '工程版本', // 下载作业调度的PKG专用
		   	            sortable : true,
		   	            dataIndex : 'prjVersion',
// flex : 2,
		   	            width: 100,
		   	            align : 'left',
		   	            hidden : jobSchedulingQuerySystemSwitch?false:true,
		   	            editor :
		   	            {
		   		            allowBlank : false
		   	            }
		   	        },bussSysUserGrant,
		   	        {
		   	            text : '工程类型',
		   	            sortable : true,
		   	            dataIndex : 'prjType',
		   	            width: 140,
		   	            hidden : jobSchedulingQuerySystemSwitch?true:false,
		   	            renderer : function (value, metaData, record)
		   	            {
		   	            return 	getProType(value);
		   		            
		   	            }
		   	        },
		   	        {
		   	            text : '系统类别',
		   	            sortable : true,
		   	            dataIndex : 'sysType',
		   	            hidden:true,
		   	            hideable: false,
		   	            // renderer : function (value, metaData, record)
		   	            // {
		   	            // var returnValue = value;
		   	            // for (var i = 0; i < syslvStore.count (); i++)
		   	            // {
		   	            // if (syslvStore.getAt (i).get ("applvlId") == value)
		   	            // {
		   	            // returnValue = syslvStore.getAt (i).get ("applvl");
		   	            // break;
		   	            // }
		   	            //			                        
		   	            // }
		   	            // return returnValue;
		   	            // },
		   	            editor : new Ext.form.field.ComboBox (
		   	            {
		   	                triggerAction : 'all',// 用all表示把下拉框列表框的列表值全部显示出来
		   	                editable : false,// 是否可输入编辑
		   	                store : syslvStore,
		   	                queryMode : 'local',
		   	                displayField : 'applvl',
		   	                valueField : 'applvl'
		   	            })
		   	        },
		   	        {
		   	            text : '优先级',
		   	            sortable : true,
		   	            dataIndex : 'priority',
		   	            hidden:true,
		   	            hideable: false,
		   	            width : 50,
		   	            editor : new Ext.form.field.ComboBox (
		   	            {
		   	            	width : 30,
		   	                triggerAction : 'all',// 用all表示把下拉框列表框的列表值全部显示出来
		   	                editable : false,// 是否可输入编辑
		   	                store : [
		   	                        [
		   	                                1, 1
		   	                        ], [
		   	                                2, 2
		   	                        ], [
		   	                                3, 3
		   	                        ], [
		   	                                4, 4
		   	                        ], [
		   	                                5, 5
		   	                        ]
		   	                ]
		   	            })
		   	        },
		   	        {
		   	        	text : '发送时间',
		   	        	sortable : true,
		   	        	dataIndex : 'sendCycle',
		   	        	hidden:true,
		   	        	width: 80,
		   	        	hideable: false,
		   	        	editor : 
		   	            {
		   	            	allowBlank : true,
		   	            	maxLength : 120,
		   	            	regex: /^((([01]?[0-9])|(2[0-3])):[0-5]?[0-9],?)+$/ ,
		   	            	regexText : '时间格式：0:00 ~ 23:59 ，多个时间点请以逗号分隔。'
		   	            }
		   	        },
		   	        {
		   	        	text : '邮件地址',
		   	        	dataIndex : 'mails',
		   	        	hidden:true,
		   	        	width: 200,
		   	        	hideable: false,
		   	        	editor : 
		   	            {
		   	            	allowBlank : true,
		   	            	maxLength : 200,
		   	            	regex: /^((([a-z0-9_\.-]+)@([\da-z\.-]+)\.([a-z\.]{2,6}\;))*(([a-z0-9_\.-]+)@([\da-z\.-]+)\.([a-z\.]{2,6})))$/ ,
		   	            	regexText : '邮件格式：<EMAIL>，多个邮件地址请以分号分隔。'
		   	            }
		   	        },
		   	        {
		   	            text : '描述',
		   	            dataIndex : 'sysDesc',
		   	            hidden:true,
		   	            hideable: false,
		   	            width: 200,
		   	            editor :
		   	            {
		   		            allowBlank : true
		   	            }
		   	        },
		   	        {
		   	        	xtype : 'checkcolumn',
		   	        	text : '开启发送',
		   	        	dataIndex : 'mailSendStatus',
		   	        	hidden:true,
		   	        	hideable: false,
		   	        	width: 60
		   	        },
		   	        {
		   	            text : '系统参数',
		   	            sortable : true,
		   	            dataIndex : 'sysParam',
		   	            hidden:true,
		   	            hideable: false, 
		   	            flex : 1,
		   	            editor :
		   	            {
		   		            allowBlank : true
		   	            }
		   	        },
		   	        {
		   	        	sortable : false,
		   	            text : '设备',
		   	            dataIndex : 'systemId',
		   	            hideable: false,
		   	            hidden : jobSchedulingQuerySystemSwitch?true:false,
		   	            width : 100,
		   	            align : 'left',
		   	            flex:1,
		   	            renderer : function (value, metaData, record)
		   	            {
		   	            	if(sysBindingComputerFlag==1)
		               		{
		               		if((record.get('prjType')=='7'||record.get('prjType')=='5'||record.get('prjType')=='3'||record.get('prjType')=='4') && record.get('pkgid')==0){
		   	            		return "<a href='javascript:void(0)'><img src='images/monitor_bg.png' class='equipment'></a>";
		   	            	}
		               		}else
		               			{
		               			if((record.get('prjType')=='7'||record.get('prjType')=='5') && record.get('pkgid')==0){
		       	            		return "<a href='javascript:void(0)'><img src='images/monitor_bg.png' class='equipment'></a>";
		       	            	}
		               			}
		   	            },
		   	            listeners :
		   	            {
		   		            click : function (a, b, c, d, e, record)
		   		            {
		   		            	if(sysBindingComputerFlag==1)
		   	            		{
		   	            		if((record.get('prjType')=='7'||record.get('prjType')=='5'||record.get('prjType')=='3'||record.get('prjType')=='4') && record.get('pkgid')==0){
		   		            		onShowSysRelation (record.get ('systemId'), record.get ('sysName'), record.get ('prjType'));
		   		            	}
		   	            		}
		   	            	else
		   	            		{
		   	            		if((record.get('prjType')=='7'||record.get('prjType')=='5') && record.get('pkgid')==0){
		   		            		onShowSysRelation (record.get ('systemId'), record.get ('sysName'), record.get ('prjType'));
		   		            	}
		   	            		}
		   		            }
		   	            }
		   	        },
		   	        {
		   	            text : pfFlag?'系统简称':'系统编码',
		   	            dataIndex : 'systemCode',
		   	            sorttable : true,
		   	            align : 'left',
		   	            hidden : jobSchedulingQuerySystemSwitch?true:false,
		   	            flex : 2,
		   	            editor : {
		   					allowBlank : false
		   				}
		   	        },
		   	        {
		   	            text :'系统编码',
		   	            dataIndex : 'systemNumber',
		   	            sorttable : true,
		   	            align : 'left',
		   	            hidden : cqnsFlag?false:true,
		   	            flex : 2,
		   	            editor : {
		   					allowBlank : true
		   				}
		   	        },
		   	        {
		   	            text : '工程下是否有运行工作流',
		   	            sortable : true,
		   	            dataIndex : 'runPrj',
		   	            hidden : true,
		   	            width: 200
		   	        },
		   	        {
		   	            text : '开发人员',
		   	            dataIndex : 'developer',
						sortable : true,
		   	            hidden:!opmBusinessSystemDevPartSwitch,
		   	            width: 200,
		       	            editor : {
		       					allowBlank : false
		       				}
		   	        },
		   	        {
		   	            text : '所属部门',
		   	            dataIndex : 'department',
						sortable : true,
		   	            hidden:!opmBusinessSystemDevPartSwitch,
		   	            width: 200,
		       	            editor : {
		       					allowBlank : false
		       				}
		   	        },
		   	        {
		   	            text : '是否excel上传工程',
		   	            sortable : true,
		   	            dataIndex : 'excelPrj',
		   	            hidden : true,
		   	            width: 200
		   	        },
		   	        {
		   	        	sortable : false,
		   	            text : '属性',
		   	            dataIndex : 'systemId',
		   	            width : 100,
		   	            align : 'left',
		   	            hideable: false,
		   	            hidden : jobSchedulingQuerySystemSwitch?true:false,
		   	            flex:1,
		   	            renderer : function (value, metaData, record)
		   	            {
		   	            	/*
							 * if(record.get('prjType')=='7' &&
							 * record.get('pkgid')==0){ return "<a
							 * href='javascript:void(0)'><img
							 * src='images/monitor_bg.png' class='property'></a>"; }
							 */
	   	            		return "<a href='javascript:void(0)'><img  src='images/monitor_bg.png' class='property'></a>";

		   	            },
		   	            listeners :
		   	            {
		   		            click : function (a, b, c, d, e, record)
		   		            {
		   		            	/*
								 * if(record.get('prjType')=='7' &&
								 * record.get('pkgid')==0){ onShowProInfo
								 * (record.get ('systemId'), record.get
								 * ('sysName'), record.get ('prjType')); }
								 */
	   		            		onShowProInfo (record.get ('systemId'), record.get ('sysName'), record.get ('prjType'));
		   		            }
		   	            }
		   	        },{
						sortable: false,
						text:'系统类别描述',
						width: 120,
						hidden: fjnxSyncSystemSwitch&&(jobSchedulingQuerySystemSwitch?true:false),
						renderer: function(value, metaData, record) {
							proInfoMap=businessSystemStore.getProxy().getReader().rawData.proInfoMap;
							// 获取当前行的ID
							// let id = record.get('projectid'); // 假设ID字段在数据中为'id'
							let id = record.data.systemId; // 假设ID字段在数据中为'id'
							// 从dataMap中获取对应的描述信息
							let description = proInfoMap[id];
							console.log(description);
							// 返回描述信息，用于显示在表格中
							return description || ''; // 如果没有找到对应的描述信息，返回空字符串或者其他默认值
						}
					},{
		   	            text : '状态',
		   	            sortable : true,
		   	            dataIndex : 'freeze',
		   	            align : 'left',
		   	            width: 100,
		   	            renderer : function (value, metaData, record)
		   	            {
		   	            	var msg="";
		   	        		if( value == 1 ) {
		   	        			msg ="冻结";
		   	        		} else {
		   	        			msg = "正常";
		   	        		}
		   	        		return msg;
		   	            }
		   	        },
		   	        {
		   	            text : '下载',
		   	            sortable : true,
		   	            align : 'left',
		   	            width: 100,
		   	            dataIndex : 'prjDownload',
		   	            renderer : function(value, p, record, rowIndex) {
		   					// var systemId = record.get ('systemId');
		   					// var prjName = record.get ('prjName');
		   					if(record.get('prjType') == 1){
		   						return '<a href="javascript:void(0)"><img src="images/download.gif" align="absmiddle"></img></a>';
		   					}
		   				},
		   	            listeners :
		   	            {
		   		            click : function (a, b, c, d, e, record)
		   		            {
		   		            	if(record.get ('prjType') == 1){
		   		            		exporIEAIPrjPKG(record.get ('systemId'), record.get ('sysName'), record.get('prjVersion'), record.get('excelPrj'));
		   		            	}
		   		            }
		   	            }
		   	        },{
					  	  	text: '自动纳管',
							xtype : 'checkcolumn',
							align : 'left',
							dataIndex : 'icansync',
							width : 150,
							hidden:!opmBusinessCansyncSHSwitch,
							editor: {allowBlank : true}
					      }
		   	];
	}else
		{
		gridColumns = [
		       	        {
		       	            text : '序号',
		       	            width : 35,
		       	            xtype : 'rownumberer'
		       	        },
// {
// text : 'systemId',
// dataIndex : 'systemId',
// width : 100
// },
// {
// text : 'upperId',
// dataIndex : 'upid',
// width : 100
// },
		       	        {
		       	            text : systemName,
		       	            sortable : true,
		       	            dataIndex : 'sysName',
		       	            flex : 2,
		       	            editor :
		       	            {
		       		            allowBlank : false
		       	            },
		       	            renderer : projectOpen
		       	        },{
		       	            text : '业务系统(IPMP)',
		       	            sortable : true,
		       	            dataIndex : 'ipmpSysName',
		       	            flex : 2,
		       	            hidden : !pfIpmpSystemSwitch,
		       	            editor :
		       	            {
		       		            allowBlank : false
		       	            },
		       	            renderer : function ipmpSystemOpen(value, metaData, record) {
							       	 		if(record.get('prjType') == 16 || record.get('prjType') == 40){
							       	 			return value;
							       	 		} else {
							       	 			return '';
							       	 		}
					       	 			} 
		       	        },
		       	        {
		       	            text : '系统名称', // 下载作业调度的PKG专用
		       	            sortable : true,
		       	            dataIndex : 'prjName',
		       	            flex : 2,
		       	            hidden : true,
		       	            editor :
		       	            {
		       		            allowBlank : false
		       	            }
		       	        },{
		       	            text : '工程版本', // 下载作业调度的PKG专用
		       	            sortable : true,
		       	            dataIndex : 'prjVersion',
		       	            flex : 2,
		       	            hidden : true,
		       	            editor :
		       	            {
		       		            allowBlank : false
		       	            }
		       	        },bussSysUserGrant,bussSysUserGrantNew,
		       	        {
		       	            text : '工程类型',
		       	            sortable : true,
		       	            dataIndex : 'prjType',
		       	            hidden: fjnxSyncSystemSwitch,
		       	            width: 200,
		       	            renderer : function (value, metaData, record)
		       	            {
		       	            return 	getProType(value);
		       		            
		       	            }
		       	        },
		       	        {
		       	            text : '系统类别',
		       	            sortable : true,
		       	            dataIndex : 'sysType',
		       	            hidden:true,
		       	            hideable: false,
		       	            // renderer : function (value, metaData, record)
		       	            // {
		       	            // var returnValue = value;
		       	            // for (var i = 0; i < syslvStore.count (); i++)
		       	            // {
		       	            // if (syslvStore.getAt (i).get ("applvlId") ==
							// value)
		       	            // {
		       	            // returnValue = syslvStore.getAt (i).get
							// ("applvl");
		       	            // break;
		       	            // }
		       	            //			                        
		       	            // }
		       	            // return returnValue;
		       	            // },
		       	            editor : new Ext.form.field.ComboBox (
		       	            {
		       	                triggerAction : 'all',// 用all表示把下拉框列表框的列表值全部显示出来
		       	                editable : false,// 是否可输入编辑
		       	                store : syslvStore,
		       	                queryMode : 'local',
		       	                displayField : 'applvl',
		       	                valueField : 'applvl'
		       	            })
		       	        },
		       	        {
		       	            text : '优先级',
		       	            sortable : true,
		       	            dataIndex : 'priority',
		       	            hidden:true,
		       	            hideable: false,
		       	            width : 50,
		       	            editor : new Ext.form.field.ComboBox (
		       	            {
		       	            	width : 30,
		       	                triggerAction : 'all',// 用all表示把下拉框列表框的列表值全部显示出来
		       	                editable : false,// 是否可输入编辑
		       	                store : [
		       	                        [
		       	                                1, 1
		       	                        ], [
		       	                                2, 2
		       	                        ], [
		       	                                3, 3
		       	                        ], [
		       	                                4, 4
		       	                        ], [
		       	                                5, 5
		       	                        ]
		       	                ]
		       	            })
		       	        },
		       	        {
		       	        	text : '发送时间',
		       	        	sortable : true,
		       	        	dataIndex : 'sendCycle',
		       	        	hidden:true,
		       	        	width: 80,
		       	        	hideable: false,
		       	        	editor : 
		       	            {
		       	            	allowBlank : true,
		       	            	maxLength : 120,
		       	            	regex: /^((([01]?[0-9])|(2[0-3])):[0-5]?[0-9],?)+$/ ,
		       	            	regexText : '时间格式：0:00 ~ 23:59 ，多个时间点请以逗号分隔。'
		       	            }
		       	        },
		       	        {
		       	        	text : '邮件地址',
		       	        	dataIndex : 'mails',
		       	        	hidden:true,
		       	        	width: 200,
		       	        	hideable: false,
		       	        	editor : 
		       	            {
		       	            	allowBlank : true,
		       	            	maxLength : 200,
		       	            	regex: /^((([a-z0-9_\.-]+)@([\da-z\.-]+)\.([a-z\.]{2,6}\;))*(([a-z0-9_\.-]+)@([\da-z\.-]+)\.([a-z\.]{2,6})))$/ ,
		       	            	regexText : '邮件格式：<EMAIL>，多个邮件地址请以分号分隔。'
		       	            }
		       	        },
		       	        {
		       	            text : '描述',
		       	            dataIndex : 'sysDesc',
		       	            hidden:true,
		       	            hideable: false,
		       	            width: 200,
		       	            editor :
		       	            {
		       		            allowBlank : true
		       	            }
		       	        },
		       	        {
		       	        	xtype : 'checkcolumn',
		       	        	text : '开启发送',
		       	        	dataIndex : 'mailSendStatus',
		       	        	hidden:true,
		       	        	hideable: false,
		       	        	width: 60
		       	        },
		       	        {
		       	            text : '系统参数',
		       	            sortable : true,
		       	            dataIndex : 'sysParam',
		       	            hidden:true,
		       	            hideable: false, 
		       	            flex : 1,
		       	            editor :
		       	            {
		       		            allowBlank : true
		       	            }
		       	        },
		       	        {
		       	        	sortable : false,
		       	            text : '设备',
		       	            dataIndex : 'systemId',
		       	            hidden: fjnxSyncSystemSwitch,
		       	            width : 100,
		       	            align : 'left',
		       	            flex:1,
		       	            renderer : function (value, metaData, record)
		       	            {
		       	            	if(sysBindingComputerFlag==1||sysBindingComputerFlagTwo==true)
		                   		{
		                   		if((record.get('prjType')=='7'||record.get('prjType')=='5'||record.get('prjType')=='3'||record.get('prjType')=='16'||record.get('prjType')=='4'||record.get('prjType')=='40') && record.get('pkgid')==0){
		       	            		return "<a href='javascript:void(0)'><img src='images/monitor_bg.png' class='equipment'></a>";
		       	            	}
		                   		}else
		                   			{
		                   			if((record.get('prjType')=='7'||record.get('prjType')=='5') && record.get('pkgid')==0){
		           	            		return "<a href='javascript:void(0)'><img src='images/monitor_bg.png' class='equipment'></a>";
		           	            	}
		                   			}
		       	            },
		       	            listeners :
		       	            {
		       		            click : function (a, b, c, d, e, record)
		       		            {
		       		            	if(sysBindingComputerFlag==1||sysBindingComputerFlagTwo==true)
		       	            		{
		       	            		if((record.get('prjType')=='7'||record.get('prjType')=='5'||record.get('prjType')=='3'||record.get('prjType')=='16'||record.get('prjType')=='4'||record.get('prjType')=='40') && record.get('pkgid')==0){
		       		            		onShowSysRelation (record.get ('systemId'), record.get ('sysName'), record.get ('prjType'));
		       		            	}
		       	            		}
		       	            	else
		       	            		{
		       	            		if((record.get('prjType')=='7'||record.get('prjType')=='5') && record.get('pkgid')==0){
		       		            		onShowSysRelation (record.get ('systemId'), record.get ('sysName'), record.get ('prjType'));
		       		            	}
		       	            		}
		       		            }
		       	            }
		       	        },
		       	        {
		       	            text : pfFlag?'系统简称':'系统编码',
		       	            dataIndex : 'systemCode',
		       	            hidden: fjnxSyncSystemSwitch,
		       	            sorttable : true,
		       	            align : 'left',
		       	            flex : 2,
		       	            editor : {
		       					allowBlank : false
		       				}
		       	        },
		       	        {
			   	            text :'系统编码',
			   	            dataIndex : 'systemNumber',
			   	            sorttable : true,
			   	            align : 'left',
			   	            hidden : cqnsFlag?false:true,
			   	            flex : 2,
			   	            editor : {
			   					allowBlank : true
			   				}
			   	        },
		       	        {
		       	            text : '工程下是否有运行工作流',
		       	            sortable : true,
		       	            dataIndex : 'runPrj',
		       	            hidden : true,
		       	            width: 200
		       	        },
		   	        {
		   	            text : '开发人员',
		   	            dataIndex : 'developer',
		   	            hidden:!opmBusinessSystemDevPartSwitch || fjnxSyncSystemSwitch,
		   	            sortable : true,
		   	            width: 200,
		       	            editor : {
		       					allowBlank : false
		       				}
		   	        },
		   	        {
		   	            text : '所属部门',
		   	            dataIndex : 'department',
		   	            hidden:(!opmBusinessSystemDevPartSwitch || fjnxSyncSystemSwitch)&& !hsBankSwitch,
						sortable : true,
		   	            width: 200,
		       	            editor : {
		       					allowBlank : false
		       				}
		   	        },
		       	        {
		       	            text : '是否excel上传工程',
		       	            sortable : true,
		       	            dataIndex : 'excelPrj',
		       	            hidden : true,
		       	            width: 200
		       	        },
		       	        {
		       	        	sortable : false,
		       	            text : '属性',
		       	            dataIndex : 'systemId',
		       	            hidden: fjnxSyncSystemSwitch,
		       	            width : 100,
		       	            align : 'left',
		       	            hideable: false,
		       	            flex:1,
		       	            renderer : function (value, metaData, record)
		       	            {
		       	            	/*
								 * if(record.get('prjType')=='7' &&
								 * record.get('pkgid')==0){ return "<a
								 * href='javascript:void(0)'><img
								 * src='images/monitor_bg.png' class='property'></a>"; }
								 */
	       	            		return "<a href='javascript:void(0)'><img  src='images/monitor_bg.png' class='property'></a>";

		       	            },
		       	            listeners :
		       	            {
		       		            click : function (a, b, c, d, e, record)
		       		            {
		       		            	/*
									 * if(record.get('prjType')=='7' &&
									 * record.get('pkgid')==0){ onShowProInfo
									 * (record.get ('systemId'), record.get
									 * ('sysName'), record.get ('prjType')); }
									 */
	       		            		onShowProInfo (record.get ('systemId'), record.get ('sysName'), record.get ('prjType'));

								}
					}
			},{
				sortable: false,
				text:'系统类别描述',
				width: 120,
				hidden: fjnxSyncSystemSwitch,
				renderer: function(value, metaData, record) {
					proInfoMap=businessSystemStore.getProxy().getReader().rawData.proInfoMap;
					// 获取当前行的ID
					// let id = record.get('projectid'); // 假设ID字段在数据中为'id'
					let id = record.data.systemId; // 假设ID字段在数据中为'id'
					// 从dataMap中获取对应的描述信息
					let description = proInfoMap[id];
					console.log(description);
					// 返回描述信息，用于显示在表格中
					return description || ''; // 如果没有找到对应的描述信息，返回空字符串或者其他默认值
				}
			},{
				sortable : false,
				text : '巡检模板',
				dataIndex : 'systemId',
				hideable: false,
				hidden : !isAutoCloneAndStartSwitch || fjnxSyncSystemSwitch,// 此处加开关
				width : 80,
				align : 'left',
				renderer : function (value, metaData, record)
				{
					if((record.get('prjType')=='7') && record.get('pkgid')==0){
						return "<a href='javascript:void(0)'><img src='images/monitor_bg.png' class='equipment'></a>";
					}
				},
				listeners :
					{
						click : function (a, b, c, d, e, record)
						{
							if((record.get('prjType')=='7') && record.get('pkgid')==0){
								onShowSysHcTemp (record.get ('systemId'), record.get ('sysName'), record.get ('prjType'));
							}
						}
					}
			},{
				text : '状态',
				sortable : true,
				dataIndex : 'freeze',
				hidden: fjnxSyncSystemSwitch,
				renderer : function (value, metaData, record)
				{
					var msg="";
					if( value == 1 ) {
						msg ="冻结";
					} else {
						msg = "正常";
					}
					return msg;
				}
			},
		       	        {
		       	            text : '下载',
		       	            sortable : true,
		       	            hidden: fjnxSyncSystemSwitch,
		       	            dataIndex : 'prjDownload',
		       	            renderer : function(value, p, record, rowIndex) {
		       					// var systemId = record.get ('systemId');
		       					// var prjName = record.get ('prjName');
		       					if(record.get('prjType') == 1){
		       						return '<a href="javascript:void(0)"><img src="images/download.gif" align="absmiddle"></img></a>';
		       					}
		       				},
		       	            listeners :
		       	            {
		       		            click : function (a, b, c, d, e, record)
		       		            {
		       		            	if(record.get ('prjType') == 1){
		       		            		exporIEAIPrjPKG(record.get ('systemId'), record.get ('sysName'), record.get('prjVersion'), record.get('excelPrj'));
		       		            	}
		       		            }
		       	            }
		       	        },{
		       	        	
		       	        	text : 'sysCode',
		       	            sortable : true,
		       	            dataIndex : '系统编号(IPMP)',
		       	            hideable: true,
		       	            flex : 1,
		       	            hidden: fjnxSyncSystemSwitch,
		       	        },{
					  	  	text: '自动纳管',
							xtype : 'checkcolumn',
							align : 'left',
							dataIndex : 'icansync',
							width : 150,
							hidden:!opmBusinessCansyncSHSwitch,
							editor: {allowBlank : true}
					      },{
		       	        	
		       	        	text : systemCodeShowSwitch?'流水线系统编号':'系统编号',
		       	            sortable : true,
		       	            dataIndex : 'systemNumberCicd',
		       	            hideable: true,
		       	            flex : 1,
							hidden: fjnxSyncSystemSwitch?false:!systemCodeShowSwitch,
							editor :
		       	            {
		       		            allowBlank : false
		       	            }
		       	        },{
		       	        	
		       	        	text : '系统简称',
		       	            sortable : true,
		       	            dataIndex : 'systemAbbreviation',
		       	            hideable: true,
		       	            flex : 1,
							hidden: !fjnxSyncSystemSwitch,
							editor :
		       	            {
		       		            allowBlank : false
		       	            }
		       	        },{
		       	        	
		       	        	text : '系统负责人',
		       	            sortable : true,
		       	            dataIndex : 'asyToUse',
		       	            hideable: true,
		       	            flex : 1,
							hidden: (fjnxSyncSystemSwitch||hsBankSwitch)?false:true,
							editor :
		       	            {
		       		            allowBlank : false
		       	            }
		       	        },{
							text : '联系方式',
							sortable : true,
							dataIndex : 'systemOwnerTel',
							hideable: true,
							flex : 1,
							hidden: hsBankSwitch?false:true,
							editor :
							{
								allowBlank : false
							}
						},{
							text : '业务系统与Agent绑定',
							sortable : true,
							dataIndex : 'isysbingagent',
							hidden: !sysBindAgentSwitch,
							flex : 1,
							editor : { allowBlank : true }
						},{
		       	        	
		       	        	text : '系统状态',
		       	            sortable : true,
		       	            dataIndex : 'systemStatus',
		       	            hideable: true,
		       	            flex : 1,
							hidden: !fjnxSyncSystemSwitch,
							editor : systemStatusComboBox,
							renderer:function (value, meta,record,rowIndex,colIndex) {
								if(value === '01'){
									return '规划';
								} else if(value === '02'){
									return '在建';
								} else if(value === '03'){
									return '运行';
								} else if(value === '04'){
									return '改造';
								} else if(value === '05'){
									return '下线';
								} else if(value === '06'){
									return '停用';
								} else{
									return '';
								}
							}
		       	        },{
		       	        	
		       	        	text : '是否持续集成',
		       	            sortable : true,
		       	            dataIndex : 'ifContinuousIntegration',
		       	            hideable: true,
		       	            flex : 1,
							hidden: !fjnxSyncSystemSwitch,
							editor : ifContinuousIntegrationComboBox,
							renderer:function (value, meta,record,rowIndex,colIndex) {
								if(value === '0'){
									return '否';
								} else if(value === '1'){
									return '是';
								} else{
									return '';
								}
							}
		       	        },{
		       	        	
		       	        	text : '是否自动化部署',
		       	            sortable : true,
		       	            dataIndex : 'ifAutoDeploy',
		       	            hideable: true,
		       	            flex : 1,
							hidden: !fjnxSyncSystemSwitch,
							editor : ifAutoDeployComboBox,
							renderer:function (value, meta,record,rowIndex,colIndex) {
								if(value === '0'){
									return '否';
								} else if(value === '1'){
									return '是';
								} else{
									return '';
								}
							}
		       	        },{
		       	        	
		       	        	text : '配置管理员',
		       	            sortable : true,
		       	            dataIndex : 'asytouseManager',
		       	            hideable: true,
		       	            flex : 1,
							hidden: !fjnxSyncSystemSwitch,
							editor :
		       	            {
		       		            allowBlank : false
		       	            }
		       	        },{
		       	        	
		       	        	text : '开发科室',
		       	            sortable : true,
		       	            dataIndex : 'asytodetOffice',
		       	            hideable: true,
		       	            flex : 1,
							hidden: !fjnxSyncSystemSwitch,
							editor :
		       	            {
		       		            allowBlank : false
		       	            }
		       	        },{
							text : '执行域名称',
							sortable : true,
							dataIndex : 'domainName',
							hideable: true,
							flex : 1
						}
		       	];
		}
	/** *********************Panel********************* */
	var FieldContainer = Ext.create ('Ext.form.field.ComboBox',
			{
				fieldLabel: '工程类型',
			    margin : '5',
			    labelWidth : 80,
			    width : 250,
			    queryMode : 'local',
			    // padding : '0 10 0 0',
			    editable : false,
			    hidden: fjnxSyncSystemSwitch,
			    store : [
			            [ 7, '健康巡检'], 
			            [6, '应急操作'], 
			            [5, '定时任务'],
			            [3, '应用变更'],
			            [16, '应用维护'],
			            [40, 'AZ切换'] 
			           
			    ],
			    listConfig :
			    {
				    minHeight : 80
			    }, listeners: {
					afterRender: function(combo) {
					    　　//combo.setValue(7);
					    //combo.setRawValue("健康巡检");    
					}  
				}
			});
	   
	   	var form = Ext.create('Ext.form.Panel', {
		region : 'north',
		baseCls:'customize_gray_back',
		border:false,
		dockedItems : [
		    {
		        xtype : 'toolbar',
		        baseCls:'customize_gray_back',
		        items : [
		           		sysNameForQuery, prjTypeForQuery, queryButtonForBSM, resetButtonForBSM,tongBuButton,'->',FieldContainer,
		                addButtonForBSM,freezeButtonForIEAI,unFreezeButtonForIEAI,
		                deleteButtonForBSM, saveButtonForBSM,syncButton,exportAllButton,syncButtonForFJNX/* changeButtonForBSM */
		        ]
		    },{
		        xtype : 'toolbar',
		        baseCls:'customize_gray_back',
		        border : false,
		        dock : 'top',
		        items : [

		                '->',/*promotion,*/importOrExportButtonForBSM,
		                syncButtonForBSM
		        ]
		    }
	    ]
	});
	/** 业务系统列表panel* */
	var grid_panel = Ext.create ('Ext.grid.Panel',
	{
	    region : 'center',
		cls:'customize_panel_back',
	    plugins : [
		    cellEditing
	    ],
	    padding:grid_space,
	    store : businessSystemStore,
	    selModel : Ext.create ('Ext.selection.CheckboxModel',
	    {
	    	checkOnly : true,
	    	// 判断复选框是否显示
	    	renderer : function (value, metaData, record, rowNum)
	    	{
	    		// begin.updated by manxi_zhao.2017-04-11.
// if(record.get('prjType')!=7 && record.get('pkgid')!=0)
	    		if(record.get('prjType')!=8 && record.get('prjType')!=7 && record.get('prjType')!=1 && record.get('pkgid')!=0 && record.get('prjType')!=3)
	    		// end.updated by manxi_zhao.2017-04-11.
 	    		 {
	    			return "";
 	    		 }
	    		else
	    		{
	    			return '<div class="x-grid-row-checker">';
	    		}
	    	},
	    	updateHeaderState: function() {
	    		var me = this,
	    		store = me.store,
	    		storeCount = store.getCount(),
	    		views = me.views,
	    		hdSelectStatus = false,
	    		selectedCount = 0,
	    		selected, len, i;
	    		var storeCountTemp = storeCount;
	    		for(var k=0;k<storeCount;k++)
	    		{
	    			// begin.updated by manxi_zhao.2017-04-11.
// if(store.getAt(k).get('prjType')!=7 && store.getAt(k).get('pkgid')!=0)
	    			if(store.getAt(k).get('prjType')!=8 && store.getAt(k).get('prjType')!=7 && store.getAt(k).get('prjType')!=1 && store.getAt(k).get('pkgid')!=0 && store.getAt(k).get('prjType')!=3)
	    			// end.updated by manxi_zhao.2017-04-11.
	    			{
	    				storeCountTemp--;
	    			}
	    		}
	    	storeCount = storeCountTemp;
	    	if (!store.buffered && storeCount > 0) 
	    	{
	    		selected = me.selected;
	    		// hdSelectStatus = true;
	    		for (i = 0, len = selected.getCount(); i < len; ++i)
	    		{
	    			if (!me.storeHasSelected(selected.getAt(i)))
	    			{
	    				break;
	    			}
	    		            ++selectedCount;
	    		}
	    		        hdSelectStatus = storeCount === selectedCount;
	    	}
	    	if (views && views.length)
	    	{
	    		me.toggleUiHeader(hdSelectStatus);
	    	}
	    },
        select: function()
       	{
		  // 重写选择选中方法，解决checkcolumn选中后整行选中问题
	    }
	    }),
	    border : false,
	    // bodyStyle: 'overflow-x:hidden; overflow-y:hidden',
	    columnLines : true,
	    // flex : 2,
	    columns : gridColumns,
	    collapsible : false,
	    bbar : bsPageBar,
	    listeners :
	    {
		    beforeedit : function (editor, e, eOpts)
		    {
		    	var PRO_TYPE_SUS=3;
		    	var PRO_TYPE_APM=16;
		    	if(isPrjEditSusSwitch){		    		
		    		if(null!=e){
		    			var _curRecord=e.record.data;
		    			if((_curRecord.prjType==PRO_TYPE_SUS)||(_curRecord.prjType==PRO_TYPE_APM) || (_curRecord.prjType==6)||(_curRecord.prjType==7)){
		    				return true;
		    			}
		    		}
		    	}
		    	
		    	
			    if (e.field == 'sysName')// 如果要编辑的列为业务系统名字则判断是否为增加，增加可以编辑，否则不可以
			    {
// if (e.record.get ("systemId") == -1)
// {
// return true;
// }
// else
// {
// return false;
// }
				   // return e.record.get ("systemId") == -1? true : false;
				    if(e.record.get ("systemId") == -1){
				    	return true;
				    }else if(e.record.data.prjType==5 || e.record.data.prjType==7){
				    	return true;
				    }else{
				    	return false;
				    }
			    }
		    },
		    edit : function( editor, e, eOpts )
		    {
		    	if (e.field == 'sysName')
			    {
		    		var sysNameFainl=e.record.get ('sysName').trim ();
			    	e.record.set('sysName',sysNameFainl);
			    }
		    	if (e.field == 'systemNumber')
			    {
		    		var systemNumber=e.record.get ('systemNumber').trim ();
			    	e.record.set('systemNumber',systemNumber);
			    }
		    	
		    },
		    beforeselect : function (abc, record, index, eOpts)
	        {
		    	// begin.updated by manxi_zhao.2017-04-11.
// if (record.data.prjType != 7 &&record.data.pkgid!=0)
		    	if (record.data.prjType!= 8 && record.data.prjType != 7 && record.data.prjType != 1 &&record.data.pkgid!=0 && record.data.prjType!=3)
		    	// end.updated by manxi_zhao.2017-04-11.
		        {
			        return false;
		        }
	        }
	    }
	});
	/** 判断删除按钮是否可用* */
	grid_panel.getSelectionModel ().on ('selectionchange', function (selModel, selections)
	{
		deleteButtonForBSM.setDisabled (selections.length === 0);
	});
	if(isTabSwitch){
		/** 主Panel* */
		businessSystem_mainPanel = Ext.create ('Ext.panel.Panel',
			{
				renderTo : "businessSystemMainDiv"+businessSystemMainnow,
				height : contentPanel.getHeight()-modelHeigth,
				width : contentPanel.getWidth(),
				layout : 'border',
				bodyCls:'customize_stbtn',
				// autoScroll:true,
				border : false,
				items : [form,grid_panel]
			});
	}else {
		/** 主Panel* */
		businessSystem_mainPanel = Ext.create ('Ext.panel.Panel',
			{
				renderTo : "businessSystemMainDiv",
				height : contentPanel.getHeight()-modelHeigth,
				width : contentPanel.getWidth(),
				layout : 'border',
				bodyCls:'customize_stbtn',
				// autoScroll:true,
				border : false,
				items : [form,grid_panel]
			});
	}
	/** 设备变更窗口tabPanel* */
	var tabPanelForBSM = Ext.create ('Ext.tab.Panel',
	{
	    // title : '业务系统名称：无',
	    tabPosition : 'top',
	    region : 'center',
	    activeTab : 0,
	    width : '100%',
	    height : contentPanel.getHeight () - 65,
	    border : false,
	    // plain : true,
	    defaults :
	    {
		    autoScroll : true
	    /*
		 * , bodyPadding : 5
		 */
	    },
	    items : [
	            {
		            hidden : true
	            },
	            {
	                title : '所属设备',
	                border : false,
	                loader :
	                {
	                    url : 'initBusinessSystemComputer.do',
	                    contentType : 'html',
	                    autoLoad : false,
	                    loadMask : true,
	                    scripts : true
	                },
	                listeners :
	                {
		                activate : function (tab)
		                {
			                tab.loader.load (
			                {
				                params :
				                {
					                sysIdForQuery : gobleSysId,
					                opersystype : opersystype
				                }
			                });
		                }
	                }
	            },
	            {
	                title : '待增加设备',
	                border : false,
	                loader :
	                {
	                    url : 'initBusinessSystemComputerNoSelected.do',
	                    contentType : 'html',
	                    autoLoad : false,
	                    loadMask : true,
	                    scripts : true
	                },
	                listeners :
	                {
		                activate : function (tab)
		                {
			                tab.loader.load (
			                {
				                params :
				                {
					                sysIdForQuery : gobleSysId,
					                opersystype : opersystype
				                }
			                });
		                }
	                }
	            }
	    ]
	});
	/** 设备绑定用户组及用户 panel */
	
	var prityCombox = Ext.create ('Ext.form.field.ComboBox',
			{
		 		fieldLabel: '优先级',
		 		name:'priority',
		 		anchor: '95%',
			    queryMode : 'local',
			    hidden:true,
			    editable : false,
			    store : [
			            [1, 1],
			            [2, 2],
			            [3, 3],
			            [4, 4],
			            [5, 5]
			    ],
			    listConfig :
			    {
				    minHeight : 80
			    }
			});
	// var handStartCombox = Ext.create ('Ext.form.field.ComboBox',
	// 	{
	// 		fieldLabel: '是否手工发起系统',
	// 		name:'handStart',
	// 		value:'手工',
	// 		anchor: '95%',
	// 		queryMode : 'local',
	// 		hidden:false,
	// 		editable : false,
	// 		store : [
	// 			[1, '手工'],
	// 			[2, '自动']
	// 		],
	// 		listConfig :
	// 			{
	// 				minHeight : 80
	// 			}
	// 	});
	var proTypeCombox = Ext.create ('Ext.form.field.ComboBox',
			{
		 		fieldLabel: '系统类别',
		 		anchor: '95%',
		 		name:'sysType',
			    queryMode : 'local',
			    displayField : 'applvl',
                valueField : 'applvl',
			    editable : false,
			    store :syslvStore
			});
	 proInfoFrom = new Ext.widget({
        xtype: 'form',
        id: 'multiColumnForm',
        width: 750,
        bodyPadding: '10 10 0',
        fieldDefaults: {
            labelAlign: 'left'
        },
        items: [{
            xtype: 'container',
            anchor: '100%',
            layout: 'hbox',
            items:[{
                xtype: 'container',
                flex: 1,
                layout: 'anchor',
                items: [{
                    xtype:'textfield',
                    fieldLabel: '系统参数',
                    allowBlank: true,
                    hidden:true,
                    name: 'sysParam',
                    anchor:'95%',
                    value: ''
                },{
                    xtype:'textfield',
                    fieldLabel: '工程ID',
                    allowBlank: false,
                    hidden:true,
                    name: 'projectid',
                    anchor:'95%',
                    value: ''
                },prityCombox,proTypeCombox
                // begin.
                /*
				 * ,{ xtype:'textfield', fieldLabel: '邮件主题', name: 'summary',
				 * editor : { allowBlank : true, maxLength : 200 }, //
				 * readOnly:true, // value:gobleSysName, hidden:true,
				 * anchor:'95%' } //end. ] },{ xtype: 'container', flex: 1,
				 * layout: 'anchor', items: [{ xtype:'textfield', fieldLabel:
				 * '发送时间', // afterLabelTextTpl: required, allowBlank: false,
				 * emptyText :'必填项目，时间格式：0:00:00 ~ 23:59:00', regex:
				 * /^((([01]?[0-9])|(2[0-3])):[0-5]?[0-9]:[0-5]?[0-9],?)+$/ ,
				 * regexText : '时间格式：0:00:00 ~ 23:59:00 ，多个时间点请以逗号分隔。', name:
				 * 'sendCycle', anchor:'95%', value: '' },{ xtype:'textfield',
				 * fieldLabel: '邮件地址', name: 'mails', editor : { allowBlank :
				 * true, maxLength : 200, regex:
				 * /^((([a-z0-9_\.-]+)@([\da-z\.-]+)\.([a-z\.]{2,6}\;))*(([a-z0-9_\.-]+)@([\da-z\.-]+)\.([a-z\.]{2,6})))$/ ,
				 * regexText : '邮件格式：<EMAIL>，多个邮件地址请以分号分隔。' },
				 * anchor:'95%' },{ xtype:'checkboxfield', fieldLabel: '开启发送', //
				 * afterLabelTextTpl: required, allowBlank: false, name:
				 * 'mailSendStatus', anchor:'95%', value: '' } //begin. ,{
				 * editable : false,// 是否可输入编辑 xtype: "combobox", fieldLabel:
				 * '星期属性', name: "dayType", lastQuery: '', store: daystore,
				 * queryModel: "local", displayField: "name", valueField: "id",
				 * renderer: function (value) { return getTextById(daystore,
				 * value); } }
				 */
                // end.
                ]
            }]
        }, {
            xtype: 'textfield',
            name: 'sysDesc',
            fieldLabel: '描述信息',
            maxLength: 50,
            anchor:'95%'
        }],

        buttons: [ {
            text: '保存',
            handler: function() {
               var isSub= this.up('form').getForm().isValid();
               if(isSub){
            	   var projectid	=proInfoFrom.form.findField("projectid").getValue();
// var mailSendStatus=proInfoFrom.form.findField("mailSendStatus").getValue();
				   var sysParam=proInfoFrom.form.findField("sysParam").getValue();
				   var sysDesc=proInfoFrom.form.findField("sysDesc").getValue();
				  // var handStart=proInfoFrom.form.findField("handStart").getValue();
				   // var isHandStart="";
				   // if("手工"==handStart){
					//    isHandStart='1';
				   // }else if("自动"==handStart){
					//    isHandStart='2';
				   // }else if("1"==handStart){
					//    isHandStart='1';
				   // }else if("2"==handStart){
					//    isHandStart='2';
				   // }

// var mails=proInfoFrom.form.findField("mails").getValue();
// var sendCycle=proInfoFrom.form.findField("sendCycle").getValue();
				   var priority=proInfoFrom.form.findField("priority").getValue();
				   var sysType=proInfoFrom.form.findField("sysType").getValue();
// var summary = proInfoFrom.form.findField("summary").getValue();//added.
// var dayType = proInfoFrom.form.findField("dayType").getValue();//added.
				  // saveProInfo(projectid,sysType,sysParam,sysDesc,priority,isHandStart);
				   saveProInfo(projectid,sysType,sysParam,sysDesc,priority);
               }else{
            	   Ext.Msg.alert ('提示', "请完善必填项目信息！");
               }
            }
        }/*
			 * ,{ text: '重置',
			 * 
			 * handler: function() { this.up('form').getForm().reset(); } }
			 */]
    });
    
    
    function onShowSysHcTemp(systemId,sysName) {
    	var title = "模板变更-业务系统名称:"+	sysName;
    	var winsystemp = Ext.create('Ext.window.Window', {
      		title : title,
      		autoScroll : true,
      		modal : true,
      		resizable : false,
      		closeAction : 'destroy',
      		width : 850,
			height : 650,
			layout : 'border',
      		loader : {
      			url : "initBusinessHcSysTempIndex.do?systemid="+systemId,
      			autoLoad : true,
      			autoDestroy : true,
      			scripts : true
      		}
      	}).show();
    }
    
	var winProInfo = Ext.create ('widget.window',
			{
			    draggable : false,// 禁止拖动
			    resizable : false,// 禁止缩放
// animateTarget:changeButtonForBSM,
			    modal : true,
			    title : '系统属性信息',
			    closable : true,
			    closeAction : 'hide',
			    width : 800,
// height : contentPanel.getHeight()*0.3,
				height : 260,
			    layout : 'border',
			    items : [
					proInfoFrom
			    ],
			    listeners :
			    {
			        'close' : function ()
			        {},
			        'show' : function ()
			        {}
			    }
			});
	/** 设备变更窗口* */
	
	
	/** 窗口尺寸调节* */
	contentPanel.on ('resize', function ()
	{
		businessSystem_mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
		businessSystem_mainPanel.setWidth (contentPanel.getWidth());
		if(win != undefined){
			win.center();
		}
		
	});
	// 当页面即将离开的时候清理掉自身页面生成的组建
    contentPanel.getLoader().on("beforeload",function(obj, options, eOpts){
    	Ext.destroy(businessSystem_mainPanel);
    	Ext.destroy(win);
    	Ext.destroy(winProInfo);
		if(Ext.isIE){
        	CollectGarbage(); 
    	}
    });
	/** *********************方法********************* */
	/* 解决IE下trim问题 */
	String.prototype.trim = function ()
	{
		return this.replace (/(^\s*)|(\s*$)/g, "");
	};
	function businessSystemNameStoreLoadCallback ()
	{
		sysNameForQueryRelation.setValue (gobleSysId);
		sysNameForQueryRelation.setRawValue (gobleSysName);
		sysNameForQueryRelation.applyEmptyText ();
		prjTypeForQueryNew.setValue ('');
		if (gobleSysId != '')
		{
			sysNameForQueryRelation.setDisabled (true);
			prjTypeForQueryNew.setDisabled (true);
			win.setTitle ("设备变更-业务系统名称：" + gobleSysName);
		}
		else
		{
			sysNameForQueryRelation.setDisabled (false);
			prjTypeForQueryNew.setDisabled (false);
		}
		tabPanelForBSM.setActiveTab (1); // 激活Tab 页
	}
	/** 显示设备变更窗口* */
	function onShowSysRelation (systemId, sysName, prjType)
	{
		if($("#businessSystemComputerDiv").length>0){
		 $("#businessSystemComputerDiv").remove();
		}
		if($("#businessSystemComputerNoSelectedDiv").length>0){
		 $("#businessSystemComputerNoSelectedDiv").remove();
		}
		
		opersystype=prjType;
		
		var appString = "";
		if (urlType == '')
		{
			if (prjType == 5)
			{
				appString = "【定时任务】";
			}
			else if (prjType == 7)
			{
				appString = "【健康巡检】";
			}
		}
		gobleSysId = systemId;
		gobleSysName = sysName + appString;
		
		if(!win){
			win = Ext.create ('widget.window',
					{
					    draggable : false,// 禁止拖动
					    resizable : false,// 禁止缩放
					    animateTarget:changeButtonForBSM,
					    modal : true,
					    title : '设备变更',
					    closable : true,
					    closeAction : 'hide',
					    // animateTarget: this,
					    width : 1050,
					    height : 542,
					    layout : 'border',
					    items : [
					            {
					                region : 'north',
					                xtype : 'panel',
					                width : '100%',
					                bodyPadding : 5,
					                // layout : 'form',
					                items : [
						                {
						                    layout : 'column',
						                    border : false,
						                    items : [
						                            sysNameForQueryRelation, prjTypeForQueryNew
						                    ]
						                
						                }
					                ]
					            }, tabPanelForBSM
					    ],
					    listeners :
					    {
					        'close' : function ()
					        {
						        tabPanelForBSM.setActiveTab (0); // 激活Tab
						        // 页
						        win.setTitle ("设备变更");
						        gobleSysId = 0;
						        gobleSysName = "";
					        },
					        'show' : function ()
					        {
						        var prjTypeInit = 0;
						        var appStringInit = true;
						        if (urlType != '')
						        {
							        prjTypeInit = urlType;
							        appStringInit = false;
						        }
						        businessSystemNameStore.load (
						        {
						            
						            params :
						            {
						                prjType : prjTypeInit,
						                appString : appStringInit
						            },
						            callback : function (records, operation, success)
						            {
							            businessSystemNameStoreLoadCallback ();
						            }
						        });
					        }
					    }
					});
			}
		// win.show ();
		if (!win.isVisible ())
		{
			win.show (changeButtonForBSM);
		}
		
	}
	/** 显示绑定的用户组及用户 */
	function onShowUserRelation(systemId, sysName, prjType) {
		opersystype=prjType;
		gobleSysId = systemId;
		gobleSysName = sysName;
		var tabPanelForUser;
		if(isOnlyShowUserSwitch){
			tabPanelForUser = Ext.create ('Ext.tab.Panel',
					{
				    tabPosition : 'top',
				    region : 'center',
				    activeTab : 0,
				    width : '100%',
				    height : contentPanel.getHeight () - modelHeigth,
				    border : false,
				    defaults :
				    {
					    autoScroll : false
				    },
				    items : [
			            {
				            hidden : true
			            },
			            {
			                border : false,
			                loader :
			                {
			                    url : 'sysConfigUser.do',
			                    autoLoad : false,
			                    loadMask : true,
			                    scripts : true
			                },
			                listeners :
			                {
				                activate : function (tab)
				                {
				                	$('#grid_area_forUser_buss').each(function() {
				                	    $(this).remove();
				                	});
					                tab.loader.load (
					                {
						                params :
						                {
							                sysIdForQuery : gobleSysId,
							                opersystype : opersystype
						                },
						                callback : function(r, options, success) {
						        			if (!options) {
						        				secureFilterRs(r,"请求返回失败！");
						        			}
						        		}
					                });
				                }
			                }
			            }
		            ]
				});
			}else{
			tabPanelForUser = Ext.create ('Ext.tab.Panel',
					{
					    tabPosition : 'top',
					    region : 'center',
					    activeTab : 0,
					    width : '100%',
					    height : contentPanel.getHeight () - modelHeigth,
					    border : false,
					    defaults :
					    {
						    autoScroll : false
					    },
					    items : [
				            {
					            hidden : true
				            },
				            {
				                title : '用户组',
				                border : false,
				                loader :
				                {
				                    url : 'sysConfigUserGroup.do',
				                    autoLoad : false,
				                    loadMask : true,
				                    scripts : true
				                },
				                listeners :
				                {
					                activate : function (tab)
					                {
					                	$('#userGroup_g_area').each(function() {
					                	    $(this).remove();
					                	});
						                tab.loader.load (
						                {
							                params :
							                {
								                sysIdForQuery : gobleSysId,
								                opersystype : opersystype
							                },
							                callback : function(r, options, success) {
							        			if (!options) {
							        				secureFilterRs(r,"请求返回失败！");
							        			}
							        		}
						                });
					                }
				                }
				            },
				            {
				                title : '用户',
				                border : false,
				                loader :
				                {
				                    url : 'sysConfigUser.do',
				                    autoLoad : false,
				                    loadMask : true,
				                    scripts : true
				                },
				                listeners :
				                {
					                activate : function (tab)
					                {
					                	$('#grid_area_forUser_buss').each(function() {
					                	    $(this).remove();
					                	});
						                tab.loader.load (
						                {
							                params :
							                {
								                sysIdForQuery : gobleSysId,
								                opersystype : opersystype
							                },
							                callback : function(r, options, success) {
							        			if (!options) {
							        				secureFilterRs(r,"请求返回失败！");
							        			}
							        		}
						                });
					                }
				                }
				            }
				    ]
			 });
		}
		
		var	user_win = Ext.create('Ext.window.Window', {
			title : '业务与用户绑定',
			draggable : false,// 禁止拖动
		    resizable : false,// 禁止缩放
// id : 'userb-win',
		    modal : true,
		    closable : true,
		    closeAction : 'destory',
// autoDestroy:true ,
		    width : contentPanel.getWidth(),
			height : contentPanel.getHeight()-modelHeigth,
		    layout : 'border',
		    items : [tabPanelForUser],
			listeners :
			{
				'close' : function ()
				{
					// tabPanelForUser.setActiveTab (1); // 激活Tab 页
				},
				'show' : function ()
				{
					tabPanelForUser.setActiveTab (1); // 激活Tab 页
				}
			}
		}).show();
	}
	
	function onShowUserRelationNew(systemId, sysName, prjType) {
		opersystype=prjType;
		gobleSysId = systemId;
		gobleSysName = sysName;
		var tabPanelForUser;
		tabPanelForUser = Ext.create ('Ext.tab.Panel',
					{
					    tabPosition : 'top',
					    region : 'center',
					    activeTab : 0,
					    width : '100%',
					    height : contentPanel.getHeight () - modelHeigth,
					    border : false,
					    defaults :
					    {
						    autoScroll : false
					    },
					    items : [
				            {
					            hidden : true
				            },
				            {
				                title : '用户组',
				                border : false,
				                loader :
				                {
				                    url : 'sysConfigUserGroupNew.do',
				                    autoLoad : false,
				                    loadMask : true,
				                    scripts : true
				                },
				                listeners :
				                {
					                activate : function (tab)
					                {
					                	$('#userGroup_g_area').each(function() {
					                	    $(this).remove();
					                	});
						                tab.loader.load (
						                {
							                params :
							                {
								                sysIdForQuery : gobleSysId,
								                opersystype : opersystype
							                },
							                callback : function(r, options, success) {
							        			if (!options) {
							        				secureFilterRs(r,"请求返回失败！");
							        			}
							        		}
						                });
					                }
				                }
				            },
				            {
				                title : '用户',
				                border : false,
				                loader :
				                {
				                    url : 'sysConfigUserNew.do',
				                    autoLoad : false,
				                    loadMask : true,
				                    scripts : true
				                },
				                listeners :
				                {
					                activate : function (tab)
					                {
					                	$('#grid_area_forUser_buss').each(function() {
					                	    $(this).remove();
					                	});
						                tab.loader.load (
						                {
							                params :
							                {
								                sysIdForQuery : gobleSysId,
								                opersystype : opersystype
							                },
							                callback : function(r, options, success) {
							        			if (!options) {
							        				secureFilterRs(r,"请求返回失败！");
							        			}
							        		}
						                });
					                }
				                }
				            }
				    ]
			 });
		
		var	user_win = Ext.create('Ext.window.Window', {
			title : '业务与用户绑定',
			draggable : false,// 禁止拖动
		    resizable : false,// 禁止缩放
		    modal : true,
		    closable : true,
		    closeAction : 'destory',
		    width : contentPanel.getWidth(),
			height : contentPanel.getHeight()-modelHeigth,
		    layout : 'border',
		    items : [tabPanelForUser],
			listeners :
			{
				'show' : function ()
				{
					tabPanelForUser.setActiveTab (1); // 激活Tab 页
				}
			}
		}).show();
	}
	
	
	function onShowProInfo (systemId, sysName, prjType)
	{
		var appString = "";
		if (urlType == '')
		{
			if (prjType == 4)
			{
				appString = "【灾备切换】";
			}
			else if (prjType == 5)
			{
				appString = "【健康巡检】";
			}
		}
		gobleSysId = systemId;
		gobleSysName = sysName + appString;
		if (!winProInfo.isVisible ())
		{
			winProInfo.show (changeButtonForBSM);
			getProInfo(systemId);
		}
		
	}
	/** 查询业务系统* */
	function queryWhere ()
	{
		bsPageBar.moveFirst ();
	}
	/** 重置业务系统查询条件* */
	function resetWhere ()
	{
		sysNameForQuery.setValue ('');
		prjTypeForQuery.setValue ('');
		bsPageBar.moveFirst ();
		/** 如果非sa界面进入，重置需要赋固定值* */
// if (urlType != '')
// {
// prjTypeForQuery.setValue (urlType);
// prjTypeForQuery.setRawValue(getTypeName(urlType));
// }
	}
	/** 增加业务系统* */
	function onAddListener ()
	{
		// alert(FieldContainer.getChecked()[0].inputValue);
		var prjTypeValue = FieldContainer.getValue();
		if(fjnxSyncSystemSwitch){
			prjTypeValue = 3;
		}
		if(prjTypeValue == null || prjTypeValue == ""){
			Ext.Msg.alert ('提示', '请先选择工程类型再进行增加！');
			return;
		}
		var p = Ext.create ('businessSystemModel',
		{
		    systemId : -1,
		    sysName : "",
		    sysType : "",
		    sysDesc : "",
		    sysParam : "",
		    priority : 1,
		    icansync:true,
		    prjType : prjTypeValue
		});
		// businessSystemStore.stopEditing();
		businessSystemStore.insert (0, p);// 在第一个位置插入
		cellEditing.startEdit(0, 2);// 指定的行/列，进行单元格内容的编辑
	}
	
	function checkInt(str)
	{
	    // 如果为空，不通过校验
	    if(str == "")
	        return false;
	    if(new RegExp(/^(\d+)$/).test(str))
	        return true;
	    else
	        return false;
	}

	function promotionFun() {
		var record = grid_panel.getSelectionModel().getSelection();
		if (record.length === 0) {
			Ext.Msg.alert('提示', '请先选择您要操作的行！');
			return;
		}
		if (record.length > 1) {
			Ext.Msg.alert('提示', '只能选择一个工程进行操作！');
			return;
		}
		// 检查所有选中记录的工程类型是否为作业调度（prjType=1）
		var isValid = true;
		var invalidNames = [];
		Ext.Array.each(record, function(recordObj) {
			if (recordObj.get('prjType') != 1) { // 1代表作业调度
				isValid = false;
				invalidNames.push(recordObj.get('sysName'));
			}
		});

		if (!isValid) {
			Ext.Msg.alert('提示', '选中的业务系统包含非作业调度类型：<br>' + invalidNames.join('<br>'));
			return;
		}
		// 添加确认弹窗
		Ext.Msg.confirm('确认晋级', '确定要晋级选中的工程吗？', function(btn) {
			if (btn === 'yes') {
				// 收集系统ID
				var systemIds = [];
				var excelPrjs = [];
				Ext.Array.each(record, function(recordObj) {
					systemIds.push(recordObj.get('systemId'));
					excelPrjs.push(recordObj.get('excelPrj'));
				});

				// 显示等待提示
				Ext.MessageBox.wait('晋级处理中...', '进度条');

				// 发送AJAX请求
				Ext.Ajax.request({
					url: 'promotionBusinessSystem.do',
					method: 'POST',
					params: { systemIds: systemIds.join(',') ,excelPrjs: excelPrjs.join(',')},
					timeout: 30000,
					success: function(response, opts) {
						Ext.MessageBox.hide(); // 隐藏进度条

						var result;
						try {
							// 尝试直接解析JSON
							result = Ext.decode(response.responseText);
						} catch (e) {
							// 如果解析失败，可能是多个JSON对象拼接，尝试提取最后一个
							console.warn('JSON解析失败，尝试提取最后一个JSON对象: ', e.message);
							console.log('原始响应: ', response.responseText);

							try {
								var responseText = response.responseText;
								var lastBraceIndex = responseText.lastIndexOf('}');

								if (lastBraceIndex > 0) {
									// 从最后一个}向前查找对应的{
									var braceCount = 1;
									var startIndex = lastBraceIndex - 1;
									while (startIndex >= 0 && braceCount > 0) {
										if (responseText.charAt(startIndex) === '}') {
											braceCount++;
										} else if (responseText.charAt(startIndex) === '{') {
											braceCount--;
										}
										startIndex--;
									}

									if (braceCount === 0) {
										var lastJsonObject = responseText.substring(startIndex + 1, lastBraceIndex + 1);
										console.log('提取的最后一个JSON对象: ', lastJsonObject);
										result = Ext.decode(lastJsonObject);
									} else {
										throw new Error('无法找到完整的JSON对象');
									}
								} else {
									throw new Error('响应中没有找到JSON对象');
								}
							} catch (parseError) {
								console.error('提取JSON对象失败: ', parseError.message);
								result = { success: false, msg: '响应解析失败：' + parseError.message };
							}
						}

						if (result.success) {
							businessSystemStore.reload();
							Ext.Msg.alert('成功', result.msg || '晋级操作成功！');
						} else {
							Ext.Msg.alert('失败', result.msg || '晋级操作失败！');
						}
					},
					failure: function(response, opts) {
						Ext.MessageBox.hide(); // 隐藏进度条
						Ext.Msg.alert('错误', '请求超时或服务器错误！');
					}
				});
			}
		});
	}

	function syncAll(){
		// 显示等待信息
		var waitMsg = Ext.MessageBox.show({
			msg: '数据同步中，请稍候...',
			progressText: '数据同步中',
			width: 300,
			wait: true,
			waitConfig: { interval: 200 }
		});
		Ext.Ajax.request({
			url: 'dockSystem.do',
			method: 'POST',
			timeout: 100000000,
			success: function(response, opts) {
				waitMsg.hide(); // 隐藏等待信息
				commonMask.hide();
				var success = Ext.decode(response.responseText).success;
				if (success) {
					Ext.Msg.alert('提示', 'CMDB数据同步成功！');
					store.reload();
					return;
				}
				if (!success) {
					Ext.Msg.alert('提示', 'CMDB数据同步失败！');
					store.reload();
					return;
				}
			},
			failure: function(result, request) {
				waitMsg.hide(); // 隐藏等待信息
				commonMask.hide();
				secureFilterRs(result, "请求返回失败！", request);
				store.reload();
				return;
			}
		});
	}



	/** 保存业务系统* */
	function onSaveListener (btn)
	{
		try
		{
			btn.setDisabled (true);
			var allrecords = businessSystemStore.getRange(0,businessSystemStore.getCount());//全部
			var m = businessSystemStore.getModifiedRecords ();
			if (m.length < 1)
			{
				Ext.Msg.alert ('提示', '无需要增加或者修改的数据！');
				return;
			}
			var jsonData = "[";
			for (var i = 0, len = m.length; i < len; i++)
			{
				var j = 0;
				Ext.each(allrecords, function(r) {
					if(m[i].data.sysName.trim()==r.data.sysName.trim() && m[i].data.prjType == r.data.prjType)
					{
						j++;
					}				
				});
				//存在重复记录
				if(j>1)
				{
					Ext.Msg.show({
					     title:'提示',
					     msg: '业务系统名称：【'+m[i].data.sysName+'】重复',
					     buttons: Ext.Msg.OK,
					     icon: Ext.Msg.INFO
					});
					flag = false;
					return;
				}
				if(checkStemNameHSSwitch){
					var k = 0;
					Ext.each(allrecords, function(r) {
						if(m[i].data.sysName.trim()==r.data.sysName.trim() )
						{
							k++;
						}
					});
					//存在重复记录
					if(k>1)
					{
						Ext.Msg.show({
							title:'提示',
							msg: '业务系统名称：【'+m[i].data.sysName+'】重复',
							buttons: Ext.Msg.OK,
							icon: Ext.Msg.INFO
						});
						flag = false;
						return;
					}
				}
				// 福建农信判断系统编号
				if (fjnxSyncSystemSwitch)
				{
					var systemNumberCicd = m[i].get ("systemNumberCicd").trim ();
					if ('' == systemNumberCicd)
					{
						Ext.Msg.alert ('提示', '系统编号不能为空！');
						return;
					}
				}
				var sysName = m[i].get ("sysName").trim ();
				if ('' == sysName)
				{
					Ext.Msg.alert ('提示', '业务系统名称不能为空！');
					return;
				}
				if (fucCheckLength (sysName) > 200)
				{
					Ext.Msg.alert ('提示', '业务系统名称不能超过200字符！');
					return;
				}
				var sysType = m[i].get ("sysType").trim ();
				if (fucCheckLength (sysType) > 100)
				{
					Ext.Msg.alert ('提示', '系统类别不能超过100字符！');
					return;
				}
				var priority = m[i].get ("priority");
				if (!checkIsInteger (priority) || priority > 999)
				{
					Ext.Msg.alert ('提示', '优先级必须为不超过3位的整形数字！');
					return;
				}
				var sysDesc = m[i].get ("sysDesc").trim ();
				if (fucCheckLength (sysDesc) > 4000)
				{
					Ext.Msg.alert ('提示', '描述不能超过4000字符！');
					return;
				}
				var sysParam = m[i].get ("sysParam").trim ();
				if (fucCheckLength (sysParam) > 100)
				{
					Ext.Msg.alert ('提示', '系统参数不能超过100字符！');
					return;
				}
				
				var systemNumber = m[i].get ("systemNumber").trim ();
				if (fucCheckLength (systemNumber) > 100)
				{
					Ext.Msg.alert ('提示', '系统编码不能超过100字符！');
					return;
				}
				var isysbingagent = m[i].get ("isysbingagent").trim ();
				if (fucCheckLength (isysbingagent) > 255)
				{
					Ext.Msg.alert ('提示', '业务系统与Agent绑定字段不能超过255字符！');
					return;
				}
				if(pfIpmpSystemSwitch){
					var ipmpSysName = m[i].get ("ipmpSysName").trim ();
					var prjType = m[i].get ("prjType");
					if ('' != ipmpSysName && null != ipmpSysName && 3 != prjType && 40 != prjType && 16 != prjType)
					{
						Ext.Msg.alert ('提示', '业务系统名称(IPMP)只能保存应用变更/维护、AZ切换模块！');
						return;
					}
				}
				
			/*
			 * if(m[i].data.mailSendStatus) { if(!m[i].data.sendCycle) {
			 * Ext.Msg.alert ('提示', m[i].data.sysName + '的发送时间为空！'); return; }
			 * if(!m[i].data.mails) { Ext.Msg.alert ('提示', m[i].data.mails +
			 * '的邮件地址为空！'); return; } }
			 */
				
				var ss = Ext.JSON.encode (m[i].data);
				if (i == 0)
					jsonData = jsonData + ss;
				else
					jsonData = jsonData + "," + ss;
			}
			jsonData = jsonData + "]";
			Ext.MessageBox.wait("数据处理中...", "进度条");
			Ext.Ajax.request (
			{
			    url : 'saveBusinessSystem.do',
			    timeout: 30000,  
			    params :
			    {
				    incdata : jsonData
			    },
			    method : 'POST',
			    success : function (response, opts)
			    {
				    var success = Ext.decode (response.responseText).success;
				    // 当后台数据同步成功时
				    if (success)
				    {
					    bsPageBar.moveFirst ();
				    }
				    Ext.Msg.alert ('提示', Ext.decode (response.responseText).message);
			    },
// failure:function(response,ooptions){
// Ext.MessageBox.hide();
// Ext.Msg.alert ('提示', '请求超时！');
// }
			    failure : function(result, request) {
					secureFilterRs(result,"操作失败！");
				}
			});
		}
		catch (e)
		{
		}
		finally
		{
			btn.setDisabled (false);
		}
	}
	
	
	
	/** 冻结业务系统(只冻结作业调度下的业务系统) * */
	function onFreezeListener(btn){
		var record = grid_panel.getSelectionModel ().getSelection ();
		if (record.length == 0)
		{
			Ext.Msg.alert ('提示', "请先选择您要操作的行!");
			return;
		}
		var prjNames = [];
		var flag = false;
		Ext.Array.each (record, function (recordObj)
		{
			if(recordObj.get('freeze') == 1){
				Ext.Msg.alert ('提示', "工程'"+recordObj.get('sysName')+"'已冻结,请不要重复冻结!");
				flag = true;
				return false; 
			}
			if(recordObj.get('prjType') != 1) {
				Ext.Msg.alert ('提示', "选择了含有非作业调度的工程,请选择作业调度下的工程!");
				flag = true;
				return false; 
			}
			// if(recordObj.get('prjType'))
			var prjName = recordObj.get('sysName');
			prjNames.push(prjName);
		});
		
		if(flag) {
			return ;
		}
		
		Ext.Ajax.request({
			url : 'freezeBusinessSystem.do',
			method : 'POST',
			params : {
				prjNames : prjNames,
				isFreeze : true
			},
			success : function(response, options) {
				var success = Ext.decode(response.responseText).success;
				var message = Ext.decode(response.responseText).message;

				if (success) {
					Ext.Msg.alert('提示', message, function() {
						if (Ext.isIE) {
							CollectGarbage();
						}
					});
					// grid重新加载
					businessSystemStore.load();
				} else {
					Ext.Msg.alert('提示', message, function() {
						if (Ext.isIE) {
							CollectGarbage();
						}
					});
				}
			},
			failure : function(result, request) {
				secureFilterRs(result, "操作失败！");
			}

		});
	}
	
	/** 解冻业务系统(只冻结作业调度下的业务系统) * */
	function onUnFreezeListener(btn){
		var record = grid_panel.getSelectionModel ().getSelection ();
		if (record.length == 0)
		{
			Ext.Msg.alert ('提示', "请先选择您要操作的行!");
			return;
		}
		var prjNames = [];
		var flag = false;
		Ext.Array.each (record, function (recordObj)
		{
			if(recordObj.get('freeze') == 0){
				Ext.Msg.alert ('提示', "工程'"+recordObj.get('sysName')+"'状态正常,不需要解冻!");
				flag = true;
				return false; 
			}
			if(recordObj.get('prjType') != 1) {
				Ext.Msg.alert ('提示', "选择了含有非作业调度的工程,请选择作业调度下的工程!");
				flag = true;
				return false; 
			}
			// if(recordObj.get('prjType'))
			var prjName = recordObj.get('sysName');
			prjNames.push(prjName);
		});
		if(flag) {
			return;
		}
		Ext.Ajax.request({
			url : 'freezeBusinessSystem.do',
			method : 'POST',
			params : {
				prjNames : prjNames,
				isFreeze : false
			},
			success : function(response, options) {
				var success = Ext.decode(response.responseText).success;
				var message = Ext.decode(response.responseText).message;

				if (success) {
					Ext.Msg.alert('提示', message, function() {
						if (Ext.isIE) {
							CollectGarbage();
						}
					});
					// grid重新加载
					businessSystemStore.load();
				} else {
					Ext.Msg.alert('提示', message, function() {
						if (Ext.isIE) {
							CollectGarbage();
						}
					});
				}
			},
			failure : function(result, request) {
				secureFilterRs(result, "操作失败！");
			}

		});
	}
	
	/** 业务系统维护-历史信息查看功能 一体化4.7.9 孙悦 * */
	function projectOpen(value, metaData, record) {
		var func = '';
		var name =  record.get('sysName');
		var IID = record.get('systemId');
		var isExcel = record.get('excelPrj');
		var prjType = record.get('prjType');
		func = "openProInfo('" + IID + "', '" + isExcel  + "', '" + name + "', '" + prjType + "')";
		if(record.get('prjType') == 1){
			return '<a href="javascript:' + func + ';">' + value + '</a>';
		} else {
			return value;
		}
	}
	
	/** 删除业务系统* */
	function onDeleteListener (btn)
	{
		var record = grid_panel.getSelectionModel ().getSelection ();
		if (record.length == 0)
		{
			Ext.Msg.alert ('提示', "请先选择您要操作的行!");
			return;
		}
		
		var runPrjBoolean = false; // 判断是否有正在运行的工程
		for (var i = 0; i < record.length; i++)
		{
			if(record[i].get ('runPrj') == 1) {
				runPrjBoolean = true;
			}
			// 查看选中的行中是否存在正在运行的活动，如果存在，则不允许删除
			if(runPrjBoolean)
			{
				Ext.Msg.alert ('提示',"工程'"+record[i].get('sysName')+"'下有正在运行的工作流，不可以进行删除!");
				return;
			}
		}
		
		var goBack = false;// 判断是否需要删除数据库数据
		for (var i = 0; i < record.length; i++)
		{
			if (-1 != record[i].get ('systemId'))// 如果预删除数据id不为-1，则需要删除数据库数据
			{
				goBack = true;
				break;
			}
		}
		if (goBack)// 通过数据库删除数据
		{
			Ext.MessageBox.confirm ('提示', "是否删除选中数据!", function (btn)
			{
				if (btn == 'no')
				{
					return;
				}
				if (btn == 'yes')
				{
					
				    Ext.MessageBox.wait("数据处理中...", "进度条");
				    // var ids = [];
				    var upperIds = [];
					var icicdsys_codes = [];
				    var businessnamelist=[];
					Ext.Array.each (record, function (recordObj)
					{
						// var cpId = recordObj.get ('systemId');
						var upperId = recordObj.get('upid');
						var icicdsys_code = recordObj.get('systemNumberCicd');

// // 如果删除的是幻影数据，则id就不传递到后台了，直接在前台删除即可
// if (-1 !=cpId)
// {
// ids.push (cpId);
// }
						upperIds.push(upperId);
						icicdsys_codes.push(icicdsys_code);
						
						
						// 增加记录删除业务系统名称
						var bsname = recordObj.get('sysName');
						businessnamelist.push(bsname);
					});
					Ext.Ajax.request (
					{
					    url : 'deleteBusinessSystem.do',
					    timeout: 30000,  
					    params :
					    {
// incdata : t_date
					    	// begin.updated by manxi_zhao.2017-05-02.
// deleteIds : ids.join (',')
						    deleteIds : upperIds.join(','),
							deleteSysCodess : icicdsys_codes.join(','),
						    delbusinessnames:businessnamelist.join(',')
						    // end. updated by manxi_zhao.2017-05-02.
					    },
					    method : 'POST',
					    success : function (response, opts)
					    {
						    var success = Ext.decode (response.responseText).success;
						    
						    if (success)
						    {
							    // 当后台数据同步成功时刷新列表
							    bsPageBar.moveFirst ();
						    }
						    Ext.Msg.alert ('提示', Ext.decode (response.responseText).message);
					    },
// failure:function(response,ooptions){
// Ext.MessageBox.hide();
// Ext.Msg.alert ('提示', '请求超时！');
// }
					    failure : function(result, request) {
							secureFilterRs(result,"操作失败！");
						}
					});
					
				}
			});
		}
		else
		{
			for (var i = 0; i < record.length; i++)
			{
				// 如果不需要删除数据库数据，则不刷新列表，仅移除store数据
				businessSystemStore.remove (record[i]);
				
			}
		}
		
		
		
	}
	/** 导出工程* */
	function onExportListener (btn)
	{
		var record = grid_panel.getSelectionModel ().getSelection ();
		if (record.length == 0)
		{
			Ext.Msg.alert ('提示', "请先选择您要操作的行!");
			return;
		}
		
		var jsonData="[";
		var countN=0;
		Ext.Array.each (record, function (recordObj)
		{
			if(countN>0)
				{
				jsonData=jsonData+",";
				}
			jsonData=jsonData+"{";
			jsonData=jsonData+"prjId:'"+recordObj.get('systemId');
			jsonData=jsonData+"',";
			jsonData=jsonData+"prjName:'"+recordObj.get('sysName');
			jsonData=jsonData+"',";
			jsonData=jsonData+"prjVer:'"+recordObj.get ('prjVersion');
			jsonData=jsonData+"'}";
		});
		 jsonData=jsonData+"]";
		// window.location.href = 'exportPkgS.do?jsonData='+jsonData;
		 var myForm = document.createElement("form");       
         myForm.method = "post";  
         myForm.action = "exportPkgS.do";   
         
         // 固定值，指定文件提取标识
         var myInput1 = document.createElement("input");       
         myInput1.setAttribute("name", "jsonData");  // 为input对象设置name
         myInput1.setAttribute("value", jsonData);  // 为input对象设置value
         myForm.appendChild(myInput1);  
         
         
         document.body.appendChild(myForm);     
         myForm.submit();   
         document.body.removeChild(myForm);  // 提交后移除创建的form
         Ext.Msg.alert ('提示', '开始下载，请稍后');
		   
	}
	function saveProInfo(projectid,sysType,sysParam,sysDesc,priority){
		Ext.Ajax.request (
				{
				    url : 'saveProInfo.do',
				    params :
				    {
				    		sysName:gobleSysName,// added
				    		projectid:projectid,
				    		sysParam:sysParam,
				    		sysDesc:sysDesc,
				    		priority:priority,
				    		sysType:sysType
				    },
				    method : 'POST',
				    success : function (response, opts)
				    {

					    var success = Ext.decode (response.responseText).success;
						proInfoMap = Ext.decode(response.responseText).proInfoMap;
					    // 当后台数据同步成功时
					    if (success)
					    {
					    	Ext.Msg.alert ('提示', Ext.decode (response.responseText).message);
					    }
						bsPageBar.moveFirst();
					},
// failure:function(response,ooptions){
// Ext.MessageBox.hide();
// Ext.Msg.alert ('提示', '请求超时！');
// }
				    failure : function(result, request) {
						secureFilterRs(result,"操作失败！");
					}
				});
	}
	function getProInfo(prjid){
		Ext.Ajax.request (
				{
				    url : 'getProInfo.do',
				    params :
				    {
				    	prjid : prjid
				    },
				    method : 'POST',
				    success : function (response, opts)
				    {
				    	proInfoFrom.form.reset();
				    	var respText=Ext.decode(response.responseText);
				    	proInfoFrom.form.findField("projectid").setValue(prjid);
					    proInfoFrom.form.findField("sysParam").setValue(respText.sysParam);
					    proInfoFrom.form.findField("sysDesc").setValue(respText.sysDesc);
					    proInfoFrom.form.findField("priority").setValue(respText.priority);
					    proInfoFrom.form.findField("sysType").setValue(respText.sysType);
						// if('1'==respText.handStart){
						// 	proInfoFrom.form.findField("handStart").setValue("手工");
						// }else if('2'==respText.handStart){
						// 	proInfoFrom.form.findField("handStart").setValue("自动");
						// }
					    // var dayTypeObj = daystore.findRecord("id",
						// respText.dayType)
				    },
				    failure:function(response,ooptions){ 
					Ext.MessageBox.hide();
					Ext.Msg.alert ('提示', '请求超时！');
				    }
				});
	}
	/** 如果非sa界面进入，隐藏页面相关组件* */
	function getTypeName (a){
		if(a == 1){
			return "作业调度";
		}
		if(a == 2){return "信息采集"}
		if(a == 3){return "应用变更"}
		if(a == 4){return "灾备切换"}
		if(a == 5){return "定时任务"}
		if(a == 6){return "应急操作"}
		if(a == 7){return "健康巡检"}
		if(a == 8){return "日常操作"}
		if(a == 0){return "未选择"}
		
		
	}
	if (urlType != '')
	{
// grid_panel.columns[2].hide ();
		/* alert('将 1017行去掉！'); */
// prjTypeForQuery.setValue (urlType);
// prjTypeForQuery.hide ();
		prjTypeForQueryNew.setValue (urlType);
		prjTypeForQueryNew.hide ();
		sysNameForQueryRelation.setWidth (775);
	}
	function getProType(typevalue){
		var msg="";
		if(typevalue==1){
			msg ="作业调度";
		}else if(typevalue==2){
			msg = "信息采集";
		}else if(typevalue==3){
			msg = "应用变更";
		}else if(typevalue==4){
			msg = "灾备切换";
		}else if(typevalue==5){
			msg = "定时任务";
		}else if(typevalue==6){
			msg = "应急操作";
		}else if(typevalue==7){
			msg = "健康巡检";
		}else if(typevalue==8){
			if(pfFlag)
			{
				msg = "标准运维";
			}
			else
			{
				msg = "日常操作";
			}
			
		}else if(typevalue==9){
			msg = "服务启停";
		}else if(typevalue==16){
			msg = "应用维护";
		}else if(typevalue==40){
			msg = "AZ切换";
		}else{
			msg = "其它";
		}
		return msg;
	}
	
// function getTextById(_store, id) {
// var r = _store.findRecord("id", id);
// var text = "";
// if (r != null) {
// text = r.get("name");
// }
//	   
// return text;
// }
	
	/** 导出业务系统* */
	function exportOperationSystem(){
		var record = grid_panel.getSelectionModel().getSelection();
		if (record.length == 0)
		{
			Ext.Msg.alert ('提示', "请先选择您要操作的行!");
			return;
		}
		
		/*
		 * if (record.length > 20) { Ext.Msg.alert ('提示', "导出最多只能选择20行!");
		 * return; }
		 */
		
		var flag = false;
		
		
		var sysParam1 = "";
		var sysDesc1  = "";
		var priority1 = "";
		var sysType1 = "";
		
		var jsonData = "[";
		for ( var i = 0, len = record.length; i < len; i++) {
				
					// 4.7.28 定时任务业务系统也可以导出 wanglei
			         if(record[i].data.prjType==3 || record[i].data.prjType == 7 || record[i].data.prjType == 16 || record[i].data.prjType == 5){
			         	if(record[i].data.prjType == 7 || record[i].data.prjType == 5){
			         		var prjid = record[i].data.systemId;
			         		Ext.Ajax.request (
							{
						    url : 'getProInfo.do',
						    params :
						    {
						    	prjid : prjid
						    },
						    method : 'POST',
						    async:false,
						    success : function (response, opts)
						    {
						    	var respText=Ext.decode(response.responseText);
							    sysParam1 = respText.sysParam;
							    sysDesc1 = respText.sysDesc;
							    priority1 = respText.priority;
							    sysType1 =  respText.sysType;
							    
						    }
							});
			         	}
			         	
			        	 flag = true;
			        	 /*
							 * delete record[i].data.prjVersion; delete
							 * record[i].data.sysType; delete
							 * record[i].data.sysDesc; delete
							 * record[i].data.systemId; delete
							 * record[i].data.sysState; delete
							 * record[i].data.priority; delete
							 * record[i].data.sysParam; delete
							 * record[i].data.sendCycle; delete
							 * record[i].data.mails; delete
							 * record[i].data.mailSendStatus; delete
							 * record[i].data.pkgid; delete record[i].data.upid;
							 * delete record[i].data.freeze; delete
							 * record[i].data.runPrj; delete
							 * record[i].data.excelPrj;
							 */
			        	 if(record[i].data.prjType == 7 || record[i].data.prjType == 5){
			        		record[i].data.sysParam=sysParam1;
			        		record[i].data.sysDesc=sysDesc1;
			        		record[i].data.priority=priority1;
			        		record[i].data.sysType=sysType1;
			        	 }
			        	 var ss = Ext.JSON.encode(record[i].data);
			   	         if (i == 0)
		   	             	jsonData = jsonData + ss;
			   	         else
		   	             	jsonData = jsonData + "," + ss;
			   	     }else{
			   	    	Ext.Msg.alert('提示',"请选择工程类型为健康巡检或应用变更的业务系统或应用维护或定时任务的业务系统");
			   	    	flag = false;
			   	    	break;
			   	     }
		}
		jsonData = jsonData + "]";
		if(flag){
			Ext.Ajax.request({
			    url: 'testIsLimits.do',
			    method : 'POST',
			    params : {
			    	jsonData : jsonData
			    },
			    success: function(response, opts) {
			        var success = Ext.decode(response.responseText).success;
			        if (success) {
			        	window.location.href = 'exportOperationSystem.do';
			        }
			    },
			    failure: function(result, opts) {
			    	secureFilterRs(result,"请求返回失败！");
			    }
			  });
			
		}
	}
	var resImpWin = null;
	
	/** 弹出上传窗口* */
	/** 导入* */
	function openImportWindows_buSM() {
		
		var upLoadformPane =Ext.create('Ext.form.Panel', {
		    border:false,
		    bodyPadding : 10,
			items: [
				{
					xtype: 'filefield',
					name: 'file', // 设置该文件上传空间的name，也就是请求参数的名字
					fieldLabel: '选择文件',
					labelWidth: 80,
					labelStyle:'margin:10px 0 0 0',
					msgTarget: 'side',
					anchor: '100%',
					buttonText: '选择文件',
					height : 30
				},
				{
					xtype : 'toolbar',
					border : false,
					dock : 'top',
					items:['->',{
						xtype : 'button',
						cls : 'Common_Btn',
						text : '上传',
						handler: function() {
	
							var form = this.up('form').getForm();
							var upfile=form.findField("file").getValue();
							// var tmpFilNam=upfile;
			    			if(upfile==''){
			    				Ext.Msg.alert('提示',"请选择文件...");
			    				return ;
			    			}
	
			    			var fileFormat = upfile.substring(upfile.lastIndexOf('.'),upfile.length+1);
			    			
			    			if(fileFormat.toLowerCase() != '.json'){
			    				Ext.Msg.alert('提示',"请选择json文件...");
			    				return ;
			    			}
			    			
							 if (form.isValid()) {
								 	Ext.MessageBox.wait("数据处理中...", "进度条");
					                form.submit({	
					                	url: 'importOperationSystem.do',
					                    success: function(form, action) {
					                      var msg = Ext.decode(action.response.responseText).message;
					                      if(!Ext.decode(action.response.responseText).isExistisData){
					                    	  if(Ext.decode(action.response.responseText).isExistsystemNumber){
					                    		  Ext.Msg.alert('提示', '目标服务器已存在的系统编码信息无法导入',function(){
							                       		importOverrideFailedOperation();
					                    		  }); 
					                    	  }else{
					                    		  Ext.Msg.alert('提示',msg);  
					                    	  }
						            	  }else{
						                       Ext.Msg.alert('提示', '目标服务器已存在的业务系统信息无法导入',function(){
							                       		importOverrideFailedOperation();
						                       });
					                       }
					                       businessSystemStore.reload();
					                       resImpWin.hide();
					                    },
					                    failure: function(form, action) {
					                    	// secureFilterRsFrom(form,action);
					                    	var msg = Ext.decode(action.response.responseText).message;
					                    	var status = Ext.decode(action.response.responseText).status;
					                    	Ext.Msg.alert('导入失败', msg,function(){
						                    	if(status<0){
					                    				openOverrideWindow_buSM();
						                    	}
					                    	});
					                    	
					                    }
					                });
					          }
						}
					},
					{
						xtype : 'button',
						cls : 'Common_Btn',
						text : '重置',
						handler: function() {
							var form = this.up('form').getForm();
							form.reset();
							return;
						}
					}]
				}
			]
		});
		    resImpWin = Ext.create ('Ext.window.Window',
		    {
		        title : '导入',
		        autoScroll : true,
		        modal : true,
		        closeAction : 'destroy',
		        buttonAlign : 'center',
		        draggable : true,// 禁止拖动
		        resizable : false,// 禁止缩放
				width : 500,
				height : 190,
		        items:[upLoadformPane]
		    }).show ();
		}
	
		var override_queryform = "";
		var resoverride_Pane = "";
		/** 选择覆盖文件重复内容窗口* */
		function openOverrideWindow_buSM(){
			resImpWin.hide();
			Ext.define('dataModel', {
		        extend: 'Ext.data.Model',
		        fields: [
		                 {name: 'sysName',  type: 'string'},
		                 {name: 'prjType',  type: 'string'},
		                 {name: 'systemCode',   type: 'string'}
		                ]
		    });
			var overrideData_store = Ext.create('Ext.data.Store', {
		        autoLoad: true,
		        model: 'dataModel',
		        pageSize:30,
		        proxy: {
		            type: 'ajax',
		            url: 'getOverrideData.do',
		            reader: {
		                type: 'json',
		                root: 'dataList',
		                totalProperty : 'total'
		            }
		        }
		    });
		    
			var columns = [
	               {
	                   text : '序号',
                       width : 200,
                       sortable : false,
	            	   xtype: 'rownumberer',
	            	   align : 'left'
	               },
	               { 
		               text: '业务系统名称',  
		               dataIndex: 'sysName',
		               width: 330,
		               align : 'left'
		           },
	    	       { 
		        	   text: '工程类型', 
		        	   width: 200,
		        	   dataIndex: 'prjType',
		        	   align : 'left',
		        	   renderer:function(value){
		        	   	if(value == 3){
		        	   		return "应用变更";
		        	   	}
		        	   	if(value == 7){
		        	   		return "健康巡检";
		        	   	}
		        	   	if(value == 16){
		        	   		return "应用维护";
		        	   	}
		        	   	if(value == 40){
		        	   		return "AZ切换";
		        	   	}
		        	   }
		           },
	    	       { 
		        	   text: pfFlag?'系统简称':'系统编码', 
		        	   width: 313,
		        	   dataIndex: 'systemCode',
		        	   align : 'left'
		           }
	    	      ];
			resoverride_Pane = Ext.create('Ext.grid.Panel', {
				columns:columns,
				autoScroll:true,
				height:screen.height - 290,
	            border:false,
	            dockedItems: [{
		            xtype: 'toolbar'
		        }],
	            store: overrideData_store,
	            selModel:Ext.create('Ext.selection.CheckboxModel', {checkOnly : true})
			});
			
			override_queryform = Ext.create('Ext.form.Panel', {
		    	region: "north",
		    	border : true,
		    	layout : 'form',
		    	items : [{
					xtype : 'toolbar',
					border : false,
					dock : 'top',
					layout : 'column',
					items: [{
			             fieldLabel: '业务系统名称',
			             labelWidth: 90,
			             width : 300,
			             name: 'sysName',
			             xtype: 'textfield'
			         },
			         {
			             fieldLabel: '工程类型',
			             labelWidth: 65,
			             width : 300,
			             name: 'prjType',
			             xtype: 'textfield'
			         },
			         {
			             fieldLabel: pfFlag?'系统简称':'系统编码',
			             labelWidth: 65,
			             width : 300,
			             name: 'systemCode',
			             xtype: 'textfield'
			         },
			         {
			             xtype: 'button',
			             width: 60,
			             margin: '5 10 0 0',
			             cls : 'Common_Btn',
			             text: '覆盖',
			             handler: 
			            	 overrideOperationSystem
			         },
			         {
			             xtype: 'button',
			             width: 60,
			             margin: '5 10 0 0',
			             cls : 'Common_Btn',
			             text: '清空',
			             handler: 
			            	 override_clearQuery
			             
			         }]
		    	}]
			});

		}
		
		/** 条件清空* */
		function  override_clearQuery() {
		    override_queryform.getForm().findField("sysName").setValue('');
		    override_queryform.getForm().findField("prjType").setValue('');
		    override_queryform.getForm().findField("systemCode").setValue('');
		}
		
		/** 选择覆盖弹窗* */
		function overrideOperationSystem(){
			var record = resoverride_Pane.getSelectionModel().getSelection();
			var sysName = override_queryform.getForm().findField("sysName").getValue();
		    var prjType = override_queryform.getForm().findField("prjType").getValue();
		    var systemCode = override_queryform.getForm().findField("systemCode").getValue();
		    
		    if(!sysName){
		    	Ext.Msg.alert('提示',"业务系统名称不能为空!");
		    	return;
		    }
		    
		    if(prjType == "应用变更"){
		    	prjType = 3;
		    }
		    
		    if(prjType == "健康巡检"){
		    	prjType = 7;
		    }
		    
		    if(prjType == "应用维护"){
		    	prjType = 16;
		    }
		    
		    var overrideData = "{";
		    overrideData = overrideData+"sysName:"+'"'+sysName+'"'+",prjType:"+prjType+",systemCode:"+'"'+systemCode+'"'+"}";
			if (record.length == 0)
			{
				Ext.Msg.alert ('提示', "请先选择您要操作的行!");
				return;
			}
			
			// var flag = false;
			var jsonData = "[";
			for ( var i = 0, len = record.length; i < len; i++) {
				         if(record[i].data.prjType==3 || record[i].data.prjType == 7 || record[i].data.prjType == 16){
				        	 var ss = JSON.stringify(record[i].data);
				   	         if (i == 0)
				   	             jsonData = jsonData + ss;
				   	         else
				   	             jsonData = jsonData + "," + ss;
				   	     }
			}
			jsonData = jsonData + "]";
			
			Ext.Msg.confirm("请确认", "确定覆盖文件重复内容？", function(button, text) {
				if(button == "yes"){
					window.location.href = 'reExportOperationSystem.do?jsonData='+jsonData+'&overrideData='+overrideData;
				}
			});
			
		}
		/** 同步方法* */
		function syncOperationSystem(){
			var record = grid_panel.getSelectionModel().getSelection();
			if (record.length == 0)
			{
				Ext.Msg.alert ('提示', "请先选择您要操作的行!");
				return;
			}
			
			var flag = false;
			
			var sysParam1 = "";
			var sysDesc1  = "";
			var priority1 = "";
			var sysType1 = "";
			
			var jsonData = "[";
			for ( var i = 0, len = record.length; i < len; i++) {
				         if(record[i].data.prjType==3 || record[i].data.prjType == 7 || record[i].data.prjType == 16){
				         	if(record[i].data.prjType == 7){
				         		var prjid = record[i].data.systemId;
				         		Ext.Ajax.request (
								{
							    url : 'getProInfo.do',
							    params :
							    {
							    	prjid : prjid
							    },
							    method : 'POST',
							    async:false,
							    success : function (response, opts)
							    {
							    	var respText=Ext.decode(response.responseText);
								    sysParam1 = respText.sysParam;
								    sysDesc1 = respText.sysDesc;
								    priority1 = respText.priority;
								    sysType1 =  respText.sysType;
								    
							    }
								});
				         	}
				         	
				        	 flag = true;
				        	 if(record[i].data.prjType == 7){
				        	 	record[i].data.sysParam=sysParam1;
				        	 	record[i].data.sysDesc=sysDesc1;
				        	 	record[i].data.priority=priority1;
				        	 	record[i].data.sysType=sysType1;
				        	 }
				        	 var ss = Ext.JSON.encode(record[i].data);
				   	         if (i == 0)
			   	             	jsonData = jsonData + ss;
				   	         else
			   	             	jsonData = jsonData + "," + ss;
				   	     }else{
				   	    	Ext.Msg.alert('提示',"请选择工程类型为健康巡检或应用变更的业务系统或应用维护的业务系统");
				   	    	flag = false;
				   	    	break;
				   	     }
			}
			jsonData = jsonData + "]";
			
			
			if(flag){
				Ext.Msg.confirm("请确认", "确定同步业务系统基础信息？", function(button, text) {
				      if (button == "yes") {
				      	Ext.MessageBox.wait ("同步中...", "进度条");
				        Ext.Ajax.request({
				          url : 'syncOperationSystemInfo.do',
				          params : {
				        	  jsonData : jsonData
				          },
				          timeout: 300000,
				          method : 'POST',
				          success : function(response, opts) {
					            Ext.Msg.alert('提示', Ext.decode(response.responseText).message,function(){
									// 关闭后执行
					            	if(Ext.decode(response.responseText).isExistisData){
					            		showFailedOperationData();
					            	}
								});
					            businessSystemStore.reload();
				          },
				          failure : function(result, request) {
				          		secureFilterRs(result,"请求返回失败!");
				          }
				        });
				      }
				    });
			}
  }
	
	function syncSystem(){
		Ext.Msg.confirm("请确认", "确定同步业务系统？", function(button, text) {
			if (button == "yes") {
				Ext.MessageBox.wait("同步数据中...", "进度条");
				Ext.Ajax.request({
		          url : 'syncSystem.do',
		          method : 'POST',
				  timeout: 900000, 
		          params :{},
		          success : function(response, opts) {
			            var success = Ext.decode (response.responseText).success;
						var message = Ext.decode (response.responseText).message;
						if (success){
							Ext.Msg.alert("提示",message);
							businessSystemStore.reload();
						}else {
							if (null !=message){
								Ext.Msg.alert("提示",message);
							}
						}

		          },
		          failure : function(result, request) {
		            	Ext.Msg.alert("提示","系统同步失败");
		          }
	    		});
			}
		});
  	}
	
	var importOverOperation_Pane = "";
	var importOverOperationDataWin = "";
	/** 选择是否覆盖导入失败的数据* */
	function importOverrideFailedOperation(){
			Ext.define('failedOperationModel', {
	        extend: 'Ext.data.Model',
	        fields: [
	                 {name: 'iid',  type: 'long'},
	                 {name: 'sysName',  type: 'string'},
	                 {name: 'prjType',   type: 'string'},
	                 {name: 'systemCode', type: 'string'},
	                 {name: 'systemNumber', type: 'string'}
	                ]
	    	});
	    	var importOverOperation_store = Ext.create('Ext.data.Store', {
		        autoLoad: true,
		        model: 'failedOperationModel',
		        proxy: {
		            type: 'ajax',
		            url: 'getImportFailedOperationData.do',
		            reader: {
		                type: 'json',
		                root: 'dataList',
		                totalProperty : 'total'
		            }
		        }
		    });
		    
		    var importOverrideQueryForm = Ext.create('Ext.form.Panel', {
		    	region: "north",
		    	border : true,
		    	layout : 'form',
		    	items : [{
					xtype : 'toolbar',
					border : false,
					dock : 'top',
					layout : 'column',
					items: [
			         {
			             xtype: 'button',
			             width: 60,
			             margin: '5 10 0 0',
			             cls : 'Common_Btn',
			             text: '导入',
			             handler: 
			            	 importOverOperationSystem
			         }
			         ]
		    	}]
			});
		    
			var columns = [
	               {
	                   text : '序号',
                       width : 200,
                       sortable : false,
	            	   xtype: 'rownumberer',
	            	   align : 'left'
	               },
	               { 
		               text: '业务系统名称',  
		               dataIndex: 'sysName',
		               width: 330,
		               align : 'left'
		           },
	    	       { 
		        	   text: '工程类型', 
		        	   width: 200,
		        	   dataIndex: 'prjType',
		        	   align : 'left',
		        	   renderer:function(value){
		        	   	if(value == 3){
		        	   		return "应用变更";
		        	   	}
		        	   	if(value == 7){
		        	   		return "健康巡检";
		        	   	}
		        	   	if(value == 16){
		        	   		return "应用维护";
		        	   	}
		        	   }
		           },
	    	       { 
		        	   text: pfFlag?'系统简称':'系统编码', 
		        	   width: 323,
		        	   dataIndex: 'systemCode',
		        	   align : 'left'
		           },{
		   	            text :'系统编码',
		   	            dataIndex : 'systemNumber',
		   	            sorttable : true,
		   	            align : 'left',
		   	            hidden : cqnsFlag?false:true
		   	        }
	    	      ];
			importOverOperation_Pane = Ext.create('Ext.grid.Panel', {
				columns:columns,
				autoScroll:true,
				height:screen.height - 290,
	            border:false,
	            dockedItems: [{
		            xtype: 'toolbar'
		        }],
	            store: importOverOperation_store,
	            selModel:Ext.create('Ext.selection.CheckboxModel', {checkOnly : true})// ,
	            // bbar : importOverOperation_pageBar
			});
			
			importOverOperationDataWin = Ext.create ('Ext.window.Window',
		    {
		        title : '请选择要覆盖的业务系统信息',
		        autoScroll : true,
		        modal : true,
		        closeAction : 'destroy',
		        buttonAlign : 'center',
		        draggable : true,// 禁止拖动
		        resizable : false,// 禁止缩放
		        width : 1200,
		        height : screen.height - 190,
		        items:[importOverrideQueryForm,importOverOperation_Pane]
		    }).show ();
	}
	
	/** 覆盖导入失败的业务系统信息* */
	function importOverOperationSystem(){
		var record = importOverOperation_Pane.getSelectionModel().getSelection();
		if (record.length == 0)
		{
			Ext.Msg.alert ('提示', "请先选择您要操作的行!");
			return;
		}
		
		// var flag = false;
		var jsonData = "[";
		for ( var i = 0, len = record.length; i < len; i++) {
			         if(record[i].data.prjType==3 || record[i].data.prjType == 7 || record[i].data.prjType == 16){
			        	 var ss = JSON.stringify(record[i].data);// Ext.JSON.encode(record[i].data);
			   	         if (i == 0)
			   	             jsonData = jsonData + ss;
			   	         else
			   	             jsonData = jsonData + "," + ss;
			   	     }
		}
		jsonData = jsonData + "]";
		
		Ext.Ajax.request({
	          url : 'overrideImportFiledInfo.do',
	          timeout: 300000,
	          method : 'POST',
	          params :{
	             jsonData : jsonData
	          },
	          success : function(response, opts) {
	            Ext.Msg.alert('提示', Ext.decode(response.responseText).message,function(){
	            	importOverOperationDataWin.hide();
	            });
	            businessSystemStore.reload();
	          },
	          failure : function(result, request) {
	            Ext.Msg.alert("提示","导入失败！");
	          }
    	});
			
	}
	
	/** 展示同步失败数据* */
	function showFailedOperationData(){
			Ext.define('failedOperationModel', {
	        extend: 'Ext.data.Model',
	        fields: [
	                 {name: 'iid',  type: 'long'},
	                 {name: 'sysName',  type: 'string'},
	                 {name: 'prjType',   type: 'string'},
	                 {name: 'systemCode', type: 'string'}
	                ]
	    	});
	}
	
	
	prjTypeForQuery.setValue("0");
});

/** 一体化运维4.7.9 业务系统维护，作业调度专用功能 on 2018-03-12 by yue_sun start * */
var messageWindow;
function openProInfo(IID, isExcel, name, prjType) {
	if (messageWindow == undefined || !messageWindow.isVisible()) {
		messageWindow = Ext.create('Ext.window.Window', {
			title : '工程详细信息',
			modal : true,
			closeAction : 'destroy',
			constrain : true,
			autoScroll : true,
			width : contentPanel.getWidth() - 350,
			height : contentPanel.getHeight(),
			draggable : false,// 禁止拖动
			resizable : false,// 禁止缩放
			layout : 'fit',
			loader : {
				url : 'initProjectDetailForIEAI.do',
				params : {
					IID : IID,
					isExcel: isExcel,
					iname: name,
					prjType: prjType

				},
				autoLoad : true,
				autoDestroy : true,
				scripts : true
			}
		})
	}
	messageWindow.show();
}

// 下载作业调度工程PKG的方法
function exporIEAIPrjPKG(iid,prjName,prjVer,excelPrj){
	if(excelPrj > 0) {
		Ext.Msg.alert ('提示', 'Excel上传的工程不可以下载!请到作业依赖关系下载中下载Excel.');
		return ;
	}
	// var prjName = record.get('prjName');
	window.location.href = 'downloadPKGForIEAI.do?prjId='+iid+'&prjName='+prjName+'&prjVer='+prjVer;
}
/** 一体化运维4.7.9 业务系统维护，作业调度专用功能 on 2018-03-12 by yue_sun end * */


function synchronButtion(){
				Ext.MessageBox.wait("同步数据中...", "进度条");
				Ext.Ajax.request({
					url : 'downloadGDSftpXml.do',
					timeout : 30000,
					params : {
						
					},
					method : 'POST',
					success : function(response, opts) {
						var success = Ext.decode (response.responseText).success;
					    if (success)
					    {
						   Ext.Msg.alert('提示', '同步成功!');
					    }
					    else
					    {
						    Ext.Msg.alert ('提示', '同步失败！');
					    }
						businessSystemStore.reload();
					},
					failure : function(result, request) {
						secureFilterRs(result, "同步失败！");
					}
				});
}



