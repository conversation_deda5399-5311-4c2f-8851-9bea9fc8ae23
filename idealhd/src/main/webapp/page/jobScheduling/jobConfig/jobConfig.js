Ext.onReady(function() {
	// 先定义 prjNameStore，以便工具栏中的下拉框使用
	var prjNameStore = Ext.create('Ext.data.Store', {
		fields: ['iprjId', 'iprjName'],
		autoLoad: true,
		proxy: {
			type: 'ajax',
			url: 'actmonitor/getPrjName.do',
			reader: {
				type: 'json',
				root: 'dataList'
			}
		}
	});

	var jobConfigForm1 = Ext.create('Ext.form.Panel', {
		region : 'north',
		bodyCls:'x-docked-noborder-top',
		baseCls:'customize_gray_back',
		dockedItems : [ {
			xtype : 'toolbar',
			baseCls:'customize_gray_back',
			dock : 'top',
			items : [ {
				xtype : 'textfield',
				name : 'queryString',
				emptyText : '请输入查询条件',
				listeners: {
					specialkey: function(field,e){
						if (e.getKey()==Ext.EventObject.ENTER){
							jobConfigGrid1.ipage.moveFirst();
						}
					}
				}
			}, {
				xtype: 'combobox',
				name: 'querySystem',
				emptyText: '请选择所属系统',
				width: 300,
				store: prjNameStore,
				queryMode: 'local',
				displayField: 'iprjName',
				valueField: 'iprjName',
				editable: true,
				typeAhead: true,
				minChars: 0,
				selectOnFocus: true,
				forceSelection: true,
				hideTrigger: false,
				triggerAction: 'all',
				listeners: {
					blur: function(combo) {
						if (combo.getRawValue() && !combo.getValue()) {
							combo.setValue('');
							combo.setRawValue('');
						}
					}
				}
			}, {
				xtype : 'button',
				cls : 'Common_Btn',
				text : '查询',
				handler : queryString
			}, {
				xtype : 'button',
				cls : 'Common_Btn',
				text : '重置',
				handler : clearString
			},'->', {
				xtype : 'button',
				cls : 'Common_Btn',
				text : '增加',
				handler : addjobConfigGrid1
			}, {
				xtype : 'button',
				cls : 'Common_Btn',
				text : '保存',
				handler : savejobConfigGrid1
			}, {
				xtype : 'button',
				cls : 'Common_Btn',
				text : '删除',
				handler : deletejobConfigGrid1
			}, {
				xtype: 'button',
				cls: 'Common_Btn',
				text: '导入',
				handler: importjobConfigGrid1
			}, {
				xtype: 'button',
				cls: 'Common_Btn',
				text: '导出',
				handler: exportjobConfigGrid1
			} ]
		} ]
	});


	var iisencryptStore = Ext.create('Ext.data.Store', {
		fields: ['iid', 'iname'],
		data : [
			{"iid":"0", "iname":"否"},
			{"iid":"1", "iname":"是"}
		]
	});

	Ext.define('jobConfigGrid1Model', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'iid',
			type : 'string'
		}, {
			name : 'iparamname',
			type : 'string'
		}, {
			name : 'iparamcode',
			type : 'string'
		}, {
			name: 'iprjname',
			type: 'string'
		}, {
			name : 'iisencrypt',
			type : 'string'
		}, {
			name : 'iparamvalue',
			type : 'string'
		} ]
	});
	var jobConfigGrid1Store = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		pageSize : 30,
		model : 'jobConfigGrid1Model',
		proxy : {
			type : 'ajax',
			url : 'getJobConfigList.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}
	});

	jobConfigGrid1Store.on('beforeload', function (store, options) {
		var new_params = {
			queryString: jobConfigForm1.getForm().findField("queryString").getValue(),
			querySystem: jobConfigForm1.getForm().findField("querySystem").getValue()
		};
		Ext.apply(jobConfigGrid1Store.proxy.extraParams, new_params);
	});

	var gridEdit = Ext.create('Ext.grid.plugin.CellEditing', {
		clicksToEdit : 1
	});

	var defultEditor = Ext.create('Ext.grid.CellEditor',{
		field : Ext.create('Ext.form.field.Text',{
			selectOnFocus : true
		})
	});
	var passwordEditor = Ext.create('Ext.grid.CellEditor',{
		field : Ext.create('Ext.form.field.Text',{
			selectOnFocus : true,
			inputType : 'password'
		})
	});

	var jobConfigGrid1 = Ext.create('Ext.ux.ideal.grid.Panel', {
		region : 'center',
		cls:'customize_panel_back',
		ipageBaseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar',
		padding : grid_space,
		columns : [ {
			xtype : 'rownumberer',
			width : 60,
			text : '序号'
		}, {
			xtype : 'gridcolumn',
			hidden : true,
			dataIndex : 'iid',
			text : '主键'
		}, {
			xtype : 'gridcolumn',
			dataIndex : 'iparamname',
			flex : 1,
			text : '参数名称',
			editor : {

			}
		}, {
			xtype : 'gridcolumn',
			flex : 1,
			dataIndex : 'iparamcode',
			text : '参数编码',
			editor: {}
		}, {
			xtype: 'gridcolumn',
			flex: 1,
			dataIndex: 'iprjname',
			text: '所属系统',
			editor: {
				xtype: 'combobox',
				store: prjNameStore,
				queryMode: 'local',
				displayField: 'iprjName',
				valueField: 'iprjName',
				editable: true,
				typeAhead: true,
				minChars: 0,
				selectOnFocus: true,
				forceSelection: true,
				hideTrigger: false,
				triggerAction: 'all',
				listeners: {
					blur: function(combo) {
						if (combo.getRawValue() && !combo.getValue()) {
							combo.setValue('');
							combo.setRawValue('');
						}
					}
				}
			}
		}, {
			xtype : 'gridcolumn',
			dataIndex : 'iisencrypt',
			width : 100,
			text : '是否加密',
			editor : {
				xtype: 'combobox',
				store: iisencryptStore,
				queryMode: 'local',
				displayField: 'iname',
				valueField: 'iid',
				listeners:{
					change:function(field,newValue,oldValue){
						if(oldValue=1 && newValue!=1){
							var date = jobConfigGrid1.getView().getSelectionModel().getSelection()[0];
							date.set("iparamvalue","");
						}
					}
				}
			},
			renderer : function(value, p, record, rowIndex) {
				var backValue = "否";
				if(value=='1'){
					backValue = '是';
				}else{
					backValue = '否';
				}
				return backValue;
			}
		}, {
			xtype : 'gridcolumn',
			dataIndex : 'iparamvalue',
			flex : 1,
			text : '参数值',
			getEditor : function(record) {
				if (record.get('iisencrypt') == "0") {
					return defultEditor;
				} else {
					return passwordEditor
				}
			},
			renderer : function(value, p, record, rowIndex) {
				var backValue = "否";
				if(record.get('iisencrypt')=='1'){
					backValue = StringToPassword(value);
				}else{
					backValue = value;
				}
				return backValue;
			}
		},{
			text: '引用作业清单',
			dataIndex: 'opera',
			flex: 0.5,
			//width: 250,
			renderer: function(value, metaData, record) {
				var iparamname = record.get('iparamname');
				var iparamcode = record.get('iparamcode');
				return "<a href=\"#\" style=\"text-decoration:none;\" valign=\"middle\" onclick=\"selectJobConfigCiteInfo('" + iparamname
					+ "','" + iparamcode + "');\">" + "查看</a>&nbsp;&nbsp;"
			},
			sortable: false
		} ],
		selModel : Ext.create('Ext.selection.CheckboxModel', {}),
		plugins : [gridEdit],
		store : jobConfigGrid1Store
	});

	var switchConfigPanel1 = Ext.create('Ext.panel.Panel', {
		renderTo : 'jobConfig_div',
		height : contentPanel.getHeight() - modelHeigth,
		width : contentPanel.getWidth(),
		layout : 'border',
		items : [ jobConfigForm1, jobConfigGrid1 ]
	});
	contentPanel.on('resize', function() {
		switchConfigPanel1.setHeight(contentPanel.getHeight() - modelHeigth);
		switchConfigPanel1.setWidth(contentPanel.getWidth());
	});
	function queryString(){
		jobConfigGrid1.ipage.moveFirst();
	}
	function clearString(){
		jobConfigForm1.getForm().findField("queryString").setValue('');
		jobConfigForm1.getForm().findField("querySystem").setValue('');
	}
	function addjobConfigGrid1() {
		var p = {
			iid : '',
			iparamname : '',
			iparamcode : '',
			iprjname: '',
			iisencrypt : '0',
			iparamvalue : ''
		};
		jobConfigGrid1Store.insert(0, p);
	}
	function savejobConfigGrid1() {
		var m = jobConfigGrid1Store.getModifiedRecords();
		if (m.length < 1) {
			setMessage('您没有进行任何修改，无需保存');
			return;
		}
		var jsonData = "[";
		for (var i = 0, len = m.length; i < len; i++) {
			var ss = Ext.JSON.encode(m[i].data);
			if (i == 0)
				jsonData = jsonData + ss;
			else
				jsonData = jsonData + "," + ss;
		}
		jsonData = jsonData + "]";
		Ext.Ajax.request({
			url : 'saveJobConfig.do',
			method : 'POST',
			params : {
				jsonData : jsonData
			},
			success : function(response, request) {
				var success = Ext.decode(response.responseText).success;
				var message = Ext.decode(response.responseText).message;
				if (success) {
					jobConfigGrid1Store.load();
				}
				Ext.Msg.alert('提示', message);
			},
			failure : function(result, request) {
				secureFilterRs(result, '保存失败！');
			}
		});
	}

	function deletejobConfigGrid1() {
		var data = jobConfigGrid1.getView().getSelectionModel().getSelection();
		if (data.length == 0) {
			Ext.Msg.alert('提示', '请先选择您要操作的数据!');
			return;
		}

		// 收集所有选中的参数ID、名称和编码
		var params = [];
		Ext.Array.each(data, function (record) {
			var iid = record.get('iid');
			if (iid) {
				params.push({
					id: iid,
					name: record.get('iparamname'),
					code: record.get('iparamcode')
				});
			}
		});

		// 检查每个参数的引用情况
		var references = [];
		var hasReferences = false;
		var checkCount = 0;

		function checkReferences() {
			if (checkCount >= params.length) {
				// 所有检查完成后显示提示
				showDeleteConfirmation();
				return;
			}

			var param = params[checkCount];
			Ext.Ajax.request({
				url: 'JobConfigCiteList.do',
				params: {
					paramname: param.name,
					paramcode: param.code
				},
				method: 'POST',
				success: function (response) {
					var result = Ext.decode(response.responseText);
					if (result.success && result.dataList && result.dataList.length > 0) {
						hasReferences = true;
						references.push({
							paramName: param.name,
							references: result.dataList
						});
					}
					checkCount++;
					checkReferences();
				},
				failure: function () {
					// 即使检查失败也继续下一个
					checkCount++;
					checkReferences();
				}
			});
		}

		function showDeleteConfirmation() {
			var message = "是否删除选中的 " + params.length + " 个参数？";
			var deleteButton = "是";

			// 如果有引用，显示引用详情
			if (hasReferences) {
				message = "以下参数被作业引用，删除后将影响相关作业的执行，是否继续删除？\n\n";

				references.forEach(function (ref, index) {
					// 获取被引用的作业数量
					var jobCount = ref.references.length;

					// 显示参数名称和被引用的作业数量
					message += "参数-" + ref.paramName + "：被 " + jobCount + " 个作业引用\n";
				});

				deleteButton = "继续删除";
			}

			Ext.Msg.confirm("请确认", message, function (button, text) {
				if (button == "yes") {
					// 收集所有要删除的参数ID
					var iidlist = [];
					params.forEach(function (param) {
						iidlist.push(param.id);
					});

					Ext.Ajax.request({
						url: 'deleteJobConfig.do',
						params: {
							deleteIds: iidlist.join(',')
						},
						method: 'POST',
						success: function (response, opts) {
							var success = Ext.decode(response.responseText).success;
							if (success) {
								jobConfigGrid1Store.reload();
							}
							Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
						},
						failure: function (result, request) {
							secureFilterRs(result, "操作失败！");
						}
					});
				}
			});
		}

		// 开始检查引用
		checkReferences();
	}

	function exportjobConfigGrid1() {
		var queryString = jobConfigForm1.getForm().findField("queryString").getValue() || '';
		var querySystem = jobConfigForm1.getForm().findField("querySystem").getValue();
		var url = 'exportJobConfigToExcel.do?queryString=' + encodeURIComponent(queryString) + '&querySystem=' + querySystem;
		window.open(url);
	}

	function importjobConfigGrid1() {
		var fileInput = document.createElement('input');
		fileInput.type = 'file';
		fileInput.accept = '.xls,.xlsx';
		fileInput.style.display = 'none';

		fileInput.onchange = function () {
			if (this.files.length > 0) {
				uploadJobConfigFile(this.files[0]);
			}
		};

		document.body.appendChild(fileInput);
		fileInput.click();
		document.body.removeChild(fileInput);
	}

	function uploadJobConfigFile(file) {
		if (!file) {
			Ext.Msg.alert('提示', '请选择要导入的文件');
			return;
		}

		var fileName = file.name.toLowerCase();
		if (!fileName.endsWith('.xls') && !fileName.endsWith('.xlsx')) {
			Ext.Msg.alert('提示', '请选择Excel文件（.xls或.xlsx格式）');
			return;
		}

		var formData = new FormData();
		formData.append('file', file);

		var loadingMask = new Ext.LoadMask(Ext.getBody(), {
			msg: '正在导入，请稍候...'
		});
		loadingMask.show();

		var xhr = new XMLHttpRequest();
		xhr.open('POST', 'importJobConfigFromExcel.do', true);

		xhr.onload = function () {
			loadingMask.hide();
			try {
				var response = JSON.parse(xhr.responseText);
				if (response.success) {
					Ext.Msg.alert('提示', response.message, function () {
						jobConfigGrid1Store.reload();
					});
				} else {
					Ext.Msg.alert('错误', response.message || '导入失败');
				}
			} catch (e) {
				Ext.Msg.alert('错误', '导入响应解析失败');
			}
		};

		xhr.onerror = function () {
			loadingMask.hide();
			Ext.Msg.alert('错误', '导入请求失败');
		};

		xhr.send(formData);
	}

	function StringToPassword(strs){
		if(strs&&strs!=null&strs!=''){
			var password = '';
			for(var i=0;i<strs.length;i++){
				password = password + '●';
			}
			return password;
		}else{
			return '';
		}
	}
});

function selectJobConfigCiteInfo(iparamname, iparamcode) {
	// 创建数据模型
	Ext.define('JobConfigCiteModel', {
		extend: 'Ext.data.Model',
		fields: [
			{name: 'id', type: 'long'},
			{name: 'sysName', type: 'string'},
			{name: 'mainlinename', type: 'string'},
			{name: 'childproname', type: 'string'},
			{name: 'actname', type: 'string'},
		]
	});

	// 创建数据存储
	var JobConfigCiteStore = Ext.create('Ext.data.Store', {
		autoLoad: true,
		model: 'JobConfigCiteModel',
		proxy: {
			type: 'ajax',
			url: 'JobConfigCiteList.do',
			extraParams: {
				paramname: iparamname,
				paramcode: iparamcode
			},
			reader: {
				type: 'json',
				root: 'dataList'
			}
		}
	});

	// 创建窗口
	var JobConfigCiteWindow = Ext.create('Ext.window.Window', {
		title:  '数据源名称：'+ name ,
		modal: true,
		width: 1200,
		height: 400,
		layout: 'fit',
		items: [{
			xtype: 'grid',
			store: JobConfigCiteStore,
			columns: [
				{text: '序号', xtype: 'rownumberer', width: 60},
				{text: '业务系统ID', dataIndex: 'sysId', hidden: true},
				{
					text: '业务系统名称', 
					dataIndex: 'sysName', 
					flex: 1,
					renderer: function(value, metaData, record, rowIndex, colIndex) {
						if (value) {
							metaData.tdAttr = 'data-qtip="' + value + '"';
						}
						return value;
					}
				},
				{
					text: '主线名称', 
					dataIndex: 'mainlinename', 
					flex: 1,
					renderer: function(value, metaData, record, rowIndex, colIndex) {
						if (value) {
							metaData.tdAttr = 'data-qtip="' + value + '"';
						}
						return value;
					}
				},
				{
					text: '子系统名称', 
					dataIndex: 'childproname', 
					flex: 1,
					renderer: function(value, metaData, record, rowIndex, colIndex) {
						if (value) {
							metaData.tdAttr = 'data-qtip="' + value + '"';
						}
						return value;
					}
				},
				{
					text: '作业名称', 
					dataIndex: 'actname', 
					flex: 1,
					renderer: function(value, metaData, record, rowIndex, colIndex) {
						if (value) {
							metaData.tdAttr = 'data-qtip="' + value + '"';
						}
						return value;
					}
				}
			],
			viewConfig: {
				stripeRows: true
			}
		}]
	});

	JobConfigCiteWindow.show();
}