var winWidth = 730;         // 弹出页面宽度
var winHeight = 620; 		// 弹出页面高度\
var utPropsStore ="";
	Ext.onReady(function() {
	destroyRubbish();
	Ext.tip.QuickTipManager.init();
	//reloadGroup();
	Ext.define('utRecordModel', {
		extend : 'Ext.data.Model',
		fields : [ {
            name: 'IID',
            type: 'long'
        },
        {
            name: 'IUUID',
            type: 'string'
        },
        {
            name: 'INAME',
            type: 'string'
        }
        ,
        {
            name: 'IMAJVER',
            type: 'long'
        }
        ,
        {
            name: 'IUPLOADUSER',
            type: 'string'
        }
        ,
        {
            name: 'IUPLOADTIME',
            type: 'string'
        }
        ,
        {
            name: 'ICOMMENT',
            type: 'string'
        }
		]

	});

	Ext.define('utPropsModel', {
		extend : 'Ext.data.Model',
		fields : [ {
            name: 'IID',
            type: 'long'
        },
        {
            name: 'IUUID',
            type: 'string'
        },
        {
            name: 'INAME',
            type: 'string'
        }
        ,
        {
            name: 'IMAJVER',
            type: 'long'
        }
        ,
        {
            name: 'IUPLOADUSER',
            type: 'string'
        }
        ,
        {
            name: 'IUPLOADTIME',
            type: 'string'
        }
        ,
        {
            name: 'ICOMMENT',
            type: 'string'
        },
        {
        	name : 'excelPrj',
        	type : 'int'
        }
		]

	});

	/** *********************Store********************* */
	/** 任务列表数据源* */
	var utRecordStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		model : 'utRecordModel',
		pageSize : 6,
		proxy : {
			type : 'ajax',
			url : 'getProjectDetail.do?iid=' +IID ,// utLogInfoRecord.do
		
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'totalUT'
			}
		}
	});

 utPropsStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		model : 'utPropsModel',
		pageSize : 6,
		proxy : {
			type : 'ajax',
			url : 'getProjectHistory.do?iid='+IID+'&isExcel=' + isExcel + '&proName=' + iname,// utLogInfoRecord.do
			reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
		}
	});

	// Basic mask:
	var myMask = new Ext.LoadMask(messageWindow, {
		msg : "读取中..."
	});
//	utRecordStore.on('beforeload', function(store, options) {
//		myMask.show();
//		var new_params = {
//			state : state,
//			taskId : taskId
//		};
//		Ext.apply(utRecordStore.proxy.extraParams, new_params);
//	});

//	utPropsStore.on('beforeload', function(store, options) {
//		var new_params = {
//			state : state,
//			taskId : taskId
//		};
//		Ext.apply(utPropsStore.proxy.extraParams, new_params);
//	});

	utRecordStore.on('load', function(store, options, success) {
		myMask.hide();
		var reader = store.getProxy().getReader();
//		if (null != reader.jsonData && "" != reader.jsonData) {
			Ext.getCmp("proNameDetail").setValue(reader.jsonData.INAME);
			Ext.getCmp("proVersion").setValue(reader.jsonData.IMAJVER);
			Ext.getCmp("uUser").setValue(reader.jsonData.IUPLOADUSER);
			Ext.getCmp("uTime").setValue(reader.jsonData.IUPLOADTIME);
			Ext.getCmp("conment").setValue(reader.jsonData.ICOMMENT);
			Ext.getCmp("IUPLOADNUM").setValue(reader.jsonData.IUPLOADNUM);
			
			/*var group = Ext.getCmp("group1");
			for (var i = 0; i < reader.jsonData.operations.length; i++) {
				var data = reader.jsonData.operations[i];
				group.add(new Ext.form.field.Radio({
					boxLabel : data.name,
					name : 'op',
					inputValue : data.id
				}));
			}*/
//		} else {
//			Ext.Msg.alert('提示', "无权限查看该任务信息!");
//		}
	});

	var utHomeForm = Ext.create('Ext.form.Panel', {
		layout : 'anchor',
		region : 'center',
		buttonAlign : 'center',
		border : false,
		items : [ {
			layout : 'column',
			anchor : '95%',
			padding : '5 0 5 0',
			border : false,
			items : [ {
				id : "proNameDetail",
				fieldLabel : '工程名称',
				labelAlign : 'right',
				labelWidth : 90,
				height : 30,
				width : 400,
				padding : '5 0 5 5',
				xtype : 'textfield'
			}, {
				id : "proVersion",
				fieldLabel : '最新版本',
				labelAlign : 'right',
				labelWidth : 90,
				height : 30,
				width : 400,
				padding : '5 0 5 5',
				xtype : 'textfield'
			}, {
				id : 'uUser',
				fieldLabel : '上载者',
				labelAlign : 'right',
				labelWidth : 90,
				height : 30,
				padding : '5 0 5 5',
				width : 400,
				xtype : 'textfield'
			}, {
				id : 'uTime',
				fieldLabel : '上载时间',
				labelAlign : 'right',
				labelWidth : 90,
				height : 30,
				padding : '5 0 5 5',
				width : 400,
				xtype : 'textfield'
			}, 
			{
				id : 'IUPLOADNUM',
				fieldLabel : '上载次数',
				labelAlign : 'right',
				labelWidth : 90,
				height : 30,
				padding : '5 0 5 5',
				width : 400,
				xtype : 'textfield'
			}, 
			{
				id : 'conment',
				fieldLabel : '说明',
				labelAlign : 'right',
				labelWidth : 90,
				height : 30,
				padding : '5 0 5 5',
				width : 400,
				xtype : 'textfield'
			}/*, {
				fieldLabel : '经办人',
				labelAlign : 'right',
				labelWidth : 90,
				height : 30,
				padding : '5 0 5 5',
				width : 400,
				xtype : 'textfield'
			}, {
				fieldLabel : '接管时间',
				labelAlign : 'right',
				labelWidth : 90,
				height : 30,
				padding : '5 0 5 5',
				width : 400,
				xtype : 'textfield'
			}, {
				id : 'description',
				fieldLabel : '描述',
				labelAlign : 'right',
				labelWidth : 90,
				height : 30,
				padding : '5 0 5 5',
				width : 400,
				xtype : 'textarea'
			}, {
				id : 'remark',
				fieldLabel : '备注',
				labelAlign : 'right',
				labelWidth : 90,
				height : 200,
				padding : '5 0 5 5',
				width : contentPanel.getWidth() - 420,
				xtype : 'textarea'
			}, {
				id : 'opDesc',
				fieldLabel : '操作说明',
				labelAlign : 'right',
				labelWidth : 90,
				hidden : true,
				height : 70,
				padding : '5 0 5 5',
				width : 804,
				xtype : 'textarea'
			}, {
				xtype : 'radiogroup',
				fieldLabel : '可执行操作',
				labelAlign : 'right',
				labelWidth : 90,
				name : "group",
				id : "group1",
				padding : '5 0 5 5',
				width : 804,
				height : 130,
				vertical : true,
				items : []
			} */]
		} ]
		/*buttons : [ {

			// xtype : 'button',
			// itemes
			// columns : 4,
			// buttonAlign: 'center',//居中
			text : '关闭',
			// width:60,
			handler : function() {
				option();
			}
		} ]*/
	});

	var columns = [ {
		text : 'iid',
		dataIndex : 'IID',
		hidden : true
	}, {
		text : '序号',
		xtype : 'rownumberer',
		width : 40
	}, {
		text : '工程名',
		dataIndex : 'INAME',
		align : 'left',
		flex : 1
	}
	, {
		text : '版本',
		dataIndex : 'IMAJVER',
		align : 'left',
		flex : 1
	}
	, {
		text : '上载者',
		dataIndex : 'IUPLOADUSER',
		align : 'left',
		flex : 1
	}
	, {
		text : '上载时间',
		dataIndex : 'IUPLOADTIME',
		align : 'left',
		flex : 1
	}
	, {
		text : '说明',
		dataIndex : 'ICOMMENT',
		align : 'left',
		flex : 1
	},
    {
        text : '是否excel上传工程',
        dataIndex : 'excelPrj',
        hidden : true,
        flex : 1
    },
    {
        text : '下载',
        sortable : true,
        dataIndex : 'prjHisDownload',
        renderer : function(value, p, record, rowIndex) {
			var systemId = record.get ('IID');
			var prjName = record.get ('INAME');
			return '<a href="javascript:void(0)"><img src="images/download.gif" align="absmiddle"></img></a>';
		},
        listeners :
        {
            click : function (a, b, c, d, e, record)
            {
            	exporIEAIPrjPKG(record.get ('IID'), record.get ('INAME'), record.get('IMAJVER'), record.get('excelPrj'));
            }
        }
	},
		{
			text : '工程回退版本',
			sortable : true,
			dataIndex : 'prjHisDownload1',
			flex : 1,
			renderer: function(value, p, record, rowIndex){
				/*var systemId = record.get ('IID');
				var prjName = record.get ('INAME');*/
				var bbIid =record.get ('IID'); var iname =record.get ('INAME'); var imajver =record.get('IMAJVER') ;  var excelprj =record.get('excelPrj');
				return '<a href="javascript:void(0);" onclick="openButtonWindow(\''+bbIid+ '\',\''+iname+ '\',\''+imajver+ '\',\''+excelprj+ '\')">回退版本</a>';
			}
		},
		{
			text : '晋级',
			sortable : true,
			dataIndex : 'promotion',
			flex : 0.3,
			renderer: function(value, p, record, rowIndex){
				/*var systemId = record.get ('IID');
				var prjName = record.get ('INAME');*/
				var bbIid =record.get ('IID');
				var excelPrj =record.get ('excelPrj');
				return '<a href="javascript:void(0);" onclick="promotionFun(\''+bbIid+ '\',\''+excelPrj+ '\')">晋级</a>';
			}
		},
		{
			text : '增量晋级',
			sortable : true,
			dataIndex : 'incrementalPromotion',
			flex : 0.5,
			renderer: function(value, p, record, rowIndex){
				// 只在最近版本（第一行）显示增量晋级链接
				if (rowIndex === 0) {
					// 检查工程类型和Excel上传条件
					if (prjType == '1' && isExcel > 0) {
						var bbIid = record.get('IID');
						return '<a href="javascript:void(0);" onclick="incrementalPromotionFun(\'' + bbIid + '\')">增量晋级</a>';
					}
				}
				return '';
			}
		}
	
	];

	var utPropsGrid = Ext.create('Ext.grid.Panel', {
		autoScroll : true,
		width : contentPanel.getWidth() - 400,
		height : contentPanel.getHeight() - 150,
		border : false,
		selType : 'cellmodel',
		autoScoll : true,
		plugins : [ Ext.create('Ext.grid.plugin.CellEditing', {
			clicksToEdit : 1
		}) ],
		store : utPropsStore,
		columns : columns
	});

	var tabPanel = Ext.create('Ext.tab.Panel', {
		items : [ {
			title : '工程信息',
			items : [ utHomeForm ]
		}, {
			title : '历史版本',
			items : [ utPropsGrid ]
		} ]

	});

	var workItemRecord_mainPanel = Ext.create('Ext.panel.Panel', {
		renderTo : "ut_grid",
		width : '100%',
		height : '100%',
		autoScroll :false,
		border : false,
		bodyPadding : 5,
		layout : 'border',
		items : [ tabPanel ]
	});

	contentPanel.getLoader().on("beforeload", function(obj, options, eOpts) {
		Ext.destroy(workItemRecord_mainPanel);
		if (Ext.isIE) {
			CollectGarbage();
		}
	});

});
function option() {
	var jobRadio = Ext.getCmp('group1').items;
	var ddd = "";
	for (var i = 0; i < jobRadio.items.length; i++) {
		var da = jobRadio.items[i];
		if (da.checked == true) {
			ddd = da.inputValue + "";
			break;
		}

	}
	if (ddd == null || ddd == "") {
		Ext.Msg.alert('提示', "请选择可执行的操作！");
		return;
	}
	// Ext.Msg.confirm('系统提示', '您确定要进行此操作吗?', function(btn) {
	// if (btn == 'yes') {
	Ext.Ajax.request({
		url : 'utRecordOptionForJob.do',// stepStop.do
		method : 'POST',
		params : {
			opId : ddd,
			taskId : taskId
		},
		success : function(response, request) {
			// var success = Ext.decode(response.responseText).success;
			// var message = Ext.decode(response.responseText).message;
			// if (success) {
			// Ext.Msg.alert('提示', message);
			// } else {
			// Ext.Msg.alert('提示', message);
			// }
			messageWindow.close();
		},
		failure : function(result, request) {
			secureFilterRs(result, "操作失败！");
		}
	});
	// }
	// })

}

//下载作业调度工程PKG的方法
function exporIEAIPrjPKG(iid,prjName,prjVer,excelPrj){
	console.log(dgBusSwitch)
	if (dgBusSwitch === 'true') {
		if(excelPrj > 0 ) {
			Ext.Msg.confirm("提示",'确认下载吗?', function(btn){
				if (btn == 'yes'){ // 提交数据
					window.location.href = 'exportTaskVersionExcel.do?prjName='+prjName
						+ "&version="+prjVer
						+ "&iid="+iid;
				}
			});
			return;
		}
	}else {
		if(excelPrj > 0) {
			Ext.Msg.alert ('提示', 'Excel上传的工程不可以下载!请到作业依赖关系下载中下载Excel.');
			return ;
		}
	}
	//var prjName = record.get('prjName');
	window.location.href = 'downloadPKGForIEAI.do?prjId='+iid+'&prjName='+prjName+'&prjVer='+prjVer;
}
// 身份认证
function userIdentity(prjId,prjName,prjVer,excelPrj){
	var userIdentityForm  = Ext.create('Ext.form.Panel', {
		border: false,
		width: '100%',
		layout: 'anchor',
		region: 'center',
		collapsible : false,
		dockedItems : [
			{
				xtype : 'toolbar',
				border : false,
				dock : 'top',
				baseCls:'customize_gray_back',
				items:[{
					fieldLabel: '用户名',
					id:'checkUser',
					name:'checkUser',
					labelAlign: 'right',
					width : '98%',
					labelWidth : 79,
					xtype: 'textfield',
				}]
			}, {
				xtype : 'toolbar',
				border : false,
				dock : 'top',
				baseCls:'customize_gray_back',
				items:[{
					fieldLabel: '密码',
					id:'password',
					name:'password',
					labelAlign: 'right',
					width : '98%',
					labelWidth : 79,
					xtype: 'textfield',
					inputType: 'password'
				}]
			}]
	});

	var userIdentityPanel = Ext.create('Ext.panel.Panel', {
		layout : 'border',
		border : true,
		region : 'center',
		//renderTo:"top_userinfo_area",
		items : [
			userIdentityForm
		]
	});

	var userIdentityWin = Ext.create ('Ext.window.Window',
		{
			title : '身份认证',
			modal : true,
			closeAction : 'destroy',
			buttonAlign : 'center',
			draggable : true,// 禁止拖动
			resizable : false,// 禁止缩放
			layout : 'fit',
			items : [userIdentityPanel],
			width : winWidth*0.8,
			height : winHeight*0.6,
			buttons :[{
				xtype: "button",
				text: "确定",
				handler: function () {
						var form_checkUser = userIdentityForm.getForm ().findField ("checkUser").getRawValue();
						var form_password = userIdentityForm.getForm ().findField ("password").getRawValue();
						if (trim (form_checkUser) == '' || (null == form_checkUser))
						{
							Ext.MessageBox.alert ("提示", "请输入用户名");
							return false;
						}

						if (trim (form_password) == '' || (null == form_password))
						{
							Ext.MessageBox.alert ("提示", "请输入密码");
							return false;
						}
					Ext.Msg.confirm("提示",'确认回退到当前版本?', function(btn){
							if (btn == 'yes'){ // 提交数据
								/*var base64_p1 =new Base64();
                                var form_password_Base64 = base64_p1.encode(form_password);*/
								Ext.Ajax.request({
									//url : 'rollbackPKGIEAI.do',
									url : 'promotionBusinessSystemLocal.do',
									method : 'POST',
									params : {
										systemIds : prjId,
										//prjName : prjName,
										//prjVer : prjVer,
										excelPrjs : excelPrj,
										local : "localhost"
										//checkUser : form_checkUser, //审核人
										//passWord : form_password 	// 未加密密码
									},
									success : function(response, request) {
										var success = Ext.decode(response.responseText).success;
										var message = Ext.decode(response.responseText).message;
										var PrjId1 =Ext.decode(response.responseText).PrjId;
										if(success){
											setMessage(message);
											userIdentityWin.close();
											//utPropsStore.close();
											searchDelayStart(PrjId1)
										} else {
											setMessage(message);
											return;
										}
								//		myMask.hide ();
									},
									failure : function(result, request) {
										Ext.Msg.alert('提示', '保存失败, 请联系管理员!');
									//	myMask.hide ();
									}
								});
							}
						});

				}
			},{
				xtype: "button",
				text: "取消",
				handler: function () {
					userIdentityForm.getForm().reset();
					userIdentityWin.close();
				}
			}]
		}).show();
	userIdentityWin.center();
}
function searchDelayStart(PrjId1){
	utPropsStore.load({
			params : {
				prjId1 : PrjId1
			}
	}
	);
	console.log(businessSystem_mainPanel);
	bsPageBar.moveFirst ();

}
function openButtonWindow(bbIid,prjName,prjVer,excelPrj){
	// if(excelPrj > 0) {
	// 	Ext.Msg.alert ('提示', 'Excel上传的工程不可以恢复版本!');
	// 	return ;
	// }else {
	// 	// 身份认证
	// 	userIdentity(bbIid,prjName,prjVer,excelPrj);
	// }
	userIdentity(bbIid,prjName,prjVer,excelPrj);
}

function promotionFun(bbIid,excelPrj) {
	// 添加确认弹窗
	Ext.Msg.confirm('确认晋级', '确定要晋级选中的工程吗？', function(btn) {
		if (btn === 'yes') {
			// 显示等待提示
			Ext.MessageBox.wait('晋级处理中...', '进度条');

			// 发送AJAX请求
			Ext.Ajax.request({
				url: 'promotionBusinessSystem.do',
				method: 'POST',
				params: {
					systemIds: bbIid, // 使用传入的工程ID
					excelPrjs: excelPrj //
				},
				timeout: 30000,
				success: function(response, opts) {
					Ext.MessageBox.hide(); // 隐藏进度条
					var result = Ext.decode(response.responseText);
					if (result.success) {
						// 重新加载历史版本的数据
						utPropsStore.reload();
						Ext.Msg.alert('成功', result.msg || '晋级操作成功！');
					} else {
						Ext.Msg.alert('失败', result.msg || '晋级操作失败！');
					}
				},
				failure: function(response, opts) {
					Ext.MessageBox.hide(); // 隐藏进度条
					Ext.Msg.alert('错误', '请求超时或服务器错误！');
				}
			});
		}
	});
}
function setMessage(msg) {
	Ext.Msg.alert('提示', msg);
}

// 增量晋级相关变量
var incrementalPromotionWindow = null;
var jobSelectionGrid = null;
var jobSelectionStore = null;

/**
 * 增量晋级主函数
 */
function incrementalPromotionFun(systemId) {
    var sysName = iname; // 使用全局变量iname作为系统名称

    // 检查工程类型是否为作业调度（prjType=1）
    if (prjType != '1') {
        Ext.Msg.alert('提示', '只能选择作业调度类型的工程进行增量晋级！');
        return;
    }

    // 检查是否为Excel上传工程（isExcel > 0）
    if (isExcel <= 0) {
        Ext.Msg.alert('提示', '只能选择Excel上传类型的工程进行增量晋级！');
        return;
    }

    // 检查是否为主流程（不是子系统）
    Ext.Ajax.request({
        url: 'checkIsMainProject.do',
        method: 'POST',
        params: {
            systemId: systemId,
            sysName: sysName
        },
        success: function(response, opts) {
            var result = Ext.decode(response.responseText);
            if (result.success) {
                if (result.isMainProject) {
                    // 是主流程，可以进行增量晋级
                    showJobSelectionWindow(systemId, sysName);
                } else {
                    Ext.Msg.alert('提示', '子系统不能晋级！');
                }
            } else {
                Ext.Msg.alert('错误', result.message || '检查工程类型失败！');
            }
        },
        failure: function(response, opts) {
            Ext.Msg.alert('错误', '检查工程类型时发生网络错误！');
        }
    });
}

/**
 * 显示作业选择窗口
 */
function showJobSelectionWindow(systemId, sysName) {
    // 创建作业选择的数据模型
    Ext.define('JobSelectionModel', {
        extend: 'Ext.data.Model',
        fields: [
            {name: 'actName', type: 'string'},
            {name: 'actDesc', type: 'string'},
            {name: 'projectName', type: 'string'},
            {name: 'flowName', type: 'string'},
            {name: 'selected', type: 'boolean', defaultValue: false}
        ]
    });

    // 创建作业选择的数据源
    jobSelectionStore = Ext.create('Ext.data.Store', {
        model: 'JobSelectionModel',
        pageSize: 20,  // 设置分页大小
        proxy: {
            type: 'ajax',
            url: 'getJobListForIncremental.do',
            reader: {
                type: 'json',
                root: 'jobList',
                totalProperty: 'totalCount'
            }
        },
        autoLoad: false
    });

    // 创建搜索框
    var jobSearchField = Ext.create('Ext.form.field.Text', {
        fieldLabel: '搜索作业',
        labelWidth: 80,
        width: 300,
        emptyText: '输入作业名称进行搜索...',
        listeners: {
            change: function(field, newValue) {
                filterJobs(newValue);
            }
        }
    });

    // 创建作业选择表格
    jobSelectionGrid = Ext.create('Ext.grid.Panel', {
        store: jobSelectionStore,
        height: 400,
        columns: [
            {
                xtype: 'checkcolumn',
                text: '选择',
                dataIndex: 'selected',
                width: 60,
                listeners: {
                    checkchange: function(column, recordIndex, checked) {
                        updateSelectedCount();
                    }
                }
            },
            {
                text: '作业名称',
                dataIndex: 'actName',
                flex: 2
            },
            {
                text: '作业描述',
                dataIndex: 'actDesc',
                flex: 2
            },
            {
                text: '主线名称',
                dataIndex: 'flowName',
                flex: 1
            }
        ],
        tbar: [
            jobSearchField,
            '->',
            {
                text: '全选',
                cls: 'Common_Btn',
                handler: function() {
                    selectAllJobs(true);
                }
            },
            {
                text: '清空',
                cls: 'Common_Btn',
                handler: function() {
                    selectAllJobs(false);
                }
            }
        ],
        bbar: Ext.create('Ext.PagingToolbar', {
            store: jobSelectionStore,
            displayInfo: true,
            baseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
            border: false,
            items: [
                '->',
                '已选择：',
                {
                    xtype: 'label',
                    itemId: 'selectedCountLabel',
                    text: '0 个作业'
                }
            ]
        })
    });

    // 创建增量晋级窗口
    incrementalPromotionWindow = Ext.create('Ext.window.Window', {
        title: '增量晋级 - ' + sysName,
        width: 800,
        height: 600,
        modal: true,
        layout: 'fit',
        items: [jobSelectionGrid],
        buttons: [
            {
                text: '确认增量晋级',
                cls: 'Common_Btn',
                handler: function() {
                    performIncrementalPromotion(systemId, sysName);
                }
            },
            {
                text: '取消',
                cls: 'Common_Btn',
                handler: function() {
                    incrementalPromotionWindow.close();
                }
            }
        ]
    });

    // 加载作业数据
    jobSelectionStore.load({
        params: {
            systemIds: [systemId]
        },
        callback: function(records, operation, success) {
            if (success) {
                incrementalPromotionWindow.show();
                updateSelectedCount();
            } else {
                Ext.Msg.alert('错误', '获取作业列表失败！');
            }
        }
    });
}

/**
 * 过滤作业列表
 */
function filterJobs(searchText) {
    if (!jobSelectionStore) return;

    jobSelectionStore.clearFilter();
    if (searchText && searchText.trim() !== '') {
        jobSelectionStore.filter([
            {
                property: 'actName',
                value: searchText,
                anyMatch: true,
                caseSensitive: false
            }
        ]);
    }
}

/**
 * 全选/清空作业
 */
function selectAllJobs(selectAll) {
    if (!jobSelectionStore) return;

    jobSelectionStore.each(function(record) {
        record.set('selected', selectAll);
    });
    updateSelectedCount();
}

/**
 * 更新选中数量显示
 */
function updateSelectedCount() {
    if (!jobSelectionGrid) return;

    var count = 0;
    jobSelectionStore.each(function(record) {
        if (record.get('selected')) {
            count++;
        }
    });

    var label = jobSelectionGrid.down('#selectedCountLabel');
    if (label) {
        label.setText(count + ' 个作业');
    }
}

/**
 * 执行增量晋级
 */
function performIncrementalPromotion(systemId, sysName) {
    // 获取选中的作业
    var selectedJobs = [];
    jobSelectionStore.each(function(record) {
        if (record.get('selected')) {
            selectedJobs.push(record.get('actName'));
        }
    });

    if (selectedJobs.length === 0) {
        Ext.Msg.alert('提示', '请至少选择一个作业！');
        return;
    }

    // 确认增量晋级
    Ext.Msg.confirm('确认增量晋级',
        '确定要对选中的 ' + selectedJobs.length + ' 个作业进行增量晋级吗？',
        function(btn) {
            if (btn === 'yes') {
                // 关闭选择窗口
                incrementalPromotionWindow.close();

                // 显示等待提示
                Ext.MessageBox.wait('增量晋级处理中...', '进度条');

                // 发送AJAX请求
                Ext.Ajax.request({
                    url: 'incrementalPromotionBusinessSystem.do',
                    method: 'POST',
                    params: {
                        systemIds: [systemId],
                        selectedJobs: selectedJobs
                    },
                    timeout: 300000, // 5分钟超时
                    success: function(response, opts) {
                        Ext.MessageBox.hide(); // 隐藏进度条

                        var result;
                        try {
                            // 尝试直接解析JSON
                            result = Ext.decode(response.responseText);
                        } catch (e) {
                            // 如果解析失败，可能是多个JSON对象拼接，尝试提取最后一个
                            console.warn('JSON解析失败，尝试提取最后一个JSON对象: ', e.message);
                            console.log('原始响应: ', response.responseText);

                            try {
                                var responseText = response.responseText;
                                var lastBraceIndex = responseText.lastIndexOf('}');

                                if (lastBraceIndex > 0) {
                                    // 从最后一个}向前查找对应的{
                                    var braceCount = 1;
                                    var startIndex = lastBraceIndex - 1;
                                    while (startIndex >= 0 && braceCount > 0) {
                                        if (responseText.charAt(startIndex) === '}') {
                                            braceCount++;
                                        } else if (responseText.charAt(startIndex) === '{') {
                                            braceCount--;
                                        }
                                        startIndex--;
                                    }

                                    if (braceCount === 0) {
                                        var lastJsonObject = responseText.substring(startIndex + 1, lastBraceIndex + 1);
                                        console.log('提取的最后一个JSON对象: ', lastJsonObject);
                                        result = Ext.decode(lastJsonObject);
                                    } else {
                                        throw new Error('无法找到完整的JSON对象');
                                    }
                                } else {
                                    throw new Error('响应中没有找到JSON对象');
                                }
                            } catch (parseError) {
                                console.error('提取JSON对象失败: ', parseError.message);
                                result = { success: false, msg: '响应解析失败：' + parseError.message };
                            }
                        }

                        if (result.success) {
                            // 强制重新加载历史版本的数据，清除缓存
                            utPropsStore.removeAll();
                            utPropsStore.load();

                            // 显示成功消息
                            Ext.Msg.alert('成功', result.msg || '增量晋级操作成功！');
                        } else {
                            Ext.Msg.alert('失败', result.msg || '增量晋级操作失败！');
                        }
                    },
                    failure: function(response, opts) {
                        Ext.MessageBox.hide(); // 隐藏进度条
                        Ext.Msg.alert('错误', '请求超时或服务器错误！');
                    }
                });
            }
        }
    );
}
