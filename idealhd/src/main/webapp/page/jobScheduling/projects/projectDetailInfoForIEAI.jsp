<%@ page import="com.ideal.ieai.server.ieaikernel.ServerEnv" %>
<%@page contentType="text/html; charset=utf-8"%>
<%

String IID=request.getParameter("IID");
String iname=request.getParameter("iname");
String isExcel=request.getParameter("isExcel");
String prjType=request.getParameter("prjType");
%>

<html>
<script type="text/javascript">
	var IID="<%=IID%>";
	var iname="<%=iname%>";
    var isExcel="<%=isExcel%>";
    var prjType="<%=prjType%>";
    var dgBusSwitch= '<%=ServerEnv.getServerEnv().getBooleanConfig(ServerEnv.IEAI_SYS_BUSINESS_EXCEL_HIS_EXPORT, false)%>';
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/jobScheduling/projects/projectDetailInfoForIEAI.js"></script>
</head>
<body>
<div id="ut_grid" style="width: 100%;height: 100%">
</div>
</body>
</html>