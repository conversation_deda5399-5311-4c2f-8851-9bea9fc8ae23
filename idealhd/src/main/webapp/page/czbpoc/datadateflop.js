Ext.onReady(function() {
	var dataDateForm1 = Ext.create('Ext.form.Panel', {
		region : 'north',
		bodyCls:'x-docked-noborder-top',
		baseCls:'customize_gray_back',
		dockedItems : [ {
			xtype : 'toolbar',
			baseCls:'customize_gray_back',
			dock : 'top',
			items : [ {
				xtype : 'combobox',
				name : 'queryString',
				fieldLabel: '',
				emptyText : '请选择或输入查询条件',
				displayField: 'name',
				valueField: 'name',
				editable: true,
				queryMode: 'local',
				triggerAction: 'all',
				typeAhead: true,
				minChars: 1,
				forceSelection: true,
				store: Ext.create('Ext.data.Store', {
					fields: ['name'],
					autoLoad: true,
					proxy: {
						type: 'ajax',
						url: 'getIeaiProject.do',
						reader: {
							type: 'json',
							root: 'dataList'
						}
					}
				}),
				listeners: {
					select: function(combo, records) {
						dataDateGrid1.ipage.moveFirst();
					},
					beforequery: function(queryEvent) {
						// 允许模糊匹配
						queryEvent.query = new RegExp(Ext.String.escapeRegex(queryEvent.query), 'i');
						queryEvent.forceAll = true;
					},
					specialkey: function(field, e) {
						if (e.getKey() == Ext.EventObject.ENTER) {
							dataDateGrid1.ipage.moveFirst();
						}
					}
				}
			}, {
				xtype : 'button',
				cls : 'Common_Btn',
				text : '查询',
				handler : queryString
			}, {
				xtype : 'button',
				cls : 'Common_Btn',
				text : '重置',
				handler : clearString
			},'->', {
				xtype : 'button',
				cls : 'Common_Btn',
				text : '增加',
				handler : adddataDateGrid1
			}, {
				xtype : 'button',
				cls : 'Common_Btn',
				text : '保存',
				handler : savedataDateGrid1
			}, {
				xtype : 'button',
				cls : 'Common_Btn',
				text : '删除',
				handler : deletedataDateGrid1
			} ]
		} ]
	});

	Ext.define('dataDateGrid1Model', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'iid',
			type : 'string'
		}, {
			name : 'isystemName',
			type : 'string'
		}, {
			name : 'idataDate',
			type : 'string'
		}, {
			name : 'iupdateTime',
			type : 'string'
		} , {
			name : 'icallTime',
			type : 'string'
		} , {
			name : 'ioffset',
			type : 'string'
		} , {
			name : 'icutTime',
			type : 'string'
		} , {
			name : 'ibusinessDate',
			type : 'string'
		} ]
	});
	var dataDateGrid1Store = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		pageSize : 30,
		model : 'dataDateGrid1Model',
		proxy : {
			type : 'ajax',
			url : 'getDataDateFlopList.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}
	});

	dataDateGrid1Store.on('beforeload', function (store, options) {
	    var combo = dataDateForm1.getForm().findField("queryString");
	    var new_params = {
	    	queryString: combo.getValue() || ''
	    };
	    Ext.apply(dataDateGrid1Store.proxy.extraParams, new_params);
    });

	var gridEdit = Ext.create('Ext.grid.plugin.CellEditing', {
		clicksToEdit : 1
	});

	var dataDateGrid1 = Ext.create('Ext.ux.ideal.grid.Panel', {
		region : 'center',
		cls:'customize_panel_back',
		ipageBaseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar',
		padding : grid_space,
		columns : [ {
			xtype : 'rownumberer',
			width : 60,
			text : '序号'
		}, {
			xtype : 'gridcolumn',
			hidden : true,
			dataIndex : 'iid',
			text : '主键'
		}, {
			xtype : 'gridcolumn',
			dataIndex : 'isystemName',
			flex : 1,
			text : '业务系统',
			editor : {
				xtype: 'combobox',
				displayField: 'name',
				valueField: 'name',
				editable: true,
				queryMode: 'local',
				triggerAction: 'all',
				typeAhead: true,
				minChars: 1,
				forceSelection: true,
				allowBlank: false,
				emptyText: '请选择或输入业务系统',
				listeners: {
					beforequery: function(queryEvent) {
						// 允许模糊匹配
						queryEvent.query = new RegExp(Ext.String.escapeRegex(queryEvent.query), 'i');
						queryEvent.forceAll = true;
					}
				},
				store: Ext.create('Ext.data.Store', {
					fields: ['name'],
					autoLoad: true,
					proxy: {
						type: 'ajax',
						url: 'getIeaiProject.do',
						reader: {
							type: 'json',
							root: 'dataList'
						}
					}
				})
			}
		}, {
			xtype : 'gridcolumn',
			flex : 1,
			dataIndex : 'idataDate',
			text : '数据日期',
			editor : {
				xtype: 'textfield',
				allowBlank: false,
				emptyText: '请输入8位数字',
				maxLength: 8,
				enforceMaxLength: true,
				regex: /^\d{8}$/,
				regexText: '数据日期必须是8位数字',
				listeners: {
					blur: function(field) {
						var value = field.getValue();
						if(value && !/^\d{8}$/.test(value)) {
							Ext.Msg.alert('提示', '数据日期必须是8位数字');
							field.focus(true, 100);
						}
					}
				}
			}
		},  {
			xtype : 'gridcolumn',
			dataIndex : 'ioffset',
			flex : 1,
			text : '偏移量',
			editor : {
				xtype: 'textfield',
				allowBlank: true,
				emptyText: '请输入偏移量，如T+1或T-1',
				maxLength: 50,
				enforceMaxLength: true,
				regex: /^T[+-]\d+$/,
				regexText: '偏移量必须是T+1或T-1这种格式',
				listeners: {
					blur: function(field) {
						var value = field.getValue();
						if(value && !/^T[+-]\d+$/.test(value)) {
							Ext.Msg.alert('提示', '偏移量必须是T+1或T-1这种格式');
							field.focus(true, 100);
						}
					}
				}
			}
		},  {
			xtype : 'gridcolumn',
			dataIndex : 'icutTime',
			flex : 1,
			text : '日切时间',
			editor : {
				xtype: 'textfield',
				allowBlank: true,
				emptyText: '请输入时间，如09:30或23:59',
				maxLength: 5,
				enforceMaxLength: true,
				regex: /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/,
				regexText: '日切时间必须是HH:mm格式，如09:30',
				listeners: {
					blur: function(field) {
						var value = field.getValue();
						if(value && !/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(value)) {
							Ext.Msg.alert('提示', '日切时间必须是HH:mm格式，如09:30');
							field.focus(true, 100);
						}
					}
				}
			}
		},  {
			xtype : 'gridcolumn',
			dataIndex : 'ibusinessDate',
			flex : 1,
			text : '业务日期'
			// 业务日期不允许编辑
		},  {
			xtype : 'gridcolumn',
			dataIndex : 'icallTime',
			flex : 1,
			text : '核心修改时间',
			// 核心修改时间不允许编辑
		} ,  {
			xtype : 'gridcolumn',
			dataIndex : 'iupdateTime',
			flex : 1,
			text : '创建(修改)时间'
		} ],
		selModel : Ext.create('Ext.selection.CheckboxModel', {}),
		plugins : [gridEdit],
		store : dataDateGrid1Store
	});

	var switchConfigPanel1 = Ext.create('Ext.panel.Panel', {
		renderTo : 'dataDateFlop_div',
		height : contentPanel.getHeight() - modelHeigth,
		width : contentPanel.getWidth(),
		layout : 'border',
		items : [ dataDateForm1, dataDateGrid1 ]
	});
	contentPanel.on('resize', function() {
		switchConfigPanel1.setHeight(contentPanel.getHeight() - modelHeigth);
		switchConfigPanel1.setWidth(contentPanel.getWidth());
	});
	function queryString(){
		dataDateGrid1.ipage.moveFirst();
	}
	function clearString(){
		var combo = dataDateForm1.getForm().findField("queryString");
		combo.clearValue();
		combo.reset();
	}
	function adddataDateGrid1() {
		var p = {
			iid : '',
			isystemname : '',
			idatadate : '',
			icutTime : ''
		};
		dataDateGrid1Store.insert(0, p);

		// 添加后自动开始编辑业务系统字段
		setTimeout(function() {
			try {
				// 业务系统列索引，包含序号列
				gridEdit.startEdit(0, 2);
			} catch(e) {
				console.log(e);
			}
		}, 100);
	}
	function savedataDateGrid1() {
		var m = dataDateGrid1Store.getModifiedRecords();
		if (m.length < 1) {
			Ext.Msg.alert('提示', '您没有进行任何修改，无需保存');
			return;
		}

		// 验证数据
		var invalidDateRecords = [];
		var invalidSystemRecords = [];
		var invalidOffsetRecords = [];
		var invalidFlopTimeRecords = [];

		Ext.each(m, function(record) {
			// 验证数据日期格式
			var dataDate = record.get('idataDate');
			if (!dataDate || !/^\d{8}$/.test(dataDate)) {
				invalidDateRecords.push(record);
			}

			// 验证业务系统是否存在
			var systemName = record.get('isystemName');
			if (!systemName) {
				invalidSystemRecords.push(record);
			}

			// 验证偏移量格式
			var offset = record.get('ioffset');
			if (offset && !/^T[+-]\d+$/.test(offset)) {
				invalidOffsetRecords.push(record);
			}

			// 验证日切时间格式
			var flopTime = record.get('icutTime');
			if (flopTime && !/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(flopTime)) {
				invalidFlopTimeRecords.push(record);
			}

			// 业务日期不允许编辑，无需校验
		});

		if (invalidDateRecords.length > 0) {
			Ext.Msg.alert('提示', '数据日期必须是8位数字，请检查后重新保存');
			return;
		}

		if (invalidSystemRecords.length > 0) {
			Ext.Msg.alert('提示', '业务系统不能为空，请检查后重新保存');
			return;
		}

		if (invalidOffsetRecords.length > 0) {
			Ext.Msg.alert('提示', '偏移量必须是T+1或T-1这种格式，请检查后重新保存');
			return;
		}

		if (invalidFlopTimeRecords.length > 0) {
			Ext.Msg.alert('提示', '日切时间必须是HH:mm格式，如09:30，请检查后重新保存');
			return;
		}
		var jsonData = "[";
		for (var i = 0, len = m.length; i < len; i++) {
			var ss = Ext.JSON.encode(m[i].data);
			if (i == 0)
				jsonData = jsonData + ss;
			else
				jsonData = jsonData + "," + ss;
		}
		jsonData = jsonData + "]";
		Ext.Ajax.request({
			// url : 'saveSwitchConfig.do',
			url : 'saveDataDateFlop.do',
			method : 'POST',
			params : {
				jsonData : jsonData
			},
			success : function(response, request) {
				var success = Ext.decode(response.responseText).success;
				var message = Ext.decode(response.responseText).message;
				if (success) {
					dataDateGrid1Store.reload();
					Ext.Msg.alert('提示',message);
				} else {
					Ext.Msg.alert('提示', message);
				}
			},
			failure : function(result, request) {
				secureFilterRs(result, '保存失败！');
			}
		});
	}
	function deletedataDateGrid1() {
		var data = dataDateGrid1.getView().getSelectionModel().getSelection();
		if (data.length == 0) {
			Ext.Msg.alert('提示', '请先选择您要操作的数据!');
			return;
		} else {
			Ext.Msg.confirm("请确认","是否真的要删除该数据？",function(button, text) {
				if (button == "yes") {
					var ids = [];
					Ext.Array.each(data, function(record) {
						var iid = record.get('iid');
						if (iid != "" && null != iid) {
							ids.push(iid);
						}
					});
					if (ids.length <= 0) {
						return;
					}
					Ext.Ajax.request({
						url : 'deleteDataDate.do',
						params : {
							deleteIds : ids.join(',')
						},
						method : 'POST',
						success : function(response, opts) {
							var success = Ext.decode(response.responseText).success;
							if (success) {
								dataDateGrid1Store.reload();
								Ext.Msg.alert('提示',Ext.decode(response.responseText).message);
							} else {
								Ext.Msg.alert('提示','删除失败！');
							}
						},
						failure : function(result, request) {
							secureFilterRs(result,'操作失败！');
						}
					});
				}
			});
		}
	}

	function StringToPassword(strs){
		if(strs&&strs!=null&strs!=''){
			var password = '';
			for(var i=0;i<strs.length;i++){
				password = password + '●';
			}
			return password;
		}else{
			return '';
		}
	}
});