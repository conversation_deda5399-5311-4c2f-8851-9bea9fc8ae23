-- 贯标管理表
-- 用于存储业务系统贯标信息，支持工程名校验

-- Oracle版本
CREATE TABLE IEAI_STANDARD_CODE (
    IID NUMERIC(19) NOT NULL,
    ISTANDARDCODE VARCHAR2(50) NOT NULL,  -- 贯标代码（英文唯一）
    BUSINESS_SYSTEM_NAME VARCHAR2(100) NOT NULL, -- 业务系统中文名（唯一）
    ICREATETIME DATE DEFAULT SYSDATE,     -- 创建时间
    ICREATEUSER VARCHAR2(255),            -- 创建用户
    CONSTRAINT PK_IEAI_STANDARD_CODE PRIMARY KEY (IID),
    CONSTRAINT UK_IEAI_STANDARD_CODE UNIQUE (ISTANDARDCODE),
    CONSTRAINT UK_IEAI_STANDARD_CODE_NAME UNIQUE (BUSINESS_SYSTEM_NAME)
);

-- 创建序列
CREATE SEQUENCE SEQ_IEAI_STANDARD_CODE
START WITH 1
INCREMENT BY 1
NOCACHE;

-- 添加注释
COMMENT ON TABLE IEAI_STANDARD_CODE IS '贯标管理表';
COMMENT ON COLUMN IEAI_STANDARD_CODE.IID IS '主键ID';
COMMENT ON COLUMN IEAI_STANDARD_CODE.ISTANDARDCODE IS '贯标代码（英文唯一）';
COMMENT ON COLUMN IEAI_STANDARD_CODE.BUSINESS_SYSTEM_NAME IS '业务系统中文名（唯一）';
COMMENT ON COLUMN IEAI_STANDARD_CODE.ICREATETIME IS '创建时间';
COMMENT ON COLUMN IEAI_STANDARD_CODE.ICREATEUSER IS '创建用户';

-- MySQL版本
/*
CREATE TABLE IF NOT EXISTS IEAI_STANDARD_CODE (
    IID BIGINT NOT NULL AUTO_INCREMENT,  -- 使用AUTO_INCREMENT自动生成ID
    ISTANDARDCODE VARCHAR(50) NOT NULL,   -- 贯标代码（英文唯一）
    BUSINESS_SYSTEM_NAME VARCHAR(100) NOT NULL, -- 业务系统中文名（唯一）
    ICREATETIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    ICREATEUSER VARCHAR(255),             -- 创建用户
    CONSTRAINT PK_IEAI_STANDARD_CODE PRIMARY KEY (IID),
    CONSTRAINT UK_IEAI_STANDARD_CODE UNIQUE (ISTANDARDCODE),
    CONSTRAINT UK_IEAI_STANDARD_CODE_NAME UNIQUE (BUSINESS_SYSTEM_NAME)
);
*/

-- 插入一些示例数据
INSERT INTO IEAI_STANDARD_CODE (IID, ISTANDARDCODE, BUSINESS_SYSTEM_NAME, ICREATEUSER) VALUES (SEQ_IEAI_STANDARD_CODE.NEXTVAL, 'CRM', '客户关系管理系统', 'admin');
INSERT INTO IEAI_STANDARD_CODE (IID, ISTANDARDCODE, BUSINESS_SYSTEM_NAME, ICREATEUSER) VALUES (SEQ_IEAI_STANDARD_CODE.NEXTVAL, 'ERP', '企业资源计划系统', 'admin');
INSERT INTO IEAI_STANDARD_CODE (IID, ISTANDARDCODE, BUSINESS_SYSTEM_NAME, ICREATEUSER) VALUES (SEQ_IEAI_STANDARD_CODE.NEXTVAL, 'OA', '办公自动化系统', 'admin');
INSERT INTO IEAI_STANDARD_CODE (IID, ISTANDARDCODE, BUSINESS_SYSTEM_NAME, ICREATEUSER) VALUES (SEQ_IEAI_STANDARD_CODE.NEXTVAL, 'HR', '人力资源管理系统', 'admin');
INSERT INTO IEAI_STANDARD_CODE (IID, ISTANDARDCODE, BUSINESS_SYSTEM_NAME, ICREATEUSER) VALUES (SEQ_IEAI_STANDARD_CODE.NEXTVAL, 'FIN', '财务管理系统', 'admin');

COMMIT;
