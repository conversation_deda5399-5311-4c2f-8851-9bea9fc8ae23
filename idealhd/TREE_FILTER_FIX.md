# 树节点过滤问题修复说明

## 问题描述
在活动监控页面中，点击树中的 item 后再点击查询按钮，仍然带着点击树的条件，导致不能查询出全部数据。

## 问题原因
1. **extraParams 持久化问题**：当点击树节点时，代码设置了 `actMonitorActListStroe.proxy.extraParams`，这些参数会一直保留在 store 的 proxy 中。
2. **查询按钮未清除树节点参数**：查询按钮的 `submitIns()` 函数使用 `load()` 方法传递新的参数，但没有清除之前设置的 `extraParams`。
3. **参数叠加问题**：新的查询参数和树节点的过滤参数同时存在，导致查询结果仍然受到树节点选择的限制。

## 解决方案

### 1. 修改 `submitIns()` 函数
在查询按钮的处理函数中添加清除逻辑：
```javascript
function submitIns() {
    // 清除树节点相关的过滤参数，确保查询按钮能查询全部数据
    actMonitorActListStroe.proxy.extraParams = {};
    
    // 清除树节点选择状态
    if (treePanel && treePanel.getSelectionModel) {
        treePanel.getSelectionModel().deselectAll();
    }
    
    // ... 原有的查询逻辑
}
```

### 2. 修改 `submitIns1()` 函数
为拓扑组名称查询也添加相同的清除逻辑：
```javascript
function submitIns1() {
    // 清除树节点相关的过滤参数，确保查询按钮能查询全部数据
    actMonitorActListStroe.proxy.extraParams = {};
    
    // 清除树节点选择状态
    if (treePanel && treePanel.getSelectionModel) {
        treePanel.getSelectionModel().deselectAll();
    }
    
    // ... 原有的查询逻辑
}
```

### 3. 修改 `filterActList()` 函数
在活动类型下拉框的过滤函数中也添加清除树节点选择的逻辑：
```javascript
function filterActList(actType, prjName) {
    // 清除树节点选择状态，因为这是通过下拉框进行的过滤
    if (treePanel && treePanel.getSelectionModel) {
        treePanel.getSelectionModel().deselectAll();
    }
    
    // ... 原有的过滤逻辑
}
```

## 修改的文件
- `idealhd/src/main/webapp/page/jobScheduling/actmonitor/actMonitor.js`

## 修改的函数
1. `submitIns()` - 主查询按钮处理函数
2. `submitIns1()` - 拓扑组名称查询处理函数  
3. `filterActList()` - 活动类型过滤函数

## 测试验证

### 测试步骤
1. **树节点选择测试**：
   - 点击树中的某个工程或工作流节点
   - 验证数据列表是否正确过滤显示该节点相关的数据
   - 验证树节点是否保持选中状态

2. **查询按钮清除测试**：
   - 在树节点选中的状态下，在查询条件框中输入条件
   - 点击查询按钮
   - 验证是否显示了基于查询条件的全部数据（不受树节点限制）
   - 验证树节点选择状态是否被清除

3. **活动类型过滤测试**：
   - 在树节点选中的状态下，改变活动类型下拉框的值
   - 验证是否显示了基于活动类型的全部数据（不受树节点限制）
   - 验证树节点选择状态是否被清除

4. **刷新功能测试**：
   - 在树节点选中的状态下，点击刷新按钮
   - 验证是否显示了全部数据
   - 验证树节点选择状态是否被清除

### 预期结果
- 点击查询按钮后，应该能够查询出全部符合查询条件的数据，不受之前树节点选择的影响
- 树节点的选择状态应该被清除，视觉上不再显示任何节点被选中
- 各种查询和过滤功能应该独立工作，互不干扰

## 技术细节

### extraParams 的作用
- `extraParams` 是 ExtJS Store Proxy 的一个属性，用于在每次请求时自动添加额外的参数
- 这些参数会持久化保存，直到被显式清除或覆盖
- 当使用 `load()` 方法时，传入的 `params` 会与 `extraParams` 合并发送到服务器

### 清除策略
- **完全清除**：`actMonitorActListStroe.proxy.extraParams = {}` 清除所有额外参数
- **选择性清除**：也可以删除特定的参数，如 `delete actMonitorActListStroe.proxy.extraParams.treeFlowName`
- **视觉反馈**：通过 `treePanel.getSelectionModel().deselectAll()` 清除树节点的选中状态，提供视觉反馈

这个修复确保了用户界面的行为符合预期，查询按钮能够独立于树节点选择进行全局查询。
