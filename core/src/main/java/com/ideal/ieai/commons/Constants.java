package com.ideal.ieai.commons;

import com.ideal.ieai.core.JudgeDB;
import com.ideal.ieai.core.version.Version;

/**
 * <p>
 * Title: communication
 * </p>
 * <p>
 * Description:
 * </p>
 * <p>
 * Copyright: Copyright (c) 2006
 * </p>
 * <p>
 * Company: ideal
 * </p>
 *
 * <AUTHOR> copy from 1.0
 * <AUTHOR>
 * @version 3.0
 */
public class Constants
{
    /**
     * Used on server to distinguish if a String represents a name of a project, adaptor, all
     * projects or all adaptors
     */
    public static final int      ITEMTYPE_JOBSCHEDULING_BATCH                 = 20;
    public static final String[] COL_ITEM_JOBSCHEDULING_BATCH                 = { "FLOWIDS", "type" };
    // 手动和自动设备采集的三种状态
    public static final Integer COLLECTSTATUS_RUNNIBG                = 10;// 正在采集
    public static final Integer COLLECTSTATUS_BREAK                  = 5;// 采集终止
    public static final Integer COLLECTSTATUS_FINISH                 = 15;// 采集完成

    public static final short   PRJ                                  = 0;
    public static final short   ALL_PRJ                              = 1;
    public static final short   ADP                                  = 2;
    public static final short   ALL_ADP                              = 3;
    public static final short   MESSAGE                              = 4;
    public static final short   NOTICE                               = 5;
    public static final short   ONLINE                               = 6;
    // update start by yan_wang
    public static final short   CALENDAR                             = 7;
    // update end by yan_wang
    public static final short   HD                                   = 4;
    public static final short   HDHC                                 = 5;
    public static final short   HDPZ                                 = 100;
    public static final short   HD_N                                 = 7;
    public static final short   SUS_AUTO                             = 8;
    // 健康检查权限设置
    public static final short   HC                                   = 7;

    // 工程类型定义，该类型定义也和分组相同，即作业调度的类型为1，其集群分组的作业调度组ID也是1
    public static final short   IEAI_IEAI_BASIC                      = 0;                                            // 自身数据源
    public static final short   IEAI_OPM                             = -1;                                          // 运维管理
    public static final short   IEAI_API                             = -1;                                          // API管理
    public static final short   IEAI_IEAI                            = 1;                                            // 作业调度
    public static final short   IEAI_INFOCOLLECTION                  = 2;                                             // 信息采集
    public static final short   IEAI_SUS                             = 3;                                            // 变更管理
    public static final short   IEAI_EMERGENCY_SWITCH                = 4;                                            // 灾备切换
    public static final short   IEAI_TIMINGTASK                      = 5;                                            // 定时任务
    public static final short   IEAI_EMERGENCY_OPER                  = 6;                                            // 应急操作
    public static final short   IEAI_CICD                            = 38;                                            // 变更管理_CICD
    public static final int IEAI_CICD_FLOWTYPE_NORMAL   =0;
    public static final int IEAI_CICD_FLOWTYPE_CD       =1;
    public static final int IEAI_CICD_FLOWTYPE_CI       =2;
    public static final String IEAI_CICD_HDFB_NO        ="1";
    public static final String IEAI_CICD_HDFB_YES       ="2";
    public static final String IEAI_HDFB_POLICY_PERSONT    ="1";  //百分比
    public static final String IEAI_HDFB_POLICY_T_PRIORITY ="2";  //优先级

    // 健康巡检为信息采集下的健康巡检模块，该值用于studio上传的的类型区分，不作为菜单以及大类型区分，信息采集下还包含一个一致性比对模板
    /**
     * 健康巡检为信息采集下的健康巡检模块，该值用于studio上传的的类型区分，不作为菜单以及大类型区分，信息采集下还包含一个一致性比对模板
     */
    public static final short   IEAI_HEALTH_INSPECTION               = 7;                                            // 健康巡检
    public static final short   IEAI_DAILY_OPERATIONS                = 8;                                            // 日常操作

    public static final short   IEAI_SERVER_START_AND_STOP           = 9;                                            // 吉林农信应用服务启停，数据源与灾备使用同一个

    public static final short   IEAI_SCRIPT_SERVICE                  = 10;                                          // 脚本服务化

    public static final short   IEAI_NOTENABLEEQUIP                  = 99;                                         // 未启用设备

    public static final short   IEAI_COMPARE                         = 13;                                          // 一致性比对

    public static final short   IEAI_CMDB                            = 15;                                          // CMDB配置管理

    public static final short   IEAI_APM                             = 16;                     // 应用维护

    public static final short   IEAI_CI                              = 18;                       // CI构建

    public static final short   IEAI_FILE_ISSUANCE                   = 35;                     // 文件下发

    public static final short   IEAI_SCRIPT_ANALYZE                  = 36;                                          // 分析算法

    public static final short   IEAI_TOPO                            = 20;                                          // 拓扑图

    public static final short   IEAI_SCRIPT_DBAAS                    = 30;                                          // 脚本服务化-数据库服务

    public static final short   IEAI_INFO_COLLECT                    = 19;                                         // 信息采集[递蓝科源]

    public static final short   IEAI_FSH                             = 22;                                  // 故障自愈

    public static final short   IEAI_DATA_COLLECT                    = 23;                                  // 数据采集

    public static final short   IEAI_AZ                              = 40;                                         // AZ切换
    public static final short   IEAI_RPA                             = 41;                                         // RPA机器人
    public static final short   IEAI_NET                             = 24;          //网络自动化数据源
    public static final short  IEAI_SUS_NET                          = 31;          // 网络自动化网络变更
    public static final short   IEAI_NET_EM                          = 311;         //网络自动化应急
    public static final short   IEAI_COMBINATION                     = 25;          //组合模板数据源
    public static final short   IEAI_SUS_GROUP                       = 300;         //变更组合模板数据源
    public static final short   IEAI_AZ_GROUP                        = 400;         //AZ组合模板数据源
    public static final short   IEAI_TOOLS                           = 505;         //工具箱数据源
    public static final short   IEAI_SYS_CHANGE                      = 51;          //系统变更数据源
    public static final short   IEAI_EMS                             = 52;          // 外管报送数据源
    public static final short   IEAI_BMI                             = 53;          // 裸机安装数据源
    public static final short   IEAI_DATA_CHANGE                     = 50;          //数据变更数据源
    public static final short  IEAI_PAAS_CLOUD                       = 26;          // 中台云管
    public static final short   IEAI_LOOPHOLE_MANAGE                 = 199;

    public static final short  IEAI_PAAS                             = 27;


    public static final short  IEAI_ROUTINE_TASKS                         = 47;  //例行任务

    public static final short  IEAI_SHUTDOWN_MAINTAIN                     = 28;  //关机维护

    // 日常维护 模板工程 常量信息
    public static final String  MAINPRJNAME                          = "日常维护模板";
    public static final String  MAINFLOWNAME                         = "healthMain";
    public static final String  SONFLOWNAME                          = "healthCheck";
    public static final String  SECONDFLOWNAME                       = "healthConcurrent";
    public static final String  SECONDACTNAME                        = "工作流调用";
    public static final String  SECONDFLOWNAME_PARAM                 = "agentip";
    public static final String  FLOWINPUT                            = "flowInput";
    public static final String  COMPUTERNAME_PARAM                   = "computerip";
    // 日常维护，类型，
    public static final Long    IEAI_DAILY_NORMAL                    = 1L;                                           // 旧版日常维护
    public static final Long    IEAI_DAILY_STANDARD                  = 2L;                                           // 新版日常维护
    /**
     * Used in ProjectManager on server, to distingush the state of a project or adaptor in DB.
     */
    // public static final short NORMAL = 0;
    // public static final short FREE = 0;
    public static final short   ACTION_LOCK                          = 1;
    public static final short   ACTION_FREEZE                        = 2;
    public static final short   ACTION_UNLOCK                        = 3;
    public static final short   ACTION_UNFREEZE                      = 4;

    /**
     * If a given permission has been specifically enabled, disabled or not set
     */
    public static final int     PERM_ENABLED                         = 1;
    public static final int     PERM_DISABLED                        = 2;
    public static final int     PERM_UNDEFINED                       = 3;

    /**
     * Used in ResourceGroupInfo on server..
     */
    public static final int     RESOURCE_IEAI                        = 0;
    public static final int     RESOURCE_HC                          = 1;
    public static final int     RESOURCE_SUS                         = 2;
    public static final int     RESOURCE_COM                         = 3;
    public static final int     RESOURCE_OTHER                       = 4;
    public static final int     YES_FLOW                             = 0;
    public static final int     BACH_FLOW                            = 1;
    public static final int     HC_NOHC                              = 0;
    public static final int     HC_ISHC                              = 1;

    /**
     * Permissions <br>
     * IMPORTANT: permission strings must be <= 8 characters long, for DB table.
     * <p>
     * New: combined "Edit" and "Update" permissions into a singl "Change" permission.
     */
    public static final String  PERM_DOWNLOAD_PROJECT                = "pDownP";
    public static final String  PERM_UPLOAD_PROJECT                  = "pUpldP";
    public static final String  PERM_DELETE_PROJECT                  = "pDelP";
    public static final String  PERM_FREEZE_PROJECT                  = "pFrzP";
    public static final String  PERM_UNFREEZE_PROJECT                = "pUfrzP";
    public static final String  PERM_VIEW_VERHISTORY_PROJECT         = "pVerhP";
    public static final String  PERM_VIEW_LOGHISTORY_PROJECT         = "pLoghP";

    public static final String  PERM_DOWNLOAD_ADAPTOR                = "pDownA";
    public static final String  PERM_UPLOAD_ADAPTOR                  = "pUpldA";
    public static final String  PERM_DELETE_ADAPTOR                  = "pDelA";
    public static final String  PERM_FREEZE_ADAPTOR                  = "pFrzA";
    public static final String  PERM_UNFREEZE_ADAPTOR                = "pUfrzA";
    public static final String  PERM_VIEW_VERHISTORY_ADAPTOR         = "pVerhA";
    public static final String  PERM_VIEW_LOGHISTORY_ADAPTOR         = "pLoghA";

    public static final String  PERM_FLOW_START                      = "pFlwStrt";
    public static final String  PERM_FLOW_STOP                       = "pFlwStop";
    public static final String  PERM_FLOW_PAUSE                      = "pFlwPause";
    public static final String  PERM_FLOW_GETINFO                    = "pFlwGInf";
    public static final String  PERM_FLOW_KILL                       = "pFlwKill";

    // /////////////////////permission for task////////////////////////////////
    public static final String  PERM_TASK_ADMIN                      = "pTaskAdmin";
    public static final String  PERM_FORCE_ACQUIRE                   = "pForceAcquireTask";
    public static final String  PERM_ACQUIRE                         = "pAcquireTask";
    public static final String  PERM_DELEGATE                        = "pDelegateTask";
    public static final String  PERM_FORWARD                         = "pForwardTask";

    // /////////////////////permission for message/////////////////////////////
    public static final String  PERM_MESSAGE_MANAGE_DESTINATIONS     = "pManageDestinations";
    public static final String  PERM_MESSAGE_MONITOR_DESTINATIONS    = "pMonitorDestinations";

    // Permission for notice
    public static final String  PERM_LOGIN_NOTICE_ANNOUNCE           = "loginNoticeAnnounce";
    public static final String  PERM_LOGIN_NOTICE_UPATE              = "loginNoticeUpdate";
    public static final String  PERM_LOGIN_NOTICE_DELETE             = "loginNoticeDelete";
    public static final String  PERM_LOGIN_NOTICE_INVALIDATE         = "loginNoticeInvalidate";
    public static final String  PERM_LOGIN_NOTICE_SEARCH             = "loginNoticeSearch";

    public static final String  PERM_TIME_NOTICE_ANNOUNCE            = "timeNoticeAnnounce";
    public static final String  PERM_TIME_NOTICE_UPATE               = "timeNoticeUpdate";
    public static final String  PERM_TIME_NOTICE_DELETE              = "timeNoticeDelete";
    public static final String  PERM_TIME_NOTICE_INVALIDATE          = "timeNoticeInvalidate";
    public static final String  PERM_TIME_NOTICE_SEARCH              = "timeNoticeSearch";

    public static final String  PERM_TASK_NOTICE_ANNOUNCE            = "taskNoticeAnnounce";
    public static final String  PERM_TASK_NOTICE_UPATE               = "taskNoticeUpdate";
    public static final String  PERM_TASK_NOTICE_DELETE              = "taskNoticeDelete";
    public static final String  PERM_TASK_NOTICE_INVALIDATE          = "taskNoticeInvalidate";
    public static final String  PERM_TASK_NOTICE_SEARCH              = "taskNoticeSearch";

    public static final String  PERM_ONLINE_VIW                      = "viewOnline";
    public static final String  PERM_ONLINE_ADMIN                    = "adminOnline";
    public static final String  PERM_GET_ALL_USERS                   = "getAllUsers";
    public static final String  PERM_GET_ALL_ROLES                   = "getAllRoles";

    // 健康检查权限设置
    public static final String  PERM_HC_VIW                          = "viewHC";

    public static final String  PERM_RECOVER                         = "pRecover";

    // calendar worktime
    public static final String  FLAG_WORKTIME_CAL                    = "0";
    public static final String  FLAG_WORKTIME_WEEKDAY                = "1";
    public static final String  FLAG_WORKTIME_SPECIALDAY             = "2";

    // activity state
    public static final String  ACT_TYPE_START                       = "Start";
    public static final String  ACT_TYPE_COMMON                      = "Common";
    public static final String  ACT_TYPE_CALL_FLOW                   = "CallFlow";
    public static final String  ACT_TYPE_CALL_SUS          = "CallSus";
    public static final String  ACT_TYPE_CALL_AZ                  = "CallAz";
    public static final String  ACT_TYPE_END                         = "End";
    public static final String  ACT_TYPE_EXCEPTION                   = "Exception";
    public static final String  ACT_TYPE_SEQ_STRUCT_ENTYR            = "SeqStructEntry";
    public static final String  ACT_TYPE_SEQ_STRUCT_EXIT             = "SeqStructExit";
    public static final String  ACT_TYPE_LOOP_STRUCT_ENTRY           = "LoopStructEntry";
    public static final String  ACT_TYPE_LOOP_STRUCT_EXIT            = "LoopStructExit";
    public static final String  ACT_TYPE_PARALLEL_STRUCT_ENTRY       = "ParallelStructEntry";
    public static final String  ACT_TYPE_PARALLEL_STRUCT_EXIT        = "ParallelStructExit";

    // activity state for flowChange
    public static final int     ACT_TYPE_CHANGE_STRUCT_ENTRY         = 3;
    public static final int     ACT_TYPE_CHANGE_STRUCT_EXIT          = 5;
    public static final int     ACT_TYPE_CHANGE_COMMON               = 7;
    public static final int     ACT_TYPE_CHANGE_CALLFLOW             = 8;
    public static final int     ACT_TYPE_CHANGE_USERTASK             = 9;
    public static final int     ACT_TYPE_CHANGE_START                = 10;
    public static final int     ACT_TYPE_CHANGE_END                  = 11;
    public static final int     ACT_TYPE_CHANGE_DELAYER              = 12;

    // activity state constants
    public static final String  ACT_STATE_NULL                       = "Null";
    public static final String  ACT_STATE_WAITING                    = "Waiting";
    public static final String  ACT_STATE_READY                      = "Ready";
    public static final String  ACT_STATE_RUNNING                    = "Running";
    public static final String  ACT_STATE_FINISH                     = "Finished";
    public static final String  ACT_STATE_SKIPPED                    = "Skipped";
    public static final String  ACT_STATE_FAIL_SKIPPED               = "Fail:Skipped";
    public static final String  ACT_STATE_FAIL                       = "Fail";
    public static final String  ACT_STATE_FAIL_BUSINESS              = "Fail:Business";
    public static final String  ACT_STATE_EXECUTE                    = "execute";
    public static final String  ACT_STATE_RETRY                      = "Retry";
    public static final String  ACT_STATE_OTHER                      = "Other";
    public static final String  ACT_STATE_TIMEOUT                    = "Timeout";
    public static final String  ACT_STATE_ARRIVED                    = "Arrived";
    public static final String  ACT_STATE_OVER                       = "Over";
    public static final String  ACT_STATE_MANUAL_RUNNING             = "ManualRunning";
    public static final String  ACT_STATE_MANUAL_FINISH              = "ManualFinish";
    public static final String  ACT_STATE_ERROR_RETRY                = "ErrorRetry";
    public static final String  ACT_STATE_ERROR_SUC                  = "ErrorSuc";
    public static final String  ACT_STATE_CONTINUE_RUNNING           = "Continue";
    public static final String  ACT_STATE_INFOMACTION                = "InfoMaction";
    public static final String  ACT_STATE_UNRUNED                    = "Not_Allowed_Exec";
    // 新增Agent宕机监控状态 add by yuyang
    public static final String  ACT_STATE_AGENT_DISCONNECT           = "Disconnect";
    public static final String  ACT_STATE_AGENT_MANUALDISCONNECT     = "ManualDisconnect";
    // 新增活动排队状态
    public static final String  ACT_STATE_QUEUEUP                    = "QueueUp";
    public static final String  ACT_STATE_HANGUP                     = "HangUp";
    public static final String  ACT_STATE_MUTEX_HANGUP               = "MUTEX_HANGUP";
    public static final String  ACT_STATE_DISABLE                    = "Disable";
    public static final String  ACT_AS400_JOBSCREEN_ALTER            = "AlterAS400";
    // 新增活动暂停状态
    public static final String  ACT_STATE_PAUSED                     = "Paused";

    public static final String  STR_LOGIN_IP                         = "login_ip";
    public static final String  STR_LOGIN_MAC                        = "login_mac ";
    public static final String  STR_LOGIN_HOSTNAME                   = "login_hostname";

    /**
     * Workflow status
     */
    public static final int     STATE_WAIT_START                        = 55;
    public static final int     STATE_RUNNING                        = 0;
    public static final int     STATE_CREATED                        = 1;
    public static final int     STATE_FINISHED                       = 2;
    public static final int     STATE_KILLED                         = 4;
    static public final int     STATE_KILLED_SUS                     = 5;
    public static final int     STATE_STOPPING                       = 6;
    public static final int     STATE_STOPPED                        = 7;
    public static final int     STATE_PAUSED                         = 8;
    public static final int     STATE_ERROR                          = 10;
    public static final int     STATE_LISTENING                      = 15;
    public static final int     STATE_UNEXPECTED_END                 = 25;
    public static final int     STATE_UNEXPECTED_END_PAUSE           = 26;
    public static final int     STATE_RECOVERING                     = 30;
    public static final int     STATE_RECOVER_FAILED                 = 35;
    public static final int     STATE_UNRUN                          = 37;
    // server切换等待状态
    static public final int     STATE_CALLBACK                       = 41;

    static public final int     STATE_HANGUP                          = 45;
    public static final int     STATE_ACT_FAIL                       = 60;
    public static final int     STATE_FLOW_DEPLOY                    = 65;
    public static final int     STATE_FLOW_FAIL                      = 66;

    public static final int     STATE_NOT_ALLOWED_EXEC               = 8;                                            // 即便该活动被拷贝到运行表中，但是因为活动不符合驱动条件，而未被驱动过
    // 新增工作流状态为排队
    public static final int     STATE_QUEUEUP                        = 40;

    public static final int     STATE_FAIL                           = 46;
    public static final int     STATE_FAIL_BUSSINESS                 = 47;
    public static final int     STATE_NEW_CREATE                     = 55;

    //工作流准备状态，准备状态之后，才能进入运行状态
    public static final int     STATE_READY_START                     = 101;

    // 新增备份平台运行状态
    public static final int     BACK_RUNSTATE_RUNNING                = 0;
    public static final int     BACK_RUNSTATE_FINISHED               = 2;
    public static final int     BACK_RUNSTATE_KILL                   = 4;
    public static final int     BACK_RUNSTATE_PAUSE                  = 8;
    public static final int     BACK_RUNSTATE_BACKING                = 11;
    public static final int     BACK_RUNSTATE_RECOVERING             = 12;
    public static final int     BACK_RUNSTATE_UNEXPECTED_END         = 25;

    /**
     * STANDARD_TASK_STATE
     */
    public static final int     STANDARD_TASK_STATE_RUNNING          = 0;
    public static final int     STANDARD_TASK_STATE_FINISHED         = 2;
    public static final int     STANDARD_TASK_STATE_KILLED           = 4;
    public static final int     STANDARD_TASK_STATE_FAIL             = 46;
    public static final int     STANDARD_TASK_STATE_LASTONCE         = 50;

    /**
     * STANDARD_TASK_RELATION_STATE
     */
    public static final int     STANDARD_TASK_RELATION_STATE_DEFAULT = 1;
    public static final int     STANDARD_TASK_RELATION_STATE_FAIL    = 0;
    //选择执行 默认STANDARD_TASK_RELATION_SELECT_STATE为3,用于判断任务历史状态为待启动
    public static final int     STANDARD_TASK_RELATION_SELECT_STATE = 3;

    /** yue_sun on 2018-02-02 迁移作业依赖关系展示所需的参数  start **/
    // 作业互斥规则
    public static final int     ACT_MUTEX_RULETYPE_DIRECT            = 1;                                            // 直接互斥配置
    public static final int     ACT_MUTEX_RULETYPE_RELATION          = 2;                                            // 关联互斥配置
    public static final int     ACT_MUTEX_RULETYPE_SELF              = 3;                                            // 自互斥配置
    public static final String  ACT_MUTEX_RULETYPE_DIRECT_S          = "直接互斥配置";
    public static final String  ACT_MUTEX_RULETYPE_RELATION_S        = "关联互斥配置";
    public static final String  ACT_MUTEX_RULETYPE_SELF_S            = "自互斥配置";

    // 活动类型
    public static final String  EXCEL_ACT_TYPE_SHELLCMD              = "ShellCmd";
    public static final String  EXCEL_ACT_TYPE_SHELLU                = "ShellU";
    public static final String  EXCEL_ACT_TYPE_APTSHELL              = "aptShell";
    public static final String  EXCEL_ACT_TYPE_CALLFLOW              = "callFLow";
    public static final String  EXCEL_ACT_TYPE_DELAYER               = "delayer";
    public static final String  EXCEL_ACT_TYPE_START                 = "start";
    public static final String  EXCEL_ACT_TYPE_END                   = "end";
    public static final String  EXCEL_ACT_TYPE_FILE                  = "file";
    public static final String  EXCEL_ACT_TYPE_UT                    = "UserTask";
    public static final String  EXCEL_ACT_TYPE_SCRIPTCALL            = "ScriptCall";
    public static final String  EXCEL_ACT_TYPE_PROC              = "PROC";

    public static final String  EXCEL_ACT_TYPE_LOOPSTART             = "LoopStructEntry";
    public static final String  EXCEL_ACT_TYPE_LOOPEND               = "LoopStructExit";
    // 活动名称
    public static final String  EXCEL_ACT_NAME_START                 = "开始活动";
    public static final String  EXCEL_ACT_NAME_END                   = "结束活动";
    public static final String  EXCEL_ACT_NAME_FILECHECK             = "_CHECK";

    public static final String  EXCEL_ACT_NAME_LOOPSTART             = "循环开始";
    public static final String  EXCEL_ACT_NAME_LOOPSEND              = "循环结束";
    // 工作流调用 true 同步 flase 异不
    public static final boolean EXCEL_CALLFLOW_ISYCCALLl             = true;
    public static final int     LOAD_PROJECT_ERROR_CODE              = -100;
    public static final String  SHELLCMD_COMMAND                     = "command";
    public static final String  SHELLCMD_YYYYMMDD                    = "YYYYMMDD";
    public static final String  SHELLCMD_YYYY_MM_DD                    = "YYYY-MM-DD";
    public static final String  DATA_COLLECT_ALARM                   = "datacollect.alarm.switch";
    public static final String  LAST_LINE                            = ".lastLine";
    //登录页面修改密码（false不显示）
    public static final String  LOGIN_CHANGE_PASSWORD                = "login.changepassword.switch";
    /** yue_sun on 2018-02-02 迁移作业依赖关系展示所需的参数  end **/

    public static boolean isFlowActivity ( int flowState )
    {
        return flowState == STATE_RUNNING || flowState == STATE_LISTENING || flowState == STATE_PAUSED
                || flowState == STATE_RECOVERING || flowState == STATE_STOPPING;
    }

    public static boolean isFlowRecoverStatus ( int flowState )
    {
        return flowState == STATE_RUNNING || flowState == STATE_LISTENING || flowState == STATE_PAUSED || flowState == STATE_CALLBACK
                || flowState == STATE_RECOVERING;
    }

    public static final String GET_TIME_LONG       = "FUN_GET_DATE_NUMBER_NEW(current_timestamp,8)";
    public static final String GET_TIME_LONG_MYSQL = "FUN_GET_DATE_NUMBER_NEW(current_timestamp,8)";
    public static final String GET_TIME_ORACLE     = "FUN_GET_DATE_NUMBER_NEW(current_timestamp,8)";
    public static final String GET_TIME_DB2        = "FUN_GET_DATE_NUMBER_NEW(current timestamp,8)";

    /**
     * <li>Description:</li>
     * <AUTHOR>
     * 2016年2月23日
     * @return
     * return String
     */
    public static String getCurrentSysDate ()
    {
        switch (JudgeDB.IEAI_DB_TYPE)
        {
            case 1:
            case 4:
                return "current_timestamp";
            case 2:
                return "current timestamp";
            case 3:
                return "NOW()";
            default:
                return "current_timestamp";
        }

    }

    // ///////////////////////////////////////////////////////////////////////
    // Server Activity Constants //
    // ///////////////////////////////////////////////////////////////////////
    // Server management
    public static final String SERVER_SETDBINFO                                    = "server.setdbinfo";
    public static final String SERVER_GETDBINFO                                    = "server.getdbinfo";
    public static final String SERVER_TESTDB                                       = "server.testdb";
    public static final String SERVER_SETSERLOGCONF                                = "server.setserverlogconfig";
    public static final String SERVER_GETSERLOGCONF                                = "server.getserverlogconfig";

    // Login/logout
    public static final String USER_LOGIN                                          = "user.login";
    public static final String USER_LOGOUT                                         = "user.logout";

    // Get/Add/Remove/Freeze/Unfreeze SN
    public static final String SN_GETALL                                           = "sn.getall";
    public static final String SN_GETINFO                                          = "sn.getinfo";
    public static final String SN_ADD                                              = "sn.add";
    public static final String SN_REMOVE                                           = "sn.remove";
    public static final String SN_FREEZE                                           = "sn.freeze";
    public static final String SN_UNFREEZE                                         = "sn.unfreeze";

    // Start, Shutdown DC, getDC info
    public static final String DC_START                                            = "dc.start";
    public static final String DC_SHUTDOWN                                         = "dc.shutdown";
    public static final String DC_GETINFO                                          = "dc.getinfo";

    // Get/Add/Remove/Change user
    public static final String USER_GETINFO                                        = "user.getinfo";
    public static final String USER_ADD                                            = "user.add";
    public static final String USER_REMOVE                                         = "user.remove";
    public static final String USER_CHANGE                                         = "user.change";

    // Get/Set user permission
    public static final String USER_GETPERM                                        = "user.getperm";
    public static final String USER_SETPERM                                        = "user.setperm";

    // Get/set groups from/to user
    public static final String USER_SETGROUPS                                      = "user.setgroups";
    public static final String USER_GETGROUPS                                      = "user.getgroups";

    // Get prj/adp from user
    public static final String USER_GETPRJS                                        = "user.getprjs";
    public static final String USER_GETADPS                                        = "user.getadps";

    // Get/Add/Remove/Change group
    public static final String GROUP_GETINFO                                       = "group.getinfo";
    public static final String GROUP_ADD                                           = "group.add";
    public static final String GROUP_REMOVE                                        = "group.remove";
    public static final String GROUP_CHANGE                                        = "group.change";

    // Get/Set group permission
    public static final String GROUP_GETPERM                                       = "group.getperm";
    public static final String GROUP_SETPERM                                       = "group.setperm";

    // Get/set users from/to group
    public static final String GROUP_SETUSERS                                      = "group.setusers";
    public static final String GROUP_GETUSERS                                      = "group.getusers";

    // Get/Create/edit/update/freeze/unfreeze project/Adaptor
    public static final String PROJ_CREATE                                         = "proj.create";
    public static final String PROJ_DELETE                                         = "adp.delete";
    public static final String PROJ_UPLOAD                                         = "proj.upload";
    public static final String PROJ_GETALLINFO                                     = "proj.getallinfo";
    public static final String PROJ_GETINFO                                        = "proj.getinfo";
    public static final String PROJ_EDIT                                           = "proj.edit";
    public static final String PROJ_RELEASE_LOCK                                   = "proj.releaselock";
    public static final String PROJ_UPDATE                                         = "proj.update";
    public static final String PROJ_DOWNLOAD                                       = "proj.download";
    public static final String PROJ_FREEZE                                         = "proj.freeze";
    public static final String PROJ_UNFREEZE                                       = "proj.unfreeze";
    public static final String PROJ_GETVERHIST                                     = "proj.getverhist";
    public static final String PROJ_GETLOG                                         = "proj.getlog";

    public static final String ADP_CREATE                                          = "adp.create";
    public static final String ADP_DELETE                                          = "adp.delete";
    public static final String ADP_UPLOAD                                          = "adp.upload";
    public static final String ADP_GETALLINFO                                      = "adp.getallinfo";
    public static final String ADP_GETINFO                                         = "adp.getinfo";
    public static final String ADP_EDIT                                            = "adp.edit";
    public static final String ADP_RELEASE_LOCK                                    = "adp.releaselock";
    public static final String ADP_UPDATE                                          = "adp.update";
    public static final String ADP_DOWNLOAD                                        = "adp.download";
    public static final String ADP_FREEZE                                          = "adp.freeze";
    public static final String ADP_UNFREEZE                                        = "adp.unfreeze";
    public static final String ADP_GETVERHIST                                      = "adp.getverhist";
    public static final String ADP_GETLOG                                          = "adp.getlog";

    // Get/Start/Stop/GetResult workflow
    public static final String FLOW_GETALL                                         = "flow.getall";
    public static final String FLOW_START                                          = "flow.start";
    public static final String FLOW_STOP                                           = "flow.stop";
    public static final String FLOW_GETRESULT                                      = "flow.getresult";
    public static final String FLOW_GETINFO                                        = "flow.getinfo";
    public static final String FLOW_GETLOG                                         = "flow.getlog";

    // Send/Receive/Peek message
    public static final String MSG_SEND                                            = "msg.send";
    public static final String MSG_PEEK                                            = "msg.peek";
    public static final String MSG_GET                                             = "msg.get";

    // workflow starting Template.
    public static final int    UNDIFINED                                           = -1;
    public static final int    MANUAL_STARTING                                     = 0;
    public static final int    TIMER_STARTING                                      = 1;
    public static final int    CYCLE_STARTING                                      = 2;
    public static final int    TIMER_AND_CYCLE_STARTING                            = 3;

    // The state of WorkflowPlan
    public static final int    WORKFLOW_PLAN_CREATED                               = 0;
    public static final int    WORKFLOW_PLAN_FINISHED                              = 1;
    public static final int    WORKFLOW_PLAN_RUNNING                               = 2;

    // the modification type of task. it is used in task history.
    public static final int    TASK_CREATE                                         = 0;
    public static final int    TASK_RENAME                                         = 1;
    public static final int    TASK_MODIFY_PRIORITY                                = 2;
    public static final int    TASK_MODIFY_DESC                                    = 3;
    public static final int    TASK_MODIFY_ASSIGNEES                               = 4;
    public static final int    TASK_ACQUIRE                                        = 5;
    public static final int    TASK_DELEGATE                                       = 6;
    public static final int    TASK_FORWARD                                        = 7;
    public static final int    TASK_SET_PROPERTY                                   = 8;
    public static final int    TASK_ADD_ATTACHMENT                                 = 9;
    public static final int    TASK_REMOVE_ATTACHMENT                              = 10;
    public static final int    TASK_UPDATE_ATTACHMENT                              = 11;
    public static final int    TASK_DELAY_TIME_LIMIT                               = 12;
    public static final int    TASK_SET_ABSOLUTE_TIME_LIMIT                        = 13;
    public static final int    TASK_SET_RELATE_FLOW_START_TIME_LIMIT               = 14;
    public static final int    TASK_SET_RELATE_TASK_START_TIME_LIMIT               = 15;
    public static final int    TASK_SET_NO_TIME_LIMIT                              = 16;
    public static final int    TASK_REFERNECE                                      = 17;
    public static final int    TASK_SET_TASK_OWNER                                 = 18;
    public static final int    TASK_CANCEL_DELEGATION                              = 19;
    public static final int    TASK_RELEASE_TASK                                   = 20;
    public static final int    TASK_FORCE_ACQUIRE                                  = 21;
    public static final int    TASK_PERFORM_TASK                                   = 22;
    public static final int    TASK_APPROVE_DELEGATED_TASK                         = 23;
    public static final int    TASK_REJECT_DELEGATED_TASK                          = 24;

    //
    static final public int    DEPLOY_SUCCESS                                      = 0;
    static final public int    DEPLOY_FAIL                                         = 1;
    static final public int    DEPLOY_NOT                                          = 2;
    static final public int    DEPLOY_HALFSUCCESS                                  = 3;

    //
    static final public String ADD_FLAG                                            = "add";
    static final public String DELETE_FLAG                                         = "del";
    static final public String MODIFY_FLOG                                         = "mod";

    /**
     * Task status
     */
    public static final String TASK_STATE_NEW                                      = "new";
    public static final String TASK_STATE_READY                                    = "ready";
    public static final String TASK_STATE_OWNED                                    = "owned";
    public static final String TASK_STATE_COMPLETE                                 = "complete";
    public static final String TASK_STATE_SKIPPED                                  = "skipped";
    public static final String TASK_STATE_OVERTIME                                 = "overtime";
    public static final String TASK_STATE_FAIL                                     = "fail";
    public static final String TASK_STATE_UNKNOWN                                  = "unknown";
    public static final String TASK_STATE_RETRY                                    = "retry";
    public static final String TASK_STATE_RUNNING                                  = "running";
    public static final String TASK_STATE_EXECUTE                                  = "execute";

    /**
     * now user by IEAIService.getFlowSnapshot to define different image types
     */
    public static final int    IMAGE_GIF                                           = 0;
    public static final int    IMAGE_JPEG                                          = 1;
    public static final int    IMAGE_PNG                                           = 2;

    /**
     * Log Type
     */
    // Program Log
    public static final int    PROGRAM_LOG                                         = 0;

    // Project Manage
    public static final int    PRJ_UPLOAD_LOG                                      = 1;
    public static final int    PRJ_DOWNLOAD_LOG                                    = 2;
    public static final int    PRJ_LOCK_LOG                                        = 3;
    public static final int    PRJ_UNLOCK_LOG                                      = 4;
    public static final int    PRJ_RMOVE_LOG                                       = 5;

    // Adaptor Manage
    public static final int    ADP_UPLOAD_LOG                                      = 11;
    public static final int    ADP_DOWNLOAD_LOG                                    = 12;
    public static final int    ADP_LOCK_LOG                                        = 13;
    public static final int    ADP_UNLOCK_LOG                                      = 14;
    public static final int    ADP_REMOVE_LOG                                      = 15;

    // Workflow Running here
    public static final int    FLOW_START_LOG                                      = 101;
    public static final int    FLOW_FINISH_LOG                                     = 102;
    public static final int    FLOW_PAUSE_LOG                                      = 103;
    public static final int    FLOW_RESUME_LOG                                     = 104;
    public static final int    FLOW_STOP_LOG                                       = 105;
    public static final int    FLOW_KILL_LOG                                       = 106;

    // Activity executing
    public static final int    ACT_START_LOG                                       = 110;
    public static final int    ACT_END_LOG                                         = 111;
    public static final int    ACT_EXCEPTION_LOG                                   = 112;
    public static final int    ACT_TIME_OUT_LOG                                    = 113;
    public static final int    ACT_KILL_LOG                                        = 114;
    public static final int    ACTIVITY_EACHOTHER_LOG                              = 115;                                                       // 活动交互
    public static final int    OPERATION_EXCEPTION_LOG                             = 116;                                                       // 活动业务异常

    // User Task 200-299
    public static final int    TASK_READY_LOG                                      = 200;
    public static final int    TASK_ACQUIRE_LOG                                    = 201;
    public static final int    TASK_FORCE_ACQUIRE_LOG                              = 202;
    public static final int    TASK_RELEASE_LOG                                    = 203;
    public static final int    TASK_DELEGATE_LOG                                   = 204;
    public static final int    TASK_FORWARD_LOG                                    = 205;
    public static final int    TASK_OPERATION_LOG                                  = 206;
    public static final int    TASK_CHANGE_PROPERTY_LOG                            = 207;
    public static final int    TASK_ADD_ATTACH_LOG                                 = 210;
    public static final int    TASK_DEL_ATTACH_LOG                                 = 211;
    public static final int    TASK_UPDATE_ATTACH_LOG                              = 212;
    public static final int    TASK_DELEGATE_FINISH_LOG                            = 220;
    public static final int    TASK_APPROVE_DELEGATE_LOG                           = 221;
    public static final int    TASK_REJECT_DELEGATE_LOG                            = 222;
    public static final int    TASK_FORCE_RELEASE_LOG                              = 223;
    public static final int    TASK_CANCEL_DELEGATE_LOG                            = 224;
    public static final int    TASK_FINISH_ITEM_LOG                                = 225;

    public static final int    TASK_TASK_QUERY_LOG                                 = 226;                                                       // 任务查询
    public static final int    TASK_TASK_OPTERATION_DETAILS_QUERY_LOG              = 227;                                                       // 任务操作明细
    public static final int    TASK_TASK_OPTERATION_HISTORY_QUERY_LOG              = 228;                                                       // 任务操作历史分析
    public static final int    TASK_WORKFLOW_QUERY_LOG                             = 229;                                                       // 工作流查询
    public static final int    TASK_WORKFLOW_CAMERA_LOG                            = 230;                                                       // 工作流快照
    public static final int    TASK_WORKFLOW_REPORT_QUERY_LOG                      = 231;                                                       // 工作流报表
    public static final int    TASK_ACTRUNNING_REPORT_QUERY_LOG                    = 232;                                                       // 活动运行报表
    public static final int    TASK_WORKFLOWACT_RUNNINGSTAT_QUERY_LOG              = 233;                                                       // 工作流活动运行统计信息
    public static final int    TASK_ACTAVECONSUMETIME_TABLE_QUERY_LOG              = 234;                                                       // 活动运行平均耗时排名表
    public static final int    TASK_EVERDAY_WORKFLOW_RUNNING_STAT_QUERY_LOG        = 235;                                                       // 日工作流执行统计查询
    public static final int    TASK_EVERDAY_WORKFLOW_RUNNING_STAT_EXPORT_LOG       = 236;                                                       // 日工作流执行统计导出
    public static final int    TASK_WORKFLOWACT_EXECUTEDETAILES_STAT_QUERY_LOG     = 237;                                                       // 工作流活动执行明细统计表查询
    public static final int    TASK_WORKFLOWACT_EXECUTEDETAILES_STAT_EXPORT_LOG    = 238;                                                       // 工作流活动执行明细统计表导出
    public static final int    TASK_EVERYDAY_BATCH_QUERY_LOG                       = 239;                                                       // 每月批量查询
    public static final int    TASK_EVERYDAY_BATCH_EXPORT_LOG                      = 240;                                                       // 每月批量导出
    public static final int    TASK_ACCOUNT_MANAGEMENT_SUBMIT_LOG                  = 241;                                                       // 账户管理提交

    // 在sm中操作的日志类型也在此处编号 1000-1024
    // 拓扑图部分编号
    public static final int    TOPOLOGYCHART_ADD_LOG                               = 1001;                                                     // 增加拓扑图
    public static final int    TOPOLOGYCHART_DELETE_LOG                            = 1002;                                                     // 删除拓扑图(拓扑图节点)
    public static final int    TOPOLOGYCHART_RENAME_LOG                            = 1003;                                                     // 修改拓扑图名
    public static final int    TOPOLOGYCHARTNOTE_ADD_LOG                           = 1004;                                                     // 增加拓扑图节点
    public static final int    TOPOLOGYCHARTNOTE_QUERY_LOG                         = 1005;                                                     // 查询拓扑图节点
    public static final int    TOPOLOGYCHARTNOTE_EDIT_LOG                          = 1006;                                                     // 编辑拓扑图节点
    // 工作流线程设置编号
    public static final int    WORKFLOWTHREAD_SET_LOG                              = 1007;                                                     // 工作流线程设置保存
    public static final int    WORKFLOWTHREAD_QUERY_LOG                            = 1008;                                                     // 工作流线程设置查询
    // 灾难恢复编号
    public static final int    DISASTERRESTORE_QUERY_LOG                           = 1009;                                                     // 恢复与调整工作流查询
    public static final int    DISASTERRESTORE_SAVE_LOG                            = 1010;                                                     // 恢复与调整工作流恢复

    public static final int    APPLICATIONGROUPCFG_QUERY_LOG                       = 1011;                                                     // 应用分组配置查询
    public static final int    APPLICATIONGROUPCFG_SAVE_LOG                        = 1012;                                                     // 应用分组配置保存
    public static final int    BATCH_CONSUMETIMESTAT_SAVE_LOG                      = 1013;                                                     // 批量耗时统计设置保存
    public static final int    BATCH_CONSUMETIMEALARM_SAVE_LOG                     = 1014;                                                     // 批量耗时报警设置保存
    public static final int    KEYNODE_ALARMVALUE_SAVE_LOG                         = 1015;                                                     // 配置关键节点报警阀值保存
    public static final int    KEYNODE_BATCHMONITOR_QUERY_LOG                      = 1016;                                                     // 批量监控查询查询
    public static final int    KEYNODE_BATCHMONITOR_PAUSE_LOG                      = 1017;                                                     // 批量监控查询暂停
    public static final int    KEYNODE_BATCHMONITOR_RESTORE_LOG                    = 1018;                                                     // 批量监控查询恢复
    public static final int    KEYNODE_BATCHMONITOR_DELETE_LOG                     = 1019;                                                     // 批量监控查询删除
    public static final int    KEYNODE_ALARMINFO_DEAL_LOG                          = 1020;                                                     // 查询报警信息处理

    // end liang_zhang

    // login and logout log
    public static final int    USER_LOGIN_LOG                                      = 300;
    public static final int    USER_LOGOUT_LOG                                     = 301;
    public static final int    USER_LOGOUT_AUTO_LOG                                = 302;
    public static final int    USER_FORCE_LOGIN_LOG                                = 303;
    public static final int    USER_FORCE_LOGOUT_LOG                               = 304;

    /**
     * error task log
     */
    // Note:It is not log type. It is used signed the begin of log type.It equals to
    // ERRORTASK_READY_LOG
    public static final int    ERRORTASK_LOG_BEGIN                                 = 400;

    public static final int    ERRORTASK_READY_LOG                                 = 400;
    public static final int    ERRORTASK_ACQUIRE_LOG                               = 401;
    public static final int    ERRORTASK_RELEASE_LOG                               = 402;
    public static final int    ERRORTASK_FORWARD_LOG                               = 403;
    public static final int    ERRORTASK_DELEGATE_LOG                              = 404;
    public static final int    ERRORTASK_FORCE_ACQUIRE_LOG                         = 405;
    public static final int    ERRORTASK_FORCE_RELEASE_LOG                         = 406;
    public static final int    ERRORTASK_REJECT_DELEGATEE_OPERATION_LOG            = 407;
    public static final int    ERRORTASK_ACCEPT_DELEGATEE_OPERATION_LOG            = 408;
    public static final int    ERRORTASK_CANCEL_DELEGATION_LOG                     = 409;

    // Note:It is not log type. It is used signed the begin of log type.
    public static final int    ERRORTASK_OPERATION_LOG_BEGIN                       = 410;

    public static final int    ERRORTASK_OPERATION_CONTINUE_LOG                    = 411;
    public static final int    ERRORTASK_OPERATION_REDO_LOG                        = 412;
    public static final int    ERRORTASK_OPERATION_SKIP_LOG                        = 413;
    public static final int    ERRORTASK_OPERATION_GOTO_LOG                        = 414;
    public static final int    ERRORTASK_OPERATION_STOP_FLOW_LOG                   = 415;
    public static final int    ERRORTASK_OPERATION_STOP_STRUCTURE_LOG              = 416;
    public static final int    ERRORTASK_OPERATION_STOP_BRANCH_LOG                 = 417;
    public static final int    ERRORTASK_OPERATION_JUMPTO_EXCEPTION_ACTIVITY_LOG   = 418;
    public static final int    ERRORTASK_OPERATION_KILL_MAINANDSUBFLOW_LOG         = 422;
    public static final int    ERRORTASK_OPERATION_KILL_ONLYSUBFLOW_LOG            = 423;

    // Note:It is not log type. It is used signed the end of log type.
    public static final int    ERRORTASK_OPERATION_LOG_END                         = 419;

    public static final int    ERRORTASK_INIT_LOG                                  = 420;
    public static final int    ERRORTASK_REACTIVE_LOG                              = 421;

    // Note:It is not log type. It is used signed the end of log type.It equals to
    // ERRORTASK_REACTIVE_LOG
    public static final int    ERRORTASK_LOG_END                                   = 423;

    // 流程变更日志查询标志
    public static final int    FLOW_VARY_ACTMOD                                    = 600;
    public static final int    APP_LOG_SHOW_ACTMOD                                 = 601;
    public static final int    APP_LOG_SHOW_IGNORE                                 = 602;
    public static final int    APP_LOG_SHOW_IGNORE_YES                             = 603;
    public static final int    APP_LOG_SHOW_IGNORE_NO                              = 604;
    public static final int    APP_LOG_SHOW_DELAY                                  = 605;
    public static final int    APP_LOG_SHOW_DELAY_HOUR                             = 606;
    public static final int    APP_LOG_SHOW_DELAY_MIN                              = 607;

    // resources
    public final static String RES_PRJS_FRM                                        = "projectsForm";
    public final static String RES_PRJ_FRM                                         = "projectForm";
    public final static String RES_PRJ_ID                                          = "prjId";
    public final static String RES_PRJ_NAME                                        = "prjName";
    public final static String RES_PRJ_VER                                         = "prjVer";
    public final static String RES_ADPS_FRM                                        = "adaptorsForm";
    public final static String RES_ADP_FRM                                         = "adaptorForm";
    public final static String RES_ADP_ID                                          = "adpId";
    public final static String RES_ADP_NAME                                        = "adpName";
    public final static String RES_ADP_VER                                         = "adpVer";
    public final static String RES_UPLD_NUM                                        = "upldNum";

    public static final int[]  ALL_APP_LOG_SHOW                                    = { APP_LOG_SHOW_ACTMOD,
            APP_LOG_SHOW_IGNORE, APP_LOG_SHOW_IGNORE_YES, APP_LOG_SHOW_IGNORE_NO, APP_LOG_SHOW_DELAY,
            APP_LOG_SHOW_DELAY_HOUR, APP_LOG_SHOW_DELAY_MIN };

    public static final int[]  ALL_APP_LOG                                         = { PRJ_UPLOAD_LOG, PRJ_DOWNLOAD_LOG,
            PRJ_LOCK_LOG, PRJ_UNLOCK_LOG, PRJ_RMOVE_LOG, ADP_UPLOAD_LOG, ADP_DOWNLOAD_LOG, ADP_LOCK_LOG, ADP_UNLOCK_LOG,
            ADP_REMOVE_LOG, FLOW_START_LOG, FLOW_FINISH_LOG, FLOW_PAUSE_LOG, FLOW_RESUME_LOG, FLOW_STOP_LOG,

            FLOW_KILL_LOG,

            TASK_WORKFLOW_QUERY_LOG, TASK_WORKFLOW_CAMERA_LOG, TASK_WORKFLOW_REPORT_QUERY_LOG,
            TASK_WORKFLOWACT_RUNNINGSTAT_QUERY_LOG, TASK_WORKFLOWACT_EXECUTEDETAILES_STAT_QUERY_LOG,
            TASK_WORKFLOWACT_EXECUTEDETAILES_STAT_EXPORT_LOG, WORKFLOWTHREAD_SET_LOG, WORKFLOWTHREAD_QUERY_LOG,

            ACT_START_LOG, ACT_END_LOG, ACT_EXCEPTION_LOG, ACT_TIME_OUT_LOG, FLOW_VARY_ACTMOD,
            TASK_ACTRUNNING_REPORT_QUERY_LOG, TASK_ACTAVECONSUMETIME_TABLE_QUERY_LOG, ACTIVITY_EACHOTHER_LOG,
            OPERATION_EXCEPTION_LOG, ACT_KILL_LOG,

            TASK_READY_LOG, TASK_ACQUIRE_LOG, TASK_FORCE_ACQUIRE_LOG, TASK_RELEASE_LOG, TASK_DELEGATE_LOG,
            TASK_FORWARD_LOG, TASK_OPERATION_LOG,

            TASK_TASK_QUERY_LOG, TASK_TASK_OPTERATION_DETAILS_QUERY_LOG, TASK_TASK_OPTERATION_HISTORY_QUERY_LOG,

            TASK_CHANGE_PROPERTY_LOG, TASK_ADD_ATTACH_LOG, TASK_DEL_ATTACH_LOG, TASK_UPDATE_ATTACH_LOG,
            TASK_DELEGATE_FINISH_LOG, TASK_APPROVE_DELEGATE_LOG, TASK_REJECT_DELEGATE_LOG, TASK_FORCE_RELEASE_LOG,
            TASK_CANCEL_DELEGATE_LOG, TASK_FINISH_ITEM_LOG,

            /*
             * USER_LOGIN_LOG,
             * USER_LOGOUT_LOG,
             * USER_LOGOUT_AUTO_LOG,
             * USER_FORCE_LOGIN_LOG,
             * USER_FORCE_LOGOUT_LOG,
             */

            /* ERRORTASK_LOG_BEGIN, */ERRORTASK_READY_LOG, ERRORTASK_ACQUIRE_LOG, ERRORTASK_RELEASE_LOG,
            ERRORTASK_FORWARD_LOG, ERRORTASK_DELEGATE_LOG, ERRORTASK_FORCE_ACQUIRE_LOG, ERRORTASK_FORCE_RELEASE_LOG,
            ERRORTASK_REJECT_DELEGATEE_OPERATION_LOG, ERRORTASK_ACCEPT_DELEGATEE_OPERATION_LOG,
            ERRORTASK_CANCEL_DELEGATION_LOG,

            /*
             * ERRORTASK_OPERATION_LOG_BEGIN,
             */

            /* ERRORTASK_OPERATION_CONTINUE_LOG, */ERRORTASK_OPERATION_REDO_LOG, ERRORTASK_OPERATION_SKIP_LOG,
            ERRORTASK_OPERATION_GOTO_LOG, ERRORTASK_OPERATION_STOP_FLOW_LOG,                                                     /*
                                                                                                                                  * ERRORTASK_OPERATION_STOP_STRUCTURE_LOG,
                                                                                                                                  * ERRORTASK_OPERATION_STOP_BRANCH_LOG
                                                                                                                                  * ,ERRORTASK_OPERATION_JUMPTO_EXCEPTION_ACTIVITY_LOG,
                                                                                                                                  */

            /*
             * ERRORTASK_OPERATION_LOG_END,
             */

            ERRORTASK_INIT_LOG,                                                                                                  /*
                                                                                                                                  * ERRORTASK_REACTIVE_LOG,
                                                                                                                                  * ERRORTASK_LOG_END
                                                                                                                                  */
            // start liang_zhang

            TASK_EVERDAY_WORKFLOW_RUNNING_STAT_QUERY_LOG, TASK_EVERDAY_WORKFLOW_RUNNING_STAT_EXPORT_LOG,
            TASK_EVERYDAY_BATCH_QUERY_LOG, TASK_EVERYDAY_BATCH_EXPORT_LOG, TASK_ACCOUNT_MANAGEMENT_SUBMIT_LOG,

            TOPOLOGYCHART_ADD_LOG, TOPOLOGYCHART_DELETE_LOG, TOPOLOGYCHART_RENAME_LOG, TOPOLOGYCHARTNOTE_ADD_LOG,
            TOPOLOGYCHARTNOTE_QUERY_LOG, TOPOLOGYCHARTNOTE_EDIT_LOG, DISASTERRESTORE_QUERY_LOG,
            DISASTERRESTORE_SAVE_LOG, APPLICATIONGROUPCFG_QUERY_LOG, APPLICATIONGROUPCFG_SAVE_LOG,
            BATCH_CONSUMETIMESTAT_SAVE_LOG, BATCH_CONSUMETIMEALARM_SAVE_LOG, KEYNODE_BATCHMONITOR_QUERY_LOG,
            KEYNODE_BATCHMONITOR_PAUSE_LOG, KEYNODE_BATCHMONITOR_RESTORE_LOG, KEYNODE_BATCHMONITOR_DELETE_LOG,
            KEYNODE_ALARMVALUE_SAVE_LOG, KEYNODE_ALARMINFO_DEAL_LOG,

            // end
            // liang_zhang

    };

    /** constants for the errortask */
    public static final byte   ERRORTASK_DELEGATE_STATE_NULL                       = 0;
    public static final byte   ERRORTASK_DELEGATE_STATE_DELEGATED                  = 1;
    public static final byte   ERRORTASK_DELEGATE_STATE_FINISHED                   = 2;

    public static final byte   ERRORTASK_STATE_READY                               = 0;
    public static final byte   ERRORTASK_STATE_ACQUIRED                            = 1;
    public static final byte   ERRORTASK_STATE_FINISHED                            = 2;

    public static final int    ERRORTASK_HANDLING_METHOD_CONTINUE                  = 0;                                                           // 0
    public static final int    ERRORTASK_HANDLING_METHOD_REDO                      = 1;                                                           // 1
    public static final int    ERRORTASK_HANDLING_METHOD_SKIP                      = 2;                                                           // 2
    public static final int    ERRORTASK_HANDLING_METHOD_GOTO                      = 3;                                                           // 3
    public static final int    ERRORTASK_HANDLING_METHOD_STOP_FLOW                 = 4;                                                           // 4
    public static final int    ERRORTASK_HANDLING_METHOD_STOP_STRUCTURE            = 5;                                                           // 5
    public static final int    ERRORTASK_HANDLING_METHOD_STOP_BRANCH               = 6;                                                           // 6
    public static final int ERRORTASK_HANDLING_METHOD_JUMPTO_EXCEPTION_ACTIVITY = 7;
    public static final int ERRORTASK_HANDLING_METHOD_SUCCESS = 10;
    public static final int    ERRORTASK_HANDLING_METHOD_KILL_MAINANDSUBFLOW       = 11;                                                         // 8
    public static final int    ERRORTASK_HANDLING_METHOD_KILL_ONLYSUBFLOW          = 12;                                                         // 9

    public static int getErrorTaskLogType ( int handlingMethod )
    {
        return handlingMethod + 411;
    }

    public static final String LOCALE_CHINESE                         = "Chinese";
    public static final String LOCALE_ENGLISH                         = "English";

    /**
     * message statics subject
     */
    static final public String DESTINATION_NAME                       = "name";
    static final public String MESSAGE_COUNT                          = "Messages";
    static final public String MESSAGE_PENDING_COUNT                  = "Message.Pending";
    static final public String MESSAGE_EXPIRED_COUNT                  = "Message.Expiring";
    static final public String MESSAGE_WAIT_TIME_TOTAL                = "Message.Wait.Total.Time";
    static final public String MESSAGE_WAIT_TIME_MAX                  = "Message.Wait.Max.Time";
    static final public String MESSAGE_WAIT_TIME_MIN                  = "Message.Wait.Min.Time";

    /**
     * notice constants
     *
     * <AUTHOR> Shi
     */
    public static final int    NOTICE_TYPE_LOGIN                      = 0;
    public static final int    NOTICE_TYPE_TASK                       = 1;
    public static final int    NOTICE_TYPE_TIME                       = 2;
    public static final int    NOTICE_NOTIFY_METHOD_ONCE              = 0;
    public static final int    NOTICE_NOTIFY_METHOD_ALWAYS            = 1;
    public static final int    NOTICE_STATUS_NEW                      = 0;
    public static final int    NOTICE_STATUS_AVAILABLE                = 1;
    public static final int    NOTICE_STATUS_INVALID                  = 2;
    public static final int    NOTICE_OPERATION_ANOUNCE               = 0;
    public static final int    NOTICE_OPERATION_CONFIRM               = 1;
    public static final int    NOTICE_OPERATION_MODIFY                = 2;
    public static final int    NOTICE_OPERATION_INVALIDATE            = 3;
    public static final int    NOTICE_OPERATION_DELETE                = 4;

    /**
     * actwarning constants
     *
     * <AUTHOR>
     */
    public static final int    ACT_WARNING_FLAG_COSTTIME              = 1;
    public static final int    ACT_WARNING_FLAG_OPERATION             = 2;
    public static final int    ACT_WARNING_FLAG_SYSTEM                = 3;

    /**
     * for act report
     */
    public static final int    ACT_STAT_EVERY_DAY                     = 0;
    public static final int    ACT_STAT_EVERY_WEEK                    = 1;
    public static final int    ACT_STAT_EVERY_MONTH                   = 2;

    /**
     * for RExecRequest
     */
    public static final int    REXEC_REQ_STATE_RUNNING                = 0;
    public static final int    REXEC_REQ_STATE_NEW                    = 2;

    public static final int    REXEC_REQ_STATE_TIMEOUT                = -1;

    public static final int    REXEC_REQ_STATE_NETWORK_RECOVERING     = -2;

    public static final int    REXEC_REQ_STATE_SUCCESS                = 1;

    public static final int    REXEC_REQ_STATE_FAILED                 = -5;

    public static final int    REXEC_REQ_STATE_TEST                   = -3;//作业试跑 当状态为试跑时，agent自动忽略执行过程
    /**
     * for calendar config for activity
     */
    /**
     * follow is static field for calendar info
     */
    public static final int    CAL_NOLIMIT                            = 0;
    public static final int    CAL_WORKDAY                            = 1;
    public static final int    CAL_UN_WORKDAY                         = 2;
    /**
     * for running time slice.
     */
    public static final int    REPEAT                                 = -1000;
    public static final int    END                                    = -1010;
    public static final int    SAVE_END                               = -1020;
    public static final int    QUEUEUP                                = -1030;
    public static final int    RETURNTYPE                             = -1040;
    /**
     * for valid time caculating.
     */
    public static final int    IN_VALID_TIME                          = 0;
    public static final int    OUTOF_VALID_TIME                       = 10;
    /**
     * Recovery Policy
     */
    public static final int    RECOVERY_POLICY_REDO                   = 0x00000001;
    public static final int    RECOVERY_POLICY_SKIP                   = 0x00000002;
    public static final int    RECOVERY_POLICY_COMPENSATE             = 0x00000004;
    public static final int    RECOVERY_POLICY_MAN_PROCESS            = 0x00000008;

    public static final int    RECOVERY_POLICY_UNKNOWN                = 0x00000010;
    public static final int    RECOVERY_POLICY_EXEC_AFTER_ACT         = 0x00000020;
    public static final int    RECOVERY_POLICY_EXEC_BEFORE_ACT        = 0x00000040;
    public static final int    RECOVERY_POLICY_RAISE_EXCEPTION        = 0x00000080;
    public static final int    RECOVERY_POLICY_END_BRANCH             = 0x00000100;
    public static final int    RECOVERY_POLICY_EXEC_AFTER_STRUCT      = 0x00000200;

    public static final int    RECOVERY_POLICY_WAIT_REMOTE_ACT_OUTPUT = 0x00000400;

    public static final int    RECOVERY_POLICY_WITHOUT_COMPENSATE     = RECOVERY_POLICY_REDO | RECOVERY_POLICY_SKIP
            | RECOVERY_POLICY_MAN_PROCESS;

    public static boolean containsRecoveryPolicy ( int flag, int flagConstant )
    {
        return (flag & flagConstant) != 0;
    }

    /**
     * Flag true or false
     */
    public static final int      FLAG_FALSE                                   = 0;
    public static final int      FLAG_TRUE                                    = 1;

    /**
     * ExecAct recovery state
     */
    public static final int      EXECACT_RECOVERY_RESULT_UNKNOWN              = 0;
    public static final int      EXECACT_RECOVERY_RESULT_SUCCESS              = 1;
    public static final int      EXECACT_RECOVERY_RESULT_FAILED               = 2;
    /**
     * Recovery Point Type
     */
    public static final int      RECOVERY_POINT_LOCAL_ACT                     = 0;
    public static final int      RECOVERY_POINT_REMOTE_ACT                    = 1;
    public static final int      RECOVERY_POINT_STRUCT                        = 2;
    public static final int      RECOVERY_POINT_STRUCT_ENTRY                  = 3;
    public static final int      RECOVERY_POINT_STRUCT_EXIT                   = 4;

    public static final int      RECOVERY_POINT_CALL                          = 4;
    /**
     * Tr Execute State
     */
    public static final int      TRANSACTION_COMMITED                         = 0;
    public static final int      TRANSACTION_ROLLBACKED                       = 1;
    public static final int      TRANSACTION_UNKNOWN                          = 2;

    // 固定参数名
    public static final String   PARAM_AGENTIP                                = "agentip";
    public static final String   PARAM_PACKAGE                                = "packageName";
    public static final String   PARAM_FILEPACKAGE                            = "filePackage";
    public static final String   PARAM_NAME_TCDATE                            = "投产日期";
    public static final String   PARAM_VERSION_DES                            = "versiondes";

    /**
     * Remote Act state recovery
     */
    public static final int      REMOTE_ACT_RUNING                            = 0;
    public static final int      REMOTE_ACT_FINISH                            = 1;
    public static final int      REMOTE_ACT_UNKNOWN                           = 2;

    /** the pagination direction */
    public static final String   PAGINATION_DIRECTION_DESC                    = "desc";
    public static final String   PAGINATION_DIRECTION_ASC                     = "asc";

    public static final String   QUERY_TASK_TASK_NAME                         = "taskName";
    public static final String   QUERY_TASK_TASK_ID                           = "taskId";
    public static final String   QUERY_TASK_TASK_OWNER                        = "taskOwner";
    public static final String   QUERY_TASK_DES                               = "taskDes";
    public static final String   QUERY_TASK_START_TIME                        = "startTime";
    public static final String   QUERY_TASK_DEAD_LINE                         = "deadline";
    public static final String   QUERY_TASK_FINISH_TIME                       = "finishTime";
    public static final String   QUERY_TASK_STATUS                            = "status";
    public static final String   QUERY_TASK_PROJ_NAME                         = "projName";
    public static final String   QUERY_TASK_FLOW_NAME                         = "flowName";
    public static final String   QUERY_TASK_FLOW_ID                           = "flowId";
    public static final String   QUERY_TASK_FLOW_INSTANCE_NAME                = "flowInstanceName";
    public static final String   CHARSET_UTF_8                                = "utf-8";
    public static final int      VARCHAR_FIELD_MAX_LENGTH                     = 255;

    // AGENTAUTORENEW
    public static final int      AGENTAUTORENEW_VER                           = 0;
    public static final int      AGENTAUTORENEW_RENEW                         = 1;
    public static final int      AGENTAUTORENEW_SEND                          = 2;

    // Agent use now by yue_sun on 20180504 迁移数据自清理功能
    public static final int      AGENTRESOURCE_USE_ONE                        = 1;
    public static final int      AGENTRESOURCE_USE_TWO                        = 2;

    // HD CEHCK RESULT
    // 检查成功
    public static final int      HD_CHECK_OK                                  = 0;

    // 正在检查点
    public static final int      HD_CHECK_RUNNING                             = -1;

    // 正在检查点
    public static final int      HD_CHECK_NOCHECK                             = -5;

    // 默认检查正常值1
    public static final int      HD_CHECK_ERROR_ONE                           = 1;
    // 巡检启动工作流名 add by yunpeng_zhang 2015.10.31
    public static final String   STARTINSPECT_FLOWNAME                        = "CTR";

    // Add by liyang
    public static final int      JOB_NONE                                     = 0;
    public static final int      JOB_ONLINE                                   = -1;
    public static final int      JOB_OFFLINE                                  = -2;

    public static final int      JOB_REVIEW_ONLINE                            = 1;
    public static final int      JOB_REVIEW_OFFLINE                           = 2;

    public static final int      CENTER_MAN                                   = 1;
    public static final int      CENTER_PER                                   = 2;

    public static final int      REMOTE_AGENT_STATE_NORMAL                    = 0;
    public static final int      REMOTE_AGENT_STATE_EXCEPTION                 = 1;
    public static final int      REMOTE_AGENT_STATE_UPGRADING                 = 2;                                                                                                   // agent正在升级中
    public static final int      REMOTE_AGENT_STATE_OUTTIME                   = 3;                                                                                                   // Agent连接超时处于假死状态
    public static final int      REMOTE_AGENT_STATE_PAUSE                     = 4;                                                                                                   // agent暂停状态
    public static final int      REMOTE_AGENT_CHANGED                         = 1;
    public static final int      REMOTE_AGENT_UNCHANGED                       = 0;
    /**
     * 福建农信新增离线状态
     */
    public static final int      REMOTE_AGENT_STATE_OFFLINE                     = 9;

    public static final String   BANK_CMB                                     = "CMB";                                                                                           // 招商银行
    public static final String   BANK_CCB                                     = "CCB";                                                                                           // 中信银行
    public static final String   BANK_GDB                                     = "GDB";                                                                                           // bankCode001银行
    public static final String   BANK_JPRCB                                   = "JPRCB";                                                                                       // 吉林农信
    public static final String   BANK_BXB                                     = "BXB";                                                                                           // 百信银行
    public static final String   BANK_SHB                                     = "SHB";                                                                                           // 山东城商合作联盟
    public static final String   BANK_PFB                                     = "PFB";                                                                                                                                        // 浦发银行
    public static final String   BANK_CZ                                      = "CZBANK";                                                                                   //沧州银行
    public static final String   BANK_BCS                                     = "BCS";                                                                                      //长沙银行
    public static final String   BANK_CEB                                     = "CEB";
    public static final String   BANK_CEB_BR                                  = "CEB_BR";
    public static final String   BANK_JZ                                      = "JZ";
    public static final String   BANK_CIB                                     = "CIB";
    public static final String   BANK_ZYB                                     = "BANK_ZYB";                                                                                 //中原银行
    public static final String   BANK_PSBC                                    = "PSBC";
    public static final String   BANK_WF                                      = "WFBANK";
    public static final String   BANK_QLB                                     = "QLB";                                                                                      //齐鲁银行
    public static final String   BANK_CQNS                                    = "CQNS";
    public static final String   SYS_MAIN_TYPE                                = "TOPO";
    public static final String   BANK_FJNX                                    = "FJNX";
    public static final String   BANK_GFBZX                                   = "GFBZX";

    // 东莞农商
    public static final String   BANK_DRBC                                   = "DRBC";
    public static final String   COL_SYSNAME                                  = "isysName";                                                                                 // 变更自动化双人复核任务表扩展字段名：业务系统名
    public static final String   COL_INSNAME                                  = "iinsName";                                                                                 // 变更自动化双人复核任务表扩展字段名：实例名
    public static final String   COL_VERSION                                  = "iversion";                                                                                 // 变更自动化双人复核任务表扩展字段名：版本号
    public static final String   COL_ENVIDS                                   = "evnIds";                                                                                   // 变更自动化双人复核任务表扩展字段名：选择环境
    public static final String   COL_ORDERNUM                                 = "orderNum";                                                                                 // 变更自动化双人复核任务表扩展字段名：单号
    public static final String   COL_CORNVALUE                                = "cornValue";                                                                                 // 变更自动化双人复核任务表扩展字段名：单号
    public static final String   COL_ISGDTIMETASK                             = "isGdTimeTask";                                                                                 // 变更自动化双人复核任务表扩展字段名：单号
    public static final String   COL_GDAPPLYREASON                            = "gdApplyReason";                                                                                 // 变更自动化双人复核任务表扩展字段名：单号
    public static final String   COL_PKGPAHT                                    = "pkgPaht";                                                                                 // 变更自动化双人复核任务表扩展字段名：单号
    public static final int      IITEMTYPE_SUS                                = 2;                                                                                                   // 变更自动化双人复核系统类型：2变更自动化
    public static final long     SUS_ALL_SYS                                  = -8;                                                                                                 // 变更自动化所有变更自动化业务系统权限
    public final static String[] COL_ITEAM_SUS                                = { "evnIds", "isysName", "iinsName",
            "iversion" ,"orderNum","cornValue","pkgPaht"};     // 变更自动化双人复核任务表扩展字段
    public final static String[] COL_ITEAM_SUS_GRAPH                          = { "evnIds", "patchPath", "iversiondes",
            "instanceName", "iinsName", "isysName", "iid", "ibusnesSysIid", "issusflow", "isbackinstance",
            "deployeType", "isDeplyedFlow", "startType", "userInnerCode", "busSysType", "isystype", "isysid", "version",
            "iversionType" ,"hdfbTotal"};                                                                                                                                                                                       // 变更管理图形版双人复核任务表扩展字段
    public final static String[] COL_ITEAM_SUS_GDMP                                = { "evnIds", "isysName", "iinsName",
            "iversion" ,"orderNum","cornValue","isGdTimeTask","gdApplyReason"};     // 光大三人审核任务表扩展字段
    public static final String   COL_SYSID_DIS                                = "isysId";                                                                                     // 灾备双人复核任务表扩展字段名：业务系统id
    public static final String   COL_INSTANNAME_DIS                           = "iinstanName";                                                                           // 灾备双人复核任务表扩展字段名：切换原因
    public static final String   COL_SWITCHDIR_DIS                            = "switchDir";                                                                               // 灾备双人复核任务表扩展字段名：切换方向
    public static final String   COL_SWITCHCHECKTYPE_DIS                      = "switchCheckType";                                                                   // 灾备双人复核任务表扩展字段名：切换类型
    public static final String   COL_STARTTYPE_DIS                            = "startType";                                                                               // 灾备双人复核任务表扩展字段名：切换分类
    public static final String   COL_SWITCHINFO_DIS                           = "switchInfo";                                                                             // 灾备双人复核任务表扩展字段名：说明
    public static final String   COL_ITASKNAME_DIS                            = "itaskName";                                                                               // 灾备双人复核任务表扩展字段名：任务名称
    public static final String   COL_IWINDOWSTART_DIS                         = "iwindowStart";                                                                         // 灾备双人复核任务表扩展字段名：切换窗口:开始
    public static final String   COL_IWINDOWEND_DIS                           = "iwindowEnd";                                                                             // 灾备双人复核任务表扩展字段名：切换窗口:结束
    public static final String   COL_ITYPE_DIS                                = "itype";                                                                                       // 灾备双人复核任务表扩展字段名：类型
    public static final String   COL_RESOURCE_CONFIG                          = "iresourceStr";                                                                                       // 灾备双人复核任务表扩展字段名：资源配置JSON
    public final static String[] COL_ITEAM_DIS                                = { "iinstanName", "switchDir",
            "switchCheckType", "startType", "itaskName", "iwindowStart", "iwindowEnd", "itype", "iresourceStr" };                                                                                      // 灾备双人复核任务表扩展字段
    public final static String[] COL_ITEAM_DISNMG                                = { "iinstanName", "switchDir",
            "switchCheckType", "startType", "itaskName", "iwindowStart", "iwindowEnd", "itype", "iresourceStr" ,"issendsms","excepttime"};//nmg 灾备双人复核任务表扩展字段issendsms短信提醒，excepttime预计耗时
    /**
     * 定义公共的返回成功和信息的变量
     */
    public static final String   COMMON_SUCCESS                  = "success";
    public static final String   COMMON_MESSAGE                  = "message";
    public static final String   COMMON_MESSAGES                 = "messages";
    public static final String   COMMON_DATALIST                 = "dataList";
    public static final String   COMMON_TOTAL                    = "total";



    // add start by wangnan 作业调度双人复核相关
    // 作业调度双人复核任务表扩展字段
    public final static String[] COL_ITEAM_JOBSCHEDULING                      = { "PRJNAME", "FLOWNAME", "INSNAME",
            "ARGS", "EVAMAP", "type" };
    // add end by wangnan 作业调度双人复核相关

    // begin.added by manxi_zhao.
    // public static final int TASK_EMERGENCY_OPER = 3; // 应急
    public static final String   COL_SYSNAME_EM                               = "isysName";                                                                                 // 应急操作-双人复核任务表扩展字段名：业务系统名
    public static final String   COL_INSNAME_EM                               = "iinsName";                                                                                 // 应急操作-双人复核任务表扩展字段名：实例名
    public static final String   COL_VERSION_EM                               = "iversion";                                                                                 // 应急操作-双人复核任务表扩展字段名：版本号/预案名
    // public
    // static
    // final
    public static final String   COMMON_PARAM_CMSN_TIME                       = "CMSN_TIME";                                                                                                                                                                                          // int
    // IITEMTYPE_EM
    // =
    // 3;
    // //
    // 应急操作-双人复核系统类型：3应急操作
    public static final long     EM_ALL_SYS                                   = -8;                                                                                                 // 应急操作-所有变更自动化业务系统权限
    public final static String[] COL_ITEAM_EM                                 = { "isysName", "iinsName", "iversion" };                         // 应急操作-双人复核任务表扩展字段

    public static final String   COL_SYSTEMID_IC                              = "isystemId";                                                                               // 信息采集-双人复核任务表扩展字段名：模板ID(工程id)
    public static final String   COL_SYSNAME_IC                               = "isysName";                                                                                 // 信息采集-双人复核任务表扩展字段名：模板名
    public static final String   COL_INSNAME_IC                               = "iinsName";                                                                                 // 信息采集-双人复核任务表扩展字段名：任务名
    public static final String   COL_FLOWNAME_IC                              = "iflowName";                                                                               // 信息采集-双人复核任务表扩展字段名：工作流名
    public static final String   COL_DESC_IC                                  = "idesc";                                                                                       // 信息采集-双人复核任务表扩展字段名：说明
    public final static String[] COL_ITEAM_IC                                 = { "isystemId", "isysName", "iinsName",
            "iflowName", "idesc" };

    public static final String   COL_SYSTEMID_IC_SPDB                         = "isystemId";                                                                                                                     // 信息采集-双人复核任务表扩展字段名：模板ID(工程id)
    public static final String   COL_SYSNAME_IC_SPDB                          = "isysName";                                                                                                                        // 信息采集-双人复核任务表扩展字段名：模板名
    public static final String   COL_INSNAME_IC_SPDB                          = "iinsName";                                                                                                                        // 信息采集-双人复核任务表扩展字段名：任务名
    public static final String   COL_FLOWNAME_IC_SPDB                         = "iflowName";                                                                                                                     // 信息采集-双人复核任务表扩展字段名：工作流名
    public static final String   COL_DESC_IC_SPDB                             = "idesc";
    public static final String   COL_EXECSTRATEGY_IC_SPDB                     = "execStrategy";
    public static final String   COL_EXECTIME_IC_SPDB                         = "execTime";
    public static final String   COL_CORNVALUE_IC_SPDB                        = "cornValue";
    public static final String   COL_ODDNUMBERSTYPE_IC_SPDB                   = "oddNumbersType";
    public static final String   COL_ODDNUMBERSVALUE_IC_SPDB                  = "oddNumbersValue";
    // public static final String COL_MONTHVALUE_IC_SPDB = "monthValue";
    // public static final String COL_DATEVALUE_IC_SPDB = "dateValue";
    // public static final String COL_WEEKVALUE_IC_SPDB = "weekValue";
    // public static final String COL_HOURVALUE_IC_SPDB = "hourValue";
    // public static final String COL_MINUTEVALUE_IC_SPDB = "minuteValue";
    public static final String   COL_EXECCRONTIME_IC_SPDB                     = "execCronTime";
    public static final String   COL_JSONCRONARR_IC_SPDB                      = "jsonCronArr";
    public static final String   COL_EXECEFFECTIVETIME_IC_SPDB                = "execEffectiveTime";
    public static final String   COL_UPLOADFLAG_IC_SPDB                       = "uploadFlag";
    public static final String   COL_ROOTPATH_IC_SPDB                         = "rootPath";
    // public final static String[] COL_ITEAM_IC_SPDB = { "isystemId", "isysName", "iinsName",
    // "iflowName", "idesc", "execStrategy", "execTime", "cornValue", "oddNumbersType",
    // "oddNumbersValue",
    // "monthValue", "dateValue", "weekValue", "hourValue", "minuteValue", "execCronTime" }; //
    // 信息采集-双人复核任务表扩展字段
    public final static String[] COL_ITEAM_IC_SPDB                            = { "isystemId", "isysName", "iinsName",
            "iflowName", "idesc", "execStrategy", "execTime", "cornValue", "oddNumbersType", "oddNumbersValue",
            "jsonCronArr", "execCronTime", "execEffectiveTime", "uploadFlag", "rootPath" };

    // 【文件下发】双人复核表扩展字段
    public static final String   COL_FTPURL_FI_SPDB                           = "ftpUrl";
    public static final String   COL_ROOTPATH_FI_SPDB                         = "rootPath";
    public static final String   COL_WHOLEPATH_FI_SPDB                        = "wholePath";
    public final static String[] COL_ITEAM_FI_SPDB                            = { "ftpUrl", "rootPath", "wholePath" };

    // 定时任务 双人复核表扩展字段
    public static final String[] COL_ITEAM_TIMETASK                           = { "itaskName", "itaskrunTime",
            "itaskCommand", /*"iip",*/ "itaskGroup", "itimeouTmins", "idescribe", "itaskType", "iexpectedtime",
            "itaskGroupid", "itaskHandle" ,"tasklevel","executeCenter"};
    public static final String   COL_ITASKNAME_TIMETASK                       = "itaskName";                                                                                    // 定时任务-双人复核任务表扩展字段名：任务名称
    public static final String   COL_ITASKRUNTIME_TIMETASK                    = "itaskrunTime";                                                                                 // 定时任务-双人复核任务表扩展字段名：周期
    public static final String   COL_ITASKCOMMAND_TIMETASK                    = "itaskCommand";                                                                                 // 定时任务-双人复核任务表扩展字段名：执行脚本
    public static final String   COL_IIP_TIMETASK                             = "iip";                                                                                          // 定时任务-双人复核任务表扩展字段名：已添加设备IP
    public static final String   COL_ITASKGROUP_TIMETASK                      = "itaskGroup";                                                                                   // 定时任务-双人复核任务表扩展字段名：任务组
    public static final String   COL_ITASKGROUPID_TIMETASK                    = "itaskGroupid";                                                                                 // 定时任务-双人复核任务表扩展字段名：任务组id
    public static final String   COL_ITIMEOUTMINS_TIMETASK                    = "itimeouTmins";                                                                                 // 定时任务-双人复核任务表扩展字段名：超时时长
    public static final String   COL_IDESCRIBE_TIMETASK                       = "idescribe";                                                                                    // 定时任务-双人复核任务表扩展字段名：发布详情描述
    public static final String   COL_ITASKTYPE_TIMETASK                       = "itaskType";                                                                                    // 定时任务-双人复核任务表扩展字段名：任务类型
    public static final String   COL_IEXPECTEDTIME_TIMETASK                   = "iexpectedtime";
    public static final String   COL_ITASKHANDLE_TIMETASK                     = "itaskHandle";

    // end.added by manxi_zhao.
    public static final boolean  DB_COMPARE                                   = false;                                                                                           // 多写操作是否进行影响数据条数验证

    public static final String   IEAI_POLLSWITCH                              = "poolswitch";
    public static final short    IEAI_POLLSWITCH_DEFAULE                      = 0;
    public static final short    IEAI_POLLSWITCH_MXGRAPH                      = 1;

    // mxgraph节点样式
    public static final String   CELLSTYLE_CMD                                = "cmdStyle";
    public static final String   CELLSTYLE_BEGIN                              = "beginStyle";
    public static final String   CELLSTYLE_END                                = "endStyle";
    public static final String   CELLSTYLE_FTPDOWNLOAD                        = "ftpDownloadStyle";
    public static final String   CELLSTYLE_USERTASK                           = "usertaskStyle";
    public static final String   CELLSTYLE_DATAMAPPING                        = "datamappingStyle";
    public static final String   CELLSTYLE_AUTOKEY                            = "autoKeyStyle";
    public static final String   CELLSTYLE_CALLPLAN                           = "callPlanStyle";
    // runinfo_itype字段的状态码
    public static final String   RUNINFO_ITYPE_UT                             = "0";
    public static final String   RUNINFO_ITYPE_DEFINED_IN_FLOW                = "2";
    public static final String   RUNINFO_ITYPE_DEFINED_IN_TEMPLATE            = "3";

    // runinfo_ACTTYPE字段的状态码
    public static final String   IACTTYPE_SHELL_0R_PAHASE                     = "0";
    public static final String   IACTTYPE_UT                                  = "2";
    public static final String   IACTTYPE_PUB                                 = "13";
    public static final String   IACTTYPE_FTP_RETRIEVE                        = "3001";
    public static final String   IACTTYPE_FTP_DATAMAPPING                     = "4001";
    public static final String   IACTTYPE_AUTOKEY                             = "5001";
    public static final String   IACTTYPE_CALLPLAN                            = "6001";
    // CICD_runinfo_ACTTYPE字段的状态码 begin
    public static final String   IACTTYPE_CICD_SHELL                            = "1";   //shellCmd
    public static final String   IACTTYPE_CICD_UT                               = "2";   //UT
    public static final String   IACTTYPE_CICD_UT_BUILD_FINISHED                = "21";  //UT子类，构建完成
    public static final String   IACTTYPE_CICD_CALLFLOW                          = "101";  //工作流调用，拉起CI流程
    public static final String   IACTTYPE_CICD_PLUGIN_GIT                       = "102"; //100~200是插件
    public static final String   IACTTYPE_CICD_DEPLOY_CD_CONFIREM               = "103";  //部署状态更新
    public static final String   IACTTYPE_CICD_CONFIG_FILE_UPDATE               = "104"; //配置文件更新
    public static final String   IACTTYPE_CICD_CREATE_DOCKER                    = "107"; //创建docker实例
    public static final String   IACTTYPE_CICD_DESTROY_DOCKER                   = "108"; //销毁docker实例
    public static final String   IACTTYPE_CICD_SHARE_RESOURCE                   = "109"; //共享资源插件

    public static final Integer  PLUGIN_TYPE_SHARE_RESOURCE_TOOLS               =  1; //共享资源插件类型 工具
    public static final Integer  PLUGIN_TYPE_SHARE_RESOURCE_CI                  =  2; //共享资源插件类型 构建
    public static final Integer  PLUGIN_TYPE_SHARE_RESOURCE_CD                  =  3; //共享资源插件类型 部署
    public static final String   ISTAGETYPE_CI="1";                                 //阶段类型分类CI
    public static final String   ISTAGETYPE_CD="2";                                 //阶段类型分类CD
    public static final String   SCRIPT_MODULE_TYPE_TEMPLATE                    ="0";  //脚本类型: 0脚本模板
    public static final String   SCRIPT_MODULE_TYPE_ONLINE                      ="1";  //脚本类型: 1在线脚本
    public static final String   SCRIPT_MODULE_TYPE_REMOTE                      ="2";  //脚本类型: 2 远程脚本
    public static final String   SCRIPT_MODULE_TYPE_SHARERES                    ="3";  //脚本类型: 3 共享资源插件
    public static final String   SCRIPT_MODULE_CHILD_TYPE_SERVICE               ="1";  //脚本类型: 1 脚本服务化
    public static final String   SCRIPT_MODULE_CHILD_TYPE_JBK                   ="2";  //脚本类型: 2 脚本库
    public static final String   CICD_FLOWTASKTYPE_GENERAL                      ="0";  //流水线类型--常规
    public static final String   CICD_FLOWTASKTYPE_DEPLOY                       ="1";  //流水线类型--部署
    public static final String   CICD_FLOWTASKTYPE_BUILD                        ="2";  //流水线类型--构建
    public static final int   SCRIPT_TYPEK_SEHLL                             =0; //脚本种类:
    public static final int   SCRIPT_TYPEK_BAT                               =1;
    public static final int   SCRIPT_TYPEK_PERL                              =2;
    public static final int   SCRIPT_TYPEK_PYTHON                            =3;
    public static final int   SCRIPT_TYPEK_POWERSHELL                        =4;
    public static final short    IEAI_CICD_IMXGRAPH_ID                           = -38; // 变更管理_CICD
    public static final int   START_WAY_PAGE                                    = 0; //启动方式_页面
    public static final int   START_WAY_PULLING                                 = 1; //启动方式_轮询
    public static final int      NO_ENCRYP                                      = 0; //默认不加密
    public static final int      SM4_ENCRYP                                     = 1; //CICD流程图自定义参数加密类型--使用国密四
    public static final int IEAI_RUN_INST_RUN = 0;                                           // 主表'运行'
    public static final int IEAI_RUNINFO_INST_UN_RUN = 1;                                           // 子表'未运行'
    public static final int IEAI_RUN_INST_FINISH = 2;                                           // 子表'未运行'
    public static final int IEAI_RUN_INST_KILL = 3;                                           // 子表'未运行'
    public static final int IEAI_RUN_INST_OTH = 4;                                           // 子表'未运行'
    public static final int IEAI_RUNINFO_INST_OK = 0;                                           // 子表'未运行'
    public static final int IEAI_RUNINFO_INST_FAIL = 1;                                           // 子表'未运行'
    public static final int IEAI_RUNINFO_INST_FAIL_SKIP = 2;                                           // 子表'未运行'
    public static final int IRERUNFLAG_INIT = 0;
    public static final int IEAI_RUNINFO_PUB_ERROR_WAIT_SKIP = 51;                                     // pub步骤异常，等待重试中
    public static final String   CICD_SYSNAME                                  = "sysName";            // 变更自动化双人复核任务表扩展字段名：业务系统名
    public static final String   CICD_INSNAME                                  = "insName";            // 变更自动化双人复核任务表扩展字段名：实例名
    public static final String   CICD_VERSION                                  = "version";            // 变更自动化双人复核任务表扩展字段名：版本号  变更自动化双人复核任务表扩展字段名：选择环境
    public static final String   CICD_CORNORTIMEVALUE                          = "cornOrTimeValue";    // 变更自动化双人复核任务表扩展字段名：执行策略内容
    public static final String   CICD_ISCOREORTIME                             = "isCoreOrTime";              // 变更自动化双人复核任务表扩展字段名：是否定时
    public static final String   CICD_DESC                                     = "desc";                // 变更自动化双人复核任务表扩展字段名：描述
    public static final String   CICD_FLOWTASKRUNID                            = "flowtaskrunid";       // 变更自动化双人复核任务表扩展字段名：运行id
    public final static String[] COL_ITEAM_SUS_CICD                            = {"sysName", "insName", "version","cornOrTimeValue","isCoreOrTime","desc","flowtaskrunid"};     // 福建农信CICD任务流水线保存任务表扩展字段
    public static final String SCRIPT_EXEC_COUNT_NODE_FRONT = "/scriptShardingCounterCount";
    public static final String SCRIPT_EXEC_BATCH_COUNT = "/scriptBatchCount";
    public static final String SCRIPT_EXEC_COUNT_NODE_LATER = "/statisticsCounter/finishedFragmentation=";
    public static final String SCRIPT_EXEC_COUNT_NODE_FRAGMENTATION = "/sharedCounter/sharedCounter-";
    public static final String SCRIPT_EXEC_COUNT_EVERY_FRA = "/sharedCounter_ever_fra/sharedCounter";

    //接口任务申请，短信审核、微信提醒等脚本服务化模块标识
    public static final String SCRIPT_APPLICATION_MODEL_FLAG = "scriptservice";
    //脚本服务化，任务申请审批通过提醒标识
    public static final String SCRIPT_AUDIT_PASS_FLAG = "SCRIPT_AUDIT_PASS_FLAG";
    //脚本服务化，任务申请审批打回提醒标识
    public static final String SCRIPT_AUDIT_NOT_PASS_FLAG = "SCRIPT_AUDIT_NOT_PASS_FLAG";
    //脚本服务化，任务开始提醒标识
    public static final String SCRIPT_START_TASK_FLAG = "startTaskAlarm";
    //脚本服务化，任务完成提醒标识
    public static final String SCRIPT_TASK_FINISHED_FLAG = "finishTaskAlarm";

    // CICD_runinfo_ACTTYPE字段的状态码 end
    public static final long    CICD_MAIN_FLOW                                 =1L;
    public static final long    CICD_SON_FLOW                                  =0L;
    // 操作系统
    public static final String   OS_WIN                                       = "1";
    public static final String   OS_NOT_WIN                                   = "2";
    public static final String   pubOsType                                    = "pubOsType";

    // 公共参数
    public static final String   COMMON_PARAM_UPLOAD_FILENAME                 = "UPLOAD_FILENAME";                                                                   // 上传文件名称（xxx.zip）
    public static final String   COMMON_PARAM_PKGPATH                         = "PKGPATH";                                                                                   // 上传路径(上传文件后，返回的全路径)
    public static final String   COMMON_PARAM_ZPKPKGINFO                      = "ZPKPKGINFO";                                                                                   // njbank制品下载路径
    public static final String   COMMON_PARAM_SOURCE_PATH                     = "SOURCE_PATH";                                                                           // 上传路径的倒数2,3域(相对路径)
    public static final String   COMMON_PARAM_PACKAGE_NAME                    = "PACKAGE_NAME";                                                                         // pub服务解析包后,json中子包名称
    public static final String   COMMON_PARAM_PACKAGE_NAME_ALL                = "PACKAGE_NAME_ALL";                                                                 // pub服务解析包后,json中子包名称的拼接,用';;;'进行分隔
    public static final String   COMMON_PARAM_CFG_FILE_NAME                   = "CFG_FILE_NAME";                                                                       // 配置文件.config
    public static final String   COMMON_PARAM_VERSION_NUM                     = "VERSION_NUM";                                                                           // 版本号
    public static final String   COMMON_PARAM_CMSN_DATE                       = "CMSN_DATE";                                                                               // 投产日期
    public static final String   COMMON_PARAM_BUS_SYS_ABB                     = "BUS_SYS_ABB";                                                                           // 业务系统简称
    public static final String   COMMON_PARAM_SVN_PATH                        = "SVN_PATH";                                                                                 // SVN路径
    public static final String   COMMON_PARAM_FTP_ROOT_PATH                   = "FTP_PATH";                                                                                 // FTP根路径(在entegor.config中进行配置)
    public static final String   COMMON_PARAM_AGENT_IP                        = "AGENT_IP";                                                                                 // 当前正在运行服务器的ip
    public static final String   COMMON_PARAM_SERVER_NAME                     = "SERVER_NAME";                                                                           // 当前正在运行服务器的名称
    public static final String   COMMON_PARAM_APP_FLAG                        = "APP_FLAG";                                                                                 // 当前正在运行服务器的应用标示
    public static final String   COMMON_PARAM_XQ                              = "XQ";                                                                                             // 需求
    public static final String   COMMON_PARAM_ISDECIDED                       = "ISDECIDED";                                                                               // 定版否
    public static final String   COMMON_PARAM_ISFIRST                         = "ISFIRST";                                                                                   // 首版否
    public static final String   COMMON_PARAM_STARTUSER                       = "STARTUSER";                                                                               // 启动用户
    public static final String   COMMON_PARAM_AGENTIP_EXCEL                   = "AgentIp";                                                                                   // Agentip
    public static final String   COMMON_PARAM_AGENTIP_TIMESTAMP               = "timeStamp";                                                                               // timeStamp
    public static final String   COMMON_PARAM_ENV_NAME                        = "ENV_NAME";
    public static final String   COMMON_PARAM_AGENTHOSTNAME_EXCEL             = "AgentHostName";                                                                           // AgentHostName
    public static final String   COMMON_PARAM_HDFB_TOTAL                      = "HDFB_ALL";                                                                                 //灰度发布全部
    public static final String   COMMON_PARAM_HDFB_CUR                        = "HDFB_CUR";                                                                                   //灰度发布当前波次
    public static final String   COMMON_PARAM_HDFB_UUID                       = "HDFB_UUID";                                                                                  //灰度发布UUID
    public static final String   COMMON_PARAM_HDFB_POLICY                     = "HDFB_POLICY";                                                                                //灰度发布策略
    public static final String   COMMON_PARAM_PKGNAME                         = "PKGNAME";          //南京银行单包名称
    public static final String   COMMON_PARAM_PKGNAMEALL                      = "PKGNAMEALL";       //南京银行多包用逗号隔开
    public static final String   COMMON_PARAM_FTPINFO                         = "FTPINFO";          //南京银行ftp地址，端口，用户名，密码，路径

    // 公共参数 整合成一个数组
    public static final String[] COMMON_PARAM_ARR                             = { COMMON_PARAM_UPLOAD_FILENAME,
            COMMON_PARAM_PKGPATH, COMMON_PARAM_SOURCE_PATH, COMMON_PARAM_PACKAGE_NAME, COMMON_PARAM_PACKAGE_NAME_ALL,
            COMMON_PARAM_CFG_FILE_NAME, COMMON_PARAM_VERSION_NUM, COMMON_PARAM_CMSN_DATE, COMMON_PARAM_BUS_SYS_ABB,
            COMMON_PARAM_SVN_PATH, COMMON_PARAM_FTP_ROOT_PATH, COMMON_PARAM_AGENT_IP, COMMON_PARAM_SERVER_NAME,
            COMMON_PARAM_APP_FLAG, COMMON_PARAM_XQ, COMMON_PARAM_ISDECIDED, COMMON_PARAM_ISFIRST,
            COMMON_PARAM_STARTUSER, COMMON_PARAM_AGENTIP_EXCEL, COMMON_PARAM_AGENTIP_TIMESTAMP, COMMON_PARAM_ENV_NAME,
            COMMON_PARAM_AGENTHOSTNAME_EXCEL,COMMON_PARAM_HDFB_TOTAL,COMMON_PARAM_HDFB_CUR,COMMON_PARAM_HDFB_UUID,COMMON_PARAM_HDFB_POLICY,COMMON_PARAM_PKGNAME,COMMON_PARAM_PKGNAMEALL,COMMON_PARAM_FTPINFO };

    // pub服务器解析包的json属性键
    public static final String   ANALYSYS_PKG_JSONKEY_PKGNAME                 = "pkgname";
    public static final String   ANALYSYS_PKG_JSONKEY_MODULETYPE              = "moduleType";
    public static final String   ANALYSYS_PKG_JSONKEY_APPTYPE                 = "appType";
    public static final String   ANALYSYS_PKG_JSONKEY_MODIFYTYPE              = "modifyType";
    public static final String   ANALYSYS_PKG_JSONKEY_COVERTYPE               = "coverType";

    public static final String   WITHOUT_PACKAGENAME                          = "【无包名】";
    public static final String   PATCH_PATH_NULL                              = "-1";
    public static final String   ACT_STATE_UN_ALLOWED_EXEC                    = "未执行";

    // 任务状态码
    public static final int      WORKTASK_STATE_CREATE                        = 0;
    public static final int      WORKTASK_STATE_SUBMIT                        = 1;
    public static final int      WORKTASK_STATE_BACK                          = 2;
    public static final int      WORKTASK_STATE_STOP                          = 3;
    public static final int      WORKTASK_STATE_CONFIRM                       = 4;
    public static final int      WORKTASK_STATE_START                         = 5;
    public static final int      WORKTASK_STATE_END                           = 6;

    public static final int      RESOURCE_FLOW                                = 1;
    public static final int      RESOURCE_ACT                                 = 2;

    // 环境构建
    public static final int      ENV_BUILD_OPT_BEGINE                         = 1;
    public static final int      ENV_BUILD_OPT_BACK                           = 2;
    public static final int      ENV_BUILD_OPT_FISHESH_SEND                   = 3;
    public static final int      ENV_BUILD_OPT_FAIL_SEND                      = 4;

    public static final int      ENV_BUILD_STATE_UNRUN                        = -1;
    public static final int      ENV_BUILD_STATE_RUNNING                      = 0;
    public static final int      ENV_BUILD_STATE_FISHESH                      = 2;
    public static final int      ENV_BUILD_STATE_FAIL                         = 3;
    public static final int      ENV_BUILD_STATE_BACK                         = 4;
    public static final String   PUSHFILE_SHELL                               = "pushFileShell.sh";                                                                 // 脚本推送非windows执行脚本
    public static final String   PUSHFILE_CMD                                 = "pushFileCmd.bat";                                                                   // 脚本推送windows执行脚本

    public static final int      SLEEPTIME60                                  = 60 * 1000;

    public static final String   REQUEST_KEY_COLLECT                          = "collect";
    public static final String   REQUEST_KEY_NESSUS                          = "nessus_";

    // 定时任务
    public static final String   TIME_TASK_FORM_ITSM                          = "ITSM";
    public static final String   TIME_TASK_FORM_SYS                           = "系统";

    // 定时任务类型
    public static final int      TIME_TASK_TYPE_MORETIME                      = 2;                                                                                               // 一天执行多次，轮询任务
    public static final int      TIME_TASK_TYPE_ONETIME                       = 1;                                               // 一天只跑一次，定时任务
    // 定时任务全部生效
    public static final String   TIME_TASK_EFFECT                             = "allStart";                                                                                  // 定时任务全部生效
    // 定时任务失效生效
    public static final String   TIME_TASK_STOP                               = "allStop";

    // 组织sql语句通用
    public static final String   STR_EXESQLS                                  = "exeSqls";
    public static final String   STR_ROLLBACKSQLS                             = "rollbackSqls";
    public static final String   STR_BASECONN                                 = "baseConn";
    public static final String   STR_DBCONNS                                  = "dbConns";

    // 返回消息常用字符串
    public static final String   STR_SUCCESS                                  = "success";
    public static final String   STR_MESSAGE                                  = "message";
    public static final String   STR_RESULT                                   = "result";
    public static final String   STR_DATALIST                                 = "dataList";
    public static final String   STR_TOTAL                                    = "total";

    public static final int      SCRIPT_CREATE_STATE                          = -1;                                                                                                 // 创建（没有展示）
    public static final int      SCRIPT_INIT_STATE                            = 1;                                                                                                       // 初始化
    public static final int      SCRIPT_RUNNING_STATE                         = 10;                                                                                               // 运行
    public static final int      SCRIPT_PARTRUNNING_STATE                     = 11;                                                                                                   // 部分运行
    public static final int      SCRIPT_FINISH_STATE                          = 20;                                                                                                    // 完成
    public static final int      SCRIPT_FAIL_STATE                            = 30;                                                                                                // agent连接异常
    public static final int      SCRIPT_KILL_STATE                            = 60;                                                                                                   // 终止
    public static final int      SCRIPT_FINISH_FAIL_STATE                     = 40;
    public static final int      SCRIPT_TO_AUTO_NET_FAIL_STATE                = 41;//脚本服务化连接不上agent，给网络自动化推送码值
    public static final int      SCRIPT_RUNNING_FAIL_STATE                    = 50;
    public static final int      SCRIPT_SKIP_STATE                            = 5;                                                                                                  // 忽略

    public static final int[]    SCRIPT_FINISH_SET                            = { SCRIPT_FINISH_STATE,
            SCRIPT_KILL_STATE, SCRIPT_FINISH_FAIL_STATE, SCRIPT_SKIP_STATE, Constants.SCRIPT_FAIL_STATE };

    public static final int      SCRIPT_FLOW_NORMAL                           = 1;
    public static final int      SCRIPT_FLOW_FILE                             = 2;
    public static final int      SCRIPT_FLOW_AGENT                            = 3;
    public static final int      SCRIPT_ENV_TEST                              = 0;
    public static final int      SCRIPT_ENV_PROD                              = 1;

    public static final short    SCRIPT_STATUS_DRAFT                          = -1;                                                                                                 // 草稿
    public static final short    SCRIPT_STATUS_AUDITING                       = 2;                                                                                                   // 审核中
    public static final short    SCRIPT_STATUS_ONLINE                         = 1;                                                                                                   // 已上线
    public static final short    SCRIPT_STATUS_SHARE                          = 3;                                                                                                   // 已共享

    public static final short    SCRIPT_TRY_STATUS_RUNNING                    = 1;                                                                                                   // 运行
    public static final short    SCRIPT_TRY_STATUS_FINISH                     = 2;                                                                                                   // 结束

    public static final int      SCRIPT_AUTI_SAVE_STATE                       = 0;
    public static final int      SCRIPT_AUTI_SAVEANDSUBMIT_STATE              = 1;
    public static final int      SCRIPT_AUTI_REBACK_STATE                     = 2;
    public static final int      SCRIPT_AUTI_STARTFAIL_STATE                  = 4;
    public static final int      SCRIPT_AUTI_FINISHED_STATE                   = 5;
    public static final int      SCRIPT_AUTI_KILL_STATE                       = 6;
    public static final int      SCRIPT_AUTI_CANCEL_STATE                     = 8;                                                 // 已撤回
    public static final int      SCRIPT_AUTI_PASS_STATE                       = 10;
    public static final int      SCRIPT_AUTI_PASS_SPLIT_STATE                 = 11;
    public static final int      SCRIPT_AUTI_DELAY_STATE                      = 12;
    public static final int      SCRIPT_SHUTDOWN_STOP_STATE                   = 13;//关机维护终止状态 bankCode001使用

    public static final short    IEAI_ZL_SB_NUM                               = 1;                                                                                                   // 基础信息源
    public static final short    IEAI_ZL_SYS_NUM                              = 2;                                                                                                   // 运维管理
    public static final short    IEAI_ZL_24HOUR_ACT                           = 3;                                                                                                   // 作业调度
    public static final short    IEAI_ZL_24HOUT_WARN                          = 4;                                                                                                   // 信息采集
    public static final short    IEAI_ZL_DD_NUM                               = 5;                                                                                                   // 变更管理
    public static final short    IEAI_ZL_HC_NUM                               = 6;                                                                                                   // 灾备切换
    public static final short    IEAI_ZL_SUS_NUM                              = 7;                                                                                                   // 定时任务
    public static final short    IEAI_ZL_ZB_NUM                               = 8;                                                                                                   // 应急操作
    public static final short    IEAI_ZL_YJ_NUM                               = 9;                                                                                                   // 应急操作
    public static final short    IEAI_ZL_JB_NUM                               = 10;

    // 数据清理状态标识
    public static final int      CLEARDATA_NOCLEAR                            = 0;
    // 清理中
    public static final int      CLEARDATA_CLEARING                           = 1;
    // 清理完成
    public static final int      CLEARDATA_CLEARFINSH                         = 2;
    public static final int      CLEARDATA_CLEARFAIL                          = 3;

    /**
     * Flag is ISPAUSE
     */
    public static final int      SENDREMOTE_NOPAUSE                           = 0;
    public static final int      SENDREMOTE_PAUSE                             = 1;

    /**
     * Flag is mainserver
     */
    public static final int      SERVER                                       = 0;
    public static final int      MAIN_SERVER                                  = 1;

    public static final int      IEAI_GROUPTYPE                               = 0;                                                                                                   // 集群组内分组类别

    public static final String   SCRIPT_SERVICE                               = "scriptService";
    public static final String   SCRIPT_SERVICE_PARA                          = "scriptServicePara";
    public static final String   SCRIPT_SERVICE_ATTCH                         = "scriptServiceAttch";
    public static final String   SCRIPT_SERVICE_MX                            = "scriptServiceMx";
    public static final String   SCRIPT_SERVICE_MX_RELATION                   = "scriptServiceMxRelation";
    public static final String   SCRIPT_SERVICE_SYS_RELATION                  = "scriptServiceSysRelation";
    public static final String   SCRIPT_SERVICE_SCRIPT_WARNNING               = "scriptServiceScriptWarnning";

    // static of monitor add by txl 20180108
    public static final int      MONITOR_VALIDTIME_UNSET                      = 0;
    public static final int      MONITOR_VALIDTIME_SET                        = 1;
    public static final int      MONITOR_INSTANCE_NEW                         = 0;
    public static final int      MONITOR_INSTANCE_LAST                        = 1;
    public static final int      MONITOR_INSTANCE_HAND                        = 2;
    public static final int      MONITOR_TYPE_ACT                             = 0;
    public static final int      MONITOR_TYPE_FLOW                            = 1;
    public static final int      MONITOR_EXPRESS_FALSE                        = 0;
    public static final int      MONITOR_EXPRESS_TRUE                         = 1;
    public static final int      MONITOR_STATE_MONITOR                        = 0;
    public static final int      MONITOR_STATE_END                            = 1;

    //
    public static final String   SUS_ACT_ENABLE                               = "0";                                                                                               // 正常
    public static final String   SUS_ACT_DISABLE                              = "1";                                                                                               // 不可用
    public static final String   DEPLOYMENT_PHASE                             = "部署分类";
    public static final int      STATE_IBRANCH                                = 31;                                                                                                 // 分支条件不满足不运行
    public static final int      STATE_CUTOFF                                 = 32;                                                                                                 // 裁减掉

    // agent监控状态标志
    public static final int      AGENTSTATE_DEAD                              = 0;
    public static final int      AGENTSTATE_ALIVE                             = 1;
    public static final int      AGENTSTATE_WARING                            = 2;

    // 活动状态
    public static final int      ACT_DEFAULT                                  = 0;
    public static final int      ACT_HANGUP                                   = 1;
    public static final int      ACT_FINISHED                                 = 2;
    public static final int      ACT_DISABLE                                  = 3;

    // 工程版本号
    public static final Version  VERSION                                      = new Version(1, 0, 0);

    // 工作流优先级
    public static final int      FLOW_PRIORY_ONE                              = 1;
    public static final int      FLOW_PRIORY_TWO                              = 2;
    public static final int      FLOW_PRIORY_THREE                            = 3;
    public static final int      FLOW_PRIORY_FOUR                             = 4;
    public static final int      FLOW_PRIORY_FIVE                             = 5;

    // actElement Type
    public static final String   ACT_ELEMENT_TYPE                             = "EXCEL";
    public static final String   ACT_ELEMENT_DAYSTARTTYPE                     = "DAYSTARTEXCEL";
    public static final String   ACT_ELEMENT_PACKDAYSTARTTYPE                 = "PACKDAYSTARTTYPE";

    // 裁减掉
    public static final String   NUM_REPORT                                   = "reportnum";
    public static final String   ACT_STATE_ERROR_ANGETCONNECT                 = "Fail:DisConnectAgent";
    // 添加活动接管状态
    public static final String   ACT_STATE_PERSON                             = "Person";
    // 添加活动超时状态
    public static final String   ACT_STATE_TIMEOFF                            = "Fail:Timeoff";
    public static final String   AVG_ACTRUN_FAILNUMSTATIC                     = "failnumswitch";

    // 作业调度双人复核系统类型：4作业调度
    public static final int      IITEMTYPE_JOBSCHEDULING                      = 4;

    // update for actruntimepart by txl 20170831
    public static final String   PART_INSERT                                  = "insert";
    public static final String   PART_UPDATE                                  = "update";
    public static final String   PART_TABLE_ACTRUNTIMEPART                    = "IEAI_ACTRUNTIME_PART";
    public static final String   PART_TABLE_VALIDPART                         = "IEAI_ACTRUNTIME_VALIDPART";
    public static final String   PART_TABLE_UPDATE                            = "tableUpdate";
    public static final String   PART_UPDATE_TASK                             = "updatetask";
    public static final String   PART_UPDATE_UTTASK                           = "updateuttask";
    public static final String   PART_UPDATE_ERROR                            = "updateerror";
    public static final String   PART_SAVE_TASKERROR                          = "savetaskerror";
    public static final String   PART_SETENTTIME                              = "setenetime";
    public static final String   PART_CLEARKILL                               = "clearkill";
    public static final String   PART_UPDATE_REMOTE                           = "updateRemote";

    // add by wangnan 20180315 工程类型：作业调度 1.0
    public static final double   PROJECT_TYPE_JOBSCHEDULING                   = 1.0;

    // add by wangnan 20180412 数据清理时间
    public static final String   CLEAR_ACT_TIME_JOBSCHEDULING                 = "cleartime";
    public static Long           AUDIT_TYPE_PROJECT                           = 10L;

    public static final String   AVG_SWITCH                                   = "avgTimeClose";
    public static final String   AVG_SETTTIME                                 = "setTime";
    public static final String   AVG_NUM_MONTH                                = "monthnum";
    public static final String   AVG_NUM_WEEK                                 = "weeknum";
    public static final String   AVG_NUM_DAY                                  = "daynum";
    public static final String   AVG_NUM_DEF                                  = "defnum";
    public static final String   DAILY_CHECK_TIME                             = "dailyCheckTime";
    public static final String   AVG_CONSUME_SWITCH                           = "avgConsumeTimeSwitch";
    public static final String   AVG_CONSUME_SETTTIME                         = "setConsumeTime";

    // add by yuxh 20180521 begin
    public static final int      ACT_TYPE_INT_SHELLCMD                        = 1;
    public static final int      ACT_TYPE_INT_USERTASK                        = 2;
    public static final int      ACT_TYPE_INT_DELAY                           = 3;
    public static final int      ACT_TYPE_INT_DATA_MAPPING                    = 4;
    public static final int      ACT_TYPE_INT_RECEIVE_MAIL                    = 5;
    public static final int      ACT_TYPE_INT_SEND_MAIL                       = 6;
    public static final int      ACT_TYPE_INT_EXTERNAL_FUNCTION               = 7;
    public static final int      ACT_TYPE_INT_HTTP                            = 8;
    public static final int      ACT_TYPE_INT_START                           = 9;
    public static final int      ACT_TYPE_INT_END                             = 10;
    public static final int      ACT_TYPE_INT_CALL_FLOW                       = 11;
    public static final int      ACT_TYPE_INT_SEQ_STRUCT_ENTYR                = 12;
    public static final int      ACT_TYPE_INT_SEQ_STRUCT_EXIT                 = 13;
    public static final int      ACT_TYPE_INT_LOOP_STRUCT_ENTRY               = 14;
    public static final int      ACT_TYPE_INT_LOOP_STRUCT_EXIT                = 15;
    public static final int      ACT_TYPE_INT_PARALLEL_STRUCT_ENTRY           = 16;
    public static final int      ACT_TYPE_INT_PARALLEL_STRUCT_EXIT            = 17;
    public static final int      ACT_TYPE_INT_EXCEPTION                       = 18;
    public static final int      ACT_TYPE_INT_ELSE_TYPE                       = 19;
    public static final int      ACT_TYPE_INT_LOADDATA                        = 20;
    public static final int      ACT_TYPE_INT_STOREPROCEDURE                  = 21;
    public static final int      ACT_TYPE_INT_UNLOADDATA                      = 22;
    public static final int      ACT_TYPE_INT_FTP                             = 23;
    public static final int      ACT_TYPE_INT_LOADDATANEW                     = 24;
    public static final int      ACT_TYPE_INT_STOREPROCEDURENEW               = 25;
    public static final int      ACT_TYPE_INT_UNLOADDATANEW                   = 26;
    public static final int      ACT_TYPE_INT_FTPNEW                          = 27;
    public static final int      ACT_TYPE_INT_DBADAPTOR                       = 28;
    public static final int      ACT_TYPE_INT_AZSTATUS                        = 29;

    public static final String   ACT_TYPE_SHELLCMD                            = "ShellCmd";
    public static final String   ACT_TYPE_USERTASK                            = "UserTask";
    public static final String   ACT_TYPE_DELAY                               = "Delayer";
    public static final String   ACT_TYPE_DATA_MAPPING                        = "Data Mapping";
    public static final String   ACT_TYPE_RECEIVE_MAIL                        = "Receive Mail";
    public static final String   ACT_TYPE_SEND_MAIL                           = "Send Mail";
    public static final String   ACT_TYPE_EXTERNAL_FUNCTION                   = "External function";
    public static final String   ACT_TYPE_HTTP                                = "Http";
    public static final String   ACT_TYPE_LOADDATA                            = "loaddata";
    public static final String   ACT_TYPE_STOREPROCEDURE                      = "StoreProcedure";
    public static final String   ACT_TYPE_UNLOADDATA                          = "unloaddata";
    public static final String   ACT_TYPE_FTP                                 = "FTP Upload File";
    public static final String   ACT_TYPE_LOADDATANEW                         = "loaddatanewadpter";
    public static final String   ACT_TYPE_STOREPROCEDURENEW                   = "dbpronewadaptor";
    public static final String   ACT_TYPE_UNLOADDATANEW                       = "unloadnewdataadaptor";
    public static final String   ACT_TYPE_FTPNEW                              = "ftpnewadaptor";
    public static final String   ACT_TYPE_DBADAPTOR                           = "dbadaptor";
    public static final String   ACT_TYPE_DBPROADAPTOR                        = "dbproadaptor";
    public static final String   ACT_TYPE_AZSTATUS                            = "azstatus";

    public static String         COMMAND                                      = "command";
    public static String         STARTIN                                      = "startIn";
    // add by yuxh 20180521 end

    // 区分工作流为手动或自动，1为自动
    public static final int      AUTOFLOW_AUTO                                = 1;
    public static final int      AUTOFLOW_HAND                                = 0;
    /**工程上传类型，0为studio,1为excel，2为Excel日启动**/
    public static final int      PROJECT_UP_STUDIO                            = 0;
    public static final int      PROJECT_UP_EXCEL                             = 1;
    public static final int      PROJECT_UP_EXCEL_DAYSTART                    = 2;

    // 标准运维-文件下发-ftp配置类型
    public static final int      DAILY_FTP_TYPE                               = 35;

    // vmmanager state constants
    public static final int      ACT_STATE_SVNCREATE                          = 0;
    public static final int      ACT_STATE_SVNCREATEFAIL                      = 1;
    public static final int      ACT_STATE_SVNCREATESUCC                      = 2;
    public static final int      ACT_STATE_ANTCREATE                          = 3;
    public static final int      ACT_STATE_ANTCREATEFAIL                      = 4;
    public static final int      ACT_STATE_ANTCREATESUCC                      = 5;
    public static final int      ACT_STATE_AGENTERROR                         = 6;
    // 版本合并中状态 licheng_zhao
    public static final int      ACT_STATE_MERGECREATE                        = 7;
    // 版本合并失败状态 licheng_zhao
    public static final int      ACT_STATE_MERGECREATEFAIL                    = 8;
    // 版本合并中成功状态 licheng_zhao
    public static final int      ACT_STATE_MERGECREATEESUCC                   = 9;
    // 回归基线中状态 licheng_zhao
    public static final int      ACT_STATE_BACKBASELINECREATE                 = 10;
    // 回归基线失败状态 licheng_zhao
    public static final int      ACT_STATE_BACKBASELINECREATEFAIL             = 11;
    // 回归基线成功状态 licheng_zhao
    public static final int      ACT_STATE_BACKBASELINECREATEESUCC            = 12;
    /**
     * @Fields COLLECT_CONFIG : 采集配置
     */
    public static final String   COLLECT_CONFIG                               = "collectConfig";

    public static final String   SUS_DATALIST                                 = "dataList";
    public static final String   SUS_SUCCESS                                  = "success";
    public static final String   SUS_MESSAGE                                  = "message";

    // 锁表类型
    public static final String   LOCK_SUS_START                               = "lock_SusStart";

    //邮储 脚本服务化定时任务 创建zk事件监听节点路径
    public static final String PSBC_SCRIPT_SERVICE_PATH = "/psbc_scriptServiceIpsCount";
    //脚本服务化 计算分批执行 zk节点存储的父路径
    public static final String SCRIPT_EXECUTE_CALC_BATCH_PATH = "/scriptExecuteCalcBatch";
    //脚本服务化 队列模式记录ipsId节点路径
    public static final String SCRIPT_QUEUE_IPSID_PATH = "/scriptQueueIpsidPath";
    //脚本服务化执行任务 创建eachNum同级节点  节点里的内容为 ignore=0&partExec=1&status=11
    public static final String EXEC_STRATEGY_PARAMETERS = "execStrategyParameters";

    //privider端 curator连接zookeeper的namespace，创建分布式锁的时候是在该节点下/ieai_curator_scriptService
    public static final String CURATOR_NAMESPACE_SCRIPT_SERVICE = "ieai_curator_scriptService";

    // 脚本服务化 我的脚本 下发记录表 状态
    // 初始状态 待下发
    public static final int      SCRIPT_ISSUERECORD_STATUS_INIT               = 0;
    // 下发成功
    public static final int      SCRIPT_ISSUERECORD_STATUS_SUCCEED            = 1;
    // 正在下发
    public static final int      SCRIPT_ISSUERECORD_STATUS_DOING              = 2;
    // 下发异常
    public static final int      SCRIPT_ISSUERECORD_STATUS_ERROR              = 3;
    // md5不同 邮储
    public static final int      SCRIPT_ISSUERECORD_MD5_ERROR              = 4;

    // 数据库备份 策略类型区分，0为备份，1为恢复
    public static final int      STRATEGY_TYPE_BACK                           = 0;
    public static final int      STRATEGY_TYPE_RES                            = 1;
    // 数据库备份 策略类型区分，0为手动，1为自动
    public static final int      STRATEGY_TYPE_HANDLE                         = 0;
    public static final int      STRATEGY_TYPE_AUT                            = 1;
    // 数据备份模版工程名称
    public static final String   DBBACK_PROJECTNAME                           = "DbBackRecoverTemplate";
    // 数据库备份恢复平台输入参数
    public static final String   DBBACK_BACK_FLOWINPUT_AGENTIP                = "AGENTIP";
    public static final String   DBBACK_BACK_FLOWINPUT_STRATEGYID             = "STRATEGYID";
    public static final String   DBBACK_BACK_FLOWINPUT_BACKPATH               = "BACKPATH";
    public static final String   DBBACK_BACK_FLOWINPUT_DBIP                   = "DBIP";
    public static final String   DBBACK_BACK_FLOWINPUT_DBPORT                 = "DBPORT";
    public static final String   DBBACK_BACK_FLOWINPUT_BACKUSER               = "BACKUSER";
    public static final String   DBBACK_BACK_FLOWINPUT_BACKPASS               = "BACKPASS";
    public static final String   DBBACK_BACK_FLOWINPUT_BACKTABLES             = "BACKTABLES";
    public static final String   DBBACK_BACK_FLOWINPUT_BACKDBNAME             = "BACKDBNAME";
    public static final String   DBBACK_BACK_FLOWINPUT_BACKSCRIPT             = "BACKSCRIPT";
    public static final String   DBBACK_BACK_FLOWINPUT_BACKDATE               = "BACKDATE";
    public static final String   DBBACK_BACK_FLOWINPUT_BACKFILENAME           = "BACKFILENAME";
    public static final String   DBBACK_BACK_FLOWINPUT_BACKSAVEDAY            = "BACKSAVEDAY";
    public static final String   DBBACK_BACK_FLOWINPUT_ISCOMPRESS             = "ISCOMPRESS";
    public static final String   DBBACK_BACK_FLOWINPUT_BACKOWNER              = "BACKOWNER";

    public static final String   DBBACK_BACK_FLOWINPUT_RESTOREDB              = "RESTOREDB";
    public static final String   DBBACK_BACK_FLOWINPUT_RESTOREDBNAME          = "RESTOREDBNAME";
    public static final String   DBBACK_BACK_FLOWINPUT_RESTOREUSER            = "RESTOREUSER";
    public static final String   DBBACK_BACK_FLOWINPUT_RESTOREPASS            = "RESTOREPASS";
    public static final String   DBBACK_BACK_FLOWINPUT_RESTOREFILE            = "RESTOREFILE";
    public static final String   DBBACK_BACK_FLOWINPUT_RESTORETABLES          = "RESTORETABLES";
    public static final String   DBBACK_BACK_FLOWINPUT_RECOVERSCRIPT          = "RECOVERSCRIPT";
    public static final String   DBBACK_BACK_FLOWINPUT_RESTOREFROMUSER        = "RESTOREFROMUSER";
    public static final String   DBBACK_BACK_FLOWINPUT_RESTORETOUSER          = "RESTORETOUSER";
    // public static final String DBBACK_BACK_FLOWINPUT_BACKFILENAME = "BACKFILENAME";

    // 法定时间
    // 时间类型，0为日，1为星期，2为月
    public static final int      LEGALTIME_TYPE_DAY                           = 0;
    public static final int      LEGALTIME_TYPE_WEEK                          = 1;
    public static final int      LEGALTIME_TYPE_MONTH                         = 2;
    // 是否全选,0为是，1为否
    public static final int      LEGALTIME_TYPE_SELECTALL                     = 0;
    public static final int      LEGALTIME_TYPE_UNSELECTALL                   = 1;
    // 选择类型 0为正选，1为反选
    public static final int      LEGALTIME_TYPE_SELECTTYPE                    = 0;
    public static final int      LEGALTIME_TYPE_UNSELECTTYPE                  = 1;

    // 浦发文件下发配置信息
    public static final String   DOUBLE_CHECK_WHOLE_PATH                      = "double.check.whole.path";
    public static final String   DOUBLE_CHECK_SENDMAIL_TIME                   = "double.check.sendmail.time";
    public static final String   FILE_ISSUANCE_FIRST_PATH                     = "file.issuance.first.path";

    // 锦州代码迁移
    public static final String   CHECKAGENTTHREAD_SLEEP_TIME                  = "checkagentthread.sleeptime";
    public static final int      CHECKAGENTTHREAD_SLEEP_TIME_DEFAULT          = 30;

    // 脚本活动运行时的id
    public static final String   SCRIPT_ACT_RUNTIME_IID                       = "script_act_runtime_iid";
    // 脚本活动工作流的id
    public static final String   SCRIPT_ACT_RUNTIME_FLOWID                    = "script_act_runtime_flowid";
    // 脚本活动运行时的脚本名
    public static final String   SCRIPT_ACT_RUNTIME_ACTNAME                   = "script_act_runtime_actname";
    // 脚本活动运行时的执行ID
    public static final String   SCRIPT_ACT_RUNTIME_IEXECACTID                = "script_act_runtime_iexecactid";
    public static final String   SCRIPT_ACT_RUNTIME_LOGINNAME                = "script_act_runtime_loginname";
    public static final String   SEQUENTIAL_CONNECTOR_GLOBAL                  = "->";
    public static final Integer  COLLECT_STATE_STOP                           = 1;
    public static final Integer  COLLECT_STATE_WAITING_START                  = 10;
    public static final Integer  COLLECT_STATE_STARTING                       = 11;
    public static final Integer  COLLECT_STATE_STARTED                        = 12;
    public static final Integer  COLLECT_STATE_WAITING_STOP                   = 20;
    public static final Integer  COLLECT_STATE_STOPPING                       = 21;

    // 脚本服务化-发布提交 发送邮件 主题常量
    public static final String   SEND_EMAIL_SCRIPT_SUBJECTLEFT_APPLAY         = "【审核申请】";
    public static final String   SEND_EMAIL_SCRIPT_SUBJECTLEFT_AGREE          = "【审核通过】";
    public static final String   SEND_EMAIL_SCRIPT_SUBJECTLEFT_BACK           = "【审核打回】";
    public static final String   SEND_EMAIL_SCRIPT_SUBJECTRIGTH_SS            = "自定义运维-脚本发布";
    public static final String   SEND_EMAIL_SCRIPT_SUBJECTRIGTH_TASK          = "自定义运维-任务申请";
    public static final String   SEND_EMAIL_SCRIPT_SUBJECTRIGTH_WS            = "自定义运维-白名单脚本发布";
    // 脚本服务化-发布提交 发送邮件 正文常量
    public static final String   SEND_EMAIL_SCRIPT_DESCTITLE_LEFT             = "&emsp;您好！ <br>&emsp;现有一份《";
    public static final String   SEND_EMAIL_SCRIPT_DESCTITLE_SS               = "自定义运维-脚本发布申请";
    public static final String   SEND_EMAIL_SCRIPT_DESCTITLE_WS               = "自定义运维-<font color=\"red\">白名单</font>脚本发布";
    public static final String   SEND_EMAIL_SCRIPT_DESCTITLE_TASK             = "自定义运维-任务申请";
    public static final String   SEND_EMAIL_SCRIPT_DESCTITLE_RIGHT_TOAPPROVAL = "》待您审批，";
    public static final String   SEND_EMAIL_SCRIPT_DESCTITLE_RIGHT_AGREE      = "》已审批通过，";
    public static final String   SEND_EMAIL_SCRIPT_DESCTITLE_RIGHT_BACK       = "》已打回，";
    public static final String   SEND_EMAIL_SCRIPT_DESCTITLE_APPLAYPSERSON    = "申请人：";
    public static final String   SEND_EMAIL_SCRIPT_DESCTITLE_APPROVALPSERSON  = "审批人：";
    public static final String   SEND_EMAIL_SCRIPT_MENU_URL                   = "。您可以点击下方链接查看：<br>&emsp;&ensp;&nbsp;请复制以下链接至Chrome或Firefox中打开：http://aoms.spdb.com/aoms &nbsp;";
    public static final String   SEND_EMAIL_SCRIPT_MENUNAME_MESSAGE           = "（小信封->自定义运维）<br>";
    public static final String   SEND_EMAIL_SCRIPT_MENUNAME_MYSCRIPT          = "（自定义运维->脚本管理->我的脚本库）<br>";
    public static final String   SEND_EMAIL_SCRIPT_MENUNAME_TASKEXEC          = "（执行任务请至ECC，查看请点击“复核任务查询”菜单）<br>";
    public static final String   SEND_EMAIL_SCRIPT_MENUNAME_REPUBLISH         = "（重新发布请点击“复核任务查询”菜单）<br>";
    public static final String   SEND_EMAIL_SCRIPT_MENUNAME_WHITESEXEC        = "（自定义运维->白名单脚本->脚本执行）<br>";
    public static final String   SEND_EMAIL_SCRIPT_THANKSWORD                 = "&emsp;&ensp;&nbsp;注：此为系统自动发送的消息，请勿直接回复。谢谢！<br>";

    public static final String   SEND_EMAIL_SCRIPT_DATETITLE_APPLAY           = "申请日期";
    public static final String   SEND_EMAIL_SCRIPT_DATETITLE_APPROVAL         = "审核日期";
    public static final String   SEND_EMAIL_SCRIPT_DATETITLE_BACK             = "打回日期";

    public static final String   SEND_EMAIL_SCRIPT_TITLECOLOR_BLUE            = "CornflowerBlue";
    public static final String   SEND_EMAIL_SCRIPT_TITLECOLOR_GOLD            = "Goldenrod";
    public static final String   SEND_EMAIL_SCRIPT_TITLECOLOR_CORAL           = "Coral";
    public static final String   SCRIPT_SERVICE_INSTANCE_OBJ_NAME             = "scriptInstance";
    public static final String   SCRIPT_SERVICE_DOWNLOAD_ADAPTOR_OBJ_NAME     = "scriptAdaptor";

    // 多写数据源命名公共变量
    public static final String   MESSAGEONE                                   = "没有基线源, 无法保存！";
    public static final String   BASECONN                                     = "baseConn";
    public static final String   DBCONNS                                      = "dbConns";
    public static final String   EXESQLS                                      = "exeSqls";
    public static final String   ROLLBACKSQLS                                 = "rollbackSqls";

    // 打印异常的通用参数
    public static final String   EXCEPTION_METHOD_NAME                        = "method:";
    public static final String   EXCEPTION_MESSAGE                            = "exception:";

    //漏扫修复 调用标准运维 接口 跳过用户权限常量
    public static final String   NESSUS_REPAIR_STANDARD_OPERATION             = "Nessus_Repair_standard_Operation_inside_constant:";
    //故障自愈任务状态生成、运行、异常、完成状态
    public static final int      FSH_TASK_STATE_CREATE                        = 1;//生成
    public static final int      FSH_TASK_STATE_RUN                           = 0;//运行
    public static final int      FSH_TASK_STATE_CANCEL                        = 3;//取消
    public static final int      FSH_TASK_STATE_FINISH                        = 2;//完成
    public static final int      FSH_TASK_STATE_KILL                          = 4;//终止
    public static final int      FSH_TASK_STATE_TIMEOUT                       = 5;//超时
    public static final int      FSH_TASK_STATE_NOTPASS                       = 6;//审批不通过
    public static final int      FSH_TASK_STATE_FAIL                          = 46;//失败
    public static final int      FSH_TASK_STATE_EXCEPTION                          = 47;//异常

    public static final String   FSH_TASK_START_SUCCESS                       = "启动成功";
    public static final String   FSH_TASK_START_FAIL                          = "启动失败";

    //辽宁农信LESB操作接口类型
    public static final int      LESB_OPT_QUERY                        = 0;//查询
    public static final int      LESB_OPT_START                        = 1;//启动
    public static final int      LESB_OPT_RSTART                       = 2;//重试
    public static final int      LESB_OPT_INTERRUPT                    = 3;//中断
    public static final int      LESB_OPT_ACTQUERY                     = 4;//活动发出查询

    public static final String   IP_SPLIT                                     = " ";
    public static final String   IP_SPLIT_ANOTHER                             = "\n";


    public static final String S_STARTFLOW                                       = "startFlow";
    public static final String S_KILLFLOW                                        = "killFlow";
    public static final String S_PAUSE_FLOW                                      = "pauseFlow";
    public static final String S_CANCEL_PAUSE_FLOW                               = "cancelPauseFlow";

    public static final int      IEAI_SUS_FP_RECORD_STATE_START               = 1;
    public static final int      IEAI_SUS_FP_RECORD_STATE_SENDSUCC            = 2;                                                                                                                                    // 2发送成功
    public static final int      IEAI_SUS_FP_RECORD_STATE_FISHESH             = 3;                                                                                                                                    // 3完成
    public static final int      IEAI_SUS_FP_RECORD_STATE_EXCEPTION           = 4;                                                                                                                                    // 4异常
    public static final int      IEAI_SUS_FP_RECORD_STATE_TERMINATION         = 5;                                                                                                                                   // 5终止
    public static final int      IEAI_SUS_FP_RECORD_STATE_READY               = 6;
    public static final String   COMMON_PARAM_BASE_SOURCE_PATH                = "BASE_SOURCE_PATH";                                                                                                 // 上传路径的去掉包名称后的剩余部分
    public static final String   COMMON_PARAM_ENVID                           = "ENVID";                                                                                                                              // 环境ID
    public static final String   COMMON_PARAM_LOGIN_NAME                      = "LOGIN_NAME";                                                                                                             // 登录名
    public static final String   IACT_MODULENAME                              = "服务器类型";
    public static final String   COMMON_PARAM_CMDB_INSTID                     = "CMDB_INSTID";                                                                                       // 启动用户
    public static final String   IEAI_SUS_STEP_CLOSE                          = "告警压制_开启";
    public static final String   IEAI_SUS_STEP_OPEN                           = "告警压制_解除";

    public static final String   ACT_EXECACTID                = "execactId";
    public static final String   ACT_EXECFLOWID                = "execflowId";
    public static final int      TEMPLATE_INS_STATE_RUNNING        = 4;
    public static final int      TEMPLATE_INS_STATE_FINISHED       = 5;
    public static final int      TEMPLATE_INS_STATE_KILLED         = 6;
    public static final int      TEMPLATE_INS_STATE_ERROR          = 7;

    // agent探测类型
    public static final String   AGENT_MAINTAIN_MONITOR                       = "monitor";
    public static final String   AGENT_MAINTAIN_SMDB                          = "smdb";

    //act monitor监控的活动运行的状态
    public static final String   ACT_MONITOR_STATE_NULL                = "未运行";                                                                                       // 启动用户
    public static final String   ACT_MONITOR_STATE_FINISHED            = "完成";
    public static final String   ACT_MONITOR_STATE_RUNNING             = "运行中";
    public static final int TEMPLATE_CALL_TYPE = 1;
    public static final int ORDER_TYPE_TEMPLATE = 0;
    public static final long IEAI_OTHER = 8;
    public static final String ACT_TYPE_CALL_TEMPLATE = "CallTemplate";


    public static final String AGENT_PULL_FUNC_REQID_PREFIX    ="$$func$$_";
    public static final String AGENT_PULL_FUNC_REQID_SPLIT    ="@@";
    public static final String SERVER_PUSH_FUNCS_KEY    ="functionData";



    /**
     * 邮储应用变更变更类型,全部，应用变更与数据变更三种
     */
    public static final String BGTYPE_ALL = "全部";
    /**
     * 邮储应用变更变更类型,全部，应用变更与数据变更三种
     */
    public static final String BGTYPE_YYBG = "应用变更";
    /**
     * 邮储应用变更变更类型,全部，应用变更与数据变更三种
     */
    public static final String BGTYPE_SJBG = "数据变更";
    //脚本服务化，provider async异步调用线程池
    public static final int PROVIDER_DEFAULT_THREAD_POOL_CORE_NUMBER = 50;
    public static final int PROVIDER_DEFAULT_THREAD_POOL_MAX_THREAD_NUMBER = 200;
    public static final int PROVIDER_DEFAULT_THREAD_POOL_QUEUE_NUMBER = 5000;

    //脚本服务化启动任务线程池配置 start
    public static final int PROVIDER_TASK_THREADPOOL_CORE_NUM = 50;
    public static final int PROVIDER_TASK_THREADPOOL_MAX_NUM = 800;
    public static final int PROVIDER_TASK_THREADPOOL_QUEUE_NUM = 200000;
    public static final int PROVIDER_TASK_THREADPOOL_KEEPLIVE_MINUTES = 15;
    //脚本服务化启动任务线程池配置 end

    public static  enum  WebStudioProjectType{
        SUS_GROUP(300, "变更组合编排","300"),
        AZ_GROUP(400, "AZ切换组合编排","400");

        public int getDbtype ()
        {
            return dbtype;
        }

        public void setDbtype ( int dbtype )
        {
            this.dbtype = dbtype;
        }

        public String getDescript ()
        {
            return descript;
        }

        public void setDescript ( String descript )
        {
            this.descript = descript;
        }

        public String getProtype ()
        {
            return protype;
        }

        public void setProtype ( String protype )
        {
            this.protype = protype;
        }

        private int dbtype;
        private String descript;
        private String protype;

        WebStudioProjectType(int db_type, String pro_descript,String pro_type) {
            this.dbtype = db_type;
            this.descript = pro_descript;
            this.protype = pro_type;
        }
        public WebStudioProjectType getById(Integer id) {
            for (WebStudioProjectType statusEnum : values()) {
                if (statusEnum.getDbtype() == id) {
                    return statusEnum;
                }
            }
            return null;
        }
    }

    public static final String SCRIPT_ENVIRONMENT_VAL = "environmentVal";
    public static final String SCRIPT_VAL_CONN = "@_@";
    // 脚本服务化spring的两个缓存key
    public static final String SCRIPT_SERVICE_VARIABLE_CACHE = "scriptServiceVariableCache";
    public static final String SCRIPT_SERVICE_OTHER_CACHE = "scriptServiceOtherCache";
    //provider选master的path
    public static final String PROVIDER_LEADER_NODE_PATH = "/ieai_provider_leader";
    public final static String UPDATE_DB ="updatedb";
    public final static String DELETE_DB ="deldb";
    public final static String SAVE_DB ="savedb";

    // 福建农信晋级步骤状态码
    public static final int FLOWTASK_STATE_SUS_CREATE = 1;                          // 创建晋级任务
    public static final int FLOWTASK_STATE_SUS_CREATE_DATA_FINISH = 2;              // 组织数据完成
    public static final int FLOWTASK_STATE_SUS_CREATE_DATA_ERROR = 3;               // 组织数据失败
    public static final int FLOWTASK_STATE_SUS_SENDDATA_ERROR = 4;                  // 发送数据失败
    public static final int FLOWTASK_STATE_SUS_SENDING = 5;                         // 发送数据中
    public static final int FLOWTASK_STATE_SUS_SENDING_FINISH = 6;                  // 对方接收成功
    public static final int FLOWTASK_STATE_SUS_SENDING_ERROR = 7;                   // 对方接收失败
    public static final int FLOWTASK_STATE_SUS_SENDING_BACK = 8;                    // 打回数据
    public static final int FLOWTASK_STATE_SUS_LOCAL_BREAK = 9;                     // 本地终止
    public static final int FLOWTASK_STATE_SUS_OK = 10;                             // 晋级完成
    public static final int FLOWTASK_STATE_SUS_ERROR = 11;                          // 晋级失败
    public static final int FLOWTASK_STATE_SUS_OUT_BREAK = 12;                      // 外部终止
    public static final int FLOWTASK_STATE_SUS_RECEVING = 20;                       // 接收中
    public static final int FLOWTASK_STATE_SUS_RECEVING_OK = 21;                    // 外部任务接收成功
    public static final int FLOWTASK_STATE_SUS_RECEVING_ERROR = 22;                 // 外部任务接收失败
    public static final int FLOWTASK_STATE_SUS_AUDIT_BEFORE_ERROR = 23;             // 审核前校验失败
    public static final int FLOWTASK_STATE_SUS_AUDIT_WATING = 24;                   // 科管待审核
    public static final int FLOWTASK_STATE_SUS_AUDIT_OK = 25;                       // 科管审核通过
    public static final int FLOWTASK_STATE_SUS_DATA_WATING = 26;                    // 数据待入库
    public static final int FLOWTASK_STATE_SUS_AUDIT_DATA = 27;                     // 复核中
    public static final int FLOWTASK_STATE_SUS_AUDIT_DATA_OK = 28;                  // 双人复核成功
    public static final int FLOWTASK_STATE_SUS_FINISH = 29;                         // 晋级成功
    public static final int FLOWTASK_STATE_SUS_OVER = 31;                           // 终止
    //fjnx 晋级表的状态值
    public static final  int CODE_CREATE = 100;                                 //已创建 待晋级
    public static final  int CODE_SEND = 200;                                   //已发送
    public static final  int CODE_SUCCESS = 400;                               //晋级成功
    public static final  int CODE_FAIL = 500;                                //晋级失败
    public static final  int CODE_RECIVE_SUC = 600;                         // 接收成功
    public static final  int CODE_RECIVE_FAIL = 700;                       //接收失败
    public static final  int CODE_AUDITED_OK = 800;                       //审核通过 或 已审核
    public static final  int CODE_AUDITED_NO = 900;                     //审核打回
    public static final  int CODE_NOAUDITED = 1000;                    //未审核
    public static final  int CODE_NOAUDITED_REVOKE = 1001;            //未审核撤回
    public static final  int CODE_AUDITED_REVOKE = 1002;             //已审核撤回
    public static final  int CODE_CANCEL = 1003;                    //取消
    public static final  int CODE_REVOKE = 1004;                    //撤回
    public static final  int CODE_TRANSFER = 1005;                    //转办
    // 晋级表
    public static final String IEAI_CICD_FLOWTASK = "IEAI_CICD_FLOWTASK";
    public static final String IEAI_INSTANCE_VERSION = "IEAI_INSTANCE_VERSION";
    public static final String IEAI_INSTANCE_VERSION_CONTENT = "IEAI_INSTANCE_VERSION_CONTENT";
    public static final String IEAI_CICD_TASK_PARAM = "IEAI_CICD_TASK_PARAM";
    public static final String IEAI_INSTANCEINFO = "IEAI_INSTANCEINFO";
    public static final String IEAI_CICD_INSINFO_PARAMS = "IEAI_CICD_INSINFO_PARAMS";
    public static final String IEAI_CICD_INSINFO_ENV = "IEAI_CICD_INSINFO_ENV";
    public static final String IEAI_CICD_INSINFO_RESGROUP = "IEAI_CICD_INSINFO_RESGROUP";
    public static final String IEAI_CICD_INSINFO_CONTENT = "IEAI_CICD_INSINFO_CONTENT";
    // 晋级相关路径配置标识
    //CI侧相关
    public static final  String RISE_TASK_BASEURL  = "rise.task.baseurl";
    public static final  String RISE_HTTP_TO_CD_URL = "rise.http.to.cd.url";
    //    public static final  String RISE_CHANGE_HTTP_URL = "rise.change.http.url";
    // CD侧相关
    public static final  String RISE_HTTP_TO_CI_URL = "rise.http.to.ci.url";
    public static final  String RISE_DECO_ZIP_URL = "rise.deco.zip.url";
    //.do
    public static final  String RISE_IMPORT = "riseImport.do";
    public static final  String RISE_CHANGE = "riseChange.do";
    public static final  String RISE_AUDIT = "approveNotify.do";

    //某个银行标识，具体内容请参考银行编码文档
    public static final String BANK_CODE_010 = "bankCode010";
    //超时标识
    public static final String AUTO_TIME_OUT_KILL = "autoTimeoutKill:";

    //锦州 所属系统操作 ISTAUS  状态
    public static final int      WORKFLOW_SYSTEM_OPER_RUNNING        = 0;
    public static final int      WORKFLOW_SYSTEM_OPER_OVER           = 1;
    public static final int      WORKFLOW_SYSTEM_OPER_ERROR          = 15;
    public static final int      WORKFLOW_SYSTEM_OPER_BEFOREKILL     = 1;
    public static final int      WORKFLOW_SYSTEM_OPER_PAUSE          = 0;
    public static final int      WORKFLOW_SYSTEM_OPER_RESUME         = 1;
    public static final int      WORKFLOW_SYSTEM_OPER_KILL           = 2;
    public static final int      WORKFLOW_SYSTEM_OPER_PAUSEKILL      = 3;
    public static final int      WORKFLOW_SYSTEM_OPER_CLEARODSFLAG   = 4;

    //新版工具箱流程模板信息
    public static final String   TOOLS_MODEL                                  = "ExecActModelTool";
    public static final String   TOOLS_SERVICEFLOW                                  = "sciptServiceMain";
    public static final String   TOOLS_LOCALFLOW                                  = "execShellCmdMain";
    public static final String   TOOLS_SERVICEFLOW_CELL                                  = "sciptServiceFlow";
    public static final String   TOOLS_LOCALFLOW_CELL                                  = "execShellCmd";

    //工具箱双人复核表扩展字段
    public static final String   COL_TOOLSID_TOOLS                           = "itoolsId";
    public static final String   COL_TOOLSNAME_TOOLS                         = "itoolsName";
    public static final String   COL_TOOLSCODE_TOOLS                         = "itoolsCode";
    public static final String   COL_TOOLSTYPEID_TOOLS                       = "itoolTypeId";

    public static final String   COL_TOOLSONETYPEID_TOOLS                    = "ioneTypeId";
    public static final String   COL_TOOLSTWOTYPEID_TOOLS                    = "itwoTypeId";

    public static final String   COL_TOOLSTHREETYPEID_TOOLS                   = "ithreeTypeId";

    public static final String   COL_TOOLSNEVENTID_TOOLS                      = "neventId";

    public static final String   COL_TOOLSTATUSID_TOOLS                      = "ItoolStatusId";

    public final static String[] COL_ITEAM_TOOLS                              ={ "itoolsId", "itoolsName","itoolsCode","itoolTypeId","ioneTypeId","itwoTypeId","ithreeTypeId","neventId","ItoolStatusId"};

    public final static String[] COL_ITEAMUPDATE_TOOLS                              ={ "itoolsName","ioneTypeId","itwoTypeId","ithreeTypeId","neventId","ItoolStatusId"};

    public static String TranckCurrentSysDate ( String format )
    {
        String params = getCurrentSysDate() + ",'" + format + "'";
        switch (JudgeDB.IEAI_DB_TYPE)
        {
            case 1:
                return "TO_CHAR(" + params + ")";

            case 4:
                return "TO_CHAR(" + params + ")";
            case 2:
                return "TO_CHAR(" + params + ")";
            case 3:
                return "DATE_FORMAT(" + params + ")";
            default:
                return "TO_CHAR(" + params + ")";
        }
    }
}
