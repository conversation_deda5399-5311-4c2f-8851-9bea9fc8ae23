package com.ideal.ieai.server.repository.czbpoc;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.util.WarningInterfaceUtilsIEAI;
import org.apache.log4j.Logger;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 日切时间报警检查器
 * 负责检查系统是否超过日切时间且核心修改时间超过24小时未更新
 */
public class CutTimeAlarmChecker {

    private static final Logger LOGGER = Logger.getLogger(CutTimeAlarmChecker.class);
    private static final String DATA_DATE_FLOP_TABLE = "IEAI_DATA_DATE_FLOP";
    private static final long ALARM_THRESHOLD_HOURS = 24; // 报警阈值：24小时

    /**
     * 检查指定日切时间的系统报警条件
     * @param targetCutTime 要检查的日切时间，格式为HH:mm
     */
    public void checkCutTimeAlarms(String targetCutTime) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        try {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);

            // 只查询指定日切时间的系统
            String sql = "SELECT iid, isystem_name, icut_time, iupdate_time, icall_time FROM " + DATA_DATE_FLOP_TABLE +
                        " WHERE icut_time = ?";
            stmt = conn.prepareStatement(sql);
            stmt.setString(1, targetCutTime);
            rs = stmt.executeQuery();

            List<AlarmInfo> alarmList = new ArrayList<>();
            long currentTime = System.currentTimeMillis();

            while (rs.next()) {
                long iid = rs.getLong("iid");
                String systemName = rs.getString("isystem_name");
                String cutTime = rs.getString("icut_time");
                long callTime = rs.getLong("icall_time");

                // 如果icall_time为null或0，跳过此条记录的告警检查
                if (callTime == 0) {
                    LOGGER.debug("系统 " + systemName + " 的icall_time为空，跳过告警检查");
                    continue;
                }

                // 检查是否需要报警（由于是在日切时间点执行，只需检查调用时间是否超时）
                if (shouldTriggerAlarm(callTime, currentTime)) {
                    AlarmInfo alarmInfo = new AlarmInfo();
                    alarmInfo.setIid(iid);
                    alarmInfo.setSystemName(systemName);
                    alarmInfo.setCutTime(cutTime);
                    alarmInfo.setCallTime(callTime);  // 使用callTime
                    alarmInfo.setCurrentTime(currentTime);
                    alarmList.add(alarmInfo);

                    LOGGER.warn("系统 " + systemName + " 触发日切时间报警：日切时间=" + targetCutTime +
                               ", 调用时间=" + new Date(callTime) +
                               ", 当前时间=" + new Date(currentTime));
                }
            }

            // 如果有需要报警的系统，触发报警
            if (!alarmList.isEmpty()) {
                triggerAlarms(alarmList);
            }

        } catch (Exception e) {
            LOGGER.error("检查日切时间报警失败: " + e.getMessage(), e);
        } finally {
            DBResource.closeConn(conn, rs, stmt, "checkCutTimeAlarms", LOGGER);
        }
    }

    /**
     * 判断是否应该触发报警
     * 由于是在日切时间点执行检查，只需要判断调用时间是否超过24小时未更新
     * @param callTime 调用时间戳
     * @param currentTime 当前时间戳
     * @return true表示需要报警，false表示不需要
     */
    private boolean shouldTriggerAlarm(long callTime, long currentTime) {
        try {
            // 检查调用时间是否超过24小时未更新
            long timeDiff = currentTime - callTime;
            long hoursDiff = timeDiff / (1000 * 60 * 60);
            boolean callTimeExpired = hoursDiff >= ALARM_THRESHOLD_HOURS;

            return callTimeExpired;

        } catch (Exception e) {
            LOGGER.error("判断报警条件失败: " + e.getMessage(), e);
            return false;
        }
    }

    /**
     * 触发报警
     * @param alarmList 需要报警的系统列表
     */
    private void triggerAlarms(List<AlarmInfo> alarmList) {
        for (AlarmInfo alarmInfo : alarmList) {
            try {
                // 记录报警日志
                logAlarm(alarmInfo);

                sendAlarmNotification(alarmInfo);
                
            } catch (Exception e) {
                LOGGER.error("触发报警失败，系统: " + alarmInfo.getSystemName() + ", 错误: " + e.getMessage(), e);
            }
        }
    }

    /**
     * 记录报警日志
     * @param alarmInfo 报警信息
     */
    private void logAlarm(AlarmInfo alarmInfo) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String logMessage = String.format(
            "【日切时间报警】系统: %s, 日切时间: %s, 调用时间: %s, 当前时间: %s, 超时: %.1f小时",
            alarmInfo.getSystemName(),
            alarmInfo.getCutTime(),
            sdf.format(new Date(alarmInfo.getCallTime())),
            sdf.format(new Date(alarmInfo.getCurrentTime())),
            (alarmInfo.getCurrentTime() - alarmInfo.getCallTime()) / (1000.0 * 60 * 60)
        );

        LOGGER.warn(logMessage);

        // 可以在这里添加数据库日志记录
        // insertAlarmLog(alarmInfo);
    }

    /**
     * 发送报警通知（预留接口）
     * @param alarmInfo 报警信息
     */
    private void sendAlarmNotification(AlarmInfo alarmInfo) {
        LOGGER.info("报警接口：系统 " + alarmInfo.getSystemName() + " 需要发送报警通知");
        WarningInterfaceUtilsIEAI.callWarning("IEAI", "IEAI_BUSINESSEXCEPTION", "five", "", "到时未翻牌，翻牌时间："+alarmInfo.getCutTime(), new Date(),alarmInfo.getSystemName(),"","",0L,null,"");
    }

    /**
     * 报警信息内部类
     */
    private static class AlarmInfo {
        private long iid;
        private String systemName;
        private String cutTime;
        private long callTime;
        private long currentTime;

        // Getters and Setters
        public long getIid() { return iid; }
        public void setIid(long iid) { this.iid = iid; }

        public String getSystemName() { return systemName; }
        public void setSystemName(String systemName) { this.systemName = systemName; }

        public String getCutTime() { return cutTime; }
        public void setCutTime(String cutTime) { this.cutTime = cutTime; }

        public long getCallTime() { return callTime; }
        public void setCallTime(long callTime) { this.callTime = callTime; }

        public long getCurrentTime() { return currentTime; }
        public void setCurrentTime(long currentTime) { this.currentTime = currentTime; }
    }
}
