package com.ideal.ieai.server.repository.czbpoc;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.idgenerator.IdGenerator;
import org.apache.log4j.Logger;
import org.quartz.*;
import org.quartz.impl.StdSchedulerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Calendar;

/**
 * 数据日期翻牌管理类
 * 负责管理数据日期的翻牌操作和定时任务
 */
public class DataDateFlopManager {

    private static final Logger LOGGER = Logger.getLogger(DataDateFlopManager.class);
    private static final String DATA_DATE_FLOP_TABLE = "IEAI_DATA_DATE_FLOP";
    private static final String JOB_NAME = "DateFlop";
    private static final String JOB_GROUP = "DateFlop-Group";

    private static Scheduler scheduler;

    static {
        try {
            scheduler = new StdSchedulerFactory().getScheduler();
        } catch (SchedulerException e) {
            LOGGER.error("创建翻牌日期job失败：" + e.getMessage(), e);
        }
    }

    /**
     * 更新数据日期翻牌信息
     *
     * @param dataDate 数据日期
     * @param date 更新时间
     * @param flopTime 翻牌时间
     * @param effective 是否生效
     * @return 更新后的数据列表
     */
    public List<Map<String, Object>> updateDataDateFlop(int dataDate, Long date, String flopTime, int effective) throws RepositoryException {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        List<Map<String, Object>> resultList = new ArrayList<>();

        for (int i = 0; ; i++) {
            Connection conn = null;
            PreparedStatement updateStmt = null;
            PreparedStatement queryStmt = null;
            ResultSet rs = null;

            try {
                try {
                    conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);
                } catch (DBException e) {
                    LOGGER.error(method + " - 获取数据库连接失败: " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_INIT);
                }

                // 更新数据
                String updateSql = "UPDATE " + DATA_DATE_FLOP_TABLE + " SET IFLOP_DATE = ?, IUPDATE_TIME = ?, IFLOP_TIME = ?, IEFFECTIVE = ?";
                updateStmt = conn.prepareStatement(updateSql);
                updateStmt.setInt(1, dataDate);
                updateStmt.setLong(2, date);
                updateStmt.setString(3, flopTime);
                updateStmt.setInt(4, effective);
                updateStmt.executeUpdate();
                conn.commit();

                // 查询更新后的数据
                String querySql = "SELECT * FROM " + DATA_DATE_FLOP_TABLE;
                queryStmt = conn.prepareStatement(querySql);
                rs = queryStmt.executeQuery();

                while (rs.next()) {
                    Map<String, Object> dataMap = new HashMap<>();
                    dataMap.put("dataDate", rs.getInt("IFLOP_DATE"));
                    dataMap.put("updateTime", rs.getLong("IUPDATE_TIME"));

                    String time = rs.getString("IFLOP_TIME");
                    String[] timeParts = time.split(":");
                    dataMap.put("hour", timeParts[0]);
                    dataMap.put("minute", timeParts[1]);
                    dataMap.put("effective", rs.getInt("IEFFECTIVE"));

                    resultList.add(dataMap);
                }

                // 根据生效状态创建或删除定时任务
                if (effective == 1) {
                    createDateFlopJob();
                } else {
                    deleteMQClearJob();
                }

                break; // 操作成功，跳出循环

            } catch (SQLException e) {
                LOGGER.error(method + " - 执行SQL失败: " + e.getMessage());
                throw new RepositoryException(ServerError.ERR_DB_QUERY);
            } catch (RepositoryException e) {
                try {
                    DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
                } catch (RepositoryException e1) {
                    LOGGER.error(method + " - 数据库操作重试失败: " + e1.getMessage(), e1);
                    break;
                }
            } finally {
                DBResource.closeConn(conn, rs, queryStmt, method, LOGGER);
                if (updateStmt != null) {
                    try {
                        updateStmt.close();
                    } catch (SQLException e) {
                        LOGGER.error(method + " - 关闭PreparedStatement失败: " + e.getMessage());
                    }
                }
            }
        }
        return resultList;
    }


    /**
     * 获取数据日期翻牌信息
     *
     * @return 数据日期翻牌信息列表
     */
    public List<Map<String, Object>> getDataDateFlop() {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        List<Map<String, Object>> resultList = new ArrayList<>();

        for (int i = 0; ; i++) {
            Connection conn = null;
            PreparedStatement stmt = null;
            ResultSet rs = null;

            try {
                try {
                    conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);
                } catch (DBException e) {
                    LOGGER.error(method + " - 获取数据库连接失败: " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_INIT);
                }

                String sql = "SELECT * FROM " + DATA_DATE_FLOP_TABLE;
                stmt = conn.prepareStatement(sql);
                rs = stmt.executeQuery();

                while (rs.next()) {
                    Map<String, Object> dataMap = new HashMap<>();
                    dataMap.put("dataDate", rs.getInt("IFLOP_DATE"));
                    dataMap.put("updateTime", rs.getLong("IUPDATE_TIME"));

                    String time = rs.getString("IFLOP_TIME");
                    String[] timeParts = time.split(":");
                    dataMap.put("hour", timeParts[0]);
                    dataMap.put("minute", timeParts[1]);
                    dataMap.put("effective", rs.getInt("IEFFECTIVE"));

                    resultList.add(dataMap);
                }

                break; // 查询成功，跳出循环

            } catch (SQLException e) {
                LOGGER.error(method + " - 执行SQL查询失败: " + e.getMessage());
            } catch (RepositoryException e) {
                try {
                    DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
                } catch (RepositoryException e1) {
                    LOGGER.error(method + " - 获取数据日期翻牌信息失败: " + e1.getMessage(), e1);
                    break;
                }
            } finally {
                DBResource.closeConn(conn, rs, stmt, method, LOGGER);
            }
        }

        return resultList;
    }


    /**
     * 创建数据日期翻牌定时任务
     *
     * @return 创建结果，true表示成功，false表示失败
     */
    public boolean createDateFlopJob() {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        boolean success = true;

        try {
            // 获取翻牌时间设置
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);
            String sql = "SELECT IFLOP_TIME, IEFFECTIVE FROM " + DATA_DATE_FLOP_TABLE;
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery();

            String flopTime = "";
            int effective = 0;

            if (rs.next()) {
                flopTime = rs.getString("IFLOP_TIME");
                effective = rs.getInt("IEFFECTIVE");
            }

            // 如果翻牌未启用，直接返回成功
            if (effective == 0) {
                return true;
            }

            // 解析翻牌时间
            String[] timeParts = flopTime.split(":");
            int hour = Integer.parseInt(timeParts[0]);
            int minute = Integer.parseInt(timeParts[1]);

            // 计算下次执行时间
            Date now = new Date();
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.HOUR_OF_DAY, hour);
            calendar.set(Calendar.MINUTE, minute);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);

            // 如果当前时间已经超过了今天的执行时间，则设置为明天执行
            if (calendar.getTime().before(now)) {
                calendar.add(Calendar.DATE, 1);
            }

            Date nextExecutionTime = calendar.getTime();

            // 创建定时任务
            scheduler.start();

            // 先删除已存在的任务
            try {
                scheduler.deleteJob(new JobKey(JOB_NAME, JOB_GROUP));
            } catch (SchedulerException e) {
                // 忽略不存在的任务异常
                LOGGER.debug("删除不存在的任务: " + e.getMessage());
            }

            JobDetail jobDetail = JobBuilder.newJob(ExecuteDateFlopJob.class)
                    .withIdentity(JOB_NAME, JOB_GROUP)
                    .build();

            Trigger trigger = TriggerBuilder.newTrigger()
                    .withIdentity(JOB_NAME, JOB_GROUP)
                    .startAt(nextExecutionTime)
                    .withSchedule(SimpleScheduleBuilder.simpleSchedule()
                            .withIntervalInHours(24)
                            .repeatForever())
                    .build();

            scheduler.scheduleJob(jobDetail, trigger);
            LOGGER.info("创建翻牌日期任务成功，下次执行时间: " + nextExecutionTime);

        } catch (SchedulerException e) {
            LOGGER.error("创建翻牌日期任务失败: " + e.getMessage(), e);
            success = false;
        } catch (DBException e) {
            LOGGER.error("创建翻牌日期任务失败，数据库连接异常: " + e.getMessage(), e);
            success = false;
        } catch (SQLException e) {
            LOGGER.error("创建翻牌日期任务失败，SQL执行异常: " + e.getMessage(), e);
            success = false;
        } finally {
            DBResource.closeConn(conn, rs, stmt, "createDateFlopJob", LOGGER);
        }

        return success;
    }


    /**
     * 删除数据日期翻牌定时任务
     *
     * @throws RepositoryException 删除任务失败时抛出异常
     */
    public void deleteMQClearJob() throws RepositoryException {
        try {
            // 只删除特定的任务，而不是清空所有任务
            scheduler.deleteJob(new JobKey(JOB_NAME, JOB_GROUP));
            LOGGER.info("删除翻牌日期任务成功");
        } catch (SchedulerException e) {
            LOGGER.error("删除翻牌日期任务失败: " + e.getMessage(), e);
            throw new RepositoryException(ServerError.ERR_DB_INIT);
        }
    }

    public List getDataDateFlopList ( Connection conn, String sql, Integer start, Integer limit )
            throws RepositoryException
    {
        List list = new ArrayList();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            ps = conn.prepareStatement(sql);
            ps.setInt(1, start);
            ps.setInt(2, start + limit);
            rs = ps.executeQuery();
            while (rs.next())
            {
                DataDateModel model = new DataDateModel();
                model.setIid(rs.getLong("iid"));
                model.setIsystemName(rs.getString("isystem_name"));
                model.setIdataDate(rs.getString("idata_date"));
                long iupdateTime = rs.getLong("iupdate_time");
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String formattedDate = sdf.format(new Date(iupdateTime));
                model.setIupdateTime(formattedDate);
                model.setIbusinessDate(rs.getString("ibusiness_date"));
                model.setIoffset(rs.getString("ioffset"));
                model.setIcutTime(rs.getString("icut_time"));
                long icallTime = rs.getLong("icall_time");
                if(icallTime == 0){
                    model.setIcallTime("");
                }else{
                    String icallTimeStr = sdf.format(new Date(icallTime));
                    model.setIcallTime(icallTimeStr);
                }
                list.add(model);
            }
        } catch (Exception e)
        {
            LOGGER.error("DataDateFlopManager.getDataDateFlopList is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getDataDateFlopList", LOGGER);
        }
        return list;
    }

    public int getDataDateFlopCount ( Connection conn, String sql ) throws RepositoryException
    {
        int count = 0;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next())
            {
                count = rs.getInt("countNum");
            }
        } catch (Exception e)
        {
            LOGGER.error("DataDateFlopManager.getDataDateFlopCount is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getDataDateFlopCount", LOGGER);
        }
        return count;
    }

    public void addDataDate ( Connection conn, DataDateModel model ) throws RepositoryException
    {
        PreparedStatement ps = null;
        try
        {
            long iid = IdGenerator.createId("IEAI_DATA_DATE_FLOP", conn);
            String sql = "insert into IEAI_DATA_DATE_FLOP( iid, isystem_name, idata_date, iupdate_time,ibusiness_date,ioffset,icut_time) values( ?, ?, ?, ?,?,?,?)";
            ps = conn.prepareStatement(sql);
            ps.setLong(1, iid);
            ps.setString(2, model.getIsystemName());
            ps.setString(3, model.getIdataDate());
            ps.setString(4, model.getIupdateTime());
            ps.setString(5, model.getIbusinessDate());
            ps.setString(6, model.getIoffset());
            ps.setString(7, model.getIcutTime());
            ps.executeUpdate();
        } catch (Exception e)
        {
            LOGGER.error("DataDateFlopManager.addDataDate is error", e);
            throw new RepositoryException(ServerError.ERR_DB_INSERT);
        } finally
        {
            DBResource.closePreparedStatement( ps, "addDataDate", LOGGER);
        }
    }

    public void updateDataDate ( Connection conn, DataDateModel model ) throws RepositoryException
    {
        PreparedStatement ps = null;
        try
        {
            String sql = "update IEAI_DATA_DATE_FLOP set isystem_name=?, idata_date=?, iupdate_time=?,ibusiness_date=?,ioffset=?,icut_time=?  where iid=?";
            ps = conn.prepareStatement(sql);
            ps.setString(1, model.getIsystemName());
            ps.setString(2, model.getIdataDate());
            ps.setString(3, model.getIupdateTime());
            ps.setString(4, model.getIbusinessDate());
            ps.setString(5, model.getIoffset());
            ps.setString(6, model.getIcutTime());
            ps.setLong(7, model.getIid());
            ps.executeUpdate();
        } catch (Exception e)
        {
            LOGGER.error("DataDateFlopManager.updateDataDate is error", e);
            throw new RepositoryException(ServerError.ERR_DB_UPDATE);
        } finally
        {
            DBResource.closePreparedStatement( ps, "updateDataDate", LOGGER);
        }
    }


    public void deleteDataDate ( Connection conn, Long iid ) throws RepositoryException
    {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            String sql = "delete from IEAI_DATA_DATE_FLOP where iid=?";
            ps = conn.prepareStatement(sql);
            ps.setLong(1, iid);
            ps.executeUpdate();
        } catch (Exception e)
        {
            LOGGER.error("DataDateFlopManager.deleteDataDate is error", e);
            throw new RepositoryException(ServerError.ERR_DB_DELETE);
        } finally
        {
            DBResource.closePSRS(rs, ps, "deleteDataDate", LOGGER);
        }
    }

    public List<Map<String,String>> getIeaiProject(Connection conn, String sqlList) throws RepositoryException {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<Map<String,String>> list = new ArrayList<>();
        try
        {
            ps = conn.prepareStatement(sqlList);
            rs = ps.executeQuery();
            while (rs.next())
            {
                Map<String,String> nameMap = new HashMap<>();
                nameMap.put("name", rs.getString("iname"));
                list.add(nameMap);
            }
        } catch (Exception e)
        {
            LOGGER.error("DataDateFlopManager.getIeaiProject is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getIeaiProject", LOGGER);
        }
        return list;
    }
}
