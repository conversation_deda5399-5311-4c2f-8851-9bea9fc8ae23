package com.ideal.ieai.server.cluster.monitor;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.core.JudgeDB;
import com.ideal.ieai.core.MonitorEnv;
import com.ideal.ieai.server.SystemConfig;
import com.ideal.ieai.server.cluster.monitor.host.HostInfoMonitorBean;
import com.ideal.ieai.server.cluster.monitor.host.HostInfoMonitorSvc;
import com.ideal.ieai.server.cluster.monitor.host.IfcHostInfoMonitorSvc;
import com.ideal.ieai.server.ieaikernel.ServerEnv;
import com.ideal.ieai.server.jobscheduling.util.DateUtil;
import com.ideal.ieai.server.platform.warnmanage.IeaiWarnModel;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.engine.LoadBalanceRepository;
import com.ideal.ieai.server.repository.idgenerator.IdGenerator;
import com.ideal.ieai.server.repository.monitor.SendCibSyslog;
import com.ideal.ieai.server.repository.monitor.WarningManager;
import com.ideal.ieai.server.util.WarningInterfaceUtilsPlatform;
import org.apache.log4j.Logger;

import java.net.UnknownHostException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Date;

/**   
 * @ClassName:  MaintainDbSelfState   
 * @Description:自写线程   
 * @author: kaijia_ma 
 * @date:   2017年11月2日 下午1:36:56   
 *     
 * @Copyright: 2017-2027 www.idealinfo.com Inc. All rights reserved. 
 * 
 */
public class MaintainDbSelfState extends MonitorThread
{
    private static final Logger _log              = Logger.getLogger(MaintainDbSelfState.class);
    private static final Logger _explog           = Logger.getLogger("server_exp");
    // 自写更新周期
    private long                databasepollcycle = ServerEnv.getServerEnv().getDatabasepollcycle() * 1000;
    // 出错的重试次数
    private long                repeatnum         = ServerEnv.getServerEnv().getRepeatnum();
    // 默认自写重试时间
    private long                repeatpollcycle   = ServerEnv.getServerEnv().getRepeatpollcycle() * 1000;
    private boolean             runflag           = true;                                                                                                                                                                                                                                                          // 用于对线程进行控制，主要用于结束线程

    private boolean             killFlag = Environment.getInstance().getBooleanConfig(Environment.BH_IS_KILLSERVER, false);
    public void stopme ()
    {
        runflag = false;
    }

    public MaintainDbSelfState()
    {
        super("Check MaintenanceSelfState Thread.");
        // 如果配置文件的轮训周期小于4秒，则默认数值为4秒
        if (databasepollcycle < 4000)
        {
            databasepollcycle = 4000;
        }
        if (repeatpollcycle < 180000)
        {
            repeatpollcycle = 180000;
        }
        if (repeatnum <= 2)
        {
            repeatnum = 2;
        }

    }

    public void run ()
    {
        boolean flagtype = false;
        while (runflag)
        {
            try
            {   // 判断线程轮询开关
                while (SystemConfig.isHeartbeat() && runflag)
                {
                    try
                    {
                        String serverIp = Environment.getInstance().getServerIP();
//                        _log.info("heartBeat start update lastTime："+System.currentTimeMillis());
                        // 方法:维护本身的状态
                        if (getIP(serverIp) == 0)
                        {
                            // 第一次注册到服务器中
                            maintainServerSelfInsert(serverIp,flagtype);
                            // 根据Servertype配置，更新Server 类型
                        } else
                        {
                            // 根据配置文件的轮训周期更新lasttime
                            serverUpdateLastAndState(serverIp,flagtype);
                        }
                        flagtype=true;
                        // 这个周期要根据配置文件中定义的周期,替换
//                        _log.info("heartBeat end update lastTime："+System.currentTimeMillis());
                        try
                        {
                            Thread.sleep(databasepollcycle);
                        } catch (InterruptedException r)
                        {
                            _log.warn(Thread.currentThread().getStackTrace()[1].getMethodName(), r);
                        }
                    } catch (Exception ex)
                    {
                        // 方法:如果线程本身出现了异常,杀掉本机的server
                        _log.error("数据库自身维护程序出现异常,心跳自杀接口开始启动！", ex);
                        killServer();
                    }
//                    if (!flagtype)
//                    {
//                        Engine.getInstance().updateServerType();
//                    }
//                    flagtype = true;
                }
            } catch (Throwable e)
            {
                _log.info("更新自写信息失败！", e);
            }
            // 这个周期要根据配置文件中定义的周期,替换
            try
            {
                Thread.sleep(databasepollcycle);
            } catch (InterruptedException e)
            {
                _log.error("停止心跳开关异常！", e);
            }
            flagtype = false;
        }
    }

    // 维护本身状态方法,同时注册该机器到服务器列表中----insert 方法

    public void maintainServerSelfInsert (String ip,boolean flagtype) throws Exception
    {

        for (int i = 0; i < repeatnum; i++)
        {
            try
            {
                Connection conn = null;
                PreparedStatement ps = null;
                try
                {

                    conn = DBManager.getInstance().getSchedulerConnection(Constants.IEAI_IEAI_BASIC);
                    // 首先维护服务器本身的信息
                    String selfsql = "INSERT INTO IEAI_SERVERLIST(ID,IP,GROUPID,STATE,LASTTIME,DISPOSE) VALUES(?,?,1,0,SYSDATE,1)";
                    if (JudgeDB.IEAI_DB_TYPE == 3)
                        selfsql = "INSERT INTO IEAI_SERVERLIST(ID,IP,GROUPID,STATE,LASTTIME,DISPOSE) VALUES(?,?,1,0,CURRENT_TIMESTAMP,1)";
                    ps = conn.prepareStatement(selfsql);
                    long id = IdGenerator.createIdNoConnection("IEAI_SERVERLIST", Constants.IEAI_IEAI_BASIC);
                    ps.setLong(1, id);
                    ps.setString(2, ip);
                    ps.execute();
                    if (!flagtype) {
                        this.updateServerTypeByServerIdConn(conn,id);
                    }
                    conn.commit();
                    break;

                } catch (Exception e)
                {
                    _log.error("保存到服务器信息失败！", e);
                    DBResource.rollback(conn, ServerError.ERR_DB_INSERT, e,
                        Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                } finally
                {
                    DBResource.closePSConn(conn, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }

            } catch (RepositoryException ex)
            {
                try
                {
                    Thread.sleep(repeatpollcycle / repeatnum);

                } catch (InterruptedException e)
                {
                    _log.warn(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
                }
                if (i == repeatnum - 1)
                {
                    throw new RepositoryException(ServerError.ERR_DB_INSERT);
                }
            }
        }

    }

    // 维护服务器自身的状态和最后时间----周期小于等于2个轮训周期

    public void serverUpdateLastAndState (String ip,boolean flagtype) throws Exception
    {

        for (int i = 0; i < repeatnum; i++)
        {
            Connection conn = null;
            try
            {
//                _log.info("heart beat serverUpdateLastAndState start sql:"+System.currentTimeMillis());
                PreparedStatement ps = null;
                try
                {
                    // 首先维护服务器本身的信息
                    conn = DBManager.getInstance().getSchedulerConnection(Constants.IEAI_IEAI_BASIC);

                    boolean isCurServerWHQ=LoadBalanceRepository.isWHQ(); //当前server是否设置了维护期
                    if(killFlag)
                    {
                        _explog.info("===========BHBANK更新serverlist开始.....");
                    }
                    
                    if (false==isCurServerWHQ) 
                    {
                        //未打开维护期控制开关
                        //String selfsql = "UPDATE IEAI_SERVERLIST T SET T.LASTTIME=CURRENT_TIMESTAMP , T.STATE=0 WHERE T.IP= ?";
                        
                        String selfsql = "update ieai_serverlist t set t.lasttime=CURRENT_TIMESTAMP , t.state=0 , t.icpu=? , t.imemery=? , t.iexetaskcnt=?,t.idisk=? where t.ip= ? ";
                        
                        // 更新时间lasttime
                        ps = conn.prepareStatement(selfsql);
                        
                        IfcHostInfoMonitorSvc hostMorSvc = new HostInfoMonitorSvc();
                        if(killFlag)
                        {
                            _log.info("killserver开关为true,不进行获得服务器的监控监控信息，然后保存到数据库表中----1");
                            ps.setString(1, "0");
                            ps.setString(2, "0");
                            ps.setString(3, "0");
                            ps.setString(4, "0");
                        }else
                        {
                            HostInfoMonitorBean hifBean = hostMorSvc.getHostInfoBean4TabUpdate();
                            // HostInfoMonitorBean hifBean = new HostInfoMonitorBean();
                            ps.setString(1, hifBean.getIcpu());
                            ps.setString(2, hifBean.getImemery());
                            ps.setString(3, hifBean.getIexetaskcnt());
                            ps.setString(4, hifBean.getIdisk());
                        }
                        
                        ps.setString(5, ip);
                        ps.executeUpdate();
//                        _log.info("heart beat serverUpdateLastAndState end sql1:"+System.currentTimeMillis());
                        if(killFlag)
                        {
                            _explog.info("===========BHBANK更新serverlist结束----！" + i);
                        }
                    }else {
                        String selfsql = "update ieai_serverlist t set t.lasttime=CURRENT_TIMESTAMP , t.state=2 , t.icpu=? , t.imemery=? , t.iexetaskcnt=?,t.idisk=? where t.ip= ? ";
                        
                        // 更新时间lasttime
                        ps = conn.prepareStatement(selfsql);
                        
                        IfcHostInfoMonitorSvc hostMorSvc = new HostInfoMonitorSvc();
                        if(killFlag)
                        {
                            _log.info("killserver开关为true,不进行获得服务器的监控监控信息，然后保存到数据库表中----1");
                            ps.setString(1, "0");
                            ps.setString(2, "0");
                            ps.setString(3, "0");
                            ps.setString(4, "0");
                        }else
                        {
                            HostInfoMonitorBean hifBean = hostMorSvc.getHostInfoBean4TabUpdate();
                            // HostInfoMonitorBean hifBean = new HostInfoMonitorBean();
                            ps.setString(1, hifBean.getIcpu());
                            ps.setString(2, hifBean.getImemery());
                            ps.setString(3, hifBean.getIexetaskcnt());
                            ps.setString(4, hifBean.getIdisk());
                        }
                        
                        ps.setString(5, ip);
                        ps.executeUpdate();
//                        _log.info("heart beat serverUpdateLastAndState end sql2:"+System.currentTimeMillis());
                        if(killFlag)
                        {
                            _explog.info("===========BHBANK更新serverlist结束---！" + i);
                        }
                    }
                    if (!flagtype) {
                        this.updateServerTypeByServerIpConn(conn,ip);
                    }
                    conn.commit();
                    break;
                } catch (Exception e)
                {
                    _log.error("SQLException更新lasttime时间和状态失败！" + i, e);
                    DBResource.rollback(conn, ServerError.ERR_DB_UPDATE, e,
                        Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                } finally
                {
                    DBResource.closePSConn(conn, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }

            } catch (RepositoryException ex)
            {
                try
                {
                    Thread.sleep(repeatpollcycle / repeatnum - 1000);

                } catch (InterruptedException e)
                {
                    _log.warn(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
                }
                if (i == repeatnum - 1)
                {
                    throw new RepositoryException(ServerError.ERR_DB_UPDATE);
                }
            }
        }

    }

    // 判断根据本机的ip服务器列表已经存在
    public int getIP (String serverIp) throws Exception
    {
        int getIpNum = 0;
        String sql = "SELECT COUNT(ID) FROM IEAI_SERVERLIST T  WHERE  T.IP=?";
        for (int i = 0; i < repeatnum; i++)
        {

            try
            {
                Connection conn = null;
                PreparedStatement stmt = null;
                ResultSet rset = null;
                try
                {
                    conn = DBManager.getInstance().getSchedulerConnection(Constants.IEAI_IEAI_BASIC);

                    stmt = conn.prepareStatement(sql);
                    stmt.setString(1, serverIp);

                    rset = stmt.executeQuery();
                    while (rset.next())
                    {
                        getIpNum = rset.getInt(1);
                    }
                    return getIpNum;
                } catch (Exception e)
                {
                    _log.error("查询该机器是否已经注册到服务器列表失败！" + i, e);
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, rset, stmt, Thread.currentThread().getStackTrace()[1].getMethodName(),
                        _log);
                }

            } catch (RepositoryException ex)
            {
                try
                {
                    Thread.sleep(repeatpollcycle / repeatnum);

                } catch (InterruptedException e)
                {
                    _log.warn(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
                }
                if (i == repeatnum - 1)
                {
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                }
            }
        }
        return getIpNum;

    }

    // 定义杀死杀死线程的方法

    public static void killServer ()
    {
        monitorSelf();
        try
        {
            System.exit(0);
        } catch (Throwable ex)
        {
            _log.error("系统调用自杀出现异常！", ex);
            System.exit(1);
        }

    }


    /**
     * <AUTHOR>
     * @des 心跳监控出现异常 2010-11-24
     */
    public static void monitorSelf ()
    {
        try
        {
        if(Environment.getInstance().getCibTivoliSwitch()) {
                SendCibSyslog syslog = new SendCibSyslog();
                String serverip = Environment.getInstance().getServerIP();
                StringBuilder sb = new StringBuilder();
                sb.append("severity=CRITICAL hostname= ").append("ip="+serverip).append(" ").append("msg=\""+"server异常宕机"+"\"");
                _log.info(sb.toString());
                syslog.sendsysLog(sb.toString());
            }
            String serverip = Environment.getInstance().getServerIP();
            IeaiWarnModel iwm = new IeaiWarnModel();
            iwm.setImodulecode("platform");
            iwm.setItypecode("serverexceptiondown");
            iwm.setIlevelcode( "five");
            iwm.setIip(serverip);
            iwm.setIwarnmsg("server异常宕机");
            iwm.setIhappentimeStr(DateUtil.formatDateTime(new Date()));
            WarningInterfaceUtilsPlatform warningInterfaceUtilsPlatform = new WarningInterfaceUtilsPlatform();
            warningInterfaceUtilsPlatform.callWarning(iwm,false);
        } catch (Exception e1)
        {
            _log.error("send Warn error",e1);
        }
        if (MonitorEnv.getBooleanConfig("ENTEGORWARN", false) && MonitorEnv.getBooleanConfig("SYSHEALTH", false))
        {
            WarningManager.getInstance().getMonitorSelf(Environment.MONITORSELF);
        }
    }
    /**
     * 首次启动通过serverid进行维护类型
     * <li>Description:</li> 
     * <AUTHOR>
     * 2024年4月17日 
     * @param con
     * @param serverId
     * @throws RepositoryException
     * @throws UnknownHostException
     * return void
     */
    public void updateServerTypeByServerIdConn (Connection con,long serverId ) throws RepositoryException, UnknownHostException
    {
        String serverType = Environment.getInstance().getServerType();
        PreparedStatement ps = null;
        PreparedStatement ps1 = null;
        try
        {
            String sql = "DELETE FROM IEAI_GROUP_SERVER WHERE ISERVERID =?";
            ps = con.prepareStatement(sql);
            ps.setLong(1, serverId);
            ps.executeUpdate();
            String sql1 = "INSERT INTO IEAI_GROUP_SERVER(IGROUPSERVERID, IGROUPID, ISERVERID) VALUES(?, ?, ?)";
            if (!serverType.equals(""))
            {
                String[] severT = serverType.split(",");
                ps1 = con.prepareStatement(sql1);
                long mainID = IdGenerator.createIdForExecActBig("ieai_group_server", severT.length, con);
                long startId = mainID - severT.length + 1;
                for (int j = 0; j < severT.length; j++)
                {
                    //long iid = IdGenerator.createId("ieai_group_server", con);
                    ps1.setLong(1, startId++);
                    ps1.setLong(2, Long.valueOf(severT[j]));
                    ps1.setLong(3, serverId);
                    ps1.addBatch();
                }
                ps1.executeBatch();
            }
        } catch (SQLException e)
        {
            DBResource.rollback(con, ServerError.ERR_DB_INSERT, e,
                Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        } finally
        {
            DBResource.closePreparedStatement(ps, "updateServerTypeByServerIdConn", _log);
            DBResource.closePreparedStatement(ps1, "updateServerTypeByServerIdConn", _log);
        }
    }
    /**
     * 非首次启动，按照serverip更新类型
     * <li>Description:</li> 
     * <AUTHOR>
     * 2024年4月17日 
     * @param con
     * @param serverIp
     * @throws RepositoryException
     * @throws UnknownHostException
     * return void
     */
    public void updateServerTypeByServerIpConn (Connection con,String serverIp) throws RepositoryException, UnknownHostException
    {
        String serverType = Environment.getInstance().getServerType();
        PreparedStatement ps = null;
        PreparedStatement ps1 = null;
        try
        {
            String sql = "DELETE FROM IEAI_GROUP_SERVER WHERE ISERVERID =(SELECT ID FROM IEAI_SERVERLIST  WHERE IP=?)";
            ps = con.prepareStatement(sql);
            ps.setString(1, serverIp);
            ps.executeUpdate();
            String sql1 = "INSERT INTO IEAI_GROUP_SERVER(IGROUPSERVERID, IGROUPID, ISERVERID) VALUES(?, ?, (SELECT ID FROM IEAI_SERVERLIST T1 WHERE T1.IP=?))";
            if (!serverType.equals(""))
            {
                String[] severT = serverType.split(",");
                ps1 = con.prepareStatement(sql1);
                long mainID = IdGenerator.createIdForExecActBig("ieai_group_server", severT.length, con);
                long startId = mainID - severT.length + 1;
                for (int j = 0; j < severT.length; j++)
                {
                   // long iid = IdGenerator.createId("ieai_group_server", con);
                    ps1.setLong(1, startId++);
                    ps1.setLong(2, Long.valueOf(severT[j]));
                    ps1.setString(3, serverIp);
                    ps1.addBatch();
                }
                ps1.executeBatch();
            }
        } catch (SQLException e)
        {
            DBResource.rollback(con, ServerError.ERR_DB_INSERT, e,
                Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        } finally
        {
            DBResource.closePreparedStatement(ps, "updateServerTypeByServerIpConn", _log);
            DBResource.closePreparedStatement(ps1, "updateServerTypeByServerIpConn", _log);
        }
    }
}
