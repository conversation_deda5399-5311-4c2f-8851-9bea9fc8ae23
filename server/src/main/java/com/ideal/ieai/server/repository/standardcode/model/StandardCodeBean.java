package com.ideal.ieai.server.repository.standardcode.model;

import java.io.Serializable;
import java.util.Date;

/**
 * 贯标管理实体类
 * 用于存储业务系统贯标信息
 * 
 * <AUTHOR>
 * @date 2025-09-23
 */
public class StandardCodeBean implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键ID
     */
    private Long iid;
    
    /**
     * 贯标代码（英文唯一）
     */
    private String istandardcode;

    /**
     * 业务系统中文名（唯一）
     */
    private String businessSystemName;

    /**
     * 创建时间
     */
    private Date icreatetime;
    
    /**
     * 创建用户
     */
    private String icreateuser;
    
    public StandardCodeBean() {
    }
    
    public StandardCodeBean(String istandardcode, String businessSystemName, String icreateuser) {
        this.istandardcode = istandardcode;
        this.businessSystemName = businessSystemName;
        this.icreateuser = icreateuser;
        this.icreatetime = new Date();
    }
    
    public Long getIid() {
        return iid;
    }
    
    public void setIid(Long iid) {
        this.iid = iid;
    }
    
    public String getIstandardcode() {
        return istandardcode;
    }
    
    public void setIstandardcode(String istandardcode) {
        this.istandardcode = istandardcode;
    }

    public String getBusinessSystemName() {
        return businessSystemName;
    }

    public void setBusinessSystemName(String businessSystemName) {
        this.businessSystemName = businessSystemName;
    }

    public Date getIcreatetime() {
        return icreatetime;
    }
    
    public void setIcreatetime(Date icreatetime) {
        this.icreatetime = icreatetime;
    }
    
    public String getIcreateuser() {
        return icreateuser;
    }
    
    public void setIcreateuser(String icreateuser) {
        this.icreateuser = icreateuser;
    }
    
    @Override
    public String toString() {
        return "StandardCodeBean{" +
                "iid=" + iid +
                ", istandardcode='" + istandardcode + '\'' +
                ", businessSystemName='" + businessSystemName + '\'' +
                ", icreatetime=" + icreatetime +
                ", icreateuser='" + icreateuser + '\'' +
                '}';
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        StandardCodeBean that = (StandardCodeBean) o;
        
        if (iid != null ? !iid.equals(that.iid) : that.iid != null) return false;
        return istandardcode != null ? istandardcode.equals(that.istandardcode) : that.istandardcode == null;
    }
    
    @Override
    public int hashCode() {
        int result = iid != null ? iid.hashCode() : 0;
        result = 31 * result + (istandardcode != null ? istandardcode.hashCode() : 0);
        return result;
    }
}
