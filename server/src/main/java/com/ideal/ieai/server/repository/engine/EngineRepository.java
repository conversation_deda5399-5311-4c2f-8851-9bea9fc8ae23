package com.ideal.ieai.server.repository.engine;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.ideal.dubbo.interfaces.IScriptInstance;
import com.ideal.dubbo.models.ScriptFunctionLibrary;
import com.ideal.ieai.adaptors.generaladaptors.datatransform.DataHandlerActKey;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.commons.WorkflowFilter;
import com.ideal.ieai.commons.WorkflowInfo;
import com.ideal.ieai.commons.agent.RemoteActivityExecRequest;
import com.ideal.ieai.commons.recoveryflow.RecoveryPoint;
import com.ideal.ieai.commons.recoveryflow.RecoveryPolicyConfig;
import com.ideal.ieai.commons.recoveryflow.ServerRunningInfo;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.core.JudgeDB;
import com.ideal.ieai.core.activity.ActStateData;
import com.ideal.ieai.core.activity.DefaultConfig;
import com.ideal.ieai.core.data.ArrayObject;
import com.ideal.ieai.core.data.BinaryObject;
import com.ideal.ieai.core.data.IEAIObject;
import com.ideal.ieai.core.data.MapObject;
import com.ideal.ieai.core.element.IEAISchemaDef;
import com.ideal.ieai.core.element.workflow.BasicActElement;
import com.ideal.ieai.core.io.MarshallingException;
import com.ideal.ieai.core.io.UnMarshallingException;
import com.ideal.ieai.core.util.IDataMarshallerHelper;
import com.ideal.ieai.core.util.Jcrypt;
import com.ideal.ieai.server.ServerException;
import com.ideal.ieai.server.SystemConfig;
import com.ideal.ieai.server.engine.Engine;
import com.ideal.ieai.server.engine.core.ExcelActUtil;
import com.ideal.ieai.server.engine.execactivity.ExecAct;
import com.ideal.ieai.server.engine.expreval.Evaluate;
import com.ideal.ieai.server.engine.task.TaskData;
import com.ideal.ieai.server.engine.util.*;
import com.ideal.ieai.server.ieaikernel.ConfigReader;
import com.ideal.ieai.server.ieaikernel.ServerEnv;
import com.ideal.ieai.server.jobscheduling.ieaikernel.EnvConfigReader;
import com.ideal.ieai.server.jobscheduling.repository.jobconfig.jobparconfig.VariableExtractor;
import com.ideal.ieai.server.jobscheduling.repository.workflowquery.WorkflowQueryManager;
import com.ideal.ieai.server.jobscheduling.util.taskuploadbean.ActSuccBean;
import com.ideal.ieai.server.platform.nessus.NessusRepairHandler;
import com.ideal.ieai.server.repository.DBUtil;
import com.ideal.ieai.server.repository.ObjectStorerDB;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.TransactionStorerJDBC;
import com.ideal.ieai.server.repository.activity.RepActExecTimeConfig;
import com.ideal.ieai.server.repository.activity.RepActValidateTimeConfig;
import com.ideal.ieai.server.repository.activity.RepActivityRuntime;
import com.ideal.ieai.server.repository.db.DBException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.db.DBRetryUtil;
import com.ideal.ieai.server.repository.engine.flowend.FlowEndFlagManager;
import com.ideal.ieai.server.repository.flowrule.DisableFlowRuleBean;
import com.ideal.ieai.server.repository.hd.actExcelToRun.ActExcelToRunData;
import com.ideal.ieai.server.repository.hd.platform.ftpinfo.model.FtpInfo;
import com.ideal.ieai.server.repository.hd.scriptManagement.BinaryEntityFiles;
import com.ideal.ieai.server.repository.hd.shellinfo.ShellInfoManager;
import com.ideal.ieai.server.repository.idgenerator.IdGenerator;
import com.ideal.ieai.server.repository.lob.LobStorer;
import com.ideal.ieai.server.repository.project.ProjectManager;
import com.ideal.ieai.server.springContext.IeaiSpringContextUtil;
import com.ideal.ieai.server.timetask.repository.executetask.CustomizationBean;
import com.ideal.ieai.server.timetask.repository.executetask.TaskInstanceBean;
import com.ideal.ieai.server.timetask.repository.executetask.TimeTaskManager;
import com.ideal.ieai.server.timetask.repository.executetask.TimetaskIPBean;
import oracle.sql.CLOB;
import org.apache.commons.lang.SerializationUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.Serializable;
import java.net.UnknownHostException;
import java.sql.*;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.*;
import java.util.Map.Entry;

public class EngineRepository
{
    private static final Logger           _log   = Logger.getLogger(EngineRepository.class);
    private static final Logger           syslog = Logger.getLogger("syslog");
    private static final EngineRepository _ins   = new EngineRepository();

    public static EngineRepository getInstance ()
    {
        return _ins;
    }

    public WorkflowInstance getFlowInstance ( long flowId ) throws RepositoryException
    {
        long start = System.currentTimeMillis();
        RepWorkflowInstance repFlow = EngineRepositotyJdbc.getInstance().getFlowInstance(flowId);
        if (SystemConfig.isElapsedTime())
            _log.info("Stamp:EngineRepository.getFlowInstance:" + (System.currentTimeMillis() - start));
        return null == repFlow ? null : repFlow.toCommons();
    }

    public WorkflowInstance getFlowInstance ( long flowId, int type ) throws RepositoryException
    {
        long start = System.currentTimeMillis();
        RepWorkflowInstance repFlow = EngineRepositotyJdbc.getInstance().getFlowInstance(flowId, type);
        if (SystemConfig.isElapsedTime())
            _log.info("Stamp:EngineRepository.getFlowInstance:" + (System.currentTimeMillis() - start));
        return null == repFlow ? null : repFlow.toCommons();
    }

    public Map getprjUuidAndflowDefId ( long flowId, int sysType ) throws RepositoryException
    {
        DbOpScheduler opScheduler = new DbOpScheduler();
        return opScheduler.getprjUuidAndflowDefId(flowId, sysType);
    }

    public Map getprjUuidAndflowDefId_KL ( long flowId, int sysType ) throws RepositoryException
    {
        DbOpScheduler opScheduler = new DbOpScheduler();
        return opScheduler.getprjUuidAndflowDefId_KL(flowId, sysType);
    }

    public int getFlowState ( long flowId, int type ) throws RepositoryException
    {
        DbOpScheduler opScheduler = new DbOpScheduler();
        return opScheduler.getFlowState(flowId, type);
    }

    public void saveFowInstance ( WorkflowInstance flowInsData ) throws RepositoryException
    {
        RepWorkflowInstance instance = RepWorkflowInstance.newInstance(flowInsData);
        // save workflowinstance
        StringBuffer sql = new StringBuffer("insert into ieai_workflowinstance(" + "IFLOWID," + "IISAUTOSTART,"
                + "IFLOWCOMMENT," + "IFLOWDEFID," + "IFLOWDES," + "IFLOWINSNAME," + "IFLOWNAME," + "IFLOWPRIOR,"
                + "IHOSTNAME," + "IISSAFEFIRST," + "IISMAINBRANCHEND," + "IMAINSCOPEID," + "IPRJUUID," + "IPROJECTNAME,"
                + "ISTARTTIME," + "ISTARTUSERID," + "ISTATUS," + "RECOVERTIME,"
                + "RECOVERPERSON) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement ps = null;
                Connection con = DBResource.getConnection("saveFowInstance", _log, Constants.IEAI_IEAI_BASIC);
                try
                {
                    ps = con.prepareStatement(sql.toString());
                    ps.setLong(1, flowInsData.getFlowId());
                    if (instance.isAutoStart())
                    {
                        ps.setInt(2, 1);
                    } else
                    {
                        ps.setInt(2, 0);
                    }
                    ps.setString(3, instance.getFlowComment());
                    ps.setLong(4, instance.getFlowDefId());
                    ps.setString(5, instance.getFlowDes());
                    ps.setString(6, instance.getFlowInsName());
                    ps.setString(7, instance.getFlowName());
                    ps.setLong(8, instance.getFlowPrior());
                    ps.setString(9, instance.getHostName());
                    if (instance.isSafeFirst())
                    {
                        ps.setInt(10, 1);
                    } else
                    {
                        ps.setInt(10, 0);
                    }
                    if (instance.isMainBranchEnd())
                    {
                        ps.setInt(11, 1);
                    } else
                    {
                        ps.setInt(11, 0);
                    }
                    ps.setLong(12, instance.getMainScopeId());
                    ps.setString(13, instance.getPrjUuid());
                    ps.setString(14, instance.getProjectName());
                    ps.setLong(15, instance.getStartTime());
                    ps.setLong(16, instance.getStartUserId().longValue());
                    ps.setLong(17, instance.getStatus());
                    ps.setLong(18, instance.getRecoverTime());
                    ps.setString(19, instance.getRecoverPerson());

                    ps.executeUpdate();
                    con.commit();
                } catch (SQLException e)
                {
                    DBResource.rollback(con, ServerError.ERR_DB_INSERT, e, "saveFowInstance", _log);
                } finally
                {
                    DBResource.closePSConn(con, ps, "saveFowInstance", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ex, ServerError.ERR_DB_INSERT);
            }
        }
    }

    public String loadBalanceIP ( long group, List addrs, Connection con ) throws RepositoryException
    {
        String ip = "*********";
//        long dateBasepollcycle = ServerEnv.getServerEnv().getMonitertimeoutpollcycle();
//
//        if (dateBasepollcycle < 360)
//        {
//            dateBasepollcycle = 360;
//        }
//        LoadBalanceRepository balance = new LoadBalanceRepository();
//        List b = balance.getServerList(group, addrs, con);
//        List a = balance.getServerOfFlow(group, addrs, con);
//        if (b.size() > a.size())
//        {
//            for (int i = 0; i < b.size(); i++)
//            {
//                if (!a.contains(b.get(i)))
//                {
//                    ip = (String) b.get(i);
//                    if (balance.updateServerList(con, ip, dateBasepollcycle))
//                    {
//                        return ip;
//                    }
//                }
//            }
//        }
//        for (int i = 0; i < a.size(); i++)
//        {
//            ip = (String) a.get(i);
//            if (balance.updateServerList(con, ip, dateBasepollcycle))
//            {
//                return ip;
//            }
//        }
        return ip;
    }

    public String loadBalanceIPV8 ( long flowId, long group, List addrs, Connection con ) throws RepositoryException
    {
        LoadBalanceRepository balance = new LoadBalanceRepository();
        long groupOfFlow = balance.getGroupFlow(flowId, con);
        if (groupOfFlow != -1)
        {
            // if (groupOfFlow == 8 || groupOfFlow == 7)
            // group = Constants.IEAI_INFOCOLLECTION;
            // else
            // group = groupOfFlow;
            group = groupOfFlow;
        }
        String ip = "";
        long dateBasepollcycle = ServerEnv.getServerEnv().getMonitertimeoutpollcycle();

        if (dateBasepollcycle < 360)
        {
            dateBasepollcycle = 360;
        }
        List b = balance.getServerList(group, addrs, con);
        List a = balance.getServerOfFlow(group, addrs, con);
        if (b.size() > a.size())
        {
            for (int i = 0; i < b.size(); i++)
            {
                if (!a.contains(b.get(i)))
                {
                    ip = (String) b.get(i);
                    if (balance.updateServerList(con, ip, dateBasepollcycle))
                    {
                        return ip;
                    }
                }
            }
        }
        for (int i = 0; i < a.size(); i++)
        {
            ip = (String) a.get(i);
            if (balance.updateServerList(con, ip, dateBasepollcycle))
            {
                return ip;
            }
        }
        return ip;
    }

    /**
     * <li>Description:对execl上传方式运行的步骤进行负载</li>
     *
     * <AUTHOR> 2016-1-14
     * @param group
     * @param addrs
     * @param con
     * @return
     * @throws RepositoryException return String
     */
    public String loadBalanceExeclIP ( long group, List addrs, Connection con ) throws RepositoryException
    {
        String ip = "";
        long dateBasepollcycle = ServerEnv.getServerEnv().getMonitertimeoutpollcycle();

        if (dateBasepollcycle < 360)
        {
            dateBasepollcycle = 360;
        }
        LoadBalanceRepository balance = new LoadBalanceRepository();
        List b = balance.getServerListExecl(group, addrs, con);
        List a = balance.getServerOfExecl(group, addrs, con);
        if (b.size() > a.size())
        {
            for (int i = 0; i < b.size(); i++)
            {
                if (!a.contains(b.get(i)))
                {
                    ip = (String) b.get(i);
                    if (balance.updateServerList(con, ip, dateBasepollcycle))
                    {
                        return ip;
                    }
                }
            }
        }
        for (int i = 0; i < a.size(); i++)
        {
            ip = (String) a.get(i);
            if (balance.updateServerList(con, ip, dateBasepollcycle))
            {
                return ip;
            }
        }
        return ip;
    }

    public long getGroupId ( String projectName, Connection conn, int prjType ) throws RepositoryException
    {
        LoadBalanceRepository balance = new LoadBalanceRepository();
        long a = balance.getGroupId(projectName, conn, prjType);
        return a;
    }

    public boolean isAlone ( Connection conn ) throws RepositoryException
    {
        LoadBalanceRepository balance = new LoadBalanceRepository();
        boolean a = balance.isAlone(conn);
        return a;
    }

    public WorkflowInfo getFlowInfo ( long theFlowId, int type ) throws RepositoryException
    {
        long startTime = System.currentTimeMillis();
        WorkflowInstance instance = getFlowInstance(theFlowId, type);
        if (null == instance)
            return null;
        WorkflowInfo flowInfo = instance.getWorkflowInfo();
        // update begin by yan_wang 2009 9.28
        for (int i = 0;; i++)
        {
            int taskNum = 0;
            try
            {
                Connection con = null;
                PreparedStatement ps = null;
                ResultSet getFlowInfoRS = null;
                try
                {
                    con = DBManager.getInstance().getJdbcConnection(type);
                } catch (DBException e)
                {
                    _log.error("EngineRepository getRexecRequest  2217 rows" + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_INIT);
                }
                try
                {
                    String sql = "select count(*) as tastCount from ieai_taskconstant reptaskcon where reptaskcon.iflowid=?";
                    ps = con.prepareStatement(sql);
                    ps.setLong(1, theFlowId);
                    getFlowInfoRS = ps.executeQuery();

                    while (getFlowInfoRS.next())
                    {
                        taskNum = (getFlowInfoRS.getInt("tastCount"));
                    }
                } catch (SQLException e)
                {
                    String method = Thread.currentThread().getStackTrace()[1].getMethodName();
                    _log.error(method + " is error at EngineRepositoty ", e);
                    DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, getFlowInfoRS, ps,
                            Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
            if (SystemConfig.isHibernateTime())
            {
                _log.info("Stamp:EngineRepository.getFlowInfo:" + (System.currentTimeMillis() - startTime));
            }
            flowInfo.setTaskNum(taskNum);
            return flowInfo;
        }
        // update end by yan_wang 2009 9.28
    }

    public String getLastServerTime () throws RepositoryException
    {
        // 修改jdbc处理 tao_ding on 2010-1-11
        return String.valueOf(EngineRepositotyJdbc.getInstance().getLastServerTime(getGroupId()));
    }

    public long getGroupId () throws RepositoryException
    {
        // 根据当前serverIp来获取相对组的ID值 xibin_gong on 2010-5-5
        String serverIp = "";
        try
        {
            serverIp = Environment.getInstance().getServerIP();
        } catch (UnknownHostException e)
        {
            e.printStackTrace();
        }
        return EngineRepositotyJdbc.getInstance().getGroupId(serverIp);
    }

    /**
     * Perform query on workflow instances based-on the specified flowFilter.
     *
     * @param flowFilter
     * @return List of WorkflowInfo
     * @throws RepositoryException
     */
    public List queryFlow ( WorkflowFilter flowFilter, int systype ) throws RepositoryException
    {
        long starttime = System.currentTimeMillis();
        // int totalRecord = queryWorkflowTotalRecord(flowFilter).intValue();

        StringBuffer criteria = new StringBuffer();
        // project name fileter
        if (!StringUtils.isBlank(flowFilter.projName))
        {
            criteria.append(" and flowinfo.iprojectname = '").append(flowFilter.projName + "'");
        }

        // 添加上一次启动的条件 start yangyang_wang 2009.6.04
        if ("true".equals(flowFilter.selectLast))
        {
            try
            {
                String _time = getLastServerTime();
                if (_time != null)
                {
                    Long lastTime = Long.valueOf(_time);
                    criteria.append(" and ( flowinfo.istarttime > ").append(lastTime)
                            .append(" or flowinfo.recovertime > ").append(lastTime).append(")");
                }
            } catch (Exception e)
            {
            }

        }
        // end

        // flow name filter
        if (null != flowFilter.flowName && flowFilter.flowName.length() > 0)
        {
            criteria.append(" and flowinfo.iflowname like '").append("%" + flowFilter.flowName + "%'");
        }
        // start user filter
        if (null != flowFilter.startUser && flowFilter.startUser.length() > 0)
        {
            criteria.append(" and flowinfo.istartuserfullname like '").append("%" + flowFilter.startUser + "%'");
        }
        // ///// StartTime 1
        if (null != flowFilter.startTime1)
        {
            criteria.append(" and flowinfo.istarttime > ").append(flowFilter.startTime1.getTime());
        }
        // ////// StartTime 2
        if (null != flowFilter.startTime2)
        {
            criteria.append(" and flowinfo.istarttime < ").append(flowFilter.startTime2.getTime());
        }
        // ////// EndTime 1
        if (null != flowFilter.endTime1)
        {
            criteria.append(" and flowinfo.iendtime > ").append(flowFilter.endTime1.getTime());
        }
        // ///// EndTime 2
        if (null != flowFilter.endTime2)
        {
            criteria.append(" and flowinfo.iendtime < ").append(flowFilter.endTime2.getTime());
        }
        // flow comment
        if (null != flowFilter.commentKeyList)
        {
            for (Iterator i = flowFilter.commentKeyList.iterator(); i.hasNext();)
            {
                String key = (String) i.next();
                criteria.append(" and flowinfo.iflowcomment like '").append("%" + key + "%'");
            }
        }
        // flow state
        if (-1 != flowFilter.state)
        {
            // 原有代码
            // criteria.append(" and flowinfo.istatus = ").append(flowFilter.state);
            // 改造代码 by yue_sun on 2018-04-11
            if (Constants.STATE_UNEXPECTED_END == flowFilter.state)
            {
                criteria.append(" and flowinfo.istatus in (" + Constants.STATE_UNEXPECTED_END + ","
                        + Constants.STATE_UNEXPECTED_END_PAUSE + ")");
            } else
            {
                criteria.append(" and flowinfo.istatus = ").append(flowFilter.state);
            }
        }
        // only show activity flow.
        if (flowFilter.actFlowsOnly)
        {
            criteria.append(" and flowinfo.istatus in (" + Constants.STATE_RUNNING + "," + Constants.STATE_LISTENING
                    + "," + Constants.STATE_RECOVERING + "," + Constants.STATE_PAUSED + "," + Constants.STATE_STOPPING
                    + ")");
        }
        // flow type
        if (WorkflowFilter.ALL != flowFilter.flowType)
        {
            Boolean type = flowFilter.flowType == WorkflowFilter.SAFE_FIRST_FLOW ? Boolean.TRUE : Boolean.FALSE;
            int isafefirst = 0;
            if (type.booleanValue())
            {
                isafefirst = 1;
            }
            criteria.append(" and flowinfo.iissafefirst =").append(isafefirst);
        }
        if (flowFilter.autoStartFlowsOnly)
        {
            criteria.append(" and flowinfo.iisautostart = 1");
        }

        if (null != flowFilter.flowInsName && flowFilter.flowInsName.length() > 0)
        {
            criteria.append(" and flowinfo.iflowinsname like '").append("%" + flowFilter.flowInsName + "%'");
        }

        if (null != flowFilter.flowId)
        {
            criteria.append(" and flowinfo.iflowid like ").append(flowFilter.flowId);
        }

        // Get flowinfo from ieai_flowinfo table basedon the criteria
        StringBuffer hqlGetFlows = new StringBuffer("select * from ieai_workflowinstance flowinfo ")
                .append(" where 1=1 and flowinfo.ihostname in (select l.ip from ieai_serverlist l where l.groupid= "
                        + systype + ") ");
        // begin.added by manxi_zhao.2016-09-10.
        if (null != flowFilter.projType)
        {
            hqlGetFlows = new StringBuffer();
            hqlGetFlows.append("select flowinfo.* from ieai_workflowinstance flowinfo ");
            hqlGetFlows.append(" , IEAI_PROJECT prj ");// added
            if("27".equals(flowFilter.projType)) {
                hqlGetFlows
                        .append(" where 1=1 ");
            }else {
                hqlGetFlows
                        .append(" where 1=1 and flowinfo.ihostname in (select l.ip from ieai_serverlist l where l.groupid= "
                                + systype + ") ");
            }

            // hqlGetFlows.append(" and flowinfo.IPRJUPPERID=prj.IID and
            // prj.PROTYPE="+flowFilter.projType);//added
            hqlGetFlows.append(" and flowinfo.IPRJUPPERID=prj.IUPPERID and prj.PROTYPE=" + flowFilter.projType);// added
        }
        if (null != flowFilter.inpermPrjIdsStr)// 权限条件过滤
        {
            // 没有任何权限
            if ("".equals(flowFilter.inpermPrjIdsStr))
            {
                hqlGetFlows.append(" and prj.IID is null ");
            } else
            {
                hqlGetFlows.append(" and prj.IID in (" + flowFilter.inpermPrjIdsStr + ") ");
            }
        }
        // end.added by manxi_zhao.2016-09-10.
        hqlGetFlows.append(criteria);
        String countSql = "select count(1) as cou from (" + hqlGetFlows.toString() + ") t ";
        int totalRecord = ObjectStorerDB.count(countSql, systype);

        if (true == flowFilter.order_isord)
        {
            setOrderBy2(hqlGetFlows, flowFilter);
        } else if (null != flowFilter.sortIndex)
        {
            if (true == flowFilter.isQueryOrder())
            {
                setOrderBy3(hqlGetFlows, flowFilter);
            } else
            {
                setOrderBy(hqlGetFlows, flowFilter);
            }
        } else
        {
            hqlGetFlows.append(" order by flowinfo.istarttime desc");
        }

        List queryResultGetFlows = null;
        if (flowFilter.canPage)
        {
            queryResultGetFlows = ObjectStorerDB.findForPageRepWorkflowInstance(hqlGetFlows.toString(),
                    flowFilter.pageSize, flowFilter.curPage, systype);
        } else
        {
            queryResultGetFlows = ObjectStorerDB.findRepWorkflowInstance(hqlGetFlows.toString(), systype);
        }

        List flowInfoResult = new ArrayList();
        for (Iterator iter = queryResultGetFlows.iterator(); iter.hasNext();)
        {
            RepWorkflowInstance flowResult = (RepWorkflowInstance) iter.next();
            WorkflowInstance instance = flowResult.toCommons();
            WorkflowInfo info = instance.getWorkflowInfo();

            StringBuffer hqlGetNumTasks = new StringBuffer(
                    "select flowinfo.iflowid, count(taskconstant.iid) as taskid from ")
                    .append(" ieai_workflowinstance flowinfo, ")
                    .append(" ieai_taskconstant taskconstant where flowinfo.iflowid=taskconstant.iflowid");

            Long flowInfoId = new Long(instance.getFlowId());
            hqlGetNumTasks.append(" and flowinfo.iflowid=" + flowInfoId + " ");

            hqlGetNumTasks.append(criteria);
            hqlGetNumTasks.append(" group by flowinfo.iflowid");

            List queryResultGetNumTasks = ObjectStorerDB.getNumTasks(hqlGetNumTasks.toString(), systype);
            // Integer i = (Integer) mapFlowTonumTasks.get(new
            // Long(instance.getFlowId()));
            Object[] record = null;
            Integer i = null;

            if (queryResultGetNumTasks.size() > 0)
            {
                record = (Object[]) queryResultGetNumTasks.get(0);
                i = (Integer) record[1];
            }

            // Integer i = (Integer) mapFlowTonumTasks.get(new
            // Long(instance.getFlowId()));

            if (i != null)
            {
                info.setTaskNum(i.intValue());
            }
            // begin.by manxi_zhao.2016.09.22.
            // 注释掉旧的权限过滤代码 ,权限过滤作为条件拼接在sql中完成
            // // if the permission is set,use it to filter the query result;
            // if (flowFilter.permissions != null)
            // {
            // if (!(Constants.PERM_ENABLED == flowFilter.permissions.checkPermission(Constants.PRJ,
            // instance.getProjectName(), Constants.PERM_FLOW_GETINFO)))
            // {
            // continue;
            // }
            // }
            // end.by manxi_zhao.2016.09.22.
            info.setTotalRecord(totalRecord);
            flowInfoResult.add(info);
        }
        if (SystemConfig.isElapsedTime())
            _log.info("Stamp:EngineRepository.queryflow:" + (System.currentTimeMillis() - starttime));
        return flowInfoResult;
    }

    // 新增中XINExt灾难恢复
    public List queryFlow ( WorkflowFilter flowFilter, Map map, int dbtype ) throws RepositoryException
    {
        long starttime = System.currentTimeMillis();
        int totalRecord = queryWorkflowTotalRecord(flowFilter).intValue();
        map.put("total", totalRecord);
        StringBuffer criteria = new StringBuffer();
        // project name fileter
        if (!StringUtils.isBlank(flowFilter.projName))
        {
            criteria.append(" and flowinfo.iprojectname = '").append(flowFilter.projName + "'");
        }

        // 添加上一次启动的条件 start yangyang_wang 2009.6.04
        if ("true".equals(flowFilter.selectLast))
        {
            try
            {
                String _time = getLastServerTime();
                if (_time != null)
                {
                    Long lastTime = Long.valueOf(_time);
                    criteria.append(" and ( flowinfo.istarttime > ").append(lastTime)
                            .append(" or flowinfo.recovertime > ").append(lastTime).append(")");
                }
            } catch (Exception e)
            {
            }

        }
        // end

        // flow name filter
        if (null != flowFilter.flowName && flowFilter.flowName.length() > 0)
        {
            criteria.append(" and flowinfo.iflowname like '").append("%" + flowFilter.flowName + "%'");
        }
        // start user filter
        if (null != flowFilter.startUser && flowFilter.startUser.length() > 0)
        {
            criteria.append(" and flowinfo.istartuserfullname like '").append("%" + flowFilter.startUser + "%'");
        }
        // ///// StartTime 1
        if (null != flowFilter.startTime1)
        {
            criteria.append(" and flowinfo.istarttime > ").append(flowFilter.startTime1.getTime());
        }
        // ////// StartTime 2
        if (null != flowFilter.startTime2)
        {
            criteria.append(" and flowinfo.istarttime < ").append(flowFilter.startTime2.getTime());
        }
        // ////// EndTime 1
        if (null != flowFilter.endTime1)
        {
            criteria.append(" and flowinfo.iendtime > ").append(flowFilter.endTime1.getTime());
        }
        // ///// EndTime 2
        if (null != flowFilter.endTime2)
        {
            criteria.append(" and flowinfo.iendtime < ").append(flowFilter.endTime2.getTime());
        }
        // flow comment
        if (null != flowFilter.commentKeyList)
        {
            for (Iterator i = flowFilter.commentKeyList.iterator(); i.hasNext();)
            {
                String key = (String) i.next();
                criteria.append(" and flowinfo.iflowcomment like '").append("%" + key + "%'");
            }
        }
        // flow state
        if (-1 != flowFilter.state)
        {
            // 原有代码
            // criteria.append(" and flowinfo.istatus = ").append(flowFilter.state);
            // 改造代码 by yue_sun on 2018-04-11
            if (Constants.STATE_UNEXPECTED_END == flowFilter.state)
            {
                criteria.append(" and flowinfo.status in (" + Constants.STATE_UNEXPECTED_END + ","
                        + Constants.STATE_UNEXPECTED_END_PAUSE + ")");
            } else
            {
                criteria.append(" and flowinfo.istatus = ").append(flowFilter.state);
            }
        }
        // only show activity flow.
        if (flowFilter.actFlowsOnly)
        {
            criteria.append(" and flowinfo.istatus in (" + Constants.STATE_RUNNING + "," + Constants.STATE_LISTENING
                    + "," + Constants.STATE_RECOVERING + "," + Constants.STATE_PAUSED + "," + Constants.STATE_STOPPING
                    + ")");
        }
        // flow type
        if (WorkflowFilter.ALL != flowFilter.flowType)
        {
            Boolean type = flowFilter.flowType == WorkflowFilter.SAFE_FIRST_FLOW ? Boolean.TRUE : Boolean.FALSE;
            int isafefirst = 0;
            if (type.booleanValue())
            {
                isafefirst = 1;
            }
            criteria.append(" and flowinfo.iissafefirst =").append(isafefirst);
        }
        if (flowFilter.autoStartFlowsOnly)
        {
            criteria.append(" and flowinfo.iisautostart = 1");
        }

        if (null != flowFilter.flowInsName && flowFilter.flowInsName.length() > 0)
        {
            criteria.append(" and ri.IRUNINSNAME like '").append("%" + flowFilter.flowInsName + "%'");
        }

        if (null != flowFilter.flowId)
        {
            criteria.append(" and flowinfo.iflowid like ").append(flowFilter.flowId);
        }

        // Get flowinfo from ieai_flowinfo table basedon the criteria
        StringBuffer hqlGetFlows = new StringBuffer(
                "select flowinfo.*,ri.ISYSNAME,ri.IRUNINSNAME,r.IACTNAME  from ieai_workflowinstance flowinfo,IEAI_RUN_INSTANCE ri,IEAI_RUNINFO_INSTANCE r ")
                .append(
                        " where ri.iid=r.IRUNINSID  and  flowinfo.iflowid=r.iflowid  and flowinfo.ihostname in (select l.ip from ieai_serverlist l where l.groupid= "
                                + dbtype + ") ");
        hqlGetFlows.append(criteria);
        if (true == flowFilter.order_isord)
        {
            setOrderBy2(hqlGetFlows, flowFilter);
        } else if (null != flowFilter.sortIndex)
        {
            if (true == flowFilter.isQueryOrder())
            {
                setOrderBy3(hqlGetFlows, flowFilter);
            } else
            {
                setOrderBy(hqlGetFlows, flowFilter);
            }
        } else
        {
            hqlGetFlows.append(" order by flowinfo.istarttime desc");
        }

        // execute the query string and get the specify result:
        List queryResultGetFlows = null;
        if (flowFilter.canPage)
        {
            queryResultGetFlows = ObjectStorerDB.findForPageRepWorkflowInstance(hqlGetFlows.toString(),
                    flowFilter.pageSize, flowFilter.curPage, dbtype);
        } else
        {
            queryResultGetFlows = ObjectStorerDB.findRepWorkflowInstance(hqlGetFlows.toString(), dbtype);
        }

        // List queryResultGetNumTasks =
        // ObjectStorer.findWithRetry(hqlGetNumTasks.toString(),
        // valueList.toArray(), (Type[]) typeList.toArray(new Type[0]));

        // map from flow id to number of user tasks
        // Map mapFlowTonumTasks = new HashMap();
        // for (int i = 0, size = queryResultGetNumTasks.size(); i < size; i++)
        // {
        // Object[] record = (Object[]) queryResultGetNumTasks.get(i);
        // mapFlowTonumTasks.put(record[0], record[1]);
        // }

        // turn RepWorkflowInstance to workflowInfo list
        List flowInfoResult = new ArrayList();
        for (Iterator iter = queryResultGetFlows.iterator(); iter.hasNext();)
        {
            RepWorkflowInstance flowResult = (RepWorkflowInstance) iter.next();
            WorkflowInstance instance = flowResult.toCommons();
            WorkflowInfo info = instance.getWorkflowInfo();

            StringBuffer hqlGetNumTasks = new StringBuffer(
                    "select flowinfo.iflowid, count(taskconstant.iid) as taskid from ")
                    .append(" ieai_workflowinstance flowinfo, ")
                    .append(" ieai_taskconstant taskconstant where flowinfo.iflowid=taskconstant.iflowid");

            Long flowInfoId = new Long(instance.getFlowId());
            hqlGetNumTasks.append(" and flowinfo.iflowid=" + flowInfoId + " ");

            hqlGetNumTasks.append(criteria);
            hqlGetNumTasks.append(" group by flowinfo.iflowid");

            List queryResultGetNumTasks = ObjectStorerDB.getNumTasks(hqlGetNumTasks.toString(), dbtype);
            // Integer i = (Integer) mapFlowTonumTasks.get(new
            // Long(instance.getFlowId()));
            Object[] record = null;
            Integer i = null;
            // for (int ii = 0, size = queryResultGetNumTasks.size(); ii < size;
            // ii++)
            // {
            // record = (Object[]) queryResultGetNumTasks.get(ii);
            // i = (Integer)record[1];
            // }
            if (queryResultGetNumTasks.size() > 0)
            {
                record = (Object[]) queryResultGetNumTasks.get(0);
                i = (Integer) record[1];
            }

            // Integer i = (Integer) mapFlowTonumTasks.get(new
            // Long(instance.getFlowId()));

            if (i != null)
            {
                info.setTaskNum(i.intValue());
            }
            // if the permission is set,use it to filter the query result;
            if (flowFilter.permissions != null)
            {
                if (!(Constants.PERM_ENABLED == flowFilter.permissions.checkPermission(Constants.PRJ,
                        instance.getProjectName(), Constants.PERM_FLOW_GETINFO)))
                {
                    continue;
                }
            }
            info.setTotalRecord(totalRecord);
            flowInfoResult.add(info);
        }
        if (SystemConfig.isElapsedTime())
            _log.info("Stamp:EngineRepository.queryflow:" + (System.currentTimeMillis() - starttime));
        return flowInfoResult;
    }

    /**
     * @update tao_ding
     * @des:修改为确定唯一的工作流进行解析
     * @datea:2010-1-21
     * @param flowId
     * @return
     * @throws RepositoryException
     */
    public WorkflowInfo queryForRecover ( long flowId, int type )
    {
        RepWorkflowInstance flowResult = null;
        try
        {
            flowResult = EngineRepositotyJdbc.getInstance().queryForRecover(flowId, type);
        } catch (RepositoryException e)
        {
            _log.error("Error when select workflow for recovery!");
        }
        if (flowResult == null)
        {
            return null;
        }
        WorkflowInstance instance = flowResult.toCommons();
        WorkflowInfo info = instance.getWorkflowInfo();
        return info;
    }

    /**
     * <AUTHOR>
     * @des:集群下工作流恢复查询
     * @datea:2010-3-26
     * @param flowId
     * @return
     * @throws RepositoryException
     */
    public RepWorkflowInstance queryForRecoverCluse ( long flowId, Connection con ) throws RepositoryException
    {
        RepWorkflowInstance flowResult = null;
        try
        {
            flowResult = EngineRepositotyJdbc.getInstance().queryForRecoverCluse(flowId, con);
        } catch (RepositoryException e)
        {
            String method = Thread.currentThread().getStackTrace()[1].getMethodName();
            _log.error(method + " is error at EngineRepository ", e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        }
        if (flowResult == null)
        {
            return null;
        }
        return flowResult;
    }

    /*
     * Perform query on workflow instances based-on the specified flowFilter.
     *
     * @param flowFilter @return List of WorkflowInfo @throws RepositoryException
     */
    public List queryIndexFlow ( WorkflowFilter flowFilter, int dbtype ) throws RepositoryException
    {
        int totalRecord = queryWorkflowTotalRecord(flowFilter).intValue();

        StringBuffer criteria = new StringBuffer();
        List typeList = new ArrayList();
        List valueList = new ArrayList();

        // project name fileter
        if (!StringUtils.isBlank(flowFilter.projName))
        {
            criteria.append(" and flowinfo.iprojectname = '").append(flowFilter.projName);
        }
        // flow name filter
        if (null != flowFilter.flowName && flowFilter.flowName.length() > 0)
        {
            criteria.append(" and flowinfo.iflowname like '").append("%" + flowFilter.flowName + "%'");
        }
        // start user filter
        if (null != flowFilter.startUser && flowFilter.startUser.length() > 0)
        {
            criteria.append(" and flowinfo.istartuserfullname like '").append("%" + flowFilter.startUser + "%'");
        }
        // ///// StartTime 1
        if (null != flowFilter.startTime1)
        {
            criteria.append(" and flowinfo.istarttime > ").append(flowFilter.startTime1.getTime());
        }
        // ////// StartTime 2
        if (null != flowFilter.startTime2)
        {
            criteria.append(" and flowinfo.istarttime < ").append(flowFilter.startTime2.getTime());
        }
        // ////// EndTime 1
        if (null != flowFilter.endTime1)
        {
            criteria.append(" and flowinfo.iendtime > ").append(flowFilter.endTime1.getTime());
        }
        // ///// EndTime 2
        if (null != flowFilter.endTime2)
        {
            criteria.append(" and flowinfo.iendtime < ").append(flowFilter.endTime2.getTime());
        }
        // flow comment
        if (null != flowFilter.commentKeyList)
        {
            for (Iterator i = flowFilter.commentKeyList.iterator(); i.hasNext();)
            {
                String key = (String) i.next();
                criteria.append(" and flowinfo.iflowcomment like '").append("%" + key + "%'");
            }
        }
        // flow state
        if (-1 != flowFilter.state)
        {
            // 原有代码
            // criteria.append(" and flowinfo.istatus = ").append(flowFilter.state);
            // 改造代码 by yue_sun on 2018-04-11
            if (Constants.STATE_UNEXPECTED_END == flowFilter.state)
            {
                criteria.append(" and flowinfo.istatus in (" + Constants.STATE_UNEXPECTED_END + ","
                        + Constants.STATE_UNEXPECTED_END_PAUSE + ")");
            } else
            {
                criteria.append(" and flowinfo.istatus = ").append(flowFilter.state);
            }
        }
        // only show activity flow.
        if (flowFilter.actFlowsOnly)
        {
            criteria.append(" and flowinfo.istatus in (" + Constants.STATE_RUNNING + "," + Constants.STATE_LISTENING
                    + "," + Constants.STATE_RECOVERING + "," + Constants.STATE_PAUSED + "," + Constants.STATE_STOPPING
                    + ")");
        }
        // flow type
        if (WorkflowFilter.ALL != flowFilter.flowType)
        {
            Boolean type = flowFilter.flowType == WorkflowFilter.SAFE_FIRST_FLOW ? Boolean.TRUE : Boolean.FALSE;
            int iissafeFirst = 0;
            if (type.booleanValue())
            {
                iissafeFirst = 1;
            }
            criteria.append(" and flowinfo.iissafeFirst = ").append(iissafeFirst);
        }
        if (flowFilter.autoStartFlowsOnly)
        {
            criteria.append(" and flowinfo.iisautostart = 1");
        }

        if (null != flowFilter.flowInsName && flowFilter.flowInsName.length() > 0)
        {
            criteria.append(" and flowinfo.iflowinsname like '").append("%" + flowFilter.flowInsName + "%'");
        }

        if (null != flowFilter.flowId)
        {
            criteria.append(" and flowinfo.iflowid like ").append(flowFilter.flowId);
        }

        // Get flowinfo from ieai_flowinfo table basedon the criteria
        StringBuffer hqlGetFlows = new StringBuffer("select * from ieai_workflowinstance flowinfo where 1=1 ");
        hqlGetFlows.append(criteria);
        if (null != flowFilter.sortIndex)
        {
            setOrderBy(hqlGetFlows, flowFilter);
        }

        // execute the query string and get the specify result:
        List queryResultGetFlows = null;
        if (flowFilter.canPage)
        {
            queryResultGetFlows = ObjectStorerDB.findForPageRepWorkflowInstance(hqlGetFlows.toString(),
                    flowFilter.pageSize, flowFilter.curPage, dbtype);
        } else
        {
            queryResultGetFlows = ObjectStorerDB.findRepWorkflowInstance(hqlGetFlows.toString(), dbtype);
        }

        // turn RepWorkflowInstance to workflowInfo list
        List flowInfoResult = new ArrayList();
        for (Iterator iter = queryResultGetFlows.iterator(); iter.hasNext();)
        {
            RepWorkflowInstance flowResult = (RepWorkflowInstance) iter.next();
            WorkflowInstance instance = flowResult.toCommons();
            WorkflowInfo info = instance.getWorkflowInfo();

            List queryResultGetNumTasks = new ArrayList(0);
            Object[] record = null;
            Integer i = null;
            if (queryResultGetNumTasks.size() > 0)
            {
                record = (Object[]) queryResultGetNumTasks.get(0);
                i = (Integer) record[1];
            }

            // Integer i = (Integer) mapFlowTonumTasks.get(new
            // Long(instance.getFlowId()));

            if (i != null)
            {
                info.setTaskNum(i.intValue());
            }
            // if the permission is set,use it to filter the query result;
            if (flowFilter.permissions != null)
            {
                if (!(Constants.PERM_ENABLED == flowFilter.permissions.checkPermission(Constants.PRJ,
                        instance.getProjectName(), Constants.PERM_FLOW_GETINFO)))
                {
                    continue;
                }
            }
            info.setTotalRecord(totalRecord);
            flowInfoResult.add(info);
        }
        return flowInfoResult;
    }

    private void setOrderBy ( StringBuffer buffer, WorkflowFilter flowFilter )
    {
        String[] fields = { null, "flowinfo.iflowname", "flowinfo.iflowinsname", "flowinfo.iflowid",
                "flowinfo.iprojectname", "flowinfo.istatus", "flowinfo.iissafefirst", "flowinfo.istartuserfullname",
                "flowinfo.istarttime", "flowinfo.iendtime", "flowinfo.iendtime-flowinfo.istarttime", null };
        String field = fields[flowFilter.sortIndex.intValue()];
        if (null != field)
        {
            buffer.append(" order by " + field + (flowFilter.order.booleanValue() ? " desc " : " asc  "));
        }

    }

    // junfeng_liu
    private void setOrderBy3 ( StringBuffer buffer, WorkflowFilter flowFilter )
    {

        String[] fields = { null, "flowinfo.iflowname", "flowinfo.iflowinsname", "flowinfo.iflowid",
                "flowinfo.iprojectname", "flowinfo.istatus", "flowinfo.istartuserfullname", "flowinfo.istarttime",
                "flowinfo.iendtime", "flowinfo.iendtime-flowinfo.istarttime", "flowinfo.iendtime-flowinfo.istarttime",
                null };
        String field = fields[flowFilter.sortIndex.intValue()];
        if (null != field)
        {
            buffer.append(" order by " + field + (flowFilter.order.booleanValue() ? " desc " : " asc  "));
        }

    }

    // 灾难查询自用排序 yangyang_wang 2009.06.07
    private void setOrderBy2 ( StringBuffer buffer, WorkflowFilter flowFilter )
    {
        String[] fields = { null, "flowinfo.iflowname", "flowinfo.iflowinsname", "flowinfo.iflowid",
                "flowinfo.iprojectname", "flowinfo.istatus", "flowinfo.istarttime", "flowinfo.iendtime", null };
        String field = fields[flowFilter.order_index];
        if (null != field)
        {
            buffer.append(" order by " + field + (flowFilter.order_isdora ? " desc " : "  asc "));
        }

    }

    // end

    private Integer queryWorkflowTotalRecord ( WorkflowFilter flowFilter ) throws RepositoryException
    {
        StringBuffer criteria = new StringBuffer();
        // project name fileter
        if (!StringUtils.isBlank(flowFilter.projName))
        {
            criteria.append(" and flowinfo.iprojectname ='").append(flowFilter.projName).append("'");
        }

        // 添加上一次启动的条件 start yangyang_wang 2009.6.04
        if ("true".equals(flowFilter.selectLast))
        {
            try
            {
                String _time = getLastServerTime();
                if (_time != null)
                {
                    Long lastTime = Long.valueOf(_time);
                    criteria.append(" and ( flowinfo.istarttime > ").append(lastTime)
                            .append(" or flowinfo.recovertime > ").append(lastTime).append(")");
                }
            } catch (Exception e)
            {
            }

        }
        // end

        // flow name filter
        if (null != flowFilter.flowName && flowFilter.flowName.length() > 0)
        {
            criteria.append(" and flowinfo.iflowname like '%").append(flowFilter.flowName).append("%'");
        }
        // start user filter
        if (null != flowFilter.startUser && flowFilter.startUser.length() > 0)
        {
            criteria.append(" and flowinfo.istartuserfullname like '%").append(flowFilter.startUser).append("%'");
        }
        // ///// StartTime 1
        if (null != flowFilter.startTime1)
        {
            criteria.append(" and flowinfo.istarttime > ").append(flowFilter.startTime1.getTime());
        }
        // ////// StartTime 2
        if (null != flowFilter.startTime2)
        {
            criteria.append(" and flowinfo.istarttime < ").append(flowFilter.startTime2.getTime());
        }
        // ////// EndTime 1
        if (null != flowFilter.endTime1)
        {
            criteria.append(" and flowinfo.iendtime > ").append(flowFilter.endTime1.getTime());
        }
        // ///// EndTime 2
        if (null != flowFilter.endTime2)
        {
            criteria.append(" and flowinfo.iendtime < ").append(flowFilter.endTime2.getTime());
        }
        // flow comment
        if (null != flowFilter.commentKeyList)
        {
            for (Iterator i = flowFilter.commentKeyList.iterator(); i.hasNext();)
            {
                String key = (String) i.next();
                criteria.append(" and flowinfo.iflowcomment like '").append("%" + key + "%'");
            }
        }
        // flow state
        if (-1 != flowFilter.state)
        {
            criteria.append(" and flowinfo.istatus =").append(flowFilter.state);
        }
        // only show activity flow.
        if (flowFilter.actFlowsOnly)
        {
            criteria.append(" and flowinfo.istatus in (" + Constants.STATE_RUNNING + "," + Constants.STATE_LISTENING
                    + "," + Constants.STATE_RECOVERING + "," + Constants.STATE_PAUSED + "," + Constants.STATE_STOPPING
                    + ")");
        }
        // flow type
        if (WorkflowFilter.ALL != flowFilter.flowType)
        {
            Boolean type = flowFilter.flowType == WorkflowFilter.SAFE_FIRST_FLOW ? Boolean.TRUE : Boolean.FALSE;
            int isafefirst = 0;
            if (type.booleanValue())
            {
                isafefirst = 1;
            }
            criteria.append(" and flowinfo.iissafefirst =").append(isafefirst);
        }
        if (flowFilter.autoStartFlowsOnly)
        {
            criteria.append(" and flowinfo.iisautostart = 1");
        }

        if (null != flowFilter.flowInsName && flowFilter.flowInsName.length() > 0)
        {
            criteria.append(" and flowinfo.iflowinsname like '").append("%" + flowFilter.flowInsName + "%'");
        }

        if (null != flowFilter.flowId)
        {
            criteria.append(" and flowinfo.iflowid like ").append(flowFilter.flowId);
        }

        long groupId = getGroupId();
        // Get flowinfo from ieai_flowinfo table basedon the criteria
        StringBuffer hqlGetFlows = new StringBuffer(
                "select count(*) from ieai_workflowinstance flowinfo,IEAI_RUN_INSTANCE ri,IEAI_RUNINFO_INSTANCE r  where ri.iid=r.IRUNINSID  and  flowinfo.iflowid=r.iflowid  and flowinfo.ihostname in (select l.ip from ieai_serverlist l where l.groupid="
                        + groupId + ") ");
        hqlGetFlows.append(criteria);

        // execute the query string and get the specify result:
        return new Integer(ObjectStorerDB.count(hqlGetFlows.toString()));
    }

    public void saveFlowEnvVars ( long flowId, Map envVarValues ) throws RepositoryException, MarshallingException
    {
        saveFlowInitEnv(flowId, envVarValues, RepFlowEnv.FLOW_ENV);
    }

    public void saveFlowArgs ( long flowId, Map paramValueMap ) throws RepositoryException, MarshallingException
    {
        saveFlowInitEnv(flowId, paramValueMap, RepFlowEnv.FLOW_PARAMETER);
    }

    public void saveFlowOutput ( long flowId, Map actOutput ) throws RepositoryException, MarshallingException
    {
        saveFlowInitEnv(flowId, actOutput, RepFlowEnv.FLOW_OUTPUT);
    }

    public void setParamValue ( long flowId, String name, Object value, int type )
            throws RepositoryException, UnMarshallingException, MarshallingException
    {
        setFlowInitEnv(flowId, name, value, RepFlowEnv.FLOW_PARAMETER);
    }

    public void setEnvValue ( long flowId, String name, Object value )
            throws RepositoryException, UnMarshallingException, MarshallingException
    {
        setFlowInitEnv(flowId, name, value, RepFlowEnv.FLOW_ENV);
    }

    public Map getFlowOutput ( long flowId ) throws RepositoryException, UnMarshallingException
    {
        return getFlowInitEnv(flowId, RepFlowEnv.FLOW_OUTPUT, Constants.IEAI_IEAI_BASIC);
    }

    public Map getFlowParameters ( long flowId, int type ) throws RepositoryException, UnMarshallingException
    {
        return getFlowInitEnv(flowId, RepFlowEnv.FLOW_PARAMETER, type);
    }

    public Map getEnvVars ( long flowId ) throws RepositoryException, UnMarshallingException
    {
        return getFlowInitEnv(flowId, RepFlowEnv.FLOW_ENV, Constants.IEAI_IEAI_BASIC);
    }

    private Map getFlowInitEnv ( long flowId, int type, int dbtype ) throws RepositoryException, UnMarshallingException
    {
        Map tempMap = null;
        for (int i = 0;; i++)
        {
            try
            {
                Connection con = null;
                try
                {
                    con = DBResource.getRConnection("getFlowInitEnv", _log, dbtype);
                    List list = EngineRepositotyJdbc.getInstance().getFlowEnv(flowId, type, con);

                    if (list.isEmpty())
                    {
                        tempMap = null;
                        if (type == RepFlowEnv.FLOW_PARAMETER)
                        {
                            return Collections.EMPTY_MAP;
                        } else
                        {
                            return null;
                        }
                    }
                    RepFlowEnv flowEnv = (RepFlowEnv) list.get(0);
                    byte[] bytes = LobStorer.getBinary(dbtype, new Long(flowEnv.getFlowDataId()));
                    // add by tao_ding on 20090911
                    if (null == bytes || bytes.length == 0)
                    {
                        try
                        {
                            Thread.sleep(3000);
                        } catch (InterruptedException e)
                        {
                        }
                        bytes = LobStorer.getBinary(dbtype, new Long(flowEnv.getFlowDataId()));
                    } else
                    {
                        tempMap = (Map) SerializationUtils.deserialize(bytes);
                    }

                    // end
                    tempMap = (Map) SerializationUtils.deserialize(bytes);
                } finally
                {

                    DBResource.closeConnection(con, "getFlowInitEnv", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
        return IDataMarshallerHelper.revertValueMap(tempMap);
    }

    private void setFlowInitEnv ( long flowId, String name, Object value, int type )
            throws RepositoryException, UnMarshallingException, MarshallingException
    {
        for (int i = 0;; i++)
        {
            try
            {
                Connection con = null;
                try
                {
                    con = DBResource.getRConnection("setFlowInitEnv", _log, Constants.IEAI_IEAI_BASIC);
                    List list = EngineRepositotyJdbc.getInstance().getFlowEnv(flowId, type, con);

                    Map map = EngineRepositotyJdbc.getInstance().getFlowInitEnv(list, type, con);
                    map.remove(name);
                    map.put(name, value);
                    Map tempMap = IDataMarshallerHelper.translateValueMap(map);
                    byte[] bytes = SerializationUtils.serialize((HashMap) tempMap);
                    RepFlowEnv flowEnv = (RepFlowEnv) list.get(0);
                    LobStorer.updateBinary(new Long(flowEnv.getFlowDataId()), bytes, con);

                    con.commit();
                } catch (SQLException e)
                {
                    DBResource.rollback(con, ServerError.ERR_DB_ROLLBACK, e, "setFlowInitEnv", _log);
                } finally
                {
                    DBResource.closeConnection(con, "setFlowInitEnv", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ex, ServerError.ERR_DB_UPDATE);
            }
        }
    }

    private void saveFlowInitEnv ( long flowId, Map object, int type ) throws RepositoryException, MarshallingException
    {
        for (int i = 0;; i++)
        {
            try
            {
                if (object == null)
                {
                    return;
                }
                Map tempMap = null;
                Connection con = null;
                try
                {
                    con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
                } catch (DBException e)
                {
                    _log.error(" saveFlowInitEnv 1083 rows " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_INIT);
                }
                try
                {
                    // 修改为JDBC连接 update by tao_ding on 2009-10-12
                    DbOpScheduler opScheduler = new DbOpScheduler();
                    RepFlowEnv flowEnv = opScheduler.querySingleFlowEnv(flowId, type, con);
                    // end update
                    if (flowEnv == null)
                    {
                        Long byteId = null;
                        tempMap = IDataMarshallerHelper.translateValueMap(object);
                        byte[] bytes = SerializationUtils.serialize((HashMap) tempMap);
                        byteId = LobStorer.saveBinaryNoCommit(bytes, con);
                        // update by tao_ding on 2009-10-12
                        opScheduler.saveFlowEnv(flowId, type, byteId.longValue(), con);
                        // end update
                    } else
                    {
                        tempMap = IDataMarshallerHelper.translateValueMap(object);
                        byte[] bytes = SerializationUtils.serialize((HashMap) tempMap);
                        if (flowEnv.getFlowDataId() == 0)
                        {
                            Long byteId = LobStorer.saveBinaryNoCommit(bytes, con);
                            // update by tao_ding on 2009-10-12
                            opScheduler.saveOrUpdateFlowEnv(flowEnv.getId(), byteId.longValue(), con);
                            // end update
                        } else
                        {
                            LobStorer.updateBinary(new Long(flowEnv.getFlowDataId()), bytes, con);
                        }
                    }
                    con.commit();
                    return;
                } catch (SQLException e)
                {
                    // 失败后数据回滚
                    DBResource.rollback(con, ServerError.ERR_DB_UPDATE, e,
                            Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                } finally
                {
                    DBResource.closeConnection(con, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
    }

    /**
     * TODO ********************* BranchScope *************************
     */
    public void saveBranchScope ( BranchScope branchScope ) throws RepositoryException
    {
        RepBranchScope branchscope = RepBranchScope.newInstance(branchScope);
        StringBuffer sql = new StringBuffer("insert into ieai_branchscope values(").append(branchscope.getScopeId())
                .append(",").append(branchscope.getFlowId()).append(",").append(branchscope.getParentScopeId())
                .append(")");
        ObjectStorerDB.save(sql.toString());
    }

    public long getParentScopeId ( long scopeId ) throws RepositoryException
    {
        // update begin by yan_wang 2009 9.28
        long startTime = System.currentTimeMillis();
        long scopeid = -1;
        for (int i = 0;; i++)
        {
            try
            {
                Connection con = null;
                PreparedStatement ps = null;
                ResultSet branchScopeRS = null;
                try
                {
                    con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
                } catch (DBException e)
                {
                    _log.error("EngineRepository getParentScopeId  1106 rows" + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_INIT);
                }
                try
                {
                    String sql = "select repbranchs.iparentscope as iparentscope from ieai_branchscope repbranchs where repbranchs.iscopeid=?";
                    ps = con.prepareStatement(sql);
                    ps.setLong(1, scopeId);
                    branchScopeRS = ps.executeQuery();

                    while (branchScopeRS.next())
                    {
                        scopeid = branchScopeRS.getLong("iparentscope");
                        return scopeid;
                    }
                    return scopeid;
                } catch (SQLException e)
                {
                    String method = Thread.currentThread().getStackTrace()[1].getMethodName();
                    _log.error(method + " is error at EngineRepositotyJdbc ", e);
                    DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, branchScopeRS, ps,
                            Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
            if (SystemConfig.isHibernateTime())
            {
                _log.info("Stamp:EngineRepository.getParentScopeId:" + (System.currentTimeMillis() - startTime));
            }
        }
        return scopeid;
        // update end by yan_wang
    }

    public long getParentScopeId ( long scopeId, int type ) throws RepositoryException
    {
        // update begin by yan_wang 2009 9.28
        long startTime = System.currentTimeMillis();
        long scopeid = -1;
        for (int i = 0;; i++)
        {
            try
            {
                Connection con = null;
                PreparedStatement ps = null;
                ResultSet branchScopeRS = null;
                try
                {
                    con = DBManager.getInstance().getJdbcConnection(type);
                } catch (DBException e)
                {
                    _log.error("EngineRepository getParentScopeId  1106 rows" + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_INIT);
                }
                try
                {
                    String sql = "select repbranchs.iparentscope as iparentscope from ieai_branchscope repbranchs where repbranchs.iscopeid=?";
                    ps = con.prepareStatement(sql);
                    ps.setLong(1, scopeId);
                    branchScopeRS = ps.executeQuery();

                    while (branchScopeRS.next())
                    {
                        scopeid = branchScopeRS.getLong("iparentscope");
                        return scopeid;
                    }
                    return scopeid;
                } catch (SQLException e)
                {
                    String method = Thread.currentThread().getStackTrace()[1].getMethodName();
                    _log.error(method + " is error at EngineRepositotyJdbc ", e);
                    DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, branchScopeRS, ps,
                            Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
            if (SystemConfig.isHibernateTime())
            {
                _log.info("Stamp:EngineRepository.getParentScopeId:" + (System.currentTimeMillis() - startTime));
            }
        }
        return scopeid;
        // update end by yan_wang
    }

    public ExecError getLastError ( long scopeId ) throws RepositoryException
    {
        StringBuffer sql = new StringBuffer("select iactid, iflowid, iactivityname, iexceptionname, imessage, ioccurtime, ilocation, iscopeid, iexecactid, ierrorstacktrace from ieai_iexecerror where iscopeid=").append(scopeId);
        RepExecError repError = ObjectStorerDB.getExecError(sql.toString());
        ExecError execError = repError.toCommons();
        execError.setNeedFinishExecAct(getExecAct(repError.getExecActId()));
        execError.setErrorStackTrace(LobStorer.getText(repError.getErrorStackTraceId()));
        return execError;
    }

    public ExecError getLastErrorByFlowId ( long flowId ) throws RepositoryException
    {
        StringBuffer sql = new StringBuffer("select * from ieai_iexecerror where iflowid=").append(flowId);
        List list = ObjectStorerDB.findExecerror(sql.toString(), Constants.IEAI_IEAI_BASIC);
        if (list.isEmpty())
        {
            return null;
        }
        RepExecError repError = (RepExecError) list.get(0);
        ExecError execError = repError.toCommons();
        execError.setErrorStackTrace(LobStorer.getText(repError.getErrorStackTraceId()));
        execError.setNeedFinishExecAct(getExecAct(repError.getExecActId()));
        return execError;
    }

    public void saveOrUpdateLastError ( ExecError execError ) throws RepositoryException
    {
        TransactionStorerJDBC storer = new TransactionStorerJDBC();

        StringBuffer sql = new StringBuffer("select iactid, iflowid, iactivityname, iexceptionname, imessage, ioccurtime, ilocation, iscopeid, iexecactid, ierrorstacktrace from ieai_iexecerror where iscopeid=")
                .append(execError.getScopeId());
        RepExecError error = ObjectStorerDB.getExecError(sql.toString());

        if (error == null)
        {
            error = RepExecError.newInstance(execError);
            Long stId = LobStorer.saveText(execError.getErrorStackTrace());
            error.setErrorStackTraceId(stId);
            storer.saveRepExecError(error);
        } else
        {
            Long errorStackId = error.getErrorStackTraceId();
            LobStorer.updateText(errorStackId, execError.getErrorStackTrace());
            error = RepExecError.newInstance(execError);
            error.setErrorStackTraceId(errorStackId);
            String sql1 = "update ieai_iexecerror set iactid = " + error.getActId() + ", iflowid = " + error.getFlowId()
                    + ", iactivityname = '" + error.getActivityName() + "', iexceptionname = '"
                    + error.getExceptionName() + "', imessage = '" + error.getMessage() + "', ioccurTime = "
                    + error.getOccurTime() + ", ilocation = '" + error.getLocation() + "', iexecactid = "
                    + error.getExecActId() + ", ierrorstacktrace = " + errorStackId + " where iscopeid = "
                    + error.getScopeId();
            storer.update(sql1);
        }
        return;
    }

    public void saveOrUpdateLastErrorForIEAI ( ExecError execError, int dbType ) throws RepositoryException
    {
        for (int i = 0;; i++)
        {
            try
            {
                TransactionStorerJDBC storer = new TransactionStorerJDBC();
                Connection conn = null;
                PreparedStatement actStat = null;
                try
                {
                    StringBuffer sql = new StringBuffer("select iactid, iflowid, iactivityname, iexceptionname, imessage, ioccurtime, ilocation, iscopeid, iexecactid, ierrorstacktrace from ieai_iexecerror where iscopeid=")
                            .append(execError.getScopeId());
                    RepExecError error = ObjectStorerDB.getExecError(sql.toString());

                    if (error == null)
                    {
                        error = RepExecError.newInstance(execError);
                        Long stId = LobStorer.saveText(execError.getErrorStackTrace());
                        error.setErrorStackTraceId(stId);
                        storer.saveRepExecError(error);
                    } else
                    {
                        conn = DBResource.getConnection("saveOrUpdateLastErrorForIEAI", _log, dbType);
                        Long errorStackId = error.getErrorStackTraceId();
                        LobStorer.updateText(errorStackId, execError.getErrorStackTrace());
                        error = RepExecError.newInstance(execError);
                        error.setErrorStackTraceId(errorStackId);
                        String sql1 = "update ieai_iexecerror set iactid = ?, iflowid = ?, iactivityname = ?, iexceptionname = ?, imessage = ?, "
                                + "ioccurTime = ?, ilocation = ?, iexecactid = ?, ierrorstacktrace = ? where iscopeid = ? ";
                        actStat = conn.prepareStatement(sql1);
                        actStat.setLong(1, error.getActId());
                        actStat.setLong(2, error.getFlowId());
                        actStat.setString(3, error.getActivityName());
                        actStat.setString(4, error.getExceptionName());
                        actStat.setString(5, error.getMessage());
                        actStat.setLong(6, error.getOccurTime());
                        actStat.setString(7, error.getLocation());
                        actStat.setLong(8, error.getExecActId());
                        actStat.setLong(9, errorStackId);
                        actStat.setLong(10, error.getScopeId());
                        actStat.execute();
                        conn.commit();
                    }
                    return;
                } catch (RepositoryException ex)
                {
                    throw ex;
                } catch (SQLException e)
                {
                    // 失败后数据回滚
                    DBResource.rollback(conn, ServerError.ERR_DB_UPDATE, e,
                            Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                } finally
                {
                    DBResource.closePSConn(conn, actStat, "saveOrUpdateLastErrorForIEAI", _log);
                }
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
    }

    public Map getActOuput ( long scopeId, String actId ) throws RepositoryException, UnMarshallingException
    {
        DbOpScheduler dbOpScheduler = new DbOpScheduler();
        RepActOutput repOutput = dbOpScheduler.getActOutputId(scopeId, actId);

        if (null == repOutput)
        {// Here, must return null.
            return null;
        }
        byte[] bytes = LobStorer.getBinary(Constants.IEAI_IEAI_BASIC, new Long(repOutput.getActOutputId()));

        for (int i = 0; i < 10; i++)
        {
            if (null == bytes || bytes.length == 0)
            {
                try
                {
                    Thread.sleep(3000);
                } catch (InterruptedException e)
                {
                }
                bytes = LobStorer.getBinary(Constants.IEAI_IEAI_BASIC, new Long(repOutput.getActOutputId()));
            } else
            {
                break;
            }

            if (i == 9)
            {
                _log.info("select database's blob  in method getActOuput() is  null! ----BlobIID:"
                        + repOutput.getActOutputId() + " ActId=" + repOutput.getActId());
            }
        }
        return IDataMarshallerHelper.revertValueMap((Map) SerializationUtils.deserialize(bytes));
    }

    public Map getActOuput ( long flowId, long scopeId, String actId )
            throws RepositoryException, UnMarshallingException
    {
        DbOpScheduler dbOpScheduler = new DbOpScheduler();
        RepActOutput repOutput = dbOpScheduler.getActOutputId(scopeId, actId);

        if (null == repOutput)
        {// Here, must return null.
            return null;
        }
        byte[] bytes = LobStorer.getBinary(Constants.IEAI_IEAI_BASIC, new Long(repOutput.getActOutputId()));

        for (int i = 0; i < 10; i++)
        {
            if (null == bytes || bytes.length == 0)
            {
                try
                {
                    Thread.sleep(3000);
                } catch (InterruptedException e)
                {
                }
                bytes = LobStorer.getBinary(Constants.IEAI_IEAI_BASIC, new Long(repOutput.getActOutputId()));
            } else
            {
                break;
            }

            if (i == 9)
            {
                _log.info("select database's blob  in method getActOuput() is  null! ----BlobIID:"
                        + repOutput.getActOutputId() + " ActId=" + repOutput.getActId());
            }
        }
        Map outputMap = IDataMarshallerHelper.revertValueMap((Map) SerializationUtils.deserialize(bytes));
        if (outputMap.containsKey("Exceptions"))
        {
            Map excepOutputMap = ShellInfoManager.getInstance().getFinishSehllCmdStdout(scopeId, actId,
                    Constants.IEAI_IEAI);
            return excepOutputMap;
        } else
        {
            return IDataMarshallerHelper.revertValueMap((Map) SerializationUtils.deserialize(bytes));
        }
    }

    public Map getAllActOutput ( long scopeId ) throws RepositoryException, UnMarshallingException
    {
        Map map = new HashMap();
        for (int i = 0;; i++)
        {
            try
            {
                Connection con = null;
                try
                {
                    con = DBResource.getRConnection("getAllActOutput", _log, Constants.IEAI_IEAI_BASIC);

                    List list = EngineRepositotyJdbc.getInstance().getActOutput(scopeId, con);
                    for (Iterator it = list.iterator(); it.hasNext();)
                    {
                        RepActOutput repOutput = (RepActOutput) it.next();
                        byte[] bytes = LobStorer.getBinary(new Long(repOutput.getActOutputId()), con);
                        // add by tao_ding on 20090911
                        if (null == bytes || bytes.length == 0)
                        {
                            try
                            {
                                Thread.sleep(3000);
                            } catch (InterruptedException e)
                            {
                            }
                            bytes = LobStorer.getBinary(new Long(repOutput.getActOutputId()), con);
                        } else
                        {
                            break;
                        }

                        if (i == 9)
                        {
                            _log.info("select database's blob  in method getAllActOutput() is  null! ----BlobIID:"
                                    + repOutput.getActOutputId() + " ActId=" + repOutput.getActId());
                        }
                        // end
                        Map tempMap = (Map) SerializationUtils.deserialize(bytes);
                        map.put(repOutput.getActId(), IDataMarshallerHelper.revertValueMap(tempMap));
                    }
                } finally
                {
                    DBResource.closeConnection(con, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
        return map;
    }

    public void saveActOutputs ( long scopeId, Map allActOuputs ) throws RepositoryException, MarshallingException
    {
        for (Iterator it = allActOuputs.keySet().iterator(); it.hasNext();)
        {
            String key = (String) it.next();
            saveOrUpdateActOutput(scopeId, key, (Map) allActOuputs.get(key));
        }
    }

    /**
     * <AUTHOR>
     * @des:保存或修改活动的输入和输出
     * @datea:2010-1-25
     * @param scopeId
     * @param allActOuputs
     * @param con
     * @throws RepositoryException
     * @throws MarshallingException
     */
    public void saveActOutputs ( long scopeId, Map allActOuputs, Connection con )
            throws RepositoryException, MarshallingException
    {
        for (Iterator it = allActOuputs.keySet().iterator(); it.hasNext();)
        {
            String key = (String) it.next();
            saveOrUpdateActOutput(scopeId, key, (Map) allActOuputs.get(key), con);
        }
    }

    public void saveActOutputs ( long scopeId, Map allActOuputs, Connection con, int type )
            throws RepositoryException, MarshallingException
    {
        for (Iterator it = allActOuputs.keySet().iterator(); it.hasNext();)
        {
            String key = (String) it.next();
            saveOrUpdateActOutput(scopeId, key, (Map) allActOuputs.get(key), con, type);
        }
    }

    public void saveOrUpdateActOutput ( long scopeId, String actId, Map actOutput )
            throws RepositoryException, MarshallingException
    {
        for (int i = 0;; i++)
        {
            try
            {
                if (actOutput.isEmpty())
                {
                    return;
                }
                Connection con = null;
                try
                {
                    con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
                } catch (DBException e)
                {
                    _log.error(" savePendingExecActs 1720 rows " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_INIT);
                }
                try
                {
                    DbOpScheduler dbOpScheduler = new DbOpScheduler();
                    RepActOutput repOutput = dbOpScheduler.getActOutputId(scopeId, actId, con);
                    Map tempMap = IDataMarshallerHelper.translateValueMap(actOutput);
                    byte[] bytes = SerializationUtils.serialize((HashMap) tempMap);
                    if (repOutput == null)
                    {
                        Long byteId = LobStorer.saveBinaryNoCommit(bytes, con);
                        // update by tao_ding on 2009-10-12
                        dbOpScheduler.saveActOutput(actId, scopeId, byteId.longValue(), con);
                        // end update
                    } else
                    {
                        LobStorer.updateBinary(new Long(repOutput.getActOutputId()), bytes, con);
                    }
                    con.commit();
                    return;
                } catch (SQLException e)
                {
                    // 失败后数据回滚
                    DBResource.rollback(con, ServerError.ERR_DB_INSERT, e,
                            Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                } finally
                {
                    DBResource.closeConnection(con, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
    }

    public void saveActOutputs ( long scopeId, Map allActOuputs, Connection con, Connection conForId )
            throws RepositoryException, MarshallingException
    {
        for (Iterator it = allActOuputs.keySet().iterator(); it.hasNext();)
        {
            String key = (String) it.next();
            saveOrUpdateActOutput(scopeId, key, (Map) allActOuputs.get(key), con, conForId);
        }
    }

    /**
     * <AUTHOR>
     * @des:保存或修改活动的输入和输出
     * @datea:2010-1-25
     * @param scopeId
     * @param actId
     * @param actOutput
     * @param con
     * @throws RepositoryException
     * @throws MarshallingException
     */
    public void saveOrUpdateActOutput ( long scopeId, String actId, Map actOutput, Connection con )
            throws RepositoryException, MarshallingException
    {
        if (actOutput.isEmpty())
        {
            return;
        }
        DbOpScheduler dbOpScheduler = new DbOpScheduler();
        RepActOutput repOutput = dbOpScheduler.getActOutputId(scopeId, actId, con);
        Map tempMap = IDataMarshallerHelper.translateValueMap(actOutput);
        byte[] bytes = SerializationUtils.serialize((HashMap) tempMap);
        if (repOutput == null)
        {
            Long byteId = LobStorer.saveBinaryNoCommit(bytes, con);
            // update by tao_ding on 2009-10-12
            dbOpScheduler.saveActOutput(actId, scopeId, byteId.longValue(), con);
            // end update
        } else
        {
            LobStorer.updateBinary(new Long(repOutput.getActOutputId()), bytes, con);
        }
        return;
    }

    public void saveOrUpdateActOutput ( long scopeId, String actId, Map actOutput, Connection con, int type )
            throws RepositoryException, MarshallingException
    {
        if (actOutput.isEmpty())
        {
            return;
        }
        DbOpScheduler dbOpScheduler = new DbOpScheduler();
        RepActOutput repOutput = dbOpScheduler.getActOutputId(scopeId, actId, con);
        Map tempMap = IDataMarshallerHelper.translateValueMap(actOutput);
        byte[] bytes = SerializationUtils.serialize((HashMap) tempMap);
        if (repOutput == null)
        {
            Long byteId = LobStorer.saveBinaryNoCommit(bytes, con, type);
            // update by tao_ding on 2009-10-12
            dbOpScheduler.saveActOutput(actId, scopeId, byteId.longValue(), con, type);
            // end update
        } else
        {
            LobStorer.updateBinary(new Long(repOutput.getActOutputId()), bytes, con);
        }
        return;
    }

    public void saveOrUpdateActOutput ( long scopeId, String actId, Map actOutput, Connection con, Connection conForId )
            throws RepositoryException, MarshallingException
    {
        if (actOutput.isEmpty())
        {
            return;
        }
        DbOpScheduler dbOpScheduler = new DbOpScheduler();
        RepActOutput repOutput = dbOpScheduler.getActOutputId(scopeId, actId, con);
        Map tempMap = IDataMarshallerHelper.translateValueMap(actOutput);
        byte[] bytes = SerializationUtils.serialize((HashMap) tempMap);
        if (repOutput == null)
        {
            Long byteId = LobStorer.saveBinaryNoCommit(bytes, con, conForId);
            // update by tao_ding on 2009-10-12
            dbOpScheduler.saveActOutput(actId, scopeId, byteId.longValue(), con, conForId);
            // end update
        } else
        {
            LobStorer.updateBinary(new Long(repOutput.getActOutputId()), bytes, con);
        }
        return;
    }

    public void saveOrUpdateActOutputs ( long scopeId, String actId, Map actOutput, Connection con, int type )
            throws RepositoryException, MarshallingException
    {
        if (actOutput.isEmpty())
        {
            return;
        }
        DbOpScheduler dbOpScheduler = new DbOpScheduler();
        RepActOutput repOutput = dbOpScheduler.getActOutputId(scopeId, actId, con);
        Map tempMap = IDataMarshallerHelper.translateValueMap(actOutput);
        byte[] bytes = SerializationUtils.serialize((HashMap) tempMap);
        if (repOutput == null)
        {
            Long byteId = LobStorer.saveBinaryNoCommit(bytes, con, type);
            // update by tao_ding on 2009-10-12
            dbOpScheduler.saveActOutput(actId, scopeId, byteId.longValue(), con, type);
            // end update
        } else
        {
            LobStorer.updateBinary(new Long(repOutput.getActOutputId()), bytes, con);
        }
        return;
    }

    /**
     * TODO *********************** StructInfo ************************
     */

    public void saveStructInfo ( StructInfo info ) throws RepositoryException
    {
        for (int i = 0;; i++)
        {
            Connection con = null;
            try
            {
                TransactionStorerJDBC storer = new TransactionStorerJDBC();
                try
                {
                    con = DBManager.getInstance().getSchedulerConnection(Constants.IEAI_IEAI_BASIC);
                } catch (DBException e)
                {
                    _log.error(" saveStructInfo error " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_INIT);
                }
                try
                {
                    RepStructInfo repStructInfo = RepStructInfo.newInstance(info);
                    repStructInfo.setId(IdGenerator.createIdForExecAct(RepStructInfo.class, con));
                    storer.saveStructInfo(repStructInfo);
                    Map map = repStructInfo.getBranchInfos();
                    for (Iterator it = map.keySet().iterator(); it.hasNext();)
                    {
                        RepBranchInfo repBranchInfo = (RepBranchInfo) map.get(it.next());
                        repBranchInfo.setStructInfoId(repStructInfo.getId());
                        storer.saveBranchInfo(repBranchInfo);
                    }
                    return;
                } catch (RepositoryException ex)
                {
                    // 失败后数据回滚
                    DBResource.rollback(con, ServerError.ERR_DB_INSERT, ex,
                            Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                } finally
                {
                    DBResource.closeConnection(con, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
    }

    /**
     * @update tao_ding
     * @des:修改实现方式
     * @datea:2010-1-21
     * @param scopeId
     * @return
     * @throws RepositoryException
     */
    public StructInfo getUnFinishedStructInfo ( long scopeId ) throws RepositoryException
    {
        RepStructInfo repStruct = null;
        for (int i = 0;; i++)
        {
            try
            {
                Connection con = null;
                try
                {
                    con = DBResource.getRConnection("sgetUnFinishedStructInfo", _log, Constants.IEAI_IEAI_BASIC);
                    repStruct = EngineRepositotyJdbc.getInstance().getRepStructInfo(scopeId, con);

                    if (null != repStruct)
                    {
                        List list = EngineRepositotyJdbc.getInstance().getRepBranchInfo(repStruct.getId(), con);
                        Map temp = new HashMap();
                        for (Iterator it = list.iterator(); it.hasNext();)
                        {
                            RepBranchInfo repBranch = (RepBranchInfo) it.next();
                            temp.put(new Long(repBranch.getScopeId()), repBranch.toCommons());
                        }
                        repStruct.setBranchInfos(temp);
                    }
                } finally
                {
                    DBResource.closeConnection(con, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
        return repStruct == null ? null : repStruct.toCommons();
    }

    public StructInfo getUnFinishedStructInfo ( long scopeId, int type ) throws RepositoryException
    {
        RepStructInfo repStruct = null;
        for (int i = 0;; i++)
        {
            try
            {
                Connection con = null;
                try
                {
                    con = DBResource.getSchedulerConnection(Thread.currentThread().getStackTrace()[1].getMethodName(),
                            _log, type);

                    repStruct = EngineRepositotyJdbc.getInstance().getRepStructInfo(scopeId, con);

                    if (null != repStruct)
                    {
                        List list = EngineRepositotyJdbc.getInstance().getRepBranchInfo(repStruct.getId(), con);
                        Map temp = new HashMap();
                        for (Iterator it = list.iterator(); it.hasNext();)
                        {
                            RepBranchInfo repBranch = (RepBranchInfo) it.next();
                            temp.put(new Long(repBranch.getScopeId()), repBranch.toCommons());
                        }
                        repStruct.setBranchInfos(temp);
                    }
                } finally
                {
                    DBResource.closeConnection(con, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
        return repStruct == null ? null : repStruct.toCommons();
    }

    /**
     * <AUTHOR>
     * @des:修改为jdbc方式实现
     * @datea:2010-1-11
     * @param structInfo
     * @throws RepositoryException
     */
    public void updateStructInfoBasic ( StructInfo structInfo ) throws RepositoryException
    {
        for (int i = 0;; i++)
        {
            try
            {
                Connection conn = null;
                try
                {
                    try
                    {
                        conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
                    } catch (DBException e)
                    {
                        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
                        _log.error(method + " is error at EngineRepository ", e);
                        DBResource.throwRepositoryException(e, ServerError.ERR_DB_INIT);
                    }

                    RepStructInfo repStruct = RepStructInfo.newInstance(structInfo);
                    EngineRepositotyJdbc.getInstance().updateStruct(repStruct, conn);
                    Map map = repStruct.getBranchInfos();
                    for (Iterator it = map.keySet().iterator(); it.hasNext();)
                    {
                        RepBranchInfo repBranchInfo = (RepBranchInfo) map.get(it.next());
                        repBranchInfo.setStructInfoId(repStruct.getId());
                        EngineRepositotyJdbc.getInstance().updateBranch(repBranchInfo, conn);
                    }

                    conn.commit();
                } catch (SQLException e)
                {
                    // 失败后数据回滚
                    DBResource.rollback(conn, ServerError.ERR_DB_UPDATE, e,
                            Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                } finally
                {
                    DBResource.closeConnection(conn, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
                break;
            } catch (RepositoryException e)
            {
                DBRetryUtil.waitForNextTry(i, e);
            }
        }
    }

    public List getUnFinishedTimtOutStructInfos ( long flowId, int type ) throws RepositoryException
    {
        StringBuffer sql1 = new StringBuffer("select * from ieai_structinfo rsi where rsi.iflowid =").append(flowId)
                .append(" and rsi.ifinished = 0 and rsi.itimeOut = 1");

        List list = new ArrayList();
        List tempList = ObjectStorerDB.findRepStructInfo(sql1.toString(), type);

        for (Iterator it = tempList.iterator(); it.hasNext();)
        {
            RepStructInfo rep = (RepStructInfo) it.next();
            StringBuffer sql2 = new StringBuffer("select * from ieai_branchinfo rbi where rbi.istructinfoid =")
                    .append(rep.getId());
            List branchlist = ObjectStorerDB.findRepBranchInfo(sql2.toString(), type);
            Map temp = new HashMap();
            for (Iterator itbranch = branchlist.iterator(); itbranch.hasNext();)
            {
                RepBranchInfo repBranch = (RepBranchInfo) itbranch.next();
                temp.put(new Long(repBranch.getScopeId()), repBranch.toCommons());
            }
            rep.setBranchInfos(temp);
            list.add(rep.toCommons());
        }
        return list;
    }

    public StructInfo getMaxSerialNoStructInfo ( long curScope, long entryActId, int type ) throws RepositoryException
    {
        StringBuffer sql1 = new StringBuffer("select * from ieai_structinfo rsi where rsi.iscopeid =").append(curScope)
                .append(" and rsi.istructentryid =").append(entryActId).append(" order by rsi.id desc");
        List listStruct = ObjectStorerDB.findRepStructInfo(sql1.toString(), type);
        RepStructInfo structInfo = (RepStructInfo) listStruct.get(0);
        StringBuffer sql2 = new StringBuffer("select * from ieai_branchinfo rbi where rbi.istructinfoid=")
                .append(structInfo.getId());
        List list = ObjectStorerDB.findRepBranchInfo(sql2.toString(), type);
        Map temp = new HashMap();
        for (Iterator it = list.iterator(); it.hasNext();)
        {
            RepBranchInfo repBranch = (RepBranchInfo) it.next();
            temp.put(new Long(repBranch.getScopeId()), repBranch.toCommons());
        }
        structInfo.setBranchInfos(temp);
        return structInfo.toCommons();
    }

    /**
     * @update tao_ding
     * @des:修改为jdbc处理查询和更新
     * @datea:2010-1-11
     * @param branchInfoScopeId
     * @throws RepositoryException
     */
    public void updateScopeParentStructInfoUnfinished ( long branchInfoScopeId, int type ) throws RepositoryException
    {
        Connection conn = null;
        for (int i = 0;; i++)
        {
            try
            {
                try
                {
                    try
                    {
                        conn = DBManager.getInstance().getSchedulerConnection(type);
                    } catch (DBException e)
                    {
                        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
                        _log.error(method + " is error at EngineRepository ", e);
                        DBResource.throwRepositoryException(e, ServerError.ERR_DB_INIT);
                    }

                    DbOpScheduler scheduler = new DbOpScheduler();
                    RepBranchInfo branchInfo = scheduler.getBranchInfo(branchInfoScopeId, conn);

                    EngineRepositotyJdbc.getInstance()
                            .updateScopeParentStructInfoUnfinished(branchInfo.getStructInfoId(), conn);

                    conn.commit();
                } catch (SQLException e)
                {
                    // 失败后数据回滚
                    DBResource.rollback(conn, ServerError.ERR_DB_UPDATE, e,
                            Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                } finally
                {
                    DBResource.closeConnection(conn, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
                break;
            } catch (RepositoryException e)
            {
                DBRetryUtil.waitForNextTry(i, e);
            }
        }
    }

    /**
     * TODO **************** BranchInfo ********************
     */

    public void saveBranchInfo ( BranchInfo branchInfo ) throws RepositoryException
    {
        RepBranchInfo info = RepBranchInfo.newInstance(branchInfo);
        int isfinish = 0;
        int isindependent = 0;
        if (info.isFinish())
        {
            isfinish = 1;
        }
        if (info.isIndependent())
        {
            isindependent = 1;
        }
        StringBuffer sql = new StringBuffer("insert into ieai_branchinfo values(").append(info.getScopeId()).append(",")
                .append(info.getFlowId()).append(",").append(isfinish).append(",").append(info.getStructInfoId())
                .append(",").append(isindependent).append(")");
        ObjectStorerDB.save(sql.toString());
    }

    public void updateBranchInfo ( BranchInfo branchInfo ) throws RepositoryException
    {
        // update by tao_ding on 2010-1-18
        EngineRepositotyJdbc.getInstance().updateBranchInfo(branchInfo);
    }

    public void updateBranchInfo ( BranchInfo branchInfo, int type ) throws RepositoryException
    {
        // update by tao_ding on 2010-1-18
        EngineRepositotyJdbc.getInstance().updateBranchInfo(branchInfo, type);
    }

    public boolean isIndependentBranch ( long scopeId ) throws RepositoryException
    {
        boolean isIndependent = false;
        DbOpScheduler dbOpScheduler = new DbOpScheduler();
        RepBranchInfo repBranch = dbOpScheduler.getBranchInfo(scopeId);
        if (repBranch != null && repBranch.isIndependent())
        {
            isIndependent = true;
        }
        return isIndependent;
    }

    public boolean isIndependentBranch ( long scopeId, int type ) throws RepositoryException
    {
        boolean isIndependent = false;
        DbOpScheduler dbOpScheduler = new DbOpScheduler();
        RepBranchInfo repBranch = dbOpScheduler.getBranchInfo(scopeId, type);
        if (repBranch != null && repBranch.isIndependent())
        {
            isIndependent = true;
        }
        return isIndependent;
    }

    public BranchInfo getBranchInfo ( long scopeId ) throws RepositoryException
    {
        DbOpScheduler scheduler = new DbOpScheduler();
        RepBranchInfo branchInfo = scheduler.getBranchInfo(scopeId);

        if (null == branchInfo)
        {
            return null;
        }
        return branchInfo.toCommons();
    }

    public BranchInfo getBranchInfo ( long scopeId, int type ) throws RepositoryException
    {
        DbOpScheduler scheduler = new DbOpScheduler();
        RepBranchInfo branchInfo = scheduler.getBranchInfo(scopeId, type);

        if (null == branchInfo)
        {
            return null;
        }
        return branchInfo.toCommons();
    }

    /**
     * TODO ********************** ExecAct *************************
     */

    public void saveExecAct ( ExecAct newExecAct ) throws RepositoryException
    {
        for (int i = 0;; i++)
        {
            try
            {
                DbOpScheduler scheduler = new DbOpScheduler();
                Connection con = null;
                try
                {
                    con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
                } catch (DBException e)
                {
                    _log.error(" saveExecAct() 1609 rows " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_INIT);
                }
                try
                {
                    // 09-09-03 by tao_ding
                    Long id = scheduler.saveNewExecAct(RepExecAct.newInstance(newExecAct), con);
                    // end update
                    RepActStateData temp = RepActStateData.newInstance(newExecAct.getActStateData());
                    if (newExecAct.getActStateData().getBytesData() != null)
                    {
                        Long byteId = LobStorer.saveBinaryNoCommit(newExecAct.getActStateData().getBytesData(), con);
                        temp.setBytedataId(byteId.longValue());
                    }
                    temp.setIid(id.longValue());
                    scheduler.saveRepActStateData(temp, con);
                    con.commit();
                    return;
                } catch (SQLException e)
                {
                    // 失败后数据回滚
                    DBResource.rollback(con, ServerError.ERR_DB_UPDATE, e,
                            Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                } finally
                {
                    DBResource.closeConnection(con, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
    }

    public void saveExecAct ( ExecAct newExecAct, int type ) throws RepositoryException
    {
        for (int i = 0;; i++)
        {
            try
            {
                DbOpScheduler scheduler = new DbOpScheduler();
                Connection con = null;
                try
                {
                    con = DBManager.getInstance().getJdbcConnection(type);
                } catch (DBException e)
                {
                    _log.error(" saveExecAct() 1609 rows " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_INIT);
                }
                try
                {
                    // 09-09-03 by tao_ding
                    Long id = scheduler.saveNewExecAct(RepExecAct.newInstance(newExecAct), con);
                    // end update
                    RepActStateData temp = RepActStateData.newInstance(newExecAct.getActStateData());
                    if (newExecAct.getActStateData().getBytesData() != null)
                    {
                        Long byteId = LobStorer.saveBinaryNoCommit(newExecAct.getActStateData().getBytesData(), con);
                        temp.setBytedataId(byteId.longValue());
                    }
                    temp.setIid(id.longValue());
                    scheduler.saveRepActStateData(temp, con);
                    con.commit();
                    return;
                } catch (SQLException e)
                {
                    // 失败后数据回滚
                    DBResource.rollback(con, ServerError.ERR_DB_UPDATE, e,
                            Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                } finally
                {
                    DBResource.closeConnection(con, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
    }

    /**
     * <AUTHOR>
     * @des:修改为JDBC连接实现
     * @datea:Sep 3, 2009
     * @param execAct
     * @throws RepositoryException
     */
    public void updateExecActBasic ( ExecAct execAct ) throws RepositoryException
    {
        DbOpScheduler opScheduler = new DbOpScheduler();
        opScheduler.updateExecActBasic(execAct);
    }

    /**
     * <li>Description:根据execactId修改记录状态</li>
     * <AUTHOR>
     * 2017年7月1日
     * @param execActId
     * @param state
     * @throws RepositoryException
     * return void
     */
    public void updateExecActState ( long execActId, int state ) throws RepositoryException
    {
        DbOpScheduler opScheduler = new DbOpScheduler();
        opScheduler.updateExecActState(execActId, state);
    }

    /**
     * <li>Description:根据execactId修改记录状态</li>
     * <AUTHOR>
     * 2017年7月1日
     * @mes    2019-4-16 下午4:55:43  tao_ding  通过事务进行处理，增加连接参数
     * @param execActId
     * @param state
     * @throws RepositoryException
     * return void
     */
    public void updateExecActStateConn ( long execActId, int state, Connection conn ) throws RepositoryException
    {
        DbOpScheduler opScheduler = new DbOpScheduler();
        opScheduler.updateExecActStateConn(execActId, state, conn);
    }

    public void updateExecActBasic ( ExecAct execAct, int type ) throws RepositoryException
    {
        DbOpScheduler opScheduler = new DbOpScheduler();
        opScheduler.updateExecActBasic(execAct, type);
    }

    /**
     * 添加通过jdbc修改Execact表的方法
     *
     * @param execAct
     * @throws RepositoryException
     * <AUTHOR>
     */
    public void updateExecActBasicForScheduler ( ExecAct execAct ) throws RepositoryException
    {
        DbOpScheduler opScheduler = new DbOpScheduler();
        opScheduler.updateExecActBasicForScheduler(execAct);
    }

    public void updateExecActBasicForRunning ( ExecAct execAct ) throws RepositoryException
    {
        DbOpScheduler opScheduler = new DbOpScheduler();
        opScheduler.updateExecActBasicForRunning(execAct);
    }

    public boolean isException ( long flowId, String actName ) throws RepositoryException
    {
        DbOpScheduler opScheduler = new DbOpScheduler();
        return opScheduler.isException(flowId, actName);
    }

    public void updateUsertaskForScheduler ( long flowId, String actName, int sysType ) throws RepositoryException
    {
        DbOpScheduler opScheduler = new DbOpScheduler();
        opScheduler.updateUsertaskForScheduler(flowId, actName, sysType);
    }

    public void updateErrorUsertaskForScheduler ( long flowId, String actName ) throws RepositoryException
    {
        DbOpScheduler opScheduler = new DbOpScheduler();
        opScheduler.updateErrorUsertaskForScheduler(flowId, actName);
    }

    public void updateErrorUsertaskForScheduler ( long flowId, String actName, Connection con )
            throws RepositoryException
    {
        DbOpScheduler opScheduler = new DbOpScheduler();
        opScheduler.updateErrorUsertaskForScheduler(flowId, actName, con);
    }

    public void updateExecAct ( ExecAct execAct ) throws RepositoryException
    {
        for (int i = 0;; i++)
        {
            try
            {
                DbOpScheduler scheduler = new DbOpScheduler();
                Connection con = null;
                try
                {
                    con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
                } catch (DBException e)
                {
                    _log.error(" updateExecAct() 1720 rows " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_UPDATE);
                }
                try
                {
                    RepExecAct tempAct = RepExecAct.newInstance(execAct);
                    // update by tao_ding 09-09-04 用JDBC连接实现
                    RepActStateData tempActStateData = new RepActStateData();
                    // end update

                    tempActStateData = RepActStateData.newInstance(execAct.getActStateData());
                    if (tempActStateData.getBytedataId() != -1)
                    {
                        LobStorer.updateBinary(new Long(tempActStateData.getBytedataId()),
                                execAct.getActStateData().getBytesData(), con);
                    } else
                    {
                        if (execAct.getActStateData().getBytesData() != null)
                        {
                            Long bytesDataId = LobStorer.saveBinaryNoCommit(execAct.getActStateData().getBytesData(),
                                    con, Constants.IEAI_IEAI_BASIC);
                            tempActStateData.setBytedataId(bytesDataId.longValue());
                        }
                    }
                    tempActStateData.setIid(execAct.getSerialNo());
                    // update by tao_ding 09-09-04 用JDBC连接实现
                    scheduler.updateRepActStateData(tempActStateData, execAct.getSerialNo(), con);
                    scheduler.updateExecAct(tempAct, execAct.getSerialNo(), con);
                    con.commit();
                    // end update
                    return;
                } catch (SQLException e)
                {
                    // 失败后数据回滚
                    DBResource.rollback(con, ServerError.ERR_DB_UPDATE, e,
                            Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                } finally
                {
                    DBResource.closeConnection(con, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
    }

    /**
     *
     * @Title: updateExecActStates
     * @Description: TODO(增肌更新actstate的方法)
     * @param: @param execAct
     * @param: @throws RepositoryException
     * @return: void
     * @throws
     * @author: xibin_gong
     * @date:   2017年12月12日 下午1:32:47
     */
    public void updateExecActStates ( ExecAct execAct ) throws RepositoryException
    {
        for (int i = 0;; i++)
        {
            try
            {
                DbOpScheduler scheduler = new DbOpScheduler();
                Connection con = null;
                try
                {
                    con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
                } catch (DBException e)
                {
                    _log.error(" updateExecActStates() " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_UPDATE);
                }
                try
                {
                    RepExecAct tempAct = RepExecAct.newInstance(execAct);
                    // update by tao_ding 09-09-04 用JDBC连接实现
                    RepActStateData tempActStateData = new RepActStateData();
                    // end update

                    tempActStateData = RepActStateData.newInstance(execAct.getActStateData());
                    if (tempActStateData.getBytedataId() != -1)
                    {
                        LobStorer.updateBinary(new Long(tempActStateData.getBytedataId()),
                                execAct.getActStateData().getBytesData(), con);
                    } else
                    {
                        if (execAct.getActStateData().getBytesData() != null)
                        {
                            Long bytesDataId = LobStorer.saveBinaryNoCommit(execAct.getActStateData().getBytesData(),
                                    con);
                            tempActStateData.setBytedataId(bytesDataId.longValue());
                        }
                    }
                    tempActStateData.setIid(execAct.getSerialNo());
                    // update by tao_ding 09-09-04 用JDBC连接实现
                    scheduler.updateRepActStateData(tempActStateData, execAct.getSerialNo(), con);
                    // scheduler.updateExecAct(tempAct, execAct.getSerialNo(), con);
                    con.commit();
                    // end update
                    return;
                } catch (SQLException e)
                {
                    DBResource.rollback(con, ServerError.ERR_DB_UPDATE, e,
                            Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                } finally
                {
                    DBResource.closeConnection(con, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
    }

    public void updateExecAct ( ExecAct execAct, int type ) throws RepositoryException
    {
        for (int i = 0;; i++)
        {
            try
            {
                DbOpScheduler scheduler = new DbOpScheduler();
                Connection con = null;
                try
                {
                    con = DBManager.getInstance().getJdbcConnection(type);
                } catch (DBException e)
                {
                    _log.error(" updateExecAct() 1720 rows " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_UPDATE);
                }
                try
                {
                    RepExecAct tempAct = RepExecAct.newInstance(execAct);
                    // update by tao_ding 09-09-04 用JDBC连接实现
                    RepActStateData tempActStateData = new RepActStateData();
                    // end update

                    tempActStateData = RepActStateData.newInstance(execAct.getActStateData());
                    if (tempActStateData.getBytedataId() != -1)
                    {
                        LobStorer.updateBinary(new Long(tempActStateData.getBytedataId()),
                                execAct.getActStateData().getBytesData(), con);
                    } else
                    {
                        if (execAct.getActStateData().getBytesData() != null)
                        {
                            Long bytesDataId = LobStorer.saveBinaryNoCommit(execAct.getActStateData().getBytesData(),
                                    con);
                            tempActStateData.setBytedataId(bytesDataId.longValue());
                        }
                    }
                    tempActStateData.setIid(execAct.getSerialNo());
                    // update by tao_ding 09-09-04 用JDBC连接实现
                    scheduler.updateRepActStateData(tempActStateData, execAct.getSerialNo(), con);
                    scheduler.updateExecAct(tempAct, execAct.getSerialNo(), con);
                    con.commit();
                    // end update
                    return;
                } catch (SQLException e)
                {
                    DBResource.rollback(con, ServerError.ERR_DB_UPDATE, e,
                            Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                } finally
                {
                    DBResource.closeConnection(con, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
    }

    /**
     * <AUTHOR>
     * @des:事务处理修改活动信息
     * @datea:2010-3-26
     * @param execAct
     * @throws RepositoryException
     */
    public void updateExecAct ( ExecAct execAct, Connection con ) throws RepositoryException
    {
        DbOpScheduler scheduler = new DbOpScheduler();

        RepExecAct tempAct = RepExecAct.newInstance(execAct);
        // update by tao_ding 09-09-04 用JDBC连接实现
        RepActStateData tempActStateData = new RepActStateData();
        // end update

        tempActStateData = RepActStateData.newInstance(execAct.getActStateData());
        if (tempActStateData.getBytedataId() != -1)
        {
            LobStorer.updateBinary(new Long(tempActStateData.getBytedataId()), execAct.getActStateData().getBytesData(),
                    con);
        } else
        {
            if (execAct.getActStateData().getBytesData() != null)
            {
                Long bytesDataId = LobStorer.saveBinaryNoCommit(execAct.getActStateData().getBytesData(), con);
                tempActStateData.setBytedataId(bytesDataId.longValue());
            }
        }
        tempActStateData.setIid(execAct.getSerialNo());
        // update by tao_ding 09-09-04 用JDBC连接实现
        scheduler.updateRepActStateData(tempActStateData, execAct.getSerialNo(), con);
        scheduler.updateExecAct(tempAct, execAct.getSerialNo(), con);
        // end update
        return;
    }

    public void updateExecActForIp ( ExecAct execAct ) throws RepositoryException
    {
        for (int i = 0;; i++)
        {
            try
            {
                DbOpScheduler scheduler = new DbOpScheduler();
                Connection con = null;
                String serverIp = "";
                try
                {
                    serverIp = Environment.getInstance().getServerIP();
                } catch (UnknownHostException e1)
                {
                    e1.printStackTrace();
                }
                try
                {
                    con = DBManager.getInstance().getSchedulerConnection(Constants.IEAI_IEAI_BASIC);
                } catch (DBException e)
                {
                    _log.error(" updateExecActForIp() 1976 rows " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_UPDATE);
                }
                try
                {
                    RepExecAct tempAct = RepExecAct.newInstance(execAct);
                    // update by tao_ding 09-09-04 用JDBC连接实现
                    RepActStateData tempActStateData = new RepActStateData();
                    // end update

                    tempActStateData = RepActStateData.newInstance(execAct.getActStateData());
                    if (tempActStateData.getBytedataId() != -1)
                    {
                        LobStorer.updateBinary(new Long(tempActStateData.getBytedataId()),
                                execAct.getActStateData().getBytesData(), con);
                    } else
                    {
                        if (execAct.getActStateData().getBytesData() != null)
                        {
                            Long bytesDataId = LobStorer.saveBinaryNoCommit(execAct.getActStateData().getBytesData(),
                                    con);
                            tempActStateData.setBytedataId(bytesDataId.longValue());
                        }
                    }
                    tempActStateData.setIid(execAct.getSerialNo());
                    // update by xibin_gong 10-04-07 用serverip进行关联更改
                    scheduler.updateRepActStateDataForIp(tempActStateData, execAct.getSerialNo(), con, serverIp);
                    scheduler.updateExecActForIp(tempAct, execAct.getSerialNo(), con, serverIp);
                    con.commit();
                    // end update
                    return;
                } catch (SQLException e)
                {
                    DBResource.rollback(con, ServerError.ERR_DB_UPDATE, e,
                            Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                } finally
                {
                    DBResource.closeConnection(con, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
    }

    public void updateExecActForIp ( ExecAct execAct, Connection con ) throws RepositoryException
    {
        DbOpScheduler scheduler = new DbOpScheduler();
        String serverIp = "";
        try
        {
            serverIp = Environment.getInstance().getServerIP();
        } catch (UnknownHostException e1)
        {
            e1.printStackTrace();
        }
        RepExecAct tempAct = RepExecAct.newInstance(execAct);
        // update by tao_ding 09-09-04 用JDBC连接实现
        RepActStateData tempActStateData = new RepActStateData();
        // end update

        tempActStateData = RepActStateData.newInstance(execAct.getActStateData());
        if (tempActStateData.getBytedataId() != -1)
        {
            LobStorer.updateBinary(new Long(tempActStateData.getBytedataId()), execAct.getActStateData().getBytesData(),
                    con);
        } else
        {
            if (execAct.getActStateData().getBytesData() != null)
            {
                Long bytesDataId = LobStorer.saveBinaryNoCommit(execAct.getActStateData().getBytesData(), con);
                tempActStateData.setBytedataId(bytesDataId.longValue());
            }
        }
        tempActStateData.setIid(execAct.getSerialNo());
        // update by xibin_gong 10-04-07 用serverip进行关联更改
        scheduler.updateRepActStateDataForIp(tempActStateData, execAct.getSerialNo(), con, serverIp);
        scheduler.updateExecActForIp(tempAct, execAct.getSerialNo(), con, serverIp);
        // end update
        return;
    }

    public void updateExecActCallflowActForIp ( ExecAct execAct ) throws RepositoryException
    {
        for (int i = 0;; i++)
        {
            try
            {
                DbOpScheduler scheduler = new DbOpScheduler();
                Connection con = null;
                String serverIp = "";
                try
                {
                    serverIp = Environment.getInstance().getServerIP();
                } catch (UnknownHostException e1)
                {
                    e1.printStackTrace();
                }
                try
                {
                    con = DBManager.getInstance().getSchedulerConnection(Constants.IEAI_IEAI_BASIC);
                } catch (DBException e)
                {
                    _log.error(" updateExecActCallflowActForIp() 1976 rows " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_UPDATE);
                }
                try
                {
                    RepExecAct tempAct = RepExecAct.newInstance(execAct);
                    // update by tao_ding 09-09-04 用JDBC连接实现
                    RepActStateData tempActStateData = new RepActStateData();
                    // end update

                    tempActStateData = RepActStateData.newInstance(execAct.getActStateData());
                    if (tempActStateData.getBytedataId() != -1)
                    {
                        LobStorer.updateBinary(new Long(tempActStateData.getBytedataId()),
                                execAct.getActStateData().getBytesData(), con);
                    } else
                    {
                        if (execAct.getActStateData().getBytesData() != null)
                        {
                            Long bytesDataId = LobStorer.saveBinaryNoCommit(execAct.getActStateData().getBytesData(),
                                    con);
                            tempActStateData.setBytedataId(bytesDataId.longValue());
                        }
                    }
                    tempActStateData.setIid(execAct.getSerialNo());
                    // update by xibin_gong 10-04-07 用serverip进行关联更改
                    scheduler.updateRepActStateDataForIp(tempActStateData, execAct.getSerialNo(), con, serverIp);
                    con.commit();
                    // end update
                    return;
                } catch (SQLException e)
                {
                    DBResource.rollback(con, ServerError.ERR_DB_UPDATE, e,
                            Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                } finally
                {
                    DBResource.closeConnection(con, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
    }

    public void savePendingExecActs ( List execActs, ExecAct oldExecAct ) throws RepositoryException
    {
        for (int i = 0;; i++)
        {
            try
            {
                DbOpScheduler scheduler = new DbOpScheduler();
                Connection con = null;
                try
                {
                    con = DBManager.getInstance().getSchedulerConnection(Constants.IEAI_IEAI_BASIC);
                } catch (DBException e)
                {
                    _log.error(" savePendingExecActs() 1799 rows " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_INIT);
                }
                try
                {
                    if (oldExecAct != null)
                    {
                        oldExecAct.setState(ExecAct.FINISH);
                        scheduler.updateExecActBasicForScheduler(oldExecAct);
                    }
                    for (Iterator it = execActs.iterator(); it.hasNext();)
                    {
                        ExecAct newExecAct = (ExecAct) it.next();
                        // 修改用JDBC连接获得 tao_ding on 2009-09-02
                        Long id = scheduler.saveNewExecAct(RepExecAct.newInstance(newExecAct), con);
                        // end update
                        RepActStateData temp = RepActStateData.newInstance(newExecAct.getActStateData());
                        if (newExecAct.getActStateData().getBytesData() != null)
                        {
                            // //修改用JDBC连接获得 tao_ding on 2009-09-03
                            Long byteId = LobStorer.saveBinaryNoCommit(newExecAct.getActStateData().getBytesData(),
                                    con);
                            // end update
                            temp.setBytedataId(byteId.longValue());
                        }
                        temp.setIid(id.longValue());
                        // 修改保存为JDBC连接 tao_ding on 2009-09-02
                        scheduler.saveRepActStateData(temp, con);
                        // end update
                    }
                    con.commit();
                    return;
                } catch (SQLException e)
                {
                    DBResource.rollback(con, ServerError.ERR_DB_UPDATE, e,
                            Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                } finally
                {
                    DBResource.closeConnection(con, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
    }

    /**
     * Get give number of PENDING activities after serialNo from database
     *
     * @param serialNo
     * @param num
     * @return List of ExecAct objects
     * @throws RepositoryException
     */
    public List getPendingExecActs ( long serialNo, int num ) throws RepositoryException
    {

        for (int i = 0;; i++)
        {
            List actList = new ArrayList();
            try
            {
                DbOpScheduler scheduler = new DbOpScheduler();
                Connection con = null;
                try
                {
                    con = DBManager.getInstance().getSchedulerConnection(Constants.IEAI_IEAI_BASIC);

                    List list = null;
                    if (JudgeDB.IEAI_DB_TYPE == 3)
                    {
                        list = scheduler.queryPendingExecActsFormysql(con, serialNo, num);
                    } else
                    {
                        list = scheduler.queryPendingExecActs(con, serialNo, num);
                    }

                    actList.addAll(list);
                    List full = getFullExecActsForScheduler(actList, con);
                    con.commit();
                    return full;

                } catch (DBException e)
                {
                    _log.error(" getPendingExecActs() get  connect : " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_INIT);
                } catch (SQLException e)
                {
                    String method = Thread.currentThread().getStackTrace()[1].getMethodName();
                    _log.error(method + " is error at EngineRepository ", e);
                    DBResource.rollback(con, Constants.IEAI_IEAI_BASIC, e, "getPendingExecActs", _log);
                    DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
                }catch(RepositoryException e) {
                    DBResource.rollback(con, Constants.IEAI_IEAI_BASIC, e, "getPendingExecActs", _log);
                    DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConnection(con, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }

    }

    public List getPendingExecActs ( long serialNo, int num, Connection conn ) throws RepositoryException
    {
        // 09-06-02 by kaijia_ma
        DbOpScheduler opScheduler = new DbOpScheduler();
        List list = opScheduler.queryPendingExecActs(serialNo, num, conn);
        // actList.addAll(list);
        // List full = getFullExecActsForScheduler(actList);
        return list;
    }

    public ExecAct getPendingOrRunningExecAct ( long scopeId ) throws RepositoryException
    {
        RepExecAct repExecAct = null;

        // 09-09-03 by tao_ding
        DbOpScheduler opScheduler = new DbOpScheduler();
        List ret = opScheduler.getPendingOrRunningExecAct(scopeId);

        if (!ret.isEmpty())
        {
            repExecAct = (RepExecAct) ret.get(0);
        }
        return getFullExecAct(repExecAct);
    }

    public ExecAct getPendingOrRunningExecAct ( long scopeId, int type ) throws RepositoryException
    {
        RepExecAct repExecAct = null;

        // 09-09-03 by tao_ding
        DbOpScheduler opScheduler = new DbOpScheduler();
        List ret = opScheduler.getPendingOrRunningExecAct(scopeId, type);

        if (!ret.isEmpty())
        {
            repExecAct = (RepExecAct) ret.get(0);
        }
        return getFullExecAct(repExecAct);
    }

    public List getPendingOrRunningExecActs ( long flowId, int type ) throws RepositoryException
    {
        // 09-09-03 by tao_ding
        DbOpScheduler opScheduler = new DbOpScheduler();
        List list = opScheduler.getPendingOrRunningExecActs(flowId, type);
        return getFullExecActs(list);
    }

    public ExecAct getPendingOrRunningExecActsByActid ( long flowId, long actId, int type ) throws RepositoryException
    {
        RepExecAct repExecAct = null;
        DbOpScheduler opScheduler = new DbOpScheduler();
        repExecAct = opScheduler.getPendingOrRunningExecActsByActid(flowId, actId, type);
        return getFullExecAct(repExecAct);
    }

    public List getExceptionExecActs ( long flowId, int type ) throws RepositoryException
    {
        // 09-09-03 by tao_ding
        DbOpScheduler opScheduler = new DbOpScheduler();
        List list = opScheduler.getExceptionExecActs(flowId, type);

        return getFullExecActs(list);
    }

    public ExecAct getMaxSerialNoExecAct ( long scopeId, long actId ) throws RepositoryException
    {
        // 09-09-03 by tao_ding
        DbOpScheduler opScheduler = new DbOpScheduler();
        List list = opScheduler.getMaxSerialNoExecAct(scopeId, actId);

        RepExecAct execAct = (RepExecAct) list.get(0);
        return getFullExecAct(execAct);
    }

    public ExecAct getMaxSerialNoExecAct ( long scopeId, long actId, int type ) throws RepositoryException
    {
        // 09-09-03 by tao_ding
        DbOpScheduler opScheduler = new DbOpScheduler();
        List list = opScheduler.getMaxSerialNoExecAct(scopeId, actId, type);

        RepExecAct execAct = (RepExecAct) list.get(0);
        return getFullExecAct(execAct);
    }

    public ExecAct getExecAct ( long serialNo ) throws RepositoryException
    {
        // 09-09-03 by tao_ding
        DbOpScheduler opScheduler = new DbOpScheduler();
        RepExecAct execAct = (RepExecAct) opScheduler.getExecAct(serialNo);

        return getFullExecAct(execAct);
    }

    public ExecAct getExecAct ( long serialNo, int type ) throws RepositoryException
    {
        // 09-09-03 by tao_ding
        DbOpScheduler opScheduler = new DbOpScheduler();
        RepExecAct execAct = (RepExecAct) opScheduler.getExecAct(serialNo, type);

        return getFullExecAct(execAct);
    }

    public int getExecActFlag ( String requestId, Connection con ) throws RepositoryException
    {
        DbOpScheduler opScheduler = new DbOpScheduler();
        int flag = opScheduler.getExecActFlag(requestId, con);
        return flag;
    }

    public List getExecActSerialNoMoreThan ( long flowId, long minExecActSerialNo ) throws RepositoryException
    {
        // 09-09-03 by tao_ding
        DbOpScheduler opScheduler = new DbOpScheduler();
        List list = opScheduler.getExecActSerialNoMoreThan(flowId, minExecActSerialNo);

        return getFullExecActs(list);
    }

    public List getMaxSerialNoFinishedExecActInSameScope ( long flowId, long structEntryId, int type )
            throws RepositoryException
    {
        List actList = new ArrayList();
        for (int i = 0;; i++)
        {
            try
            {
                Connection con = null;

                try
                {
                    con = DBResource.getRConnection("getMaxSerialNoFinishedExecActInSameScope", _log, type);
                    List list = EngineRepositotyJdbc.getInstance().getStructInfo(flowId, structEntryId, con);
                    for (Iterator it = list.iterator(); it.hasNext();)
                    {
                        long actScopeId = ((RepStructInfo) it.next()).getScopeId();
                        List temp = EngineRepositotyJdbc.getInstance().getExecAct(actScopeId, con);
                        if (!temp.isEmpty())
                        {
                            actList.add(temp.get(0));
                        }
                    }

                } catch (Exception e)
                {
                    String method = Thread.currentThread().getStackTrace()[1].getMethodName();
                    _log.error(method + " is error at EngineRepository ", e);
                    DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConnection(con, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
        return actList;
    }

    /**
     * Convert list of RepExecAct to ExacAct
     *
     * @param list
     * @return
     * @throws RepositoryException
     */
    private List getFullExecActs ( List list ) throws RepositoryException
    {
        List actList = new ArrayList();
        for (Iterator it = list.iterator(); it.hasNext();)
        {
            RepExecAct repExecAct = (RepExecAct) it.next();
            actList.add(getFullExecAct(repExecAct));
        }
        return actList;
    }

    private List getFullExecActsForScheduler ( List list, Connection conn ) throws RepositoryException
    {
        List actList = new ArrayList();
        for (Iterator it = list.iterator(); it.hasNext();)
        {
            RepExecAct repExecAct = (RepExecAct) it.next();
            actList.add(getFullExecActForScheduler(repExecAct, conn));
        }
        return actList;
    }

    private List getFullExecActsForScheduler ( int sysType, List list ) throws RepositoryException
    {
        List actList = new ArrayList();
        for (Iterator it = list.iterator(); it.hasNext();)
        {
            RepExecAct repExecAct = (RepExecAct) it.next();
            actList.add(getFullExecActForScheduler(sysType, repExecAct));
        }
        return actList;
    }

    private ExecAct getFullExecActForScheduler ( int sysType, RepExecAct repExecAct ) throws RepositoryException
    {
        if (null == repExecAct)
        {
            return null;
        }
        DbOpScheduler opScheduler = new DbOpScheduler();
        RepActStateData repStateData = opScheduler.getRepActStateData(sysType, repExecAct.getIid());
        byte[] bytes = null;
        ActStateData stateData = null;
        if (repStateData != null)
        {
            if (repStateData.getBytedataId() != -1)
            {
                bytes = LobStorer.getBinary(sysType, new Long(repStateData.getBytedataId()));
            }
            stateData = repStateData.toCommons();
            stateData.setBytesData(bytes);
        }
        ExecAct execAct = repExecAct.toCommons();
        execAct.setActStateData(stateData);
        return execAct;
    }

    public ExecAct getFullExecActForScheduler ( RepExecAct repExecAct, Connection conn ) throws RepositoryException
    {
        if (null == repExecAct)
        {
            return null;
        }
        DbOpScheduler opScheduler = new DbOpScheduler();
        RepActStateData repStateData = opScheduler.getRepActStateData(repExecAct.getIid(), conn);
        byte[] bytes = null;
        ActStateData stateData = null;
        if (repStateData != null)
        {
            if (repStateData.getBytedataId() != -1)
            {
                bytes = LobStorer.getBinary(new Long(repStateData.getBytedataId()), conn);
            }
            stateData = repStateData.toCommons();
            stateData.setBytesData(bytes);
        }
        ExecAct execAct = repExecAct.toCommons();
        execAct.setActStateData(stateData);
        return execAct;
    }

    private ExecAct getFullExecAct ( RepExecAct repExecAct ) throws RepositoryException
    {
        if (null == repExecAct)
        {
            return null;
        }
        DbOpScheduler scheduler = new DbOpScheduler();
        RepActStateData repStateData = (RepActStateData) scheduler.getRepActStateData(repExecAct.getIid());
        byte[] bytes = null;
        ActStateData stateData = null;
        if (repStateData != null)
        {
            if (repStateData.getBytedataId() != -1)
            {
                bytes = LobStorer.getBinary(Constants.IEAI_IEAI_BASIC, new Long(repStateData.getBytedataId()));
            }
            stateData = repStateData.toCommons();
            stateData.setBytesData(bytes);
        }
        ExecAct execAct = repExecAct.toCommons();
        execAct.setActStateData(stateData);
        return execAct;
    }

    /**
     * TODO ********************* RecoveryPoint ***********************
     */

    // 判断该恢复的工作流是否是软删除的工作流 update by tao_ding on 2009-03-18
    public boolean hasDelRecoveryPoint ( long flowId, int type ) throws RepositoryException
    {
        TransactionStorerJDBC storer = new TransactionStorerJDBC();
        boolean has = false;
        List list = storer.queryRepRecoveryPoint(
                "select * from ieai_recoverypoint rrp where rrp.flowId = " + flowId + " and rrp.ideleteflag = 0", type);
        if (list.size() > 0)
        {
            has = true;
        }
        return has;
    }

    // 对要进行的软删除的工作流进行软删除 update by tao_ding on 2009-03-18
    public void delRecoveryPoints ( long flowId, int type ) throws RepositoryException
    {
        try
        {
            TransactionStorerJDBC storer = new TransactionStorerJDBC();
            try
            {
                String sql = "select * from ieai_recoverypoint t where t.iflowid = " + flowId;
                String sqlUpdate = "";
                List delPoint = storer.queryRepRecoveryPoint(sql, type);
                for (Iterator iter = delPoint.iterator(); iter.hasNext();)
                {
                    RepRecoveryPoint delRecoverPoint = (RepRecoveryPoint) iter.next();
                    delRecoverPoint.setIdeleteflag(1);
                    sqlUpdate = "update ieai_recoverypoint t set t.ideleteflag = 1 where t.iid = "
                            + delRecoverPoint.getSerialNo();
                    storer.update(sqlUpdate, type);
                }
                return;
            } catch (RepositoryException ex)
            {
                throw ex;
            }
        } catch (RepositoryException ex)
        {
            _log.error("delRecoveryPoints error !", ex);
        }

    }

    public List getRecoveryPoints ( long flowId, int type ) throws RepositoryException
    {
        TransactionStorerJDBC storer = new TransactionStorerJDBC();
        StringBuffer sql = new StringBuffer("select * from ieai_recoverypoint rrp where rrp.iflowid =").append(flowId);
        List list = storer.queryRepRecoveryPoint(sql.toString(), type);
        return list;
    }

    public RecoveryPoint getRecoveryPoint ( long serialNo ) throws RepositoryException
    {
        TransactionStorerJDBC storer = new TransactionStorerJDBC();
        StringBuffer sql = new StringBuffer("select * from ieai_recoverypoint where iid=").append(serialNo);
        RepRecoveryPoint recoveryPoint = storer.getRepRecoveryPoint(sql.toString());
        return recoveryPoint.toCommonts();
    }

    public RecoveryPoint getRecoveryPoint ( long flowId, long scopeId, long actId ) throws RepositoryException
    {
        // update by tao_ding on 2009-05-15 对需要恢复的工作流查询时有相同的记录则获得最新的记录
        // Object[] values = { new Long(flowId), new Long(scopeId), new
        // Integer(actId) };
        // String[] fields = { "flowId", "scopeId", "actId" };
        // Type[] types = { Hibernate.LONG, Hibernate.LONG, Hibernate.INTEGER };
        // RepRecoveryPoint recoveryPoint = (RepRecoveryPoint) ObjectStorer.(
        // RepRecoveryPoint.class, values, fields, types);

        TransactionStorerJDBC storer = new TransactionStorerJDBC();

        String hql = "select * from ieai_recoverypoint rep where rep.iflowid=" + flowId + " and rep.iactid=" + actId
                + " and rep.iscopeid=" + scopeId + " order by rep.iid desc";
        List recoveryPoint = storer.queryRepRecoveryPoint(hql, Constants.IEAI_IEAI_BASIC);
        RecoveryPoint RecoveryPoint = new RecoveryPoint();
        if (recoveryPoint.size() > 0)
        {
            RecoveryPoint = (RecoveryPoint) recoveryPoint.get(0);
        }
        // end update
        return RecoveryPoint;
    }

    public RecoveryPoint getRecoveryPoint ( long flowId, long scopeId, long actId, int dbType )
            throws RepositoryException
    {
        // update by tao_ding on 2009-05-15 对需要恢复的工作流查询时有相同的记录则获得最新的记录
        // Object[] values = { new Long(flowId), new Long(scopeId), new
        // Integer(actId) };
        // String[] fields = { "flowId", "scopeId", "actId" };
        // Type[] types = { Hibernate.LONG, Hibernate.LONG, Hibernate.INTEGER };
        // RepRecoveryPoint recoveryPoint = (RepRecoveryPoint) ObjectStorer.(
        // RepRecoveryPoint.class, values, fields, types);

        TransactionStorerJDBC storer = new TransactionStorerJDBC();

        String hql = "select * from ieai_recoverypoint rep where rep.iflowid=" + flowId + " and rep.iactid=" + actId
                + " and rep.iscopeid=" + scopeId + " order by rep.iid desc";
        List recoveryPoint = storer.queryRepRecoveryPoint(hql, dbType);
        RecoveryPoint RecoveryPoint = new RecoveryPoint();
        if (recoveryPoint.size() > 0)
        {
            RecoveryPoint = (RecoveryPoint) recoveryPoint.get(0);
        }
        // end update
        return RecoveryPoint;
    }

    /**
     * <AUTHOR>
     * @des:修改为jdbc处理
     * @datea:2010-1-18
     * @param recoverPolicies
     * @throws RepositoryException
     */
    public void setRecoveryPolicy ( List recoverPolicies ) throws RepositoryException
    {
        String sql = "update ieai_recoverypoint tt set tt.irecoverypolicy = ? where tt.iid = ? ";
        for (int i = 0;; i++)
        {
            try
            {
                Connection con = DBResource.getConnection("setRecoveryPolicy", _log, Constants.IEAI_IEAI_BASIC);
                PreparedStatement ps = null;
                try
                {
                    for (Iterator it = recoverPolicies.iterator(); it.hasNext();)
                    {
                        RecoveryPolicyConfig policieConfig = (RecoveryPolicyConfig) it.next();

                        ps = con.prepareStatement(sql);
                        ps.setInt(1, policieConfig.getRecoverPolicy());
                        ps.setLong(2, policieConfig.getSerialNo());

                        ps.executeUpdate();
                        ps = null;
                    }
                    con.commit();
                    return;
                } catch (SQLException e)
                {
                    DBResource.rollback(con, ServerError.ERR_DB_UPDATE, e, "setRecoveryPolicy", _log);
                } finally
                {
                    DBResource.closePSConn(con, ps, "setRecoveryPolicy", _log);
                }
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ex, ServerError.ERR_DB_UPDATE);
            }
        }
    }

    public void saveRecoveryPoints ( List finallyRecoveryPoints, int type ) throws RepositoryException
    {
        for (int i = 0;; i++)
        {
            try
            {
                TransactionStorerJDBC storer = new TransactionStorerJDBC();
                try
                {
                    for (Iterator it = finallyRecoveryPoints.iterator(); it.hasNext();)
                    {
                        storer.saveRecoveryPoint(RepRecoveryPoint.newInstance((RecoveryPoint) it.next()), type);
                    }
                    return;
                } catch (RepositoryException ex)
                {
                    throw ex;
                }
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
    }

    public void clearRecoveryPoints ( long flowId ) throws RepositoryException
    {
        ObjectStorerDB.remove("delete from ieai_recoverypoint where iflowid=" + flowId);
    }

    public void clearRecoveryPoints ( long flowId, int type ) throws RepositoryException
    {
        ObjectStorerDB.remove("delete from ieai_recoverypoint where iflowid=" + flowId, type);
    }

    public List getAllRecoveryActIds ( List data, int sysType ) throws RepositoryException
    {
        List ids = new ArrayList();
        for (Iterator it = data.iterator(); it.hasNext();)
        {
            TaskData taskData = (TaskData) it.next();
            // 修改为jdbc方式实现 tao_ding on 2010-1-12
            long actId = EngineRepositotyJdbc.getInstance().getAllRecoveryActIds(taskData.getTaskName(),
                    taskData.getFlowId().longValue(), sysType);
            ids.add(new Long(actId));
        }
        return ids;
    }

    /**
     * TODO *************** ServerRunningInfo *************************
     */

    public long saveServerRunningInfo ( ServerRunningInfo serverRunningInfo ) throws RepositoryException
    {
        RepServerRunningInfo runninginfo = RepServerRunningInfo.newInstance(serverRunningInfo);
        runninginfo.setSerialNo(IdGenerator.createIdFor(RepServerRunningInfo.class));
        StringBuffer sql = new StringBuffer("insert into ieai_serverrunninginfo values(")
                .append(runninginfo.getSerialNo()).append(",").append(runninginfo.getCurrentTime()).append(",")
                .append(runninginfo.getServerStartTime()).append(",").append(runninginfo.getGroupId()).append(")");
        ObjectStorerDB.save(sql.toString());
        return runninginfo.getSerialNo();
    }

    /**
     * <AUTHOR>
     * @des:修改server自检时间更新
     * @datea:2010-1-11
     * @param id
     * @param date
     * @throws RepositoryException
     */
    public void updateServerRunningInfoCurTime ( long id, Date date ) throws RepositoryException
    {
        EngineRepositotyJdbc.getInstance().updateServerRunningInfoCurTime(id, date);
    }

    /**
     * TODO ******************* ExecRequest ***************************
     */

    public List getRexecRequestsOfFlow ( long flowId ) throws RepositoryException, UnMarshallingException
    {
        TransactionStorerJDBC storer = new TransactionStorerJDBC();
        List list = storer.queryRemoteExecAct("select * from ieai_remoteexecact rrea where rrea.iflowId =" + flowId);
        return list;
    }

    public void removeFlowAllRexecRequest ( long flowId ) throws RepositoryException
    {
        try
        {
            DbOpScheduler opScheduler = new DbOpScheduler();
            List list = opScheduler.getExecActIdList(flowId);
            for (Iterator it = list.iterator(); it.hasNext();)
            {
                String repExecActId = (String) it.next();
                if (null != repExecActId)
                    removeRExecRequest(repExecActId);
            }
            return;
        } catch (RepositoryException ex)
        {
            throw ex;
        }

    }

    public long resultForCM ( String requestId, String output ) throws RepositoryException
    {
        long startTime = System.currentTimeMillis();
        long ret = 0;
        for (int i = 0;; i++)
        {
            try
            {
                Connection con = null;
                try
                {
                    con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
                    ret = LobStorer.saveBlobResult(output.getBytes(), con);
                    _log.info("Act " + requestId + " ,update result successfully!");
                } catch (Exception e)
                {
                    // 失败后数据回滚
                    DBResource.rollback(con, ServerError.ERR_DB_INSERT, e,
                            Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                } finally
                {
                    DBResource.closeConnection(con, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
        long endTime = System.currentTimeMillis();
        long interval = endTime - startTime;
        if (SystemConfig.isHibernateTime())
        {
            _log.info("Stamp:EngineRepository.resultForCM:" + interval);
        }
        return ret;
    }

    public RemoteActivityExecRequest getRexecRequest ( String requestId, int type )
            throws RepositoryException, UnMarshallingException
    {
        long startTime = System.currentTimeMillis();
        // synchronized (requestId)
        // {
        // update begin by yan_wang 2009 9.28
        for (int i = 0;; i++)
        {
            RepRemoteExecAct repAct = null;
            try
            {
                Connection con = null;
                PreparedStatement ps = null;
                ResultSet rs = null;
                try
                {
                    con = DBManager.getInstance().getJdbcConnection(type);

                    String sql = "select repremotee.iid iid,repremotee.iactid iactid,repremotee.iactname iactname,repremotee.iactstatedataversion iactstat,repremotee.iadaptordefname iadaptor,repremotee.iadaptordefuuid iadaptordefuuid,repremotee.iagenthost iagenthost,repremotee.iagentport iagentport,repremotee.iexceptionid iexcepti,repremotee.iflowid iflowid,repremotee.iflowname iflowname,repremotee.iinputid iinputid,repremotee.iissafefirst iissafe,repremotee.ilastupdatetime ilastup,repremotee.ioutputid ioutputid,repremotee.iprojectname iprojec,repremotee.iscopeid iscopeid,repremotee.iserverhost iserverHost,repremotee.iserverport iserverPort,repremotee.istarttime istarttime,repremotee.istatus istatus,repremotee.itimeout itimeout from ieai_remoteexecact repremotee where repremotee.iid = ?";
                    ps = con.prepareStatement(sql);
                    ps.setString(1, requestId);
                    rs = ps.executeQuery();

                    while (rs.next())
                    {
                        repAct = new RepRemoteExecAct();
                        repAct.setId(rs.getString("iid"));
                        repAct.setActId(rs.getString("iactid"));
                        repAct.setActName(rs.getString("iactname"));
                        repAct.setActStateDataVersion(rs.getInt("iactstat"));
                        repAct.setAdaptorDefName(rs.getString("iadaptor"));
                        repAct.setAdaptorDefUUID(rs.getString("iadaptordefuuid"));
                        repAct.setAgentHost(rs.getString("iagenthost"));
                        repAct.setAgentPort(rs.getInt("iagentport"));
                        repAct.setExceptionId(rs.getLong("iexcepti"));
                        repAct.setFlowId(rs.getLong("iflowid"));
                        repAct.setFlowName(rs.getString("iflowname"));
                        repAct.setInputId(rs.getLong("iinputid"));
                        repAct.setSafeFirst(rs.getBoolean("iissafe"));
                        repAct.setLastUpdateTime(rs.getLong("ilastup"));
                        repAct.setOutputId(rs.getLong("ioutputid"));
                        repAct.setProjectName(rs.getString("iprojec"));
                        repAct.setScopeId(rs.getLong("iscopeid"));
                        repAct.setServerHost(rs.getString("iserverHost"));
                        repAct.setServerPort(rs.getInt("iserverPort"));
                        repAct.setStartTime(rs.getLong("istarttime"));
                        repAct.setStatus(rs.getInt("istatus"));
                        repAct.setTimeout(new Long(rs.getLong("itimeout")));
                    }
                    long endTime = System.currentTimeMillis();
                    long interval = endTime - startTime;
                    if (SystemConfig.isHibernateTime())
                    {
                        _log.info("Stamp:EngineRepository.getRexecRequest:" + interval);
                    }
                    if (null == repAct || null == repAct.getId())
                    {
                        return new RemoteActivityExecRequest();
                    }
                    return getFullRemoteExecAct(type, repAct);
                } catch (DBException e)
                {
                    _log.error("EngineRepository getRexecRequest  2217 rows" + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_INIT);
                } catch (SQLException e)
                {
                    _log.error("EngineRepository getRexecRequest  2259 rows " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);

                } finally
                {
                    DBResource.closeConn(con, rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }

            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }

        }
    }

    /**
     * @update tao_ding
     * @des:
     * @datea:2010-1-28
     * @param rexecRequest
     * @param con
     * @throws RepositoryException
     * @throws MarshallingException
     */
    public void saveRExecRequest ( RemoteActivityExecRequest rexecRequest, Connection con )
            throws RepositoryException, MarshallingException
    {
        // 修改blob表的操作为JDBC连接 09-09-04 by tao_ding
        RepRemoteExecAct repRemoteAct = RepRemoteExecAct.newInstance(rexecRequest);
        if (rexecRequest.getException() != null)
        {
            Long exceptionId = LobStorer.saveBinaryNoCommit(SerializationUtils.serialize(rexecRequest.getException()),
                    con, Constants.IEAI_IEAI_BASIC);
            repRemoteAct.setExceptionId(exceptionId.longValue());
        }
        if (rexecRequest.getInput() != null)
        {
            Long inputId = LobStorer.saveBinaryNoCommit(
                    SerializationUtils
                            .serialize((HashMap) IDataMarshallerHelper.translateValueMap(rexecRequest.getInput())),
                    con, Constants.IEAI_IEAI_BASIC);
            repRemoteAct.setInputId(inputId.longValue());
        }
        if (rexecRequest.getOutput() != null)
        {
            Long inputId = LobStorer.saveBinaryNoCommit(
                    SerializationUtils
                            .serialize((Hashtable) IDataMarshallerHelper.translateValueMap(rexecRequest.getOutput())),
                    con, Constants.IEAI_IEAI_BASIC);
            repRemoteAct.setInputId(inputId.longValue());
        }
        // update by tao_ding on 2009-01-12
        // // 通过工程名获取工程信息
        // Project project = null;
        // try
        // {
        // project = ProjectManager.getInstance().loadProject(repRemoteAct.getProjectName());
        // } catch (ServerException e)
        // {
        // _log.error("saveRExecRequest getProject is error" + e);
        // }
        // if (project != null)
        // {
        // // 判断工程类型是否为作业调度
        // if (project.getProjectType().getProjectType() == Constants.PROJECT_TYPE_JOBSCHEDULING)
        // {
        DbOpScheduler dbOpScheduler = new DbOpScheduler();
        dbOpScheduler.saveRemoteExecAct(repRemoteAct, con);
        // }
        // }
        // end update
        return;
    }

    public void removeRExecRequest ( String reqId ) throws RepositoryException
    {
        String sqlRemoteExecAct = "";
        String sqlRequestStateData = "";
        TransactionStorerJDBC storer = new TransactionStorerJDBC();
        try
        {
            sqlRemoteExecAct = "select * from ieai_remoteexecact t where t.iid = '" + reqId + "'";
            RepRemoteExecAct repAct = storer.getRepRemoteExecAct(sqlRemoteExecAct);
            sqlRequestStateData = "select t.iid, t.ibytedataid, t.iintdata, t.ilongdata from ieai_requeststatedata t where t.iid = '" + reqId + "'";
            RepRequestStateData repStateData = storer.getRequestStateData(sqlRequestStateData);
            if (repStateData != null && repStateData.getBytedataId() != -1)
            {
                LobStorer.removeBinary(new Long(repStateData.getBytedataId()));
            }
            if (repStateData != null)
            {
                storer.remove("delete from ieai_requeststatedata where ieai_requeststatedata.iid = '" + reqId + "'");
            }
            if (null != repAct)
            {
                if (repAct.getExceptionId() != -1)
                {
                    LobStorer.removeBinary(new Long(repAct.getExceptionId()));
                }
                if (repAct.getInputId() != -1)
                {
                    LobStorer.removeBinary(new Long(repAct.getInputId()));
                }
                if (repAct.getOutputId() != -1)
                {
                    LobStorer.removeBinary(new Long(repAct.getOutputId()));
                }
                storer.remove("delete from ieai_remoteexecact where ieai_remoteexecact.iid = '" + reqId + "'");
            }
            return;
        } catch (RepositoryException ex)
        {
            throw ex;
        }
    }

    /**
     * <AUTHOR>
     * @des:设置超时时间为jdbc方式实现
     * @datea:2010-1-11
     * @param reqId
     * @param newTimeout
     * @throws RepositoryException
     */
    public void setTimeOut ( String reqId, long newTimeout ) throws RepositoryException
    {
        EngineRepositotyJdbc.getInstance().setTimeOut(reqId, newTimeout);
    }

    public void updateRExecRequest ( RemoteActivityExecRequest request, Connection con )
            throws RepositoryException, MarshallingException
    {

        DbOpScheduler dbOpScheduler = new DbOpScheduler();

        RepRemoteExecAct repAct = new RepRemoteExecAct();
        repAct.setStatus(request.getStatus());

        if (request.getLastUpdateTime() != null)
        {
            repAct.setLastUpdateTime(DBUtil.getTime(request.getLastUpdateTime().getTime()));
        }
        repAct.setTimeout(request.getTimeout());
        if (request.getOutput() != null)
        {
            if (repAct.getOutputId() == -1)
            {
                Long outputId = null;

                outputId = LobStorer.saveBinaryNoCommit(SerializationUtils
                                .serialize((Serializable) IDataMarshallerHelper.translateValueMap(request.getOutput())),
                        con);

                repAct.setOutputId(outputId.longValue());
            } else
            {

                LobStorer.updateBinary(new Long(repAct.getOutputId()), SerializationUtils
                                .serialize((Serializable) IDataMarshallerHelper.translateValueMap(request.getOutput())),
                        con);
                repAct.setOutputId(repAct.getOutputId());
            }
        }
        if (request.getException() != null)
        {
            if (repAct.getExceptionId() == -1)
            {
                Long exceptionId = LobStorer.saveBinaryNoCommit(SerializationUtils.serialize(request.getException()),
                        con);
                repAct.setExceptionId(exceptionId.longValue());
            } else
            {
                LobStorer.updateBinary(new Long(repAct.getExceptionId()),
                        SerializationUtils.serialize(request.getException()), con);
                repAct.setExceptionId(repAct.getExceptionId());
            }
        }
        // update by tao_ding on 2009-10-12
        dbOpScheduler.updateRemoteExecactMess(repAct, request.getId(), con);
        // end update

    }

    /**
     * 添加Agent端返回结果时，将活动的状态修改为pending
     *
     * @param requestId
     * @throws RepositoryException
     * <AUTHOR>
     * @param state
     */
    public void updateExecActPending ( int state, int flag, String requestId, Connection con )
            throws RepositoryException
    {
        PreparedStatement ps = null;
        try
        {
            if (-5 == state)
            {
                ps = con.prepareStatement(
                        "update ieai_execact ar set ar.istate = ?, ar.iflag=?,ar.IISFORRECOVERY=? ,ar.isexcepted=0 where ar.istate <>15 and ar.irexecrequestid = ? ");
                ps.setInt(1, ExecAct.EXCEPTION);
                ps.setInt(2, flag);
                ps.setBoolean(3, false);
                ps.setString(4, requestId);
            } else
            {
                ps = con.prepareStatement(
                        "update ieai_execact ar set ar.istate = ?, ar.iflag=?,ar.IISFORRECOVERY=? ,ar.isexcepted=0 where ar.istate <>15 and  ar.irexecrequestid = ? ");
                ps.setInt(1, ExecAct.PENDING);
                ps.setInt(2, flag);
                ps.setBoolean(3, false);
                ps.setString(4, requestId);
            }

            int row = ps.executeUpdate();
            if (row > 0)
                _log.info("updateExecActPending success , the requestId is " + requestId);
            else
                _log.info("updateExecActPending failed , the requestId is " + requestId + "; the state is " + state);
        } catch (SQLException e)
        {
            DBResource.rollback(con, ServerError.ERR_DB_UPDATE, e,
                    Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        } finally
        {
            DBResource.closePreparedStatement(ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }
    }

    public void saveOrUpdateRExecRequestActStateData ( String reqId, int version, ActStateData actStateData )
            throws RepositoryException
    {
        for (int i = 0;; i++)
        {
            try
            {
                Connection con = null;
                try
                {
                    con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
                } catch (DBException e)
                {
                    _log.error(" saveOrUpdateRExecRequestActStateData 1720 rows " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_INIT);
                }
                try
                {
                    DbOpScheduler dbOpScheduler = new DbOpScheduler();
                    long id = ((Long) dbOpScheduler.getExecActId(reqId).get(0)).longValue();
                    RepActStateData repStateData = dbOpScheduler.getRepActStateData(id);
                    if (repStateData == null)
                    {
                        RepActStateData temp = RepActStateData.newInstance(actStateData);
                        if (actStateData.getBytesData() != null)
                        {
                            Long byteId = LobStorer.saveBinaryNoCommit(actStateData.getBytesData(), con);
                            temp.setBytedataId(byteId.longValue());
                        }
                        temp.setIid(id);
                        // update by tao_ding on 2009-10-12
                        dbOpScheduler.saveActStateData(temp, con);
                        // end update
                    } else
                    {
                        if (actStateData.getBytesData() != null)
                        {
                            if (repStateData.getBytedataId() == -1)
                            {
                                Long bytedataId = LobStorer.saveBinaryNoCommit(actStateData.getBytesData(), con);
                                repStateData.setBytedataId(bytedataId.longValue());
                            } else
                            {
                                LobStorer.updateBinary(new Long(repStateData.getBytedataId()),
                                        actStateData.getBytesData(), con);
                            }
                        }
                        repStateData.setIntData(actStateData.getIntData());
                        repStateData.setLongData(actStateData.getLongData());
                        repStateData.setShortStringData(actStateData.getShortStringData());
                        // update by tao_ding on 2009-10-12
                        dbOpScheduler.updateActStateData(repStateData, con);
                        // end update
                    }
                    // update by tao_ding on 2009-10-12
                    dbOpScheduler.updateVersion(reqId, version, con);
                    // end update
                    con.commit();
                    return;
                } catch (SQLException e)
                {
                    DBResource.rollback(con, ServerError.ERR_DB_UPDATE, e,
                            Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                } finally
                {
                    DBResource.closeConnection(con, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
            } catch (RepositoryException ex)
            {
                String method = Thread.currentThread().getStackTrace()[1].getMethodName();
                _log.error(method + " is error at EngineRepository ", ex);
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
    }

    private RemoteActivityExecRequest getFullRemoteExecAct ( int sysType, RepRemoteExecAct repRemoteAct )
            throws RepositoryException, UnMarshallingException
    {
        // update by tao_ding on 2009-06-02
        RemoteActivityExecRequest remoteAct = repRemoteAct.toCommons();
        if (remoteAct.getId().indexOf("cm") > -1)
        {
            ActStateData stateData = new ActStateData();
            remoteAct.setActStateData(stateData);
        } else if (remoteAct.getId().indexOf("ieai-auto-") > -1)
        {
            ActStateData stateData = new ActStateData();
            remoteAct.setActStateData(stateData);
        } else if (remoteAct.getId().indexOf("ieai-BigpkgBuild-") > -1)
        {
            ActStateData stateData = new ActStateData();
            remoteAct.setActStateData(stateData);
        } else if (remoteAct.getId().indexOf("ieai-intall-") > -1)
        {
            ActStateData stateData = new ActStateData();
            remoteAct.setActStateData(stateData);
        } else if (remoteAct.getId().indexOf("ieai-checkini-") > -1)
        {
            ActStateData stateData = new ActStateData();
            remoteAct.setActStateData(stateData);
        } else if (remoteAct.getId().indexOf("ieai-CheckInstall-") > -1)
        {
            ActStateData stateData = new ActStateData();
            remoteAct.setActStateData(stateData);
        } else if (remoteAct.getId().indexOf("ieai-vmInstall-") > -1)
        {
            ActStateData stateData = new ActStateData();
            remoteAct.setActStateData(stateData);
        } else if (remoteAct.getId().indexOf("ieai-optDivideVLAN-") > -1)
        {
            ActStateData stateData = new ActStateData();
            remoteAct.setActStateData(stateData);
        } else if (remoteAct.getId().indexOf("ieai-vmVersion-") > -1)
        {
            ActStateData stateData = new ActStateData();
            remoteAct.setActStateData(stateData);
        } else if (remoteAct.getId().indexOf("maop") > -1)
        {
            ActStateData stateData = new ActStateData();
            remoteAct.setActStateData(stateData);
            Hashtable scriptHashtable = new Hashtable();
            try
            {
                Map<String,Object> map = EngineRepository.getInstance().getToolsRequestByUUID(remoteAct.getId(), Constants.IEAI_TOOLS);
                String toolScriptType = String.valueOf(map.get("toolScriptType"));

                if (null != map)
                {
                    String cmd="";
                    //1、通过系统类型，判断实行su脚本类型，组装command
                    String iusername = String.valueOf(map.get("iusername"));
                    //2、组装参数，key=value
                    if("1".equals(map.get("toolScriptType"))){
                        scriptHashtable.put("scriptContent", map.get("icontent"));
                        scriptHashtable.put("command", map.get("icontent"));
                        String scriptContent = String.valueOf(map.get("icontent"));
                        scriptHashtable.put("serviceName", map.get("iscriptname"));
                        scriptHashtable.put("servicesType", map.get("iscripttype"));
                        scriptHashtable.put("executeUsername", iusername);
                        if ("windows".equals(String.valueOf(map.get("nOstype")))){
                            String batPath = Environment.getInstance()
                                    .getSysConfig(ServerEnv.GD_SUS_FLOW_RUN_BAT_PATH, "");
                            scriptHashtable.put("startIn", batPath);
                        }
                    }else{
                        DefaultConfig config = new DefaultConfig();
                        if ("windows".equals(String.valueOf(map.get("nOstype")))){
                            String batPath = Environment.getInstance()
                                    .getSysConfig(ServerEnv.GD_SUS_FLOW_RUN_BAT_PATH, "");
                            cmd = "cmd.exe /c SU.bat " + getAreaUserName(iusername) +" "
                                    + String.valueOf(map.get("command")+ " " + map.get("pm"));
                            config.put("startIn", batPath);
                            scriptHashtable.put("startIn", batPath);
                            scriptHashtable.put("executeUsername", iusername);
                        }else{
                            cmd = "sh ./baseScript/SU.sh "   + iusername + " " + String.valueOf(map.get("command")+ " " + map.get("pm"));
                        }
                        config.put("command", cmd);
                        scriptHashtable.put("command", cmd);
                    }
                }
                String gdbe = Environment.getInstance().getSysConfig(Environment.GD_SCRIPT_BEGIN_END);
                if(null!=gdbe&&!"".equals(gdbe)) {
                    scriptHashtable.put("startInTmp", gdbe);
                }
                List aneexFiles = null;
                try
                {
                    aneexFiles = getAneexBinary(String.valueOf(map.get("iscriptiId")), Constants.IEAI_SCRIPT_SERVICE);
                } catch (RepositoryException e1)
                {
                    _log.error(" get AneexFile is Error!", e1);
                }
                if (null != aneexFiles && aneexFiles.size() > 0)
                {
                    //Object[] obj = new Object[aneexFiles.size() * 2];
                    MapObject mapObject = new MapObject();
                    int n = 0;
                    for (int m = 0; m < aneexFiles.size(); m++)
                    {
                        BinaryEntityFiles blobEntity = (BinaryEntityFiles) aneexFiles.get(m);
                        BinaryObject binaryObject = new BinaryObject(blobEntity.getContent());
                        mapObject.put(blobEntity.getName(),binaryObject);
                    }
                    scriptHashtable.put("annexFiles", mapObject);
                }
                scriptHashtable.put("pm", map.get("pm"));
                scriptHashtable.put("expectLastline", "0");// 预期结果
                scriptHashtable.put("expecttype", "1");// 判断类型，1：lastLine，2：ret
                scriptHashtable.put("errorExpectLastline", "");// 预期异常结果
                scriptHashtable.put("isShutdown", false);
                scriptHashtable.put("isTest", true);
                scriptHashtable.put("scriptInstanceId", String.valueOf(0));
                scriptHashtable.put(Environment.SCRIPT_AGENT_CONSUMER_SCRIPTINSTANCE_BEAN_ID,Environment.getInstance().getScriptAgentConsumerScriptinstanceBeanId());
                scriptHashtable.put(Environment.SCRIPT_AGENT_CONSUMER_SCRIPTADAPTOR_BEAN_ID,Environment.getInstance().getScriptAgentConsumerScriptadaptorBeanId());
                remoteAct.setInput(scriptHashtable);
            } catch (Exception e)
            {
                e.printStackTrace();
            }
        }else
        {
            RepActStateData repStateData = null;
            DbOpScheduler scheduler = new DbOpScheduler();
            List list = scheduler.getExecActId(repRemoteAct.getId());
            Long execActId = null;
            if (list.size() > 0)
            {
                execActId = (Long) list.get(0);
                // end update
                repStateData = scheduler.getRepActStateData(execActId.longValue());

            }

            ActStateData stateData = null;
            if (repStateData != null)
            {
                if (repStateData.getBytedataId() != -1)
                {
                    byte[] bytes = LobStorer.getBinary(sysType, new Long(repStateData.getBytedataId()));
                    stateData = repStateData.toCommons();
                    stateData.setBytesData(bytes);
                }
            }
            // RemoteActivityExecRequest remoteAct = repRemoteAct.toCommons();
            remoteAct.setActStateData(stateData);
            if (repRemoteAct.getExceptionId() != -1)
            {
                byte[] exception = LobStorer.getBinary(sysType, new Long(repRemoteAct.getExceptionId()));
                // add by tao_ding on 20090911
                for (int i = 0; i < 10; i++)
                {
                    if (null == exception || exception.length == 0)
                    {
                        try
                        {
                            Thread.sleep(3000);
                        } catch (InterruptedException e)
                        {
                        }
                        exception = LobStorer.getBinary(sysType, new Long(repRemoteAct.getExceptionId()));
                    } else
                    {
                        break;
                    }

                    if (i == 9)
                    {
                        _log.info("select database's blob  for exception is  null! ----BlobIID:"
                                + repRemoteAct.getExceptionId() + " ActId=" + repRemoteAct.getActId());
                    }
                }
                // end
                remoteAct.setException((Throwable) SerializationUtils.deserialize(exception));
            }
            if (repRemoteAct.getInputId() != -1)
            {

                byte[] inputBytes = LobStorer.getBinary(sysType, new Long(repRemoteAct.getInputId()));

                // add by tao_ding on 20090911
                for (int i = 0; i < 10; i++)
                {
                    if (null == inputBytes || inputBytes.length == 0)
                    {
                        try
                        {
                            Thread.sleep(3000);
                        } catch (InterruptedException e)
                        {
                        }
                        inputBytes = LobStorer.getBinary(sysType, new Long(repRemoteAct.getInputId()));
                    } else
                    {
                        break;
                    }

                    if (i == 9)
                    {
                        _log.info(" select database's blob  for inputBytes is  null! ----BlobIID:"
                                + repRemoteAct.getExceptionId() + " AvctId" + repRemoteAct.getActId());
                    }
                }
                // end
                Map input = new HashMap();
                if (!(null == inputBytes || inputBytes.length == 0))
                {
                    input = (Map) SerializationUtils.deserialize(inputBytes);
                }

                // 替换 YYYYMMDD
                String cmd = (String) input.get(Constants.SHELLCMD_COMMAND);
                if (null != cmd && cmd.contains(Constants.SHELLCMD_YYYYMMDD)||null != cmd && cmd.contains(Constants.SHELLCMD_YYYY_MM_DD))
                {
                    try
                    {
                        cmd = ExcelActUtil.getInstance().replaceYYYYMMDD(cmd, repRemoteAct.getFlowId());
                        input.put(Constants.SHELLCMD_COMMAND, cmd);
                    } catch (ServerException e)
                    {
                        _log.error("转换数据日期出错，工作流ID: " + repRemoteAct.getFlowId() + "获取数据日期失败 ：-90909 ");
                        throw new RepositoryException(-90909);
                    }
                }
                // 替换脚本命令，格式为%%%key%%%
                if (null != cmd && !"".equals(cmd)) {
                    try {
                        VariableExtractor ve = new VariableExtractor();
                        cmd = ve.replaceJobCommand(cmd);
                        input.put(Constants.SHELLCMD_COMMAND, cmd);
                    } catch (Exception e) {
                        _log.error("转换程序命令出错，工作流ID: " + repRemoteAct.getFlowId() + "获取程序命令失败 ：-90909 ", e);
                    }
                }
                // 替换前继活动输出，格式为$活动名.lastLine
                if (null != cmd && (cmd.indexOf("$") != -1 && cmd.indexOf(".lastLine") != -1)) {
                    cmd = Evaluate.concurrentExcelEvalShellLastline(cmd, repRemoteAct.getFlowId());
                    input.put(Constants.SHELLCMD_COMMAND, cmd);
                }
                // 替换环境变量，格式为###key###
                if (null != cmd && !"".equals(cmd)) {
                    try {
                        VariableExtractor ve = new VariableExtractor();
                        cmd = ve.replaceJobParameter(cmd, repRemoteAct.getProjectName());
                        input.put(Constants.SHELLCMD_COMMAND, cmd);
                    } catch (Exception e) {
                        _log.error("转换环境变量出错，工作流ID: " + repRemoteAct.getFlowId() + "获取环境变量失败 ：-90909 ", e);
                    }
                }

                remoteAct.setInput(new Hashtable(IDataMarshallerHelper.revertValueMap(input)));
            }
            if (repRemoteAct.getOutputId() != -1)
            {

                byte[] outputBytes = LobStorer.getBinary(sysType, new Long(repRemoteAct.getOutputId()));

                // add by tao_ding on 20090911
                for (int i = 0; i < 10; i++)
                {
                    if (null == outputBytes || outputBytes.length == 0)
                    {
                        try
                        {
                            Thread.sleep(3000);
                        } catch (InterruptedException e)
                        {
                        }
                        outputBytes = LobStorer.getBinary(sysType, new Long(repRemoteAct.getOutputId()));
                    } else
                    {
                        break;
                    }

                    if (i == 9)
                    {
                        _log.info(" select database's blob  for outputBytes is  null! --------BlobIID:"
                                + repRemoteAct.getExceptionId() + " AvctId" + repRemoteAct.getActId());
                    }
                }
                // end
                Map output = (Map) SerializationUtils.deserialize(outputBytes);
                remoteAct.setOutput(new Hashtable(IDataMarshallerHelper.revertValueMap(output)));
            }
        }

        return remoteAct;
    }

    /* begin updata by zhigang_zhou* */

    /**
     * TODO ********************** when act finished delete from IEAI_EXECACT
     * *************************
     */
    public void removeExecAct ( long flowId ) throws RepositoryException
    {
        String sql = "delete from ieai_execact as rreb where rreb.iid in (select rrea.iid from ieai_execact as rrea where rrea.iflowid = "
                + flowId + ")";
        ObjectStorerDB.remove(sql);
        return;
    }

    /**
     * TODO ********************** when act finished delete from IEAI_ACTSTATEDATA
     * *************************
     */
    public void removeActStateData ( long flowId ) throws RepositoryException
    {
        String sql = "delete from select rreb.iid from ieai_actstatedata rreb where rreb.iid in (select rrea.iid from ieai_actruntime rrea where rrea.iflowId = "
                + flowId + ")";
        ObjectStorerDB.remove(sql);
        return;
    }

    /**
     * TODO ********************** when act finished delete from IEAI_ACTVALTIMECONFIG,
     * IEAI_VALIDTIMEDAY
     * ,IEAI_VALIDTIMEWEEK,IEAI_VALIDTIMEMONTH,IEAI_VALIDTIMEHOUR,IEAI_VALIDTIMEMINUTE
     * *************************
     */
    private List getPreActivityTimeData ( Long runTimeId, int type ) throws RepositoryException
    {
        return ObjectStorerDB
                .findRepActExecTimeConfig("select * from ieai_acttimeconfig rrea where rrea.iid = " + runTimeId, type);

    }

    public List getFlowRuntimeAct ( long flowId, int type ) throws RepositoryException
    {
        StringBuffer sql = new StringBuffer("select * from ieai_actruntime rrea where rrea.iflowid =").append(flowId);
        List result = ObjectStorerDB.findAllActivityRuntime(sql.toString(), type);
        return result;
    }

    public void removeTimeData ( long flowId, int type ) throws RepositoryException, ServerException
    {
        try
        {
            List runTimeList = getFlowRuntimeAct(flowId, type);
            for (int i = 0; i < runTimeList.size(); i++)
            {
                RepActivityRuntime runTime = (RepActivityRuntime) runTimeList.get(i);
                Long runTimeId = runTime.getId();
                List timeConfigList = getPreActivityTimeData(runTimeId, type);
                for (int m = 0; m < timeConfigList.size(); m++)
                {
                    RepActExecTimeConfig repRuntime = (RepActExecTimeConfig) timeConfigList.get(m);
                    // List valTimeList = new ArrayList();
                    List valTimeList = repRuntime.getValidateTimes();
                    for (int n = 0; n < valTimeList.size(); n++)
                    {
                        RepActValidateTimeConfig timeConfig = (RepActValidateTimeConfig) valTimeList.get(n);
                        ObjectStorerDB.remove("delete from ieai_actvaltimeconfig" + timeConfig.getId(), type);
                    }
                }
            }
        } catch (RepositoryException ex)
        {
            _log.error("error while setting the state of activity runtime", ex);
            throw new ServerException(ex.getServerError());
        }
    }

    /**
     * TODO ********************** when flow finished delete from IEAI_BLOB
     * *************************
     */
    /**
     * <AUTHOR>
     * @des:
     * @datea:Sep 4, 2009
     * @param flowId
     * @throws RepositoryException
     */
    public void removeActBlob ( long flowId ) throws RepositoryException
    {
        String sql = "select rreb.iid from ieai_flowenv rrea,ieai_blob rreb where rrea.iflowId = ? and rrea.iflowDataId = rreb.iid";
        for (int i = 0;; i++)
        {
            try
            {
                Connection con = null;
                PreparedStatement ps = null;
                ResultSet actRS = null;
                long id = 0;
                try
                {
                    con = DBManager.getInstance().getSchedulerConnection(Constants.IEAI_IEAI_BASIC);
                } catch (DBException e)
                {
                    _log.error(" Dbopscheduler getFlowState 365 rows " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_INIT);
                }
                try
                {
                    ps = con.prepareStatement(sql);
                    ps.setLong(1, flowId);
                    actRS = ps.executeQuery();
                    while (actRS.next())
                    {
                        id = actRS.getLong("IID");
                    }
                    DbOpScheduler opScheduler = new DbOpScheduler();
                    opScheduler.removeActBlob(id, con);
                } catch (SQLException e)
                {
                    String method = Thread.currentThread().getStackTrace()[1].getMethodName();
                    _log.error(method + " is error at ObjectStorerDB ", e);
                    DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, actRS, ps, Thread.currentThread().getStackTrace()[1].getMethodName(),
                            _log);
                }
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
    }

    /**
     * TODO ********************** when flow finished delete from IEAI_ACTSTATES Output and BLOB
     * *************************
     */
    public void removeActOutput ( long flowId ) throws RepositoryException
    {
        TransactionStorerJDBC storer = new TransactionStorerJDBC();
        // 删除ieai_branchscope表中指定条件的记录
        String sql = "delete from ieai_branchscope rreb where rreb.iid in (select rreb.iid from ieai_branchscope rrea,ieai_actoutput rreb where rrea.iflowid = "
                + flowId + " and rrea.iscopeid = rreb.iactscopeid)";
        try
        {
            storer.remove(sql);
        } catch (RepositoryException e)
        {
            throw new RepositoryException(ServerError.ERR_DB_DELETE);

        }
        return;
    }

    /**
     * TODO ********************** when flow finished delete from IEAI_STRUCTINFO
     * *************************
     */
    public void removeStructInfo ( long flowId ) throws RepositoryException
    {
        TransactionStorerJDBC storer = new TransactionStorerJDBC();
        // 删除ieai_structinfo表中指定条件的记录
        String sql = "delete from ieai_structinfo rreb where rreb.iid in (select rrea.idd from ieai_structinfo rrea where rrea.flowId ="
                + flowId + ")";
        try
        {
            storer.remove(sql);
            return;
        } catch (RepositoryException ex)
        {
            throw new RepositoryException(ServerError.ERR_DB_DELETE);
        }
    }

    /**
     * TODO ********************** when flow finished delete from IEAI_BRANCHINFO
     * *************************
     */
    public void removeBranchInfo ( long flowid ) throws RepositoryException
    {
        TransactionStorerJDBC storer = new TransactionStorerJDBC();
        // 删除ieai_branchinfo表中指定条件的记录
        String sql = "delete from ieai_branchinfo rreb where rreb.iid in (select rrea.iid from ieai_branchinfo rrea where rrea.iflowid = "
                + flowid + ")";
        try
        {
            storer.remove(sql);
            return;
        } catch (RepositoryException ex)
        {
            throw new RepositoryException(ServerError.ERR_DB_DELETE);
        }
    }

    /**
     * TODO ********************** when flow finished delete from IEAI_BRANCHSCOPE
     * *************************
     */
    public void removeBranchScope ( long flowid ) throws RepositoryException
    {
        // 删除ieai_branchscope表中指定条件的记录
        String sql = "delete from ieai_branchscope rreb where rreb.iid in (select rrea.iflowid from ieai_branchscope rrea where rrea.iflowid = "
                + flowid + ")";
        ObjectStorerDB.remove(sql);
        return;
    }

    /**
     * TODO ********************** when flow finished delete from IEAI_ACTRUNTIME
     * *************************
     */
    public void removeActRunTime ( long flowid ) throws RepositoryException
    {
        TransactionStorerJDBC storer = new TransactionStorerJDBC();
        // 删除ieai_actruntime表中指定条件的记录
        String sql = "delete from ieai_actruntime rreb where rreb.iid in (select rrea.iid from ieai_actruntime rrea where rrea.iflowId = "
                + flowid + ")";
        try
        {
            storer.remove(sql);
        } catch (RepositoryException ex)
        {
            throw new RepositoryException(ServerError.ERR_DB_DELETE);
        }
        return;
    }

    // finish act done
    public void removeActFinished ( ExecAct endExecAct ) throws ServerException
    {
        /*
         * try{ removeExecAct(endExecAct); removeActStateData(endExecAct);
         * //removeActStates(endExecAct); removeTimeData(endExecAct); }catch (RepositoryException
         * ex){ throw new ServerException(ex.getServerError().errCode); }
         */
    }

    // finish flow done
    public void removeFlowFinished ( long flowId ) throws ServerException
    {
        // try{
        // removeExecAct(flowId);
        // removeActStateData(flowId);
        // removeTimeData(flowId);
        // removeActBlob(flowId);
        // removeActOutput(flowId);
        // removeBranchInfo(flowId);
        // removeStructInfo(flowId);
        // removeBranchScope(flowId);
        // //removeActPre(flowId);
        // removeActRunTime(flowId);
        // }catch (RepositoryException ex){
        // throw new ServerException(ex.getServerError().errCode);
        // }
    }

    /**
     * <AUTHOR>
     * @des:灾难恢复时添加灾难恢复的时间和灾难恢复人
     * @datea:Jun 7, 2009
     * @update tao_ding on 2010-1-11 修改为jdbc方式实现
     * @update xibin_gong on 2010-4-7 增加保存serverIp
     */
    public void recoverTimePerson ( String userId, long flowId, String serverIp, int type ) throws RepositoryException
    {
        EngineRepositotyJdbc.getInstance().recoverTimePerson(userId, flowId, serverIp, type);
    }

    /**
     * <AUTHOR>
     * @throws ServerException
     * @throws RepositoryException
     * @des:判断execact是否为当前server所属
     * @datea:2010-4-7
     */
    public boolean isContorlExec ( long actId ) throws ServerException
    {
        String serverIp = "";
        boolean isContorl = true;
        try
        {
            serverIp = Environment.getInstance().getServerIP();
            isContorl = EngineRepositotyJdbc.getInstance().isContorlExec(actId, serverIp);
        } catch (UnknownHostException e1)
        {
            _log.error("get server Ip error: isContorlExec()");
            e1.printStackTrace();
        } catch (RepositoryException ex)
        {
            throw new ServerException(ex.getServerError().getErrCode());
        }
        return isContorl;
    }

    public List getHostPortOfFlow ( long flowId ) throws RepositoryException
    {
        List hostPort = new ArrayList();
        for (int i = 0;; i++)
        {
            try
            {
                Connection con = null;
                PreparedStatement ps = null;
                ResultSet grs = null;
                try
                {
                    con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
                } catch (DBException e)
                {
                    _log.error("EngineRepository getHostPortOfFlow  1" + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_INIT);
                }
                try
                {
                    String sql = "select iagenthost, iagentport from (select count(*) as cnt ,a.iagenthost, a.iagentport from ieai_remoteexecact a where a.iflowid = ? group by a.iagenthost, a.iagentport)";
                    ps = con.prepareStatement(sql);
                    ps.setLong(1, flowId);
                    grs = ps.executeQuery();

                    while (grs.next())
                    {
                        Map hostAPort = new HashMap();
                        hostAPort.put("host", grs.getString("iagenthost"));
                        hostAPort.put("port", new Integer(grs.getInt("iagentport")));
                        hostPort.add(hostAPort);
                    }
                } catch (SQLException e)
                {
                    String method = Thread.currentThread().getStackTrace()[1].getMethodName();
                    _log.error(method + " is error at EngineRepository ", e);
                    DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, grs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }

            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
            return hostPort;
        }
    }

    /**
     * <li>Description:工作流调用活动构建执行活动对象</li>
     *
     * <AUTHOR> 2015-3-2
     * @param flowid
     * @param actid
     * @return
     * @throws RepositoryException return List
     */
    public List queryPendingExecActForException ( long flowid, long actid ) throws RepositoryException
    {
        List actList = new ArrayList();

        DbOpScheduler opScheduler = new DbOpScheduler();
        List list = opScheduler.queryPendingExecActForException(flowid, actid);
        actList.addAll(list);
        List full = getFullExecActsForScheduler(Constants.IEAI_IEAI_BASIC, actList);
        return full;
    }

    /**
     * <li>Description:工作流结束时用于获取，父工作流中对应的工作流调用活动Execact Id</li>
     *
     * <AUTHOR> 2017年7月1日
     * @param flowId
     * @return
     * @throws RepositoryException return long
     */

    public long queryPendingExecActForCallflow ( long flowId ) throws RepositoryException
    {
        DbOpScheduler opScheduler = new DbOpScheduler();
        long execactId = opScheduler.queryPendingExecActForCallflow(flowId);
        return execactId;
    }
    /**
     * 调整终止时调用的驱动后续节点方法，增加传递conn方式
     * <li>Description:</li> 
     * <AUTHOR>
     * 2022年8月3日 
     * @param flowId
     * @param type
     * @throws RepositoryException
     * return void
     */
    public void queryPendingExecActForCallflowType ( long flowId ,int type) throws RepositoryException
    {
        String thread=Thread.currentThread().getStackTrace()[1].getMethodName();
        for (int i = 0;; i++)
        {
            try
            {
                Connection con = null;
                try
                {
                    con = DBResource.getSchedulerConnection(thread, _log,type);
                    long execactId = EngineRepository.getInstance().queryPendingExecActForCallflowConn(flowId,con);
                    if (execactId > 0)
                    {
                        EngineRepository.getInstance().updateExecActStateConn(execactId, ExecAct.PENDING,con);
                        _log.info("queryPendingExecActForCallflowType :flowid:" + flowId + " ; execactId:" + execactId);

                    }
                    con.commit();
                } catch (SQLException e)
                {
                    _log.error(e);
                    DBResource.rollback(con, ServerError.ERR_DB_UPDATE, e,thread, _log);
                } finally
                {
                    DBResource.closeConnection(con,thread, _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
        
    }

    /**
     * <li>Description:工作流结束时用于获取，父工作流中对应的工作流调用活动Execact Id</li>
     *
     * <AUTHOR> 2017年7月1日
     * @mes    2019-4-16 下午4:55:43  tao_ding  通过事务进行处理，增加连接参数
     * @param flowId
     * @return
     * @throws RepositoryException return long
     */
    public long queryPendingExecActForCallflowConn ( long flowId, Connection con ) throws RepositoryException
    {
        DbOpScheduler opScheduler = new DbOpScheduler();
        return opScheduler.queryPendingExecActForCallflowConn(flowId, con);
    }

    /**
     * <li>Description:工作流调用活动构建执行活动对象</li>
     *
     * <AUTHOR> 2015-3-2
     * @param flowid
     * @param actid
     * @return
     * @throws RepositoryException return List
     */
    public List queryPendingExecActForCallflows ( long flowid ) throws RepositoryException
    {
        List actList = new ArrayList();

        DbOpScheduler opScheduler = new DbOpScheduler();
        List list = opScheduler.queryPendingExecActForCallfolws(flowid);
        actList.addAll(list);
        List full = getFullExecActsForScheduler(Constants.IEAI_IEAI_BASIC, actList);
        return full;
    }

    public List queryPendingExecActForCallfolw ( long flowid, int type ) throws RepositoryException
    {
        List actList = new ArrayList();

        DbOpScheduler opScheduler = new DbOpScheduler();
        List list = opScheduler.queryPendingExecActForCallfolw(flowid, type);
        actList.addAll(list);
        List full = getFullExecActsForScheduler(type, actList);
        return full;
    }

    /**
     * 获取所有可需执行数据 <li>Description:</li>
     *
     * <AUTHOR> 2016年1月14日
     * @param serverIp
     * @return
     * @throws RepositoryException return List
     */
    public List getExcelPendingActs ( String serverIp ) throws RepositoryException
    {
        DbOpScheduler opScheduler = new DbOpScheduler();
        List list = opScheduler.getExcelPendingActs(serverIp);
        return list;
    }

    /**
     * 获取本次可执行数据 <li>Description:</li>
     *
     * <AUTHOR> 2016年1月14日
     * @param actList
     * @param mainWorkId
     * @param preWorkIds
     * @param bean
     * @throws RepositoryException return void
     */
    public void getValidExecActs ( List actList, long mainWorkId, String preWorkIds, ActExcelToRunData bean, long flag )
            throws RepositoryException
    {
        DbOpScheduler opScheduler = new DbOpScheduler();
        opScheduler.getValidExecActs(actList, mainWorkId, preWorkIds, bean, flag);
    }

    /**
     * 招商可执行数据以及异常后对应的活动设置为略过 <li>Description:</li>
     *
     * <AUTHOR> 2016年1月21日
     * @param actList
     * @param mainWorkId
     * @param preWorkIds
     * @param bean
     * @param flag
     * @throws RepositoryException return void
     */
    public void getValidExecPreActState ( List actList, long mainWorkId, String preWorkIds, ActExcelToRunData bean,
                                          long flag ) throws RepositoryException
    {
        DbOpScheduler opScheduler = new DbOpScheduler();
        opScheduler.getValidExecPreActState(actList, mainWorkId, preWorkIds, bean, flag);
    }

    /**
     * 流程启动更新启动时间和状态以及回执工作流ID <li>Description:</li>
     *
     * <AUTHOR> 2016年1月14日
     * @param stepId
     * @param flowId
     * @param state
     * @param failstate
     * @throws RepositoryException return void
     */
    public void updateExecActsStartState ( long stepId, long flowId, long state, long failstate, int sysType )
            throws RepositoryException
    {
        DbOpScheduler opScheduler = new DbOpScheduler();
//        boolean flag = Environment.getInstance().getBooleanConfig(Environment.GZNX_SENDSNS, false);
//        if (flag)
//        {
//            try
//            {
//                // 中间插入,步骤异常的时候进行动态短信提醒
//                Map<String, Object> pwd = opScheduler.getInforByStepId(stepId, sysType);
//                if (pwd.get("iownertype") != null && pwd.get("iowner") != null && pwd.get("iid") != null&&failstate>0)
//                {
//                    String msgcontents = "步骤："+pwd.get("iactname")+"，发生异常，请登录灾备切换平台查看！";
//                    GznxSendMessThread thre = new GznxSendMessThread(Integer.parseInt(pwd.get("iownertype") + ""),
//                            Integer.parseInt(pwd.get("iid") + ""), msgcontents);
//                    thre.run();
//                }
//            } catch (Exception e)
//            {
//                _log.error("GZNX发送短信发生异常",e);
//            }
//           
//        }
        opScheduler.updateExecActsStartState(stepId, flowId, state, failstate, sysType);
    }

    /**
     * 流程未分组等异常，重试、实例重新执行，更新状态和异常 <li>Description:</li>
     *
     * <AUTHOR> 2016年1月14日
     * @param mainWorkId
     * @param status
     * @param stepId
     * @param flag
     * @throws RepositoryException return void
     */
    public void updateExcelFlowExcepFail ( long mainWorkId, long status, long stepId, long flag )
            throws RepositoryException
    {
        DbOpScheduler opScheduler = new DbOpScheduler();
        opScheduler.updateExcelFlowExcepFail(mainWorkId, status, stepId, flag);
    }

    /**
     * 工作流出现异常，未启动起来 <li>Description:</li>
     *
     * <AUTHOR> 2016年1月13日
     * @param stepId
     * @throws ServerException return void
     */
    public void flowLevelReRun ( long stepId, int flag, int type ) throws ServerException, RepositoryException
    {
        DbOpScheduler opScheduler = new DbOpScheduler();
        ActExcelToRunData bean = null;
        opScheduler.setExcelStepInfo(stepId, flag, type);
    }

    /**
     * 实例级重新执行
     */
    public void InstanceLevelReRun ( long InstanceId, int type ) throws ServerException, RepositoryException
    {
        DbOpScheduler opScheduler = new DbOpScheduler();
        opScheduler.instanceLevelReRun(InstanceId, type);
    }

    /**
     * excelip格式为名称，通过名称获取真实IP <li>Description:</li>
     *
     * <AUTHOR> 2016年2月23日
     * @param agentIp
     * @return
     * @throws ServerException
     * @throws RepositoryException return String
     */
    public String getRealAgentIp ( String agentIp ) throws ServerException, RepositoryException
    {
        String realIp = "";
        DbOpScheduler opScheduler = new DbOpScheduler();
        realIp = opScheduler.getRealAgentIp(agentIp);
        return realIp;
    }

    /**
     * 轮训线程，初始更新轮询内的所有步骤的状态为运行，以便后续异步发起 <li>Description:</li>
     *
     * <AUTHOR> 2016年2月23日
     * @param stepId
     * @throws RepositoryException return void
     */
    public void updateExecActsInitState ( long stepId[] ) throws RepositoryException
    {
        DbOpScheduler opScheduler = new DbOpScheduler();
        opScheduler.updateExecActsInitState(stepId);
    }

    public void updateRunStepState ( long flowId, String actName, String results, int flag ) throws RepositoryException
    {
        DbOpScheduler opScheduler = new DbOpScheduler();
        opScheduler.updateRunStepState(flowId, actName, results, flag);
    }

    public long resultForAuto ( String requestId, String output, int flag ) throws RepositoryException
    {
        long startTime = System.currentTimeMillis();
        long ret = 0;
        for (int i = 0;; i++)
        {
            try
            {
                Connection con = null;
                try
                {
                    con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
                    ret = LobStorer.saveBlobResult(output.getBytes(), con);
                    _log.info("Act " + requestId + " ,update result successfully!");
                } catch (Exception e)
                {
                    // 失败后数据回滚
                    DBResource.rollback(con, ServerError.ERR_DB_INSERT, e,
                            Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                } finally
                {
                    DBResource.closeConnection(con, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
        long endTime = System.currentTimeMillis();
        long interval = endTime - startTime;
        if (SystemConfig.isHibernateTime())
        {
            _log.info("Stamp:EngineRepository.resultForAuto:" + interval);
        }
        return ret;
    }

    public void updateAutoResult ( String requestId, String output, int flag ) throws ServerException
    {// 0:run,1:exc,2:over
        if (DBManager.Orcl_Faimily())
        {
            // oracle数据库处理
            updateAutoResult_oracle(requestId, output, flag);
        } else
        {
            String sql = "UPDATE IEAI_AUTOSINGLE_ACT SET  IACTSTATE =?,IRESULT=?  WHERE REQID = ?";
            for (int i = 0;; i++)
            {
                try
                {
                    PreparedStatement actStat = null;
                    Connection con = null;
                    try
                    {
                        byte[] content = output.getBytes();
                        con = DBResource.getConnection("updateAutoResult", _log, Constants.IEAI_IEAI_BASIC);
                        actStat = con.prepareStatement(sql);
                        // InputStream inputBlob = new ByteArrayInputStream(content);
                        actStat.setInt(1, flag);
                        java.sql.Clob c = new javax.sql.rowset.serial.SerialClob(output.toCharArray());
                        actStat.setClob(2, c);
                        actStat.setString(3, requestId);
                        actStat.execute();

                        con.commit();
                    } catch (SQLException e)
                    {
                        DBResource.rollback(con, ServerError.ERR_DB_UPDATE, e,
                                Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                    } finally
                    {
                        DBResource.closePSConn(con, actStat, "updateAutoResult", _log);
                    }
                    _log.info("Act " + requestId + " update state successfully!");
                    break;
                } catch (RepositoryException ex)
                {
                    _log.error(ex);
                    try
                    {
                        DBRetryUtil.waitForNextTry(i, ex);
                    } catch (RepositoryException e)
                    {
                        throw new ServerException(ServerError.ERR_DB_INSERT);
                    }
                }
            }
        }

    }

    public void updateAutoResult ( String requestId, String lastline, String stdout, int flag ) throws ServerException
    {
        boolean bhOutPutLangSwitch = Environment.getInstance().getBhOutPutLangSwitch();
        if (lastline==null){
            lastline="";
        }
        if (stdout==null)
        {
            stdout="";
        }
        if (DBManager.Orcl_Faimily() && JudgeDB.IEAI_DB_TYPE != 4)
        {
            // oracle数据库处理
            updateAutoResult_oracle(requestId, lastline, stdout, flag);
        } else
        {
            // 0:run,1:exc,2:over
            String sql = "UPDATE IEAI_AUTOSINGLE_ACT SET  IACTSTATE =?,IRESULT=?,IRESULT4STDOUT=?   WHERE REQID = ?";
            for (int i = 0;; i++)
            {
                try
                {
                    PreparedStatement actStat = null;
                    Connection con = null;
                    try
                    {
                        byte[] content = lastline.getBytes();
                        con = DBResource.getConnection("updateAutoResult", _log, Constants.IEAI_IEAI_BASIC);
                        actStat = con.prepareStatement(sql);

                        if(bhOutPutLangSwitch) {
                            try {
                                actStat.setInt(1, flag);

                                if("".equals(lastline)) {
                                    actStat.setString(2, "");
                                }else {
                                    java.sql.Clob c = new javax.sql.rowset.serial.SerialClob(lastline.toCharArray());
                                    actStat.setClob(2, c);
                                }
                                
                                if("".equals(stdout)) {
                                    actStat.setString(3, "");
                                }else {
                                    java.sql.Clob cStdOut = new javax.sql.rowset.serial.SerialClob(stdout.toCharArray());
                                    actStat.setClob(3, cStdOut);
                                }

                                actStat.setString(4, requestId);
                                actStat.execute();
                            }catch (Exception e){
                                String[] encodings = {
                                        "UTF-8", "UTF-16", "UTF-32", 
                                        "GBK", "GB2312", "ISO-8859-1", "Big5"
                                    };
                                for(String encoding : encodings) {
                                    try {
                                        actStat.setString(2, new String(lastline.getBytes(encoding), encoding));
                                        actStat.setString(3, new String(stdout.getBytes(encoding), encoding));
                                        actStat.execute();
                                        break;
                                    }catch (Exception ex) {
                                        _log.error("Agent返回日志字符集异常，转换字符集保存失败：" + encoding);
                                        if("Big5".equals(encoding)) {
                                            try {
                                                actStat.setString(2, removeGarbageChars(lastline));
                                                actStat.setString(3, removeGarbageChars(stdout));
                                                actStat.execute();
                                                _log.error("Agent返回日志字符集异常，过滤异常字符后保存！");
                                            }catch (Exception exc){
                                                actStat.setString(2, "Agent返回日志字符集异常！");
                                                actStat.setString(3, "Agent返回日志字符集异常！");
                                                actStat.execute();
                                                _log.error("Agent返回日志字符集异常！" + exc);
                                            }
                                        }
                                    }
                                }
                            }
                        }else {
                            actStat.setInt(1, flag);

                            if("".equals(lastline)) {
                                actStat.setString(2, "");
                            }else {
                                java.sql.Clob c = new javax.sql.rowset.serial.SerialClob(lastline.toCharArray());
                                actStat.setClob(2, c);
                            }
                            
                            if("".equals(stdout)) {
                                actStat.setString(3, "");
                            }else {
                                java.sql.Clob cStdOut = new javax.sql.rowset.serial.SerialClob(stdout.toCharArray());
                                actStat.setClob(3, cStdOut);
                            }

                            actStat.setString(4, requestId);
                            actStat.execute();
                        }

                        con.commit();
                    } catch (SQLException e)
                    {
                        DBResource.rollback(con, ServerError.ERR_DB_UPDATE, e,
                                Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                    } finally
                    {
                        DBResource.closePSConn(con, actStat, "updateAutoResult", _log);
                    }
                    _log.info("Act " + requestId + " update state successfully!");
                    break;
                } catch (RepositoryException ex)
                {
                    _log.error(ex);
                    try
                    {
                        DBRetryUtil.waitForNextTry(i, ex);
                    } catch (RepositoryException e)
                    {
                        throw new ServerException(ServerError.ERR_DB_INSERT);
                    }
                }
            }
        }

    }
    
    public static String removeGarbageChars(String input) {
        // 使用正则表达式匹配乱码部分并换为空字符串
        String cleanedString = input.replaceAll("[^\\p{Print}]", "\n");
        
        // 使用正则表达式删除空行
        String result = cleanedString.replaceAll("(?m)^\\s*$", "");
        return result;
    }

    public void updateAutoResult_oracle ( String requestId, String lastline, String stdout, int flag )
            throws ServerException

    {// 0:run,1:exc,2:over
        if (null==lastline){
            lastline="";
        }
        String sql = "UPDATE IEAI_AUTOSINGLE_ACT SET  IACTSTATE =?,IRESULT=?,IRESULT4STDOUT=?   WHERE REQID = ?";
        String sqlUpdate = "SELECT A.IRESULT,A.IRESULT4STDOUT FROM IEAI_AUTOSINGLE_ACT A  WHERE A.REQID=? FOR UPDATE ";
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                PreparedStatement psUpdate = null;
                Connection con = null;
                ResultSet rs = null;
                try
                {
                    try
                    {
                        con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
                    } catch (DBException e)
                    {
                        _log.error("EngineRepository getRexecRequestNormal  3425 rows" + e.getMessage());
                        throw new RepositoryException(ServerError.ERR_DB_INIT);
                    }

                    psUpdate = con.prepareStatement(sqlUpdate);
                    psUpdate.setString(1, requestId);
                    rs = psUpdate.executeQuery();

                    CLOB clob1 = null;
                    CLOB clob2 = null;
                    java.sql.Clob clob3 = null;
                    java.sql.Clob clob4 = null;
                    while (rs.next())
                    {
                        if(JudgeDB.IEAI_DB_TYPE ==5){
                            clob3 = rs.getClob("IRESULT");
                            if (null!=clob3){
                                clob3.setString(1,lastline.toString());
                            }
                            clob4 = rs.getClob("IRESULT4STDOUT");
                            if (null!=clob4){
                                clob4.setString(1,stdout.toString());
                            }
                        }else{
                            Object objClob1 =rs.getClob("IRESULT");
                            if (null!=objClob1){
                                clob1 = (CLOB) objClob1;
                                clob1.putString(1, lastline.toString());
                            }
                            Object objclob2 =rs.getClob("IRESULT4STDOUT");
                            if(null!=objclob2){
                                clob2 = (CLOB) objclob2;
                                clob2.putString(1, stdout.toString());
                            }
                        }


                    }

                    psUpdate = con.prepareStatement(sql);
                    psUpdate.setInt(1, flag);
                    if(JudgeDB.IEAI_DB_TYPE ==5){
                        psUpdate.setClob(2, clob3);
                        psUpdate.setClob(3, clob4);
                    }else{
                        psUpdate.setClob(2, clob1);
                        psUpdate.setClob(3, clob2);
                    }
                    psUpdate.setString(4, requestId);
                    psUpdate.executeUpdate();
                    con.commit();

                } catch (Exception e)
                {
                    e.printStackTrace();
                    DBResource.rollback(con, ServerError.ERR_DB_UPDATE, e,
                            Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                } finally
                {
                    DBResource.closePSRS(rs, psUpdate, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                    DBResource.closePSConn(con, actStat, Thread.currentThread().getStackTrace()[1].getMethodName(),
                            _log);
                }
                _log.info("Act " + requestId + " update state successfully!");
                break;
            } catch (RepositoryException ex)
            {
                try
                {
                    DBRetryUtil.waitForNextTry(i, ex);
                } catch (RepositoryException e)
                {
                    throw new ServerException(ServerError.ERR_DB_UPDATE);
                }
            }
        }
    }

    public void updateAutoResult_oracle ( String requestId, String output, int flag ) throws ServerException
    {// 0:run,1:exc,2:over
        String sql = "UPDATE IEAI_AUTOSINGLE_ACT SET  IACTSTATE =?,IRESULT=?  WHERE REQID = ?";
        String sqlUpdate = "SELECT A.IRESULT FROM IEAI_AUTOSINGLE_ACT A  WHERE A.REQID=? FOR UPDATE ";

        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                PreparedStatement psUpdate = null;
                Connection con = null;
                ResultSet rs = null;
                try
                {
                    try
                    {
                        con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
                    } catch (DBException e)
                    {
                        _log.error("EngineRepository getRexecRequestNormal  3425 rows" + e.getMessage());
                        throw new RepositoryException(ServerError.ERR_DB_INIT);
                    }

                    psUpdate = con.prepareStatement(sqlUpdate);
                    psUpdate.setString(1, requestId);
                    rs = psUpdate.executeQuery();

                    CLOB clob = null;
                    while (rs.next())
                    {

                        clob = (CLOB) rs.getClob("IRESULT");
                        clob.putString(1, output.toString());

                    }

                    psUpdate = con.prepareStatement(sql);

                    psUpdate.setInt(1, flag);
                    psUpdate.setClob(2, clob);
                    psUpdate.setString(3, requestId);

                    psUpdate.executeUpdate();
                    con.commit();

                } catch (SQLException e)
                {
                    DBResource.rollback(con, ServerError.ERR_DB_UPDATE, e,
                            Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                } finally
                {
                    DBResource.closePSRS(rs, psUpdate, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                    DBResource.closePSConn(con, actStat, Thread.currentThread().getStackTrace()[1].getMethodName(),
                            _log);
                }
                _log.info("Act " + requestId + " update state successfully!");
                break;
            } catch (RepositoryException ex)
            {
                try
                {
                    DBRetryUtil.waitForNextTry(i, ex);
                } catch (RepositoryException e)
                {
                    throw new ServerException(ServerError.ERR_DB_UPDATE);
                }
            }
        }
    }

    public void insertintoAutosingleState ( String requestid, Map table, String script, String pkgInfo )
            throws RepositoryException
    {
        DbOpScheduler opScheduler = new DbOpScheduler();
        opScheduler.insertintoAutosingleState(requestid, table, script, pkgInfo);
    }

    public Map getAutoActState ( String requestid, Connection con ) throws RepositoryException
    {
        DbOpScheduler opScheduler = new DbOpScheduler();
        return opScheduler.getAutoActState(requestid);
    }

    public FtpInfo getAutoFtpInfo () throws RepositoryException
    {
        DbOpScheduler opScheduler = new DbOpScheduler();
        return opScheduler.getAutoFtpInfo();
    }

    public ActExcelToRunData getPubInfoFromDeviceTable ( Connection con ) throws RepositoryException
    {
        DbOpScheduler opScheduler = new DbOpScheduler();
        return opScheduler.getPubInfoFromDeviceTable(con);
    }

    public RemoteActivityExecRequest getRexecRequestTimeTask ( String requestId, int type )
            throws UnMarshallingException, RepositoryException
    {
        RepRemoteExecAct repAct = new RepRemoteExecAct();
        repAct.setId(requestId);
        repAct.setActId("ShellCmd");
        repAct.setActName("ShellCmd");
        repAct.setActStateDataVersion(-1);
        repAct.setAdaptorDefName("shellcmd");
        String adpDefUuid = null;
        for (int i = 0;; i++)
        {
            try
            {
                Connection con = null;
                try
                {
                    con = DBResource.getRConnection("getRexecRequestTimeTask", _log, type);
                    adpDefUuid = ProjectManager.getInstance().getLatestAdaptorInfo("shellcmd", con);// 获取接口UUID
                } catch (Exception e)
                {
                    // 失败后数据回滚
                    String method = Thread.currentThread().getStackTrace()[1].getMethodName();
                    _log.error(method + " is error at EngineRepository ", e);
                    DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConnection(con, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
        repAct.setAdaptorDefUUID(adpDefUuid);

        TimetaskIPBean ipbean = TimeTaskManager.getInstance().queryIpBean(requestId);
        String ip = "";
        int port = -1;
        String aa = ipbean.getIP();
        if (null != ipbean.getIP() && !ipbean.getIP().equals(""))
        {
            if (ipbean.getIP().contains(":"))
            {
                ip = ipbean.getIP().split(":")[0];
                port = Integer.parseInt(ipbean.getIP().split(":")[1]);
            } else
            {
                ip = ipbean.getIP();
                port = 1500;
            }
        }
        repAct.setAgentHost(ip);
        repAct.setAgentPort(port);

        repAct.setExceptionId(-1);

        TaskInstanceBean taskinsbean = TimeTaskManager.getInstance().getTaskins(requestId);
        long flowid = 1;
        if (taskinsbean.getInsId() > 0)
        {
            flowid = taskinsbean.getInsId();
        }
        repAct.setFlowId(flowid);// 表IEAI_TIMETASK_RUNTIME字段TASKINSID(实例ID)

        String flowname = "";
        CustomizationBean cbean = TimeTaskManager.getInstance().getTaskInfo(requestId);
        if (null != cbean.getTaskName() && !cbean.getTaskName().equals(""))
        {
            flowname = cbean.getTaskName();
        }
        repAct.setFlowName(flowname);

        repAct.setInputId(-1);
        repAct.setSafeFirst(true);
        repAct.setLastUpdateTime(0);
        repAct.setOutputId(-1);
        repAct.setProjectName("shell_test");
        repAct.setScopeId(162865);

        String serverip = "";
        try
        {
            serverip = Environment.getInstance().getServerIP();
        } catch (UnknownHostException e)
        {
            _log.error("EngineRepository getRexecRequestTimeTask  " + e.getMessage());
        }
        repAct.setServerHost(serverip);

        int serverPort = 8888;
        ServerEnv env = ServerEnv.getServerEnv();
        serverPort = env.getIntConfig(ServerEnv.TOM_PORT, ServerEnv.TOM_PORT_DEFAULT);
        repAct.setServerPort(serverPort);

        long starttime = 0;
        if (null != taskinsbean.getInsStartTime() && !taskinsbean.getInsStartTime().equals(""))
        {
            try
            {
                starttime = TimeTaskManager.getInstance().stringToLong(taskinsbean.getInsStartTime(),
                        "yyyy-MM-dd hh:mm:ss");
            }  catch (java.text.ParseException e)
            {
                _log.error("EngineRepository getRexecRequestTimeTask  " + e.getMessage());
            }
        }
        repAct.setStartTime(starttime);

        repAct.setStatus(2);
        repAct.setTimeout(new Long(0));
        return getFullRemoteExecAct(type, repAct);
    }

    /**
     *
     * <li>Description:Agent 升级</li>
     * <AUTHOR>
     * 2017年4月11日
     * @param requestId
     * @return
     * @throws RepositoryException
     * @throws UnMarshallingException
     * return RemoteActivityExecRequest
     */
    public RemoteActivityExecRequest getRexecRequestAgentMaintain ( String requestId, int sysType )
            throws RepositoryException, UnMarshallingException
    {
        for (int i = 0;; i++)
        {
            RepRemoteExecAct repAct = null;
            try
            {
                Connection con = null;
                PreparedStatement ps = null;
                ResultSet RexecRS = null;
                try
                {
                    con = DBManager.getInstance().getJdbcConnection(sysType);
                } catch (DBException e)
                {
                    _log.error("EngineRepository getRexecRequestAgentMaintain  2217 rows" + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_INIT);
                }
                try
                {
                    String sql = " SELECT * FROM IEAI_AGENT_MAINTAIN_TASK WHERE IREQUESTID=? ";
                    ps = con.prepareStatement(sql);
                    ps.setString(1, requestId);
                    RexecRS = ps.executeQuery();

                    while (RexecRS.next())
                    {
                        repAct = new RepRemoteExecAct();
                        repAct.setId(RexecRS.getString("IREQUESTID"));
                        repAct.setActId(RexecRS.getString("IACTID"));
                        repAct.setActName(RexecRS.getString("IACTNAME"));
                        repAct.setAdaptorDefName(RexecRS.getString("IADAPTORDEFNAME"));
                        repAct.setAdaptorDefUUID(RexecRS.getString("IADAPTORDEFUUID"));
                        repAct.setAgentHost(RexecRS.getString("IAGENTHOST"));
                        repAct.setAgentPort(RexecRS.getInt("IAGENTPORT"));
                        repAct.setSafeFirst(true);
                        repAct.setScopeId(162865);
                        repAct.setServerHost(RexecRS.getString("ISERVERHOST"));
                        repAct.setServerPort(RexecRS.getInt("ISERVERPORT"));
                        repAct.setStartTime(RexecRS.getLong("ISTARTTIME"));
                        repAct.setStatus(2);
                        repAct.setTimeout(new Long(0));
                    }
                } catch (SQLException e)
                {
                    String method = Thread.currentThread().getStackTrace()[1].getMethodName();
                    _log.error(method + " is error at EngineRepository ", e);
                    DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, RexecRS, ps, Thread.currentThread().getStackTrace()[1].getMethodName(),
                            _log);
                }

            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
            if (null == repAct || null == repAct.getId())
            {
                return new RemoteActivityExecRequest();
            }
            return getFullRemoteExecAct(sysType, repAct);
        }
    }

    public Map<String,Object> getToolsRequestByUUID ( String requestId, int sysType ) throws Exception
    {
        Map map = new HashMap<>();
        Connection con = null;
        Connection con1 = null;
        PreparedStatement ps = null;
        ResultSet RexecRS = null;
        List<String> params = new ArrayList<String>();
        try
        {
            con = DBManager.getInstance().getJdbcConnection(sysType);
            con1 = DBManager.getInstance().getJdbcConnection(Constants.IEAI_SCRIPT_SERVICE);
            String sql = "SELECT r.IID,r.ITOOL_SCRIPT_ID,r.IADAPTORDEFUUID,r.IAGENTHOST,r.IAGENTPORT,r.ISERVERIP,"
                    +" r.ISERVERPORT,r.IEXC_TIME,b.IUSER_NAME,r.N_OSTYPE,r.itool_script_type,r.ITOOL_SCRIPT_NAME,"
                    +"r.IUSER_NAME,r.N_OSTYPE,r.itool_type_id FROM IEAI_TOOLS_RESULT r left join IEAI_TOOLS_AGENT_BIND b"
                    + " on b.iresult_id=r.iid "
                    //  +"left join IEAI_TOOLS_INFO info on info.iid=r.itool_id "
                    +"where b.IUUID= ?";
            ps = con.prepareStatement(sql);
            ps.setString(1, requestId);
            RexecRS = ps.executeQuery();

            while (RexecRS.next())
            {
                map.put("IUUID", requestId);
                map.put("iscriptiId", RexecRS.getString("ITOOL_SCRIPT_ID"));
                map.put("actId", "maop");
                map.put("actName", "ShellCmd");
                map.put("adaptorDefName", "shellcmd");
                map.put("adaptorDefUUID", RexecRS.getString("IADAPTORDEFUUID"));
                map.put("agentHost", RexecRS.getString("IAGENTHOST"));
                map.put("agentPort", RexecRS.getString("IAGENTPORT"));
                map.put("safeFirst", true);
                map.put("scopeId", 162865);
                map.put("serverHost", RexecRS.getString("ISERVERIP"));
                map.put("serverPort", RexecRS.getString("ISERVERPORT"));
                map.put("startTime", RexecRS.getString("IEXC_TIME"));
                map.put("status", 2);
                map.put("timeOut", new Long(0));
                map.put("toolScriptType", RexecRS.getString("ITOOL_SCRIPT_TYPE"));
                map.put("toolScriptName", RexecRS.getString("ITOOL_SCRIPT_NAME"));
                map.put("params", "");
                map.put("iusername", RexecRS.getString("IUSER_NAME"));
                map.put("nOstype", RexecRS.getString("N_OSTYPE"));
                map.put("itoolTypeId", RexecRS.getString("ITOOL_TYPE_ID"));
                map.put("icontent", "");
                map.put("iscriptname", "");
                map.put("iscripttype", "");
                map.put("pm", " ");
                String sql2 = "";
                String pm = "  ";
                if("1".equals(RexecRS.getString("ITOOL_SCRIPT_TYPE"))){
                    PreparedStatement ps1 = null;
                    ResultSet rs1 = null;
                    PreparedStatement ps2 = null;
                    ResultSet rs2 = null;
                    try
                    {
                        String sql1 = "select a.iscriptname,a.iscripttype,a.icontent from IEAI_SCRIPT_SERVICES a where a.ISCRIPTUUID = ? ";
                        sql2 = "select IPARAM_NAME,IPARAM_VALUE from IEAI_TOOLS_RESULT_PARAM  where ITOOLS_RESULT_ID = ?";
                        ps1 = con1.prepareStatement(sql1);
                        ps1.setString(1, RexecRS.getString("ITOOL_SCRIPT_ID"));
                        rs1 = ps1.executeQuery();
                        while (rs1.next())
                        {
                            map.put("iscriptname", rs1.getString("iscriptname"));
                            map.put("icontent", rs1.getString("icontent"));
                            map.put("iscripttype", rs1.getString("iscripttype"));
                            map.put("command",rs1.getString("icontent"));
                        }
                        ps2 = con.prepareStatement(sql2);
                        ps2.setString(1, RexecRS.getString("IID"));
                        rs2 = ps2.executeQuery();
                        if ("windows".equals(RexecRS.getString("N_OSTYPE"))){
                            while (rs2.next())
                            {
                                pm += ("\""+rs2.getString("IPARAM_NAME") +"="+ (null==rs2.getString("IPARAM_VALUE")?" ":rs2.getString("IPARAM_VALUE")) +"\"" + "@@");
                            }
                        }else{
                            while (rs2.next())
                            {
                                pm += (rs2.getString("IPARAM_NAME") +"="+ (null==rs2.getString("IPARAM_VALUE")?" ":rs2.getString("IPARAM_VALUE")) + "@@");
                            }
                        }
                        if(pm.length()>2){
                            pm = pm.substring(2,pm.length()-2);
                        }
                    } catch (SQLException e)
                    {
                        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
                        _log.error(method + " is error at EngineRepository ", e);
                        DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
                    } finally
                    {
                        DBResource.closePSRS(rs1, ps1, Thread.currentThread().getStackTrace()[1].getMethodName(),
                            _log);
                        DBResource.closePSRS(rs2, ps2, Thread.currentThread().getStackTrace()[1].getMethodName(),
                            _log);
                    }
                }else{
                    map.put("command",RexecRS.getString("ITOOL_SCRIPT_NAME"));
                    sql2 = "select IPARAM_NAME,IPARAM_VALUE from IEAI_TOOLS_RESULT_PARAM where ITOOLS_RESULT_ID = ?";
                    PreparedStatement ps1 = null;
                    ResultSet rs1 = null;
                    try
                    {
                        ps1 = con.prepareStatement(sql2);
                        ps1.setString(1, RexecRS.getString("IID"));
                        rs1 = ps1.executeQuery();
                        if ("windows".equals(RexecRS.getString("N_OSTYPE"))){
                            while (rs1.next())
                            {
                                pm += ("\""+rs1.getString("IPARAM_NAME") +"="+(null==rs1.getString("IPARAM_VALUE")?" ":rs1.getString("IPARAM_VALUE")) +"\"" + "@@");
                                params.add(rs1.getString("IPARAM_NAME") +"="+(null==rs1.getString("IPARAM_VALUE")?" ":rs1.getString("IPARAM_VALUE")));
                            }
                        }else{
                            while (rs1.next())
                            {
                                pm += (rs1.getString("IPARAM_NAME") +"="+(null==rs1.getString("IPARAM_VALUE")?" ":rs1.getString("IPARAM_VALUE")) + "@@");
                                params.add(rs1.getString("IPARAM_NAME") +"="+(null==rs1.getString("IPARAM_VALUE")?" ":rs1.getString("IPARAM_VALUE")));
                            }
                        }
                        if(pm.length()>2){
                            pm = pm.substring(2,pm.length()-2);
                        }
                    } catch (SQLException e)
                    {
                        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
                        _log.error(method + " is error at EngineRepository ", e);
                        DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
                    } finally
                    {
                        DBResource.closePSRS(rs1, ps1, Thread.currentThread().getStackTrace()[1].getMethodName(),
                            _log);
                    }
                }
                map.put("pm",getPmResult(pm));
                map.put("params",params);
                break;
            }
        } catch (SQLException e)
        {
            String method = Thread.currentThread().getStackTrace()[1].getMethodName();
            _log.error(method + " is error at EngineRepository ", e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closeConn(con, RexecRS, ps, Thread.currentThread().getStackTrace()[1].getMethodName(),
                _log);
            DBResource.closeConnection(con1,Thread.currentThread().getStackTrace()[1].getMethodName(),
                    _log);
        }
        return map;
    }
    
    /**
     * <li>Description:" a=${b} b=1 c=${a}_${d}_23 d=3"</li> 
     * <AUTHOR>
     * 2022年5月23日 
     * @param str
     * return  a=1 b=1 c=1_3_23 d=3 
     */
    public static String getPmResult(String str) {
        String result = " ";
        Map<String,Object> map = new LinkedHashMap();
        if(StringUtils.isNotBlank(str)) {
            String pm1[] = str.split("@@");
            if(pm1.length>0) {
                for(String p1 : pm1) {
                    if(StringUtils.isNotBlank(p1)&&StringUtils.isNotBlank(p1.trim())) {
                        String pm2[] = p1.split("=");
                        if(pm2.length>0) {
                            map.put(pm2[0],pm2[1]);                           
                        }
                    }
                }
            }
        }
        getPmMap(map);
        for(String key : map.keySet()) {
            result += key + "=" + map.get(key).toString() + " ";
        }
        return result;
    }
    
    public static void getPmMap(Map<String,Object> map) {
        Map<String,Object> rmap = new LinkedHashMap();
        for(String key : map.keySet()) {
            String value = map.get(key).toString();
            if(value.contains("${")) {
                String b = value.substring(value.indexOf("{") + 1, value.indexOf("}"));  
                String b1 = (String)map.get(b);
                if(StringUtils.isNotBlank(b1)) {
                    map.put(key, value.replace("${"+b+"}",b1));
                }else {
                    map.put(key, value.replace("${"+b+"}",""));
                }
            }
        }
        for(String key : map.keySet()) {
            String value = map.get(key).toString();
            if(value.contains("${")) {
                getPmMap(map);
            }
        }
        
    }

    public RemoteActivityExecRequest getToolsRequest ( String requestId, int sysType )
            throws RepositoryException, UnMarshallingException
    {
        for (int i = 0;; i++)
        {
            RepRemoteExecAct repAct = null;
            try
            {
                Connection con = null;
                PreparedStatement ps = null;
                ResultSet RexecRS = null;
                try
                {
                    con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
                } catch (DBException e)
                {
                    _log.error("EngineRepository getToolsRequest  2217 rows" + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_INIT);
                }
                try
                {
                    String sql = " SELECT * FROM IEAI_TOOLS_AGENT_BIND WHERE IUUID=? ";
                    ps = con.prepareStatement(sql);
                    ps.setString(1, requestId);
                    RexecRS = ps.executeQuery();

                    while (RexecRS.next())
                    {
                        repAct = new RepRemoteExecAct();
                        repAct.setId(RexecRS.getString("IUUID"));
                        repAct.setActId("maop");
                        repAct.setActName("ShellCmd");
                        repAct.setAdaptorDefName("shellcmd");
                        repAct.setAdaptorDefUUID(RexecRS.getString("IADAPTORDEFUUID"));
                        repAct.setAgentHost(RexecRS.getString("IAGENTHOST"));
                        repAct.setAgentPort(RexecRS.getInt("IAGENTPORT"));
                        repAct.setSafeFirst(true);
                        repAct.setScopeId(162865);
                        repAct.setServerHost(RexecRS.getString("ISERVERIP"));
                        repAct.setServerPort(RexecRS.getInt("ISERVERPORT"));
                        repAct.setStartTime(RexecRS.getLong("IEXC_TIME"));
                        repAct.setStatus(2);
                        repAct.setTimeout(new Long(0));
                        break;
                    }
                } catch (SQLException e)
                {
                    String method = Thread.currentThread().getStackTrace()[1].getMethodName();
                    _log.error(method + " is error at EngineRepository ", e);
                    DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, RexecRS, ps, Thread.currentThread().getStackTrace()[1].getMethodName(),
                            _log);
                }

            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
            if (null == repAct || null == repAct.getId())
            {
                return new RemoteActivityExecRequest();
            }
            return getFullRemoteExecAct(sysType, repAct);
        }
    }

    public Map getToolsRequestCmd (String requestId) throws Exception
    {
        for (int i = 0;; i++)
        {
            Map map = new HashMap<>();
            try
            {
                Connection con = null;
                PreparedStatement ps = null;
                ResultSet RexecRS = null;
                try
                {
                    con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
                } catch (DBException e)
                {
                    _log.error("EngineRepository getToolsRequestAgentMaintain  2217 rows" + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_INIT);
                }
                try
                {
                    String sql = " SELECT * FROM IEAI_TOOLS_RESULT WHERE IUUID=? ";
                    ps = con.prepareStatement(sql);
                    ps.setString(1, requestId);
                    RexecRS = ps.executeQuery();
                    while (RexecRS.next())
                    {
                        map.put("command",RexecRS.getString("itool_result"));
                        map.put("params", "1");
                    }
                } catch (SQLException e)
                {
                    String method = Thread.currentThread().getStackTrace()[1].getMethodName();
                    _log.error(method + " is error at EngineRepository ", e);
                    DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, RexecRS, ps, Thread.currentThread().getStackTrace()[1].getMethodName(),
                            _log);
                }

            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
            return map;
        }
    }

    /**
     * <li>Description:</li>
     * <AUTHOR>
     * 2016年7月11日
     * @param requestId
     * @return
     * @throws RepositoryException
     * @throws UnMarshallingException
     * return RepRemoteExecAct
     */
    public RepRemoteExecAct getRexecRequestNormal ( String requestId )
            throws RepositoryException, UnMarshallingException
    {
        RepRemoteExecAct repAct = new RepRemoteExecAct();
        for (int i = 0;; i++)
        {
            try
            {
                Connection con = null;
                PreparedStatement ps = null;
                ResultSet RexecRS = null;
                try
                {
                    con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
                } catch (DBException e)
                {
                    _log.error("EngineRepository getRexecRequestNormal  3425 rows" + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_INIT);
                }
                try
                {
                    String sql = "select repremotee.iid iid,repremotee.iactid iactid,repremotee.iactname iactname,repremotee.iactstatedataversion iactstat,repremotee.iadaptordefname iadaptor,repremotee.iadaptordefuuid iadaptordefuuid,repremotee.iagenthost iagenthost,repremotee.iagentport iagentport,repremotee.iexceptionid iexcepti,repremotee.iflowid iflowid,repremotee.iflowname iflowname,repremotee.iinputid iinputid,repremotee.iissafefirst iissafe,repremotee.ilastupdatetime ilastup,repremotee.ioutputid ioutputid,repremotee.iprojectname iprojec,repremotee.iscopeid iscopeid,repremotee.iserverhost iserverHost,repremotee.iserverport iserverPort,repremotee.istarttime istarttime,repremotee.istatus istatus,repremotee.itimeout itimeout from ieai_remoteexecact repremotee where repremotee.iid = ?";
                    ps = con.prepareStatement(sql);
                    ps.setString(1, requestId);
                    RexecRS = ps.executeQuery();

                    while (RexecRS.next())
                    {
                        repAct.setId(RexecRS.getString("iid"));
                        repAct.setActId(RexecRS.getString("iactid"));
                        repAct.setActName(RexecRS.getString("iactname"));
                        repAct.setActStateDataVersion(RexecRS.getInt("iactstat"));
                        repAct.setAdaptorDefName(RexecRS.getString("iadaptor"));
                        repAct.setAdaptorDefUUID(RexecRS.getString("iadaptordefuuid"));
                        repAct.setAgentHost(RexecRS.getString("iagenthost"));
                        repAct.setAgentPort(RexecRS.getInt("iagentport"));
                        repAct.setExceptionId(RexecRS.getLong("iexcepti"));
                        repAct.setFlowId(RexecRS.getLong("iflowid"));
                        repAct.setFlowName(RexecRS.getString("iflowname"));
                        repAct.setInputId(RexecRS.getLong("iinputid"));
                        repAct.setSafeFirst(RexecRS.getBoolean("iissafe"));
                        repAct.setLastUpdateTime(RexecRS.getLong("ilastup"));
                        repAct.setOutputId(RexecRS.getLong("ioutputid"));
                        repAct.setProjectName(RexecRS.getString("iprojec"));
                        repAct.setScopeId(RexecRS.getLong("iscopeid"));
                        repAct.setServerHost(RexecRS.getString("iserverHost"));
                        repAct.setServerPort(RexecRS.getInt("iserverPort"));
                        repAct.setStartTime(RexecRS.getLong("istarttime"));
                        repAct.setStatus(RexecRS.getInt("istatus"));
                        repAct.setTimeout(new Long(RexecRS.getLong("itimeout")));
                    }
                } catch (SQLException e)
                {
                    String method = Thread.currentThread().getStackTrace()[1].getMethodName();
                    _log.error(method + " is error at EngineRepository ", e);
                    DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, RexecRS, ps, Thread.currentThread().getStackTrace()[1].getMethodName(),
                            _log);
                }

            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
            return repAct;
        }
    }

    /**
     * 通过reqid获取基线库活动信息
     * @Title: getRexecRequestNormal
     * @Description: 通过reqid获取基线库活动信息
     * @param requestId
     * @return
     * @throws RepositoryException
     * @throws UnMarshallingException
     * @author: Administrator
     * @date:   2019年2月27日 上午11:58:14
     */
    public RepRemoteExecAct getRexecRequestNormal ( String requestId, int sysType )
            throws RepositoryException, UnMarshallingException
    {
        RepRemoteExecAct repAct = new RepRemoteExecAct();
        for (int i = 0;; i++)
        {
            try
            {
                Connection con = null;
                PreparedStatement ps = null;
                ResultSet RexecRS = null;
                try
                {
                    con = DBManager.getInstance().getJdbcConnection(sysType);
                } catch (DBException e)
                {
                    _log.error("EngineRepository getRexecRequestNormal  3425 rows" + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_INIT);
                }
                try
                {
                    String sql = "select repremotee.iid iid,repremotee.iactid iactid,repremotee.iactname iactname,repremotee.iactstatedataversion iactstat,repremotee.iadaptordefname iadaptor,repremotee.iadaptordefuuid iadaptordefuuid,repremotee.iagenthost iagenthost,repremotee.iagentport iagentport,repremotee.iexceptionid iexcepti,repremotee.iflowid iflowid,repremotee.iflowname iflowname,repremotee.iinputid iinputid,repremotee.iissafefirst iissafe,repremotee.ilastupdatetime ilastup,repremotee.ioutputid ioutputid,repremotee.iprojectname iprojec,repremotee.iscopeid iscopeid,repremotee.iserverhost iserverHost,repremotee.iserverport iserverPort,repremotee.istarttime istarttime,repremotee.istatus istatus,repremotee.itimeout itimeout from ieai_remoteexecact repremotee where repremotee.iid = ?";
                    ps = con.prepareStatement(sql);
                    ps.setString(1, requestId);
                    RexecRS = ps.executeQuery();

                    while (RexecRS.next())
                    {
                        repAct.setId(RexecRS.getString("iid"));
                        repAct.setActId(RexecRS.getString("iactid"));
                        repAct.setActName(RexecRS.getString("iactname"));
                        repAct.setActStateDataVersion(RexecRS.getInt("iactstat"));
                        repAct.setAdaptorDefName(RexecRS.getString("iadaptor"));
                        repAct.setAdaptorDefUUID(RexecRS.getString("iadaptordefuuid"));
                        repAct.setAgentHost(RexecRS.getString("iagenthost"));
                        repAct.setAgentPort(RexecRS.getInt("iagentport"));
                        repAct.setExceptionId(RexecRS.getLong("iexcepti"));
                        repAct.setFlowId(RexecRS.getLong("iflowid"));
                        repAct.setFlowName(RexecRS.getString("iflowname"));
                        repAct.setInputId(RexecRS.getLong("iinputid"));
                        repAct.setSafeFirst(RexecRS.getBoolean("iissafe"));
                        repAct.setLastUpdateTime(RexecRS.getLong("ilastup"));
                        repAct.setOutputId(RexecRS.getLong("ioutputid"));
                        repAct.setProjectName(RexecRS.getString("iprojec"));
                        repAct.setScopeId(RexecRS.getLong("iscopeid"));
                        repAct.setServerHost(RexecRS.getString("iserverHost"));
                        repAct.setServerPort(RexecRS.getInt("iserverPort"));
                        repAct.setStartTime(RexecRS.getLong("istarttime"));
                        repAct.setStatus(RexecRS.getInt("istatus"));
                        repAct.setTimeout(new Long(RexecRS.getLong("itimeout")));
                    }
                } catch (SQLException e)
                {
                    String method = Thread.currentThread().getStackTrace()[1].getMethodName();
                    _log.error(method + " is error at EngineRepository ", e);
                    DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, RexecRS, ps, Thread.currentThread().getStackTrace()[1].getMethodName(),
                            _log);
                }

            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
            return repAct;
        }
    }

    public RemoteActivityExecRequest getRexecRequestCollect ( String requestId, int type )
            throws UnMarshallingException, RepositoryException
    {
        RepRemoteExecAct repAct = new RepRemoteExecAct();
        repAct.setId(requestId);
        repAct.setActId("ShellCmd");
        repAct.setActName("ShellCmd");
        repAct.setActStateDataVersion(-1);
        repAct.setAdaptorDefName("shellcmd");

        String adpDefUuid = null;
        for (int i = 0;; i++)
        {
            try
            {
                Connection con = null;
                try
                {
                    con = DBResource.getRConnection("getRexecRequestCollect", _log, type);
                    adpDefUuid = ProjectManager.getInstance().getLatestAdaptorInfo("shellcmd", con);// 获取接口UUID
                } catch (Exception e)
                {
                    String method = Thread.currentThread().getStackTrace()[1].getMethodName();
                    _log.error(method + " is error at EngineRepository ", e);
                    DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConnection(con, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
        repAct.setAdaptorDefUUID(adpDefUuid);

        repAct.setAgentHost("************");
        repAct.setAgentPort(1500);

        repAct.setExceptionId(-1);
        repAct.setFlowId(-1001);// 表IEAI_TIMETASK_RUNTIME字段TASKINSID(实例ID)

        String flowname = "Collect";
        repAct.setFlowName(flowname);

        repAct.setInputId(-1);
        repAct.setSafeFirst(true);
        repAct.setLastUpdateTime(0);
        repAct.setOutputId(-1);
        repAct.setProjectName("shell_test");
        repAct.setScopeId(162865);

        String serverip = "";
        try
        {
            serverip = Environment.getInstance().getServerIP();
        } catch (UnknownHostException e)
        {
            _log.error("EngineRepository getRexecRequestTimeTask  " + e.getMessage());
        }
        repAct.setServerHost(serverip);

        int serverPort = 8888;
        ServerEnv env = ServerEnv.getServerEnv();
        serverPort = env.getIntConfig(ServerEnv.TOM_PORT, ServerEnv.TOM_PORT_DEFAULT);
        repAct.setServerPort(serverPort);
        repAct.setStartTime(new Date().getTime());
        repAct.setStatus(2);
        repAct.setTimeout(new Long(0));
        return getFullRemoteExecAct(type, repAct);
    }

    private String get ( Connection con, int type ) throws RepositoryException
    {
        String ret = "";
        try
        {
            con = DBResource.getRConnection("adpDefUuid", _log, type);
            ret = ProjectManager.getInstance().getLatestAdaptorInfo("shellcmd", con);// 获取接口UUID
        } catch (Exception e)
        {
            String method = Thread.currentThread().getStackTrace()[1].getMethodName();
            _log.error(method + " is error at EngineRepository ", e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closeConnection(con, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }
        return ret;
    }

    private String adpDefUuid ( int type ) throws RepositoryException
    {
        String adpDefUuid = null;
        for (int i = 0;; i++)
        {
            try
            {
                Connection con = null;
                adpDefUuid = get(con, type);
                break;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
        return adpDefUuid;
    }

    public RemoteActivityExecRequest getRexecRequestNessus ( String requestId, int type )
            throws UnMarshallingException, RepositoryException
    {
        String adpDefUuid = adpDefUuid(type);
        RepRemoteExecAct repAct = NessusRepairHandler.getInstance().getRexecRequestNessus(requestId, adpDefUuid);
        return getFullRemoteExecAct(type, repAct);
    }

    public List getModelExecActsToRun ( String serverIp ) throws RepositoryException
    {
        DbOpScheduler opScheduler = new DbOpScheduler();
        List list = opScheduler.getModelExecActsToRun(serverIp);
        return list;
    }

    public void setModelValidExecActs ( List actList, ActExcelToRunData bean ) throws RepositoryException
    {
        DbOpScheduler opScheduler = new DbOpScheduler();
        // 獲取模版內所有步驟，產生相應的ip、參數等內容，存入運行表，生成新的實例ID，更新改bean內實例ID對應的childID未新生成的實例ID
        long newId = 0;// 与昨天那个一样没返回来新的id？我来
        try
        {
            newId = opScheduler.getModelAllActs(bean);
            // 查詢出改新實例ID下的有效活動（無依賴未運行，依賴完成自身未運行）
            opScheduler.setModelValidExecActs(actList, newId, bean.getServerIp());
        } catch (ServerException e)
        {
            _log.error(e);
            throw new RepositoryException(ServerError.ERR_DB_INSERT);
        }
    }

    public void getModelValidExecActs ( List actList, ActExcelToRunData bean ) throws RepositoryException
    {
        DbOpScheduler opScheduler = new DbOpScheduler();

        // 查詢出改新實例ID下的有效活動（無依賴未運行，依賴完成自身未運行）
        opScheduler.getModelValidExecActs(actList, bean);

    }

    /**
     * 流程启动更新启动时间和状态以及回执工作流ID <li>Description:</li>
     *
     * <AUTHOR> 2016年1月14日
     * @param stepId
     * @param flowId
     * @param state
     * @param failstate
     * @throws RepositoryException return void
     */
    public void updateExecActsStartState_withoutTime ( long stepId, long flowId, long state, long failstate )
            throws RepositoryException
    {
        DbOpScheduler opScheduler = new DbOpScheduler();
        opScheduler.updateExecActsStartState_withoutTime(stepId, flowId, state, failstate);
    }

    // 组合方案依赖校验
    public boolean getValidProject ( ActExcelToRunData bean ) throws Exception
    {
        DbOpScheduler opScheduler = new DbOpScheduler();
        return opScheduler.getValidProject(bean);
    }

    // 系统间依赖校验
    public boolean getValidProjectAndStep ( ActExcelToRunData bean ) throws Exception
    {
        DbOpScheduler opScheduler = new DbOpScheduler();
        return opScheduler.getValidProjectAndStep(bean);
    }

    // 资源组锁定校验
    public boolean getValidResourceLock ( ActExcelToRunData bean ) throws Exception
    {
        DbOpScheduler opScheduler = new DbOpScheduler();
        return opScheduler.getValidResourceLock(bean);
    }

    /**
     *
     * @Title: updateExecActForIpNoFlag
     * @Description: 失效备援修改子流程标识
     * @param execAct
     * @throws RepositoryException
     * @author: licheng_zhao
     * @date:   2017年11月24日 下午3:45:10
     */
    public void updateExecActForIpNoFlag ( ExecAct execAct ) throws RepositoryException
    {
        for (int i = 0;; i++)
        {
            try
            {
                DbOpScheduler scheduler = new DbOpScheduler();
                Connection con = null;
                String serverIp = "";
                try
                {
                    serverIp = Environment.getInstance().getServerIP();
                } catch (UnknownHostException e1)
                {
                    e1.printStackTrace();
                }
                try
                {
                    con = DBManager.getInstance().getSchedulerConnection(Constants.IEAI_IEAI_BASIC);
                } catch (DBException e)
                {
                    _log.error(" updateExecActForIp() 1976 rows " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_UPDATE);
                }
                try
                {
                    RepExecAct tempAct = RepExecAct.newInstance(execAct);
                    // update by tao_ding 09-09-04 用JDBC连接实现
                    RepActStateData tempActStateData = new RepActStateData();
                    // end update

                    tempActStateData = RepActStateData.newInstance(execAct.getActStateData());
                    if (tempActStateData.getBytedataId() != -1)
                    {
                        LobStorer.updateBinary(new Long(tempActStateData.getBytedataId()),
                                execAct.getActStateData().getBytesData(), con);
                    } else
                    {
                        if (execAct.getActStateData().getBytesData() != null)
                        {
                            Long bytesDataId = LobStorer.saveBinaryNoCommit(execAct.getActStateData().getBytesData(),
                                    con);
                            tempActStateData.setBytedataId(bytesDataId.longValue());
                        }
                    }
                    tempActStateData.setIid(execAct.getSerialNo());
                    // update by xibin_gong 10-04-07 用serverip进行关联更改
                    scheduler.updateRepActStateDataForIp(tempActStateData, execAct.getSerialNo(), con, serverIp);
                    scheduler.updateExecActForIpNoflag(tempAct, execAct.getSerialNo(), con, serverIp);
                    _log.info("recoverActInfo-recover savepenging and flowid=" + tempAct.getFlowId() + " actname="
                            + tempAct.getActName());
                    con.commit();
                    // end update
                    return;
                } catch (SQLException e)
                {
                    DBResource.rollback(con, ServerError.ERR_DB_UPDATE, e,
                            Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                } finally
                {
                    DBResource.closeConnection(con, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
    }

    /** 一体化运维******* 加入实例名称校验，相同工程名，工作流名以及实例名的工作流启动时，加入校验，页面不可以启动，工作流调用则控制台提示 by yue_sun on 2018-03-15 start **/
    // 根据工程名、工作流名、数据日期查询运行表记录
    public int getFlowInstanceState ( String proName, String workflowName, String workflowInstanxe, int dbType )
            throws RepositoryException
    {
        int listrepFlow = EngineRepositotyJdbc.getInstance().getFlowInstanceState(proName, workflowName,
                workflowInstanxe, dbType);
        return listrepFlow;
    }

    // 根据工程名、工作流名查询运行表记录
    public int getFlowInstanceStateNoConnection ( String proName, String workflowName, String workflowInstanxe,
                                                  Connection con ) throws RepositoryException
    {
        int listrepFlow = EngineRepositotyJdbc.getInstance().getFlowInstanceStateNoConnection(proName, workflowName,
                workflowInstanxe, con);
        return listrepFlow;
    }

    public int getWorkFlowState ( String proName, String workflowName, int dbType ) throws RepositoryException
    {
        int listrepFlow = EngineRepositotyJdbc.getInstance().getWorkFlowState(proName, workflowName, dbType);
        return listrepFlow;
    }

    // 根据工程名、工作流名查询运行表记录
    public int getWorkFlowStateNoConnection ( String proName, String workflowName, Connection con )
            throws RepositoryException
    {
        int listrepFlow = EngineRepositotyJdbc.getInstance().getWorkFlowStateNoConnection(proName, workflowName, con);
        return listrepFlow;
    }

    /** 一体化运维******* 加入实例名称校验，相同工程名，工作流名以及实例名的工作流启动时，加入校验，页面不可以启动，工作流调用则控制台提示 by yue_sun on 2018-03-15 end **/

    /**
     *
     * @Title: updateExecActRecoverCallNoFlag
     * @Description:失效备援修改工作流调用的标识
     * @param execAct
     * @throws RepositoryException
     * @author: licheng_zhao
     * @date:   2017年11月24日 下午3:44:26
     */
    public void updateExecActRecoverCallNoFlag ( ExecAct execAct ) throws RepositoryException
    {
        for (int i = 0;; i++)
        {
            try
            {
                DbOpScheduler scheduler = new DbOpScheduler();
                Connection con = null;
                try
                {
                    con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
                } catch (DBException e)
                {
                    _log.error(" updateExecActRecoverCall() 1720 rows " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_UPDATE);
                }
                try
                {
                    RepExecAct tempAct = RepExecAct.newInstance(execAct);
                    // update by tao_ding 09-09-04 用JDBC连接实现
                    RepActStateData tempActStateData = new RepActStateData();
                    // end update

                    tempActStateData = RepActStateData.newInstance(execAct.getActStateData());
                    if (tempActStateData.getBytedataId() != -1)
                    {
                        LobStorer.updateBinary(new Long(tempActStateData.getBytedataId()),
                                execAct.getActStateData().getBytesData(), con);
                    } else
                    {
                        if (execAct.getActStateData().getBytesData() != null)
                        {
                            Long bytesDataId = LobStorer.saveBinaryNoCommit(execAct.getActStateData().getBytesData(),
                                    con);
                            tempActStateData.setBytedataId(bytesDataId.longValue());
                        }
                    }
                    tempActStateData.setIid(execAct.getSerialNo());
                    // update by tao_ding 09-09-04 用JDBC连接实现
                    scheduler.updateRepActStateData(tempActStateData, execAct.getSerialNo(), con);
                    scheduler.updateExecActRecoverCallNoFlag(tempAct, execAct.getSerialNo(),
                            execAct.getActStateData().getLongData(), con);
                    con.commit();
                    // end update

                    _log.info("recoverActInfo savepenging and flowid=" + tempAct.getFlowId() + " actname="
                            + tempAct.getActName() + " execactid:" + execAct.getSerialNo() + " callflowid:"
                            + execAct.getActStateData().getLongData());
                    return;
                } catch (RepositoryException ex)
                {
                    throw ex;
                } catch (SQLException e)
                {
                    DBResource.rollback(con, ServerError.ERR_DB_UPDATE, e,
                            Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                } catch (Exception e)
                {
                    _log.error("updateExecActRecoverCall():" + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_UPDATE);
                } finally
                {
                    DBResource.closeConnection(con, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
    }

    /** 主线定时配置列表上载功能迁移所需方法  by yue_sun on 2018-02-26 start **/
    public long getGroupId ( String projectName, Connection conn ) throws RepositoryException
    {
        LoadBalanceRepository balance = new LoadBalanceRepository();
        long a = balance.getGroupId(projectName, conn);
        return a;
    }

    /** 主线定时配置列表上载功能迁移所需方法  by yue_sun on 2018-02-26 end **/

    /** 活动变更所需方法  by yue_sun on 2018-03-01 start **/
    /**
     * 记录修改优先级信息
     *
     * <AUTHOR> 2016-5-3
     * @param actName
     * @param pri
     * @throws RepositoryException return void
     */
    public void saveupdatePriRecord ( int pri, String ssn, String system, String userName, Long flowid, int priOld,
                                      int dbType ) throws RepositoryException
    {
        String sql6 = "INSERT INTO IEAI_UPDATEPRIRECORD (IID,IMAINPRONAME, IMAINLINENAME, ICHILDPRONAME, IACTNAME, IPRIORITY, ISYSTEM, UPDATETIME, UPDATEUSER,IPRIORITYOLD) "
                + " select ?,t.imainproname,t.imainlinename,t.ichildproname,t.iactname," + pri + "," + "'" + system
                + "'" + ",FUN_GET_DATE_NUMBER(current_timestamp,0)," + "'" + userName + "'," + priOld
                + " from ieai_excelmodel t    where t.isystem=?  and " + ssn;
        for (int i = 0;; i++)
        {
            try
            {
                Connection con = null;
                PreparedStatement actStat = null;
                try
                {
                    con = DBResource.getConnection("saveupdatePriRecord", _log, Constants.IEAI_IEAI);
                    long iid = IdGenerator.createIdNoConnection("IEAI_UPDATEPRIRECORD", Constants.IEAI_IEAI);
                    if ("".equals(system) && "".equals(ssn) && flowid > 0)
                    {
                        sql6 = "INSERT INTO IEAI_UPDATEPRIRECORD (IID,IMAINPRONAME, IMAINLINENAME, ICHILDPRONAME, IACTNAME, IPRIORITY, ISYSTEM, UPDATETIME, UPDATEUSER,IPRIORITYOLD) "
                                + " select ?,t.imainproname,t.imainlinename,t.ichildproname,t.iactname," + pri + ","
                                + "T.ISYSTEM" + ",FUN_GET_DATE_NUMBER(current_timestamp,0)," + "'" + userName + "',"
                                + priOld
                                + " from ieai_excelmodel t    where t.isystem IN (SELECT T1.ISYSTEM FROM IEAI_WORKFLOWINSTANCE T1 WHERE T1.IFLOWID=?)  and iactname in (SELECT T2.IFLOWNAME FROM IEAI_WORKFLOWINSTANCE T2 WHERE T2.IFLOWID=?) ";
                        actStat = con.prepareStatement(sql6);
                        actStat.setLong(1, iid);
                        actStat.setLong(2, flowid);
                        actStat.setLong(3, flowid);
                        actStat.executeUpdate();
                    } else
                    {
                        actStat = con.prepareStatement(sql6);
                        actStat.setLong(1, iid);
                        actStat.setString(2, system);
                        actStat.executeUpdate();
                    }
                    con.commit();
                } catch (SQLException e)
                {
                    _log.error("saveupdatePriRecord() is error2" + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closePSConn(con, actStat, "saveupdatePriRecord", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ex, ServerError.ERR_DB_QUERY);
            }
        }
    }

    /**
     * <li>Description:保存活动与工作流内活动完成数 事务处理</li>
     *
     * <AUTHOR> 2015-4-1
     * @param execAct
     * @param actElem
     * @throws RepositoryException return void
     */
    public void updateExecActForIpSuccNum ( ExecAct execAct, BasicActElement actElem, boolean forPause, int dbType )
            throws RepositoryException
    {
        execAct.setState(ExecAct.PENDING);
        for (int i = 0;; i++)
        {
            try
            {
                DbOpScheduler scheduler = new DbOpScheduler();
                Connection con = null;
                String serverIp = "";
                try
                {
                    serverIp = Environment.getInstance().getServerIP();
                } catch (UnknownHostException e1)
                {
                    e1.printStackTrace();
                }
                try
                {
                    con = DBManager.getInstance().getSchedulerConnection(dbType);
                } catch (DBException e)
                {
                    _log.error(" updateExecActForIpSuccNum() 1976 rows " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_UPDATE);
                }
                try
                {
                    RepExecAct tempAct = RepExecAct.newInstance(execAct);
                    // update by tao_ding 09-09-04 用JDBC连接实现
                    RepActStateData tempActStateData = new RepActStateData();
                    // end update

                    tempActStateData = RepActStateData.newInstance(execAct.getActStateData());
                    if (tempActStateData.getBytedataId() != -1)
                    {
                        LobStorer.updateBinary(new Long(tempActStateData.getBytedataId()),
                                execAct.getActStateData().getBytesData(), con);
                    } else
                    {
                        if (execAct.getActStateData().getBytesData() != null)
                        {
                            Long bytesDataId = LobStorer.saveBinaryNoCommit(execAct.getActStateData().getBytesData(),
                                    con);
                            tempActStateData.setBytedataId(bytesDataId.longValue());
                        }
                    }
                    tempActStateData.setIid(execAct.getSerialNo());
                    // update by xibin_gong 10-04-07 用serverip进行关联更改
                    scheduler.updateRepActStateDataForIp(tempActStateData, execAct.getSerialNo(), con, serverIp);
                    scheduler.updateExecActForIp(tempAct, execAct.getSerialNo(), con, serverIp);
                    if (!forPause)
                    {
                        EngineRepositotyJdbc.getInstance().saveActsuccNum(execAct.getFlowId(),
                                FlowEndFlagManager.getInstance().getActSuccNum(actElem.getSucceedingActs()),
                                execAct.getActId(), actElem, con);
                    }
                    con.commit();
                    // end update
                    return;
                } catch (RepositoryException ex)
                {
                    throw ex;
                } catch (SQLException e)
                {
                    DBResource.rollback(con, ServerError.ERR_DB_UPDATE, e,
                            Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                } finally
                {
                    DBResource.closeConnection(con, "updateExecActForIpSuccNum", _log);
                }
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
    }

    public void updateActStatus ( long flowid, long actid ) throws RepositoryException
    {
        String sql = "update ieai_actruntime set istate = ? where iflowid = ? and iactid = ?";
        for (int i = 0; i < 10; i++)
        {
            try
            {
                Connection con = DBResource.getConnection("updateActStatus", _log, Constants.IEAI_IEAI);
                PreparedStatement actStat = null;
                try
                {

                    actStat = con.prepareStatement(sql);
                    actStat.setString(1, "Finished");
                    actStat.setLong(2, flowid);
                    actStat.setLong(3, actid);
                    actStat.executeUpdate();
                    con.commit();
                } catch (SQLException e)
                {
                    _log.error("updateActStatus() is error2" + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closePSConn(con, actStat, "updateActStatus", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ex, ServerError.ERR_DB_QUERY);
            }
        }
    }

    /** 活动变更所需方法  by yue_sun on 2018-03-01 end **/

    /**
     * <li>Description:查询工作流是不是主线。如果 是主线直接返回 主线ID。如果不是主线 查回工作流的主线ID</li>
     *
     * <AUTHOR> 2016年10月18日
     * @param projectName
     * @param flowName
     * @param fid
     * @return
     * @throws RepositoryException return long
     * * 2018-01-31.manxi_zhao.迁移过来
     */
    public long getExcelDayStartFlowsMainLineId ( String projectName, String flowName, long fid )
            throws RepositoryException
    {
        return EngineRepositotyJdbc.getInstance().getExcelDayStartFlowsMainLineId(projectName, flowName, fid);
    }

    /**
     * <li>Description:查询工作流是不是主线。如果 是主线直接返回 主线ID。如果不是主线 查回工作流的主线ID</li>
     *
     * <AUTHOR> 2014年11月16日
     * @param projectName
     * @param flowName
     * @param fid
     * @return
     * @throws RepositoryException return long
     * 2018-01-31.manxi_zhao.迁移过来
     */
    public long getFlowsMainLineId ( String projectName, String flowName, long fid ) throws RepositoryException
    {
        return EngineRepositotyJdbc.getInstance().getFlowsMainLineId(projectName, flowName, fid);
    }

    /**
     * @Title: getShellExtTimes
     * @Description: TODO(根据flowid查询该工作流下各个步骤执行的次数)
     * @param: @param prjId
     * @param: @return
     * @param: @throws RepositoryException
     * @return: int
     * @throws
     * @author: tiejun_fan
     * @date: 2017-10-23 上午9:12:39
     * 2018-01-31.manxi_zhao.迁移过来
     */
    public int getShellExtTimes ( long flowid ) throws RepositoryException
    {
        return EngineRepositotyJdbc.getInstance().getShellExtTimes(flowid);
    }

    /**
     *
     * @Title: getTaskNum
     * @Description: 查询人工执行次数
     * @param flowid
     * @return
     * @throws RepositoryException
     * @author: junyu_zhang
     * @date:   2018年10月30日 上午9:44:10
     */
    public int getTaskNum ( long flowid ) throws RepositoryException
    {
        return EngineRepositotyJdbc.getInstance().getTaskNum(flowid);
    }

    /**
     *
     * @Title: getDisableRuleCountForExcelRow
     * @Description:  查询禁用表总记录数(Excel横向导入查询)
     * @param excelJson
     * @return
     * @throws RepositoryException
     * @author: junyu_zhang
     * @date:   2018年1月30日 上午10:10:10
     */
    public int getDisableRuleCountForExcelRow ( String excelJson ) throws RepositoryException
    {
        return EngineRepositotyJdbc.getInstance().getDisableFlowCountForExcelRow(excelJson);
    }

    /**
     *
     * @Title: getDisableRuleForExcelRow
     * @Description:查询禁用表记录(Excel横向导入查询)
     * @param pageF
     * @param pageT
     * @param excelJson
     * @return
     * @throws RepositoryException
     * @author: junyu_zhang
     * @date:   2018年1月30日 上午10:23:34
     */
    public List<DisableFlowRuleBean> getDisableRuleForExcelRow ( int pageF, int pageT, String excelJson )
            throws RepositoryException
    {
        return EngineRepositotyJdbc.getInstance().getDisableFlowRuleForExcelRow(pageF, pageT, excelJson);
    }

    /**
     *
     * @Title: getDisableRule
     * @Description: 查询禁用规则数据
     * @param pageF
     * @param pageT
     * @param pName
     * @param fName
     * @param system
     * @return
     * @throws RepositoryException
     * @author: junyu_zhang
     * @date:   2018年1月30日 上午10:42:52
     */
    public List<DisableFlowRuleBean> getDisableRule ( int pageF, int pageT, String pName, String fName, String system )
            throws RepositoryException
    {
        return EngineRepositotyJdbc.getInstance().getDisableFlowRule(pageF, pageT, pName, fName, system);
    }

    /**
     *
     * @Title: getDisableRuleCount
     * @Description: 查询禁用规则数据
     * @param pName
     * @param flowName
     * @param system
     * @return
     * @throws RepositoryException
     * @author: junyu_zhang
     * @date:   2018年1月30日 上午10:47:32
     */
    public int getDisableRuleCount ( String pName, String flowName, String system ) throws RepositoryException
    {
        return EngineRepositotyJdbc.getInstance().getDisableFlowCount(pName, flowName, system);
    }

    /**
     *
     * @Title: getRepFlowCicleInstance
     * @Description: 根据工作id查询周期表中对应记录
     * @param flowId
     * @return
     * @throws RepositoryException
     * @author: junyu_zhang
     * @date:   2018年2月1日 上午9:42:01
     */
    public RepWorkflowInstance getRepFlowCicleInstance ( long flowId ) throws RepositoryException
    {
        RepWorkflowInstance repFlow = EngineRepositotyJdbc.getInstance().getFlowCicleInstance(flowId);

        return null == repFlow ? null : repFlow;
    }

    public boolean isOnline ( String prjName, String flowName ) throws RepositoryException
    {
        boolean isOnline = true;
        String sql = "select IISONLINE from IEAI_MAINFLOW_INSTANCE where ISYSNAME=? and IMAINFLOWNAME=? ";
        for (int i = 0;; i++)
        {
            try
            {
                Connection conn = null;
                PreparedStatement ps = null;
                ResultSet rs = null;
                try
                {
                    conn = DBResource.getSchedulerConnection(Thread.currentThread().getStackTrace()[1].getMethodName(),
                            _log, Constants.IEAI_IEAI_BASIC);
                    ps = conn.prepareStatement(sql);
                    ps.setString(1, prjName);
                    ps.setString(2, flowName);
                    rs = ps.executeQuery();
                    while (rs.next())
                    {
                        int int1 = rs.getInt(1);
                        if (int1 == 0)
                        {
                            isOnline = false;
                        }
                    }

                } catch (SQLException e)
                {
                    String method = Thread.currentThread().getStackTrace()[1].getMethodName();
                    _log.error(method + " is error at EngineRepository ", e);
                    DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
        return isOnline;
    }

    public RemoteActivityExecRequest getRexecRequest ( String requestId )
            throws RepositoryException, UnMarshallingException
    {
        long startTime = System.currentTimeMillis();
        RepRemoteExecAct repAct = null;
        // synchronized (requestId)
        // {
        // update begin by yan_wang 2009 9.28
        for (int i = 0;; i++)
        {
            String sql = "";
            try
            {
                Connection con = null;
                PreparedStatement ps = null;
                ResultSet RexecRS = null;
                try
                {
                    try
                    {
                        con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
                    } catch (DBException e)
                    {
                        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
                        _log.error(method + " is error at EngineRepository ", e);
                        throw new RepositoryException(ServerError.ERR_DB_INIT);
                    }
                    sql = "select repremotee.iid iid,repremotee.iactid iactid,repremotee.iactname iactname,repremotee.iactstatedataversion iactstat,repremotee.iadaptordefname iadaptor,repremotee.iadaptordefuuid iadaptordefuuid,repremotee.iagenthost iagenthost,repremotee.iagentport iagentport,repremotee.iexceptionid iexcepti,repremotee.iflowid iflowid,repremotee.iflowname iflowname,repremotee.iinputid iinputid,repremotee.iissafefirst iissafe,repremotee.ilastupdatetime ilastup,repremotee.ioutputid ioutputid,repremotee.iprojectname iprojec,repremotee.iscopeid iscopeid,repremotee.iserverhost iserverHost,repremotee.iserverport iserverPort,repremotee.istarttime istarttime,repremotee.istatus istatus,repremotee.itimeout itimeout from ieai_remoteexecact repremotee where repremotee.iid = ?";
                    ps = con.prepareStatement(sql);
                    ps.setString(1, requestId);
                    RexecRS = ps.executeQuery();

                    while (RexecRS.next())
                    {
                        repAct = new RepRemoteExecAct();
                        repAct.setId(RexecRS.getString("iid"));
                        repAct.setActId(RexecRS.getString("iactid"));
                        repAct.setActName(RexecRS.getString("iactname"));
                        repAct.setActStateDataVersion(RexecRS.getInt("iactstat"));
                        repAct.setAdaptorDefName(RexecRS.getString("iadaptor"));
                        repAct.setAdaptorDefUUID(RexecRS.getString("iadaptordefuuid"));
                        repAct.setAgentHost(RexecRS.getString("iagenthost"));
                        repAct.setAgentPort(RexecRS.getInt("iagentport"));
                        repAct.setExceptionId(RexecRS.getLong("iexcepti"));
                        repAct.setFlowId(RexecRS.getLong("iflowid"));
                        repAct.setFlowName(RexecRS.getString("iflowname"));
                        repAct.setInputId(RexecRS.getLong("iinputid"));
                        repAct.setSafeFirst(RexecRS.getBoolean("iissafe"));
                        repAct.setLastUpdateTime(RexecRS.getLong("ilastup"));
                        repAct.setOutputId(RexecRS.getLong("ioutputid"));
                        repAct.setProjectName(RexecRS.getString("iprojec"));
                        repAct.setScopeId(RexecRS.getLong("iscopeid"));
                        repAct.setServerHost(RexecRS.getString("iserverHost"));
                        repAct.setServerPort(RexecRS.getInt("iserverPort"));
                        repAct.setStartTime(RexecRS.getLong("istarttime"));
                        repAct.setStatus(RexecRS.getInt("istatus"));
                        repAct.setTimeout(new Long(RexecRS.getLong("itimeout")));
                    }
                    break;
                } catch (SQLException e)
                {
                    String method = Thread.currentThread().getStackTrace()[1].getMethodName();
                    _log.error(method + " is error at EngineRepository ", e);
                    DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, RexecRS, ps, Thread.currentThread().getStackTrace()[1].getMethodName(),
                            _log);
                }

            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
        long endTime = System.currentTimeMillis();
        long interval = endTime - startTime;
        if (SystemConfig.isHibernateTime())
        {
            _log.info("Stamp:EngineRepository.getRexecRequest:" + interval);
        }
        if (null == repAct || null == repAct.getId())
        {
            return new RemoteActivityExecRequest();
        }
        return getFullRemoteExecAct(repAct, Constants.IEAI_IEAI_BASIC);
    }

    private RemoteActivityExecRequest getFullRemoteExecAct ( RepRemoteExecAct repRemoteAct, int dbType )
            throws RepositoryException, UnMarshallingException
    {
        Connection conn = null;
        if (repRemoteAct.getId().indexOf("agentmaintain-") > -1 || repRemoteAct.getId().indexOf("agentmonitor-") > -1
                || repRemoteAct.getId().indexOf("cm") > -1 || repRemoteAct.getId().indexOf("ieai-auto-") > -1)
        {
            RemoteActivityExecRequest remoteAct = repRemoteAct.toCommons();
            ActStateData stateData = new ActStateData();
            remoteAct.setActStateData(stateData);
            return remoteAct;
        } else
        {
            try
            {
                conn = DBResource.getSchedulerConnection("getFullRemoteExecAct", _log, dbType);
                if (SystemConfig.isOther())
                {
                    _log.info("RemoteActivityExecRequest getFullRemoteExecAct is start old:" + repRemoteAct.getId());
                }
                DbOpScheduler scheduler = new DbOpScheduler();
                List list = scheduler.getExecActId(repRemoteAct.getId());
                // _log.info("remote info step2 "+repRemoteAct.getFlowId());
                Long execActId = null;
                if (list.size() > 0)
                {
                    execActId = (Long) list.get(0);
                }
                // end update
                // _log.info("remote info step3 "+repRemoteAct.getFlowId());
                RepActStateData repStateData = scheduler.getRepActStateData(execActId.longValue());
                // _log.info("remote info step4 "+repRemoteAct.getFlowId());
                ActStateData stateData = null;
                if (repStateData != null)
                {
                    if (repStateData.getBytedataId() != -1)
                    {
                        byte[] bytes = LobStorer.getBinary(dbType, new Long(repStateData.getBytedataId()));
                        stateData = repStateData.toCommons();
                        stateData.setBytesData(bytes);
                    }
                }
                // _log.info("remote info step5 "+repRemoteAct.getFlowId());
                RemoteActivityExecRequest remoteAct = repRemoteAct.toCommons();
                remoteAct.setActStateData(stateData);
                if (repRemoteAct.getExceptionId() != -1)
                {
                    if (repRemoteAct.getAdaptorDefName().equals("aptshell"))
                    {

                    } else
                    {
                        byte[] exception =null;
                        // add by tao_ding on 20090911
                        for (int i = 0;; i++)
                        {
                            try
                            {
                                exception = LobStorer.getBinary(dbType, new Long(repRemoteAct.getExceptionId()));
                                if (null == exception || exception.length == 0)
                                {
                                    throw new RepositoryException(new ServerError());
//                                    try
//                                    {
//                                        Thread.sleep(3000);
//                                    } catch (InterruptedException e)
//                                    {
//                                    }
//                                    exception = LobStorer.getBinary(dbType, new Long(repRemoteAct.getExceptionId()));
                                } else
                                {
                                    break;
                                }
                            } catch (RepositoryException ex)
                            {
                                _log.info("select database's blob  for exception is  null! ----BlobIID:"
                                        + repRemoteAct.getExceptionId() + " ActId=" + repRemoteAct.getActId());
                                DBRetryUtil.waitForNextTry(i, ex);
                            }

                        }
                        // end
                        remoteAct.setException((Throwable) SerializationUtils.deserialize(exception));
                    }
                }
                // _log.info("remote info step6 "+repRemoteAct.getFlowId());
                if (repRemoteAct.getInputId() != -1)
                {

                    byte[] inputBytes = null;

                    // add by tao_ding on 20090911
                    for (int i = 0;; i++)
                    {
                        try
                        {
                            inputBytes = LobStorer.getBinary(dbType, new Long(repRemoteAct.getInputId()));
                            if (null == inputBytes || inputBytes.length == 0)
                            {
                                throw new RepositoryException(new ServerError());
//                                try
//                                {
//                                    Thread.sleep(3000);
//                                } catch (InterruptedException e)
//                                {
//                                }
//                                inputBytes = LobStorer.getBinary(dbType, new Long(repRemoteAct.getInputId()));
                            } else
                            {
                                break;
                            }
                        } catch (RepositoryException ex)
                        {
                            _log.info(" select database's blob  for inputBytes is  null! ----BlobIID:"
                                    + repRemoteAct.getExceptionId() + " AvctId" + repRemoteAct.getActId());
                            DBRetryUtil.waitForNextTry(i, ex);
                        }
                    }
                    // end
                    Map input = new HashMap();
                    if (!(null == inputBytes || inputBytes.length == 0))
                    {
                        input = (Map) SerializationUtils.deserialize(inputBytes);
                    }
                    // 替换 YYYYMMDD
                    String cmd = (String) input.get(Constants.SHELLCMD_COMMAND);
                    if (null != cmd && cmd.contains(Constants.SHELLCMD_YYYYMMDD)||null != cmd && cmd.contains(Constants.SHELLCMD_YYYY_MM_DD))
                    {
                        try
                        {
                            cmd = ExcelActUtil.getInstance().replaceYYYYMMDD(cmd, repRemoteAct.getFlowId(), dbType);
                            input.put(Constants.SHELLCMD_COMMAND, cmd);
                        } catch (ServerException e)
                        {
                            _log.error("转换数据日期出错，工作流ID: " + repRemoteAct.getFlowId() + "获取数据日期失败 ：-90909 ");
                            throw new RepositoryException(-90909);
                        }
                        // 调试 日志
                        // _log.info("ods_cmd=" + cmd);
                    }
                    //
                    remoteAct.setInput(new Hashtable(IDataMarshallerHelper.revertValueMap(input)));

                }
                // _log.info("remote info step7 "+repRemoteAct.getFlowId());
                if (repRemoteAct.getOutputId() != -1)
                {

                    byte[] outputBytes = LobStorer.getBinary(dbType, new Long(repRemoteAct.getOutputId()));

                    // add by tao_ding on 20090911
                    for (int i = 0;; i++)
                    {
                        try
                        {
                            if (null == outputBytes || outputBytes.length == 0)
                            {
                                try
                                {
                                    Thread.sleep(3000);
                                } catch (InterruptedException e)
                                {
                                }
                                outputBytes = LobStorer.getBinary(dbType, new Long(repRemoteAct.getOutputId()));
                            } else
                            {
                                break;
                            }
                        } catch (RepositoryException ex)
                        {
                            _log.info(" select database's blob  for outputBytes is  null! --------BlobIID:"
                                    + repRemoteAct.getExceptionId() + " AvctId" + repRemoteAct.getActId());
                            DBRetryUtil.waitForNextTry(i, ex);
                        }
                    }
                    // end
                    Map output = (Map) SerializationUtils.deserialize(outputBytes);
                    remoteAct.setOutput(new Hashtable(IDataMarshallerHelper.revertValueMap(output)));
                }
                return remoteAct;
            } catch (Exception ex)
            {
                _log.error("init getFullRemoteExecAct connection error!", ex);
                throw new RepositoryException(ServerError.ERR_DB_INIT);
            } finally
            {
                DBResource.closeConnection(conn, "", _log);
            }
        }
    }

    public void updateExecActForIpSuccNumStart ( ExecAct execAct, BasicActElement actElem ) throws RepositoryException
    {
        // execAct.setState(ExecAct.PENDING);
        for (int i = 0;; i++)
        {
            try
            {
                DbOpScheduler scheduler = new DbOpScheduler();
                Connection con = null;
                String serverIp = "";
                try
                {
                    serverIp = Environment.getInstance().getServerIP();
                } catch (UnknownHostException e1)
                {
                    e1.printStackTrace();
                }
                try
                {
                    con = DBResource.getSchedulerConnection(Thread.currentThread().getStackTrace()[1].getMethodName(),
                            _log, Constants.IEAI_IEAI_BASIC);
                    RepExecAct tempAct = RepExecAct.newInstance(execAct);
                    // update by tao_ding 09-09-04 用JDBC连接实现
                    RepActStateData tempActStateData = new RepActStateData();
                    // end update

                    tempActStateData = RepActStateData.newInstance(execAct.getActStateData());
                    if (tempActStateData.getBytedataId() != -1)
                    {
                        LobStorer.updateBinary(new Long(tempActStateData.getBytedataId()),
                                execAct.getActStateData().getBytesData(), con);
                    } else
                    {
                        if (execAct.getActStateData().getBytesData() != null)
                        {
                            Long bytesDataId = LobStorer.saveBinaryNoCommit(execAct.getActStateData().getBytesData(),
                                    con);
                            tempActStateData.setBytedataId(bytesDataId.longValue());
                        }
                    }
                    tempActStateData.setIid(execAct.getSerialNo());
                    // update by xibin_gong 10-04-07 用serverip进行关联更改
                    scheduler.updateRepActStateDataForIp(tempActStateData, execAct.getSerialNo(), con, serverIp);
                    scheduler.updateExecActForIp(tempAct, execAct.getSerialNo(), con, serverIp);
                    EngineRepositotyJdbc.getInstance().saveActsuccNum(execAct.getFlowId(),
                            FlowEndFlagManager.getInstance().getActSuccNum(actElem.getSucceedingActs()), execAct.getActId(),
                            actElem, con);
                    con.commit();
                    // end update
                    return;
                } catch (RepositoryException ex)
                {
                    throw ex;
                } catch (SQLException e)
                {
                    DBResource.rollback(con, ServerError.ERR_DB_UPDATE, e,
                            Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                } finally
                {
                    DBResource.closeConnection(con, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
    }

    /**
     * <li>Description:查询禁用规则数据根据工程名和工作流名</li>
     *
     * <AUTHOR> 2014年11月6日
     * @param pName
     * @param fName
     * @param flowid
     * @return
     * @throws RepositoryException return boolean
     */
    public boolean getDisableRuleForRun ( String pName, String fName, Long flowid, boolean isChangeRule )
            throws RepositoryException
    {
        List<DisableFlowRuleBean> list = EngineRepositotyJdbc.getInstance().getDisableRuleForRun(pName, fName, null,
                flowid, Constants.IEAI_IEAI);
        boolean flag = false;
        for (DisableFlowRuleBean rule : list)
        {
            // 指定日期范围和只一次 都没配置 该配置认为是满足禁用规则
            if (rule.getIsonlyone().equals("0") && rule.getIsApp().equals("0"))
            {
                flag = true;
            }
            if (rule.getIsonlyone().equals("1"))
            {// 只一次
                flag = true;
                try
                {
                    if (isChangeRule)
                    {
                        if (!rule.getIsApp().equals("1"))
                        {
                            EngineRepositotyJdbc.getInstance().deleteDisableRulesAct(new int[] { rule.getIid() },
                                    Constants.IEAI_IEAI);
                            _log.info("该规则项为只一次生效,该规则已经被使用。删除该规则,规则ID为" + rule.getIid());
                        }
                    }
                } catch (Exception e)
                {
                    _log.error("清除规则失败,规则ID为：" + rule.getIid() + e);
                }

                // return flag;
            }
            if (rule.getIsApp().equals("1"))
            {// 是否是指定日期范围
                int ruleInsF = Integer.parseInt(rule.getDatadatef());
                int ruleInsT = Integer.parseInt(rule.getDatadatet());
                // 在指定数据日期内
                if (rule.getFlowDateIns() == -1)
                {
                    flag = false;
                }
                if (isChangeRule)
                {
                    if (!rule.getIsonlyone().equals("0"))
                    {
                        EngineRepositotyJdbc.getInstance().updateDisablerule(rule, Constants.IEAI_IEAI);
                        _log.info("该规则项为只一次生效并且是指定日期范围的规则,将该规则的只一次规则去除,规则ID为" + rule.getIid());
                    }

                }
                if (rule.getFlowDateIns() >= ruleInsF && rule.getFlowDateIns() <= ruleInsT)
                {
                    flag = true;
                }

            }
        }
        return flag;
    }

    // add by yuxh 20180427
    public Map getprjUuidAndflowDefId ( long flowId ) throws RepositoryException
    {
        DbOpScheduler opScheduler = new DbOpScheduler();
        return opScheduler.getprjUuidAndflowDefId(flowId);
    }

    /**
     *
     * @Title: getSuccAct
     * @Description: 查询后继
     * @param pName
     * @param fName
     * @param all
     * @return
     * @throws RepositoryException
     * @author: junyu_zhang
     * @date:   2018年5月18日 下午3:57:44
     */
    public List<ActSuccBean> getSuccAct ( String pName, String fName, List<ActSuccBean> all ) throws RepositoryException
    {
        long a = System.currentTimeMillis();
        List<ActSuccBean> list = WorkflowQueryManager.getInstance().getActSUcc(pName, fName);
        _log.info("指定位置发起 查询后继检测 flowname:" + fName + "  time:" + (System.currentTimeMillis() - a));
        for (ActSuccBean act : list)
        {
            all.add(act);
            getSuccAct(act.getChildProjectName(), act.getSuccActName(), all);
        }
        return all;
    }

    /**
     *
     * @Title: loadBalanceIPforMainFlow
     * @Description: 主流程负载算法1.0
     * @param group
     * @param addrs
     * @param con
     * @return
     * @throws RepositoryException
     * @author: junyu_zhang
     * @date:   2018年5月18日 下午4:51:19
     */
    public String loadBalanceIPforMainFlow ( long group, List addrs, Connection con ) throws RepositoryException
    {
        String ip = "";

        long dateBasepollcycle = ServerEnv.getServerEnv().getMonitertimeoutpollcycle() * 1000;

        if (dateBasepollcycle < 180000)
        {
            dateBasepollcycle = 180000;
        }
        for (int t = 0; t < 10; t++)
        {
            LoadBalanceRepository balance = new LoadBalanceRepository();
            long starttime = System.currentTimeMillis();
            List b = balance.getServerList(group, addrs, con);
            _log.info("Start workflow for normal workflow is time long getServerList: "
                    + (System.currentTimeMillis() - starttime));
            long startserver = System.currentTimeMillis();
            List a = balance.getServerOfFlowforMainFlow(group, addrs, con);
            _log.info("Start workflow for normal workflow is time long getServerOfFlowforMainFlow: "
                    + (System.currentTimeMillis() - startserver));
            if (b.size() > a.size())
            {
                for (int i = 0; i < b.size(); i++)
                {
                    if (!a.contains(b.get(i)))
                    {
                        ip = (String) b.get(i);
                        long astarttime = System.currentTimeMillis();
                        if (balance.updateServerList(con, ip, dateBasepollcycle))
                        {
                            _log.info("Start workflow for normal workflow is time long a updateServerList: "
                                    + (System.currentTimeMillis() - astarttime));
                            return ip;
                        }
                    }
                }
            }
            for (int i = 0; i < a.size(); i++)
            {
                ip = (String) a.get(i);
                long bstarttime = System.currentTimeMillis();
                if (balance.updateServerList(con, ip, dateBasepollcycle))
                {
                    _log.info("Start workflow for normal workflow is time long b updateServerList: "
                            + (System.currentTimeMillis() - bstarttime));
                    return ip;
                }
            }
        }
        return ip;
    }

    /**
     *
     * @Title: getRepFlowInstance
     * @Description:根据工作id查询运行表中对应记录
     * @param flowId
     * @return
     * @throws RepositoryException
     * @author: junyu_zhang
     * @date:   2018年5月18日 下午6:01:05
     */
    public RepWorkflowInstance getRepFlowInstance ( long flowId ) throws RepositoryException
    {
        long start = System.currentTimeMillis();
        RepWorkflowInstance repFlow = EngineRepositotyJdbc.getInstance().getFlowInstance(flowId);
        if (SystemConfig.isElapsedTime())
            _log.info("Stamp:EngineRepository.getFlowInstance:" + (System.currentTimeMillis() - start));
        return null == repFlow ? null : repFlow;
    }

    public class DataBean
    {
        private Long   startTime;
        private Long   endTime;
        private Long   status;
        private String prjName;
        private String flowName;
        private String actName;
        private String instanceName;
        private Long   isDisable;
        private String groupNum;

        public String getFlowName ()
        {
            return flowName;
        }

        public void setFlowName ( String flowName )
        {
            this.flowName = flowName;
        }

        public String getGroupNum ()
        {
            return groupNum;
        }

        public void setGroupNum ( String groupNum )
        {
            this.groupNum = groupNum;
        }

        public Long getIsDisable ()
        {
            return isDisable;
        }

        public void setIsDisable ( Long isDisable )
        {
            this.isDisable = isDisable;
        }

        public void setStatus ( Long status )
        {
            this.status = status;
        }

        public void setEndTime ( Long endtime )
        {
            this.endTime = endtime;
        }

        public Long getEndTime ()
        {
            return endTime;
        }

        public Long getStatus ()
        {
            return status;
        }

        public void setStartTime ( Long startTime )
        {
            this.startTime = startTime;
        }

        public Long getStartTime ()
        {
            return startTime;
        }

        public void setPrjName ( String prjName )
        {
            this.prjName = prjName;
        }

        public String getPrjName ()
        {
            return prjName;
        }

        public void setActName ( String actName )
        {
            this.actName = actName;
        }

        public String getActName ()
        {
            return actName;
        }

        public void setInstanceName ( String instanceName )
        {
            this.instanceName = instanceName;
        }

        public String getInstanceName ()
        {
            return instanceName;
        }
    }

    public void saveExecActDataMappingOutPut ( ExecAct execAct, Map actOutput ) throws RepositoryException
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        long start = System.currentTimeMillis();
        if (actOutput.isEmpty())
        {
            return;
        }
        try
        {
            DbOpScheduler dbOpScheduler = new DbOpScheduler();
            Map tempMap = IDataMarshallerHelper.translateValueMap(actOutput);
            IEAIObject object = (IEAIObject) actOutput.get(DataHandlerActKey.SCHEMATYPENAME);
            String strAllBuss = getSchemaValue(null, null, object);
            // datamapping:0:系统名称，1:业务名称,2:工作流调用名称3:提交时间,4:状态,5:消息,6:开始时间,7:结束时间,8:数据日期
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            SimpleDateFormat sdfIns = new SimpleDateFormat("yyyy-MM-dd");
            SimpleDateFormat sdfYMD = new SimpleDateFormat("yyyyMMdd");
            Map<String, DataBean> map = new HashMap<String, DataBean>();
            Map<String, DataBean> mapup = new HashMap<String, DataBean>();
            Map<String, DataBean> mapGs = new HashMap<String, DataBean>();
            Map<String, DataBean> mapGe = new HashMap<String, DataBean>();
            Long currDate = Long.parseLong(sdfYMD.format(new Date()));
            String sql = "";
            _log.info("判断4 strAllBuss 的值是" + strAllBuss);
            if (null != strAllBuss && !"".equals(strAllBuss) && !"null".equals(strAllBuss))
            {
                boolean para5 = false;
                boolean para6 = false;
                String[] sbd = strAllBuss.split(",;");// 多个参数
                for (int j = 0; j < sbd.length; j++)
                {
                    Connection con = null;
                    try
                    {
                        con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
                        String[] sbs = sbd[j].split("#;#");// 一个参数内的多个结果值
                        for (int k = 0; k < sbs.length; k++)
                        {
                            String[] sbv = null;
                            if (null != sbs[k] && !"".equals(sbs[k]))
                            {
                                sbv = sbs[k].split(",");
                            }
                            _log.info("sbv.length is " + sbv.length + "||sbv.str=" + sbs[k]);
                            if (null != sbv && sbv.length == 9)
                            {
                                PreparedStatement ps = null;
                                PreparedStatement psins = null;
                                ResultSet rs = null;
                                long isExists = 0;
                                long status = -1;
                                String retstate = sbv[4];
                                String retstr = sbv[5];
                                try
                                {
                                    try
                                    {
                                        // datamapping:0:系统名称，1:业务名称,2:工作流调用名称,3:提交时间,4:状态,5:消息,6:开始时间,7:结束时间,8:数据日期
                                        sql = "SELECT TD.IDICNODE,TD.ISTATE,TD.IDES  FROM IEAI_TOPO_DICTIONARY TD WHERE TD.IPRJNAME=? AND TD.ISTATE=?";
                                        psins = con.prepareStatement(sql);
                                        psins.setString(1, sbv[0]);
                                        psins.setString(2, sbv[4]);
                                        rs = psins.executeQuery();
                                        while (rs.next())
                                        {
                                            if (null != rs.getString("IDICNODE") && !"".equals(rs.getString("IDICNODE"))
                                                    && !"null".equals(rs.getString("IDICNODE")))
                                            {
                                                status = rs.getLong("IDICNODE");
                                            }
                                            retstate = rs.getString("ISTATE");
                                            retstr = rs.getString("IDES");
                                            break;
                                        }
                                    } catch (Exception e)
                                    {
                                        e.printStackTrace();
                                        _log.error(" query IDICNODE is error" + e);
                                        throw e;
                                    } finally
                                    {
                                        DBResource.closePSRS(rs, psins, method, _log);
                                    }
                                    _log.info("dicnode  status " + status + "||retstate=" + retstate + "||PRJ="
                                            + sbv[0] + "||ACT=" + sbv[1]);
                                    try
                                    {
                                        sql = "SELECT COUNT(1) FROM IEAI_DATAMAPPING_OUTPUT D WHERE D.IPRJNAME=? AND D.IACTNAME=? AND D.IINSTANCENAME=?";
                                        psins = con.prepareStatement(sql);
                                        psins.setString(1, sbv[0]);
                                        psins.setString(2, sbv[1]);
                                        psins.setString(3, sbv[8]);
                                        rs = psins.executeQuery();
                                        while (rs.next())
                                        {
                                            isExists = rs.getLong(1);
                                        }
                                    } catch (Exception e)
                                    {
                                        _log.error(" query IDICNODE is error", e);
                                        throw e;
                                    } finally
                                    {
                                        DBResource.closePSRS(rs, psins, method, _log);
                                    }
                                    _log.info(
                                            " isExists is 0:" + sbv[0] + " 1:" + sbv[1] + " 8:" + sbv[8] + " " + isExists);
                                    long isDisable = 0;
                                    Long sTime = 0l;
                                    Long eTime = 0l;
                                    if ("DIS".equals(sbv[4]))
                                    {// 禁用状态，标识完成
                                        status = -2;
                                    }
                                    if (isExists > 0)
                                    {
                                        // update
                                        // datamapping:0:系统名称，1:业务名称,2:工作流调用名称,3:提交时间,4:状态,5:消息,6:开始时间,7:结束时间,8:数据日期
                                        sql = " UPDATE  IEAI_DATAMAPPING_OUTPUT SET  IENDTIME=? ,ISTATE=? ,IMSG=?,ISTATE_DIC=?,IMSG_DIC=? WHERE IPRJNAME=?  AND IACTNAME=?  AND IINSTANCENAME=? ";
                                        // 第一个mapping，插入数据
                                        Long endTime = 0l;
                                        if (null != sbv[0] && !"".equals(sbv[0]) && !"null".equals(sbv[0])
                                                && null != sbv[1] && !"".equals(sbv[1]) && !"null".equals(sbv[1])
                                                && null != sbv[8] && !"".equals(sbv[8]) && !"null".equals(sbv[8]))
                                        {
                                            DataBean bean = null;
                                            // if (null != sbv[5] && !"".equals(sbv[5]) &&
                                            // !"null".equals(sbv[5]))
                                            // {
                                            // Date date = sdf.parse(sbv[5]);
                                            // sTime = date.getTime();
                                            // }
                                            if (null != sbv[6] && !"".equals(sbv[6]) && !"null".equals(sbv[6]))
                                            {
                                                Date date = sdf.parse(sbv[6]);
                                                sTime = date.getTime();
                                            }
                                            if (null != sbv[7] && !"".equals(sbv[7]) && !"null".equals(sbv[7]))
                                            {
                                                Date date = sdf.parse(sbv[7]);
                                                eTime = date.getTime();
                                            }
                                            String key = ",," + sbv[0] + "--ieai--" + sbv[1] + "--ieai--" + sbv[8]
                                                    + ",,";
                                            if (mapup.containsKey(key))
                                            {
                                                bean = (DataBean) mapup.get(key);
                                                if (bean.getEndTime() < eTime)
                                                {
                                                    bean.setEndTime(eTime);
                                                }
                                                if (bean.getStartTime() == 0)
                                                {
                                                    bean.setStartTime(sTime);
                                                }
                                                mapup.put(key, bean);
                                            } else
                                            {
                                                if ("DIS".equals(sbv[4]))
                                                {
                                                    isDisable = 1;
                                                    status = -2;
                                                }
                                                bean = new DataBean();
                                                bean.setEndTime(eTime);
                                                bean.setStartTime(sTime);
                                                bean.setPrjName(sbv[0]);
                                                bean.setActName(sbv[1]);
                                                bean.setStatus(status);
                                                bean.setIsDisable(isDisable);
                                                bean.setInstanceName(sbv[8]);
                                                mapup.put(key, bean);
                                            }
                                            _log.info(" update 1:" + eTime + " 2:" + status + " 3:" + sbv[5] + " 4:"
                                                    + retstate + " 5:" + retstr + " 6:" + sbv[0] + " 7:" + sbv[1]
                                                    + " 8:" + sbv[8]);
                                            try
                                            {
                                                ps = con.prepareStatement(sql);
                                                ps.setLong(1, eTime);
                                                ps.setLong(2, status);
                                                ps.setString(3, sbv[5]);
                                                ps.setString(4, retstate);
                                                ps.setString(5, retstr);
                                                ps.setString(6, sbv[0]);
                                                ps.setString(7, sbv[1]);
                                                ps.setString(8, sbv[8]);
                                                ps.executeUpdate();
                                            } catch (Exception e)
                                            {
                                                _log.error(" query IDICNODE is error", e);
                                                throw e;
                                            } finally
                                            {
                                                DBResource.closePreparedStatement(ps, method, _log);
                                            }
                                        }
                                    } else
                                    {
                                        // insert
                                        sql = " INSERT INTO IEAI_DATAMAPPING_OUTPUT(IID,IPRJNAME,IACTNAME,ISTARTTIME,IENDTIME,ISTATE,IMSG,IFLOWID,IINSTANCENAME,ISUBMITTIME,ISTATE_DIC,IMSG_DIC,IACTEXECDATE,ICALLFLOWNAME) VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
                                        // datamapping:0:系统名称，1:业务名称,2:工作流调用名称,3:提交时间,4:状态,5:消息,6:开始时间,7:结束时间,8:数据日期
                                        if (null != sbv[0] && !"".equals(sbv[0]) && !"null".equals(sbv[0])
                                                && null != sbv[1] && !"".equals(sbv[1]) && !"null".equals(sbv[1])
                                                && null != sbv[6] && !"".equals(sbv[6]) && !"null".equals(sbv[6])
                                                && null != sbv[8] && !"".equals(sbv[8]) && !"null".equals(sbv[8]))
                                        {
                                            DataBean bean = null;
                                            Date date = sdf.parse(sbv[6]);
                                            sTime = date.getTime();

                                            if (null != sbv[7] && !"".equals(sbv[7]) && !"null".equals(sbv[7]))
                                            {
                                                Date date1 = sdf.parse(sbv[7]);
                                                eTime = date1.getTime();
                                            }
                                            String key = ",," + sbv[0] + "--ieai--" + sbv[1] + "--ieai--" + sbv[8]
                                                    + ",,";
                                            if (map.containsKey(key))
                                            {
                                                bean = (DataBean) map.get(key);
                                                if (bean.getStartTime() > sTime)
                                                {
                                                    bean.setStartTime(sTime);
                                                }
                                                if (bean.getEndTime() == 0)
                                                {
                                                    bean.setEndTime(eTime);
                                                }
                                                map.put(key, bean);
                                            } else
                                            {
                                                if ("DIS".equals(sbv[4]))
                                                {
                                                    isDisable = 1;
                                                    status = -2;
                                                }
                                                bean = new DataBean();
                                                bean.setEndTime(eTime);
                                                bean.setStartTime(sTime);
                                                bean.setPrjName(sbv[0]);
                                                bean.setActName(sbv[1]);
                                                bean.setStatus(status);
                                                bean.setInstanceName(sbv[8]);
                                                bean.setIsDisable(isDisable);
                                                map.put(key, bean);
                                            }
                                            _log.info(" insert 1:iid 2:" + sbv[0] + " 3:" + sbv[1] + " 4:" + sTime
                                                    + " 5:" + eTime + " 6:" + status + " 7:" + sbv[5] + " 8:"
                                                    + execAct.getFlowId() + " 9:" + sbv[8] + " 10:" + sbv[3] + " 11:"
                                                    + retstate + " 12:" + retstr + " 13:" + currDate + " 14:" + sbv[2]);
                                            try
                                            {
                                                ps = con.prepareStatement(sql);
                                                ps.setLong(1, IdGenerator.createId("IEAI_DATAMAPPING_OUTPUT", con));
                                                ps.setString(2, sbv[0]);
                                                ps.setString(3, sbv[1]);
                                                ps.setLong(4, sTime);
                                                ps.setLong(5, eTime);
                                                ps.setLong(6, status);
                                                ps.setString(7, sbv[5]);
                                                ps.setLong(8, execAct.getFlowId());
                                                ps.setString(9, sbv[8]);
                                                ps.setString(10, sbv[3]);
                                                ps.setString(11, retstate);
                                                ps.setString(12, retstr);
                                                ps.setLong(13, currDate);
                                                ps.setString(14, sbv[2]);
                                                ps.executeUpdate();
                                            } catch (Exception e)
                                            {
                                                _log.error(" query IDICNODE is error", e);
                                                throw e;
                                            } finally
                                            {
                                                DBResource.closePreparedStatement(ps, method, _log);
                                            }
                                        }
                                    }
                                    if (status != -1)
                                    {
                                        if (status == 2 || status == -2)
                                        {
                                            updateDataMappingDataToGroupEnd(mapGe, sbv, status, retstate, retstr,
                                                    execAct.getFlowId(), currDate, con);
                                        } else
                                        {
                                            // 更新组开始时间
                                            updateDataMappingDataToGroupStart(mapGs, sbv, status, retstate, retstr,
                                                    execAct.getFlowId(), currDate, con);
                                        }
                                    }
                                    if (status != -1 && status != 0 && status != 2 && status != -2)
                                    {
                                        saveDataMappingDataToException(sbv, status, retstate, retstr,
                                                execAct.getFlowId(), currDate);
                                        // /**
                                        // * 如果状态为1 工作流处于运行状态 修改警告状态为1 同时将该数据插入警告历史表中
                                        // */
                                        // updateWarningState(sbv, status, retstate, retstr,
                                        // execAct.getFlowId(), currDate);
                                        // insertWarningHistory(sbv, status, retstate, retstr,
                                        // execAct.getFlowId(), currDate);

                                    } else
                                    {
                                        if (status == 2 || status == -2 || status == 0)
                                        {
                                            // 消除报警记录
                                            saveDataMappingDataFromExceptionToCancel(sbv, status, retstate, retstr,
                                                    execAct.getFlowId(), currDate);
                                            insertWarningHistory(sbv, status, retstate, retstr, execAct.getFlowId(),
                                                    currDate);
                                        }
                                    }
                                } catch (Exception e)
                                {
                                    con.rollback();
                                    _log.info("saveExecActDataMappingOutPut save  is error" + e);
                                } finally
                                {
                                    try
                                    {
                                        DBResource.closePreparedStatement(ps, "saveExecActDataMappingOutPut", _log);
                                        DBResource.closePreparedStatement(psins, "saveExecActDataMappingOutPut", _log);
                                    } catch (Exception e)
                                    {
                                        _log.info("saveExecActDataMappingOutPut ps close is error" + e);
                                    }
                                }
                            }
                        }
                        con.commit();
                    } catch (Exception e)
                    {
                        con.rollback();
                        _log.error(" saveExecActDataMappingOutPut 8536 rows " + e.getMessage(), e);
                        throw new RepositoryException(ServerError.ERR_DB_INIT);
                    } finally
                    {
                        DBResource.closeConnection(con, "saveExecActDataMappingOutPut", _log);
                    }
                }
                // 每次返回的信息，只能滿足一种情况才会入库
                if (null != map && map.size() > 0)
                {
                    // 更新开始时间
                    PreparedStatement ps = null;
                    PreparedStatement psins = null;
                    Connection con1 = null;

                    for (Entry<String, DataBean> str : map.entrySet())
                    {
                        try
                        {
                            con1 = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
                        } catch (Exception e)
                        {
                            _log.error(" saveExecActDataMappingOutPut 8551 rows " + e.getMessage());
                            throw new RepositoryException(ServerError.ERR_DB_INIT);
                        }
                        try
                        {
                            // sql = " UPDATE IEAI_ACT_TOPO_INSTANCE T SET
                            // T.IREALBEGINTIME=?,t.IRUNSTATE=?,T.IACTSTATE=0,T.IISFAIL=? WHERE
                            // T.IPROJECTNAME=? AND T.IFLOWNAME=(SELECT A.IACTNAME FROM
                            // IEAI_CALLWORKFLOW_INFO C ,IEAI_ACTRUNTIME A WHERE C.IMAINFLOWID =
                            // A.IFLOWID AND C.ICALLFLOWACTID=A.IACTID AND C.ICALLFLOWID=?) AND
                            // T.IDATADATE=? ";
                            sql = " UPDATE IEAI_ACT_TOPO_INSTANCE T SET T.IREALBEGINTIME=?,t.IRUNSTATE=?,T.IACTSTATE=0,T.IISFAIL=? WHERE T.IPROJECTNAME=? AND T.IACTNAME=? AND  T.IDATADATE=? ";
                            DataBean bean = str.getValue();
                            if (2 == bean.getStatus() || -2 == bean.getStatus())
                            {
                                sql = " UPDATE IEAI_ACT_TOPO_INSTANCE T SET T.IREALBEGINTIME=?,t.IRUNSTATE=?,T.IACTSTATE=?,T.IREALENDTIME=?,T.IISFAIL=0,t.ITIMEOUTNUM=?,t.IFIRSTTIMEOUTTIME=TO_NUMBER(SYSDATE - TO_DATE('1970-01-01 8:0:0', 'YYYY-MM-DD HH24:MI:SS')) * 24 * 60 * 60 * 1000  WHERE T.IPROJECTNAME=? AND T.IACTNAME=? AND  T.IDATADATE=? ";
                            } else
                            {
                                sql = " UPDATE IEAI_ACT_TOPO_INSTANCE T SET T.IREALBEGINTIME=?,t.IRUNSTATE=?,T.IACTSTATE=?,T.IISFAIL=?,t.ITIMEOUTNUM=?,t.IFIRSTTIMEOUTTIME=TO_NUMBER(SYSDATE - TO_DATE('1970-01-01 8:0:0', 'YYYY-MM-DD HH24:MI:SS')) * 24 * 60 * 60 * 1000  WHERE T.IPROJECTNAME=? AND T.IACTNAME=? AND  T.IDATADATE=? ";
                            }
                            psins = con1.prepareStatement(sql);
                            psins.setLong(1, bean.getStartTime());
                            psins.setLong(2, bean.getStatus());
                            if (2 == bean.getStatus())
                            {
                                psins.setString(3, "2");
                            } else if (-2 == bean.getStatus())
                            {
                                psins.setString(3, "-2");
                            } else if (-1 == bean.getStatus())
                            {
                                psins.setString(3, "1");
                            } else
                            {
                                psins.setString(3, "0");
                            }
                            if (2 == bean.getStatus() || -2 == bean.getStatus())
                            {
                                psins.setLong(4, bean.getEndTime());
                            } else
                            {
                                if (1 == bean.getStatus())
                                {
                                    psins.setLong(4, bean.getStatus());
                                } else
                                {
                                    psins.setLong(4, 0);
                                }
                            }
                            if (3 == bean.getStatus())
                            {
                                psins.setLong(5, 1);
                            } else
                            {
                                psins.setLong(5, 0);
                            }
                            psins.setString(6, bean.getPrjName());
                            psins.setString(7, bean.getActName());
                            psins.setString(8, bean.getInstanceName());
                            psins.executeUpdate();
                            con1.commit();
                        } catch (Exception e)
                        {
                            con1.rollback();
                            _log.info("map UPDATE wf or aci is error" + e);
                        } finally
                        {
                            DBResource.closePreparedStatement(ps, "map UPDATE", _log);
                            DBResource.closePreparedStatement(psins, "map UPDATE", _log);
                            DBResource.closeConnection(con1, "map UPDATE", _log);
                        }
                    }
                }
                if (null != mapup && mapup.size() > 0)
                {
                    // 更新结束时间和状态
                    for (Entry<String, DataBean> str : mapup.entrySet())
                    {
                        PreparedStatement ps = null;
                        PreparedStatement psins = null;
                        ResultSet rs = null;
                        Connection con1 = null;
                        try
                        {
                            con1 = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
                        } catch (Exception e)
                        {
                            _log.error(" saveExecActDataMappingOutPut 8551 rows " + e.getMessage());
                            throw new RepositoryException(ServerError.ERR_DB_INIT);
                        }
                        try
                        {
                            DataBean bean = str.getValue();
                            long startT = 0;
                            sql = " select IREALBEGINTIME from  IEAI_ACT_TOPO_INSTANCE T WHERE T.IPROJECTNAME=? AND T.IACTNAME=? AND  T.IDATADATE=?";
                            ps = con1.prepareStatement(sql);
                            ps.setString(1, bean.getPrjName());
                            ps.setString(2, bean.getActName());
                            ps.setString(3, bean.getInstanceName());
                            rs = ps.executeQuery();
                            while (rs.next())
                            {
                                startT = rs.getLong("IREALBEGINTIME");
                            }
                            if (startT == 0)
                            {
                                sql = " UPDATE IEAI_ACT_TOPO_INSTANCE T SET T.IREALENDTIME=? ,t.IRUNSTATE=?,T.IISFAIL=?,T.IACTSTATE=? ,ITIMEOUTNUM=?,IREALBEGINTIME=? "
                                        + "  WHERE T.IPROJECTNAME=? AND T.IACTNAME=? AND  T.IDATADATE=?";
                            } else
                            {
                                sql = " UPDATE IEAI_ACT_TOPO_INSTANCE T SET T.IREALENDTIME=? ,t.IRUNSTATE=?,T.IISFAIL=?,T.IACTSTATE=? ,ITIMEOUTNUM=? "
                                        + "  WHERE T.IPROJECTNAME=? AND T.IACTNAME=? AND  T.IDATADATE=?";
                            }
                            // if (3 == bean.getStatus())
                            // {
                            // sql = " UPDATE IEAI_ACT_TOPO_INSTANCE T SET T.IREALENDTIME=?
                            // ,t.IRUNSTATE=?,T.IISFAIL=?,T.IACTSTATE=? ,ITIMEOUTNUM=?"
                            // + "WHERE T.IPROJECTNAME=? AND T.IACTNAME=? AND T.IDATADATE=?";
                            // }
                            psins = con1.prepareStatement(sql);
                            psins.setLong(1, bean.getEndTime());
                            psins.setLong(2, bean.getStatus());
                            if (1 == bean.getStatus())
                            {
                                psins.setLong(3, bean.getStatus());
                            } else
                            {
                                psins.setLong(3, 0);
                            }
                            if (-2 == bean.getStatus())
                            {
                                psins.setString(4, "-2");
                            } else if (1 == bean.getStatus())
                            {
                                psins.setString(4, "0");
                            } else if (3 == bean.getStatus())
                            {
                                psins.setString(4, "0");
                            } else if (0 == bean.getStatus())
                            {
                                psins.setString(4, "0");
                            } else if (-1 == bean.getStatus())
                            {
                                psins.setString(4, "1");
                            } else
                            {
                                psins.setString(4, "2");
                            }
                            if (3 == bean.getStatus())
                            {
                                psins.setLong(5, 1);
                            } else
                            {
                                psins.setLong(5, 0);
                            }
                            if (startT == 0)
                            {
                                psins.setLong(6, bean.getStartTime());
                                psins.setString(7, bean.getPrjName());
                                psins.setString(8, bean.getActName());
                                psins.setString(9, bean.getInstanceName());
                            } else
                            {
                                psins.setString(6, bean.getPrjName());
                                psins.setString(7, bean.getActName());
                                psins.setString(8, bean.getInstanceName());
                            }
                            psins.executeUpdate();
                            con1.commit();
                        } catch (Exception e)
                        {
                            con1.rollback();
                            _log.info("mapup UPDATE wf or aci is error" + e);
                        } finally
                        {
                            DBResource.closePreparedStatement(ps, "mapup UPDATE", _log);
                            DBResource.closePreparedStatement(psins, "mapup UPDATE", _log);
                            DBResource.closeConnection(con1, "mapup UPDATE", _log);
                        }
                    }
                }

                if (null != mapGs && mapGs.size() > 0)
                {
                    Connection con = null;
                    try
                    {
                        con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
                        // 更新start时间和状态
                        try
                        {
                            for (Entry<String, DataBean> str : mapGs.entrySet())
                            {
                                PreparedStatement psins = null;
                                try
                                {
                                    DataBean bean = str.getValue();
                                    if (bean.getStatus() == 2)
                                    {
                                        if (null != bean.getStartTime() && !"".equals(bean.getStartTime())
                                                && !"null".equals(bean.getStartTime()))
                                        {
                                            sql = " UPDATE IEAI_TOPOINFO_BUSS_G_INS GI SET GI.ISTATE=?,GI.IENTTIME=? WHERE GI.IPGROUPNUM=?  AND GI.IDATADATE=?";
                                            psins = con.prepareStatement(sql);
                                            psins.setLong(1, bean.getStatus());
                                            psins.setLong(2, bean.getEndTime());
                                            psins.setString(3, bean.getGroupNum());
                                            psins.setString(4, bean.getInstanceName());
                                        } else
                                        {
                                            sql = " UPDATE IEAI_TOPOINFO_BUSS_G_INS GI SET GI.ISTARTTIME=? , GI.ISTATE=?,GI.IENTTIME=? WHERE GI.IPGROUPNUM=?  AND GI.IDATADATE=?";
                                            psins = con.prepareStatement(sql);
                                            psins.setLong(1, bean.getStartTime());
                                            psins.setLong(2, bean.getStatus());
                                            psins.setLong(3, bean.getEndTime());
                                            psins.setString(4, bean.getGroupNum());
                                            psins.setString(5, bean.getInstanceName());
                                        }

                                    } else
                                    {
                                        sql = " UPDATE IEAI_TOPOINFO_BUSS_G_INS GI SET GI.ISTARTTIME=? , GI.ISTATE=? WHERE GI.IPGROUPNUM=?  AND GI.IDATADATE=?";
                                        psins = con.prepareStatement(sql);
                                        psins.setLong(1, bean.getStartTime());
                                        psins.setLong(2, bean.getStatus());
                                        psins.setString(3, bean.getGroupNum());
                                        psins.setString(4, bean.getInstanceName());
                                    }
                                    psins.executeUpdate();
                                } catch (Exception e)
                                {
                                    con.rollback();
                                    _log.info("mapGs UPDATE wf or aci is error" + e);
                                } finally
                                {
                                    DBResource.closePreparedStatement(psins, "mapGs UPDATE", _log);
                                }
                            }
                            con.commit();
                        } catch (Exception e)
                        {
                            _log.info("mapGs UPDATE wf or aci is error", e);
                            throw e;
                        }
                    } catch (Exception e)
                    {
                        _log.info("mapGs UPDATE wf or aci is error" + e);
                    } finally
                    {
                        DBResource.closeConnection(con, "mapGs UPDATE", _log);
                    }
                }
                if (null != mapGe && mapGe.size() > 0)
                {
                    Connection con = null;
                    try
                    {
                        con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
                        // 更新end时间和状态
                        // 查询组内是否存在异常，存在则未完成，不存在，看是否全部完成，全部完成，则更新组状态为结束。
                        for (Entry<String, DataBean> str : mapGe.entrySet())
                        {
                            PreparedStatement psins = null;
                            ResultSet rs = null;
                            PreparedStatement ps = null;
                            ResultSet rs1 = null;
                            PreparedStatement ps1 = null;
                            DataBean bean = str.getValue();
                            try
                            {
                                long exceptionNum = 0;
                                int state = 0;
                                // String dataDate =
                                // sdfYMD.format(sdfIns.parse(bean.getInstanceName()));
                                String sqlquery = "SELECT COUNT(T.IID) FROM IEAI_DATAMAPPING_OUTPUT T,(SELECT * FROM IEAI_TOPOINFO_BUSS TB WHERE TB.IPGROUPNUM=?) M  WHERE M.SYSTEMNAME=T.IPRJNAME AND M.TASKNAME=T.IACTNAME AND T.ISTATE=1 AND T.IINSTANCENAME=?";
                                ps = con.prepareStatement(sqlquery);
                                ps.setString(1, bean.getGroupNum());
                                ps.setString(2, bean.getInstanceName());
                                rs = ps.executeQuery();
                                while (rs.next())
                                {
                                    exceptionNum = rs.getLong(1);
                                }
                                if (exceptionNum == 0)
                                {
                                    sqlquery = "SELECT (SELECT COUNT(TB.IID) FROM IEAI_TOPOINFO_BUSS TB WHERE TB.IPGROUPNUM = ?), (SELECT COUNT(T.IID) FROM IEAI_DATAMAPPING_OUTPUT T, (SELECT * FROM IEAI_TOPOINFO_BUSS TB WHERE TB.IPGROUPNUM = ?) M WHERE M.SYSTEMNAME = T.IPRJNAME AND M.TASKNAME = T.IACTNAME AND T.ISTATE = 2 AND T.IINSTANCENAME = ?) FROM DUAL";
                                    ps1 = con.prepareStatement(sqlquery);
                                    ps1.setString(1, bean.getGroupNum());
                                    ps1.setString(2, bean.getGroupNum());
                                    ps1.setString(3, bean.getInstanceName());
                                    rs1 = ps1.executeQuery();
                                    while (rs1.next())
                                    {
                                        if (rs1.getLong(1) != 0 && rs1.getLong(1) == rs1.getLong(2))
                                        {
                                            state = 2;
                                        }
                                    }
                                } else
                                {
                                    state = 1;
                                }
                                if (state == 2)
                                {
                                    Long count = 0l;
                                    Long startTime = 0l;
                                    sql = "SELECT COUNT(*),GI.ISTARTTIME FROM IEAI_TOPOINFO_BUSS_G_INS  GI WHERE GI.IPGROUPNUM=?  AND GI.IDATADATE=?  GROUP BY GI.ISTARTTIME";
                                    psins = con.prepareStatement(sql);
                                    psins.setString(1, bean.getGroupNum());
                                    psins.setString(2, bean.getInstanceName());
                                    rs = psins.executeQuery();
                                    while (rs.next())
                                    {
                                        count = rs.getLong(1);
                                        if (count != 0 && null != rs.getString("ISTARTTIME")
                                                && !"".equals(rs.getString("ISTARTTIME"))
                                                && !"null".equals(rs.getString("ISTARTTIME")))
                                        {
                                            startTime = rs.getLong("ISTARTTIME");
                                        }
                                    }
                                    if (count > 0)
                                    {
                                        if (null == bean.getStartTime() || "".equals(bean.getStartTime()))
                                        {
                                            bean.setStartTime(startTime);
                                        } else if (null != bean.getStartTime() && bean.getStartTime() != 0
                                                && startTime > bean.getStartTime())
                                        {
                                            bean.setStartTime(startTime);
                                        }
                                        sql = " UPDATE IEAI_TOPOINFO_BUSS_G_INS GI SET GI.ISTARTTIME=?,GI.IENDTIME=? , GI.ISTATE=? WHERE GI.IPGROUPNUM=?  AND GI.IDATADATE=?";
                                        psins = con.prepareStatement(sql);
                                        psins.setLong(1, bean.getStartTime());
                                        psins.setLong(2, bean.getEndTime());
                                        psins.setLong(3, state);
                                        psins.setString(4, bean.getGroupNum());
                                        psins.setString(5, bean.getInstanceName());
                                    } else
                                    {
                                        sql = " UPDATE IEAI_TOPOINFO_BUSS_G_INS GI SET GI.IENDTIME=? , GI.ISTATE=? WHERE GI.IPGROUPNUM=?  AND GI.IDATADATE=?";
                                        psins = con.prepareStatement(sql);
                                        psins.setLong(1, bean.getEndTime());
                                        psins.setLong(2, state);
                                        psins.setString(3, bean.getGroupNum());
                                        psins.setString(4, bean.getInstanceName());
                                    }

                                } else
                                {
                                    sql = " UPDATE IEAI_TOPOINFO_BUSS_G_INS GI SET GI.ISTATE=? WHERE GI.IPGROUPNUM=?  AND GI.IDATADATE=?";
                                    psins = con.prepareStatement(sql);
                                    psins.setLong(1, state);
                                    psins.setString(2, bean.getGroupNum());
                                    psins.setString(3, bean.getInstanceName());
                                }
                                psins.executeUpdate();
                            } catch (Exception e)
                            {
                                con.rollback();
                                _log.info("mapGe UPDATE wf or aci is error" + e);
                            } finally
                            {
                                DBResource.closePSRS(rs, ps, "mapGe UPDATE", _log);
                                DBResource.closePSRS(rs1, psins, "mapGe UPDATE", _log);
                                DBResource.closePreparedStatement(ps1, "mapGe UPDATE", _log);
                            }
                            this.updateCallFlowStatus(bean.getPrjName(), bean.getFlowName(), bean.getGroupNum(),
                                    bean.getInstanceName(), con);
                        }
                        con.commit();
                    } catch (Exception e)
                    {
                        con.rollback();
                        _log.info("mapGs UPDATE wf or aci is error", e);
                    } finally
                    {
                        DBResource.closeConnection(con, "mapGs UPDATE", _log);
                    }
                    // 查询当前组的开始和结束时间最为流程的开始和结束时间，更新流程表，触发工作流调用继续向后跑。
                    // Engine.getInstance().updateFlowState(0, Constants.STATE_FINISHED);
                }

            }
        } catch (Exception e)
        {
            _log.info("saveOrUpdateActOutput is err", e);
        }
        long starte = System.currentTimeMillis();
        _log.info("saveOrUpdateActOutput dataMapping times:" + (starte - start));
        return;
    }

    private String getSchemaValue ( StringBuffer strBuf, Count count, IEAIObject object )
    {
        if (null == strBuf)
        {
            strBuf = new StringBuffer();
        }
        if (null == count)
        {
            count = new Count();
        }
        String nodePath = "";
        Object nodeValue;
        Object arrayItem;
        for (int i = 0; i < object.getChildCount(); i++)
        {
            String indexName = object.getNameByIndex(i);
            Object valueObj = (Object) object.get(i);
            if (valueObj != null && valueObj.toString().indexOf('[') != -1)
            {
                arrayToString(strBuf, valueObj);
            } else if (valueObj instanceof MapObject)
            {
                mapToString(strBuf, (MapObject) valueObj);
            } else if (valueObj instanceof IEAIObject)
            {
                iealObjectTostring(strBuf, (IEAIObject) valueObj);
            } else
            {
                strBuf.append(valueObj);
            }

            // SchemaLeafNodeDef leafNode = (SchemaLeafNodeDef) schemaDef
            // .getChildAt(i);
            // nodePath = leafNode.getNodePath();
            // nodeValue = object.getValue(nodePath);
            // if (null == nodeValue) {
            // strBuf.append(nodeDef.getName() + FLAG + "null");
            // } else if (nodeValue instanceof MapObject) { // it is a map
            // // type
            // strBuf.append(nodeDef.getName() + FLAG);
            // mapToString(strBuf, (MapObject) nodeValue);
            // } else if (nodeDef.getType() == DataTypeNames.TYPE_ARRAY) { // it
            // // is
            // // a
            // // array
            // // type
            // strBuf.append(nodeDef.getName() + FLAG);
            // // array value
            // arrayToString(strBuf, nodeValue);
            // } else { // it is a basic type
            // strBuf.append(nodeDef.getName() + FLAG + nodeValue);
            // }
            // } catch (InvalidPathException ex) {
            // _log.info(ex.getMessage(), ex);
            // }
            // strBuf.append(",;");

        }
        return strBuf.toString();
    }

    public static String  BLANK_FLAG = "  ";

    private IEAISchemaDef _iEAISchemaDef;

    private static String FLAG       = ":";

    static private class Count
    {
        public Count()
        {
            // empty
        }

        public int count = 0;
    }

    public void mapToString ( StringBuffer strBuf, MapObject mapObj )
    {

        for (Iterator iter = mapObj.keySet().iterator(); iter.hasNext();)
        {
            strBuf.append("[");
            Object key = iter.next();
            Object val = mapObj.get(key);
            if (key instanceof MapObject)
            {
                mapToString(strBuf, (MapObject) key);
            } else if (key instanceof ArrayObject)
            {
                arrayToString(strBuf, key);
            } else
            {
                strBuf.append(key);
            }
            strBuf.append(",");
            if (val instanceof MapObject)
            {
                mapToString(strBuf, (MapObject) val);
            } else if (val instanceof ArrayObject)
            {
                arrayToString(strBuf, val);
            } else
            {
                strBuf.append(val);
            }
            strBuf.append("]");
        }
    }

    public void arrayToString ( StringBuffer strBuf, Object array )
    {
        int len = 0;
        if (array instanceof ArrayObject)
        {
            len = ((ArrayObject) array).length();
        } else
        {
            len = java.lang.reflect.Array.getLength(array);
        }
        strBuf.append("{");
        Object arrayItem;
        for (int j = 0; j < len; j++)
        {
            if (array instanceof ArrayObject)
            {
                arrayItem = ((ArrayObject) array).get(j);
            } else
            {
                arrayItem = java.lang.reflect.Array.get(array, j);
            }
            if (arrayItem != null && arrayItem.toString().indexOf('[') != -1)
            {
                arrayToString(strBuf, arrayItem);
            } else if (arrayItem instanceof MapObject)
            {
                mapToString(strBuf, (MapObject) arrayItem);
            } else if (arrayItem instanceof IEAIObject)
            {
                iealObjectTostring(strBuf, (IEAIObject) arrayItem);
            } else
            {
                strBuf.append(arrayItem);
            }
            if (j < len - 1)
            {
                strBuf.append(",");
            }

        }
        strBuf.append("}");
    }

    private void iealObjectTostring ( StringBuffer sb, IEAIObject object )
    {
        for (int i = 0; i < object.getChildCount(); i++)
        {
            sb.append("[");
            sb.append(object.getNameByIndex(i) + "," + object.get(i));
            sb.append("]" + BLANK_FLAG);
        }
        sb.append("\n");
    }

    public void saveDataMappingDataToException ( String[] sbv, Long status, String retstate, String retstr, Long flowId,
                                                 Long currDate )
    {
        String sql = "";
        String sqltivoli = "";
        Connection con = null;
        PreparedStatement ps = null;
        PreparedStatement psins = null;
        PreparedStatement pstivoli = null;
        ResultSet rs = null;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat sdfIns = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat sdfs = new SimpleDateFormat("yyyyMMdd");
        boolean isExists = false;
        try
        {
            // datamapping:0:系统名称，1:业务名称,2:工作流调用名称,3:提交时间,4:状态,5:消息,6:开始时间,7:结束时间,8:数据日期
            if (null != sbv[0] && !"".equals(sbv[0]) && !"null".equals(sbv[0]) && null != sbv[1] && !"".equals(sbv[1])
                    && !"null".equals(sbv[1]) && null != sbv[8] && !"".equals(sbv[8]) && !"null".equals(sbv[8]))
            {
                sql = "SELECT COUNT(TD.IID)  FROM IEAI_TOPO_BUSS_EXCEPTION TD WHERE TD.IPRJNAME=? AND TD.IACTNAME=? AND TD.ISTATE=? AND TD.IINSTANCENAME=?";
                con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
                psins = con.prepareStatement(sql);
                psins.setString(1, sbv[0]);
                psins.setString(2, sbv[1]);
                psins.setLong(3, status);
                psins.setString(4, sbv[8]);
                rs = psins.executeQuery();
                while (rs.next())
                {
                    if (rs.getLong(1) > 0)
                    {
                        isExists = true;
                    }
                }
                if (!isExists)
                {
                    Long sTime = 0l;
                    Long eTime = 0l;
                    if (null != sbv[6] && !"".equals(sbv[6]) && !"null".equals(sbv[6]))
                    {
                        Date date = sdf.parse(sbv[6]);
                        sTime = date.getTime();
                    }
                    if (null != sbv[7] && !"".equals(sbv[7]) && !"null".equals(sbv[7]))
                    {
                        Date date = sdf.parse(sbv[7]);
                        eTime = date.getTime();
                    }
                    // insert
                    sql = " INSERT INTO IEAI_TOPO_BUSS_EXCEPTION(IID,IPRJNAME,IACTNAME,ISTARTTIME,IENDTIME,ISTATE,IMSG,IFLOWID,IINSTANCENAME,ISUBMITTIME,ISTATE_DIC,IMSG_DIC,IACTEXECDATE,ICALLFLOWNAME) VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
                    // datamapping:0:系统名称，1:业务名称,2:工作流调用名称,3:提交时间,4:状态,5:消息,6:开始时间,7:结束时间,8:数据日期
                    ps = con.prepareStatement(sql);
                    ps.setLong(1, IdGenerator.createId("IEAI_TOPO_BUSS_EXCEPTION", con));
                    ps.setString(2, sbv[0]);
                    ps.setString(3, sbv[1]);
                    ps.setLong(4, sTime);
                    ps.setLong(5, eTime);
                    ps.setLong(6, status);
                    ps.setString(7, sbv[5]);
                    ps.setLong(8, flowId);
                    ps.setString(9, sbv[8]);
                    ps.setString(10, sbv[3]);
                    ps.setString(11, retstate);
                    ps.setString(12, retstr);
                    ps.setLong(13, currDate);
                    ps.setString(14, sbv[2]);
                    ps.executeUpdate();
                    syslog.warn(sbv[5]);
                    sqltivoli = " INSERT INTO IEAI_TOPO_TIVOLI_EXCEPTION(IID,IPRJNAME,IACTNAME,ISTARTTIME,IENDTIME,ISTATE,IMSG,IFLOWID,IINSTANCENAME,ISUBMITTIME,ISTATE_DIC,IMSG_DIC,IACTEXECDATE,ICALLFLOWNAME) VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
                    pstivoli = con.prepareStatement(sqltivoli);
                    pstivoli.setLong(1, IdGenerator.createId("IEAI_TOPO_TIVOLI_EXCEPTION", con));
                    pstivoli.setString(2, sbv[0]);
                    pstivoli.setString(3, sbv[1]);
                    pstivoli.setLong(4, sTime);
                    pstivoli.setLong(5, eTime);
                    pstivoli.setLong(6, status);
                    pstivoli.setString(7, sbv[5]);
                    pstivoli.setLong(8, flowId);
                    pstivoli.setString(9, sbv[8]);
                    pstivoli.setString(10, sbv[3]);
                    pstivoli.setString(11, retstate);
                    pstivoli.setString(12, retstr);
                    pstivoli.setLong(13, currDate);
                    pstivoli.setString(14, sbv[2]);
                    pstivoli.executeUpdate();
                }
                try
                {
                    isExists = false;
                    sql = "SELECT COUNT(TW.IID) FROM IEAI_TOPO_WARNING TW WHERE TW.IWARNINGSTATE=0  AND TW.IWARNINGTYPE=2  and  TW.IPRJNAME = ?  AND TW.IACTNAME = ? AND TW.IDATADATE=?";
                    psins = con.prepareStatement(sql);
                    psins.setString(1, sbv[0]);
                    psins.setString(2, sbv[1]);
                    psins.setString(3, sbv[8]);
                    rs = psins.executeQuery();
                    while (rs.next())
                    {
                        if (rs.getLong(1) > 0)
                        {
                            isExists = true;
                        }
                    }
                } catch (Exception e)
                {
                    _log.info("saveDataMappingDataToException query is error! " + e);
                } finally
                {
                    DBResource.closePreparedStatement(psins, "saveDataMappingDataToException", _log);
                }
                if (!isExists)
                {
                    sql = "INSERT INTO  IEAI_TOPO_WARNING(IID,IDATADATE,IWARNTIME,IACTMESID,IWARNINGTYPE, IPRJNAME, IFLOWNAME,IACTNAME, IWARNINGSTATE, ICONFIRMTYPE,IWARNTIMETYPE) VALUES (?,?,?,?,2,?,?,?,0,0,3)";
                    psins = con.prepareStatement(sql);
                    psins.setLong(1, IdGenerator.createId("ieai_topo_warning", con));
                    psins.setString(2, sbv[8]);
                    psins.setString(3, sdf.format(new Date()));
                    psins.setLong(4, -1);
                    psins.setString(5, sbv[0]);
                    psins.setString(6, sbv[2]);
                    psins.setString(7, sbv[1]);
                    psins.executeUpdate();
                } else
                {
                    sql = "UPDATE  IEAI_TOPO_WARNING  SET IWARNINGSTATE=0, IWARNTIME=?  WHERE   IWARNTIMETYPE=3 AND IWARNINGTYPE=2 AND  IACTNAME=?  AND  IPRJNAME=?  AND IDATADATE=?";
                    psins = con.prepareStatement(sql);
                    psins.setString(1, sdf.format(new Date()));
                    psins.setString(2, sbv[1]);
                    psins.setString(3, sbv[0]);
                    psins.setString(4, sbv[8]);
                    psins.executeUpdate();
                }
            }
            con.commit();
        } catch (Exception e)
        {
            try
            {
                con.rollback();
            } catch (SQLException e1)
            {
            }
            _log.info("saveDataMappingDataToException is error! " + e);
        } finally
        {
            try
            {
                DBResource.closePreparedStatement(psins, "saveDataMappingDataToException", _log);
                DBResource.closePreparedStatement(pstivoli, "IEAI_TOPO_TIVOLI_EXCEPTION", _log);
                DBResource.closeConn(con, rs, ps, "saveDataMappingDataToException", _log);
            } catch (Exception e)
            {
                _log.info("saveDataMappingDataToException close is error! " + e);
            }
        }
    }

    /**
     * @Des 如果工作流启动 修改警告状态
     * @Time 2018年4月16日18:16:33
     * <AUTHOR>
     * @param sbv
     * @param status
     * @param retstate
     * @param retstr
     * @param flowId
     * @param currDate
     */
    public void updateWarningState ( String[] sbv, Long status, String retstate, String retstr, Long flowId,
                                     Long currDate )
    {
        Connection con = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            // datamapping:0:系统名称，1:业务名称,2:工作流调用名称,3:提交时间,4:状态,5:消息,6:开始时间,7:结束时间,8:数据日期
            con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            String sql = "update IEAI_TOPO_WARNING set  iwarningstate = ? where IPRJNAME = ? and iactname = ? and idatadate=? ";
            ps = con.prepareStatement(sql);
            ps.setString(1, "1");
            ps.setString(2, sbv[0]);
            ps.setString(3, sbv[1]);
            ps.setString(4, sbv[8]);
            ps.executeUpdate();
            con.commit();
        } catch (Exception e)
        {
            _log.info("saveDataMappingDataToException is error! " + e);
        } finally
        {
            try
            {
                DBResource.closeConn(con, rs, ps, "saveDataMappingDataToException", _log);
            } catch (Exception e)
            {
                _log.info("saveDataMappingDataToException close is error! " + e);
            }
        }
    }

    /**
     * @Des 插入历史数据
     * @Time 2018年4月16日18:16:33
     * <AUTHOR>
     * @param sbv
     * @param status
     * @param retstate
     * @param retstr
     * @param flowId
     * @param currDate
     */
    public void insertWarningHistory ( String[] sbv, Long status, String retstate, String retstr, Long flowId,
                                       Long currDate )
    {
        String sql = "";
        Connection con = null;
        PreparedStatement ps = null;
        PreparedStatement psins = null;
        ResultSet rs = null;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat sdfIns = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat sdfs = new SimpleDateFormat("yyyyMMdd");
        ParsePosition pos = new ParsePosition(0);
        try
        {
            con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            sql = " INSERT INTO IEAI_TOPO_HISWARN (IID,   IDATADATE,   IWARNTIME,   IACTMESID,   IWARNINGTYPE,   IPRJNAME,   IACTNAME,   IWARNINGSTATE,   ICONFIRMTYPE,   IWARNTIMETYPE,   CONFIRMWAY) SELECT W.IID,W.IDATADATE,W.IWARNTIME,W.IACTMESID,W.IWARNINGTYPE,W.IPRJNAME,W.IACTNAME,W.IWARNINGSTATE,W.ICONFIRMTYPE,W.IWARNTIMETYPE,1  FROM IEAI_TOPO_WARNING W WHERE ((W.IWARNINGSTATE = 1 AND W.IWARNINGTYPE = 2)   OR   (W.IWARNINGSTATE = 1 AND W.IWARNTIMETYPE = 0))  AND  W.IPRJNAME=? AND W.IACTNAME=? AND W.IDATADATE=? AND w.iid NOT IN (SELECT W.IID  FROM ieai_topo_hiswarn W WHERE W.IPRJNAME = ?  AND W.IACTNAME = ?  AND W.IDATADATE = ?)";
            psins = con.prepareStatement(sql);
            psins.setString(1, sbv[0]);
            psins.setString(2, sbv[1]);
            psins.setString(3, sbv[8]);
            psins.setString(4, sbv[0]);
            psins.setString(5, sbv[1]);
            psins.setString(6, sbv[8]);
            psins.executeUpdate();
            con.commit();
            // syslog.warn(sbv[5]);
        } catch (Exception e)
        {
            _log.info("insertWarningHistory is error! " + e);
        } finally
        {
            try
            {
                DBResource.closePreparedStatement(psins, "saveDataMappingDataToException", _log);
                DBResource.closeConn(con, rs, ps, "saveDataMappingDataToException", _log);
            } catch (Exception e)
            {
                _log.info("insertWarningHistory close is error! " + e);
            }
        }
    }

    public void saveDataMappingDataFromExceptionToCancel ( String[] sbv, Long status, String retstate, String retstr,
                                                           Long flowId, Long currDate )
    {
        String sql = "";
        Connection con = null;
        PreparedStatement ps = null;
        PreparedStatement psins = null;
        ResultSet rs = null;
        SimpleDateFormat sdfIns = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat sdfs = new SimpleDateFormat("yyyyMMdd");
        boolean isExists = false;
        try
        {
            // datamapping:0:系统名称，1:业务名称,2:工作流调用名称,3:提交时间,4:状态,5:消息,6:开始时间,7:结束时间,8:数据日期
            if (null != sbv[0] && !"".equals(sbv[0]) && !"null".equals(sbv[0]) && null != sbv[1] && !"".equals(sbv[1])
                    && !"null".equals(sbv[1]) && null != sbv[8] && !"".equals(sbv[8]) && !"null".equals(sbv[8]))
            {
                sql = "SELECT COUNT(TW.IID) FROM IEAI_TOPO_WARNING TW WHERE TW.IWARNINGSTATE=0  AND TW.IWARNINGTYPE=2 AND  TW.IPRJNAME = ?  AND TW.IACTNAME = ? AND TW.IDATADATE=?";
                con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
                psins = con.prepareStatement(sql);
                psins.setString(1, sbv[0]);
                psins.setString(2, sbv[1]);
                psins.setString(3, sbv[8]);
                rs = psins.executeQuery();
                while (rs.next())
                {
                    if (rs.getLong(1) > 0)
                    {
                        isExists = true;
                    }
                }
                if (isExists)
                {
                    // update 报警表
                    sql = "UPDATE  IEAI_TOPO_WARNING SET IWARNINGSTATE=1,ICONFIRMTYPE=1   WHERE IWARNINGSTATE=0  AND IWARNINGTYPE=2  AND  IPRJNAME = ?  AND IACTNAME = ? AND IDATADATE=?";
                    ps = con.prepareStatement(sql);
                    ps.setString(1, sbv[0]);
                    ps.setString(2, sbv[1]);
                    ps.setString(3, sbv[8]);
                    ps.executeUpdate();

                    con.commit();
                }
            }
        } catch (Exception e)
        {
            try
            {
                con.rollback();
            } catch (SQLException e1)
            {
            }
            _log.info("saveDataMappingDataFromExceptionToCancel is error! " + e);
        } finally
        {
            try
            {
                DBResource.closePreparedStatement(psins, "saveDataMappingDataFromExceptionToCancel", _log);
                DBResource.closeConn(con, rs, ps, "saveDataMappingDataFromExceptionToCancel", _log);
            } catch (Exception e)
            {
                _log.info("saveDataMappingDataFromExceptionToCancel close is error! " + e);
            }
        }
    }

    public void updateDataMappingDataToGroupStart ( Map<String, DataBean> mapGs, String[] sbv, Long status,
                                                    String retstate, String retstr, Long flowId, Long currDate, Connection con )
    {
        // 查询组内所有批量运行状态，无，则将当前开始时间设置成开始时间；均需要更新状态【存在异常则异常，不存在则更新到正常】
        String sql = "";
        // Connection con = null;
        PreparedStatement ps = null;
        PreparedStatement psins = null;
        ResultSet rs = null;
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat sdfs = new SimpleDateFormat("yyyyMMdd");
        boolean isExists = false;
        try
        {
            // datamapping:0:系统名称，1:业务名称,2:工作流调用名称,3:提交时间,4:状态,5:消息,6:开始时间,7:结束时间,8:数据日期
            if (null != sbv[0] && !"".equals(sbv[0]) && !"null".equals(sbv[0]) && null != sbv[1] && !"".equals(sbv[1])
                    && !"null".equals(sbv[1]) && null != sbv[8] && !"".equals(sbv[8]) && !"null".equals(sbv[8]))
            {
                String groupnum = "";
                sql = "SELECT COUNT(T.IID), T1.IPGROUPNUM FROM (SELECT B.* FROM IEAI_TOPOINFO_BUSS B,(SELECT TB.SYSTEMNAME, TB.TASKNAME, TB.GRO, TB.IPGROUPNUM FROM IEAI_TOPOINFO_BUSS TB WHERE TB.SYSTEMNAME = ? AND TB.TASKNAME = ?) A WHERE A.GRO = B.GRO  AND A.IPGROUPNUM = B.IPGROUPNUM) T1 LEFT JOIN IEAI_DATAMAPPING_OUTPUT T ON  T.IPRJNAME = T1.SYSTEMNAME  AND T.IACTNAME = T1.TASKNAME   AND T.IINSTANCENAME = ? GROUP BY T1.IPGROUPNUM";
                // con = DBManager.getInstance().getJdbcConnection();
                psins = con.prepareStatement(sql);
                psins.setString(1, sbv[0]);
                psins.setString(2, sbv[1]);
                psins.setString(3, sbv[8]);
                rs = psins.executeQuery();
                while (rs.next())
                {
                    if (rs.getLong(1) > 0)
                    {
                        isExists = true;
                    }
                    groupnum = rs.getString("IPGROUPNUM");
                }
                if (isExists && null != sbv[6] && !"".equals(sbv[6]) && !"null".equals(sbv[6]))
                {
                    Long eTime = null;
                    if (status == 2)
                    {
                        if (null != sbv[7] && !"".equals(sbv[7]) && !"null".equals(sbv[7]))
                        {
                            Date date = sdf.parse(sbv[6]);
                            eTime = date.getTime();
                        } else
                        {
                            eTime = System.currentTimeMillis();
                        }
                    }
                    DataBean bean = null;
                    // 更新开始时间
                    String key = "ieai--topo--g--" + groupnum;
                    Date date = sdf.parse(sbv[6]);
                    Long currTime = date.getTime();
                    if (mapGs.containsKey(key))
                    {
                        bean = (DataBean) mapGs.get(key);
                        Long sTime = bean.getStartTime();
                        if (currTime < sTime)
                        {
                            bean.setStartTime(currTime);
                            mapGs.put(key, bean);
                        }
                        if (status != 0 && status != 2)
                        {
                            bean.setStatus(status);
                        }
                    } else
                    {
                        bean = new DataBean();
                        bean.setStartTime(currTime);
                        bean.setEndTime(eTime);
                        bean.setGroupNum(groupnum);
                        bean.setStatus(status);
                        bean.setInstanceName(sbv[8]);
                        bean.setPrjName(sbv[0]);
                        bean.setActName(sbv[1]);
                        bean.setFlowName(sbv[2]);
                        mapGs.put(key, bean);
                    }
                }
            }
        } catch (Exception e)
        {
            try
            {
                con.rollback();
            } catch (SQLException e1)
            {
            }
            _log.info("updateDataMappingDataToGroupStart is error! " + e);
        } finally
        {
            try
            {
                DBResource.closePreparedStatement(psins, "updateDataMappingDataToGroupStart", _log);
                DBResource.closePSRS(/* con, */rs, ps, "updateDataMappingDataToGroupStart", _log);
            } catch (Exception e)
            {
                _log.info("updateDataMappingDataToGroupStart close is error! " + e);
            }
        }
    }

    public void updateDataMappingDataToGroupEnd ( Map<String, DataBean> mapGe, String[] sbv, Long status,
                                                  String retstate, String retstr, Long flowId, Long currDate, Connection con )
    {
        // 查询组内所有批量是否完成，全部完成，则更新当前的完成时间为，组完成时间，状态为完成
        String sql = "";
        // Connection con = null;
        PreparedStatement ps = null;
        PreparedStatement psins = null;
        ResultSet rs = null;
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        boolean isExists = false;
        try
        {
            // datamapping:0:系统名称，1:业务名称,2:工作流调用名称,3:提交时间,4:状态,5:消息,6:开始时间,7:结束时间,8:数据日期
            if (null != sbv[0] && !"".equals(sbv[0]) && !"null".equals(sbv[0]) && null != sbv[1] && !"".equals(sbv[1])
                    && !"null".equals(sbv[1]) && null != sbv[8] && !"".equals(sbv[8]) && !"null".equals(sbv[8]))
            {
                String groupnum = "";
                sql = "SELECT COUNT(T.IID), T1.IPGROUPNUM FROM (SELECT B.* FROM IEAI_TOPOINFO_BUSS B,(SELECT TB.SYSTEMNAME, TB.TASKNAME, TB.GRO, TB.IPGROUPNUM FROM IEAI_TOPOINFO_BUSS TB WHERE TB.SYSTEMNAME = ? AND TB.TASKNAME = ?) A WHERE A.GRO = B.GRO  AND A.IPGROUPNUM = B.IPGROUPNUM) T1 LEFT JOIN IEAI_DATAMAPPING_OUTPUT T ON  T.IPRJNAME = T1.SYSTEMNAME  AND T.IACTNAME = T1.TASKNAME   AND T.IINSTANCENAME = ? GROUP BY T1.IPGROUPNUM";
                // con = DBManager.getInstance().getJdbcConnection();
                psins = con.prepareStatement(sql);
                psins.setString(1, sbv[0]);
                psins.setString(2, sbv[1]);
                psins.setString(3, sbv[8]);
                rs = psins.executeQuery();
                while (rs.next())
                {
                    if (rs.getLong(1) > 0)
                    {
                        isExists = true;
                    }
                    groupnum = rs.getString("IPGROUPNUM");
                }
                DataBean bean = null;
                Long sTime = 0l;
                Long currTime = 0l;
                if (null != sbv[7] && !"".equals(sbv[7]) && !"null".equals(sbv[7]))
                {
                    Date date = sdf.parse(sbv[7]);
                    currTime = date.getTime();
                }
                if (null != sbv[6] && !"".equals(sbv[6]) && !"null".equals(sbv[6]))
                {
                    Date date = sdf.parse(sbv[6]);
                    sTime = date.getTime();
                }
                if (null != sbv[7] && !"".equals(sbv[7]) && !"null".equals(sbv[7]))
                {
                    // 更新开始时间
                    String key = "ieai--topo--g--" + groupnum;
                    if (mapGe.containsKey(key))
                    {
                        bean = (DataBean) mapGe.get(key);
                        Long eTime = bean.getEndTime();
                        if (currTime > eTime)
                        {
                            bean.setEndTime(currTime);
                            mapGe.put(key, bean);
                        }
                        Long oldsTime = bean.getStartTime();
                        if (sTime < oldsTime)
                        {
                            bean.setStartTime(sTime);
                            mapGe.put(key, bean);
                        }
                    } else
                    {
                        bean = new DataBean();
                        bean.setEndTime(currTime);
                        bean.setStartTime(sTime);
                        bean.setGroupNum(groupnum);
                        bean.setStatus(2l);
                        bean.setInstanceName(sbv[8]);
                        bean.setPrjName(sbv[0]);
                        bean.setActName(sbv[1]);
                        bean.setFlowName(sbv[2]);
                        mapGe.put(key, bean);
                    }
                }
                // }
            }
        } catch (Exception e)
        {
            try
            {
                con.rollback();
            } catch (SQLException e1)
            {
            }
            _log.info("updateDataMappingDataToGroupEnd is error! " + e);
        } finally
        {
            try
            {
                DBResource.closePreparedStatement(psins, "updateDataMappingDataToGroupEnd", _log);
                DBResource.closePSRS(/* con, */rs, ps, "updateDataMappingDataToGroupEnd", _log);
            } catch (Exception e)
            {
                _log.info("updateDataMappingDataToGroupEnd close is error! " + e);
            }
        }
    }

    public boolean updateCallFlowStatus ( String prjName, String flowName, String groupNum, String insName,
                                          Connection conn ) throws RepositoryException
    {
        boolean isSuccess = false;
        String sql = "select t.istarttime,t.iendtime from IEAI_TOPOINFO_BUSS_G_INS t WHERE t.ipgroupnum=? AND t.idatadate=?";
        String updatesql = "UPDATE ieai_workflowinstance w SET  w.iflowstarttime=?,w.iflowendtime=?,w.istatus=2  WHERE w.iflowid IN (SELECT w.iflowid FROM ieai_workflowinstance w,ieai_actruntime a WHERE w.iflowid=a.iflowid AND w.iprojectname=? AND a.iactname=? AND a.iacttype='CallFlow' AND w.iflowinsname=?) and (w.istatus=0 or w.istatus=8)";
        String upruntimeql = "  UPDATE IEAI_ACTRUNTIME W SET W.Istate = ?,w.iendtime=?  WHERE W.IID IN (SELECT a.iid  FROM IEAI_WORKFLOWINSTANCE W, IEAI_ACTRUNTIME A   WHERE W.IFLOWID = A.IFLOWID   AND W.IPROJECTNAME = ?   AND A.IACTNAME = ?   AND A.IACTTYPE = 'CallFlow'  AND W.IFLOWINSNAME = ?)";
        PreparedStatement ps = null;
        PreparedStatement ps1 = null;
        PreparedStatement ps2 = null;
        ResultSet rs = null;
        SimpleDateFormat sdfIns = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat sdfYMD = new SimpleDateFormat("yyyyMMdd");
        try
        {
            long sTime = 0;
            long eTime = 0;
            String tmpStime = "";
            String tmpEtime = "";
            ps = conn.prepareStatement(sql);
            ps.setString(1, groupNum);
            ps.setString(2, insName);
            rs = ps.executeQuery();
            while (rs.next())
            {
                tmpStime = rs.getString("istarttime");
                tmpEtime = rs.getString("iendtime");
                if (null != tmpStime && !"".equals(tmpStime) && !"null".equals(tmpStime))
                {
                    sTime = rs.getLong("istarttime");
                }
                if (null != tmpEtime && !"".equals(tmpEtime) && !"null".equals(tmpEtime))
                {
                    eTime = rs.getLong("iendtime");
                }
            }
            ps2 = conn.prepareStatement(upruntimeql);
            ps2.setString(1, "Finished");
            ps2.setLong(2, eTime);
            ps2.setString(3, prjName);
            ps2.setString(4, flowName);
            ps2.setString(5, sdfYMD.format(sdfIns.parse(insName)));
            ps2.executeUpdate();

            ps1 = conn.prepareStatement(updatesql);
            ps1.setLong(1, sTime);
            ps1.setLong(2, eTime);
            ps1.setString(3, prjName);
            ps1.setString(4, flowName);
            ps1.setString(5, sdfYMD.format(sdfIns.parse(insName)));
            ps1.executeUpdate();
            // 数仓批量，跑虚拟作业，驱动工作流调用
            // EnvConfigReader ecf =EnvConfigReader.getInstance();
            // ecf.init();
            boolean dataFlatSwitch = Boolean.parseBoolean(EnvConfigReader.getInstance().getProperties(
                    Environment.DATAMAPPING_ANALY_SWITCH, String.valueOf(Environment.DATAMAPPING_ANALY_SWITCH_DEFAULT)));
            String dataFlat = EnvConfigReader.getInstance().getProperties(Environment.DATA_PLATFORM_NAME, "");
            if (dataFlatSwitch && null != dataFlat && !"".equals(dataFlat) && !"null".equals(dataFlat))
            {
                String[] prjAndflow = dataFlat.split(":");
                if (prjAndflow.length == 2)
                {
                    if (prjAndflow[0].equals(prjName))
                    {
                        _log.info("dataFlatSwitch:" + dataFlatSwitch + ",prjAndflow:" + prjAndflow + ",prjName:"
                                + prjName + ",flowName:" + flowName);
                        sql = "SELECT c.icallflowid as iflowid FROM ieai_workflowinstance w,ieai_actruntime a,ieai_callworkflow_info c WHERE w.iflowid=c.imainflowid AND a.iactid=c.icallflowactid AND  w.iflowid=a.iflowid AND w.iprojectname=? AND a.iactname=?  AND w.iflowinsname=?";
                        ps = conn.prepareStatement(sql);
                        ps.setString(1, prjName);
                        ps.setString(2, flowName);
                        ps.setString(3, sdfYMD.format(sdfIns.parse(insName)));
                        rs = ps.executeQuery();
                        while (rs.next())
                        {
                            List execact = EngineRepository.getInstance()
                                    .queryPendingExecActForCallfolw(rs.getLong("iflowid"), Constants.IEAI_IEAI_BASIC);
                            if (execact.size() > 0)
                            {
                                ExecAct updateExecact = (ExecAct) execact.get(0);
                                if (updateExecact.getRuningPosition() < ExecAct.POSITION_EXECUTE_ACT)
                                {
                                    updateExecact.setRuningPosition(ExecAct.POSITION_EXECUTE_ACT);
                                }
                                Engine.getInstance().getScheduler().updateExecActAndMakeItPending(updateExecact);
                            }
                        }
                    }
                }
            }
            isSuccess = true;
        } catch (Exception e)
        {
            _log.error("updateCallFlowStatus query DB Error :", e);
        } finally
        {
            DBResource.closePreparedStatement(ps1, "updateCallFlowStatus", _log);
            DBResource.closePSRS(rs, ps, "updateCallFlowStatus", _log);
        }
        return isSuccess;
    }

    public boolean getDisableRuleForIsRun ( String pName, String fName, String insName, Long flowid, boolean isChange )
            throws RepositoryException
    {
        List<DisableFlowRuleBean> list = EngineRepositotyJdbc.getInstance().getDisableRuleForRun(pName, fName, insName,
                flowid, Constants.IEAI_IEAI);
        boolean flag = false;
        for (DisableFlowRuleBean drb : list)
        {

            // 首先判断是否是满足只一次的 规则 如果 满足 将该规则集合的 所有 只一次规则删除(不包括 指定数据日期范围)
            if (drb.getIsonlyone().equals("1"))
            {
                flag = true;
                if (!drb.getIsApp().equals("1"))
                {
                    // 删除该规则
                    if (isChange)
                    {
                        WorkflowQueryManager.getInstance().deleteDisableRules(new int[] { drb.getIid() });
                    }
                } else
                {
                    if (isChange)
                    {
                        // 更新该规则 将只一次 更改成否
                        WorkflowQueryManager.getInstance().updateDisablerule(drb);
                    }
                }
            }
            if (drb.getIsApp().equals("1"))
            {
                int dateF = Integer.parseInt(drb.getDatadatef());
                int dateT = Integer.parseInt(drb.getDatadatet());
                if (dateF <= drb.getFlowDateIns() && drb.getFlowDateIns() <= dateT)
                {
                    flag = true;
                } else
                {
                    if (flag)
                    {
                        flag = true;
                    } else
                    {
                        flag = false;
                    }
                }
            } else
            {
                flag = true;
            }
        }
        if (flag)
        {
            _log.info("工作流：" + fName + ",未启动。该工作流已经被禁用。");
        }

        return flag;
    }

    /**
     * <li>Description:保存活动运行位置</li>
     *
     * <AUTHOR> 2015-4-1
     * @param execAct
     * @param actElem
     * @throws RepositoryException return void
     */
    public void saveActRunningposition ( ExecAct execAct, BasicActElement actElem, int dbType )
            throws RepositoryException
    {
        for (int i = 0;; i++)
        {
            try
            {
                DbOpScheduler scheduler = new DbOpScheduler();
                Connection con = null;
                String serverIp = "";
                try
                {
                    serverIp = Environment.getInstance().getServerIP();
                } catch (UnknownHostException e1)
                {
                    _log.error(" saveActRunningposition()  get serverip errors ", e1);
                }
                try
                {
                    con = DBManager.getInstance().getSchedulerConnection(dbType);
                } catch (DBException e)
                {
                    _log.error(" saveActRunningposition() get db errors ", e);
                    throw new RepositoryException(ServerError.ERR_DB_UPDATE);
                }
                try
                {
                    RepExecAct tempAct = RepExecAct.newInstance(execAct);
                    scheduler.updateExecActRunnpostion(tempAct, execAct.getSerialNo(), con, serverIp);
                    EngineRepositotyJdbc.getInstance().saveActsuccNum(execAct.getFlowId(),
                            FlowEndFlagManager.getInstance().getActSuccNum(actElem.getSucceedingActs()), execAct.getActId(),
                            actElem, con);
                    con.commit();
                    // end update
                    return;
                } catch (RepositoryException ex)
                {
                    throw ex;
                } catch (SQLException e)
                {
                    // 增加对sql异常的处理 09-09-05 by tao_ding
                    _log.error("saveActRunningposition() ", e);
                    try
                    {
                        con.rollback();
                        throw new RepositoryException(ServerError.ERR_DB_UPDATE);
                    } catch (SQLException ex1)
                    {
                        _log.error("saveActRunningposition() Rollback Error ", e);
                        throw new RepositoryException(ServerError.ERR_DB_UPDATE);
                    }
                } finally
                {
                    if (con != null)
                        try
                        {
                            con.close();
                        } catch (SQLException e)
                        {
                            _log.error("saveActRunningposition() close conn error ", e);
                        }
                }
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
    }


    /**
     * <li>Description:保存活动运行位置</li>
     *
     * <AUTHOR> 2015-4-1
     * @param execAct
     * @param actElem
     * @throws RepositoryException return void
     */
    public void saveActRunningpositionSuccNum ( ExecAct execAct, BasicActElement actElem, int dbType )
            throws RepositoryException
    {
        for (int i = 0;; i++)
        {
            try
            {
                DbOpScheduler scheduler = new DbOpScheduler();
                Connection con = null;
                String serverIp = "";
                try
                {
                    serverIp = Environment.getInstance().getServerIP();
                } catch (UnknownHostException e1)
                {
                    _log.error(" saveActRunningpositionSuccNum()  get serverip errors ", e1);
                }
                try
                {
                    con = DBManager.getInstance().getSchedulerConnection(dbType);
                } catch (DBException e)
                {
                    _log.error(" saveActRunningpositionSuccNum() get db errors ", e);
                    throw new RepositoryException(ServerError.ERR_DB_UPDATE);
                }
                try
                {
                    EngineRepositotyJdbc.getInstance().saveActsuccNum(execAct.getFlowId(),
                            FlowEndFlagManager.getInstance().getActSuccNum(actElem.getSucceedingActs()), execAct.getActId(),
                            actElem, con);
                    con.commit();
                    // end update
                    return;
                } catch (RepositoryException ex)
                {
                    throw ex;
                } catch (SQLException e)
                {
                    // 增加对sql异常的处理 09-09-05 by tao_ding
                    _log.error("saveActRunningpositionSuccNum() ", e);
                    try
                    {
                        con.rollback();
                        throw new RepositoryException(ServerError.ERR_DB_UPDATE);
                    } catch (SQLException ex1)
                    {
                        _log.error("saveActRunningpositionSuccNum() Rollback Error ", e);
                        throw new RepositoryException(ServerError.ERR_DB_UPDATE);
                    }
                } finally
                {
                    if (con != null)
                        try
                        {
                            con.close();
                        } catch (SQLException e)
                        {
                            _log.error("saveActRunningpositionSuccNum() close conn error ", e);
                        }
                }
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
    }

    public Map<String, String> getStepMesById ( Long stepId ) throws ServerException, RepositoryException
    {
        DbOpScheduler opScheduler = new DbOpScheduler();
        Map<String, String> map = opScheduler.getStepMesById(stepId);
        return map;
    }

    public List getPrenerOutput ( Map map ) throws ServerException, RepositoryException
    {
        DbOpScheduler opScheduler = new DbOpScheduler();
        List list = opScheduler.getPrenerOutput(map);
        return list;
    }

    public void updateExecActForRecoverNoPending ( long id, int state, int type ) throws RepositoryException
    {
        DbOpScheduler opScheduler = new DbOpScheduler();
        opScheduler.updateExecActForRecoverNoPending(id, state, type);
    }

    public void updateAutoResultAsm ( String requestId, String lastline, String stdout, int flag )
            throws ServerException
    {
        String[] ids = requestId.split("ieai-asm-");
        String id = ids[1].substring(0, ids[1].length() - 1);

        String method = "saveAsmInfo";

        Connection conn = null;
        PreparedStatement ps = null;
        String sqlDel = "delete from ieai_script_asm_info where imainid=?";
        String sqlIns = "insert into ieai_script_asm_info (iid, imainid, state, type, rebal, sector, block, au, total_mb, free_mb, req_mir_free_mb, usable_file_mb, offline_disks, voting_files, name)values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        String sqlUpd = "update ieai_script_asm set istatus=? where iid=?";
        try
        {

            long iid = Long.parseLong(id);
            lastline = lastline.replace("\"", "").replaceAll("/", "`");
            if (null != lastline && !"".equals(lastline))
            {
                Map<String, Object> a = ParseJson.JSON2Map(lastline);
                String asmvalue = String.valueOf(a.get("AsmValue"));
                if (null != asmvalue && !"".equals(asmvalue) && !"null".equals(asmvalue))
                {
                    List<Map<String, Object>> jsonlist = ParseJson.JSON2List(asmvalue);
                    String istatus = String.valueOf(a.get("AsmState"));
                    conn = DBResource.getConnection(method, _log, Constants.IEAI_IEAI_BASIC);
                    ps = conn.prepareStatement(sqlDel);
                    ps.setLong(1, iid);
                    ps.executeUpdate();

                    for (int i = 0; i < jsonlist.size(); i++)
                    {
                        ps = conn.prepareStatement(sqlIns);
                        ps.setLong(1, IdGenerator.createId("IEAI_SCRIPT_ASM_INFO", conn));
                        ps.setLong(2, iid);
                        ps.setString(3, String.valueOf(jsonlist.get(i).get("State")));
                        ps.setString(4, String.valueOf(jsonlist.get(i).get("Type")));
                        ps.setString(5, String.valueOf(jsonlist.get(i).get("Rebal")));
                        ps.setString(6, String.valueOf(jsonlist.get(i).get("Sector")));
                        ps.setString(7, String.valueOf(jsonlist.get(i).get("Block")));
                        ps.setString(8, String.valueOf(jsonlist.get(i).get("AU")));
                        ps.setString(9, String.valueOf(jsonlist.get(i).get("Total_MB")));
                        ps.setString(10, String.valueOf(jsonlist.get(i).get("Free_MB")));
                        ps.setString(11, String.valueOf(jsonlist.get(i).get("Req_mir_free_MB")));
                        ps.setString(12, String.valueOf(jsonlist.get(i).get("Usable_file_MB")));
                        ps.setString(13, String.valueOf(jsonlist.get(i).get("Offline_disks")));
                        ps.setString(14, String.valueOf(jsonlist.get(i).get("Voting_files")));
                        ps.setString(15, String.valueOf(jsonlist.get(i).get("Name")).replace("`", "/"));

                        ps.executeUpdate();
                    }

                    ps = conn.prepareStatement(sqlUpd);
                    ps.setString(1, istatus);
                    ps.setLong(2, iid);
                    ps.executeUpdate();
                    conn.commit();
                    _log.info("asm info is finished");
                } else
                {
                    _log.info("asm info value is null");
                }
            } else
            {
                _log.info("asm info is null");
            }

        } catch (Exception e)
        {
            _log.error("saveAsmInfo error: ", e);
            _log.info("json:  " + lastline);
        } finally
        {
            DBResource.closePSConn(conn, ps, method, _log);
        }

    }

    public synchronized void resultForResApp ( String requestId, String lastline, String stdout, String err, int flag )
            throws ServerException
    {
        String[] ids = requestId.split("ieai-resapp-");
        String id = ids[1].substring(0, ids[1].length() - 1);
        String insid = ids[3];
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        String sql = "select count(1) from ieai_script_instance where istate in (" + Constants.SCRIPT_FINISH_FAIL_STATE
                + "," + Constants.SCRIPT_FINISH_STATE + ")  and IID=?";
        String updSql = " update  IEAI_RESOURCE_APPLY a set a.IIFLAG=? ,a.RETURNCONTENT=?,a.RETURNTIME=FUN_GET_DATE_NUMBER_NEW("
                + Constants.getCurrentSysDate() + ",8)  where  a.IID =?";
        String updSql2 = " update  ieai_script_instance a set a.istate=?,a.istdout=? ,a.istderr=?,a.ilastline=? ,a.iendtime=FUN_GET_DATE_NUMBER_NEW("
                + Constants.getCurrentSysDate() + ",8)  where  a.IID =?";
        String updSql3 = " update  ieai_scripts_coat a set a.istate=?,a.iend_time=FUN_GET_DATE_NUMBER_NEW("
                + Constants.getCurrentSysDate()
                + ",8)  where  a.IID =(select IMAINID from ieai_script_instance where iid=?)";
        String updSql4 = " update  IEAI_SCRIPT_FLOW a set a.ISTATUS=?,a.iendtime=FUN_GET_DATE_NUMBER_NEW("
                + Constants.getCurrentSysDate()
                + ",8)  where  a.IID =(select IFLOWID from ieai_scripts_coat where iid=(select IMAINID from ieai_script_instance where iid=?)) ";
        PreparedStatement ps = null;
        PreparedStatement updStat = null;
        PreparedStatement updStat2 = null;
        PreparedStatement updStat3 = null;
        PreparedStatement updStat4 = null;
        ResultSet rs = null;
        Connection con = null;
        try
        {
            long count = 0;
            con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            ps = con.prepareStatement(sql);
            ps.setLong(1, Long.parseLong(insid));
            rs = ps.executeQuery();
            while (rs.next())
            {
                count = rs.getLong(1);
            }
            if (flag == 3)
            {
                updStat = con.prepareStatement(updSql);
                updStat.setLong(1, flag);
                updStat.setString(2, "资源申请ID：" + id + ",申请失败!");
                updStat.setLong(3, Long.parseLong(id));
                updStat.executeUpdate();
            } else
            {
                ConfigReader cf = ConfigReader.getInstance();
                cf.init();
                boolean flagswitch = cf.getBooleanProperties(Environment.DBAAS_SS_RESAPP_SHELL_SWITCH, true);
                if (flagswitch)
                {
                    if (!"".equals(lastline))
                    {
                        updStat = con.prepareStatement(updSql);
                        updStat.setLong(1, flag);
                        updStat.setString(2, lastline);
                        updStat.setLong(3, Long.parseLong(id));
                        updStat.executeUpdate();
                    } else
                    {
                        updSql = " update  IEAI_RESOURCE_APPLY a set a.IIFLAG=?,a.RETURNTIME=FUN_GET_DATE_NUMBER_NEW("
                                + Constants.getCurrentSysDate() + ",8)  where  a.IID =?";
                        updStat = con.prepareStatement(updSql);
                        updStat.setLong(1, flag);
                        updStat.setLong(2, Long.parseLong(id));
                        updStat.executeUpdate();
                    }
                    _log.info("ieai-resapp:" + lastline);
                } else
                {
                    updSql = " update  IEAI_RESOURCE_APPLY a set a.IIFLAG=?,a.RETURNTIME=FUN_GET_DATE_NUMBER_NEW("
                            + Constants.getCurrentSysDate() + ",8)  where  a.IID =?";
                    updStat = con.prepareStatement(updSql);
                    updStat.setLong(1, flag);
                    updStat.setLong(2, Long.parseLong(id));
                    updStat.executeUpdate();
                }
            }
            updStat2 = con.prepareStatement(updSql2);
            updStat2.setLong(1, flag == 3 ? Constants.SCRIPT_FINISH_FAIL_STATE : Constants.SCRIPT_FINISH_STATE);
            updStat2.setString(2, stdout);
            updStat2.setString(3, err);
            updStat2.setString(4, lastline);
            updStat2.setLong(5, Long.parseLong(insid));
            updStat2.executeUpdate();

            updStat3 = con.prepareStatement(updSql3);
            updStat3.setLong(1, flag == 3 ? Constants.SCRIPT_FINISH_FAIL_STATE : Constants.SCRIPT_FINISH_STATE);
            updStat3.setLong(2, Long.parseLong(insid));
            updStat3.executeUpdate();

            updStat4 = con.prepareStatement(updSql4);
            updStat4.setLong(1, flag == 3 ? Constants.SCRIPT_FINISH_FAIL_STATE : Constants.SCRIPT_FINISH_STATE);
            updStat4.setLong(2, Long.parseLong(insid));
            updStat4.executeUpdate();

            con.commit();
            if (count == 0)
            {
                _log.info("requestId:" + requestId + ",count:" + count);
                this.saveDataSource(id);
            }
        } catch (Exception e)
        {
            e.printStackTrace();
            _log.error(method + " is error ");
        } finally
        {
            DBResource.closePSRS(rs, ps, method, _log);
            DBResource.closePreparedStatement(updStat2, method, _log);
            DBResource.closePreparedStatement(updStat3, method, _log);
            DBResource.closePreparedStatement(updStat4, method, _log);
            DBResource.closePSConn(con, updStat, method, _log);
        }
    }

    public void resultForResAppRecover ( String requestId, String lastline, String stdout, String err, int flag )
            throws ServerException
    {
        String[] ids = requestId.split("ieai-resrecover-");
        String id = ids[1].substring(0, ids[1].length() - 1);
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        String updSql = " update  IEAI_RESOURCE_APPLY a set a.IIFLAG=?,a.RETURNTIME=FUN_GET_DATE_NUMBER_NEW("
                + Constants.getCurrentSysDate() + ",8)  where  a.IID =?";
        String sql = "update IEAI_RESOURCE_MANAGE M set M.IISDEL=1,m.istatus=1 where m.IREAPPSID=? ";
        PreparedStatement updStat = null;
        PreparedStatement ps = null;
        Connection con = null;
        try
        {
            con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            updStat = con.prepareStatement(updSql);
            _log.info("ieai-resrecover:" + lastline);
            if (flag == 1)
            {
                updStat.setLong(1, 30);
            } else
            {
                updStat.setLong(1, 40);
            }
            updStat.setLong(2, Long.parseLong(id));
            updStat.executeUpdate();
            ps = con.prepareStatement(sql);
            ps.setLong(1, Long.parseLong(id));
            ps.executeUpdate();
            con.commit();
        } catch (Exception e)
        {
            e.printStackTrace();
            _log.error(method + " is error ");
        } finally
        {
            DBResource.closePreparedStatement(ps, method, _log);
            DBResource.closePSConn(con, updStat, method, _log);
        }
    }

    public void saveDataSource ( String resid ) throws RepositoryException, ServerException
    {

        String method = "saveDataSource";
        Connection conn = null;
        PreparedStatement savePst = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_SCRIPT_SERVICE);
        } catch (Exception e)
        {
            throw new RepositoryException(ServerError.ERR_DB_INIT);
        }
        try
        {
            Map<String, String> map = this.getResAppRecordInfo(Long.parseLong(resid));
            int flag = 0;
            String version = "";
            String newdbid = "";
            String dbid = "";
            String rac = "";
            try
            {
                Map mapVersion = this.getDataBaseDbId(map);
                if (mapVersion.containsKey("success")
                        && Boolean.parseBoolean(String.valueOf(mapVersion.get("success"))))
                {
                    newdbid = String.valueOf(mapVersion.get("DBID"));
                    version = String.valueOf(mapVersion.get("VERSION"));
                    rac = String.valueOf(mapVersion.get("RAC"));
                } else
                {
                    flag = 1;
                }
            } catch (Exception e)
            {
                _log.error("get resource version status exp ", e);
            }

            String saveSql = "insert into  IEAI_RESOURCE_MANAGE"
                    + "(IID, INAME, IIP,IDBPORT, IRSTYPE, IFLAG,IBUSINESS, IDBUSER, IDBPWD, IAPPLYUSER, ISID,ITYPE,IENV,ISTATUS,IUSERID,IALTERLOGNAME,ICPU,IMEMORY,IDISK,IMODEL,IREAPPSID,IDBID,IDBVERSION,IRAC) "
                    + "values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
            savePst = conn.prepareStatement(saveSql);
            Map getMap = null;
            long id = IdGenerator.createId(" IEAI_RESOURCE_MANAGE", conn);
            savePst.setLong(1, id);
            savePst.setString(2, map.get("INAME"));
            savePst.setString(3, map.get("IIP"));
            savePst.setLong(4, Long.parseLong(map.get("IDBPORT")));
            savePst.setString(5, map.get("IRSTYPE"));
            savePst.setString(6, "0");
            savePst.setString(7, map.get("IBUSINESS"));
            savePst.setString(8, map.get("IDBUSER"));
            String dbPwd = map.get("IDBPWD");

            if (!"".equals(dbPwd) && null != dbPwd && !"null".equals(dbPwd))
            {
                dbPwd = Jcrypt.encrypt(dbPwd);
            }
            savePst.setString(9, dbPwd);
            savePst.setString(10, map.get("IAPPLYUSER"));
            savePst.setString(11, map.get("ISID"));
            savePst.setLong(12, 1);
            savePst.setLong(13, Long.parseLong(map.get("IENV")));
            savePst.setInt(14, flag);
            savePst.setLong(15, Long.parseLong(map.get("IUSERID")));
            savePst.setString(16, "");
            savePst.setLong(17, Long.parseLong(String.valueOf(map.get("ICPU"))));
            savePst.setLong(18, Long.parseLong(String.valueOf(map.get("IMEM"))));
            savePst.setLong(19, Long.parseLong(map.get("IDISK") == null ? "50" : map.get("IDISK")));
            savePst.setLong(20, Long.parseLong(map.get("IMODEL")));
            savePst.setLong(21, Long.parseLong(resid));
            savePst.setString(22, newdbid);
            savePst.setString(23, version);
            savePst.setString(24, rac);
            savePst.executeUpdate();
            conn.commit();
        } catch (Exception ex)
        {
            _log.error("saveDataSource", ex);
        } finally
        {
            DBResource.closePSConn(conn, savePst, method, _log);
        }
    }

    public Connection getExtendJdbcConnection ( Map map ) throws Exception
    {
        Connection con = null;
        String key = "";
        String dbId = String.valueOf(map.get("DBID"));
        String dbType = String.valueOf(map.get("IRSTYPE"));
        String sid = String.valueOf(map.get("ISID"));
        String ip = String.valueOf(map.get("IIP"));
        String port = String.valueOf(map.get("IDBPORT"));
        String usr = String.valueOf(map.get("IDBUSER"));
        String pwd = String.valueOf(map.get("IDBPWD"));
        key = dbId + "$$" + dbType + "$$" + sid + "$$" + usr + "$$" + pwd + "$$" + ip + "$$" + port;
        int flag = 0;
        try
        {
            if ("oracle".equalsIgnoreCase(dbType))
            {
                Class.forName("oracle.jdbc.driver.OracleDriver");
                con = DriverManager.getConnection("jdbc:oracle:thin:@//" + ip + ":" + port + "/" + sid, usr, pwd);
            } else if ("mysql".equalsIgnoreCase(dbType))
            {
                Class.forName("com.mysql.jdbc.Driver");
                con = DriverManager.getConnection("jdbc:mysql://" + ip + ":" + port + "/" + sid + "?serverTimezone=UTC",
                        usr, pwd);
            } else if ("db2".equalsIgnoreCase(dbType))
            {
                Class.forName("com.ibm.db2.jcc.DB2Driver");
                con = DriverManager.getConnection("jdbc:db2://" + ip + ":" + port + "/" + sid, usr, pwd);
            }
            return con;
        } catch (Exception e)
        {
            _log.error("创建数据库连接异常 " + key, e);
            throw e;
        }
    }

    public Map getDataBaseDbId ( Map map )
    {
        Map retMap = new HashMap();
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        String dbid = "";
        String rac = "FALSE";
        Connection con = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        PreparedStatement psv = null;
        ResultSet rsv = null;
        try
        {
            con = this.getExtendJdbcConnection(map);
            String dbType = String.valueOf(map.get("IRSTYPE"));
            String sid = String.valueOf(map.get("ISID"));
            String sqlversion = " select a.HOST_NAME,a.VERSION,a.STATUS,a.ARCHIVER,a.DATABASE_STATUS,a.ACTIVE_STATE from v$instance a where instance_name=?";
            String sql = "select DBID from v$database";
            if ("oracle".equalsIgnoreCase(dbType))
            {
                sql = "select DBID from v$database";
                sqlversion = " select a.VERSION from v$instance a";
                ps = con.prepareStatement(sql);
                rs = ps.executeQuery();
                while (rs.next())
                {
                    dbid = rs.getString(1);
                }
                String sqlRac = " select value from v$option a where a.PARAMETER='Real Application Clusters'";
                ps = con.prepareStatement(sqlRac);
                rs = ps.executeQuery();
                while (rs.next())
                {
                    rac = "TRUE".equalsIgnoreCase(rs.getString(1)) ? "TRUE" : "FALSE";
                }
            } else if ("mysql".equalsIgnoreCase(dbType))
            {
                sqlversion = "select @@version";// select version()

            } else if ("db2".equalsIgnoreCase(dbType))
            {
                sqlversion = " SELECT SERVICE_LEVEL FROM SYSIBMADM.ENV_INST_INFO";// select
                // version()
            }
            psv = con.prepareStatement(sqlversion);
            rsv = psv.executeQuery();
            String version = "";
            while (rsv.next())
            {
                version = rsv.getString(1) == null ? "" : rsv.getString(1);
            }
            retMap.put("VERSION", version);
            retMap.put("DBID", dbid);
            retMap.put("RAC", rac);
            retMap.put("success", true);
        } catch (Exception e)
        {
            dbid = e.getMessage();
            retMap.put("DBID", "获取DBID和version信息异常 " + e.getMessage());
            retMap.put("success", false);
            _log.error("获取DBID和version异常 " + e.getMessage());
        } finally
        {
            DBResource.closePSRS(rs, ps, method, _log);
            DBResource.closeConn(con, rsv, psv, method, _log);
        }
        return retMap;
    }

    public Map<String, String> getResourcesManagerList ( long iid )
    {
        Connection con = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        Map<String, String> map = new HashMap();
        try
        {
            con = DBResource.getConnection("getResourcesManagerList", _log, Constants.IEAI_IEAI_BASIC);
            String sql = " select  t.IID,t.IDBID,t.icheck,t.istatus, t.INAME, t.IIP,t.IDBPORT,t.IRSTYPE,t.IFLAG, t.IBUSINESS, t.IDBUSER, t.IDBPWD, t.ICOPYNUM, u.ifullname AS iapplyuser,u.IID AS IUSERID,u.IEMAIL, t.ISID,t.ITYPE,T.IENV  FROM  IEAI_RESOURCE_MANAGE t,ieai_user u where t.IISDEL=0 and u.iloginname = t.iapplyuser and  t.iid="
                    + iid;
            pstmt = con.prepareStatement(sql);
            rs = pstmt.executeQuery();
            while (rs.next())
            {
                map.put("IID", rs.getString("IID"));
                map.put("IP", rs.getString("IIP"));
                map.put("PORT", rs.getString("IDBPORT"));
                map.put("DBTYPE", rs.getString("IRSTYPE"));
                map.put("IFLAG", rs.getString("IFLAG"));
                map.put("IBUSINESS", rs.getString("IBUSINESS"));
                map.put("USR", rs.getString("IDBUSER"));
                if (!"".equals(rs.getString("IDBPWD")) && null != rs.getString("IDBPWD"))
                {
                    map.put("PWD", Jcrypt.decrypt(rs.getString("IDBPWD")));
                } else
                {
                    map.put("PWD", "");
                }
                map.put("SID", rs.getString("ISID"));
                map.put("ITYPE", rs.getString("ITYPE"));
                map.put("IENV", rs.getString("IENV"));
                map.put("ICHECK", rs.getString("ICHECK"));
                map.put("ISTATUS", rs.getString("ISTATUS"));
                map.put("IDBID", rs.getString("IDBID"));
                map.put("DBINFO", rs.getString("IRSTYPE") + "$" + rs.getString("IIP") + ":" + rs.getString("IDBPORT")
                        + "/" + rs.getString("ISID") + "$" + rs.getString("IDBUSER"));
                map.put("USERNAME", rs.getString("iapplyuser"));
                map.put("USERID", rs.getString("iUSERID"));
                map.put("ADDRESS", rs.getString("IEMAIL"));
            }
        } catch (Exception e)
        {
            _log.error("getResourcesManagerList is error ! " + e.getMessage());
        } finally
        {
            DBResource.closeConn(con, rs, pstmt, "getResourcesManagerList", _log);
        }
        return map;
    }

    public Map getResAppRecordInfo ( Long id )
    {
        Map mapBack = new HashMap();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rset = null;
        PreparedStatement ps2 = null;
        ResultSet rset2 = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_SCRIPT_SERVICE);
            String sql = "  SELECT T.OPERATIONSYSTEM AS IBUSINESS,  t.IIP,  '1521' AS IDBPORT,t.SPECIFICATION,  T.DATATYPE AS IRSTYPE,  t.IAPPLYUSER,  (CASE WHEN t.ENV=1 THEN 0 WHEN t.ENV=3 THEN 1 ELSE 2 END) AS IENV,  (SELECT iid FROM ieai_user u  WHERE u.iloginname=t.iapplyuser) AS  IUSERID,t.USEDATE,T.SETMODEL  FROM IEAI_RESOURCE_APPLY T  WHERE T.IID=?";

            ps = conn.prepareStatement(sql);
            ps.setLong(1, id);
            rset = ps.executeQuery();
            while (rset.next())
            {
                mapBack.put("IBUSINESS", rset.getString("IBUSINESS"));
                mapBack.put("IIP", rset.getString("IIP"));
                mapBack.put("IDBPORT", rset.getString("IDBPORT"));
                int ispecifi = rset.getInt("SPECIFICATION");
                long cpu = 0;
                long mem = 0;
                String ispecifiString = "";
                switch (ispecifi)
                {
                    case 1:
                        cpu = 1;
                        mem = 1;
                        break;
                    case 2:
                        cpu = 1;
                        mem = 2;
                        break;
                    case 3:
                        cpu = 2;
                        mem = 4;
                        break;
                    case 4:
                        cpu = 2;
                        mem = 8;
                        break;
                    case 5:
                        cpu = 4;
                        mem = 8;
                        break;
                    case 6:
                        cpu = 4;
                        mem = 16;
                        break;
                    case 7:
                        cpu = 8;
                        mem = 16;
                        break;
                    case 8:
                        cpu = 8;
                        mem = 32;
                        break;
                    case 9:
                        cpu = 16;
                        mem = 64;
                        break;
                }
                mapBack.put("ICPU", cpu);
                mapBack.put("IMEM", mem);
                mapBack.put("IRSTYPE", rset.getString("IRSTYPE"));
                mapBack.put("IAPPLYUSER", rset.getString("IAPPLYUSER"));
                mapBack.put("IUSEDATE", rset.getString("USEDATE"));
                mapBack.put("IENV", rset.getString("IENV"));
                mapBack.put("IUSERID", rset.getString("IUSERID"));
                mapBack.put("IMODEL", rset.getString("SETMODEL"));
            }
            String paramSql = "SELECT INAME,IVALUE FROM IEAI_SCRIPT_RESAPP_PARAM_VALUE WHERE IRESID = ?";
            ps2 = conn.prepareStatement(paramSql);
            ps2.setLong(1, id);
            rset2 = ps2.executeQuery();
            int ii = 0;
            while (rset2.next())
            {
                String name = rset2.getString("INAME");
                if (null != name)
                {
                    name = name.toUpperCase();
                }
                // if (ii == 6)
                // {
                // break;
                // }
                String value = rset2.getString("IVALUE");
                if ("SID".equals(name))
                {
                    ii++;
                    mapBack.put("ISID", value);
                    continue;
                }
                if ("DBUSERNAME".equals(name))
                {
                    ii++;
                    mapBack.put("IDBUSER", value);
                    continue;
                }
                if ("DBPASSWORD".equals(name))
                {
                    ii++;
                    mapBack.put("IDBPWD", value);
                    continue;
                }
                if ("TABLESPACETOTAL".equals(name))
                {
                    ii++;
                    mapBack.put("IDISK", value);
                    continue;
                }
                if ("TABLESPACENAME".equals(name))
                {
                    ii++;
                    mapBack.put("TABLESPACENAME", value);
                    continue;
                }
            }
        } catch (Exception e)
        {
            _log.error("getResAppRecordInfo", e);
        } finally
        {
            DBResource.closePSRS(rset2, ps2, "getResAppRecordInfo", _log);
            DBResource.closeConn(conn, rset, ps, "getResAppRecordInfo", _log);
        }
        return mapBack;
    }

    public void updateExecActsStartStateForTmpDisable ( long stepId, long flowId, long state, long failstate, int sysType) throws RepositoryException
    {
        DbOpScheduler opScheduler = new DbOpScheduler();
        opScheduler.updateExecActsStartStateForTmpDisable(stepId, flowId, state, failstate, sysType);
    }

    public void updateScriptContent ( String requestid, String scriptContent ) throws RepositoryException
    {
        DbOpScheduler opScheduler = new DbOpScheduler();
        opScheduler.updateScriptContent(requestid, scriptContent);
    }
    public void updateExecActsInitState ( List<ActExcelToRunData> delayList,int istate ) throws RepositoryException
    {
        String sql = " update ieai_runinfo_instance ar set ar.istate=?  where  ar.IID=?";
        for (int i = 0;; i++)
        {
            try
            {
                Connection con = null;
                PreparedStatement ps = null;
                try
                {
                    con = DBResource.getSchedulerConnection("updateExecActsInitState", _log, Constants.IEAI_IEAI_BASIC);
                    ps = con.prepareStatement(sql);
                    if(delayList!=null&&!delayList.isEmpty())
                    {
                        for (ActExcelToRunData aetrd:delayList)
                        {
                            ps.setInt(1, istate);
                            ps.setLong(2, aetrd.getIid());
                            ps.addBatch();
                        }
                        ps.executeBatch();
                        // ps.executeUpdate();
                        con.commit();
                    }

                } catch (SQLException e)
                {
                    String method = Thread.currentThread().getStackTrace()[1].getMethodName();
                    _log.error(method + " is error at DbOpScheduoler ", e);
                    // 失败后数据回滚
                    DBResource.rollback(con, ServerError.ERR_DB_UPDATE, e, method, _log);
                } finally
                {
                    DBResource.closePSConn(con, ps, "updateExecActsInitState", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
    }

    public String getAreaUserName(String name)
    {
        String username = name;
        if(username!=null&&username.indexOf("/")>0)
        {
            username=username.substring(username.indexOf("/")+1,username.length());
        }
        return username;
    }
    
    /**
     * 
     * <li>Description:拉取函数</li> 
     * <AUTHOR>
     * 2022年5月30日 
     * @param requestId
     * @param type
     * @return
     * @throws UnMarshallingException
     * @throws RepositoryException
     * return RemoteActivityExecRequest
     */
    public RemoteActivityExecRequest getRexecRequestOfFuncExec ( String requestId, int type )
            throws UnMarshallingException, RepositoryException
    {
        RemoteActivityExecRequest repAct = new RemoteActivityExecRequest();
        repAct.setId(requestId);
        
        try {
            String[] split = requestId.split(Constants.AGENT_PULL_FUNC_REQID_SPLIT);
            if(split.length<3) {
                _log.error("agent端发来请求，server端获取拼接请求ID格式错误！");
                return repAct;
            }
            String requestid = split[1];
            String agentIpAndport = split[2];//需要判断是否有冒号，如果有就是ip:port格式，如果没有则为port
            String paramJson = split[3];
//            _log.info("需要拉取函数内容的函数名称如下："+paramJson);
            List<String> funcnames = JSONArray.parseArray(paramJson,String.class);
            
            List<Map> list = new ArrayList<Map>();
            IScriptInstance dubboBean = IeaiSpringContextUtil.getDubboBean(IScriptInstance.class);
            List<ScriptFunctionLibrary> pullFunctionFiles = dubboBean.pullFunctionFiles(funcnames);
            for(ScriptFunctionLibrary bean:pullFunctionFiles) {
                Hashtable map = new Hashtable();
                map.put("iname", bean.getIname());
                map.put("isupportLanguage", bean.getIsupportLanguage());
                map.put("functionContent", bean.getFunctionContent());
                map.put("functionMd", bean.getFunctionMd());
                list.add(map);
            }
            Hashtable mapFunc = new Hashtable();
            String funcs = "";
            if(list!=null && !list.isEmpty()){
                funcs = JSON.toJSONString(list);
            }
            
            
            mapFunc.put("funcs", funcs);
            //调用函数获取接口
            repAct.setFuncMap(mapFunc);
        }catch (Exception e) {
           _log.error("agent端发来请求，获取函数变更拉取，响应失败！", e);
        }
        
        return  repAct;
    }

    public static List getAneexBinary(String uuid, int systype) throws RepositoryException
    {
        //开启了 任务申请 支持临时附件开关，并且workitemid不为0 否则还查原有的脚本绑定的附件表
        String sql = "SELECT A.INAME ,A.ICONTENTS  FROM IEAI_SCRIPT_ATTACHMENT A WHERE A.ISCRIPTIID = ?";
        List annexFiles = new ArrayList();
        try
        {
            Connection con = null;
            PreparedStatement ps = null;
            ResultSet rs = null;
            try
            {
                con = DBManager.getInstance().getJdbcConnection(systype);
                ps = con.prepareStatement(sql);
                ps.setString(1, uuid);                   
                rs = ps.executeQuery();
                while (rs.next())
                {
                    BinaryEntityFiles blobEntity = new BinaryEntityFiles();
                    blobEntity.setName(rs.getString("INAME"));
                    InputStream in = null;
                    ByteArrayOutputStream out = new ByteArrayOutputStream();
                    if (rs.getBlob("ICONTENTS") != null)
                    {
                        try
                        {
                            in = rs.getBlob("ICONTENTS").getBinaryStream();
                        } catch (SQLException e2)
                        {
                            _log.error("error while getting inputStream from a blob with it's id " + uuid, e2);
                            throw new RepositoryException(ServerError.ERR_DB_ERROR);
                        }
                        byte buff[] = new byte[1000];

                        try
                        {
                            while (true)
                            {
                                int c = in.read(buff);
                                if (c <= 0)
                                {
                                    break;
                                }
                                out.write(buff, 0, c);
                            }

                        } catch (IOException e)
                        {
                            _log.error("error while reading from a blob with it's id " + uuid, e);
                            throw new RepositoryException(ServerError.ERR_DB_ERROR);

                        } finally
                        {
                            try
                            {
                                in.close();
                                out.close();
                            } catch (IOException e1)
                            {
                                _log.error("error while closing stream", e1);
                                throw new RepositoryException(ServerError.ERR_DB_ERROR);
                            }
                        }
                        blobEntity.setContent(out.toByteArray());
                        annexFiles.add(blobEntity);
                    }

                }

            } catch (SQLException e)
            {

                _log.error("getAneexBinary  SQLException " + e.getMessage());
                try
                {
                    con.rollback();
                    throw new RepositoryException(ServerError.ERR_DB_INSERT);
                } catch (SQLException ex1)
                {
                    _log.error("getAneexBinary SQLException Rollback Error " + ex1.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_INSERT);
                }
            } catch (DBException e)
            {
                _log.error("getAneexBinary is error at DBException " + e.getMessage());
                throw new RepositoryException(ServerError.ERR_DB_ERROR);
            } finally
            {
                try
                {
                    if (rs != null)
                        rs.close();
                    if (ps != null)
                        ps.close();
                    if (con != null)
                        con.close();
                } catch (SQLException ex)
                {
                    _log.error("Close Connection Of  State Error", ex);
                }
            }
        } catch (RepositoryException ex)
        {
            try
            {
                Thread.sleep(1000);
            } catch (InterruptedException e)
            {
            }
        }
        // byte[] result = out.toByteArray();
        return annexFiles;
    }
    
 
    
    public String loadBalanceOrderIP ( long group, List addrs, Connection con ) throws RepositoryException
    {
        String ip = "";
        long dateBasepollcycle = ServerEnv.getServerEnv().getMonitertimeoutpollcycle();

        if (dateBasepollcycle < 360)
        {
            dateBasepollcycle = 360;
        }
        LoadBalanceRepository balance = new LoadBalanceRepository();
        List b = balance.getServerListExecl(group, addrs, con);
        List a = balance.getServerOfOrder(group, addrs, con);
        if (b.size() > a.size())
        {
            for (int i = 0; i < b.size(); i++)
            {
                if (!a.contains(b.get(i)))
                {
                    ip = (String) b.get(i);
                    if (balance.updateServerList(con, ip, dateBasepollcycle))
                    {
                        return ip;
                    }
                }
            }
        }
        for (int i = 0; i < a.size(); i++)
        {
            ip = (String) a.get(i);
            if (balance.updateServerList(con, ip, dateBasepollcycle))
            {
                return ip;
            }
        }
        return ip;
    }
    public List getPrenerInfo ( Map map ) throws ServerException, RepositoryException
    {
        String iprener = map.get("iprener") == null ? "-100" : String.valueOf(map.get("iprener"));
        List list = new ArrayList();
        //        String sql = "select b.istdout from ieai_runinfo_instance a,ieai_shellcmd_output b where a.iid=b.istepid and a.iruninsid=? and a.iserner in ("
        //                + iprener + ")";
        String sql = "select b.istdout,a.iserner\r\n" +
                "  from (select max(iid) as iid,iserner\r\n" +
                "          from ieai_runinfo_instance\r\n" +
                "         where iruninsid = ?\r\n" +
                "           and iserner in ("+iprener+")\r\n" +
                "         group by iserner) a left join" +
                "       ieai_shellcmd_output b\r\n" +
                " on a.iid = b.istepid";
        for (int i = 0;; i++)
        {
            try
            {
                Connection conn = null;
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                try
                {
                    conn = DBManager.getInstance().getSchedulerConnection(Constants.IEAI_IEAI_BASIC);
                    actStat = conn.prepareStatement(sql);
                    actStat.setLong(1, Long.valueOf(String.valueOf(map.get("iruninsid"))));
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        Map mm = new HashMap();
                        mm.put("stdout", actRS.getString("istdout"));
                        mm.put("serner", actRS.getString("iserner"));
                        list.add(mm);
                    }
                } catch (DBException e)
                {
                    _log.error("getPrenerInfo() is error at DbOpScheduoler " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_INIT);
                } catch (SQLException e)
                {
                    _log.error("getPrenerInfo() is error at DbOpScheduoler " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {

                    if (null != actStat)
                    {
                        try
                        {
                            actStat.close();
                        } catch (SQLException e)
                        {
                            _log.error("getPrenerInfo is error at DbOpScheduoler actStat.close() " + e.getMessage());
                        }
                        actStat = null;
                    }
                    if (null != actRS)
                    {
                        try
                        {
                            actRS.close();
                        } catch (SQLException e)
                        {
                            _log.error("getPrenerInfo is error at DbOpScheduoler actRS.close() " + e.getMessage());
                        }
                        actRS = null;
                    }
                    try
                    {
                        if (null != conn && !conn.isClosed())
                        {
                            conn.close();
                        }
                    } catch (SQLException e)
                    {
                        _log.error("getPrenerInfo is error at DbOpScheduoler conn.close() " + e.getMessage());
                        conn = null;
                        throw new RepositoryException(ServerError.ERR_DB_ERROR);
                    }
                    conn = null;
                }
                break;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
        return list;
    }
    /**
     * Perform query on workflow instances based-on the specified flowFilter.
     *
     * @param flowFilter
     * @return List of WorkflowInfo
     * @throws RepositoryException
     */
    public List queryJZFlow ( WorkflowFilter flowFilter, int systype ,String sysName) throws RepositoryException
    {
        long starttime = System.currentTimeMillis();
        // int totalRecord = queryWorkflowTotalRecord(flowFilter).intValue();

        StringBuffer criteria = new StringBuffer();
        // project name fileter
        if (!StringUtils.isBlank(flowFilter.projName))
        {
            criteria.append(" and flowinfo.iprojectname = '").append(flowFilter.projName + "'");
        }

        // 添加上一次启动的条件 start yangyang_wang 2009.6.04
        if ("true".equals(flowFilter.selectLast))
        {
            try
            {
                String _time = getLastServerTime();
                if (_time != null)
                {
                    Long lastTime = Long.valueOf(_time);
                    criteria.append(" and ( flowinfo.istarttime > ").append(lastTime)
                            .append(" or flowinfo.recovertime > ").append(lastTime).append(")");
                }
            } catch (Exception e)
            {
            }

        }
        // end

        // flow name filter
        if (null != flowFilter.flowName && flowFilter.flowName.length() > 0)
        {
            criteria.append(" and flowinfo.iflowname like '").append("%" + flowFilter.flowName + "%'");
        }
        // start user filter
        if (null != flowFilter.startUser && flowFilter.startUser.length() > 0)
        {
            criteria.append(" and flowinfo.istartuserfullname like '").append("%" + flowFilter.startUser + "%'");
        }
        // ///// StartTime 1
        if (null != flowFilter.startTime1)
        {
            criteria.append(" and flowinfo.istarttime > ").append(flowFilter.startTime1.getTime());
        }
        // ////// StartTime 2
        if (null != flowFilter.startTime2)
        {
            criteria.append(" and flowinfo.istarttime < ").append(flowFilter.startTime2.getTime());
        }
        // ////// EndTime 1
        if (null != flowFilter.endTime1)
        {
            criteria.append(" and flowinfo.iendtime > ").append(flowFilter.endTime1.getTime());
        }
        // ///// EndTime 2
        if (null != flowFilter.endTime2)
        {
            criteria.append(" and flowinfo.iendtime < ").append(flowFilter.endTime2.getTime());
        }
        // flow comment
        if (null != flowFilter.commentKeyList)
        {
            for (Iterator i = flowFilter.commentKeyList.iterator(); i.hasNext();)
            {
                String key = (String) i.next();
                criteria.append(" and flowinfo.iflowcomment like '").append("%" + key + "%'");
            }
        }
        // flow state
        if (-1 != flowFilter.state)
        {
            // 原有代码
            // criteria.append(" and flowinfo.istatus = ").append(flowFilter.state);
            // 改造代码 by yue_sun on 2018-04-11
            if (Constants.STATE_UNEXPECTED_END == flowFilter.state)
            {
                criteria.append(" and flowinfo.istatus in (" + Constants.STATE_UNEXPECTED_END + ","
                        + Constants.STATE_UNEXPECTED_END_PAUSE + ")");
            } else
            {
                criteria.append(" and flowinfo.istatus = ").append(flowFilter.state);
            }
        }
        // only show activity flow.
        if (flowFilter.actFlowsOnly)
        {
            criteria.append(" and flowinfo.istatus in (" + Constants.STATE_RUNNING + "," + Constants.STATE_LISTENING
                    + "," + Constants.STATE_RECOVERING + "," + Constants.STATE_PAUSED + "," + Constants.STATE_STOPPING
                    + ")");
        }
        // flow type
        if (WorkflowFilter.ALL != flowFilter.flowType)
        {
            Boolean type = flowFilter.flowType == WorkflowFilter.SAFE_FIRST_FLOW ? Boolean.TRUE : Boolean.FALSE;
            int isafefirst = 0;
            if (type.booleanValue())
            {
                isafefirst = 1;
            }
            criteria.append(" and flowinfo.iissafefirst =").append(isafefirst);
        }
        if (flowFilter.autoStartFlowsOnly)
        {
            criteria.append(" and flowinfo.iisautostart = 1");
        }

        if (null != flowFilter.flowInsName && flowFilter.flowInsName.length() > 0)
        {
            criteria.append(" and flowinfo.iflowinsname like '").append("%" + flowFilter.flowInsName + "%'");
        }

        if (null != flowFilter.flowId)
        {
            criteria.append(" and flowinfo.iflowid like ").append(flowFilter.flowId);
        }

        // Get flowinfo from ieai_flowinfo table basedon the criteria
        StringBuffer hqlGetFlows = new StringBuffer("select * from ieai_workflowinstance flowinfo ")
                .append(" where 1=1 and flowinfo.ihostname in (select l.ip from ieai_serverlist l where l.groupid= "
                        + systype + ") ");
        // begin.added by manxi_zhao.2016-09-10.
        if (null != sysName && sysName.length() > 0)
        {
            sysName = "AND prj.isysName ='"+ sysName+"'";
        }else {
            sysName = "";
        }
        if (null != flowFilter.projType)
        {
            hqlGetFlows = new StringBuffer();
            hqlGetFlows.append("select flowinfo.*, prj.isysname from ieai_workflowinstance flowinfo ");
            hqlGetFlows.append(" , IEAI_PROJECT prj ");// added
            hqlGetFlows
                    .append(" where 1=1  "+sysName+" and flowinfo.ihostname in (select l.ip from ieai_serverlist l where l.groupid= "
                            + systype + ") ");
            // hqlGetFlows.append(" and flowinfo.IPRJUPPERID=prj.IID and
            // prj.PROTYPE="+flowFilter.projType);//added
            hqlGetFlows.append(" and flowinfo.IPRJUPPERID=prj.IUPPERID and prj.PROTYPE=" + flowFilter.projType);// added
        }
        if (null != flowFilter.inpermPrjIdsStr)// 权限条件过滤
        {
            // 没有任何权限
            if ("".equals(flowFilter.inpermPrjIdsStr))
            {
                hqlGetFlows.append(" and prj.IID is null ");
            } else
            {
                hqlGetFlows.append(" and prj.IID in (" + flowFilter.inpermPrjIdsStr + ") ");
            }
        }
        // end.added by manxi_zhao.2016-09-10.
        hqlGetFlows.append(criteria);
        String countSql = "select count(1) as cou from (" + hqlGetFlows.toString() + ") t ";
        int totalRecord = ObjectStorerDB.count(countSql, systype);

        if (true == flowFilter.order_isord)
        {
            setOrderBy2(hqlGetFlows, flowFilter);
        } else if (null != flowFilter.sortIndex)
        {
            if (true == flowFilter.isQueryOrder())
            {
                setOrderBy3(hqlGetFlows, flowFilter);
            } else
            {
                setOrderBy(hqlGetFlows, flowFilter);
            }
        } else
        {
            hqlGetFlows.append(" order by flowinfo.istarttime desc");
        }

        List queryResultGetFlows = null;
        if (flowFilter.canPage)
        {
            queryResultGetFlows = ObjectStorerDB.findForPageRepWorkflowInstance(hqlGetFlows.toString(),
                    flowFilter.pageSize, flowFilter.curPage, systype);
        } else
        {
            queryResultGetFlows = ObjectStorerDB.findRepWorkflowInstance(hqlGetFlows.toString(), systype);
        }

        List flowInfoResult = new ArrayList();
        for (Iterator iter = queryResultGetFlows.iterator(); iter.hasNext();)
        {
            RepWorkflowInstance flowResult = (RepWorkflowInstance) iter.next();
            WorkflowInstance instance = flowResult.toCommons();
            WorkflowInfo info = instance.getWorkflowInfo();

            StringBuffer hqlGetNumTasks = new StringBuffer(
                    "select flowinfo.iflowid, count(taskconstant.iid) as taskid from ")
                    .append(" ieai_workflowinstance flowinfo, ")
                    .append(" ieai_taskconstant taskconstant where flowinfo.iflowid=taskconstant.iflowid");

            Long flowInfoId = new Long(instance.getFlowId());
            hqlGetNumTasks.append(" and flowinfo.iflowid=" + flowInfoId + " ");

            hqlGetNumTasks.append(criteria);
            hqlGetNumTasks.append(" group by flowinfo.iflowid");

            List queryResultGetNumTasks = ObjectStorerDB.getNumTasks(hqlGetNumTasks.toString(), systype);
            // Integer i = (Integer) mapFlowTonumTasks.get(new
            // Long(instance.getFlowId()));
            Object[] record = null;
            Integer i = null;

            if (queryResultGetNumTasks.size() > 0)
            {
                record = (Object[]) queryResultGetNumTasks.get(0);
                i = (Integer) record[1];
            }

            // Integer i = (Integer) mapFlowTonumTasks.get(new
            // Long(instance.getFlowId()));

            if (i != null)
            {
                info.setTaskNum(i.intValue());
            }
            // begin.by manxi_zhao.2016.09.22.
            // 注释掉旧的权限过滤代码 ,权限过滤作为条件拼接在sql中完成
            // // if the permission is set,use it to filter the query result;
            // if (flowFilter.permissions != null)
            // {
            // if (!(Constants.PERM_ENABLED == flowFilter.permissions.checkPermission(Constants.PRJ,
            // instance.getProjectName(), Constants.PERM_FLOW_GETINFO)))
            // {
            // continue;
            // }
            // }
            // end.by manxi_zhao.2016.09.22.
            info.setTotalRecord(totalRecord);
            flowInfoResult.add(info);
        }
        if (SystemConfig.isElapsedTime())
            _log.info("Stamp:EngineRepository.queryflow:" + (System.currentTimeMillis() - starttime));
        return flowInfoResult;
    }
}
