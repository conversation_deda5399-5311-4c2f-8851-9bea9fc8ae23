package com.ideal.ieai.server.engine;

import EDU.oswego.cs.dl.util.concurrent.PooledExecutor;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.dhcc.itsm.cmdb.webservice.server.service.alarm.Alarm;
import com.ideal.ieai.adaptors.commadaptors.dbconnection.ConnectionPoolManage;
import com.ideal.ieai.adaptors.commadaptors.dbconnection.ConnectionPoolManageBean;
import com.ideal.ieai.cbb.socket.RPASocketServerThread;
import com.ideal.ieai.cbb.strategy.manager.StrategyBean;
import com.ideal.ieai.cbb.strategy.service.GroupExecutionStrategyHandler;
import com.ideal.ieai.cbb.strategy.thread.ExecutionStartThread;
import com.ideal.ieai.cbb.strategy.thread.ExecutionStrategyThread;
import com.ideal.ieai.commons.*;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.core.IEAIRuntime;
import com.ideal.ieai.core.ProjectAdaptorCache;
import com.ideal.ieai.core.activity.*;
import com.ideal.ieai.core.element.Project;
import com.ideal.ieai.core.element.Resource;
import com.ideal.ieai.core.element.Workflow;
import com.ideal.ieai.core.element.workflow.ActivityElement;
import com.ideal.ieai.core.element.workflow.BasicActElement;
import com.ideal.ieai.core.element.workflow.ExitActElement;
import com.ideal.ieai.core.io.UnMarshallingException;
import com.ideal.ieai.server.ServerException;
import com.ideal.ieai.server.SystemConfig;
import com.ideal.ieai.server.alert.StartAndStopFlowDealer;
import com.ideal.ieai.server.cluster.monitor.*;
import com.ideal.ieai.server.cmdb.cqns.CQNS_CmdbMonitorThread;
import com.ideal.ieai.server.cmdb.cqns.thread.CqCmdbTxtThread;
import com.ideal.ieai.server.cmdb.gd.thread.Gd_IAMThread;
import com.ideal.ieai.server.cmdb.gd.thread.Gd_UserSyncThread;
import com.ideal.ieai.server.cmdb.service.CmdbMonitorThread;
import com.ideal.ieai.server.cmdb.service.SyncCmdbServerInfoDeletingSchedulerService;
import com.ideal.ieai.server.cmdb.service.SyncCmdbServerInfoSchedulerService;
import com.ideal.ieai.server.cmdb.service.SynchronizationCmdbSchedulerThread;
import com.ideal.ieai.server.cmdb.zy.ZyIncrementDataExecuteThread;
import com.ideal.ieai.server.compare.thread.CompareStartSwitchThread;
import com.ideal.ieai.server.datacollect.job.QuartzMainServerThread;
import com.ideal.ieai.server.datacollect.job.QuartzThread;
import com.ideal.ieai.server.datacollect.thread.CleanDataThread;
import com.ideal.ieai.server.datacollect.thread.DataCollectAutoControlThread;
import com.ideal.ieai.server.datacollect.thread.DataCollectAutoTaskStateThread;
import com.ideal.ieai.server.distributedtask.DistributedTaskExecutor;
import com.ideal.ieai.server.dm.bean.FlowPlanBean;
import com.ideal.ieai.server.dm.repository.task.thread.ICTaskScreenThread;
import com.ideal.ieai.server.dm.thread.StandardTaskStateMonitorThread;
import com.ideal.ieai.server.ebanksmdb.SmdbResultQueueThread;
import com.ideal.ieai.server.emergency.repository.doublereview.TaskAutoBackThread;
import com.ideal.ieai.server.engine.agent.ActivityRExecHelper;
import com.ideal.ieai.server.engine.agent.AgentBean;
import com.ideal.ieai.server.engine.agent.AgentUpdateThread;
import com.ideal.ieai.server.engine.burst.BurstCleaner;
import com.ideal.ieai.server.engine.burst.BurstThread;
import com.ideal.ieai.server.engine.collectconfig.CollectConfigMonitorThread;
import com.ideal.ieai.server.engine.collectconfig.HaInfoAlignThread;
import com.ideal.ieai.server.engine.core.*;
import com.ideal.ieai.server.engine.errortask.ExceptionMunualHandingManager;
import com.ideal.ieai.server.engine.exceptionhandler.ExceptionHandingHelper;
import com.ideal.ieai.server.engine.exceptionhandler.IExceptionHandingHelper;
import com.ideal.ieai.server.engine.execactivity.*;
import com.ideal.ieai.server.engine.hcresult.HcAnalyTicleResultQueueThread;
import com.ideal.ieai.server.engine.opm.OpmMessageThread;
import com.ideal.ieai.server.engine.recover.RecoverManagerCluster;
import com.ideal.ieai.server.engine.task.TaskData;
import com.ideal.ieai.server.engine.task.TaskManager;
import com.ideal.ieai.server.engine.thread.CheckDBConnection;
import com.ideal.ieai.server.engine.thread.ProjectToCacheThread;
import com.ideal.ieai.server.engine.util.*;
import com.ideal.ieai.server.engine.workflowscycle.CheckWorkFlowsCycleThread;
import com.ideal.ieai.server.engine.workflowscycle.CheckWorkFlowsICThread;
import com.ideal.ieai.server.engine.workflowscycle.CycleSaveBusinessCpThread;
import com.ideal.ieai.server.engine.workflowscycle.CycleStartDailyOperThread;
import com.ideal.ieai.server.eswitch.thread.*;
import com.ideal.ieai.server.faultselfhealing.thread.FasDataTransferThread;
import com.ideal.ieai.server.faultselfhealing.thread.FshTaskTimeOutThread;
import com.ideal.ieai.server.faultselfhealing.thread.KafkaMessageConsumeThread;
import com.ideal.ieai.server.faultselfhealing.thread.ReadFshEmailThread;
import com.ideal.ieai.server.hd.thread.*;
import com.ideal.ieai.server.hd.thread.AgentCheckThread;
import com.ideal.ieai.server.idealcmdb.thread.CmdbGetDataAutoThread;
import com.ideal.ieai.server.ieaikernel.ConfigReader;
import com.ideal.ieai.server.ieaikernel.PersonalityEnv;
import com.ideal.ieai.server.ieaikernel.ServerEnv;
import com.ideal.ieai.server.instance.InstanceRegister;
import com.ideal.ieai.server.jobscheduling.bean.OffsetFreqBean;
import com.ideal.ieai.server.jobscheduling.bean.runtimevalid.RuntimeValidPart;
import com.ideal.ieai.server.jobscheduling.ieaikernel.EnvConfigReader;
import com.ideal.ieai.server.jobscheduling.repository.activity.ActivityReDoManager;
import com.ideal.ieai.server.jobscheduling.repository.activity.ActivityRedoBean;
import com.ideal.ieai.server.jobscheduling.repository.appsystemdaily.CheckKeyFlowThread;
import com.ideal.ieai.server.jobscheduling.repository.configinfoview.ConfigInfoViewQLThread;
import com.ideal.ieai.server.jobscheduling.repository.dbback.dbbacksql.DbbackApplicobjThread;
import com.ideal.ieai.server.jobscheduling.repository.dbback.dbojectmaintenance.DBOjectMaintenanceThread;
import com.ideal.ieai.server.jobscheduling.repository.dbback.dboptionrecord.DBOptionRecordUtil;
import com.ideal.ieai.server.jobscheduling.repository.dbback.thread.DBBackDailyMailThread;
import com.ideal.ieai.server.jobscheduling.repository.dbback.thread.DBbackClearDataThread;
import com.ideal.ieai.server.jobscheduling.repository.dbback.thread.DbInfoStatusAutoRefreshThread;
import com.ideal.ieai.server.jobscheduling.repository.dbback.thread.NohupLogDelThread;
import com.ideal.ieai.server.jobscheduling.repository.delayer.DelayerManager;
import com.ideal.ieai.server.jobscheduling.repository.engine.AvgConsumeTimeThread;
import com.ideal.ieai.server.jobscheduling.repository.engine.DailyCheckThread;
import com.ideal.ieai.server.jobscheduling.repository.engine.PlanCheckThread;
import com.ideal.ieai.server.jobscheduling.repository.engine.SaveHistoryThread;
import com.ideal.ieai.server.jobscheduling.repository.onsMessage.DateCheckManager;
import com.ideal.ieai.server.jobscheduling.repository.onsMessage.OnsProducerManager;
import com.ideal.ieai.server.jobscheduling.repository.platformmonitor.sendmonitor.SendMonitor;
import com.ideal.ieai.server.jobscheduling.repository.platformmonitor.sendmonitor.SendMonitorService;
import com.ideal.ieai.server.jobscheduling.repository.project.ProjectBean;
import com.ideal.ieai.server.jobscheduling.repository.project.ProjectManager;
import com.ideal.ieai.server.jobscheduling.repository.taskupload.UpLoadExcelManager;
import com.ideal.ieai.server.jobscheduling.repository.taskupload.UpLoadTempExcleManager;
import com.ideal.ieai.server.jobscheduling.repository.workflow.WorkFlowManager;
import com.ideal.ieai.server.jobscheduling.repository.workflowquery.WorkflowQueryManager;
import com.ideal.ieai.server.jobscheduling.thread.*;
import com.ideal.ieai.server.jobscheduling.util.taskuploadbean.ActInfoBean;
import com.ideal.ieai.server.nessus.task.KeyScanThread;
import com.ideal.ieai.server.nessus.task.LoopholeDataClearThread;
import com.ideal.ieai.server.onlineconfig.InitOnlineConifg;
import com.ideal.ieai.server.paas.cloud.CloudFlowMananger;
import com.ideal.ieai.server.paas.thread.OrderMoveToHisThread;
import com.ideal.ieai.server.paas.thread.OrderRelyStartThread;
import com.ideal.ieai.server.paas.thread.SynProjectModelToTemplateThread;
import com.ideal.ieai.server.pfproctokfnksync.kfcsToICMSSyncThread;
import com.ideal.ieai.server.pfproctokfnksync.procToKfcsSyncThread;
import com.ideal.ieai.server.platform.asynchrony.handler.AsynchronyRuntimeThread;
import com.ideal.ieai.server.platform.cmdb.thread.CmdbAutoSyncThread;
import com.ideal.ieai.server.platform.common.DistributedThread;
import com.ideal.ieai.server.platform.fileissuance.thread.FileSendMailRegularlyThread;
import com.ideal.ieai.server.platform.infoSysCapacity.thread.ClearDataCaracityThread;
import com.ideal.ieai.server.platform.portal.task.PortalThread;
import com.ideal.ieai.server.platform.repository.computerManage.AgentshellcheckThread;
import com.ideal.ieai.server.platform.repository.computerManage.SyncCmdbDataThread;
import com.ideal.ieai.server.platform.switchload.thread.MonitorRollbackWorkFlowThread;
import com.ideal.ieai.server.platform.thread.AgentGetEffectThread;
import com.ideal.ieai.server.platform.thread.DataSyncConfigurableThread;
import com.ideal.ieai.server.platform.thread.ShUserServiceTasktime;
import com.ideal.ieai.server.platform.warnmanage.UserModel;
import com.ideal.ieai.server.poc.DBUtilsNew;
import com.ideal.ieai.server.pubtoken.PublicTokenTimeoutThead;
import com.ideal.ieai.server.quartz.KafkaServerStatusSumissThread;
import com.ideal.ieai.server.queue.worker.QueuedWorkflowProcessor;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.azconfig.thread.AZSwitchTimingTaskThread;
import com.ideal.ieai.server.repository.butterfly.ButteflySyncThread;
import com.ideal.ieai.server.repository.butterflyincludesyscount.ButterflySusConditionMailThread;
import com.ideal.ieai.server.repository.butterflyincludesyscount.ButterflySusNoAomsMailThread;
import com.ideal.ieai.server.repository.butterflyincludesyscount.ButterflySusNoDevopsMailThread;
import com.ideal.ieai.server.repository.butterflyincludesyscount.ButterflySyncIncludeSysThread;
import com.ideal.ieai.server.repository.checklist.CheckListSkipErrStepThread;
import com.ideal.ieai.server.repository.ci.thread.CiTaskCicleThread;
import com.ideal.ieai.server.repository.ci.thread.CiTaskTimingThread;
import com.ideal.ieai.server.repository.customReport.CustomReportDailyCheckThread;
import com.ideal.ieai.server.repository.customReport.CustomReportPlanCheckThread;
import com.ideal.ieai.server.repository.czbpoc.CutTimeAlarmScheduler;
import com.ideal.ieai.server.repository.db.*;
import com.ideal.ieai.server.repository.dock.Thread.CmdbDayThread;
import com.ideal.ieai.server.repository.dock.Thread.CmdbThread;
import com.ideal.ieai.server.repository.dock.Thread.ItsmThread;
import com.ideal.ieai.server.repository.engine.*;
import com.ideal.ieai.server.repository.groupMessage.GroupMessageManager;
import com.ideal.ieai.server.repository.hd.actExcelToRun.ActExcelDataHanleThread;
import com.ideal.ieai.server.repository.hd.agentCheck.PollAgentChangeThread;
import com.ideal.ieai.server.repository.hd.cicd.clean.thread.CleanConfigThread;
import com.ideal.ieai.server.repository.hd.cicd.sync.thread.SeqenceAutoThread;
import com.ideal.ieai.server.repository.hd.cicd.sync.thread.SeqenceSyncThread;
import com.ideal.ieai.server.repository.hd.cicd.syncenvironment.thread.SyncEnvironmentThread;
import com.ideal.ieai.server.repository.hd.ic.alarmconfighis.HcAlarmSwitchThread;
import com.ideal.ieai.server.repository.hd.ic.hcstrategy.strategyEquipmentHcHis.HcStrategySummaryResultThread;
import com.ideal.ieai.server.repository.hd.ic.hcstrategy.strategyEquipmentHcHis.StrategyRestartThread;
import com.ideal.ieai.server.repository.hd.ic.hcstrategy.synchronization.SynchronizationCMDBPermissionThread;
import com.ideal.ieai.server.repository.hd.ic.hcstrategy.synchronization.SynchronizationCMDBThread;
import com.ideal.ieai.server.repository.hd.ic.hcstrategy.synchronization.SynchronizationCMDBToPlatfromThread;
import com.ideal.ieai.server.repository.hd.ic.supperhc.checkresult.SupperHcCheckResultQueueHandlerThread;
import com.ideal.ieai.server.repository.hd.ic.supperhc.partitionclean.SupperCleanResultPartitionThread;
import com.ideal.ieai.server.repository.hd.ic.supperhc.report.SupperHcReportThread;
import com.ideal.ieai.server.repository.hd.ic.supperhc.thread.SupperCheckResultTimeoutThread;
import com.ideal.ieai.server.repository.hd.infoCollection.thread.InfoCollectionPuThread;
import com.ideal.ieai.server.repository.hd.itsmsync.ItsmSycnUserThread;
import com.ideal.ieai.server.repository.hd.orgmanagement.OrgThread;
import com.ideal.ieai.server.repository.hd.platform.clearedata.AutoClearDataThread;
import com.ideal.ieai.server.repository.hd.platform.cmdbentity.thread.EntityModelThread;
import com.ideal.ieai.server.repository.hd.poc.db.ASMManagerThread;
import com.ideal.ieai.server.repository.hd.releaseMonitor.ReleaseMonitorManager;
import com.ideal.ieai.server.repository.hd.releaseMonitor.SendSUSAPMMailThread;
import com.ideal.ieai.server.repository.hd.scriptservice.ScriptDeleteTempAttachmentThread;
import com.ideal.ieai.server.repository.hd.scriptservice.ScriptPlatformCodeSync;
import com.ideal.ieai.server.repository.hd.timeOut.FlowTimeOutNodeInfoThreak;
import com.ideal.ieai.server.repository.idgenerator.IdGenerator;
import com.ideal.ieai.server.repository.keynodealarm.KeyNodeAlarmManager;
import com.ideal.ieai.server.repository.log.app.AppLogManager;
import com.ideal.ieai.server.repository.monitor.*;
import com.ideal.ieai.server.repository.project.ProjectManagerForMultiple;
import com.ideal.ieai.server.repository.project.ProjectSaveUtilBean;
import com.ideal.ieai.server.repository.project.RepProject;
import com.ideal.ieai.server.repository.publicnoticemessage.thread.PublicNoticTimingTaskThread;
import com.ideal.ieai.server.repository.role.ChangeUnvalidRoleThread;
import com.ideal.ieai.server.repository.sequential.SequentialSyncThread;
import com.ideal.ieai.server.repository.sequential.SequentialThread;
import com.ideal.ieai.server.repository.sus.flowstart.external.BXExternalStartPollThread;
import com.ideal.ieai.server.repository.sus.importexecl.timeout.SUSExcelTimeOutPollThread;
import com.ideal.ieai.server.repository.sus.startflow.ExecItsmAutoConfirmMergeThread;
import com.ideal.ieai.server.repository.task.UserTaskRepository;
import com.ideal.ieai.server.repository.timeSet.TimeFailFlow;
import com.ideal.ieai.server.repository.timingstart.TimingstartThread;
import com.ideal.ieai.server.repository.topo.omsubmitted.SubAlarmThread;
import com.ideal.ieai.server.repository.topo.toposys.TopoSendTriPoolThread;
import com.ideal.ieai.server.repository.topo.toposys.TopoTriPoolThread;
import com.ideal.ieai.server.repository.updatePublicPassword.UpdatePublicPassword;
import com.ideal.ieai.server.repository.user.SyncUserDownloadThread;
import com.ideal.ieai.server.repository.user.UserManager;
import com.ideal.ieai.server.repository.vmmonitormanagerpoc.VmMonitorPocThread;
import com.ideal.ieai.server.repository.webstudio.project.ProjectManagerWS;
import com.ideal.ieai.server.repository.workflow.RepActRunInfo;
import com.ideal.ieai.server.repository.workflow.WorkflowManager;
import com.ideal.ieai.server.repository.xmdb.ActiveDiscoverySyncThread;
import com.ideal.ieai.server.shutdown.thread.ShutdownTaskStateMonitorThread;
import com.ideal.ieai.server.sysChange.SysChangeThread;
import com.ideal.ieai.server.timetask.repository.manage.TTMovetoCacheThread;
import com.ideal.ieai.server.timetask.thread.*;
import com.ideal.ieai.server.toolBoxInfo.timeTask.AlarmInfoDataThread;
import com.ideal.ieai.server.toolBoxInfo.timeTask.AlarmThread;
import com.ideal.ieai.server.toolBoxInfo.timeTask.ToolInfoPollThread;
import com.ideal.ieai.server.toposerver.thread.*;
import com.ideal.ieai.server.util.WarningInterfaceUtilsIEAI;
import com.ideal.ieai.server.warn.NodeWarnFlowAct;
import com.ideal.ieai.server.warn.NodeWarnRuleCh;
import com.ideal.ieai.server.warn.TakeTimeWarningThread;
import com.ideal.ieai.server.webservice.WsErrorTaskOperation;
import com.ideal.ieai.server.webservice.agent.RemoteMainThread;
import com.ideal.util.CronDateUtils;
import com.ideal.util.DateUtil;
import com.ideal.util.StringUtil;
import org.apache.commons.dbutils.QueryRunner;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.log4j.Logger;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <li>Title: Engine.java</li>
 * <li>Project: server</li>
 * <li>Package: com.ideal.ieai.server.engine</li>
 * <li>Description: The class is facade of Engine.
 * <li>
 * <li>Copyright: Copyright (c) 2006</li>
 * <li>Company: IdealTechnologies</li>
 * <li>Created on 06-Jun-2006, 08:06:02</li>
 * 
 * <AUTHOR> liancheng_gu
 * @version iEAI v3.5
 */
public class Engine
{
    /**
     * Logger for this class
     */
    private static final Logger                      _log                                 = Logger
            .getLogger(Engine.class);

    /**
     * Engine single instance
     */
    private static Engine                            inst                                 = null;

    private ResourceManager                          resourceManager                      = null;

    private RecoverManagerCluster                    recoverManagerCluster                = null;

    /**
     * Time slice manager
     */
    private Scheduler                                scheduler                            = null;

    private BranchManager                            branchManager                        = null;

    private RecoverManager                           recoverManager                       = null;

    private ActivityContextManager                   actCtxManager                        = null;

    private PooledExecutor                           threadPoolUnlimited                  = null;

    private PooledExecutor                           threadPoolLimited                    = null;

    private PooledExecutor                           collectThreadPool                    = null;

    private PooledExecutor                           agentStateThreadPool                 = null;
    private  ThreadPoolExecutor importWebstudioswitch = null;

    /**
     * key: flowId value: WorkflowInsData object
     */
    private Map                                      efficientflowInstance                = new HashMap();

    // session
    private Map                                      mapurl                               = new ConcurrentHashMap();
    private Map                                      maptime                              = new ConcurrentHashMap();

    private ConcurrentHashMap                        collectResultHAMap                   = new ConcurrentHashMap();

    /**
     * key: Object instance. value: flow id.
     */
    private WeakHashMap                              lockMap                              = new WeakHashMap();

    private List                                     stoppingFlows                        = new ArrayList();

    /** 数据自清理单独使用的list by yue_sun on 20180504 **/
    public  List                                     stoppingIEAIFlows                    = new ArrayList();

    public Thread                                   checkStoppingFlowsThread             = null;

    private Thread                                   asm_isinsertThread                   = null;

    private Thread                                   checkDBConnectionPoolThread          = null;

    private boolean                                  isDBConnect                          = true;

    private Thread                                   checkDBConnectionThread              = null;

    // start 2010.4.12 update by zsg

    private MonitorThread                            heartbeatMonitorThread               = null;
    private MonitorThread                            maintainDbSelfStateThread            = null;
    private MonitorThread                            rotationMaintainThread               = null;
    private MonitorThread                            hearbeatSelfCheckThread              = null;
    private MonitorThread                            mailSendThread                       = null;
    private Thread                                   transCheckResultThread               = null;
    private Thread                                   syncAdUserThread                     = null;
    private Thread                                   cmdbGetDataAutoThread                = null;

    // end 2010.4.12 update by zsg

    // begin.added by manxi_zhao.2016-04-08.
    // 扫表启动、停止任务线程
    private Thread                                   timetaskFailoverThread               = null;
    private Thread                                   SyncUserGroupThread                  = null;

    // 扫表立即启动定时任务线程
    private Thread                                   timetaskImmedStartThread             = null;

    private Thread                                   timetaskAppointmentThread            = null;
    //工具箱定时任务
    private Thread                                   TimetaskToolsThread            = null;

    //工具箱执行任务
//    private Thread                                   toolPollThread            = null;

    private Thread                                   toolInfoPollThread            = null;

    //权限转移定时任务
    private Thread                                   timetaskTransferPermissionsThread            = null;

    // 计算柱状图数据线程
    private Thread                                   calculateChartDataThread             = null;
    // 扫表、强行停止实例线程
    private Thread                                   timetaskStopInsThread                = null;
    private Thread                                   syncUserDownloadThread            = null;
    private Thread                                   agentupThread                        = null;
    private Thread                                   timetaskTimeoutThread                = null;
    // end.added by manxi_zhao.2016-04-08.
    private Thread                                   timetaskTaskExceptionThread = null;

    // 延时器监控线程
    private Thread                                   execDelayMonitorThread               = null;

    private Thread                                   execItsmAutoDelayMonitorThread       = null;
    private Thread                                   execItsmAutoTimeTaskThread           = null;
    private Thread                                   timetaskMoveToHisThread              = null;
    private Thread                                   switchTaskAutoStartThread            = null;

    private Thread                                   execSendMessageItsmAutoMonitorThread = null;

    private Thread                                   checkDBPool                          = null;

    // add by yan_wang
    private Thread                                   checkDBPoolLog                       = null;

    // add by xibin_gong
    private Thread                                   agentCheckPool                       = null;

    private ActExcelDataHanleThread                  actExcelDataHanleThread              = null;

    private Thread                                   reportPlanCheckThread                = null;

    private Thread                                   reportDailyCheckThread               = null;

    private PollAgentChangeThread                    pollAgentChange                      = null;
    private Thread                                   templateCallThread                     = null;

    private AgentStateThread                         agentStatePool                       = null;
    
    private AgentLiveMainThread                      agentLiveMainThread                 = null;
    private AgentLiveThread                          agentLiveThread                       = null;
    private AgentLiveOutTimeThread                   agentLiveOutTimeThread               = null;
    
    

    private Thread                                   checkSession                         = null;

    private DBResourceMonitorThread                  dbresourceThread                     = null;

    private Thread                                   flowTimeOutNodeInfoThreak            = null;

    private Thread                                   checkAgentThread                     = null;

    // 远程活动发送主线程
    private Thread                                   sendRomteMainThread                  = null;

    // Agent并发数实时监控线程
    private Thread                                   agentConcurrentMonitorThread         = null;

    private String                                   _serverIp                            = "";

    // guogang add topo dumpdata
    private Thread                                   checkStopFlowsToToPoThread           = null;

    private Thread                                   actTimeCalculateThread               = null;

    private Thread                                   topoTriPoolThread                    = null;
    private Thread                                   topoSendTriPoolThread                = null;

    private Thread                                   avgTimeThread                        = null;
    /**
     * 拓扑图自动同步数据线程 add by wu_jian
     */
    private Thread                                   autoSyncTopoInfoThread               = null;
    /**
     * 拓扑图计算活动平均耗时并写入耗时表 add by wu_jian
     */
    private Thread                                   averageBaseLineThread                = null;
    private Thread                                   sysTimeOffAverageThread              = null;

    private Thread                                   clearDataThread                      = null;
    private Thread                                   dbbackClearDataThread                = null;
    private Thread                                   nohupLogDelThread                    = null;

    // CGB WORKFLOWCHECKThread 2017.12.07 yue_sun 从作业调度版本迁移
    private Thread                                   checkWorkFlowCycleThread             = null;
    // 日常操作工作流转移历史表线程
    private Thread                                   checkWorkFlowICThread                = null;
    
    private Thread                                   nodeWarningMqThread                = null;

    // 监控面板数据线程
    private SetJobNumThread                          setJobNumThread                      = null;

    // 渤海日启动 等待表拉起线程
    private DayStartWaitThread                       dayStartWaitThread                      = null;

    /**监控接口线程**/
    private Thread                                   monitorActThread                     = null;

    /** 数据自清理功能，杀死工作流时，清理工作流信息 by yue_sun on 2018-05-03 **/
    private Thread                                   addClearFlowsThread                  = null;
    private Thread                                   clearDataIEAIThread                  = null;

    private Thread                                   planCheckThread                      = null;

    private Thread                                   dailyCheckThread                     = null;

    private Thread                                   avgConsumeTimeThread                 = null;

    private Thread                                   checkWorkflowEndThread               = null;

    // 配置采集线程
    private Thread                                   collectConfigMonitorThread           = null;

    // 配置自动删除消息线程
    private Thread                                   opmMessageThread                     = null;

    // 配置主备拉齐线程
    private Thread                                   haInfoAlignThread                    = null;

    // 异步任务状态驱动线程
    private Thread                                   asynchronyRuntimeThread              = null;

    private Thread                                   executionStrategyThread              = null;

    private Thread                                   distributedThread                    = null;

    // 浦发银行任务状态监控线程
    private Thread                                   standardTaskStateMonitorThread       = null;

    /** 业务异常自动重试线程 move by txl 2018-5-10 **/
    private Thread                                   activityRedoThread                   = null;

    private Thread                                   bxExternalStartPollThread            = null;
    private Thread                                   burstThread                          = null;
    private Thread                                   burstCleaner                         = null;

    private Thread                                   checkActRedoDataThread               = null;

    // 浦发定时同步cmdb数据线程
    private Thread                                   syncCmdbDataThread                   = null;
    // 浦发定时同步butterfly数据线程
    private Thread                                   syncButterflyDataThread              = null;
    // 浦发定时发送应用维护/变更邮件线程
    private Thread                                   sendSUSAPMMailThread                 = null;


    /** 邮储定时CMDB同步数据线程  20220214 **/

    private Thread                                  synchronizationCMDBThread             = null;

    private Thread                                  synchronizationCMDBToPlatfromThread             = null;

    private Thread                                  synchronizationCMDBPermissionThread             = null;

    private Thread                                  hcStrategySummaryResultThread             = null;

    private Thread                                   dbOjectMaintenanceThread             = null;
    // 光大发送邮件
    private Thread                                   dbBackDailyMailThread                = null;

    private Thread                                   dbbackApplicobjThread                = null;


    // V4.7.16 yue_sun on 2019-03-14 版本 平台管理--文件下发，每日定时发送邮件给指定接收人功能的线程
    private Thread                                   fileSendMailRegularlyThread          = null;
    // 脚本活动状态的轮询线程
    private Thread                                   scriptCallThread                     = null;

    private Thread                                   gFXMDBThread                         = null;
    //bankCode001XMDB获取业务系统线程(健康巡检)
    private Thread                                   gfHcXMDBThread                         = null;
    
    // 顺德数据清理线程
    private Thread                                   clearDataCaracityThread              = null;

    private Thread                                   smdbResultQueueThread                = null;

    // 一致性比对方案发起灾备组合方案线程
    private Thread                                   compareStartSwitchThread             = null;

    // add by lch 2019-07-26
    private Thread                                   warningTimeOutMailSendThread         = null;
    
    // add by lch 2022-03-08
    private Thread                                   publicTokenTimeoutThead         = null;

    private Thread                                   warningTimeOutSmsSendThread          = null;
    // add by lch 2020-04-01
    private Thread                                   hcAnalyTicleResultQueueThread        = null;
    
    
    private Thread                                   supperCleanResultPartitionThread     = null;

    // 分组执行日常操作任务
    // gang_wang
    private Thread                                   cycleStartDailyOperThread            = null;

    private Thread                                   portalThread                         = null;
    // 保存 业务系统与设备 关系开关
    // gang_wang
    private Thread                                   cycleSaveBusinessCpThread            = null;
    // 一键扫描
    // gang_wang
    private Thread                                   keyScanThread                        = null;
    // gang_wang
    private Thread                                   iCTaskScreenThread                   = null;
    private Thread                                   iRPASocketServerThead                   = null;
    // 年终决算报警线程
    private Thread                                   yearendactaskwarnThread              = null;

    private Thread                                   susExcelTimeOutPollThread            = null;

    // 浦发定时同步平台纳管系统统计数据线程
    private Thread                                   syncButterflyIncludeSysThread        = null;

    // 浦发统计发送自动化变更推广情况邮件
    private Thread                                   sendButterflyNoAomsSysMailThread     = null;

    // 浦发butterfly变更未发起Devops流程邮件
    private Thread                                   sendButterflyNoDevopsMailThread      = null;

    // 浦发butterfly变更投产结束未使用自动化变更通知邮件
    private Thread                                   sendButterflyNoAomsMailThread        = null;

    // 自动关闭告警： 巡检告警红铃铛中告警数据超过24时后自动转移到告警历史表中，自动关闭告警
    private Thread                                   hcAlarmthread                        = null;

    // 数据同步定时线程
    private Thread                                   dataSyncConfigurableThread           = null;

    private Thread                                   sendHttpESBThread                    = null;

    // 定时修改数据库中public密码线程
    private Thread                                   updatePublicPasswordThread           = null;

    // 故障自愈数据转移至历史表线程
    private Thread                                   fasDataTransferThread                = null;
    // 障自愈任务超时计算线程
    private Thread                                   fshTaskTimeOutThread                 = null;
    // 故障自愈读取邮件线程
    private Thread                                   readFshEmailThread                   = null;
    // 二级线程发送线程监控
    private static ConcurrentHashMap<String, String> remoteTime                           = new ConcurrentHashMap<String, String>();
    // 监控二级线程
    private Thread                                   checkRemotActThread                  = null;

    private Thread                                   agentGroupNumSyncThread              = null;

    private Thread                                   saveServerInfoThread                 = null;
    // proxy状态监控线程
    private Thread                                   proxyStateMonitorThread              = null;
    // 浦发Agent有效性线程
    private Thread                                   agentGetEffectThread                 = null;
    // 备份平台获取数据库状态自动轮询线程
    private Thread                                   dbInfoStatusAutoRefreshThread        = null;
    // 定时启动轮询线程
    private Thread                                   timingStartThread                    = null;
    // 大包构建定时启动轮询线程
    private Thread                                   tpkgStartThread                      = null;
    // 应用变更延时等待活动轮询线程
    private Thread                                   execDelayMonitorSUSThread            = null;

    // 浦发应用变更checklist检查流程跳过异常略过、手工步骤线程
    private Thread                                   checklistSkipErrStepThread           = null;
    // CI版本构建poc定时构建线程
    private Thread                                   vmMonitorPocThread                   = null;

    private Thread                                   execDelayCleanThread                 = null;

    private Thread                                   clearDataTwoThread                   = null;

    private Thread                                   agentshellcheckThread                = null;

    private Thread                                   configInfoViewQLThread               = null;

    // 数据采集，黑白名单自动清理采集任务线程
    private Thread                                   dataCollectAutoControlThread         = null;

    private Thread                                   dataCollectAutoTaskStateThread       = null;

    // 数据采集，定时清理采集量统计数据
    private Thread                                   dataCollectAutoCleanThread           = null;

    // 定时任务自动将ieai_timetask_runtime,ieai_timetask_his,ieai_timetask_ip 表数据转移到对应的cach表中
    private Thread                                   ttMovetoCacheThread                  = null;

    private Thread                                   autoClearDataThread                  = null;
    // 用于xy银行用户同步
    private Thread                                   cibSyncUserThread                    = null;

    private Thread                                   cmdbDayThread                    = null;

    private Thread                                   cmdbThread                    = null;

    private Thread                                   itsmThread                    = null;


    //用于兴业银行CMBD同步
    private Thread                                   cibCmdbDataThread                    = null;

    // 中原银行自动合并预启任务线程
    private Thread                                   _execItsmAutoConfirmMergeThread      = null;
    // 应用变更预解析线程
    private Thread                                   _execItsmAutoIadvanceMonitorThread   = null;

    // 远程任务返回并发更新数据库线程
    private Thread                                   remoteMainThread                     = null;
    // cib可视化推送数据线程
    private Thread                                   sendVisualDataThread                 = null;

    private Thread                                   excelmodelScanThread                 = null;

    private Thread                                   zyfdTakeTimeThread                   = null;
    private Thread                                   changeUnvalidRoleThread              = null;
    //工程加载到缓存线程
    private Thread                                   projectToCacheThread                 = null;

    //浦发银行对接开发内控平台 ，proc自动化同步到kfcs线程--生产环境下开放
    private Thread procToKfcsSyncThread=null;
    //浦发银行对接开发内控平台 ，proc自动化同步到kfcs线程--KFCS环境下开放
    private Thread kfcsToICMSSyncThread=null;
    //浦发AZ切换定时任务线程
    private Thread azSwitchTimingThread=null;

    // 山东城商联盟 工作流负载切换线程
    private Thread monitorRollbackWorkFlowThread=null;
    //浦发同步采集表的系统类型到脚本服务化适用平台码表
    private Thread ScriptSyncPlatformCodeThread=null;
    //邮储 清理脚本参数附件表线程
    private Thread scriptDeleteTempAttachmentThread=null;

  //浦发cmdb监控杆线程
    private Thread                                   cmdbMonitorThread                    = null;
   
    //浦发kfcs cmdb定时任务线程
    private Thread                                    SynchronizationCmdbSchedulerThread =null;
    
    //新巡检返回巡检结果到server队列模式线程开关
    private Thread                                    supperHcCheckResultQueueHandlerThread = null;
    
    //topo监控执行开关
    private Thread                                    monitoringExecutionThread = null;
    //topo监控执行开关
    private Thread                                    monitoringKlNjExecutionThread = null;
    //定时任务和脚本服务化双人复核任务超时未审核自动打回线程
    private Thread                                    taskAutoBackThread = null;
    
    //yc itsm 用户同步
    private Thread                                    itsmSycnUserThread = null;
    
    ///**订单迁移到历史线程*/
    private Thread                                    orderMoveToHisThread = null;
    
  ///**订单顺序依赖启动*/
    private Thread                                    orderRelyStartThread = null;
    
    
    //fjnx 用户同步
    private Thread                                    fjSyncUserThread = null;
    //同步模板线程
    private Thread  SynProjectModelToTemplateThread = null;
    //山东cmdb定时同步线程
    private Thread                                    sdCmdbAutoSyncThread= null;
    
    private Thread                                    publicNoticTimingTaskThread = null;
    private Thread                                      distributedTaskExecutor=null;
    
    private Thread                                    deleteHistoryDataCollectSizeNewThread = null;    
    private Thread                                    cqnsCmdbMonitorThread= null;
    private Thread                                    gd_UserSyncThread= null;

    private Thread                                    cqCmdbTxtThread=null;
    
    private Thread                                    supperHcReportThread=null;

    // bankCode001银行关机维护任务状态监控线程
    private Thread                                   shutdownTaskStateMonitorThread       = null;
    // XMDB定时同步线程
    private Thread                                   activeDiscoverySyncThread       = null;
    
    private Thread                                    missionarySystemThread              = null;
    
    private Thread                                    supperCheckResultTimeoutThread      = null;
    private Thread                                    onsConsumerThread = null;
    private Thread                                    sendWarningDGThread = null;
    private Thread                                    theDatabaseTablePartitionThread = null;
    private Thread                                    strategyRestartThread = null;
    private Thread                                    empRestartThread = null;
    private Thread                                    dayStartDelayThread = null;
    private Thread                                    sysChangeThread = null;
    private Thread                                    alarmThread = null;

    private Thread                                    alarmInfoDataThread = null;
	//吉林银行CI任务定时任务线程
    private Thread ciTaskCicleThread  = null;
    private Thread ciTaskTimingThread = null;


    //福建农信定时同步线程
    private Thread seqenceSyncThread = null;
    //福建农信定时发送本周投产系统线程
    private Thread seqenceAutoThread = null;

    //光大IMA线程开关
    private Thread  gd_IAMThread = null;
    //长城金融漏洞修复数据清理 add by 20231007
    private Thread                                   loopholeDataClearThread                    = null;

    // 福建农信银行轮询清理线程
    private Thread cleanConfigThread = null;
    // 福建农信银行定时同步固化环境信息
    private Thread syncEnvironmentThread = null;

    public ThreadPoolExecutor timetaskWarnThreadPool = null;
    
    //外管报送告警线程
    private Thread subAlarmThread = null;
    /**
     * 用于重启自写线程，避免自写线程卡死导致心跳自杀。
     */

    // 渤海数据清理线程（mysql数据库的数据清理）
    private Thread dataClearForMysqlThread = null;
    /**
     * 东莞农商作业调度排队超时告警
     */
    private Thread jobLineUpWarnThread = null;

    /**
     * 补偿线程
     */
    private Thread updateRuninfoIstateThread = null;
    public boolean restartSelfStateTreads ()
    {
        maintainDbSelfStateThread.stopme();
        _log.info("stop _MaintainDbSelfStateThread OK!");
        maintainDbSelfStateThread = new MaintainDbSelfState();
        maintainDbSelfStateThread.start();
        _log.info("Check MaintainDbSelfState Thread restartSelfStateTreads succeeded!");
        return true;
    }

    public ConcurrentHashMap getCollectResultHAMap ()
    {
        return collectResultHAMap;
    }

    /*
     * begin update by liang_guo 2008-06-27 监控数据库连接池是否达到八小时之内没有被使用过，当八小时内没有被使用过则关闭连接池。否则循环监控
     */
    private class CheckDBConnectionPool extends Thread
    {

        public CheckDBConnectionPool()
        {
            super("Check Connection Pool Thread.");
        }

        @Override
        public void run ()
        {
            while (true)
            {
                try
                {
                    // 取得ConnectionPoolManage对象
                    ConnectionPoolManage manage = ConnectionPoolManage.getConnectionPoolManage();
                    // 获得Map
                    Map map = manage.getMap();
                    // 遍历map
                    for (Iterator iter = map.entrySet().iterator(); iter.hasNext();)
                    {
                        Entry entry = (Entry) iter.next();
                        String mapKey = (String) entry.getKey();
                        ConnectionPoolManageBean bean = (ConnectionPoolManageBean) entry.getValue();
                        // 取得线程池被访问的最新时间
                        long poolDate = bean.getPoolDate();
                        // 取得系统当前时间
                        long nowDate = System.currentTimeMillis();
                        // 当时间差为8个小时以上时,证明连接池已经8个小时没有被使用,则关闭连接池
                        if (nowDate - poolDate > 28800000)
                        {
                            // 关闭连接池
                            manage.closeConPool(mapKey);
                        }
                    }
                    // 线程休眠30秒,证明该线程是30秒检测一次线程池的访问时间
                    Thread.sleep(30000);

                } catch (Exception e)
                {
                    _log.error(e);
                }
            }
        }
    }

    /* =============end update by liang_guo===================================== */

    public class CheckStoppingFlows extends Thread
    {
        public CheckStoppingFlows()
        {
            super("Check Stopping Flow Thread.");
        }

        @Override
        public void run ()
        {

            while (true)
           {

                try
                {

                    synchronized (stoppingIEAIFlows)
                    {

                        if (stoppingIEAIFlows.isEmpty())
                        {
                            stoppingIEAIFlows.wait();
                        } else
                        {
                            stoppingIEAIFlows.wait(1000);
                        }

                        // 判断数据自清理开关是否已启动，如已启动，则运行数据自清理
                        Boolean isClearDateSwitch = PersonalityEnv.isClearDataSwitchIeaiDefault();
                        /** 迁移 结束工作流清理工作流相关数据的方法 by yue_sun  on 2018-05-03 start **/
                        for (Iterator itFlows = stoppingIEAIFlows.iterator(); itFlows.hasNext();)
                        {
                            // 要清理的工作流Id
                            String info = (String) itFlows.next();
                            String flowId = info.split(":")[0];
                            String state = info.split(":")[1];

                            // add start by wangnan for 更新作业调度报表基础数据
                            DbOpScheduler opScheduler = new DbOpScheduler();

                            // 要清理的工作流Id
                            WorkflowInfo flowInfo = Engine.getInstance().getFlowInfo(Long.parseLong(flowId),
                                Constants.IEAI_IEAI_BASIC);
                            String projectName = flowInfo.getProjectName();
                            if (projectName != null && !"".equals(projectName))
                            {
                                Project project = com.ideal.ieai.server.repository.project.ProjectManager.getInstance()
                                        .loadProject(projectName);
                                // 判断工程类型是否为作业调度
                                if (project.getProjectType().getProjectType() == Constants.PROJECT_TYPE_JOBSCHEDULING)
                                {
                                    if (Integer.parseInt(state) == Constants.STATE_KILLED)
                                    {
                                        try
                                        {
                                            doKillFlow(Long.parseLong(flowId), Constants.IEAI_IEAI);
                                        } catch (ServerException e1)
                                        {
                                            _log.info("工作流ID：" + flowId + " 执行内存清理异常！", e1);
                                        }

                                        _log.info("开始清理数据，对应的工作流ID为：" + flowId);

                                        // addbyyuxh20180608工作流强行终止清除IEAI_ACTRUNTIME_VALIDPART
                                        // 清理被杀工作流的有效活动表和异常表
                                        RuntimeValidPart runtimePart = new RuntimeValidPart();
                                        runtimePart.setFlowId(Long.parseLong(flowId));
                                        ActRuntimeValThread thread = new ActRuntimeValThread(Constants.PART_CLEARKILL,
                                                runtimePart);
                                        thread.start();
                                    }

                                    try
                                    {
                                        if(DBManager.Mysql_Faimily()){
                                            try {
                                                opScheduler.saveDelFlowId(Long.parseLong(flowId), Constants.IEAI_IEAI);
                                                _log.info("mysql清理模式：工作流ID：" + flowId + " 保存到待清理表成功！！");
                                            } catch (RepositoryException e) {
                                                _log.info("mysql清理模式：工作流ID：" + flowId + " 保存到待清理表失败！！");
                                            }
                                        }else if(isClearDateSwitch){
                                            if (DBManager.Orcl_Faimily())
                                            {
                                                opScheduler.clearData(Long.parseLong(flowId), Constants.IEAI_IEAI);
                                            } else
                                            {
                                                // 存储要删除信息的工作流ID到删除临时表中
                                                opScheduler.saveDelFlowId(Long.parseLong(flowId), Constants.IEAI_IEAI);
                                                // 进入清理数据线程，清理掉工作流相关表的数据信息
                                                clearDataIEAIThread = new ClearDataIEAIThread(Long.parseLong(flowId));
                                                clearDataIEAIThread.start();
                                            }
                                        }


                                        _log.info("工作流ID：" + flowId + " 对应的数据清理完毕");
                                        // clear monitor Act info for kill workflow by txl
                                        // 20160906
                                        // 增加开关控制，当监控接口开关开启时才需要进行actmonitor数据清理
                                        if (SystemConfig.isActmonitorswitch())
                                        {
                                            opScheduler.clearExecactMonitorByFlow(Long.parseLong(flowId));
                                        }
                                    } catch (NumberFormatException e)
                                    {
                                        _log.error("工作流ID转换异常：" + flowId + " info:" + e.getMessage());
                                        _log.info("工作流ID：" + flowId + " 对应的数据清理异常！");
                                    } catch (RepositoryException e)
                                    {
                                        _log.error("调用存储过程异常：" + e.getMessage());
                                        _log.info("工作流ID：" + flowId + " 对应的数据清理异常！");
                                    }
                                }
                            }

                            itFlows.remove();
                        }
                        /** 迁移 结束工作流清理工作流相关数据的方法 by yue_sun  on 2018-05-03 end **/
                    }
                } catch (InterruptedException ex)
                {
                    Thread.currentThread().interrupt();
                } catch (ServerException e)
                {
                    _log.error(e);
                }
            }
        }
    }

    /** 孙悦 迁移工行数据自清理功能 on 2018-05-04 start **/
    /**
     * <li>Description:杀工作流操作</li>
     * 
     * <AUTHOR> Jul 24, 2012
     * @param flowId
     * @throws ServerException return void
     */
    public void doKillFlow ( long flowId, int dbType ) throws ServerException
    {
        WorkflowInstance flowInstance = getFlowInstance(flowId, dbType);

        List acts = null;
        try
        {
            DbOpScheduler dbOpScheduler = new DbOpScheduler();
            acts = dbOpScheduler.getShellCmdReId(flowId, dbType);
            if (!acts.isEmpty())
            {
                _log.info("开始终止工作流" + flowId + " 下的shellcmd活动");
                for (int i = 0; i < acts.size(); i++)
                {
                    String requestId = (String) acts.get(i);
                    try
                    {
                        ActivityRExecHelper.stopShellCmdProcess(requestId, dbType);
                    } catch (Exception e)
                    {
                        _log.info("error when stop shell: " + requestId, e);
                    }
                }
                _log.info("终止工作流" + flowId + " 下的shellcmd活动结束");
            }

        } catch (Exception e)
        {
            _log.info("error when stop shellcmd", e);
        }

        if (flowInstance != null)
        {
            _log.info("info killFlow: start clear memory and flowid=" + flowId);
            branchManager.stopFlow(flowId, flowInstance.getMainScopeId(), flowInstance.isSafeFirst(), true);
            _log.info("info killFlow: clear memory of branchManager and flowid=" + flowId);
        }
        TaskManager.getInstance().flowStop(new Long(flowId));
        _log.info("info killFlow: clear memory of taskManager and flowid=" + flowId);
        actCtxManager.flowStop(flowId);
        _log.info("info killFlow: clear memory successfully and flowid=" + flowId);
    }

    /**
     * <AUTHOR> 20130922
     * @des 根据工作流id获得工程名
     * @param flowId
     * @return
     * @throws ServerException
     */
    public Project getProjectNameByFlowId ( long flowId ) throws ServerException
    {
        return resourceManager.getProjectByFlowId(flowId);
    }

    /** 孙悦 迁移工行数据自清理功能 on 2018-05-04 end **/

    /*
     * begin update by tao_ding 2012-12-14 监控sesstion是否超过半个小时没有操作，清理
     */
    private class CheckSessionInfo extends Thread
    {
        public CheckSessionInfo()
        {
            super("Check CheckSessionInfo Thread.");
        }

        @Override
        public void run ()
        {
            while (true)
            {
                try
                {
                    String sesstion = null;
                    long time = 0;
                    for (Iterator iter = maptime.keySet().iterator(); iter.hasNext();)
                    {
                        sesstion = (String) iter.next();
                        time = (Long) maptime.get(sesstion);
                        if (System.currentTimeMillis() - time > 30 * 60 * 1000)
                        {
                            removeSession(sesstion);
                        }
                    }

                    Thread.sleep(7200000);
                } catch (Exception ex)
                {
                    _log.error(ex.getMessage());
                }
            }
        }
    }

    public void removeSession ( String session )
    {
        if (mapurl.containsKey(session))
        {
            mapurl.remove(session);
            maptime.remove(session);
        }
    }

    public void setSessionInfo ( String session, String url )
    {
        if (mapurl.containsKey(session))
        {
            maptime.put(session, System.currentTimeMillis());
        } else
        {
            mapurl.put(session, url);
            maptime.put(session, System.currentTimeMillis());
        }
    }

    public void setSessionTime ( String session )
    {
        if (mapurl.containsKey(session))
        {
            maptime.remove(session);
            maptime.put(session, System.currentTimeMillis());
        }
    }

    public String[] logoutSesstion ( String session )
    {
        String url[] = new String[3];
        String info = null;
        if (mapurl.containsKey(session))
        {
            info = (String) mapurl.get(session);
            removeSession(session);
            return info.split("##");
        }
        return url;
    }

    public String[] logoutSess ( String session )
    {
        String url[] = new String[3];
        String info = null;
        if (mapurl.containsKey(session))
        {
            info = (String) mapurl.get(session);
            return info.split("##");
        }
        return url;
    }

    /**
     * 转移数据给拓扑图使用
     * <ul>
     * <li>Title: Engine.java</li>
     * <li>Description:</li>
     * <li>Copyright: Copyright 2003</li>
     * <li>Company: ideal</li>
     * </ul>
     * 
     * <AUTHOR>
     * 
     * 2018年1月8日
     */
    private List stoppFlowsToToPo = new ArrayList();

    private class CheckStopFlowsToToPoThread extends Thread
    {

        public CheckStopFlowsToToPoThread()
        {
            super("Check Stopp Flow to ToPo Thread start .");
        }

        @Override
        public void run ()
        {
            while (true)
            {
                try
                {
                    ConfigReader cr = ConfigReader.getInstance();
                    cr.init();
                    boolean flag = cr.getBooleanProperties(ServerEnv.DATA_EXTRACT, false);
                    if (flag)
                    {
                        synchronized (stoppFlowsToToPo)
                        {

                            if (stoppFlowsToToPo.isEmpty())
                            {
                                _log.info("_stoppFlowsToToPo is empty");
                                stoppFlowsToToPo.wait();
                            } else
                            {
                                _log.info("_stoppFlowsToToPo is wait");
                                stoppFlowsToToPo.wait(100);
                            }

                            for (Iterator itFlows = stoppFlowsToToPo.iterator(); itFlows.hasNext();)
                            {
                                String info = (String) itFlows.next();
                                String flowId = info.split(":")[0];
                                try
                                {
                                    DbOpScheduler opScheduler = new DbOpScheduler();
                                    _log.info("开始进行运行活动收集");
                                    opScheduler.dataDump(Long.parseLong(flowId));
                                    _log.info("进行运行活动收集结束");
                                } catch (Exception e)
                                {
                                    _log.error("调用工作流ID转存存储过程异常：" + e);
                                }
                                itFlows.remove();
                            }
                        }
                    }
                } catch (Exception ex)
                {
                    _log.info(ex);
                }
            }
        }
    }

    /**
     * mkj remove jgroup
     * 
     * @throws ServerException
     */
    private Engine() throws ServerException
    {
        resourceManager = new ResourceManager();
        _log.info("ResourceManager initialization succeeded!");

        scheduler = Scheduler.getInstance();

        branchManager = new BranchManager(scheduler);
        _log.info("BranchManager initialization succeeded!");

        recoverManager = new RecoverManager();
        recoverManager.init();
        _log.info("RecoverManager initialization succeeded!");

        recoverManagerCluster = new RecoverManagerCluster();
        _log.info("RecoverManagerCluster initialization succeeded!");

        actCtxManager = new ActivityContextManager();
        _log.info("ActivityContextManager initialization succeeded!");
        
        importWebstudioswitch = new ThreadPoolExecutor(1, 10, 0, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<Runnable>(20), new Engine.ImportWebstudioThreadFactory(), new Engine.ImportWebstudioRejectexecution());
        importWebstudioswitch.prestartCoreThread();
        threadPoolUnlimited = new PooledExecutor();
        threadPoolLimited = new PooledExecutor();
        threadPoolLimited.setMaximumPoolSize(ServerEnv.getServerEnv().getSchedulerThreadNum());
        threadPoolLimited.abortWhenBlocked();
        threadPoolUnlimited.abortWhenBlocked();
        _log.info("ThreadPool initialization succeeded!");

        collectThreadPool = new PooledExecutor();
        collectThreadPool.setMaximumPoolSize(ServerEnv.getServerEnv().getCollectThreadNum());
        // collectThreadPool.abortWhenBlocked();
        collectThreadPool.waitWhenBlocked();
        _log.info("collectThreadPool initialization succeeded!");

//        agentStateThreadPool = new PooledExecutor();
//        agentStateThreadPool.setMaximumPoolSize(ServerEnv.getServerEnv().getAgentStateThreadNum());
        // collectThreadPool.abortWhenBlocked();
//        agentStateThreadPool.waitWhenBlocked();
//        _log.info("agentStateThreadPool initialization succeeded!");

        // 初始化监控连接池线程
        checkDBConnectionPoolThread = new CheckDBConnectionPool();

        checkStoppingFlowsThread = new CheckStoppingFlows();

        asm_isinsertThread = new ASMManagerThread();

        _log.info("Check Stopping Flows Thread initialization succeeded!");

        checkDBConnectionThread = new CheckDBConnection();
        _log.info("Check DBConnection Thread initialization succeeded!");
        // start 2010.4.12 update by zsg

        maintainDbSelfStateThread = new MaintainDbSelfState();
        _log.info("MaintainDbSelfState Thread initialization succeeded!");

        heartbeatMonitorThread = new HeartbeatMonitor();
        _log.info("Check HeartbeatMonitorThread initialization succeeded!");

        rotationMaintainThread = new RotationMaintainThreadState();
        _log.info("Check MaintainThreadState Thread initialization succeeded!");

        hearbeatSelfCheckThread = new HearbeatSelfCheck();
        _log.info("Check HearbeatSelfCheck Thread initialization succeeded!");

        mailSendThread = new SendMailThread();
        _log.info("SendMailThread Thread initialization succeeded!");

        transCheckResultThread = new TransCheckResultThread();
        _log.info("TransCheckResult Thread initialization succeeded!");
        
        
        supperCleanResultPartitionThread = new SupperCleanResultPartitionThread();
        _log.info("supperCleanResultPartitionThread Thread initialization succeeded!");
        

        // end 2010.4.12 update by zsg

        // begin add by geyunsong 2019.03.13 CMDB自动获取数据线成
        cmdbGetDataAutoThread = new CmdbGetDataAutoThread();
        _log.info("CmdbGetDataAutoThread Thread initialization succeeded!");
        // end add by geyunsong 2019.03.13 CMDB自动获取数据线成

        // begin add by geyunosng 2019.12.24 心跳数据进行持续的收集保存
        saveServerInfoThread = new SaveServerInfoThread();
        _log.info("saveServerInfoThread Thread initialization succeeded!");
        // end add by geyunosng 2019.12.24 心跳数据进行持续的收集保存

        // begin update start by yue_sun 2017-12-07 从作业调度主线迁移
        checkWorkFlowCycleThread = new CheckWorkFlowsCycleThread();
        _log.info("Check WorkFlowCycle Thread initialization succeeded!");

        checkWorkFlowICThread = new CheckWorkFlowsICThread();
        _log.info("Check checkWorkFlowIC Thread initialization succeeded!");
        
        nodeWarningMqThread = new NodeWarningMqThread();
        _log.info("Check NodeWarningMqThread Thread initialization succeeded!");
        
        // end update start by yue_sun 2017-12-07 从作业调度主线迁移
        SyncUserGroupThread = new SyncUserGroupThread();
        _log.info("SyncUserGroup Thread initialization succeeded!");

        // begin.added by manxi_zhao.2016-04-08.
        timetaskFailoverThread = new TimetaskFailoverThread();
        _log.info("Check TimetaskFailoverThread initialization succeeded!");

        timetaskImmedStartThread = new TimetaskImmedStartThread();
        _log.info("Check timetaskImmedStartThread initialization succeeded!");

        timetaskAppointmentThread = new TimetaskAppointmentThread();
        _log.info("Check timetaskAppointmentThread initialization succeeded!");

        syncUserDownloadThread = new SyncUserDownloadThread();
        _log.info("Check syncUserDownloadThread initialization succeeded!");

        TimetaskToolsThread = new TimetaskToolsThread();
        _log.info("Check TimetaskToolsThread initialization succeeded!");

//        toolPollThread = new ToolPollThread();
//        _log.info("Check toolPollThread initialization succeeded!");

        toolInfoPollThread = new ToolInfoPollThread();
        _log.info("Check toolInfoPollThread initialization succeeded!");


        timetaskTransferPermissionsThread = new TimetaskTransferPermissionsThread();
        _log.info("Check timetaskTransferPermissionsThread initialization succeeded!");

        calculateChartDataThread = new CalculateChartDataThread();
        _log.info("Check CalculateChartDataThread initialization succeeded!");
        timetaskStopInsThread = new TimetaskStopInsThread();
        _log.info("Check TimetaskStopInsThread initialization succeeded!");
        agentupThread = new AgentUpdateThread();
        _log.info("Check AgentUpdateThread initialization succeeded!");
        timetaskTimeoutThread = new TimetaskTimeoutThread();
        _log.info("Check TimetaskTimeoutThread initialization succeeded!");
        timetaskTaskExceptionThread = new TimeTaskTaskExceptionThread();
        _log.info("Check TimeTaskTaskExceptionThread initialization succeeded!");
        scriptCallThread = new ScriptCallThread(getSNHostName());
        _log.info("Check scriptCallThread initialization succeeded!");
        // end.added by manxi_zhao.2016-04-08.

        // 采集配置拉齐线程
        haInfoAlignThread = new HaInfoAlignThread(
                Environment.getInstance().getSysConfig(Environment.HAINFOALIGN_JOB_SWITCH_CRON, "0 0 5 * * ?"));

        // 配置采集线程 by yue_sun 20181109 start
        collectConfigMonitorThread = new CollectConfigMonitorThread();
        _log.info("Check CollectConfigMonitorThread initialization succeeded!");
        // 配置采集线程 by yue_sun 20181109 end

        // 配置自动删除消息线程
        opmMessageThread = new OpmMessageThread();
        _log.info("Check OpmMessageThread initialization succeeded!");

        asynchronyRuntimeThread = new AsynchronyRuntimeThread(getSNHostName());
        _log.info("Check AsynchronyRuntimeThread initialization succeeded!");

        // 浦发银行任务状态监控线程
        standardTaskStateMonitorThread = new StandardTaskStateMonitorThread();
        _log.info("Check StandardTaskStateMonitorThread initialization succeeded!");

        // 浦发银行启动策略策略监控线程
        executionStrategyThread = new ExecutionStrategyThread();
        _log.info("Check ExecutionStrategyThread initialization succeeded!");

        // 浦发银行启动策略策略监控线程
        distributedThread = new DistributedThread();
        // 上海银行用户同步线程
        ShUserServiceTasktime.showTimer();
        // bankCode001XMDB信息获取线程
        gFXMDBThread = new GFXMDBThread();
        _log.info("Check GFXMDBThread initialization succeeded!");
        
        // bankCode001XMDB信息获取线程(健康巡检)
        gfHcXMDBThread = new XMDBSyncBSToHcThread();
        _log.info("Check XMDBSyncBSToHcThread initialization succeeded!");

        // 监控数据库连接池使用情况
        checkDBPool = new CheckDBPool();

        // add by yan_wang
        checkDBPoolLog = new CheckDBPoolLog();

        // Sesstion管理
        checkSession = new CheckSessionInfo();

        // 延时器监控线程
        execDelayMonitorThread = new ExecDelayMonitorThread();

        execItsmAutoDelayMonitorThread = new ExecItsmAutoDelayMonitorThread();
        execItsmAutoTimeTaskThread = new ExecItsmAutoTimeTaskThread();
        timetaskMoveToHisThread = new TimetaskMoveToHisThread();
        switchTaskAutoStartThread = new SwitchTaskAutoStartThread();

        execSendMessageItsmAutoMonitorThread = new ExecSendMessageItsmAutoMonitorThread();
        // agent监控
        agentCheckPool = new AgentCheckThread();

        actExcelDataHanleThread = new ActExcelDataHanleThread();
        pollAgentChange = new PollAgentChangeThread();
        agentStatePool = new AgentStateThread();

        dbresourceThread = new DBResourceMonitorThread();

        flowTimeOutNodeInfoThreak = new FlowTimeOutNodeInfoThreak();

        // 添加Agent宕机监控线程
        checkAgentThread = new com.ideal.ieai.server.cluster.monitor.AgentCheckThread();
        _log.info("Check AgentCheckThread Thread initialization successded!");

        // 远程发送线程
        sendRomteMainThread = new SendRomteMainThread();
        // agent监控线程
        agentConcurrentMonitorThread = new AgentConcurrentMonitorThread();
        // guogang add topo dumpdata
        checkStopFlowsToToPoThread = new CheckStopFlowsToToPoThread();

        actTimeCalculateThread = new ActTimeCalculateThread();

        topoTriPoolThread = new TopoTriPoolThread();
        topoSendTriPoolThread = TopoSendTriPoolThread.getInstance();

        avgTimeThread = new AvgTimeThread();

        autoSyncTopoInfoThread = new AutoSyncTopoInfoThread();

        averageBaseLineThread = new AverageBaseLineThread();

        sysTimeOffAverageThread = new TimeOffAverageThread();

        clearDataThread = new ClearDataThread();
        dbbackClearDataThread = new DBbackClearDataThread();
        nohupLogDelThread = new NohupLogDelThread();
        // 监控面板数据线程
        setJobNumThread = new SetJobNumThread();

        dayStartWaitThread = new DayStartWaitThread();

        /**监控接口线程**/
        monitorActThread = new Thread(new ExecMonitorThread());

        reportPlanCheckThread = new CustomReportPlanCheckThread();
        _log.info("Check ReportPlanCheckThread initialization succeeded!");

        reportDailyCheckThread = new CustomReportDailyCheckThread();
        _log.info("Check ReportDailyCheckThread initialization succeeded!");

        planCheckThread = new PlanCheckThread();

        dailyCheckThread = new DailyCheckThread();

        avgConsumeTimeThread = new AvgConsumeTimeThread();

        checkWorkflowEndThread = new CheckWorkflowEndThread();

        activityRedoThread = new ActivityRedo_Thread();

        bxExternalStartPollThread = new BXExternalStartPollThread();

        burstThread = new BurstThread();
        burstCleaner = new BurstCleaner();

        checkActRedoDataThread = new CheckActRedoData();
        templateCallThread = new TemplateCallThread();

        syncCmdbDataThread = new SyncCmdbDataThread();

        synchronizationCMDBThread = new SynchronizationCMDBThread();
        synchronizationCMDBToPlatfromThread = new SynchronizationCMDBToPlatfromThread();
        synchronizationCMDBPermissionThread = new SynchronizationCMDBPermissionThread();
        hcStrategySummaryResultThread = new HcStrategySummaryResultThread();

        syncButterflyDataThread = new ButteflySyncThread();

        smdbResultQueueThread = new SmdbResultQueueThread(); 
        sendSUSAPMMailThread = new SendSUSAPMMailThread();

        dbOjectMaintenanceThread = new Thread(new DBOjectMaintenanceThread());
        // 光大数据恢复备份邮件发送
        dbBackDailyMailThread = new Thread(new DBBackDailyMailThread());

        dbbackApplicobjThread = new Thread(new DbbackApplicobjThread());
        // V4.7.16 yue_sun on 2019-03-14 版本 平台管理--文件下发，每日定时发送邮件给指定接收人功能的线程
        fileSendMailRegularlyThread = new FileSendMailRegularlyThread();

        clearDataCaracityThread = new ClearDataCaracityThread();

        warningTimeOutMailSendThread = new SendWaringTimeoutMailThread();
        publicTokenTimeoutThead = new PublicTokenTimeoutThead();
        warningTimeOutSmsSendThread = new WarningTimeOutSmsSendThread();
        hcAnalyTicleResultQueueThread = new HcAnalyTicleResultQueueThread();

        compareStartSwitchThread = new CompareStartSwitchThread();
        
        iRPASocketServerThead = new RPASocketServerThread(ServerEnv.getServerEnv().getRPASocketPort(),PersonalityEnv.getDiskPathConfigValue());

        cycleStartDailyOperThread = new CycleStartDailyOperThread();

        portalThread = new PortalThread();

        cycleSaveBusinessCpThread = new CycleSaveBusinessCpThread();

        keyScanThread = new KeyScanThread();

        iCTaskScreenThread = new ICTaskScreenThread();

        yearendactaskwarnThread = new YearendactaskwarnThread();

        susExcelTimeOutPollThread = new SUSExcelTimeOutPollThread();

        syncButterflyIncludeSysThread = new ButterflySyncIncludeSysThread();

        sendButterflyNoAomsSysMailThread = new ButterflySusConditionMailThread();

        sendButterflyNoDevopsMailThread = new ButterflySusNoDevopsMailThread();

        sendButterflyNoAomsMailThread = new ButterflySusNoAomsMailThread();

        hcAlarmthread = new HcAlarmSwitchThread();

        dataSyncConfigurableThread = new DataSyncConfigurableThread();

        dataCollectAutoControlThread = new DataCollectAutoControlThread();

        dataCollectAutoCleanThread = new CleanDataThread();

        dataCollectAutoTaskStateThread = new DataCollectAutoTaskStateThread();

        sendHttpESBThread = new SendHttpESBThread();

        updatePublicPasswordThread = new UpdatePublicPassword();

        fasDataTransferThread = new FasDataTransferThread();

        fshTaskTimeOutThread = new FshTaskTimeOutThread();
        readFshEmailThread = new ReadFshEmailThread();
        checkRemotActThread = new CheckSendRemote();

        agentGroupNumSyncThread = new AgentGroupNumSyncThread();

        proxyStateMonitorThread = new ProxyStateMonitorThread();

        cmdbDayThread = new CmdbDayThread();
        cmdbThread = new CmdbThread();
        itsmThread = new ItsmThread();

        agentGetEffectThread = new AgentGetEffectThread();

        dbInfoStatusAutoRefreshThread = new DbInfoStatusAutoRefreshThread();

        timingStartThread = new TimingstartThread();

        execDelayMonitorSUSThread = new ExecDelayMonitorSUSThread();

        checklistSkipErrStepThread = new CheckListSkipErrStepThread();

        vmMonitorPocThread = new VmMonitorPocThread();

        execDelayCleanThread = new ExecDelayCleanThread();

        clearDataTwoThread = new ClearDataTwoThread();

        agentshellcheckThread = new AgentshellcheckThread();

        configInfoViewQLThread = new ConfigInfoViewQLThread();

        ttMovetoCacheThread = new TTMovetoCacheThread();

        autoClearDataThread = new AutoClearDataThread();
        cibSyncUserThread = new CibSyncUserThread();
        sendVisualDataThread = new SendVisualDataThread();
        cibCmdbDataThread = new CibCmdbDataThread();
        
        _execItsmAutoConfirmMergeThread = new ExecItsmAutoConfirmMergeThread();
        _execItsmAutoIadvanceMonitorThread = new ExecItsmAutoIadvanceMonitorThread();

        remoteMainThread = new RemoteMainThread();

        excelmodelScanThread = new ExcelModelScanThread();

        zyfdTakeTimeThread = new ZyfdTakeTimeThread();

        changeUnvalidRoleThread = new ChangeUnvalidRoleThread();
        
        procToKfcsSyncThread = new procToKfcsSyncThread();
        kfcsToICMSSyncThread = new kfcsToICMSSyncThread();
        
        azSwitchTimingThread = new AZSwitchTimingTaskThread();
        
        projectToCacheThread = new ProjectToCacheThread();
        
        cmdbMonitorThread  =new CmdbMonitorThread();
        
        SynchronizationCmdbSchedulerThread =new SynchronizationCmdbSchedulerThread();

        /**工作流负载切换线程**/
        monitorRollbackWorkFlowThread = new Thread(new MonitorRollbackWorkFlowThread());

        //浦发 脚本服务化同步采集过来数据的系统类型线程
        ScriptSyncPlatformCodeThread = new Thread(new ScriptPlatformCodeSync());

        scriptDeleteTempAttachmentThread = new Thread(new ScriptDeleteTempAttachmentThread());

        
        supperHcCheckResultQueueHandlerThread = new SupperHcCheckResultQueueHandlerThread();
        
        monitoringExecutionThread = new MonitoringExecutionThread();
        
        monitoringKlNjExecutionThread = new MonitoringKlNjExecutionThread();
        
        deleteHistoryDataCollectSizeNewThread = new DelHistoryDataCollectSizeNewThread();
        
        agentLiveMainThread                 = new AgentLiveMainThread();
        agentLiveThread                       = new AgentLiveThread();
        agentLiveOutTimeThread               = new AgentLiveOutTimeThread();
        
        taskAutoBackThread = new TaskAutoBackThread();

        
        itsmSycnUserThread = new ItsmSycnUserThread();

        fjSyncUserThread = new FJSyncUserThread();
        
        /**订单迁移到历史线程*/
        orderMoveToHisThread = new OrderMoveToHisThread();
        
        /**订单顺序启动线程*/
        orderRelyStartThread = new OrderRelyStartThread();
        
        
        SynProjectModelToTemplateThread  = new SynProjectModelToTemplateThread();

        sdCmdbAutoSyncThread = new CmdbAutoSyncThread();
        
        publicNoticTimingTaskThread = new PublicNoticTimingTaskThread();
        distributedTaskExecutor =new DistributedTaskExecutor();
        
        cqnsCmdbMonitorThread = new CQNS_CmdbMonitorThread();
        gd_UserSyncThread     = new Gd_UserSyncThread();
        

        cqCmdbTxtThread = new CqCmdbTxtThread();
        
        supperHcReportThread = new SupperHcReportThread();

        shutdownTaskStateMonitorThread = new ShutdownTaskStateMonitorThread();

        activeDiscoverySyncThread = new ActiveDiscoverySyncThread();
        
        // add by lch sigle Server start checkresult thread
        supperCheckResultTimeoutThread = new SupperCheckResultTimeoutThread();

        dayStartDelayThread = new DayStartDelayThread();
        /** 渤海Mq 启动线程*/
        onsConsumerThread  = new OnsConsumerThread();
        sendWarningDGThread = new SendWarningDGThread();
        strategyRestartThread  = new StrategyRestartThread();
        empRestartThread  = new EmpRestartThread();
        sysChangeThread  = new SysChangeThread();
        //渤海MY分区 启动线程
        theDatabaseTablePartitionThread = new TheDatabaseTablePartitionThread();
        /**重庆农商变更超时终止*/
        missionarySystemThread = new MissionarySystemThread();
        //吉林银行CI任务定时任务线程
        ciTaskCicleThread  = new CiTaskCicleThread();
        ciTaskTimingThread = new CiTaskTimingThread();

        //福建农信定时同步线程
        seqenceSyncThread = new SeqenceSyncThread();
        seqenceAutoThread = new SeqenceAutoThread();

        cleanConfigThread = new CleanConfigThread();
        syncEnvironmentThread = new SyncEnvironmentThread();

        //外管报送告警线程
        subAlarmThread = new SubAlarmThread();
        //光大报警管理
        alarmThread = new AlarmThread();
        _log.info("Check MissionarySystemThread initialization succeeded!");

        alarmInfoDataThread = new AlarmInfoDataThread();

        //光大IAM线程
        gd_IAMThread =  new Gd_IAMThread();
        //长城金融漏洞修复数据清理
        loopholeDataClearThread = new LoopholeDataClearThread();
        //南京银行更新组织信息
        OrgThread.showTimer();

        //渤海数据清理线程
        dataClearForMysqlThread = new DataClearForMysqlThread();
        jobLineUpWarnThread = new JobLineUpWarnThread();
        if (null != Environment.getInstance().getTimetaskWarningPool()) {
            
            if (Environment.getInstance().getTimetaskWarningPool() > 15) {
                _log.error("定时任务批量终止告警线程配timetask.warn.pool置不能大于15");
                throw new ServerException(ServerError.FAILED_TIMETASK_WARN_POOL_SET);
            }
            
            timetaskWarnThreadPool = new ThreadPoolExecutor(
                    Environment.getInstance().getTimetaskWarningPool(),
                    Environment.getInstance().getTimetaskWarningPool(),
                    60000, TimeUnit.MILLISECONDS, new LinkedBlockingQueue());
            _log.info("TimeTask timetaskWarnThreadPool initialization succeeded!");
        }

        /**压力测试查询状态异常数据进行修改*/
        updateRuninfoIstateThread = new UpdateRuninfoIstateThread();
        _log.info("Check UpdateRuninfoIstateThread initialization succeeded!");
    }

    /**
     * 独立出该部分，方便控制开发，以免混淆其他代码
     */
    private void monitorThreads ()
    {
        maintainDbSelfStateThread = new MaintainDbSelfState();
        _log.info("Check MaintainDbSelfState Thread initialization succeeded!");

        heartbeatMonitorThread = new HeartbeatMonitor();
        _log.info("Check HeartbeatMonitorThread initialization succeeded!");

        rotationMaintainThread = new RotationMaintainThreadState();
        _log.info("Check MaintainThreadState Thread initialization succeeded!");

        hearbeatSelfCheckThread = new HearbeatSelfCheck();
        _log.info("Check HearbeatSelfCheck Thread initialization succeeded!");

    }

    /**
     * 用于页面线程设置中，终止心跳监控线程，重置监控周期
     */
    public boolean stopMonitorTreads ()
    {
        maintainDbSelfStateThread.stopme();
        heartbeatMonitorThread.stopme();
        rotationMaintainThread.stopme();
        hearbeatSelfCheckThread.stopme();

        boolean stopflag = true;
        int i = 0;
        while (stopflag && i < 25)
        {
            i++;
            stopflag = !maintainDbSelfStateThread.isAlive() && !heartbeatMonitorThread.isAlive()
                    && !rotationMaintainThread.isAlive() && !hearbeatSelfCheckThread.isAlive();

            try
            {
                Thread.sleep(5000);
            } catch (InterruptedException e)
            {
                Thread.currentThread().interrupt();
            }
        }
        if (i >= 25)
        {
            _log.error("stop Monitor Fail!");
            return false;
        } else
        {
            clearMonitorThread();
            _log.info("stop Monitor OK!");
            return true;
        }
    }

    private void clearMonitorThread ()
    {
        maintainDbSelfStateThread = null;
        heartbeatMonitorThread = null;
        rotationMaintainThread = null;
        hearbeatSelfCheckThread = null;
    }

    /**
     * The method is used to initialize Engine.
     * 
     */
    public static synchronized void initEngine () throws ServerException
    {
        _log.info("Initializing WorkFlow Engine...");

        QuartzThread.getInstance().start();
        if (Environment.getInstance().getBooleanConfig(Environment.PROJECTS_GROUPRESET,
            Environment.PROJECTS_GROUPRESET_DEFAULT))
        {
            String serverip = "";
            try
            {
                serverip = Environment.getInstance().getServerIP();
            } catch (UnknownHostException e)
            {
                _log.error(e);
                System.exit(0);
            }
            GroupMessageManager.getInstance().updateNullGroupMessage(serverip);
        }

        Engine.inst = new Engine();

        Engine.inst.scheduler.start();

        Engine.checkThreadStart(Engine.inst.checkStoppingFlowsThread, Environment.ISCHECKSTOPPINGFLOWS,
            Environment.TRUE);

        Engine.checkThreadStart(Engine.inst.asm_isinsertThread, Environment.ASM_ISINSERT, Environment.FALSE);

        // 启动连接池监控线程
        Engine.checkThreadStart(Engine.inst.checkDBConnectionPoolThread, Environment.ISCHECKDBCONNECTIONPOOL,
            Environment.FALSE);

        Engine.checkThreadStart(Engine.inst.checkDBConnectionThread, Environment.ISCHECKDBCONNECTION, Environment.TRUE);

        _log.info("WorkFlow Engine initialization succeeded!");

        // add by hao_niu 2013.12.17 标准输出表信息清理线程
        Engine.inst.checkWorkFlowCycleThread.start();
        // 日常操作工作流转移历史
        Engine.inst.checkWorkFlowICThread.start();
        // 清空ieai_detect表
        Engine.inst.deleteIeaiDetect();
        
        // start 2010.4.12 update by zsg

        // 心跳自身数据库维护
        Engine.checkThreadStart(Engine.inst.maintainDbSelfStateThread, Environment.ISMAINTAINDBSELFSTATE,
            Environment.TRUE);
        _log.info("MonitorThread initialization succeeded!");
        // 心跳自身线程维护
        Engine.checkThreadStart(Engine.inst.rotationMaintainThread, Environment.ISROTATIONMAINTAINTHREADSTATE,
            Environment.TRUE);
        _log.info("CheckThreadPool initialization succeeded!");

        Engine.inst.checkWorkflowEndThread.start();
        // 邮件发送线程
        Engine.checkThreadStart(Engine.inst.mailSendThread, Environment.ISMAILSEND, Environment.FALSE);
        _log.info("_MailSendThread initialization succeeded!");

        // 心跳自自我检查线程
        Engine.checkThreadStart(Engine.inst.hearbeatSelfCheckThread, Environment.ISHEARBEATSELFCHECK, Environment.TRUE);
        _log.info("HearbeatSelfCheckThread initialization succeeded!");
        // 心跳守候线程
        Engine.checkThreadStart(Engine.inst.heartbeatMonitorThread, Environment.ISHEARTBEATMONITOR, Environment.TRUE);
        _log.info("maintenanceSelfStatePool initialization succeeded!");

        Engine.checkThreadStart(Engine.inst.transCheckResultThread, Environment.ISTRANSCHECKRESULT, Environment.TRUE);
        _log.info("TransCheckResultThread initialization succeeded!");
        
        //add by lch 2021-01-04 启动删除巡检结果分区线程
        Engine.checkThreadStart(Engine.inst.supperCleanResultPartitionThread, Environment.HC_RESULT_CLEAN_PARTITION_SWITCH, Environment.TRUE);
        _log.info("supperCleanResultPartitionThread initialization succeeded!");
        
        
        // end 2010.4.12 update by zsg
        Engine.checkThreadStart(Engine.inst.SyncUserGroupThread, Environment.ISSYNCUSERGROUPSWITCH, Environment.FALSE);
        _log.info("SyncUserGroupThread initialization succeeded!");
        Engine.checkThreadStart(Engine.inst.templateCallThread, Environment.TEMPLATECALL_STATE_MANAGE_THREAD_SWITCH,
            Environment.FALSE);

        // begin.added by manxi_zhao.2016-04-08.
        String isStartDefault = Environment.FALSE;// 默认不启动定时任务相关线程
        String serverType = "";
        try
        {
            serverType = Environment.getInstance().getServerType();
        } catch (UnknownHostException e)
        {
            _log.info("getServerType error !");
        }
        
        String[] types=serverType.split(",");
        // 判断是否包含定时任务模块
        if (Arrays.asList(types).contains(String.valueOf(Constants.IEAI_TIMINGTASK)))
        {
            isStartDefault = Environment.TRUE;
        }

        // 添加Agent宕机监控启动线程 add by yuyang
        if (ServerEnv.getServerEnv().hasServerType(Constants.IEAI_IEAI, serverType)||ServerEnv.getServerEnv().hasServerType(Constants.IEAI_DATA_COLLECT, serverType))
        {
            Engine.checkThreadStart(Engine.inst.checkAgentThread, PersonalityEnv.AGENTCHECK_SWITCH,
                String.valueOf(PersonalityEnv.isAgentCheckSwitchDefault()));
        }

        // 根据系统配置初始化线程
        Engine.checkThreadStart(Engine.inst.timetaskFailoverThread, Environment.ISTIMETASKFAILOVER, isStartDefault);
        _log.info("TimetaskFailoverThread initialization succeeded!");
        Engine.checkThreadStart(Engine.inst.calculateChartDataThread, Environment.ISCALCULATECHARTDATA,
            Environment.FALSE);
        _log.info("CalculateChartDataThread initialization succeeded!");
        Engine.checkThreadStart(Engine.inst.timetaskStopInsThread, Environment.ISTIMETASKSTOPINS, isStartDefault);
        _log.info("TimetaskStopInsThread initialization succeeded!");
        Engine.checkThreadStart(Engine.inst.agentupThread, Environment.ISAGENTUPDATE, "true");
        _log.info("AgentUpdateThread initialization succeeded!");
        Engine.checkThreadStart(Engine.inst.timetaskTimeoutThread, Environment.ISTIMETASKTIMEOUT, isStartDefault);
        _log.info("TimetaskTimeoutThread initialization succeeded!");
        // end.added by manxi_zhao.2016-04-08.

        Engine.checkThreadStart(Engine.inst.timetaskTaskExceptionThread, Environment.ISTIMETASKTASKEXCEPTION, "false");
        // 定时任务预启轮询线程
        Engine.checkThreadStart(Engine.inst.execItsmAutoTimeTaskThread, Environment.ISEXPECTEDTASK, isStartDefault);
        // 定时任务迁移历史的轮询线程
        Engine.checkThreadStart(Engine.inst.timetaskMoveToHisThread, Environment.ISTIMETASKMOVEHIS, isStartDefault);
        //同步用户线程
        Engine.checkThreadStart(Engine.inst.syncUserDownloadThread, Environment.ZY_SYNCUSER_DOWNLOAD_SWITCH,
                Environment.FALSE);

        // 定时
        // 任务立即启动轮询线程
        Engine.checkThreadStart(Engine.inst.timetaskImmedStartThread, Environment.ISTIMETASKIMMEDSTART, isStartDefault);

        Engine.checkThreadStart(Engine.inst.timetaskAppointmentThread, Environment.ISTIMETASKAPPOINTMENT,
            isStartDefault);
        Engine.checkThreadStart(Engine.inst.TimetaskToolsThread, Environment.TIMETASKTOOLSTHREAD,
                Environment.FALSE);
        //工具箱执行轮询线程
//        Engine.checkThreadStart(Engine.inst.toolPollThread, Environment.TOOLPOLLTHREAD,
//                Environment.FALSE);

        Engine.checkThreadStart(Engine.inst.toolInfoPollThread, Environment.TOOLINFOPOLLTHREAD,
                Environment.FALSE);

        Engine.checkThreadStart(Engine.inst.timetaskTransferPermissionsThread, Environment.NJ_AUTHORITY_TRANSFER_SWITCH,
                Environment.FALSE);
        // 灾备切换预启轮询线程
        Engine.checkThreadStart(Engine.inst.switchTaskAutoStartThread, Environment.ISSWITCHTASKAUTOSTART,
            Environment.FALSE);

        // cmdb自动发现线程
        Engine.checkThreadStart(Engine.inst.cmdbGetDataAutoThread, Environment.ISCMDBGETDATAAUTOSTART,
            Environment.FALSE);

        // 心跳数据进行持续收集每分钟收集一次,跟随心跳开关设置,若不配置则关闭
        Engine.checkThreadStart(Engine.inst.saveServerInfoThread, Environment.ISMAINTAINDBSELFSTATE, Environment.FALSE);

        // 监控数据库连接池情况
        Engine.checkThreadStart(Engine.inst.checkDBPool, Environment.ISCHECKDBPOOL, Environment.FALSE);

        // add by yan_wang
        Engine.checkThreadStart(Engine.inst.checkDBPoolLog, Environment.ISCHECKDBPOOLLOG, Environment.TRUE);
        // Session管理启动
        Engine.checkThreadStart(Engine.inst.checkSession, Environment.ISCHECKSESSIONINFO, Environment.FALSE);

        // agent监控启动
        Engine.checkThreadStart(Engine.inst.agentCheckPool, Environment.ISAGENTCHECK, Environment.FALSE);

        Engine.checkThreadStart(Engine.inst.execDelayMonitorThread, Environment.ISEXECDELAYMONITOR, Environment.FALSE);
        // 一致性比对发起灾备切换
        Engine.checkThreadStart(Engine.inst.compareStartSwitchThread, Environment.ISCOMPARESTARTSWITCH,
            Environment.FALSE);
        // 应用变更延时等待活动轮询线程
        Engine.checkThreadStart(Engine.inst.execDelayMonitorSUSThread, Environment.SUS_ISEXECDELAYMONITOR,
            Environment.FALSE);

        // 浦发应用变更checklist检查跳过人工、异常步骤线程开关轮询线程
        Engine.checkThreadStart(Engine.inst.checklistSkipErrStepThread, Environment.PF_SUS_SKIP_ERR_STEP_SWITCH,
            Environment.FALSE);

        Engine.checkThreadStart(Engine.inst.keyScanThread, Environment.KEY_SCAN_REPAIR_SWITCH, Environment.FALSE);

        // 运维rpa 监控
        Engine.checkThreadStart(Engine.inst.iCTaskScreenThread, Environment.STANDARD_OPERATION_BIG_SCREEN_SWITCH,
            Environment.FALSE);
        //运维rpa socket服务线程
        Engine.checkThreadStart(Engine.inst.iRPASocketServerThead, Environment.RPA_SOCKET_SERVICE_SWITCH, Environment.FALSE);
        
        // portal 头部数据线程获取
        Engine.checkThreadStart(Engine.inst.portalThread, Environment.STANDARD_OPERATION_BIG_SCREEN_SWITCH,
            Environment.TRUE);

        // CI版本构建poc定时构建线程
        Engine.checkThreadStart(Engine.inst.vmMonitorPocThread, Environment.VMMONITORPOCTHREAD_SWITCH,
            Environment.FALSE);

        // 失效备援数据清理
        Engine.checkThreadStart(Engine.inst.execDelayCleanThread, Environment.CLEAR_EXECACT_THREAD_SWITCH,
            Environment.FALSE);

        Engine.checkThreadStart(Engine.inst.clearDataTwoThread, Environment.CLEAR_DATA_TWO_SWITCH, Environment.FALSE);

        // Agent30天检查下线功能
        // Engine.checkThreadStart(Engine.inst.agentshellcheckThread,
        // Environment.AGENT_CHECK_SHELLD, Environment.FALSE);
        // 分组执行日常操作任务
        // gang_wang
        if (ServerEnv.getInstance().getCYCLEStartDailyOperSwitch())
        {
            String serverip = "";
            try
            {
                serverip = Environment.getInstance().getServerIP();
            } catch (UnknownHostException e)
            {
                _log.error(e);
                System.exit(0);
            }
            // 清理IEAI_STANDARD_TASK_MIDDLE
            String sql = "DELETE FROM IEAI_STANDARD_TASK_MIDDLE WHERE SERVERIP = ?";
            Object[] delData = { serverip };
            DBUtilsNew utils = new DBUtilsNew(Constants.IEAI_DAILY_OPERATIONS);
            utils.delete(sql, delData);
            Engine.checkThreadStart(Engine.inst.cycleStartDailyOperThread, Environment.CYCLE_START_DAILY_OPER_SWITCH,
                Environment.FALSE);
        }

        if (ServerEnv.getInstance().getCycleSaveBusinessCpRelationSwitch())
        {
            Engine.checkThreadStart(Engine.inst.cycleSaveBusinessCpThread, Environment.CYCLE_SAVE_BUSINESS_CP_SWITCH,
                Environment.FALSE);
        }

        /**Engine._inst._monitorActThread.start()**/
        boolean actMonitorSwitch = Environment.getInstance().getBooleanConfig(Environment.ACT_MONITOR_SWITCH,
            Environment.ACT_MONITOR_SWITCH_DEFAULT);
        if (actMonitorSwitch)
        {
            Engine.checkThreadStart(Engine.inst.monitorActThread, Environment.ISEXECMONITOR, Environment.TRUE);
        }

        String type = "";
        String[] str = null;
        try
        {
            type = Environment.getInstance().getServerType();
        } catch (UnknownHostException e1)
        {
            _log.info("Environment.getInstance().getServerType() is error!");
        }
        if (type != "" || type != null)
        {
            str = type.split(",");
        }
        if (str != null && str.length > 0)
        {
            for (int i = 0; i < str.length; i++)
            {
                if (str[i].equals(String.valueOf(Constants.IEAI_SUS)))
                {
                    Engine.inst.execItsmAutoDelayMonitorThread.start();
                    Engine.checkThreadStart(Engine.inst.execSendMessageItsmAutoMonitorThread,
                        Environment.ISEXECSENDMESSAGEITSMAUTOMONITOR, Environment.TRUE);
                    Engine.checkThreadStart(Engine.inst.flowTimeOutNodeInfoThreak, Environment.ISFLOWTIMEOUTNODEINFO,
                        Environment.FALSE);
                }
            }
        }

        if (ServerEnv.getInstance().getJobSchedulingQuerySystemSwitch())
        {
            Engine.checkThreadStart(Engine.inst.actExcelDataHanleThread, Environment.ISACTEXCELDATAHANLE,
                Environment.FALSE);

        } else
        {
            Engine.checkThreadStart(Engine.inst.actExcelDataHanleThread, Environment.ISACTEXCELDATAHANLE,
                Environment.TRUE);

        }

        Engine.checkThreadStart(Engine.inst.dbresourceThread, Environment.ISDBRESOURCEMONITOR, Environment.TRUE);

        Engine.checkThreadStart(Engine.inst.agentStatePool, Environment.ISAGENTSTATEPOOL, Environment.FALSE);

        Engine.checkThreadStart(Engine.inst.reportPlanCheckThread, Environment.ISREPORTDAILCHECK, Environment.FALSE);

        Engine.checkThreadStart(Engine.inst.reportDailyCheckThread, Environment.ISREPORTDAILYSENDCHECK,
            Environment.FALSE);
        Engine.checkThreadStart(Engine.inst.checkStopFlowsToToPoThread, Environment.TOPO_DATADUMP_SWITCH,
            Environment.FALSE);
        Engine.checkThreadStart(Engine.inst.actTimeCalculateThread, Environment.TOPO_ACT_CALC, Environment.FALSE);
        Engine.checkThreadStart(Engine.inst.topoTriPoolThread, Environment.TOPO_TRIGGER_CHECK_SWITCH, Environment.FALSE);
        Engine.checkThreadStart(Engine.inst.topoSendTriPoolThread, Environment.TOPO_TRIGGER_SEND_SWITCH, Environment.FALSE);
        Engine.checkThreadStart(Engine.inst.avgTimeThread, Environment.AVG_SWITCH, Environment.FALSE);
        Engine.checkThreadStart(Engine.inst.clearDataThread, Environment.CLEAR_ACT_SWITCH, Environment.FALSE);
        Engine.checkThreadStart(Engine.inst.dbbackClearDataThread, Environment.JOBSCHEDULING_DBBACK_DATACLEAR_SWITCH,
            Environment.FALSE);
        Engine.checkThreadStart(Engine.inst.nohupLogDelThread, Environment.JOBSCHEDULING_DBBACK_DELNOHUP_SWITCH,
            Environment.FALSE);
        Engine.checkThreadStart(Engine.inst.autoSyncTopoInfoThread, Environment.TOPO_AUTO_SYNC_TOPOINFO,
            Environment.FALSE);
        if (ServerEnv.getInstance().getJobSchedulingQuerySystemSwitch())
        {
            Engine.checkThreadStart(Engine.inst.averageBaseLineThread, Environment.TOPO_SYS_ACT_AVG_BASELINE_SWITCH,
                Environment.FALSE);
        } else
        {
            Engine.checkThreadStart(Engine.inst.averageBaseLineThread, Environment.TOPO_SYS_ACT_AVG_BASELINE_SWITCH,
                Environment.TRUE);
        }

        Engine.checkThreadStart(Engine.inst.sysTimeOffAverageThread, Environment.TOPO_SYS_ACT_AVGTIME_SWITCH,
            Environment.FALSE);
        // 添加到时未发起到时未结束等报警计算开关和规则改变开关和运行耗时计算开关。
        // 启动报警阀值计算线程。
        new NodeWarnFlowAct().threadStart();
        // 启动报警阀值规则计算线程。
        new NodeWarnRuleCh().threadStart();
        // 启动运行耗时计算线程
        new TakeTimeWarningThread().start();
        _log.info("TakeTimeWarningThread start successful!");

        // 启动活动数计算线程
        boolean jobNumSwitch = Environment.getInstance().getBooleanConfig(Environment.JOB_NUM_SWITCH,
            Environment.JOB_NUM_SWITCH_DEFAULT);
        _log.info("SetJobNumThread switch is " + jobNumSwitch);
        if (jobNumSwitch)
        {
            new Thread(Engine.inst.setJobNumThread).start();
            _log.info("SetJobNumThread start successfully!");
        }

        // 启动日启动等待拉起线程
        boolean datWaitStartSwitch = Environment.getInstance().getBooleanConfig(Environment.DAY_WAIT_START_SWITCH,
                Environment.DAY_WAIT_START_SWITCH_DEFAULT);
        _log.info("dayStartWaitThread switch is " + datWaitStartSwitch);
        if (datWaitStartSwitch)
        {
            new Thread(Engine.inst.dayStartWaitThread).start();
            _log.info("dayStartWaitThread start successfully!");
        }

        boolean isplancheckSwitch = PersonalityEnv.isPlanChechSwitchDefault();
        // 日报邮件发送线程
        if (isplancheckSwitch)
        {
            Engine.inst.planCheckThread.start();
            Engine.inst.dailyCheckThread.start();
            _log.info("DailyCheckThread start successfully!");
        }
        boolean isAvgConsumeTime = PersonalityEnv.isAvgTimeFailNumDefault();
        boolean failNumStatic = PersonalityEnv.isAvgActrunFailnumstaticValue();
        // 平均耗时和出错数统计
        if (isAvgConsumeTime || failNumStatic)
        {
            Engine.inst.avgConsumeTimeThread.start();
        }
        // add by tiejun_fan 2014.10.31 业务异常自动重试线程
        Engine.checkThreadStart(Engine.inst.activityRedoThread, PersonalityEnv.ACTREDO_THREAD_SWITCH,
            Environment.FALSE);

        if (PersonalityEnv.isKEYFLOWCHECK_SWITCH_VALUE())
        {
            Thread keyFlowThread = new Thread(new CheckKeyFlowThread());
            keyFlowThread.start();
            _log.info("CheckKeyFlowThread start successfully!");
        }
        if (ServerEnv.getInstance().getJobSchedulingQuerySystemSwitch())
        {
            Engine.checkThreadStart(Engine.inst.bxExternalStartPollThread, Environment.SUS_EXTERNAL_DELAY_START,
                Environment.FALSE);
        } else
        {
            Engine.checkThreadStart(Engine.inst.bxExternalStartPollThread, Environment.SUS_EXTERNAL_DELAY_START,
                Environment.TRUE);
        }



        Engine.checkThreadStart(Engine.inst.burstThread, Environment.BURST_SWITCH, Environment.FALSE);
        Engine.checkThreadStart(Engine.inst.burstCleaner, Environment.BURST_SWITCH, Environment.FALSE);
        // 配置采集线程是否开启
        Engine.checkThreadStart(Engine.inst.collectConfigMonitorThread, Environment.COLLENCT_CONFIG_SWITCH,
            Environment.FALSE);
        Engine.checkThreadStart(Engine.inst.haInfoAlignThread, Environment.HAINFOALIGN_JOB_SWITCH, Environment.FALSE);

        // 配置删除消息线程是否开启
        Engine.checkThreadStart(Engine.inst.opmMessageThread, Environment.OPENDELETEMESSAGE_SWITCH, Environment.FALSE);

        // 异步状态线程开关
        Engine.checkThreadStart(Engine.inst.asynchronyRuntimeThread, Environment.ASYNCHRONY_RUNTIME_SWITCH,
            Environment.FALSE);

        // 浦发银行任务状态监控线程是否开启
        Engine.checkThreadStart(Engine.inst.standardTaskStateMonitorThread,
            PersonalityEnv.DAILY_MONITOR_TASK_STATE_SPDB_SWITCH, Environment.FALSE);

        // 浦发银行任务状态监控线程是否开启
        Engine.checkThreadStart(Engine.inst.executionStrategyThread,
            PersonalityEnv.DAILY_EXECUTION_STRATEGY_SPDB_SWITCH, Environment.FALSE);
        
        Engine.checkThreadStart(Engine.inst.nodeWarningMqThread,
            PersonalityEnv.BH_MQ_WARN_MONITOR_SWITCH, Environment.FALSE);

        // 浦发银行 分布任务处理线程
        Engine.checkThreadStart(Engine.inst.distributedThread, PersonalityEnv.DISTRIBUTED_THREAD_MONITOR_SWITCH,
            Environment.FALSE);

        // 浦发银行agent 定时同步
        Engine.checkThreadStart(Engine.inst.syncCmdbDataThread, PersonalityEnv.PF_CMDB_MYSQL_SYNC_START_SWITCH,
            Environment.FALSE);

        // 浦发银行 定时同步butterfly数据
        Engine.checkThreadStart(Engine.inst.syncButterflyDataThread, PersonalityEnv.PF_SYNC_BUTTERFLY_SWITCH,
            Environment.FALSE);

        // 浦发银行 定时发送应用维护/变更邮件
        Engine.checkThreadStart(Engine.inst.sendSUSAPMMailThread, PersonalityEnv.PF_SUS_APM_MAIL_SWITCH,
            Environment.FALSE);

        //邮储银行 CMDB定时同步数据到中间库(测试环境)
        Engine.checkThreadStart(Engine.inst.synchronizationCMDBThread, PersonalityEnv.YC_CMDB_SYNC_MID_START_SWITCH,
                Environment.FALSE);
        //邮储银行 CMDB定时同步数据到中间库(测试环境)
        Engine.checkThreadStart(Engine.inst.synchronizationCMDBToPlatfromThread, PersonalityEnv.YC_CMDB_SYNC_USE_START_SWITCH,
                Environment.FALSE);
        //邮储银行 CMDB定时同步鉴权(用户名密码)数据(测试环境)
        Engine.checkThreadStart(Engine.inst.synchronizationCMDBPermissionThread, PersonalityEnv.YC_CMDB_SYNC_PERMISSION_START_SWITCH,
                Environment.FALSE);
        //邮储策略巡检每日巡检结果汇总任务开启开关
        Engine.checkThreadStart(Engine.inst.hcStrategySummaryResultThread, PersonalityEnv.HC_STRATEGY_SUMMARY_RESULT_SWITCH,
                Environment.FALSE);

        // 锦州银行自动重试
        Engine.checkThreadStart(Engine.inst.checkActRedoDataThread, PersonalityEnv.REDO_START_SWITCH,
            Environment.FALSE);

        Engine.checkThreadStart(Engine.inst.dbOjectMaintenanceThread, Environment.JOBSCHEDULING_SWITCH_DBBACK_DBSCAN,
            Environment.FALSE);

        Engine.checkThreadStart(Engine.inst.dbBackDailyMailThread, Environment.JOBSCHEDULING_DBBACK_SENDMAIL_SWITCH,
            Environment.FALSE);

        Engine.checkThreadStart(Engine.inst.dbbackApplicobjThread, Environment.JOBSCHEDULING_DBBACK_APPLIOBJECT_SWITCH,
            Environment.FALSE);



        // V4.7.16 yue_sun on 2019-03-14 版本 平台管理--文件下发，每日定时发送邮件给指定接收人功能的线程
        Engine.checkThreadStart(Engine.inst.fileSendMailRegularlyThread, PersonalityEnv.DAILY_FILE_EVERYDAY_MAIL_SWITCH,
            Environment.FALSE);

        // bankCode001XMDB信息获取线程开关
        Engine.checkThreadStart(Engine.inst.gFXMDBThread, Environment.GF_XMDB_MESSAGE_SWITCH, Environment.FALSE);
        // bankCode001XMDB信息获取同步健康巡检业务系统线程开关
        Engine.checkThreadStart(Engine.inst.gfHcXMDBThread, Environment.GF_HC_XMDB_MESSAGE_SWITCH, Environment.FALSE);

        // 浦发银行 平台自动化纳管系统统计定时推送数据
        Engine.checkThreadStart(Engine.inst.syncButterflyIncludeSysThread, PersonalityEnv.PF_SYNC_BF_DATA_TIMING_SWITCH,
            Environment.FALSE);

        // 浦发银行 浦发统计发送自动化变更推广情况邮件
        Engine.checkSendBfMailThreadStart(Engine.inst.sendButterflyNoAomsSysMailThread, "sendBfCondition.properties",
            "pf.sus.condition.mail.switch");

        // 浦发银行 butterfly变更未发起Devops流程邮件
        Engine.checkSendBfMailThreadStart(Engine.inst.sendButterflyNoDevopsMailThread, "sendBfNoDevops.properties",
            "pf.sus.no.devops.mail.switch");

        // 浦发银行 butterfly投产结束未使用自动化变更通知邮件
        Engine.checkSendBfMailThreadStart(Engine.inst.sendButterflyNoAomsMailThread, "sendBfNoAoms.properties",
            "pf.sus.no.aoms.mail.switch");

        // 启动脚本轮询线程
        boolean scriptCallThreadswitch = Environment.getInstance().getBooleanConfig(Environment.SCRIPT_CALL_LOOP_SWITCH,
            Environment.SCRIPT_CALL_DEFAULT_LOOP_SWITCH);
        if (scriptCallThreadswitch)
        {
            Engine.checkThreadStart(Engine.inst.scriptCallThread, Environment.SCRIPT_CALL_LOOP_SWITCH,
                Environment.TRUE);
            _log.info("scriptCallThread start successful!");
        }
        // 顺德数据清理线程
        Engine.checkThreadStart(Engine.inst.clearDataCaracityThread, Environment.SHUNDE_CAPACITY_DATACLEAN_SWITCH,
            Environment.FALSE);

        if (PersonalityEnv.isInfoCollectionPuthreadSwitchValue())
        {
            /**   
             * @Fields 信息采集,计算未向一体化运维平台返回信息时,计算出符合条件的记录,向 一体化运维平台补发消息的线程 
             */
            InfoCollectionPuThread infoCollectionResCallThread = new InfoCollectionPuThread();
            Thread keyFlowThread = new Thread(infoCollectionResCallThread, "补充返回一体化运维平台消息的线程");
            keyFlowThread.start();
            _log.info("InfoCollectionPuThread start successfully!");
        }

        // V4.7.25 配置CMDB获取采集信息线程开启开关
        if (PersonalityEnv.isCMDBEntityModelValue())
        {
            EntityModelThread entityModel = new EntityModelThread();
            Thread entityModelThread = new Thread(entityModel, "CMDB获取采集信息线程");
            entityModelThread.start();
            _log.info("entityModelThread start successfully!");
        }

        Engine.checkThreadStart(Engine.inst.smdbResultQueueThread,
            Environment.SMDB_RESULT_CACHE_QUEUE_SWITCH, Environment.FALSE);
        
        // 为是否根据采集结果更新纳管时间的开关赋值
        boolean updateICreatetimeSwitch = Environment.getInstance()
                .getBooleanConfig(Environment.COLLECT_UPDATE_COMPUTER_ICREATE, Boolean.FALSE);
        if (updateICreatetimeSwitch)
        {
            SystemConfig.setUpdateICreatetimeSwitch(updateICreatetimeSwitch);
            _log.info("updateICreatetimeSwitch set successful! the switch value :" + updateICreatetimeSwitch);
        }

        // add by lch 是否开启告警邮件超时发送线程开关
        Engine.checkThreadStart(Engine.inst.warningTimeOutMailSendThread,
            Environment.SEND_HC_WARING_TIMEOUT_EMAIL_SWITCH, Environment.FALSE);
        
        // add by lch 是否开启统一token功能
        Engine.checkThreadStart(Engine.inst.publicTokenTimeoutThead,
            Environment.UNITY_TOKEN_SGS_SWITCH, Environment.FALSE);

        // add by lch 是否开启超时告警未处理短信提醒线程开关
        Engine.checkThreadStart(Engine.inst.warningTimeOutSmsSendThread, Environment.SEND_HC_WARING_TIMEOUT_SMS_SWITCH,
            Environment.FALSE);

        // add by lch 是否开启巡检结果缓存队列方式处理线程开关
        Engine.checkThreadStart(Engine.inst.hcAnalyTicleResultQueueThread,
            Environment.HC_CHECKRESULT_CACHE_QUEUE_SWITCH, Environment.FALSE);

        // 浦发银行public用户密码自动更新
        Engine.checkThreadStart(Engine.inst.updatePublicPasswordThread, PersonalityEnv.PF_COMMON_UPDATE_PP_SWITCH,
            Environment.FALSE);

        //长城金融漏洞修复数据清理
        Engine.checkThreadStart(Engine.inst.loopholeDataClearThread, Environment.CCJR_LOOPHOLEDATA_CLEAR_SWITCH, Environment.FALSE);
        /**
         * 启动server 将entegor.config 信息放入缓存中
         * @author: wg 
         */
       /** InitOnlineConifg online = new InitOnlineConifg();
        online.initConfigList();
        
        //启动查询库中配置，更改缓存
        online.reloadConfigInit();*/

        //渤海数据清理线程（mysql数据库的清理）
        Engine.checkThreadStart(Engine.inst.dataClearForMysqlThread,Environment.IEAI_CLEAR_DATA_SWITCH, Environment.FALSE);

        Engine.checkThreadStart(Engine.inst.jobLineUpWarnThread,Environment.JOB_LINEUP_WARN_SWITCH, Environment.FALSE);
        // 非核心功能server启动如下线程
        if (ServerEnv.getInstance().getCoreFlag())
        {
            // 计算时序状态线程
            new Thread(new SequentialThread()).start();
            // 同步时序工单线程
            new Thread(new SequentialSyncThread()).start();
        }

        // 年终决算报警线程
        Engine.checkThreadStart(Engine.inst.yearendactaskwarnThread, Environment.COMM_YAERENDACTASK_WARNTHREAD_SWITCH,
            Environment.FALSE);

        // 应用变更超时发送邮件开关
        Engine.checkThreadStart(Engine.inst.susExcelTimeOutPollThread, Environment.SUS_EXCELTIMEOUT_SENDEMAIL_SWICH,
            Environment.FALSE);

        // 自动关闭告警： 巡检告警红铃铛中告警数据超过24时后自动转移到告警历史表中，自动关闭告警
        Engine.checkThreadStart(Engine.inst.hcAlarmthread, Environment.HC_ALARM_SWITCH_THREAD_SWITCH,
            Environment.FALSE);

        Engine.checkThreadStart(Engine.inst.dataSyncConfigurableThread, Environment.DATA_SYNC_CONFIG_SWITCH,
            Environment.FALSE);



        // 数据采集-数据清理线程
        Engine.checkThreadStart(Engine.inst.dataCollectAutoCleanThread, Environment.DATACOLLECT_AUTOCLEANDATA,
            Environment.FALSE);
        Engine.checkThreadStart(Engine.inst.dataCollectAutoTaskStateThread, Environment.DATACOLLECT_AUTOSTATE,
            Environment.FALSE);
        Engine.checkThreadStart(Engine.inst.sendHttpESBThread, Environment.LLNX_ESBQUERY_SWITCH, Environment.FALSE);

        // 故障自愈数据转移至历史表线程
        Engine.checkThreadStart(Engine.inst.fasDataTransferThread, Environment.FAS_DATATRANSFER_SWITCH,
            Environment.FALSE);
        Engine.checkThreadStart(Engine.inst.fshTaskTimeOutThread, Environment.FSH_TIMEOUT_THREAD_SWITCH,
            Environment.FALSE);
        Engine.checkThreadStart(Engine.inst.readFshEmailThread, Environment.FSH_READEMAIL_THREAD_SWITCH,
            Environment.FALSE);
        Engine.inst.checkRemotActThread.start();

        Engine.checkThreadStart(Engine.inst.agentGroupNumSyncThread, Environment.AGENTGROUP_NUMSYNC_SWITCH,
                Environment.FALSE);

        // ProxyStateMonitorThread
        Engine.checkThreadStart(Engine.inst.proxyStateMonitorThread, Environment.PROXY_THREAD_STATEMONITOR_SWITCH,
            Environment.FALSE);
        _log.info("ProxyStateMonitorThread initialization succeeded!");

        Engine.checkThreadStart(Engine.inst.agentGetEffectThread, Environment.AGENT_GET_EFFECT_SWITCH,
            Environment.FALSE);

        Engine.checkThreadStart(Engine.inst.dbInfoStatusAutoRefreshThread,
            Environment.JOBSCHEDULING_DBBACK_THREAD_DBSTATUSAUTOREFRESH_SWITCH, Environment.FALSE);

        Engine.checkThreadStart(Engine.inst.configInfoViewQLThread, Environment.QLWARNTIME_UPDATE_SWITCH,
            Environment.FALSE);

        Engine.checkThreadStart(Engine.inst.timingStartThread, Environment.TIMING_START_THREAD, Environment.FALSE);

        Engine.checkThreadStart(Engine.inst.ttMovetoCacheThread, Environment.TT_MOVETOCACHE_THREAD_SWITCH,
            Environment.FALSE);

        Engine.checkThreadStart(Engine.inst.autoClearDataThread, Environment.SD_AUTO_CLEARDATASWITCH,
            Environment.FALSE);
        Engine.checkThreadStart(Engine.inst.cibSyncUserThread, Environment.CIB_EDIP_SWITCH, Environment.FALSE);
        Engine.checkThreadStart(Engine.inst._execItsmAutoConfirmMergeThread, Environment.SUS_ITSM_AUTOCONFIRMMERGE,
            Environment.FALSE);
        Engine.checkThreadStart(Engine.inst._execItsmAutoIadvanceMonitorThread, Environment.SUS_IADVANCE_SWITCH,
            Environment.FALSE);

        Engine.checkThreadStart(Engine.inst.cmdbDayThread, Environment.CMDBDAY_THREAD_SWITCH,
                Environment.FALSE);

        Engine.checkThreadStart(Engine.inst.cmdbThread, Environment.CMDB_THREAD_SWITCH,
                Environment.FALSE);

        Engine.checkThreadStart(Engine.inst.itsmThread, Environment.ITSM_THREAD_SWITCH,
                Environment.FALSE);

        Engine.checkThreadStart(Engine.inst.remoteMainThread, Environment.REMOTE_THREAD_SWITCH, Environment.FALSE);
        Engine.checkThreadStart(Engine.inst.sendVisualDataThread, Environment.CIB_AIM_SWITCH, Environment.FALSE);
        Engine.checkThreadStart(Engine.inst.cibCmdbDataThread, Environment.CIB_CMDB_SWITCH, Environment.FALSE);

        Engine.checkThreadStart(Engine.inst.excelmodelScanThread, Environment.DATADATE_DELAY_SWITCH, Environment.FALSE);

        Engine.checkThreadStart(Engine.inst.zyfdTakeTimeThread, Environment.TIMESETALARM_SWITCH, Environment.FALSE);

        Engine.checkThreadStart(Engine.inst.changeUnvalidRoleThread, Environment.OPM_ROLE_TMPROLE_SWITCH, Environment.FALSE);
        Engine.checkThreadStart(Engine.inst.procToKfcsSyncThread, PersonalityEnv.PF_SUS_PROCTOKFCS_SWITCH,
            Environment.FALSE);
        Engine.checkThreadStart(Engine.inst.kfcsToICMSSyncThread, PersonalityEnv.PF_SUS_KFCSTOKFNK_SWITCH,
            Environment.FALSE);
        //浦发定时任务获取AZ状态线程
        Engine.checkThreadStart(Engine.inst.azSwitchTimingThread, PersonalityEnv.PF_AZ_STATUS_TIMING_ACQUIRE_SWITCH,
            Environment.FALSE);
        
        Engine.checkThreadStart(Engine.inst.projectToCacheThread, Environment.PROJECT_CACHE_THREAD_SWITCH,
            Environment.FALSE);
        
        Engine.checkThreadStart(Engine.inst.cmdbMonitorThread, PersonalityEnv.PF_CMDB_MONITOR_THREAD_SWITCH,
            Environment.FALSE);
        
        Engine.checkThreadStart(Engine.inst.SynchronizationCmdbSchedulerThread, PersonalityEnv.PF_CMDB_SYNC_THREAD_SWITCH,
            Environment.FALSE);

        Engine.checkThreadStart(Engine.inst.monitorRollbackWorkFlowThread, Environment.ROLLBACK_WORKFLOW_SWITCH,
                Environment.FALSE);

        Engine.checkThreadStart(Engine.inst.ScriptSyncPlatformCodeThread, Environment.SCRIPT_SYNC_PLATFORM_CODE_THREAD_SWITCH,
                Environment.FALSE);

        Engine.checkThreadStart(Engine.inst.scriptDeleteTempAttachmentThread, Environment.SCRIPT_DELETE_TEMP_ATTACHMENT_JOB_THREAD_SWITCH,
                Environment.FALSE);
        
        
        Engine.checkThreadStart(Engine.inst.supperHcCheckResultQueueHandlerThread, Environment.HC_SUPPER_CHECKRESULT_CACHE_QUEUE_SWITCH,
            Environment.FALSE);
        
        Engine.inst.monitoringExecutionThread.start();
        Engine.inst.monitoringKlNjExecutionThread.start();
        
        Engine.inst.deleteHistoryDataCollectSizeNewThread.start();
        Engine.inst.agentLiveMainThread.start();
        Engine.inst.agentLiveThread.start();
        Engine.inst.agentLiveOutTimeThread.start();        

        Engine.checkThreadStart(Engine.inst.taskAutoBackThread, Environment.TASK_AUTO_BACK_THREAD_SWITCH,
            Environment.FALSE);

        
        Engine.checkThreadStart(Engine.inst.itsmSycnUserThread, Environment.YC_USER_SYNC_ITSM_SWITCH,
            Environment.FALSE);
        ExecutionStartThread executionStartThread = new ExecutionStartThread();
        Engine.checkThreadStart(executionStartThread,
            PersonalityEnv.SPDB_GROUP_SUS_EXECUTION_STRATEGY_START, Environment.FALSE);
        
        Engine.checkThreadStart(Engine.inst.orderMoveToHisThread,
            Environment.ISORDERMOVETOHIS, Environment.FALSE);

        Engine.checkThreadStart(Engine.inst.fjSyncUserThread, Environment.FJ_USER_SYNC_SWITCH,
                Environment.FALSE);
        
        Engine.checkThreadStart(Engine.inst.SynProjectModelToTemplateThread,
            Environment.MOVETOPASS, Environment.FALSE);

        Engine.checkThreadStart(Engine.inst.orderRelyStartThread,
            Environment.SH_PAAS_ORDER_SWITCH, Environment.FALSE);
        
        Engine.checkThreadStart(Engine.inst.sdCmdbAutoSyncThread,Environment.CMDB_AUTO_SYNC_COMPUTER_SWITCH,
                Environment.FALSE);
        
        Engine.checkThreadStart(Engine.inst.publicNoticTimingTaskThread, PersonalityEnv.PF_PUBLIC_NOTICE_THREAD_SWITCH,
            Environment.FALSE);
        Engine.checkThreadStart(Engine.inst.empRestartThread, Environment.DATADATE_EMP_SWITCH, Environment.FALSE);
        Engine.checkThreadStart(Engine.inst.sysChangeThread, Environment.SYS_CHANGE_RUNINFO_SWITCH, Environment.FALSE);
        Engine.checkThreadStart(Engine.inst.strategyRestartThread, Environment.HC_STRATEGY_RESTART_SWITCH, Environment.FALSE);
        Engine.checkThreadStart(Engine.inst.distributedTaskExecutor, PersonalityEnv.PF_DISTRIBUTED_TASK_RUN,
            Environment.FALSE);
        
        Engine.checkThreadStart(Engine.inst.cqnsCmdbMonitorThread, Environment.ENTEGOR_SUS_CQNS_CMDB_SWITCH,
            Environment.FALSE);
        Engine.checkThreadStart(Engine.inst.gd_UserSyncThread, Environment.IAM_CASP_CLIENT_SWITCH,
            Environment.FALSE);

        Engine.checkThreadStart(Engine.inst.cqCmdbTxtThread, Environment.CQ_PUBLIC_AGENTTXT_SWITCH,
            Environment.FALSE);
        
        Engine.checkThreadStart(Engine.inst.supperHcReportThread, Environment.HC_SUPPER_HC_REPORT_SWITCH,
            Environment.FALSE);
        Engine.checkThreadStart(Engine.inst.dayStartDelayThread, Environment.DELAY_DAYSTART_SWITCH, Environment.FALSE);

        // add by lch  单server开启的巡检结果超期未返回存储到数据库告警
        Engine.checkThreadStart(Engine.inst.supperCheckResultTimeoutThread, Environment.HC_CHECKRESULT_TIMEOUT_CHECK_SWITCH,
            Environment.FALSE);

        Engine.checkThreadStart(Engine.inst.shutdownTaskStateMonitorThread, Environment.SHUTDOWNTASK_TASK_MONITOR_THREAD,
                Environment.FALSE);
        Engine.checkThreadStart(Engine.inst.activeDiscoverySyncThread, Environment.XMDB_DISCOVERY_SYNC_THREAD,
                Environment.FALSE);
        /** 渤海Mq 启动线程*/
        Engine.checkThreadStart(Engine.inst.onsConsumerThread, Environment.BH_ONS_SWITCH, Environment.FALSE);
        /** 东莞预告警线程开关 ()*/
        Engine.checkThreadStart(Engine.inst.sendWarningDGThread, Environment.DG_WARN_DOWN_SWITCH, Environment.FALSE);
        /** 渤海Mq表分区 启动线程*/
        Engine.checkThreadStart(Engine.inst.theDatabaseTablePartitionThread, Environment.BH_MQ_PARTITION_SWITCH, Environment.FALSE);
        //重庆农商变更超时终止
        Engine.checkThreadStart(Engine.inst.missionarySystemThread, Environment.ISTHREEMINUTESMANDEP, Environment.FALSE);
        //吉林银行CI周期、定时任务线程
        Engine.checkThreadStart(Engine.inst.ciTaskCicleThread, Environment.CI_TASK_CICLETIMING_SWITCH, Environment.FALSE);
        Engine.checkThreadStart(Engine.inst.ciTaskTimingThread,Environment.CI_TASK_CICLETIMING_SWITCH, Environment.FALSE);

        //福建农信定时同步线程
        Engine.checkThreadStart(Engine.inst.seqenceSyncThread, Environment.FJNX_SYNC_TIMING_SWITCH, Environment.FALSE);
        Engine.checkThreadStart(Engine.inst.seqenceAutoThread, Environment.FJNX_SYNC_AUTOSYSTEM_TIMING_SWITCH, Environment.FALSE);
        //报警管理线程开启计算匹配工具个数
        //Engine.checkThreadStart(Engine.inst.alarmThread, Environment.ALARMTHREAD, Environment.FALSE);
        Engine.checkThreadStart(Engine.inst.alarmInfoDataThread, Environment.ALARMTHREAD, Environment.FALSE);

        // 福建农信清理策略轮询清理线程
        Engine.checkThreadStart(Engine.inst.cleanConfigThread, Environment.FJNX_CLEANCONFIG_THREAD_SWITCH, Environment.FALSE);

        // 福建农信固化环境信息同步线程
        Engine.checkThreadStart(Engine.inst.syncEnvironmentThread, Environment.FJNX_SYNCENVIRONMENT_THREAD_SWITCH, Environment.FALSE);

        // 外观报送告警线程
        Engine.checkThreadStart(Engine.inst.subAlarmThread, Environment.SUBALARMTHREAD, Environment.FALSE);

        // 启动日切时间报警调度器
        try {
            CutTimeAlarmScheduler.getInstance().startCutTimeAlarmScheduling();
            _log.info("----------------------------日切时间报警调度器启动成功");
        } catch (Exception e) {
            _log.error("日切时间报警调度器启动失败: " + e.getMessage(), e);
        }

        boolean recoverTimetaskSwitch = ServerEnv.getServerEnv().recoverTimetaskSwitch();
        boolean ttAutoRecoverySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.TT_AUTO_RECOVERY_SWITCH, false);
        //两个开关同时为true，开启定时任务拉起自身任务线程
        if(recoverTimetaskSwitch && ttAutoRecoverySwitch){
          //定时任务拉起自身任务线程
            Thread ttRecoverSelfTaskThread = new TTRecoverSelfTaskThread();
            ttRecoverSelfTaskThread.start();
        }
        //光大IAM
        Engine.checkThreadStart(Engine.inst.gd_IAMThread,Environment.IAM_CASP_CLIENT_SWITCH, Environment.FALSE);
        //光大 agent状态上送 线程 agent agent 生产数据发主题 monitor-topic  proxy 消费主题monitor-topic 后 ，生产数据主题rdp提供主题，server 消费nrdp提供的主题 start
        //Status submission
            boolean  statusB = ServerEnv.getServerEnv().getBooleanConfig("agent.timed.task.switch",false);
            if(statusB){
                Thread kafkaproxy = new Thread(new KafkaServerStatusSumissThread(),"serverStatusSumiss");
                kafkaproxy.start(); // 启动生产者消费者
            }
        //agent状态上送 线程 agent agent 生产数据发主题 monitor-topic  proxy 消费主题monitor-topic 后 ，生产数据主题rdp提供主题，server 消费nrdp提供的主题 end

        // 光大 分配置任务批量操作记录信息 jiaMing 2023-5-22 start
            boolean  assignB = ServerEnv.getServerEnv().getBooleanConfig("assignServerTaskSwitch",false);
            if(assignB){
                Thread assignThread = new Thread(new AssignTaskDetailsThread(),"assignThread");
                assignThread.start(); //分配置任务批量操作记录信息
            }
        //分配置任务批量操作记录信息 jiaMing 2023-5-22 end

        //光大 批量执行操作任务 jiaMing 2023-5-23 start
            boolean  terStartCollTask = ServerEnv.getServerEnv().getBooleanConfig("batchTerStartCollTaskSwitch",false);
            if(terStartCollTask){
                Thread batchTaskThread = new Thread(new BatchTerStartCollTaskThread(),"batchTaskThread");
                batchTaskThread.start();
            }
        //批量执行操作任务 jiaMing 2023-5-23 end

        //光大 消费者线程监控获取消费者状态信息 jiaMing 2023-7-6 start
            boolean  consumerThreadIsRun = ServerEnv.getServerEnv().getBooleanConfig("proxyTopicThreadIsRun",false);
            if(consumerThreadIsRun){
                Thread consumerThread = new Thread(new ProxyTopicStateThread(),"proxyTopicStateThread");
                consumerThread.start();
            }
        //消费者线程监控获取消费者状态信息 jiaMing 2023-7-6 end
        // CMDB同步
        if (ServerEnv.getInstance().getBooleanConfig(Environment.CMDB_SYNC_ENABLED, false)) {
            // CMDB同步任务
            _log.info("starting cmdb \"data syncing\" job.");
            SyncCmdbServerInfoSchedulerService.getInstance().start();
            // CMDB过期数据删除任务
            _log.info("starting cmdb \"data cleaning\" job.");
            SyncCmdbServerInfoDeletingSchedulerService.getInstance().start();
        }

        // 中原银行故障自愈消费kafka中消息，匹配故障自愈中的自愈方案开关
        boolean fshZyConsumerKafkaSwitch = ServerEnv.getInstance().getBooleanConfig(ServerEnv.ZY_CONSUMER_KAFKA_SWITCH, false);
        if(fshZyConsumerKafkaSwitch){
            try{
                Thread thread = new Thread(new KafkaMessageConsumeThread(),"fshZyConsumerKafka");
                thread.start();
            }catch (Exception e){
                _log.error("消费Kafka消息时启动注册失败!");
            }
        }

        boolean  zyCmdbKafkaServerSwitch = ServerEnv.getServerEnv().getZyCmdbKafkaServerSwitch();
        if(zyCmdbKafkaServerSwitch){
            try{
                Thread zyIncrementDataExecuteThread = new Thread(new ZyIncrementDataExecuteThread(),"zyIncrementDataExecuteThread");
                zyIncrementDataExecuteThread.start();
            }catch (Exception e){
                _log.error("zy的CMDB增量消费Kafka消息时启动注册失败!");
            }
        }

        InitOnlineConifg online = new InitOnlineConifg();
        online.initConfigList();

        //启动查询库中配置，更改缓存
        online.reloadConfigInit();
        //中信压力测试查询状态异常数据进行修改
        Engine.checkThreadStart(Engine.inst.updateRuninfoIstateThread, Environment.COMPENSATION_THREAD_ENABLE, Environment.FALSE);
    }

    private void deleteIeaiDetect ()
    {
        Connection con = null;
        PreparedStatement ps = null;
        try
        {
            con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            ps = con.prepareStatement("delete from ieai_detect ");
            ps.executeUpdate();
            con.commit();
        } catch (Exception ex)
        {
            _log.error(ex);
        } finally
        {
            DBResource.closePSConn(con, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }

    }

    /**
     * Get Engine instance.
     * 
     * @return
     */
    static public Engine getInstance ()
    {
        return inst;
    }

    /**
     * Get thread pool without limitation
     * 
     * @return
     */
    public PooledExecutor getThreadPoolUnlimited ()
    {
        return threadPoolUnlimited;
    }

    /**
     * get a thread pool that can be configed.
     * 
     * @return
     */
    public PooledExecutor getThreadPoolLimited ()
    {
        return threadPoolLimited;
    }

    public PooledExecutor getCollectThreadPool ()
    {
        return collectThreadPool;
    }

    public PooledExecutor getAgentStateThreadPool ()
    {
        return agentStateThreadPool;
    }

    /**
     * get ActivityContextManager
     * 
     * @return
     */
    public ActivityContextManager getActCtxManager ()
    {
        return actCtxManager;
    }

    /**
     * get BranchManager
     * 
     * @return
     */
    public BranchManager getBranchManager ()
    {
        return branchManager;
    }

    /**
     * get RecoverManager
     * 
     * @return
     */
    public RecoverManager getRecoverManager ()
    {
        return recoverManager;
    }

    /**
     * get ResourceManager
     * 
     * @return
     */
    public ResourceManager getResourceManager ()
    {
        return resourceManager;
    }

    /**
     * get RecoverManagerCluster
     * 
     * @return
     */
    public RecoverManagerCluster getRecoverManagerCluster ()
    {
        return recoverManagerCluster;
    }

    /**
     * get Scheduler
     * 
     * @return
     */
    public Scheduler getScheduler ()
    {
        return scheduler;
    }

    public void setDBConnect ( boolean isDBConnect )
    {
        this.isDBConnect = isDBConnect;
    }

    public boolean isDBConnect ()
    {
        return isDBConnect;
    }

    /**
     * get WorkflowInstance
     * 
     * @param flowId
     * @return
     */
    public WorkflowInstance getFlowInstance ( long flowId, int type ) throws ServerException
    {
        return getSafetyFirstFlowInstance(flowId, type);
    }

    public WorkflowInstance getSafetyFirstFlowInstance ( final long flowId, int type ) throws ServerException
    {
        try
        {
            return EngineRepository.getInstance().getFlowInstance(flowId, type);
        } catch (RepositoryException ex)
        {
            throw new ServerException(ex.getServerError().getErrCode());
        }
    }

    public WorkflowInstance getSafetyFirstFlowInstance ( final Long flowId ) throws ServerException
    {
        return getSafetyFirstFlowInstance(flowId);
    }

    /**
     * update flow state
     * 
     * @param flowId
     * @param state
     */
    public void updateFlowState ( long flowId, int state, int type ) throws ServerException
    {
        Date endTime = null;
        switch (state)
        {
            case Constants.STATE_FINISHED:
                ActivityRExecHelper.workflowStopped(flowId, true);
                _log.info("info agent workflow end. flowid=" + flowId);
            case Constants.STATE_KILLED:
            case Constants.STATE_STOPPED:
                endTime = new Date();
                scheduler.dispose(flowId);
                resourceManager.dispose(flowId);
                actCtxManager.flowStop(flowId);
        }
        if (Environment.getInstance().getBhMqSendSwitch() && state == Constants.STATE_KILLED) {
            // 终止向MQ发送成功信息 工作流查询终止 ODS监控终止
            boolean isMHead = WorkFlowManager.getInstance().isHeadModelVal(flowId, Constants.IEAI_IEAI);
            if (isMHead) {
                _log.error("工作流终止向MQ发送 SUCCESS 信息，flowId= "+flowId);
                Map<String, String> map = OnsProducerManager.getInstance().sendMq("SUCCESS", flowId,"", endTime);
                String result = map.get("success");
                if ("false".equals(result)) {
                    _log.error("工作流结束发送MQ消息失败");
                }
            }
        }
        try
        {
            // 修改jdbc处理 tao_ding on 2010-1-11
            EngineRepositotyJdbc.getInstance().updateFlowInstanceState(flowId, state, endTime, type);

            CloudFlowMananger cloudManager = new CloudFlowMananger();
            cloudManager.updateOrderStatus(state, flowId);
        } catch (RepositoryException ex)
        {
            _log.error(ex);
            throw new ServerException(ex.getServerError().getErrCode());
        }

        if (!ServerEnv.getInstance().getJobSchedulingQuerySystemSwitch())
        {
            // guogang add for topo转移数据
            stopFlowsToToPo(flowId, state);
            // 日报邮件发送线程方法
            saveHistory(flowId, state, type);
        }
        if (type == Constants.IEAI_IEAI && state==Constants.STATE_FINISHED)
        {
            //拓扑图线程增加中间表：工作流完成状态数据 begin
            long beginTime = System.currentTimeMillis();
            TopoSendTriPoolThread.getInstance().addRequest(flowId, " ", 1, "2", beginTime, 2);
            //拓扑图线程增加中间表：工作流完成状态数据 end
        }
        // junyu.zhang add for db option reccord
        if (Environment.getInstance().getSwitchDBRunrecord())
        {

            DBOptionRecordUtil.getInstance().updateBackupRunRecord(flowId, state);
        }
        if(type == Constants.IEAI_IEAI){
            WorkflowInstance flowInstance = Engine.getInstance().getFlowInstance(flowId, Constants.IEAI_IEAI);
            boolean isM = false;
            try {
                isM = WorkFlowManager.getInstance().isModel(flowInstance.getProjectName(), flowInstance.getFlowName(), Constants.IEAI_IEAI);
            } catch (RepositoryException e) {
                throw new RuntimeException(e);
            }
            if (isM) {
                Engine.getInstance().endFlowImportExcelModel(flowId);
                _log.info("------------------主线结束更新版本-------------------");
            }
            // 徽商工作流结束对接告警平台
            if (Environment.getInstance().getHuiShangBankSwitch() && type == Constants.IEAI_IEAI) {
                try {
                    if (!flowInstance.getFlowName().contains("日启动") && !flowInstance.getFlowName().contains("主流程")) {
                        String host = Environment.getInstance().getServerIP();
                        _log.info("工作流结束对接告警平台");
                        UserModel userModel = UserManager.getInstance().getStartOrEndFlowUser(flowId);
                        List<UserModel> userList = new ArrayList<UserModel>();
                        userList.add(userModel);
                        WarningInterfaceUtilsIEAI.callWarningForStartEndFlow("IEAI", "IEAI_ENDFLOW", "one", host,
                                "工作流结束", new Date(), flowInstance.getProjectName(), flowInstance.getFlowName(),
                                "",flowId,null ,null, userList);
                    }
                } catch (UnknownHostException e) {
                    throw new RuntimeException(e);
                }

            }
        }
        if (Environment.getInstance().getDgProjectParamSwitch()) {
            TopoLogicalThread topoLogicalThread = new TopoLogicalThread(flowId, TopoLogicalThread.ENDTYPE);
            topoLogicalThread.start();
        }
    }// end of method

    /**   
     * @Title: updateFlowStateConn   
     * @Description: 工作流修改状态的方法，使用事务连接的方式进行修改   
     * @param flowId
     * @param state
     * @param conn
     * @throws ServerException      
     * @author: tao_ding 
     * @date:   2019-4-16 下午4:18:48   
     */
    public void updateFlowStateConn ( long flowId, int state, Connection conn ) throws ServerException
    {
        try
        {
            Date endTime = new Date();
            scheduler.dispose(flowId);
            resourceManager.dispose(flowId);
            actCtxManager.flowStop(flowId);
            switch (state)
            {
                case Constants.STATE_FINISHED:
                    ActivityRExecHelper.workflowStopped(flowId, true);
                    _log.info("info agent workflow end. flowid=" + flowId);
                    break;
                case Constants.STATE_KILLED:
                case Constants.STATE_STOPPED:
                    break;
                default:
            }
            EngineRepositotyJdbc.getInstance().updateFlowInstanceState(flowId, state, endTime, conn);
            if(!Environment.getInstance().getEnginePocSwitch())
            {
                CloudFlowMananger cloudManager = new CloudFlowMananger();
                cloudManager.updateOrderStatus(state, flowId);
            }

            //SwitchMonitorManage.getInstance().sendFininshMsgToThridPart(flowId, conn);

            if (state == Constants.STATE_FINISHED&&!Environment.getInstance().getEnginePocSwitch())
            {
                // add by y恢复(条件设置在流程上非活动上)
                EngineRepositotyJdbc er = new EngineRepositotyJdbc();
                RepWorkflowInstance rfi = null;
                int type = 0;
                try
                {
                    rfi = er.getFlowInstance(flowId);
                    type = ProjectManager.getInstance().getProjectTypeByFlowId(flowId);
                } catch (RepositoryException e)
                {
                    e.printStackTrace();
                }
                MonitorSystem ms = MonitorSystem.getInstance();
                if (ServerEnv.getInstance().isCheckActRecoverSwitch() && null != rfi && type == Constants.IEAI_IEAI)
                {
                    ms.getActEndRecover(rfi.getProjectName(), rfi.getFlowName(), "", flowId);
                }
                
                AddClearFlowId clear = new AddClearFlowId(flowId, state);
                clear.start();
                if (type == Constants.IEAI_IEAI)
                {
                    //拓扑图线程增加中间表：工作流完成状态数据 begin
                    long beginTime = System.currentTimeMillis();
                    TopoSendTriPoolThread.getInstance().addRequest(flowId, " ", 1, "2", beginTime, 2);
                    //拓扑图线程增加中间表：工作流完成状态数据 end
                }
                /**增加内蒙古流程结束告警*/
                boolean monitorSwitchNMG = PersonalityEnv.getWebserviceWarnSwitchNMGVALUE();
                if(monitorSwitchNMG)
                {
                    int proCount = EngineRepositotyJdbc.getInstance().getCountEndAlarmByProname(flowId, Constants.IEAI_IEAI);
                    int flowCount = EngineRepositotyJdbc.getInstance().getCountEndAlarmByFlownameAndProName(flowId, Constants.IEAI_IEAI);
                    String proName = EngineRepositotyJdbc.getInstance().getProjectNameByFlowid(flowId, Constants.IEAI_IEAI);
                    String flowName = EngineRepositotyJdbc.getInstance().getFlowNameByFlowid(flowId, Constants.IEAI_IEAI);
                    if (monitorSwitchNMG && (proCount > 0 || flowCount > 0))
                    {
                        Alarm alarm = new Alarm();
                        alarm = ms.getAlarm();
                        String warnMsg = getWarnMessageNMG(proName, flowName);
                        alarm.setMessage(warnMsg);
                        alarm.setSeverity(ms.SEVERITY_WARN);
                        String namePhone = "";
                        namePhone = ms.getPhone(proName);
                        alarm.setAdditionalInfo(namePhone);
                        
                        _log.info("========符合内蒙古流程结束发送短信通知条件,开始发送！");
                        SendWarnOfNMGThread thread = new SendWarnOfNMGThread(alarm);
                        thread.start();
                    }
                }
                /** add by lsd 20231024 重庆银行报警发告警平台 */
                boolean warnSwitchCq = PersonalityEnv.isCqWarnSwitchValue();
                if (warnSwitchCq )
                {
                    String prjName = EngineRepositotyJdbc.getInstance().getProjectNameByFlowid(flowId, Constants.IEAI_IEAI);
                    String flowName = EngineRepositotyJdbc.getInstance().getFlowNameByFlowid(flowId, Constants.IEAI_IEAI);

                    SendSocketWarnCQ.sendSocketWarnCQ(prjName, flowName, flowId, null, null, "", 20, "FINISH", "",
                            "", "","");
                }

                /** 渤海发送Mq消息 */
                if (Environment.getInstance().getBhMqSendSwitch()) {
                    Map<String, String> map = OnsProducerManager.getInstance().sendMq("SUCCESS", flowId,"", endTime);
                    String result = map.get("success");
                    if ("false".equals(result)) {
                        _log.error("工作流结束发送MQ消息失败");
                    }
                }
                /** 渤海工作流完成后,删除超时告警信息 删除到时未结束的告警 */
                if (Environment.getInstance().getBhNodeDelSwitch()){
                    boolean deleteWarnInfoBh = KeyNodeAlarmManager.getInstance().deleteWarnInfoBh(flowId, "sa");
                    if (!deleteWarnInfoBh) {
                        _log.error("自动删除到时未结束告警失败!");
                    }

                }
                /**徽商 启动工作流对告警平台发送消息*/
                if (Environment.getInstance().getHuiShangBankSwitch()  && type == Constants.IEAI_IEAI){
                    try {
                        String prjName = EngineRepositotyJdbc.getInstance().getProjectNameByFlowid(flowId, Constants.IEAI_IEAI);
                        String flowName = EngineRepositotyJdbc.getInstance().getFlowNameByFlowid(flowId, Constants.IEAI_IEAI);
                        if (!flowName.contains("日启动") &&  !flowName.contains("主流程")) {
                            String host = Environment.getInstance().getServerIP();
                            _log.info("工作流结束对接告警平台");
                            UserModel userModel = UserManager.getInstance().getStartOrEndFlowUser(flowId);
                            List<UserModel> userList = new ArrayList<UserModel>();
                            userList.add(userModel);
                            WarningInterfaceUtilsIEAI.callWarningForStartEndFlow("IEAI", "IEAI_ENDFLOW", "one", host,
                                    "工作流结束", new Date(), prjName, flowName,
                                    "",flowId,null ,null, userList);
                        }
                    } catch (UnknownHostException e) {
                        throw new RuntimeException(e);
                    }

                }

                if (state == Constants.STATE_FINISHED || state == Constants.STATE_KILLED){
                    boolean bhTimeCheckSwitch = DateCheckManager.getInstance().queryDataTimeCheckSwitch("bhTimeCheckSwitch");
                    if (bhTimeCheckSwitch){
                        WorkflowInstance flowInstance = getFlowInstance(flowId, Constants.IEAI_IEAI);
                        if (null != flowInstance) {
                            boolean isM = WorkFlowManager.getInstance().isModel(flowInstance.getProjectName(), flowInstance.getFlowName(), Constants.IEAI_IEAI);
                            if (isM){
                                DateCheckManager.getInstance().updateDataTimeCheck(flowInstance.getFlowName(),flowInstance.getFlowInsName());
                            }
                        }
                    }
                }

            }

            if (Environment.getInstance().getDgProjectParamSwitch()) {
                TopoLogicalThread topoLogicalThread = new TopoLogicalThread(flowId, TopoLogicalThread.ENDTYPE);
                topoLogicalThread.start();
            }
        } catch (Exception ex)
        {
            _log.error(ex);
            throw new ServerException(ex.hashCode());
        }
    }

    /**   
     * @Title: updateFlowStateConn   
     * @Description: 工作流修改状态的方法，使用事务连接的方式进行修改   
     * @param flowId
     * @param state
     * @param conn
     * @throws ServerException      
     * @author: tao_ding 
     * @date:   2019-4-16 下午4:18:48   
     */
    public void updateFlowStateSUSConn ( long flowId, int state, Connection conn ) throws ServerException
    {
        try
        {
            Date endTime = new Date();
            // scheduler.dispose(flowId);
            // resourceManager.dispose(flowId);
            // actCtxManager.flowStop(flowId);
            switch (state)
            {
                case Constants.STATE_FINISHED:
                    // ActivityRExecHelper.workflowStopped(flowId, true);
                    _log.info("info agent workflow end. flowid=" + flowId);
                    break;
                case Constants.STATE_KILLED:
                case Constants.STATE_STOPPED:
                    break;
                default:
            }
            EngineRepositotyJdbc.getInstance().updateFlowInstanceState(flowId, state, endTime, conn);
        } catch (Exception ex)
        {
            _log.error(ex);
            throw new ServerException(ex.hashCode());
        }
    }

    /**   
     * @Title: stopFlowsToToPo   
     * @Description:  topo转移数据(方法提取)
     * @param flowId
     * @param
     * @param state      
     * @author: yue_sun 
     * @date:   2019年1月7日 上午10:18:17   
     */
    public void stopFlowsToToPo ( Long flowId, int state )
    {
        if (state == Constants.STATE_FINISHED)
        {
            // 只有当开关开启时，才收集拓扑图信息
            boolean stratFlag = false;
            try
            {
                stratFlag = Boolean.parseBoolean(
                    Environment.getInstance().getSysConfig(Environment.TOPO_DATADUMP_SWITCH, Environment.FALSE));
            } catch (Exception e)
            {
                stratFlag = false;
                _log.error(Environment.TOPO_DATADUMP_SWITCH + " flag error!");
            }
            if (stratFlag)
            {
                synchronized (stoppFlowsToToPo)
                {
                    stoppFlowsToToPo.add(flowId + ":" + state);
                    stoppFlowsToToPo.notify();
                }
            }
            // 判断工程类型是否为作业调度
            try
            {
                Project project = getProjectNameByFlowId(flowId);
                double proType = project.getProjectTpye();
                // 判断工程类型是否为作业调度
                if (proType == Constants.PROJECT_TYPE_JOBSCHEDULING)
                {
                    AddClearFlowId clear = new AddClearFlowId(flowId, state);
                    clear.start();
                }
            } catch (ServerException e)
            {
                _log.error("Engine stopFlowsToToPo method get Project is error ; " + e.getMessage(), e);
            }

        }
    }

    /**   
     * @Title: saveHistory   
     * @Description: 日报邮件发送线程方法提取  
     * @param flowId
     * @param
     * @param state      
     * @author: yue_sun 
     * @date:   2019年1月7日 上午10:15:16   
     */
    public void saveHistory ( Long flowId, int state, int sysType )
    {
        try
        {
            ProjectBean projectBean = ProjectManager.getInstance().getProjectInfoByFlowId(flowId, sysType);

            Project project = com.ideal.ieai.server.repository.project.ProjectManager.getInstance()
                    .loadProject(projectBean.getProjectName());
            boolean saveHistorySwitch = PersonalityEnv.getSAVEHISTORYSWITCH_DEFAULT();
            // 日报邮件发送线程, 工程类型为作业调度的工程才发送邮件
            if (saveHistorySwitch && project.getProjectType().getProjectType() == Constants.PROJECT_TYPE_JOBSCHEDULING)
            {
                if (state == Constants.STATE_FINISHED)
                {
                    // 更新工作流最后一次运行信息
                    SaveHistoryThread ms = new SaveHistoryThread(flowId, false);
                    ms.start();

                } else if (state == Constants.STATE_KILLED)
                {
                    // 保存关键批量信息
                    SaveHistoryThread ms = new SaveHistoryThread(flowId, true);
                    ms.start();
                }
                _log.info("SaveHistoryThread start successfully!");
            }
        } catch (ServerException e)
        {
            _log.error(e.getMessage(), e);
        }
    }

    /**
     * update flow state
     * 
     * @param flowId
     * @param state
     */
    public void updateFlowState ( long flowId, int state, int type, Connection con ) throws ServerException
    {
        Date endTime = null;
        switch (state)
        {
            case Constants.STATE_FINISHED:
            case Constants.STATE_KILLED:
            case Constants.STATE_STOPPED:
                endTime = new Date();
                scheduler.dispose(flowId);
                resourceManager.dispose(flowId);
                actCtxManager.flowStop(flowId);
        }
        try
        {
            // 修改jdbc处理 tao_ding on 2010-1-11
            EngineRepositotyJdbc.getInstance().updateFlowInstanceState(flowId, state, endTime, type);
        } catch (RepositoryException ex)
        {
            System.out.println("hehre");
            ex.printStackTrace();
            throw new ServerException(ex.getServerError().getErrCode());
        }
        // guogang add for topo转移数据
        if (state == Constants.STATE_FINISHED)
        {
            synchronized (stoppFlowsToToPo)
            {
                stoppFlowsToToPo.add(flowId + ":" + state);
                stoppFlowsToToPo.notify();
            }
            // 判断工程类型是否为作业调度
            if (type == Constants.IEAI_IEAI)
            {
                //拓扑图线程增加中间表：工作流完成状态数据 begin
                long beginTime = System.currentTimeMillis();
                TopoSendTriPoolThread.getInstance().addRequest(flowId, " ", 1, "2", beginTime, 2);
                //拓扑图线程增加中间表：工作流完成状态数据 end
                AddClearFlowId clear = new AddClearFlowId(flowId, state);
                clear.start();
            }
        }

    }

    /**
     * main scope end
     * 
     * @param flowId
     */
    public void mainBranchEnd ( boolean isSafeFirst, long flowId )
    {
        if (isSafeFirst)
        {
            try
            {
                // 修改为JDBC方式实现 tao_ding on 2010-1-13
                EngineRepositotyJdbc.getInstance().setFlowInsMainBranchEnd(flowId);
            } catch (RepositoryException ex)
            {
                _log.error(ex.getMessage(), ex);
            }
        } else
        {
            synchronized (efficientflowInstance)
            {
                WorkflowInstance flowIns = (WorkflowInstance) efficientflowInstance.get(new Long(flowId));
                flowIns.setMainBranchEnd(true);
            }
        }
    }

    /**
     * get flow lock
     * 
     * @param flowId
     * @return
     */
    public Object getFlowLock ( long flowId )
    {
        Object lock = null;
        synchronized (lockMap)
        {
            Iterator iter = lockMap.entrySet().iterator();
            while (iter.hasNext())
            {
                Entry entry = (Entry) iter.next();
                Object key = entry.getKey();
                Long value = (Long) entry.getValue();
                if (value.longValue() == flowId && key != null)
                {
                    return key;
                }
            }
            lock = new Object();
            lockMap.put(lock, new Long(flowId));
            return lock;
        }
    }

    /**
     * monitor stopping flow.
     * 
     * @param flow
     */
    public void addStoppingFlow ( StoppingFlow flow )
    {
        synchronized (stoppingFlows)
        {
            stoppingFlows.add(flow);
            stoppingFlows.notify();
        }
    }

    /**
     * <li>
     * <li>fellowing methods is used to provide service to user. *
     * <li>
     * @throws RepositoryException ********************************************************
     */
    public long startFlow ( UserInfo user, String prjName, String flowName, List args, Map envVars,
            WorkflowLogConfig logConfig, String instanceName, String comment, boolean forceEfficiencyPrior, int type,
            int prjType ) throws ServerException, RepositoryException
    {
        return startFlow(user, prjName, flowName, args, envVars, logConfig, instanceName, comment, forceEfficiencyPrior,
            null, null, type, prjType, false);
    }

    public long startFlow ( UserInfo user, String prjName, String flowName, List args, Map envVars,
            WorkflowLogConfig logConfig, String instanceName, String iworkItemid, String comment,
            boolean forceEfficiencyPrior, BasicActElement starterSucElem, Date startTime, int type, int prjType,
            int istartType, FlowPlanBean pbean ) throws ServerException, RepositoryException
    {
        if (null != pbean && (null != pbean.getPlanId() && !"".equals(pbean.getPlanId())))
        {
            if (!"ExcelActExecModelSUS".equals(prjName))
            {
                String[] mainlineName = null;
                try
                {
                    mainlineName = ProjectManager.getInstance().getProjectMainlineName(prjName, flowName);
                } catch (RepositoryException e)
                {
                    _log.info("startFlow is getProjectMainlineName get error projectName:" + prjName + " flowName:"
                            + flowName);
                }
                long starttime = System.currentTimeMillis();
                if (StringUtils.isNotEmpty(mainlineName[0]) && StringUtils.isNotEmpty(mainlineName[1]))
                {
                    try
                    {
                        WorkFlowManager.getInstance().importExcelModel(mainlineName[0], mainlineName[1]);
                    } catch (RepositoryException e)
                    {
                        _log.info(
                            "startFlow is importExcelModel get error projectName:" + prjName + " flowName:" + flowName);
                    }
                }
                _log.info("启动工作流导入Excelmodel总耗时为：" + ((System.currentTimeMillis()) - starttime) + "projectName:"
                        + prjName + " flowName:" + flowName);
            }
            _log.info("开始启动工作流 projectName:" + prjName + " flowName:" + flowName);
            return startFlow(user, prjName, flowName, args, envVars, logConfig, instanceName, iworkItemid, comment,
                forceEfficiencyPrior, starterSucElem, startTime, false, 0, type, prjType, istartType, pbean);

        } else
        {
            return this.startFlow(user, prjName, flowName, args, envVars, logConfig, instanceName, iworkItemid, comment,
                forceEfficiencyPrior, starterSucElem, startTime, type, prjType, false);
        }
    }

    /**
     * Start workflow for normal workflow
     * 
     * @param user
     * @param prjName
     * @param flowName
     * @param args
     * @param envVars
     * @param logConfig
     * @param instanceName
     * @param comment
     * @param forceEfficiencyPrior
     * @param starterSucElem
     * @param startTime
     * @return
     * @throws ServerException
     * @throws RepositoryException 
     */
    public long startFlow ( UserInfo user, String prjName, String flowName, List args, Map envVars,
            WorkflowLogConfig logConfig, String instanceName, String iworkItemid, String comment,
            boolean forceEfficiencyPrior, BasicActElement starterSucElem, Date startTime, int type, int prjType,
            boolean isAdvance ) throws ServerException, RepositoryException
    {
        if (!"ExcelActExecModelSUS".equals(prjName) && !"ExcelActExecModelDR".equals(prjName))
        {
            String[] mainlineName = null;
            try
            {
                mainlineName = ProjectManager.getInstance().getProjectMainlineName(prjName, flowName);
            } catch (RepositoryException e)
            {
                _log.info(
                    "startFlow is getProjectMainlineName get error projectName:" + prjName + " flowName:" + flowName);
            }
            long starttime = System.currentTimeMillis();
            if (StringUtils.isNotEmpty(mainlineName[0]) && StringUtils.isNotEmpty(mainlineName[1]))
            {
                try
                {
                    WorkFlowManager.getInstance().importExcelModel(mainlineName[0], mainlineName[1]);
                } catch (RepositoryException e)
                {
                    _log.info(
                        "startFlow is importExcelModel get error projectName:" + prjName + " flowName:" + flowName);
                }
            }
            _log.info("启动工作流导入Excelmodel总耗时为：" + ((System.currentTimeMillis()) - starttime)/1000 + "s," + "projectName:" + prjName
                    + " flowName:" + flowName);
        }
        _log.info("开始启动工作流 projectName:" + prjName + " flowName:" + flowName);
        return startFlow(user, prjName, flowName, args, envVars, logConfig, instanceName, iworkItemid, comment,
            forceEfficiencyPrior, starterSucElem, startTime, false, 0, type, prjType, isAdvance);
    }


    public long startFlowForPass ( UserInfo user, String prjName, String flowName, List args, Map envVars,
                                   WorkflowLogConfig logConfig, String instanceName, String iworkItemid, String comment,
                                   boolean forceEfficiencyPrior, BasicActElement starterSucElem, Date startTime, int type, int prjType,
                                   boolean isAdvance,String tempUuid ) throws ServerException, RepositoryException
    {
        if (!"ExcelActExecModelSUS".equals(prjName) && !"ExcelActExecModelDR".equals(prjName))
        {
            String[] mainlineName = null;
            try
            {
                mainlineName = ProjectManager.getInstance().getProjectMainlineName(prjName, flowName);
            } catch (RepositoryException e)
            {
                _log.info(
                        "startFlow is getProjectMainlineName get error projectName:" + prjName + " flowName:" + flowName);
            }
            long starttime = System.currentTimeMillis();
            if (StringUtils.isNotEmpty(mainlineName[0]) && StringUtils.isNotEmpty(mainlineName[1]))
            {
                try
                {
                    WorkFlowManager.getInstance().importExcelModel(mainlineName[0], mainlineName[1]);
                } catch (RepositoryException e)
                {
                    _log.info(
                            "startFlow is importExcelModel get error projectName:" + prjName + " flowName:" + flowName);
                }
            }
            _log.info("启动工作流导入Excelmodel总耗时为：" + ((System.currentTimeMillis()) - starttime) + "projectName:" + prjName
                    + " flowName:" + flowName);
        }
        _log.info("开始启动工作流 projectName:" + prjName + " flowName:" + flowName);
        return startFlowForPass(user, prjName, flowName, args, envVars, logConfig, instanceName, iworkItemid, comment,
                forceEfficiencyPrior, starterSucElem, startTime, false, 0, type, prjType, isAdvance,tempUuid);
    }


    public long startFlowNew ( UserInfo user, String prjName, String flowName, List args, Map envVars,
            WorkflowLogConfig logConfig, String instanceName, String comment, boolean forceEfficiencyPrior,
            BasicActElement starterSucElem, Date startTime, int type, int prjType, int istartType, FlowPlanBean pbean )
            throws ServerException, RepositoryException
    {
        if (null != pbean && (null != pbean.getPlanId() && !"".equals(pbean.getPlanId())))
        {
            return startFlow(user, prjName, flowName, args, envVars, logConfig, instanceName, null, comment,
                forceEfficiencyPrior, starterSucElem, startTime, type, prjType, istartType, pbean);
        } else
        {
            return this.startFlow(user, prjName, flowName, args, envVars, logConfig, instanceName, comment,
                forceEfficiencyPrior, starterSucElem, startTime, type, prjType, false);
        }
    }

    public long startFlowForPass ( UserInfo user, String prjName, String flowName, List args, Map envVars,
                                   WorkflowLogConfig logConfig, String instanceName, String comment, boolean forceEfficiencyPrior,
                                   BasicActElement starterSucElem, Date startTime, int type, int prjType, boolean isAdvance,String tempUuid )
            throws ServerException, RepositoryException
    {

        return startFlowForPass(user, prjName, flowName, args, envVars, logConfig, instanceName, null, comment,
                forceEfficiencyPrior, starterSucElem, startTime, type, prjType, isAdvance,tempUuid);
    }


    public long startFlow ( UserInfo user, String prjName, String flowName, List args, Map envVars,
            WorkflowLogConfig logConfig, String instanceName, String comment, boolean forceEfficiencyPrior,
            BasicActElement starterSucElem, Date startTime, int type, int prjType, boolean isAdvance )
            throws ServerException, RepositoryException
    {
    	Boolean inQueue=QueuedWorkflowProcessor.addWorkflowStartMethod(user,prjName,  flowName,  args,  envVars,
                logConfig,  instanceName,  comment,  forceEfficiencyPrior,
                starterSucElem,  startTime,  type,  prjType,  isAdvance);
       if(inQueue){
           return -1L;
       }
        return startFlow(user, prjName, flowName, args, envVars, logConfig, instanceName, null, comment,
            forceEfficiencyPrior, starterSucElem, startTime, type, prjType, isAdvance);
    }

    /**
     * 小信封审核 作业调用 启动
     * @param user
     * @param prjName
     * @param flowName
     * @param args
     * @param envVars
     * @param logConfig
     * @param instanceName
     * @param comment
     * @param forceEfficiencyPrior
     * @param starterSucElem
     * @param startTime
     * @param type
     * @param prjType
     * @param workitemId
     * @return
     * @throws ServerException
     */
    public long startFlowForDoubleReview ( UserInfo user, String prjName, String flowName, List args, Map envVars,
            WorkflowLogConfig logConfig, String instanceName, String comment, boolean forceEfficiencyPrior,
            BasicActElement starterSucElem, Date startTime, int type, int prjType, Long workitemId )
            throws ServerException
    {
        String[] mainlineName = null;
        try
        {
            mainlineName = ProjectManager.getInstance().getProjectMainlineName(prjName, flowName);
        } catch (RepositoryException e)
        {
            _log.info("startFlow is getProjectMainlineName get error projectName:" + prjName + " flowName:" + flowName);
        }
        long starttime = System.currentTimeMillis();
        if (StringUtils.isNotEmpty(mainlineName[0]) && StringUtils.isNotEmpty(mainlineName[1]))
        {
            try
            {
                WorkFlowManager.getInstance().importExcelModel(mainlineName[0], mainlineName[1]);
            } catch (RepositoryException e)
            {
                _log.info("startFlow is importExcelModel get error projectName:" + prjName + " flowName:" + flowName);
            }
        }
        _log.info("启动工作流导入Excelmodel总耗时为：" + ((System.currentTimeMillis()) - starttime) + "projectName:" + prjName
                + " flowName:" + flowName);
        return startFlowForDoubleReview(user, prjName, flowName, args, envVars, logConfig, instanceName, comment,
            forceEfficiencyPrior, starterSucElem, startTime, false, 0, type, prjType, workitemId);
    }

    /**
     * The method is prepared for starter workflow
     * 
     * @param user
     * @param prjName
     * @param flowName
     * @param logConfig
     * @param instanceName
     * @param comment
     * @param forceEfficiencyPrior
     * @param starterSucElem
     * @param startTime
     * @param starterFlowId
     * @return
     * @throws ServerException
     * @throws RepositoryException 
     */
    public long starterStartFlow ( UserInfo user, String prjName, String flowName, WorkflowLogConfig logConfig,
            String instanceName, String comment, boolean forceEfficiencyPrior, BasicActElement starterSucElem,
            Date startTime, long starterFlowId, int prjType ) throws ServerException, RepositoryException
    {
        return startFlow(user, prjName, flowName, null, null, logConfig, instanceName, comment, forceEfficiencyPrior,
            starterSucElem, startTime, true, starterFlowId, Constants.IEAI_IEAI_BASIC, prjType);
    }

    /**
     * <AUTHOR>
     * @des:start a workflow.
     * @datea:2010-1-22
     * @param user
     * @param prjName
     * @param flowName
     * @param args
     * @param envVars
     * @param logConfig
     * @param instanceName
     * @param comment
     * @param forceEfficiencyPrior
     * @param starterSucElem
     * @param startTime
     * @param isStarter
     * @param starterFlowId
     * @param
     * @return
     * @throws ServerException
     * @throws RepositoryException 
     */
    public long startFlow ( UserInfo user, String prjName, String flowName, List args, Map envVars,
            WorkflowLogConfig logConfig, String instanceName, String comment, boolean forceEfficiencyPrior,
            BasicActElement starterSucElem, Date startTime, boolean isStarter, long starterFlowId, int type,
            int prjType ) throws ServerException, RepositoryException
    {
        return startFlow(user, prjName, flowName, args, envVars, logConfig, instanceName, null, comment,
            forceEfficiencyPrior, starterSucElem, startTime, isStarter, starterFlowId, type, prjType, false);
    }

    public long startFlow ( UserInfo user, String prjName, String flowName, List args, Map envVars,
            WorkflowLogConfig logConfig, String instanceName, String workItemId, String comment,
            boolean forceEfficiencyPrior, BasicActElement starterSucElem, Date startTime, boolean isStarter,
            long starterFlowId, int type, int prjType, int istartType, FlowPlanBean pbean )
            throws ServerException, RepositoryException
    {
        if (null != pbean && (null != pbean.getPlanId() && !"".equals(pbean.getPlanId())))
        {

            long flowId = 0;
            String system = null;
            Connection con = null;
            // 任务查询用 双人复核ID
            for (int i = 0;; i++)
            {
                try
                {
                    try
                    {
                        con = DBResource.getConnection("startFlow", _log, type);
                        WorkFlowManager manager = WorkFlowManager.getInstance();
                        if (startTime != null && startTime.getTime() > System.currentTimeMillis())
                        {
                            throw new ServerException(ServerError.ERR_INVALID_FLOW_START_TIME);
                        }
                        String ip = "";
                        List addrs = new ArrayList();

                        long group = EngineRepository.getInstance().getGroupId(prjName, con, prjType);
                        if (group == 0)
                        {
                            _log.error("group not found project is：" + prjName + " ,flow name is: " + flowName);
                            throw new ServerException(ServerError.ERR_GROUP_NOT_FOUND);
                        }
                        /* 判断是否主线 */
                        boolean isM = manager.isMainFlow(prjName, flowName, con);
                        if (isM)
                        {
                            ip = EngineRepository.getInstance().loadBalanceIP(group, addrs, con);
                        } else
                        {
                            /* 判断互斥作业 */
                            int count = manager.getMutexActCount(flowName, prjName, con);
                            if (count > 0)
                            {
                                _log.error("mutex actname is running  is：projectName" + prjName + " ,flow name is: "
                                        + flowName);
                                throw new ServerException(ServerError.ERR_MUTEX_RUUNING);
                            }
                            ip = EngineRepository.getInstance().loadBalanceIP(group, addrs, con);
                        }
                        if ("" == ip)
                        {
                            if (SystemConfig.isHeartbeat())
                            {
                                _log.error("ip not found group is：" + group + " ,flow name is: " + flowName);
                                throw new ServerException(ServerError.ERR_SERVER_IP_NOT_FOUND);
                            } else
                            {
                                ip = getSNHostName();
                            }
                        }
                        OffsetFreqBean offsetFreqBean = null;
                        boolean isDM = false;
                        /* 判断是否日启动 */
                        if (!"ExcelActExecModelSUS".equals(prjName))
                        {
                            isDM = manager.isDayStartMainFlow(prjName, flowName, con);

                            if (isDM)
                            {
                                offsetFreqBean = manager.getDayStartOffsetFreq(prjName, flowName, con);
                                if (SystemConfig.isOther())
                                {
                                    _log.info("startFlow is getDayStartOffsetFreq  prjName: " + prjName + " flowName: "
                                            + flowName + " offset: " + offsetFreqBean.getOffset() + " freq: "
                                            + offsetFreqBean.getFreq());
                                }
                            } else
                            {
                                OffsetFreqBean dayStartBean = manager.getMainProjectName(prjName, flowName, con);
                                offsetFreqBean = manager.getOffsetFreq(dayStartBean.getProjectName(),
                                    dayStartBean.getFlowName(), con);
                                if (SystemConfig.isOther())
                                {
                                    _log.info("startFlow is getDayStartOffsetFreq  prjName: " + prjName + " flowName: "
                                            + flowName + " offset: " + offsetFreqBean.getOffset() + " freq: "
                                            + offsetFreqBean.getFreq() + " getProjectName: "
                                            + dayStartBean.getProjectName() + " getFlowName(): "
                                            + dayStartBean.getFlowName());
                                }
                            }
                            system = manager.getSystem(prjName, flowName, con);
                        }

                        String prjUuid = resourceManager.getLatestPrjUuid(prjName, con, prjType);
                        Project project = resourceManager.getProjectByUuid(prjUuid);
                        if (!"ExcelActExecModelSUS".equals(prjName))
                        {
                            if (StringUtils.isEmpty(system))
                            {
                                system = project.getSysname();
                            }
                        }
                        if( Environment.getInstance().getXanxSwitch()&&isDM){//西安导入
                            String ISYSNAME="";
                            if (StringUtils.isEmpty(system))
                            {
                                ISYSNAME = manager.isDayStartMainISYSNAMEFlow(prjName, flowName, con);
                                system = ISYSNAME;
                            }
                        }
                        flowId = IdGenerator.createIdForExecAct(RepWorkflowInstance.class, con);
                        long mainScopeId = IdGenerator.createIdForExecAct(RepBranchScope.class, con);

                        WorkflowInstance flowInstance = new WorkflowInstance();

                        Long prjUpperId = ProjectManager.getInstance().getPrjUpperIdByPrjName(prjName, type);
                        Long prjLatestId = ProjectManager.getInstance().getPrjLatestIdByPrjName(prjName, type);
                        Long flowUpperId = ProjectManager.getInstance().getFlowUpperIdByFlowName(flowName, prjUpperId,
                            type);
                        Long flowLastId = ProjectManager.getInstance().getFlowLatestIdByFlowName(flowName, prjUpperId,
                            type);

                        // init workflow infomation
                        Workflow flow = project.getWorkflowByName(flowName);
                        flowInstance.setAutoStart(flow.isAutoStartFlow());
                        flowInstance.setFlowComment(comment);
                        flowInstance.setFlowDefId(flow.getID());
                        flowInstance.setFlowDes(flow.getDescription());
                        flowInstance.setFlowId(flowId);
                        flowInstance.setFlowInsName(instanceName);
                        flowInstance.setFlowLogConfig(logConfig);
                        flowInstance.setFlowName(flowName);
                        flowInstance.setFlowPrior(flow.getPriority());
                        flowInstance.setHostName(ip);
                        flowInstance.setMainScopeId(mainScopeId);
                        flowInstance.setPrjUuid(prjUuid);
                        flowInstance.setProjectName(prjName);
                        flowInstance.setSafeFirst(true);
                        flowInstance.setIstartType(istartType);
                        // 日常操作——任务查询 显示参数功能 反写IEAI_DOUBLECHECK_WORKITEM.iflowid 双人复核ID
                        if (null != workItemId && !"".equals(workItemId))
                        {
                            flowInstance.setWorkItemId(workItemId);
                        }
                        Date flowStartTime = startTime != null ? startTime : new Date();
                        flowInstance.setStartTime(flowStartTime);

                        flowInstance.setStartUser(user);
                        flowInstance.setStatus(Constants.STATE_RUNNING);
                        flowInstance.set_system(system);
                        if (offsetFreqBean != null)
                        {
                            flowInstance.setOffset(offsetFreqBean.getOffset());
                            flowInstance.setFreq(offsetFreqBean.getFreq());
                        }

                        flowInstance.setPrjUpperId(prjUpperId);
                        flowInstance.setPrjId(prjLatestId);
                        flowInstance.setFlowUpperId(flowUpperId);
                        flowInstance.setFlowOwnId(flowLastId);
                        String butterflyversion = "";
                        if (envVars != null)
                        {
                            butterflyversion = envVars.get("butterflyversion") == null ? ""
                                    : envVars.get("butterflyversion").toString();
                        }
                        flowInstance.setButterflyversion(butterflyversion);
                        if (ServerEnv.getInstance().getJobSchedulingQuerySystemSwitch())
                        {
                            if (flow.isUseCheckflag())
                            {
                                EngineRepositotyJdbc.getInstance().delOdsActFinishedFlag(prjName, flowName,
                                    instanceName, con);
                            }
                        }
                        if (isDM)
                        {
                            EngineRepositotyJdbc.getInstance().saveFowInstanceDayStart(flowInstance, con);
                        } else
                        {
                            EngineRepositotyJdbc.getInstance().saveFowInstance(flowInstance, con, pbean);
                        }

                        resourceManager.initFlowArgsAndEnvVars(flowId, project, flow, args, envVars, con, type);

                        actCtxManager.initFlow(flow, flowInstance, type, con);

                        if (starterSucElem == null)
                        {
                            EngineRepositotyJdbc.getInstance().saveFlowisStartMiddle(flowId, 0, 0, con);
                        } else
                        {
                            EngineRepositotyJdbc.getInstance().saveFlowisStartMiddle(flowId, 0, 1, con);
                        }

                        branchManager.startMainBranch(flowId, mainScopeId, flowInstance.isSafeFirst(), flow,
                            starterSucElem, con);

                        EngineRepositotyJdbc.getInstance().saveMainFlowACTPRENUM(flowId, instanceName, prjName,
                            flowName, starterSucElem, con);

                        con.commit();
                        //拓扑节点：启动工作流  begin
                        TopoSendTriPoolThread.getInstance().addRequest(flowId, " ", 1, "0",
                                System.currentTimeMillis(), 1);
                        //拓扑节点：启动工作流  end

                        _log.info("the flow " + flowId + " is start success, The server is " + ip);

                    } catch (Exception e)
                    {
                        // 失败后数据回滚
                        DBResource.rollback(con, ServerError.ERR_DB_UPDATE, e,
                            Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
                        _log.error("Error when start Flow ! and flowName = " + flowName, e);
                        DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
                    } finally
                    {
                        DBResource.closeConnection(con, Thread.currentThread().getStackTrace()[1].getMethodName(),
                            _log);
                    }
                    break;
                } catch (RepositoryException ex)
                {
                    _log.error("Error when start Flow ! and flowName = " + flowName, ex);
                    DBRetryUtil.waitForNextTry(i, ex);
                }
            }
            return flowId;

        } else
        {
            return this.startFlow(user, prjName, flowName, args, envVars, logConfig, instanceName, workItemId, comment,
                forceEfficiencyPrior, starterSucElem, startTime, isStarter, starterFlowId, type, prjType, false);
        }
    }


    public long startFlowForPass ( UserInfo user, String prjName, String flowName, List args, Map envVars,
                                   WorkflowLogConfig logConfig, String instanceName, String workItemId, String comment,
                                   boolean forceEfficiencyPrior, BasicActElement starterSucElem, Date startTime, boolean isStarter,
                                   long starterFlowId, int type, int prjType, boolean isAdvance,String tempUuid ) throws ServerException, RepositoryException
    {
        long flowId = 0;
        String system = null;
        Connection con = null;
        // 任务查询用 双人复核ID
        for (int i = 0;; i++)
        {
            try
            {
                try
                {
                    con = DBResource.getConnection("startFlow", _log, type);
                    WorkFlowManager manager = WorkFlowManager.getInstance();
                    if (startTime != null && startTime.getTime() > System.currentTimeMillis())
                    {
                        throw new ServerException(ServerError.ERR_INVALID_FLOW_START_TIME);
                    }
                    String ip = "";
                    List addrs = new ArrayList();

                    long group = EngineRepository.getInstance().getGroupId(prjName, con, prjType);
                    if (group == 0)
                    {
                        _log.error("group not found project is：" + prjName + " ,flow name is: " + flowName);
                        throw new ServerException(ServerError.ERR_GROUP_NOT_FOUND);
                    }
                    /* 判断是否主线 */
                    boolean isM = manager.isMainFlow(prjName, flowName, con);
                    if (isM)
                    {
                        ip = EngineRepository.getInstance().loadBalanceIP(group, addrs, con);
                    } else
                    {
                        /* 判断互斥作业 */
                        int count = manager.getMutexActCount(flowName, prjName, con);
                        if (count > 0)
                        {
                            _log.error(
                                    "mutex actname is running  is：projectName" + prjName + " ,flow name is: " + flowName);
                            throw new ServerException(ServerError.ERR_MUTEX_RUUNING);
                        }
                        ip = EngineRepository.getInstance().loadBalanceIP(group, addrs, con);
                    }

                    if ("" == ip)
                    {
                        if (SystemConfig.isHeartbeat())
                        {
                            _log.error("ip not found group is：" + group + " ,flow name is: " + flowName);
                            throw new ServerException(ServerError.ERR_SERVER_IP_NOT_FOUND);
                        } else
                        {
                            ip = getSNHostName();
                        }
                    }
                    OffsetFreqBean offsetFreqBean = null;
                    boolean isDM = false;
                    /* 判断是否日启动 */
                    if (!"ExcelActExecModelSUS".equals(prjName))
                    {
                        isDM = manager.isDayStartMainFlow(prjName, flowName, con);

                        if (isDM)
                        {
                            offsetFreqBean = manager.getDayStartOffsetFreq(prjName, flowName, con);
                            if (SystemConfig.isOther())
                            {
                                _log.info("startFlow is getDayStartOffsetFreq  prjName: " + prjName + " flowName: "
                                        + flowName + " offset: " + offsetFreqBean.getOffset() + " freq: "
                                        + offsetFreqBean.getFreq());
                            }
                        } else
                        {
                            OffsetFreqBean dayStartBean = manager.getMainProjectName(prjName, flowName, con);
                            offsetFreqBean = manager.getOffsetFreq(dayStartBean.getProjectName(),
                                    dayStartBean.getFlowName(), con);
                            if (SystemConfig.isOther())
                            {
                                _log.info("startFlow is getDayStartOffsetFreq  prjName: " + prjName + " flowName: "
                                        + flowName + " offset: " + offsetFreqBean.getOffset() + " freq: "
                                        + offsetFreqBean.getFreq() + " getProjectName: " + dayStartBean.getProjectName()
                                        + " getFlowName(): " + dayStartBean.getFlowName());
                            }
                        }
                        system = manager.getSystem(prjName, flowName, con);
                    }

                    String prjUuid = resourceManager.getLatestPrjUuid(prjName, con, prjType);
                    Project project = resourceManager.getProjectByUuid(prjUuid);
                    if (!"ExcelActExecModelSUS".equals(prjName))
                    {
                        if (StringUtils.isEmpty(system))
                        {
                            system = project.getSysname();
                        }
                    }
                    if( Environment.getInstance().getXanxSwitch()&&isDM){//西安导入
                        String ISYSNAME="";
                        if (StringUtils.isEmpty(system))
                        {
                            ISYSNAME = manager.isDayStartMainISYSNAMEFlow(prjName, flowName, con);
                            system = ISYSNAME;
                        }
                    }
                    flowId = IdGenerator.createIdForExecAct(RepWorkflowInstance.class, con);
                    long mainScopeId = IdGenerator.createIdForExecAct(RepBranchScope.class, con);

                    WorkflowInstance flowInstance = new WorkflowInstance();

                    Long prjUpperId = ProjectManager.getInstance().getPrjUpperIdByPrjName(prjName, type);
                    Long prjLatestId = ProjectManager.getInstance().getPrjLatestIdByPrjName(prjName, type);
                    Long flowUpperId = ProjectManager.getInstance().getFlowUpperIdByFlowName(flowName, prjUpperId,
                            type);
                    Long flowLastId = ProjectManager.getInstance().getFlowLatestIdByFlowName(flowName, prjUpperId,
                            type);

                    // init workflow infomation
                    Workflow flow = project.getWorkflowByName(flowName);
                    flowInstance.setAutoStart(flow.isAutoStartFlow());
                    flowInstance.setFlowComment(comment);
                    flowInstance.setFlowDefId(flow.getID());
                    flowInstance.setFlowDes(flow.getDescription());
                    flowInstance.setFlowId(flowId);
                    flowInstance.setFlowInsName(instanceName);
                    flowInstance.setFlowLogConfig(logConfig);
                    flowInstance.setFlowName(flowName);
                    flowInstance.setFlowPrior(flow.getPriority());
                    flowInstance.setHostName(ip);
                    flowInstance.setMainScopeId(mainScopeId);
                    flowInstance.setPrjUuid(prjUuid);
                    flowInstance.setProjectName(prjName);
                    flowInstance.setSafeFirst(true);
                    flowInstance.setTempLateUuid(tempUuid);
                    // 日常操作——任务查询 显示参数功能 反写IEAI_DOUBLECHECK_WORKITEM.iflowid 双人复核ID
                    if (null != workItemId && !"".equals(workItemId))
                    {
                        flowInstance.setWorkItemId(workItemId);
                    }
                    Date flowStartTime = startTime != null ? startTime : new Date();
                    flowInstance.setStartTime(flowStartTime);

                    flowInstance.setStartUser(user);
                    flowInstance.setStatus(Constants.STATE_RUNNING);
                    if (isAdvance)
                    {
                        flowInstance.setStatus(-6);   // 预加载状态
                    }
                    flowInstance.set_system(system);
                    if (offsetFreqBean != null)
                    {
                        flowInstance.setOffset(offsetFreqBean.getOffset());
                        flowInstance.setFreq(offsetFreqBean.getFreq());
                    }

                    flowInstance.setPrjUpperId(prjUpperId);
                    flowInstance.setPrjId(prjLatestId);
                    flowInstance.setFlowUpperId(flowUpperId);
                    flowInstance.setFlowOwnId(flowLastId);
                    String butterflyversion = "";
                    if (envVars != null)
                    {
                        butterflyversion = envVars.get("butterflyversion") == null ? ""
                                : envVars.get("butterflyversion").toString();
                    }
                    flowInstance.setButterflyversion(butterflyversion);
                    if (ServerEnv.getInstance().getJobSchedulingQuerySystemSwitch())
                    {
                        if (flow.isUseCheckflag())
                        {
                            EngineRepositotyJdbc.getInstance().delOdsActFinishedFlag(prjName, flowName, instanceName,
                                    con);
                        }
                    }
                    if (isDM)
                    {
                        EngineRepositotyJdbc.getInstance().saveFowInstanceDayStart(flowInstance, con);
                    } else
                    {
                        EngineRepositotyJdbc.getInstance().saveFowInstance(flowInstance, con);
                    }

                    resourceManager.initFlowArgsAndEnvVars(flowId, project, flow, args, envVars, con, type);

                    actCtxManager.initFlow(flow, flowInstance, type, con);

                    if (starterSucElem == null)
                    {
                        EngineRepositotyJdbc.getInstance().saveFlowisStartMiddle(flowId, 0, 0, con);
                    } else
                    {
                        EngineRepositotyJdbc.getInstance().saveFlowisStartMiddle(flowId, 0, 1, con);
                    }

                    branchManager.startMainBranch(flowId, mainScopeId, flowInstance.isSafeFirst(), flow, starterSucElem,
                            con);

                    EngineRepositotyJdbc.getInstance().saveMainFlowACTPRENUM(flowId, instanceName, prjName, flowName,
                            starterSucElem, con);

                    con.commit();
                    //拓扑节点：启动工作流  begin
                    TopoSendTriPoolThread.getInstance().addRequest(flowId, " ", 1, "0",
                            System.currentTimeMillis(), 1);
                    //拓扑节点：启动工作流  end

                    _log.info("the flow " + flowId + " is start success, The server is " + ip);

                    MonitorSystem ms = MonitorSystem.getInstance();
                    if (ServerEnv.getInstance().isCheckActRecoverSwitch() && type == Constants.IEAI_IEAI)
                    {
                        ms.getActStartRecover(prjName, flowName, "", flowId);
                    }

                } catch (Exception e)
                {
                    // 失败后数据回滚
                    DBResource.rollback(con, ServerError.ERR_DB_UPDATE, e,
                            Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                    String method = Thread.currentThread().getStackTrace()[1].getMethodName();
                    _log.error("Error when start Flow ! and flowName = " + flowName, e);
                    DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConnection(con, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log.error("Error when start Flow ! and flowName = " + flowName, ex);
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
        return flowId;
    }

    /**
     * <AUTHOR>
     * @des:start a workflow.
     * @datea:2010-1-22
     * @param user
     * @param prjName
     * @param flowName
     * @param args
     * @param envVars
     * @param logConfig
     * @param instanceName
     * @param comment
     * @param forceEfficiencyPrior
     * @param starterSucElem
     * @param startTime
     * @param isStarter
     * @param starterFlowId
     * @param
     * @return
     * @throws ServerException
     * @throws RepositoryException 
     */
    public long startFlow ( UserInfo user, String prjName, String flowName, List args, Map envVars,
            WorkflowLogConfig logConfig, String instanceName, String workItemId, String comment,
            boolean forceEfficiencyPrior, BasicActElement starterSucElem, Date startTime, boolean isStarter,
            long starterFlowId, int type, int prjType, boolean isAdvance ) throws ServerException, RepositoryException
    {
        long flowId = 0;
        String system = null;
        Connection con = null;
        // 此连接为创建id连接
        Connection conForId = null;
        // 任务查询用 双人复核ID
        for (int i = 0;; i++)
        {
            try
            {
                try
                {
                    con = DBResource.getConnection("startFlow", _log, type);
                    conForId = DBResource.getConnection("startFlow", _log, type);
                    WorkFlowManager manager = WorkFlowManager.getInstance();
                    if (startTime != null && startTime.getTime() > System.currentTimeMillis())
                    {
                        throw new ServerException(ServerError.ERR_INVALID_FLOW_START_TIME);
                    }
                    String ip = "";
                    List addrs = new ArrayList();

                    long group = EngineRepository.getInstance().getGroupId(prjName, con, prjType);
                    if (group == 0)
                    {
                        _log.error("group not found project is：" + prjName + " ,flow name is: " + flowName);
                        throw new ServerException(ServerError.ERR_GROUP_NOT_FOUND);
                    }
                    /* 判断是否主线 */
                    boolean isM = manager.isMainFlow(prjName, flowName, con);
                    if (isM)
                    {
                        ip = EngineRepository.getInstance().loadBalanceIP(group, addrs, con);
                    } else
                    {
                        /* 判断互斥作业 */
                        int count = manager.getMutexActCount(flowName, prjName, con);
                        if (count > 0)
                        {
                            _log.error(
                                "mutex actname is running  is：projectName" + prjName + " ,flow name is: " + flowName);
                            throw new ServerException(ServerError.ERR_MUTEX_RUUNING);
                        }
                        ip = EngineRepository.getInstance().loadBalanceIP(group, addrs, con);
                    }

                    if ("" == ip)
                    {
                        if (SystemConfig.isHeartbeat())
                        {
                            _log.error("ip not found group is：" + group + " ,flow name is: " + flowName);
                            throw new ServerException(ServerError.ERR_SERVER_IP_NOT_FOUND);
                        } else
                        {
                            ip = getSNHostName();
                        }
                    }
                    OffsetFreqBean offsetFreqBean = null;
                    boolean isDM = false;
                    /* 判断是否日启动 */
                    if (!"ExcelActExecModelSUS".equals(prjName))
                    {
                        isDM = manager.isDayStartMainFlow(prjName, flowName, con);

                        if (isDM)
                        {
                            offsetFreqBean = manager.getDayStartOffsetFreq(prjName, flowName, con);
                            if (SystemConfig.isOther())
                            {
                                _log.info("startFlow is getDayStartOffsetFreq  prjName: " + prjName + " flowName: "
                                        + flowName + " offset: " + offsetFreqBean.getOffset() + " freq: "
                                        + offsetFreqBean.getFreq());
                            }
                        } else
                        {
                            OffsetFreqBean dayStartBean = manager.getMainProjectName(prjName, flowName, con);
                            offsetFreqBean = manager.getOffsetFreq(dayStartBean.getProjectName(),
                                dayStartBean.getFlowName(), con);
                            if (SystemConfig.isOther())
                            {
                                _log.info("startFlow is getDayStartOffsetFreq  prjName: " + prjName + " flowName: "
                                        + flowName + " offset: " + offsetFreqBean.getOffset() + " freq: "
                                        + offsetFreqBean.getFreq() + " getProjectName: " + dayStartBean.getProjectName()
                                        + " getFlowName(): " + dayStartBean.getFlowName());
                            }
                        }
                        system = manager.getSystem(prjName, flowName, con);
                    }

                    String prjUuid = resourceManager.getLatestPrjUuid(prjName, con, prjType);
                    Project project = resourceManager.getProjectByUuid(prjUuid);
                    if (!"ExcelActExecModelSUS".equals(prjName))
                    {
                        if (StringUtils.isEmpty(system))
                        {
                            system = project.getSysname();
                        }
                    }
                    if( Environment.getInstance().getXanxSwitch()&&isDM){//西安导入
                        String ISYSNAME="";
                        if (StringUtils.isEmpty(system))
                        {
                            ISYSNAME = manager.isDayStartMainISYSNAMEFlow(prjName, flowName, con);
                            system = ISYSNAME;
                        }
                    }

                    flowId = IdGenerator.createIdForExecAct(RepWorkflowInstance.class, conForId);
                    long mainScopeId = IdGenerator.createIdForExecAct(RepBranchScope.class, conForId);

                    WorkflowInstance flowInstance = new WorkflowInstance();

                    Long prjUpperId = ProjectManager.getInstance().getPrjUpperIdByPrjName(prjName, type);
                    Long prjLatestId = ProjectManager.getInstance().getPrjLatestIdByPrjName(prjName, type);
                    Long flowUpperId = ProjectManager.getInstance().getFlowUpperIdByFlowName(flowName, prjUpperId,
                        type);
                    Long flowLastId = ProjectManager.getInstance().getFlowLatestIdByFlowName(flowName, prjUpperId,
                        type);

                    // init workflow infomation
                    Workflow flow = project.getWorkflowByName(flowName);
                    flowInstance.setAutoStart(flow.isAutoStartFlow());
                    flowInstance.setFlowComment(comment);
                    flowInstance.setFlowDefId(flow.getID());
                    flowInstance.setFlowDes(flow.getDescription());
                    flowInstance.setFlowId(flowId);
                    flowInstance.setFlowInsName(instanceName);
                    flowInstance.setFlowLogConfig(logConfig);
                    flowInstance.setFlowName(flowName);
                    flowInstance.setFlowPrior(flow.getPriority());
                    flowInstance.setHostName(ip);
                    flowInstance.setMainScopeId(mainScopeId);
                    flowInstance.setPrjUuid(prjUuid);
                    flowInstance.setProjectName(prjName);
                    flowInstance.setSafeFirst(true);
                    // 日常操作——任务查询 显示参数功能 反写IEAI_DOUBLECHECK_WORKITEM.iflowid 双人复核ID
                    if (null != workItemId && !"".equals(workItemId))
                    {
                        flowInstance.setWorkItemId(workItemId);
                    }
                    Date flowStartTime = startTime != null ? startTime : new Date();
                    flowInstance.setStartTime(flowStartTime);

                    flowInstance.setStartUser(user);

                    //POC增加作业试跑功能
                    boolean isTest = false;
                    if(envVars!=null && envVars.get("isTest")!=null){
                        try{
                            isTest = (boolean) envVars.get("isTest");
                        } catch (Exception e) {
                            _log.error("isTest is not boolean type",e);
                        }
                    }
                    if(isTest){
                        flowInstance.setIsCheck(1);
                    }else{
                        flowInstance.setIsCheck(0);
                    }


                    int listWorkFlows=0;
                    //判断是否开启重复会计日志启动工作流校验开关
                    if (PersonalityEnv.isDateRepeatSwitchDefault()&&prjType==1&&StringUtils.isNotBlank(instanceName))
                    {
                        flowInstance.setStatus(Constants.STATE_READY_START);
                    }else {
                        flowInstance.setStatus(Constants.STATE_RUNNING);
                    }

                    if (isAdvance)
                    {
                        flowInstance.setStatus(-6);   // 预加载状态
                    }
                    flowInstance.set_system(system);
                    if (offsetFreqBean != null)
                    {
                        flowInstance.setOffset(offsetFreqBean.getOffset());
                        flowInstance.setFreq(offsetFreqBean.getFreq());
                    }

                    flowInstance.setPrjUpperId(prjUpperId);
                    flowInstance.setPrjId(prjLatestId);
                    flowInstance.setFlowUpperId(flowUpperId);
                    flowInstance.setFlowOwnId(flowLastId);
                    String butterflyversion = "";
                    if (envVars != null)
                    {
                        butterflyversion = envVars.get("butterflyversion") == null ? ""
                                : envVars.get("butterflyversion").toString();
                    }
                    flowInstance.setButterflyversion(butterflyversion);
                    if (ServerEnv.getInstance().getJobSchedulingQuerySystemSwitch())
                    {
                        if (flow.isUseCheckflag())
                        {
                            EngineRepositotyJdbc.getInstance().delOdsActFinishedFlag(prjName, flowName, instanceName,
                                con);
                        }
                    }
                    if (isDM)
                    {
                        EngineRepositotyJdbc.getInstance().saveFowInstanceDayStart(flowInstance, con);
                    } else
                    {
                        EngineRepositotyJdbc.getInstance().saveFowInstance(flowInstance, con);
                    }

                    resourceManager.initFlowArgsAndEnvVars(flowId, project, flow, args, envVars, con, type);

                    long time1 = System.currentTimeMillis();
                    actCtxManager.initFlow(flow, flowInstance, type, con);

                    if (starterSucElem == null)
                    {
                        EngineRepositotyJdbc.getInstance().saveFlowisStartMiddle(flowId, 0, 0, con);
                    } else
                    {
                        EngineRepositotyJdbc.getInstance().saveFlowisStartMiddle(flowId, 0, 1, con);
                    }

                    branchManager.startMainBranch(flowId, mainScopeId, flowInstance.isSafeFirst(), flow, starterSucElem,
                            con,conForId);

                    long time = System.currentTimeMillis();
                    EngineRepositotyJdbc.getInstance().saveMainFlowACTPRENUM(flowId, instanceName, prjName, flowName,
                            starterSucElem, con,conForId);
                    _log.info("==========saveMainFlowACTPRENUM endtime: "  + (System.currentTimeMillis()-time)/1000 + "s, flowid=" + flowId);

                    conForId.commit();

                    con.commit();

                    //拓扑节点：启动工作流  begin
                    TopoSendTriPoolThread.getInstance().addRequest(flowId, " ", 1, "0",
                            System.currentTimeMillis(), 1);
                    //拓扑节点：启动工作流  end


                    if (PersonalityEnv.isDateRepeatSwitchDefault()&&prjType==1&&StringUtils.isNotBlank(instanceName))
                    {

                        startFlowForReady(flowInstance,Constants.IEAI_IEAI);
                    }

                   
                    new ProjectManagerWS().synFlowXml(String.valueOf(flowId),flowName);
                    


                    _log.info("the flow " + flowId + " is start success, The server is " + ip);

                    MonitorSystem ms = MonitorSystem.getInstance();
                    if (ServerEnv.getInstance().isCheckActRecoverSwitch() && type == Constants.IEAI_IEAI)
                    {
                        ms.getActStartRecover(prjName, flowName, "", flowId);
                    }

                } catch (Exception e)
                {
                    // 失败后数据回滚
                    DBResource.rollback(con, ServerError.ERR_DB_UPDATE, e,
                        Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                    DBResource.rollback(conForId, ServerError.ERR_DB_UPDATE, e,
                            Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                    String method = Thread.currentThread().getStackTrace()[1].getMethodName();
                    _log.error("Error when start Flow ! and flowName = " + flowName, e);
                    DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConnection(con, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                    DBResource.closeConnection(conForId, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log.error("Error when start Flow ! and flowName = " + flowName, ex);
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
        /** 渤海发送Mq消息 */
        //渤海mq启动信息发送两次问题修改
//        if (Environment.getInstance().getBhMqSendSwitch()){
//            Map<String, String> map = OnsProducerManager.getInstance().sendMq("START", flowId,"", new Date());
//            String result = map.get("success");
//            _log.info("启动工作流发送MQ消息命令: " +flowId );
//            if ("false".equals(result)) {
//                _log.error("工作流开始发送MQ消息失败");
//            }
//        }

        /**徽商 启动工作流对告警平台发送消息*/
        if (Environment.getInstance().getHuiShangBankSwitch() && type == Constants.IEAI_IEAI){
            try {
                if (!flowName.contains("日启动") &&  !flowName.contains("主流程")) {
                    String host = Environment.getInstance().getServerIP();
                    _log.info("工作流启动对接告警平台");
                    UserModel userModel = UserManager.getInstance().getStartOrEndFlowUser(flowId);
                    List<UserModel> userList = new ArrayList<UserModel>();
                    userList.add(userModel);
                    WarningInterfaceUtilsIEAI.callWarningForStartEndFlow("IEAI", "IEAI_STARTFLOW", "one", host,
                            "工作流启动", new Date(), prjName, flowName,
                            "",flowId,null ,null, userList);
                }
            } catch (UnknownHostException e) {
                throw new RuntimeException(e);
            }

        }
        _log.info("*********工作流启动完成***********");
        return flowId;
    }



    /***
     * @Title:
     * @Description: 将启动的作业状态置成准备，之后通过本方法判断是否将准备变成运行
     * @author: bo_yang
     * @Date: 2023/9/6 9:35
     * @return: void
     */
    public void startFlowForReady ( WorkflowInstance flowInstance ,int type)
    {
        Connection con =null;
        try
        {

            con = DBResource.getConnection("updateFlowInstanceStateNewNoConn", _log, type);
            //方法使用一个连接，方法内关闭
            _log.info("作业启动 进入更改准备状态  信息如下： flowid："+flowInstance.getFlowId()+"，工程名："+
                    flowInstance.getProjectName()+"，工作流名："+flowInstance.getFlowName()+"，实例名："+flowInstance.getFlowInsName());
            int listrepFlow = EngineRepositotyJdbc.getInstance().getFlowInstanceStateNoConnection(flowInstance.getProjectName(), flowInstance.getFlowName(),
                    flowInstance.getFlowInsName(),con);
            //判断是否有相同工作流在运行，如果有就返回
            if(listrepFlow>0){
                _log.info("相同实例名称的工作流，准备启动将本次启动的工作流状态置成终止状态 信息如下： flowid："+flowInstance.getFlowId()+"工程名："+
                        flowInstance.getProjectName()+"工作流名："+flowInstance.getFlowName()+"实例名："+flowInstance.getFlowInsName());

                //把自己改成终止
                DelayerManager.getInstance().updateFlowInstanceStateNewNoConn(flowInstance.getFlowId(),Constants.STATE_READY_START,Constants.STATE_STOPPED,con);
                _log.info("相同实例名称的工作流已经启动！，将本次启动的工作流状态置成终止状态 信息如下： flowid："+flowInstance.getFlowId()+"工程名："+
                        flowInstance.getProjectName()+"工作流名："+flowInstance.getFlowName()+"实例名："+flowInstance.getFlowInsName());
                return;
            }
            //将最小准备状态改成运行状态，其他改为终止
            EngineRepositotyJdbc.getInstance().startReadyFlowInstance(flowInstance.getProjectName(), flowInstance.getFlowName(),
                    flowInstance.getFlowInsName(),   con);
        } catch (RepositoryException e)
        {
            _log.error("startFlowForReadye is error:",e);
            throw new RuntimeException(e);
        }finally
        {
            DBResource.closeConnection(con,"startFlowForReady",_log);
        }

    }



    public long startFlowForDoubleReview ( UserInfo user, String prjName, String flowName, List args, Map envVars,
            WorkflowLogConfig logConfig, String instanceName, String comment, boolean forceEfficiencyPrior,
            BasicActElement starterSucElem, Date startTime, boolean isStarter, long starterFlowId, int type,
            int prjType, Long workitemId ) throws ServerException
    {

        long flowId = 0;
        String system = null;

        for (int i = 0;; i++)
        {
            try
            {
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("startFlow", _log, type);
                    WorkFlowManager manager = WorkFlowManager.getInstance();
                    if (startTime != null && startTime.getTime() > System.currentTimeMillis())
                    {
                        throw new ServerException(ServerError.ERR_INVALID_FLOW_START_TIME);
                    }
                    String ip = "";
                    List addrs = new ArrayList();

                    long group = EngineRepository.getInstance().getGroupId(prjName, con, prjType);
                    if (group == Constants.IEAI_DAILY_OPERATIONS)
                    {
                        group = Constants.IEAI_DAILY_OPERATIONS;
                    }
                    if (group == 0)
                    {
                        _log.error("group not found project is：" + prjName + " ,flow name is: " + flowName);
                        throw new ServerException(ServerError.ERR_GROUP_NOT_FOUND);
                    }
                    /* 判断是否主线 */
                    boolean isM = manager.isMainFlow(prjName, flowName, con);
                    if (isM)
                    {
                        ip = EngineRepository.getInstance().loadBalanceIP(group, addrs, con);
                    } else
                    {
                        /* 判断互斥作业 */
                        int count = manager.getMutexActCount(flowName, prjName, con);
                        if (count > 0)
                        {
                            _log.error(
                                "mutex actname is running  is：projectName" + prjName + " ,flow name is: " + flowName);
                            throw new ServerException(ServerError.ERR_MUTEX_RUUNING);
                        }
                        ip = EngineRepository.getInstance().loadBalanceIP(group, addrs, con);
                    }
                    if ("" == ip)
                    {
                        if (SystemConfig.isHeartbeat())
                        {
                            _log.error("ip not found group is：" + group + " ,flow name is: " + flowName);
                            throw new ServerException(ServerError.ERR_SERVER_IP_NOT_FOUND);
                        } else
                        {
                            ip = getSNHostName();
                        }
                    }
                    boolean isDM = false;
                    OffsetFreqBean offsetFreqBean = null;
                    if (!"ExcelActExecModelSUS".equals(prjName))
                    {
                        /* 判断是否日启动 */
                        isDM = manager.isDayStartMainFlow(prjName, flowName, con);

                        if (isDM)
                        {
                            offsetFreqBean = manager.getDayStartOffsetFreq(prjName, flowName, con);
                            if (SystemConfig.isOther())
                            {
                                _log.info("startFlow is getDayStartOffsetFreq  prjName: " + prjName + " flowName: "
                                        + flowName + " offset: " + offsetFreqBean.getOffset() + " freq: "
                                        + offsetFreqBean.getFreq());
                            }
                        } else
                        {
                            OffsetFreqBean dayStartBean = manager.getMainProjectName(prjName, flowName, con);
                            offsetFreqBean = manager.getOffsetFreq(dayStartBean.getProjectName(),
                                dayStartBean.getFlowName(), con);
                            if (SystemConfig.isOther())
                            {
                                _log.info("startFlow is getDayStartOffsetFreq  prjName: " + prjName + " flowName: "
                                        + flowName + " offset: " + offsetFreqBean.getOffset() + " freq: "
                                        + offsetFreqBean.getFreq() + " getProjectName: " + dayStartBean.getProjectName()
                                        + " getFlowName(): " + dayStartBean.getFlowName());
                            }
                        }
                        system = manager.getSystem(prjName, flowName, con);
                    }
                    if( Environment.getInstance().getXanxSwitch()&&isDM){//西安导入
                        String ISYSNAME="";
                        if (StringUtils.isEmpty(system))
                        {
                            ISYSNAME = manager.isDayStartMainISYSNAMEFlow(prjName, flowName, con);
                            system = ISYSNAME;
                        }
                    }
                    String prjUuid = resourceManager.getLatestPrjUuid(prjName, con, prjType);
                    Project project = resourceManager.getProjectByUuid(prjUuid);
                    flowId = IdGenerator.createIdForExecAct(RepWorkflowInstance.class, con);
                    long mainScopeId = IdGenerator.createIdForExecAct(RepBranchScope.class, con);

                    WorkflowInstance flowInstance = new WorkflowInstance();

                    Long prjUpperId = ProjectManager.getInstance().getPrjUpperIdByPrjName(prjName, type);
                    Long prjLatestId = ProjectManager.getInstance().getPrjLatestIdByPrjName(prjName, type);
                    Long flowUpperId = ProjectManager.getInstance().getFlowUpperIdByFlowName(flowName, prjUpperId,
                        type);
                    Long flowLastId = ProjectManager.getInstance().getFlowLatestIdByFlowName(flowName, prjUpperId,
                        type);

                    // init workflow infomation
                    Workflow flow = project.getWorkflowByName(flowName);
                    flowInstance.setAutoStart(flow.isAutoStartFlow());
                    flowInstance.setFlowComment(comment);
                    flowInstance.setFlowDefId(flow.getID());
                    flowInstance.setFlowDes(flow.getDescription());
                    flowInstance.setFlowId(flowId);
                    flowInstance.setFlowInsName(instanceName);
                    flowInstance.setFlowLogConfig(logConfig);
                    flowInstance.setFlowName(flowName);
                    flowInstance.setFlowPrior(flow.getPriority());
                    flowInstance.setHostName(ip);
                    flowInstance.setMainScopeId(mainScopeId);
                    flowInstance.setPrjUuid(prjUuid);
                    flowInstance.setProjectName(prjName);
                    flowInstance.setSafeFirst(true);
                    Date flowStartTime = startTime != null ? startTime : new Date();
                    flowInstance.setStartTime(flowStartTime);

                    flowInstance.setStartUser(user);
                    flowInstance.setStatus(Constants.STATE_RUNNING);
                    flowInstance.set_system(system);
                    if (offsetFreqBean != null)
                    {
                        flowInstance.setOffset(offsetFreqBean.getOffset());
                        flowInstance.setFreq(offsetFreqBean.getFreq());
                    }

                    flowInstance.setPrjUpperId(prjUpperId);
                    flowInstance.setPrjId(prjLatestId);
                    flowInstance.setFlowUpperId(flowUpperId);
                    flowInstance.setFlowOwnId(flowLastId);

                    //作业调度双人复核启动先进行参数校验  防止参数校验不成功 保存工作流信息
                    resourceManager.validateArgsAndEnv(flow,args,project,envVars);

                    if (isDM)
                    {
                        EngineRepositotyJdbc.getInstance().saveFowInstanceDayStart(flowInstance, con);
                    } else
                    {
                        EngineRepositotyJdbc.getInstance().saveFowInstance(flowInstance, con);
                    }
                    ///保存工作流的公共变量和参数
                    resourceManager.initFlowArgsAndEnvVars(flowId, project, flow, args, envVars, con, type);
                    //工作流
                    actCtxManager.initFlow(flow, flowInstance, type, con);

                    if (starterSucElem == null)
                    {
                        EngineRepositotyJdbc.getInstance().saveFlowisStartMiddle(flowId, 0, 0, con);
                    } else
                    {
                        EngineRepositotyJdbc.getInstance().saveFlowisStartMiddle(flowId, 0, 1, con);
                    }
                    //启动工作流
                    branchManager.startMainBranch(flowId, mainScopeId, flowInstance.isSafeFirst(), flow, starterSucElem,
                        con);

                    EngineRepositotyJdbc.getInstance().saveMainFlowACTPRENUM(flowId, instanceName, prjName, flowName,
                        starterSucElem, con);
                    // 保存双人复核相关信息至历史表
                    EngineRepositotyJdbc.getInstance().removeWorkItemForJobscheduling(workitemId, con);
                    con.commit();

                    //拓扑节点：启动工作流  begin
                    TopoSendTriPoolThread.getInstance().addRequest(flowId, " ", 1, "0",
                            System.currentTimeMillis(), 1);
                    //拓扑节点：启动工作流  end
                    _log.info("the flow " + flowId + " is start success, The server is " + ip);

                    MonitorSystem ms = MonitorSystem.getInstance();
                    if (ServerEnv.getInstance().isCheckActRecoverSwitch() && type == Constants.IEAI_IEAI)
                    {
                        ms.getActStartRecover(prjName, flowName, "", flowId);
                    }

                    /**徽商 启动工作流对告警平台发送消息*/
                    if (Environment.getInstance().getHuiShangBankSwitch() && type == Constants.IEAI_IEAI){
                        try {
                            if (!flowName.contains("日启动") &&  !flowName.contains("主流程")) {
                                String host = Environment.getInstance().getServerIP();
                                _log.info("工作流启动对接告警平台");
                                UserModel userModel = UserManager.getInstance().getStartOrEndFlowUser(flowId);
                                List<UserModel> userList = new ArrayList<UserModel>();
                                userList.add(userModel);
                                WarningInterfaceUtilsIEAI.callWarningForStartEndFlow("IEAI", "IEAI_STARTFLOW", "one", host,
                                        "工作流启动", new Date(), prjName, flowName,
                                        "",flowId,null ,null, userList);
                            }
                        } catch (UnknownHostException e) {
                            throw new RuntimeException(e);
                        }

                    }


                } catch (SQLException e)
                {
                    // 失败后数据回滚
                    DBResource.rollback(con, ServerError.ERR_DB_UPDATE, e,
                        Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                    String method = Thread.currentThread().getStackTrace()[1].getMethodName();
                    _log.error("Error when start Flow ! and flowName = " + flowName, e);
                    DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConnection(con, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log.error("Error when start Flow ! and flowName = " + flowName, ex);
            }
        }
        return flowId;

    }

    public long startFlowCMB ( UserInfo user, String prjName, String flowName, List args, Map envVars,
            WorkflowLogConfig logConfig, String instanceName, String comment, boolean forceEfficiencyPrior,
            BasicActElement starterSucElem, Date startTime, boolean isStarter, long starterFlowId, String serverip,
            int type, int prjType ) throws ServerException
    {
        long flowId = 0;
        for (int i = 0;; i++)
        {
            try
            {
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("startFlowCMB", _log, type);
                    if (startTime != null && startTime.getTime() > System.currentTimeMillis())
                    {
                        throw new ServerException(ServerError.ERR_INVALID_FLOW_START_TIME);
                    }
                    String ip = serverip;
                    if ("".equals(ip))
                    {
                        ip = getSNHostName();
                    }
                    String prjUuid = resourceManager.getLatestPrjUuid(prjName, con, prjType);
                    Project project = resourceManager.getProjectByUuid(prjUuid);
                    flowId = IdGenerator.createIdForExecAct(RepWorkflowInstance.class, con);
                    long mainScopeId = IdGenerator.createIdForExecAct(RepBranchScope.class, con);

                    WorkflowInstance flowInstance = new WorkflowInstance();

                    // init workflow infomation
                    Workflow flow = project.getWorkflowByName(flowName);

                    flowInstance.setAutoStart(flow.isAutoStartFlow());
                    flowInstance.setFlowComment(comment);
                    flowInstance.setFlowDefId(flow.getID());
                    flowInstance.setFlowDes(flow.getDescription());
                    flowInstance.setFlowId(flowId);
                    flowInstance.setFlowInsName(instanceName);
                    flowInstance.setFlowLogConfig(logConfig);
                    flowInstance.setFlowName(flowName);
                    flowInstance.setFlowPrior(flow.getPriority());
                    flowInstance.setHostName(ip);
                    flowInstance.setMainScopeId(mainScopeId);
                    flowInstance.setPrjUuid(prjUuid);
                    flowInstance.setProjectName(prjName);
                    flowInstance.setSafeFirst(true);
                    Date flowStartTime = startTime != null ? startTime : new Date();
                    flowInstance.setStartTime(flowStartTime);

                    flowInstance.setStartUser(user);
                    flowInstance.setStatus(Constants.STATE_RUNNING);

                    EngineRepositotyJdbc.getInstance().saveFowInstance(flowInstance, con);

                    resourceManager.initFlowArgsAndEnvVars(flowId, project, flow, args, envVars, con, type);

                    actCtxManager.initFlow(flow, flowInstance, type, con);

                    branchManager.startMainBranch(flowId, mainScopeId, flowInstance.isSafeFirst(), flow, starterSucElem,
                        con);

                    con.commit();
                    //拓扑节点：启动工作流  begin
                    TopoSendTriPoolThread.getInstance().addRequest(flowId, " ", 1, "0",
                            System.currentTimeMillis(), 1);
                    //拓扑节点：启动工作流  end

                    _log.info("the flow " + flowId + " is start success, The server is " + ip);
                } catch (SQLException e)
                {
                    // 失败后数据回滚
                    DBResource.rollback(con, ServerError.ERR_DB_INSERT, e,
                        Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                    String method = Thread.currentThread().getStackTrace()[1].getMethodName();
                    _log.error("Error when start Flow ! and flowName = " + flowName);
                    DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConnection(con, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                try
                {
                    DBRetryUtil.waitForNextTry(i, ex);
                } catch (RepositoryException e)
                {
                    _log.error("Error when start Flow ! and flowName = " + flowName);
                }
            }
        }
        return flowId;
    }

    /**
     * <AUTHOR>
     * @des:工作流调用活动的启动工作流
     * @datea:2010-1-25
     * @param user
     * @param prjName
     * @param flowName
     * @param args
     * @param envVars
     * @param logConfig
     * @param instanceName
     * @param comment
     * @param forceEfficiencyPrior
     * @param starterSucElem
     * @param startTime
     * @param isStarter
     * @param starterFlowId
     * @param con
     * @return
     * @throws ServerException
     * @throws RepositoryException 
     */
    public long startFlow ( UserInfo user, String prjName, String flowName, List args, Map envVars,
            WorkflowLogConfig logConfig, String instanceName, String comment, boolean forceEfficiencyPrior,
            BasicActElement starterSucElem, Date startTime, boolean isStarter, long starterFlowId, Connection con,
            int type, int prjType ) throws ServerException, RepositoryException
    {
        long flowId = 0;
        String system = "";
        try
        {
            WorkFlowManager manager = WorkFlowManager.getInstance();
            if (startTime != null && startTime.getTime() > System.currentTimeMillis())
            {
                throw new ServerException(ServerError.ERR_INVALID_FLOW_START_TIME);
            }
            String ip = "";

            List addrs = new ArrayList();

            long group = EngineRepository.getInstance().getGroupId(prjName, con, prjType);
            if (group == 0)
            {
                _log.error("group not found project is：" + prjName + " ,flow name is: " + flowName);
                throw new ServerException(ServerError.ERR_GROUP_NOT_FOUND);
            }
            // 如果是 健康巡检 类型的工程 。 取Server 用信息采集 下的服务器。
            // if (group != 13 && group >= Constants.IEAI_HEALTH_INSPECTION)
            // {
            // group = Constants.IEAI_HEALTH_INSPECTION;
            // }
            if ("ExcelActExecModelSUS".equals(prjName))
            {
                ip = EngineRepository.getInstance().loadBalanceIP(group, addrs, con);
            } else
            {
                boolean isM = manager.isMainFlow(prjName, flowName, con);
                if (isM)
                {
                    _log.info("this flow is mainFlow ,flow name is:" + flowName + ",project is :" + prjName);
                    ip = EngineRepository.getInstance().loadBalanceIPforMainFlow(group, addrs, con);

                } else
                {
                    ip = EngineRepository.getInstance().loadBalanceIP(group, addrs, con);
                }
            }

            if ("" == ip)
            {
                if (SystemConfig.isHeartbeat())
                {
                    _log.error("ip not found group is：" + group + " ,flow name is: " + flowName);
                    throw new ServerException(ServerError.ERR_SERVER_IP_NOT_FOUND);
                } else
                    ip = getSNHostName();
            }
            if (null != instanceName && !"".equals(instanceName))
            {
                int listWorkFlows = 0;
                if (ServerEnv.getServerEnv().getDateRepeat())
                {
                    listWorkFlows = EngineRepository.getInstance().getFlowInstanceStateNoConnection(prjName, flowName,
                        instanceName, con);
                }
                if (listWorkFlows > 0)
                {
                    _log.error("同数据日期的工作流已经启动，请确认！ projectname：" + prjName + " ,flow name is: " + flowName);
                    throw new ServerException(ServerError.ERR_NOT_START_CALLWORKFLOW);
                }
            }

            system = manager.getSystem(prjName, flowName, con);
            OffsetFreqBean dayStartBean = manager.getMainProjectName(prjName, flowName, con);
            OffsetFreqBean startBean = manager.getOffsetFreq(dayStartBean.getProjectName(), dayStartBean.getFlowName(),
                con);
            if (SystemConfig.isOther())
            {
                _log.info("call startFlow is getOffsetFreq  prjName: " + prjName + " flowName: " + flowName
                        + " offset: " + startBean.getOffset() + " freq: " + startBean.getFreq() + " getProjectName: "
                        + dayStartBean.getProjectName() + " getFlowName: " + dayStartBean.getFlowName());
            }

            String prjUuid = resourceManager.getLatestPrjUuid(prjName, con, prjType);
            Project project = resourceManager.getProjectByUuid(prjUuid);
            flowId = IdGenerator.createIdForAll(RepWorkflowInstance.class, type);
            long mainScopeId = IdGenerator.createIdForAll(RepBranchScope.class, type);

            // about flow log
            if (logConfig == null || logConfig.isEmpty())
            {
                logConfig = AppLogManager.getInstance().getFlowLogConfig(prjName, flowName, con);
            }

            WorkflowInstance flowInstance = new WorkflowInstance();

            // init workflow infomation
            Workflow flow = project.getWorkflowByName(flowName);

            Long prjUpperId = ProjectManager.getInstance().getPrjUpperIdByPrjName(prjName, type);
            Long prjLatestId = ProjectManager.getInstance().getPrjLatestIdByPrjName(prjName, type);
            Long flowUpperId = ProjectManager.getInstance().getFlowUpperIdByFlowName(flowName, prjUpperId, type);
            Long flowLastId = ProjectManager.getInstance().getFlowLatestIdByFlowName(flowName, prjUpperId, type);

            flowInstance.setAutoStart(flow.isAutoStartFlow());
            flowInstance.setFlowComment(comment);
            flowInstance.setFlowDefId(flow.getID());
            flowInstance.setFlowDes(flow.getDescription());
            flowInstance.setFlowId(flowId);
            flowInstance.setFlowLogConfig(logConfig);
            long port = 0;
            if (instanceName != null && !"".equals(instanceName))
            {
                String[] agentip = instanceName.split(":");
                if (agentip.length == 2)
                {
                    try
                    {
                        port = Long.parseLong(agentip[1]);
                        instanceName = agentip[0];
                    } catch (Exception e)
                    {
                        _log.info("this flow isn't patchSend");
                    }
                }
            }
            flowInstance.setFlowInsName(instanceName);

            flowInstance.setFlowName(flowName);
            flowInstance.setPatchPort(port);
            flowInstance.setFlowPrior(flow.getPriority());
            flowInstance.setHostName(ip);
            flowInstance.setMainScopeId(mainScopeId);
            flowInstance.setPrjUuid(prjUuid);
            flowInstance.setProjectName(prjName);
            flowInstance.setSafeFirst(true);

            flowInstance.setPrjUpperId(prjUpperId);
            flowInstance.setPrjId(prjLatestId);
            flowInstance.setFlowUpperId(flowUpperId);
            flowInstance.setFlowOwnId(flowLastId);

            Date flowStartTime = startTime != null ? startTime : new Date();
            flowInstance.setStartTime(flowStartTime);

            flowInstance.setStartUser(user);
            flowInstance.setStatus(Constants.STATE_RUNNING);
            flowInstance.set_system(system);

            if (ServerEnv.getInstance().getBaseCheckTimeSwitch() && Constants.STARTINSPECT_FLOWNAME.equals(flowName)
                    && type == Constants.IEAI_HEALTH_INSPECTION)
            {
                EngineRepositotyJdbc.getInstance().saveHCBaseTime(instanceName, con);// 巡检流程的实例名就是设备的IP地址
            }
            EngineRepositotyJdbc.getInstance().saveFowInstance(flowInstance, con);

            resourceManager.initFlowArgsAndEnvVars(flowId, project, flow, args, envVars, con, type);

            actCtxManager.initFlow(flow, flowInstance, type, con);

            if (starterSucElem == null)
            {
                EngineRepositotyJdbc.getInstance().saveFlowisStartMiddle(flowId, 0, 0, con);
            } else
            {
                EngineRepositotyJdbc.getInstance().saveFlowisStartMiddle(flowId, 0, 1, con);
            }
            // start workflow use branch manager

            /**此处开关为工作流运行历史开关，此处暂时不用 add by txl20180528 **/
            String historySwitch = ServerEnv.getServerEnv().getSysConfig("HISTORY_SWITCH",
                String.valueOf(ServerEnv.HISTORY_SWITCH_DEFAULT));
            if ("true".equals(historySwitch))
            {
                new StartAndStopFlowDealer().startFlowAdditional(flowInstance.getPrjUuid(), flowInstance.getFlowDefId(),
                    flowInstance.getFlowId(), type);
            }

            branchManager.startMainBranch(flowId, mainScopeId, flowInstance.isSafeFirst(), flow, starterSucElem, con,
                type);
            EngineRepositotyJdbc.getInstance().saveMainFlowACTPRENUM(flowId, instanceName, prjName, flowName,
                starterSucElem, con);

            //拓扑节点：启动工作流 begin
            TopoSendTriPoolThread.getInstance().addRequest(flowId, " ", 1, "0",
                    System.currentTimeMillis(), 1);
            //拓扑节点：启动工作流 end

            _log.info("the flow " + flowId + " is start successfull, the server is " + ip);
        } catch (RepositoryException e)
        {
            _log.error("Error when start Flow ! and flowName = " + flowName);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        }
        return flowId;

    }

    private String getIPForRun ( String prjName, Connection con, int prjType )
            throws RepositoryException, ServerException
    {
        String ip = "";
        List addrs = new ArrayList();
        long group = EngineRepository.getInstance().getGroupId(prjName, con, prjType);
        // if (group == Constants.IEAI_DAILY_OPERATIONS || group ==
        // Constants.IEAI_HEALTH_INSPECTION)
        // {
        // group = Constants.IEAI_INFOCOLLECTION;
        // }
        if (group == 0)
        {
            _log.error("group not found project is：" + prjName);
            throw new ServerException(ServerError.ERR_GROUP_NOT_FOUND);
        }
        ip = EngineRepository.getInstance().loadBalanceIP(group, addrs, con);

        if ("" == ip)
        {
            if (SystemConfig.isHeartbeat())
            {
                _log.error("ip not found group is：" + group);
                throw new ServerException(ServerError.ERR_SERVER_IP_NOT_FOUND);
            } else
            {
                ip = getSNHostName();
            }
        }
        return ip;
    }

    private WorkflowInstance createWorkFlowInstance ( Project project, Workflow flow, String comment, long flowId,
            String instanceName, String ip, long mainScopeId, String prjUuid )
    {
        WorkflowInstance flowInstance = new WorkflowInstance();

        // init workflow infomation

        long prjUpperId = 0;
        long prjLastId = 0;
        long flowUpperId = 0;
        long flowLastId = 0;
        String system = "";
        try
        {
            //POC引擎提速
            if(!Environment.getInstance().getEnginePocSwitch()) {
                prjUpperId = ProjectManager.getInstance().getPrjUpperIdByPrjName(project.getName(), Constants.IEAI_IEAI);
                prjLastId = ProjectManager.getInstance().getPrjLastIdByPrjName(project.getName());
                flowUpperId = ProjectManager.getInstance().getFlowUpperIdByFlowName(project.getName(), prjUpperId,
                        Constants.IEAI_IEAI);
                flowLastId = ProjectManager.getInstance().getFlowLastIdByFlowName(project.getName(), prjUpperId);
            }
            system = WorkFlowManager.getInstance().getSystem(project.getName(), flow.getName());
            if (StringUtils.isEmpty(system))
            {
                system = project.getSysname();
            }
        } catch (RepositoryException e)
        {
            _log.error("createWorkFlowInstance  is error" + e.getMessage());
        }

        flowInstance.setAutoStart(flow.isAutoStartFlow());
        flowInstance.setFlowComment(comment);
        flowInstance.setFlowDefId(flow.getID());
        flowInstance.setFlowDes(flow.getDescription());
        flowInstance.setFlowId(flowId);
        flowInstance.setFlowInsName(instanceName);

        flowInstance.setFlowName(flow.getName());
        flowInstance.setFlowPrior(flow.getPriority());

        flowInstance.setHostName(ip);
        flowInstance.setMainScopeId(mainScopeId);
        flowInstance.setPrjUuid(prjUuid);
        flowInstance.setProjectName(project.getName());
        flowInstance.setSafeFirst(true);
        flowInstance.setStartTime(new Date());
        try
        {
            UserInfo userInfo = IEAIRuntime.current().getUser();
            flowInstance.setStartUser(userInfo);
        }catch (NullPointerException e){
            _log.error("IEAIRuntime.getUser  is null ");
        }

        //如果开启相同会计日期重复启动开关就将工作流置成准备状态
        if (PersonalityEnv.isDateRepeatSwitchDefault()&&project.getProjectTpye()==1.0&&StringUtils.isNotBlank(instanceName))
        {
            flowInstance.setStatus(Constants.STATE_READY_START);
        }else{
            flowInstance.setStatus(Constants.STATE_RUNNING);
        }

        flowInstance.setPrjUpperId(prjUpperId);
        flowInstance.setPrjId(prjLastId);
        flowInstance.setFlowUpperId(flowUpperId);
        flowInstance.setFlowOwnId(flowLastId);
        flowInstance.set_system(system);
        return flowInstance;
    }

    public long startFlows ( String prjName, String flowName, List args, Map envVars, String instanceName,
            String comment, boolean forceEfficiencyPrior, boolean isStarter, long starterFlowId, Connection con,
            int type, int prjType, Project project, String prjUuid, Workflow flow )
            throws ServerException, RepositoryException
    {
        long flowId = 0;
        long mainScopeId =0;
        try
        {
            long start = System.currentTimeMillis();
            //POC引擎提速
            if(Environment.getInstance().getEnginePocSwitch())
            {
                flowId = IdUtil.getSnowflakeNextId();;
                mainScopeId = IdUtil.getSnowflakeNextId();;
            }else{
                flowId = IdGenerator.createIdFor(RepWorkflowInstance.class.getName(), con);
                mainScopeId = IdGenerator.createIdFor(RepBranchScope.class.getName(), con);
            }

            _log.info("创建实例主键和域主键的耗时：" + (System.currentTimeMillis() - start)+" ; flowid : "+flowId);
            start = System.currentTimeMillis();
            String ip = getIPForRun(prjName, con, prjType);
            _log.info("获取负载IP的耗时：" + (System.currentTimeMillis() - start)+" ; flowid : "+flowId);
            start = System.currentTimeMillis();
            WorkflowInstance flowInstance = this.createWorkFlowInstance(project, flow, comment, flowId, instanceName,
                ip, mainScopeId, prjUuid);
            //POC增加作业试跑功能
            boolean isTest = false;
            if(envVars!=null && envVars.get("isTest")!=null){
                try{
                    isTest = (boolean) envVars.get("isTest");
                } catch (Exception e) {
                    _log.error("isTest is not boolean type",e);
                }
            }
            if(isTest){
                flowInstance.setIsCheck(1);
            }else{
                flowInstance.setIsCheck(0);
            }
            _log.info("创建工作流实例的耗时：" + (System.currentTimeMillis() - start)+" ; flowid : "+flowId);
            start = System.currentTimeMillis();
            EngineRepositotyJdbc.getInstance().saveFowInstance(flowInstance, con);
            _log.info("保存工作流实例的耗时：" + (System.currentTimeMillis() - start)+" ; flowid : "+flowId);
            start = System.currentTimeMillis();
            //POC引擎提速
            if(!Environment.getInstance().getEnginePocSwitch())
            {
                resourceManager.initFlowArgsAndEnvVars(flowId, project, flow, args, envVars, con, type);
            }else{
                _log.info("初始化流程参数和变量：; flowid : "+flowId);
            }

            _log.info("初始化流程参数和变量的耗时：" + (System.currentTimeMillis() - start)+" ; flowid : "+flowId);
            start = System.currentTimeMillis();
            actCtxManager.initFlow(flow, flowInstance, type, con);
            _log.info("初始化流程的耗时：" + (System.currentTimeMillis() - start)+" ; flowid : "+flowId);
            start = System.currentTimeMillis();
            branchManager.startMainBranch(flowId, mainScopeId, flowInstance.isSafeFirst(), flow, null, con, type);
            _log.info("startMainBranch的耗时：" + (System.currentTimeMillis() - start)+" ; flowid : "+flowId);
            _log.info("the flow " + flowId + " is start successfull, the server is " + ip);

            if(!Environment.getInstance().getEnginePocSwitch())
            {
                //拓扑节点：启动工作流  begin
                TopoSendTriPoolThread.getInstance().addRequest(flowId, " ", 1, "0",
                        System.currentTimeMillis(), 1);
                //拓扑节点：启动工作流  end

                MonitorSystem ms = MonitorSystem.getInstance();
                if (ServerEnv.getInstance().isCheckActRecoverSwitch() && type == Constants.IEAI_IEAI)
                {
                    ms.getActStartRecover(prjName, flowName, "", flowId);
                }
            }

        } catch (Exception e)
        {
            _log.error("Error when start Flow ! and flowName = " + flowName);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        }
        return flowId;

    }

    /**   
     * @Title: startFlows   
     * @Description: 工作流调用使用   
     * @param prjName
     * @param flowName
     * @param args
     * @param envVars
     * @param instanceName
     * @param comment
     * @param forceEfficiencyPrior
     * @param isStarter
     * @param starterFlowId
     * @param con
     * @param type
     * @param prjType
     * @return
     * @throws ServerException      
     * @author: xinglin_tian 
     * @throws RepositoryException 
     * @date:   2018年7月12日 下午1:57:51   
     */
    public long startFlows ( String prjName, String flowName, List args, Map envVars, String instanceName,
            String comment, boolean forceEfficiencyPrior, boolean isStarter, long starterFlowId, Connection con,
            int type, int prjType ,Connection conForId) throws ServerException, RepositoryException
    {
        long flowId = 0;
        try
        {
            flowId = IdGenerator.createIdForAll(RepWorkflowInstance.class, type);
            Boolean inQueue=QueuedWorkflowProcessor.queuedStartFlows(flowId,prjName,  flowName,  args,  envVars,  instanceName,
                    comment,  forceEfficiencyPrior,  isStarter,  starterFlowId,
                    type,  prjType  );
            if(!inQueue){
                startFlowsSubProcess(flowId,prjName,  flowName,  args,  envVars,  instanceName,
                        comment,  forceEfficiencyPrior,  isStarter,  starterFlowId,  con,
                        type,  prjType , conForId);
            }
        } catch (RepositoryException e)
        {
            _log.error("Error when start Flow ! and flowName = " + flowName);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        }
        return flowId;

    }

	public void startFlowsSubProcess(Long flowId, String prjName, String flowName, List args, Map envVars,
			String instanceName, String comment, boolean forceEfficiencyPrior, boolean isStarter, long starterFlowId,
			Connection con, int type, int prjType, Connection conForId) throws ServerException, RepositoryException {
		long mainScopeId = IdGenerator.createIdForAll(RepBranchScope.class, type);
		String ip = getIPForRun(prjName, con, prjType);
		String prjUuid = resourceManager.getLatestPrjUuid(prjName, con, prjType);
		Project project = resourceManager.getProjectByUuid(prjUuid);
		Workflow flow = project.getWorkflowByName(flowName);
		WorkflowInstance flowInstance = this.createWorkFlowInstance(project, flow, comment, flowId, instanceName, ip,
				mainScopeId, prjUuid);

		// add by zhaolicheng for 判断实例名是否为空，条件成立的话判断工作流是否已经启动 2013.12.27
		int listWorkFlows = 0;
		if (null != instanceName && !"".equals(instanceName) && prjType == 1) {
			if (PersonalityEnv.isDateRepeatSwitchDefault()) {
				listWorkFlows = EngineRepository.getInstance().getFlowInstanceStateNoConnection(prjName, flowName,
						instanceName, con);
			}
			if (listWorkFlows > 0) {
				_log.error("相同实例名称的工作流已经启动，请确认！ projectname：" + prjName + " ,flow name is: " + flowName);
				throw new ServerException(ServerError.ERR_NOT_START_CALLWORKFLOW);
			}
		}

		// 工作流调用启动工作流时，被调用的工作流在相同工程名、相同工作流名时不能重复启动
		/*
		 * if (PersonalityEnv.isWorkflowRepeatSwitchDefault()) { listWorkFlows =
		 * EngineRepository.getInstance().getWorkFlowStateNoConnection(prjName,
		 * flowName, con); if (listWorkFlows > 0) {
		 * _log.error("相同工作流名称的工作流已经启动，请确认！ projectname：" + prjName + " ,flow name is: "
		 * + flowName); throw new
		 * ServerException(ServerError.ERR_NOT_START_CALLWORKFLOW); } }
		 */

		EngineRepositotyJdbc.getInstance().saveFowInstance(flowInstance, con);
		resourceManager.initFlowArgsAndEnvVars(flowId, project, flow, args, envVars, con, conForId);
		actCtxManager.initFlow(flow, flowInstance, type, con, conForId);
		branchManager.startMainBranch(flowId, mainScopeId, flowInstance.isSafeFirst(), flow, null, con, conForId);
		EngineRepositotyJdbc.getInstance().saveFlowisStartMiddle(flowId, 0, 0, con);
		EngineRepositotyJdbc.getInstance().saveMainFlowACTPRENUM(flowId, instanceName, prjName, flowName, null, con,
				conForId);

		try {
			_log.info("工作流调用 存流程表  ");
			new ProjectManagerWS().synFlowXml(String.valueOf(flowId), flowName);
		} catch (Exception e) {
			_log.info("ProjectManagerWS   --  synFlowXml have a   error  ");
		}

		MonitorSystem ms = MonitorSystem.getInstance();
		if (ServerEnv.getInstance().isCheckActRecoverSwitch() && type == Constants.IEAI_IEAI_BASIC) {
			ms.getActStartRecover(prjName, flowName, "", flowId);
		}

		
		// 拓扑节点：启动工作流 begin
		TopoSendTriPoolThread.getInstance().addRequest(flowId, " ", 1, "0", System.currentTimeMillis(), 1);
		// 拓扑节点：启动工作流 end

		_log.info("the flow " + flowId + " is start successfull, the server is " + ip);
	}
    /**
     * <b>It is a heavy method,if don't need to get task number of this flow ,use
     * getFlowInfoWithoutTaskCount instead.</b> <br/>
     * Given a flow id, get its information. in ieai_flowresult table. and task number of this
     * flowresult <br>
     * caller can set CheckDB to false to prevent from DB checking.
     * 
     * 
     * @param
     * @return the flow or null if nothing found
     * @throws ServerException
     */
    public WorkflowInfo getFlowInfo ( long flowId, int type ) throws ServerException
    {
        try
        {
            return EngineRepository.getInstance().getFlowInfo(flowId, type);
        } catch (RepositoryException ex)
        {
            _log.error(ex.getMessage(), ex);
            throw new ServerException(ex.getServerError());
        }
    }

    /**
     * Given a flow id, get its information. in ieai_flowresult table.
     * 
     * @param flowId
     * @return null if nothing found
     * @throws ServerException
     */
    public WorkflowInfo getFlowInfoWithoutTaskCount ( long flowId, int type ) throws ServerException
    {
        try
        {
            WorkflowInstance instance = EngineRepository.getInstance().getFlowInstance(flowId, type);
            if (instance != null)
            {
                return instance.getWorkflowInfo();
            } else
            {
                return null;
            }
        } catch (RepositoryException ex)
        {
            _log.error(ex.getMessage(), ex);
            throw new ServerException(ex.getServerError());
        }
    }

    public List queryFlow ( final WorkflowFilter flowFilter, int type ) throws ServerException
    {
        try
        {
            return EngineRepository.getInstance().queryFlow(flowFilter, type);
        } catch (RepositoryException ex)
        {
            _log.error(ex.getMessage(), ex);
            throw new ServerException(ex.getServerError().getErrCode());
        }

    }

    public void killFlow ( UserInfo user, long flowId, int type ) throws ServerException
    {
        String method = "killFlow";
        // chunyu_jing 当用户强行终止工作流时向日志打印用户
        try
        {
            _log.info("info user " + user.getFullName() + " start to stop workflow and flowid =" + flowId);
        } catch (Exception e)
        {
            _log.info("info start to stop workflow and flowid=" + flowId);
        }
        //拓扑图节点：终止工作流 begin
        TopoSendTriPoolThread.getInstance().addRequest(flowId, "", 1, "4",
                System.currentTimeMillis(), 1);
        //拓扑图节点：终止工作流 end
        List acts = null;
        try
        {
            Workflow flow = Engine.getInstance().getWorkfow(flowId, type);
            acts = EngineRepository.getInstance().getPendingOrRunningExecActs(flowId, type);
            for (int i = 0; i < acts.size(); i++)
            {
                ExecAct act = (ExecAct) acts.get(i);
                BasicActElement actElem = flow.getActivity(act.getActId());
                // kaijia_ma 添加由于终止活动时的异常导致工作流出错

                Connection con = null;
                try
                {
                    IActivity activity = Engine.getInstance().getResourceManager().getActivityWithCache(flowId, actElem,
                        type);
                    if (actElem.isRemoteExec() && activity.getClass().getName().indexOf("ShellCmdAct") != -1)
                    {
                        ActivityRExecHelper.stopShellCmdProcess(act.getRexecRequestId(), user, type);
                    }
                    if (actElem instanceof ActivityElement) {
                        // 判断锦州的接口，因为使用旧的agent，无法清理agent组中的线程数，在终止工作流的时候手动-1
                        String actTypeName = "";
                        if (null != ((ActivityElement) actElem).getActDef())
                        {
                            actTypeName = ((ActivityElement) actElem).getActDef().getAdaptorName();
                        }
                        String reqId = act.getRexecRequestId();
                        if (checkJzActType(actTypeName))
                        {
                            AgentBean agent = getAgentInfoByReqid((ActivityElement) actElem, reqId, 0);
                            con = DBManager.getInstance().getJdbcConnection(type);
                            EngineRepositotyJdbc.getInstance().saveAgentRunActNumDel(reqId, agent.getRemoteHost(),
                                    agent.getRemotePort(), "1", con);
                            con.commit();
                        }
                    }
                } catch (Exception e)
                {
                    _log.info("error when stop shell", e);
                }finally {
                    DBResource.closeConnection(con,method,_log);
                }
            }
        } catch (Exception e)
        {
            _log.info("error when stop shell");
        }
        synchronized (stoppingFlows)
        {
            Iterator iter = stoppingFlows.iterator();
            while (iter.hasNext())
            {
                StoppingFlow stoppingFlow = (StoppingFlow) iter.next();
                if (stoppingFlow.getFlowId() == flowId)
                {
                    Iterator iterNeedKillScope = stoppingFlow.getActiveScopes().iterator();
                    while (iterNeedKillScope.hasNext())
                    {
                        Long scopeId = (Long) iterNeedKillScope.next();
                        scheduler.stopScope(true, true, scopeId.longValue());
                    }
                    TaskManager.getInstance().flowStop(new Long(flowId));
                    // _checkStoppingFlowsThread will clean the useless
                    // StoppingFlow instance.
                    return;
                }
            }
        }

        WorkflowInstance flowInstance = getFlowInstance(flowId, type);
        if (flowInstance != null)
        {
            Engine.getInstance().updateFlowState(flowId, Constants.STATE_KILLED, type);
            try
            {
                //调整终止时调用设置pending方法，从queryPendingExecActForCallfolw 调整成queryPendingExecActForCallflow
                EngineRepository.getInstance().queryPendingExecActForCallflowType(flowId,type);
                //原终止时调用的设置pending逻辑
//                List execact = EngineRepository.getInstance().queryPendingExecActForCallfolw(flowId, type);
//                if (!execact.isEmpty())
//                {
//                    ExecAct exec = (ExecAct) execact.get(0);
//                    exec.setExcepted(false);
//                    Engine.getInstance().getScheduler().updateExecActAndMakeItPending(exec, type);
//                }
            } catch (Exception ee)
            {
                throw new ServerException(ServerError.ERR_DB_UPDATE);
            }
            _log.info("info stop workflow of branchManager and flowid=" + flowId);
        }
        TaskManager.getInstance().flowStop(flowId);
        _log.info("info stop workflow of taskManager and flowid=" + flowId);
        actCtxManager.flowStop(flowId);
        _log.info("info stop workflow successfully and flowid=" + flowId);

        String[] flowInfo = new String[1];
        flowInfo[0] = String.valueOf(flowId);
        addClearFlowsThread = new AddClearFlowInfo(flowInfo, type);
        addClearFlowsThread.start();
    }

	/**
     * <li>Description:检查是否是锦州的新接口</li> 
     * <AUTHOR>
     * 2019年6月7日 
     * @param actTypeName
     * @return
     * return Boolean
     */
    private static Boolean checkJzActType ( String actTypeName )
    {
        return "loaddata".contentEquals(actTypeName) || "dbproadaptor".contentEquals(actTypeName)
                || "unloaddata".contentEquals(actTypeName) || "ftp".contentEquals(actTypeName)
                || "loaddatanewadpter".contentEquals(actTypeName) || "dbpronewadaptor".contentEquals(actTypeName)
                || "unloadnewdataadaptor".contentEquals(actTypeName) || "ftpnewadaptor".contentEquals(actTypeName);
    }

    private static AgentBean getAgentInfoByReqid ( ActivityElement actElem, String reqId, int sslMode )
            throws AgentCommunicationException
    {
        String[] nodeInfo = new String[2];
        try
        {
            nodeInfo = EngineRepositotyJdbc.getInstance().getAgentInfoOfRequest(reqId);
        } catch (RepositoryException e)
        {
            _log.debug(e);
        }
        if (null == nodeInfo[0] || "".equals(nodeInfo[0]) || null == nodeInfo[1] || "".equals(nodeInfo[1]))
        {
            agentExp(actElem);
        }
        AgentBean ret = new AgentBean();
        ret.setRemoteHost(nodeInfo[0]);
        ret.setRemotePort(Integer.parseInt(nodeInfo[1]));
        ret.setSslMode(sslMode);
        return ret;
    }

    private static void agentExp ( ActivityElement actElem ) throws AgentCommunicationException
    {
        AgentCommunicationException agentExp = new AgentCommunicationException(new Exception());
        agentExp.setAgentPort(String.valueOf(actElem.getRemotePort()));
        agentExp.setAgentHost(actElem.getRemoteHost());
        agentExp.setConnectionType(actElem.getConnectType());
        agentExp.setActivityName(actElem.getName());
        agentExp.setLocation(ActivityException.AGENT_COMMUNICATION_EXCEPTION);
        agentExp.setMessage("Communication Agent Error!");
        agentExp.setTime(new Date());
        throw agentExp;
    }
	
    public void stopFlow ( UserInfo user, long flowId, int type ) throws ServerException
    {
        Engine.getInstance().updateFlowState(flowId, Constants.STATE_STOPPING, type);
        WorkflowInstance flowInstance = getFlowInstance(flowId, type);

        if (flowInstance != null)
        {
            branchManager.stopFlow(flowId, flowInstance.getMainScopeId(), flowInstance.isSafeFirst(), false, type);
            TaskManager.getInstance().flowStop(flowId);
            actCtxManager.flowStop(flowId);
        }

    }

    public void pauseFlow ( UserInfo user, long flowId, int type ) throws ServerException
    {
        // chunyu_jing 用户暂停工作流时日志打印记录
        try
        {
            _log.info("info user " + user.getFullName() + " pause workflow and flowid =" + flowId);
        } catch (Exception e)
        {
            _log.info("info the workflow is paused and flowid =" + flowId, e);
        }
        branchManager.pauseFlow(flowId, type);
    }

    public void resumeFlow ( UserInfo user, long flowId, int type ) throws ServerException
    {
        // chunyu_jing 用户恢复工作流时打印日志
        try
        {
            _log.info("info user " + user.getFullName() + " resume workflow and flowid =" + flowId);
        } catch (Exception e)
        {
            _log.info("info the workflow is resumed and flowid =" + flowId, e);
        }
        branchManager.resumeFlow(flowId, type);

    }

    public List getCurRunningAct ( long flowId, int type ) throws ServerException
    {
        return actCtxManager.getCurrentRunningActs(flowId, type);
    }

    public List getWaitingAct ( long flowId ) throws ServerException
    {
        return actCtxManager.getWaitingActs(flowId);
    }

    public List getFinishedActs ( long flowId, int type ) throws ServerException
    {
        return actCtxManager.getFinishedActs(flowId, type);
    }

    public Workflow getWorkfow ( final long flowId, int type ) throws ServerException
    {
        return resourceManager.getWorkflow(flowId, type);
    }

    public Workflow getWorkfowCopy ( final ExecAct execAct ) throws ServerException
    {
        return resourceManager.getWorkflowCopy(execAct);
    }

    public Workflow getWorkfow_KL ( final long flowId, int dbType ) throws ServerException
    {
        return resourceManager.getWorkflow_KL(flowId, dbType);
    }

    public Workflow getWorkfowByOneFlow ( final long flowId, int dbType ) throws ServerException, RepositoryException
    {
        return resourceManager.getWorkflowByOneFlow(flowId, dbType);
    }

    /**
     * 获取作业调度启动预览(用)
     * @param prjName
     * @param flowName
     * @return
     * @throws ServerException
     */
    public Workflow getWorkfowByOneFlow ( final String prjName, final String flowName ) throws ServerException
    {
        return resourceManager.getWorkflow(prjName, flowName);
    }
    /**
     * <AUTHOR>
     * @des:获得工作流信息
     * @datea:2010-1-28
     * @param
     * @param uuid
     * @return
     * @throws ServerException
     */
    public Workflow getWorkfow ( int flowDefid, String uuid ) throws ServerException
    {
        return resourceManager.getWorkflow(uuid, flowDefid);
    }

    public Workflow getWorkfowByProjectid ( final long flowId, int type ) throws ServerException
    {
        return resourceManager.getWorkflowByProjectid(flowId, type);
    }

    public void reLoadWorkflow ( final long flowId, int type ) throws ServerException
    {
        resourceManager.reLoadWorkflow(flowId, type);
    }

    /**
     * <AUTHOR>
     * @des:重新加载工作流
     * @datea:2010-1-28
     * @param
     * @param uuid
     * @throws ServerException
     */
    public void reLoadWorkflow ( String uuid ) throws ServerException
    {
        resourceManager.removeWorkflow(uuid);
    }

    public void setParamValue ( long flowId, String name, Object value, int type ) throws ServerException
    {
        resourceManager.setParamValue(flowId, name, value, type);
    }

    /**
     * @param flowId
     * @return
     * @throws ServerException
     */
    public List getAllMonitorActCfg ( long flowId, int type ) throws ServerException
    {
        return actCtxManager.getAllMonitorActCfg(flowId, type);
    }

    public int getNumRunningFlows () throws ServerException
    {
        try
        {
            // 修改为jdbc方式 tao_ding on 2010-1-13
            return EngineRepositotyJdbc.getInstance().getNumRunningFlows();
        } catch (RepositoryException ex)
        {
            throw new ServerException(ex.getServerError().getErrCode());
        }
    }

    public void saveFlowOutput ( long flowId, Map actOutput )
    {
        try
        {
            EngineRepository.getInstance().saveFlowOutput(flowId, actOutput);
        } catch (Exception ex)
        {
            _log.error("save flow output error. flowId:" + flowId, ex);
        }
    }

    public void log ( long flowId, int type, UserInfo paramUser, String actName, String expMsg, int dbtype )
    {
        WorkflowInstance flowIns = null;
        try
        {
            flowIns = getFlowInstance(flowId, dbtype);
        } catch (Exception ex)
        {
            _log.error("log error", ex);
            return;
        }

        UserInfo userInfo = paramUser;
        if (userInfo == null)
        {
            userInfo = flowIns.getStartUser();
        }

        WorkflowLogConfig logConfig = getFlowLogConfig(flowIns);
        if (logConfig == null || !logConfig.contains(type))
        {
            return;
        }
        ApplicationLog appLog = new ApplicationLog();
        appLog.setActName(actName);
        appLog.setFlowId(flowId);
        appLog.setFlowName(flowIns.getFlowName());
        appLog.setFlowInsName(flowIns.getFlowInsName());
        appLog.setPrjName(flowIns.getProjectName());

        appLog.setUser(userInfo);
        appLog.setTime(new Date());
        appLog.setType(type);
        appLog.setLogDetail(expMsg);
        InstanceRegister.getRegisterInstance().getAppLogManager().log(appLog, dbtype);
    }

    private WorkflowLogConfig getFlowLogConfig ( WorkflowInstance flowIns )
    {
        if (null == flowIns.getFlowLogConfig())
        {
            try
            {
                return AppLogManager.getInstance().getFlowLogConfig(flowIns.getProjectName(), flowIns.getFlowName());
            } catch (RepositoryException e)
            {
                _log.error("Can't get applicate log by project name:" + flowIns.getProjectName() + "flow name:"
                        + flowIns.getFlowName(),
                    e);
                return null;
            }
        } else
        {
            return flowIns.getFlowLogConfig();
        }
    }

    public Map getEnvVarValues ( long flowId ) throws ServerException
    {
        return resourceManager.getEnvVars(flowId);
    }

    public Map getParamValues ( long flowId, int type ) throws ServerException
    {
        return resourceManager.getFlowParameters(flowId, type);
    }

    public void setEnvVarsValue ( long flowId, String name, Object value ) throws ServerException
    {
        resourceManager.setEnvValue(flowId, name, value);
    }

    public void setParamsValue ( long flowId, String name, Object value, int type ) throws ServerException
    {
        resourceManager.setParamValue(flowId, name, value, type);
    }

    public Map getFlowResult ( long flowId ) throws ServerException
    {
        try
        {
            return EngineRepository.getInstance().getFlowOutput(flowId);
        } catch (UnMarshallingException ex)
        {
            throw new ServerException(ServerError.ERR_UNMARSHAL);
        } catch (RepositoryException ex)
        {
            throw new ServerException(ex.getServerError().getErrCode());
        }
    }

    public void initAllTaskToTaskSummary ( long flowId, int sysType ) throws ServerException
    {
        try
        {
            List taskDatas = UserTaskRepository.getInstance().getTasksOfFlow(flowId, sysType);
            if (taskDatas == null || taskDatas.isEmpty())
            {
                return;
            }
            List taskIds = new ArrayList();
            for (Iterator it = taskDatas.iterator(); it.hasNext();)
            {
                TaskData taskData = (TaskData) it.next();
                taskIds.add(new Long(taskData.getId()));
            }
            List actIds = EngineRepository.getInstance().getAllRecoveryActIds(taskDatas, sysType);
            TaskManager.getInstance().initRecoveryTaskSummary(flowId, taskIds, taskDatas, actIds);
        } catch (RepositoryException ex)
        {
            throw new ServerException(ex.getServerError().getErrCode());
        }
    }

    /**
     * Load safety-first flow information from database. Those are not safety first flows will be
     * skipped
     * 
     * @param userId
     * @param flowIds
     * @return
     * @throws ServerException
     */
    public List getAllActInfoFromSafetyFirstFlows ( final Long userId, final List flowIds, PermissionSet permissions,
            boolean isAll, int type ) throws ServerException
    {
        return actCtxManager.getAllActInfoFromSafetyFirstFlows(userId, flowIds, permissions, isAll, type);
    }

    /**
     * <AUTHOR>
     * @des:获得主机的IP
     * @datea:2010-1-22
     * @return
     */
    public String getSNHostName ()
    {
        String ip = null;
        try
        {
            ip = Environment.getInstance().getServerIP();
        } catch (Exception e)
        {
            _log.error(e);
        }
        return ip;
    }

    /**
     * <li>Description:启动合规管理所需要的线程</li>
     * 
     * <AUTHOR> Oct 29, 2012 return void
     * @throws ServerException
     */
    public static synchronized void startCmThread () throws ServerException
    {
        Engine.inst = new Engine();
        Engine.inst.checkDBConnectionThread.start();
        _log.info("Check DBConnection Thread initialization succeeded!");
    }

    public void updateServerType () throws RepositoryException, UnknownHostException
    {
        Connection con = null;
        String serverIp = Environment.getInstance().getServerIP();
        String serverType = Environment.getInstance().getServerType();
        PreparedStatement ps = null;

        for (int i = 0;; i++)
        {
            try
            {
                con = DBResource.getConnection(Thread.currentThread().getStackTrace()[1].getMethodName(), _log,
                    Constants.IEAI_IEAI_BASIC);
                try
                {
                    String sql = "DELETE FROM IEAI_GROUP_SERVER WHERE ISERVERID =(SELECT ID FROM IEAI_SERVERLIST  WHERE IP=?)";
                    ps = con.prepareStatement(sql);
                    ps.setString(1, serverIp);
                    ps.executeUpdate();
                    ps.close();
                    String sql1 = "INSERT INTO IEAI_GROUP_SERVER(IGROUPSERVERID, IGROUPID, ISERVERID) VALUES(?, ?, (SELECT ID FROM IEAI_SERVERLIST T1 WHERE T1.IP=?))";
                    if (!serverType.equals(""))
                    {
                        String[] severT = serverType.split(",");
                        ps = con.prepareStatement(sql1);
                        for (int j = 0; j < severT.length; j++)
                        {
                            long iid = IdGenerator.createId("ieai_group_server", con);
                            ps.setLong(1, iid);
                            ps.setLong(2, Long.valueOf(severT[j]));
                            ps.setString(3, serverIp);
                            ps.executeUpdate();
                        }
                    }
                    con.commit();
                } catch (SQLException e)
                {
                    DBResource.rollback(con, ServerError.ERR_DB_INSERT, e,
                        Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                } finally
                {
                    DBResource.closePSConn(con, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ex, ServerError.ERR_DB_INSERT);
            }
        }
    }

    /**
     * 
     * @Title: checkThreadStart
     * @Description: 判断线程是否启动
     * @param inThread 线程
     * @param inname 线程开关名
     * @param defaultBoolean 默认开关布尔值字符串
     * @return void 返回类型
     * @throws @变更记录 2016年7月14日 yunpeng_zhang
     */
    public static void checkThreadStart ( Thread inThread, String inname, String defaultBoolean )
    {
        boolean stratFlag = false;
        try
        {
            stratFlag = Boolean.parseBoolean(Environment.getInstance().getSysConfig(inname, defaultBoolean));
        } catch (Exception e)
        {
            stratFlag = false;
            _log.error(inname + " flag error!");
        }
        if (stratFlag)
        {
            inThread.start();
        }
    }

    /**
     * <li>Description:</li>
     * 
     * <AUTHOR> 2016年4月8日
     * @return return Thread
     */
    public Thread get_CalculateChartDataThread ()
    {
        return calculateChartDataThread;
    }

    public ConcurrentHashMap map = new ConcurrentHashMap();

    /**
     * 
     * @ClassName:  SendRomteMainThread   
     * @Description:远程发送线程一级处理类   
     * @author: licheng_zhao 
     * @date:   2017年11月23日 上午8:31:27   
     *     
     * @Copyright: 2017-2027 www.idealinfo.com Inc. All rights reserved. 
     *
     */
    private class SendRomteMainThread extends Thread
    {
        public SendRomteMainThread()
        {
            super("SendRomteMainThread");
        }

        @Override
        public void run ()
        {

            String groupid = "";
            while (true)
            {
                try
                {
                    try
                    {
                        if (ServerEnv.getInstance().getJobSchedulingQuerySystemSwitch())
                        {
                            Thread.sleep(5);
                        } else
                        {
                            Thread.sleep(SystemConfig.getSleepTime() * 100);
                        }

                    } catch (InterruptedException e)
                    {
                        Thread.currentThread().interrupt();
                    }
                    List<RepSendRemote> list = null;
                    try
                    {
                        list = EngineRepositotyJdbc.getInstance().getSendRemoteProject();
                    } catch (Exception e)
                    {
                        _log.error("error when get listOfSendRemote!");
                    }
                    SendMonitorService sendMonitorService = SendMonitorService.getInstance();
                    while (!list.isEmpty())
                    {
                        if (list.isEmpty())
                        {
                            break;
                        }
                        try
                        {
                            RepSendRemote bean = list.remove(0);
                            groupid = String.valueOf(map.get(bean.getIagentGroupId()));
                            if (!"groupid".equals(groupid))
                            {
                                (new SendRomteActThread(bean.getIagentGroupId())).start();
                                _log.info("SendRomteMainThread groupid：" + bean.getIagentGroupId() + " start success");
                                map.put(bean.getIagentGroupId(), "groupid");
                                SendMonitor.getInstance().getMapAgentGroupValue().put(bean.getIagentGroupId(),
                                    bean.getIagentGroupId());
                                sendMonitorService.addInfo(bean.getIagentGroupId(), bean.getIagentGroupName());
                                _log.info("SendRomteMainThread groupid：" + bean.getIagentGroupId() + " end success");
                            }
                        } catch (Exception ex)
                        {
                            _log.error("Error when SendRomteMainThread and mes:" + ex.getMessage());
                        }
                    }
                } catch (Exception tt)
                {
                    _log.error("SendRomteMainThread error and next! ErrorInfo:" + tt.getMessage());
                }
            }
        }
    }

    /**
     * 
     * @ClassName:  SendRomteActThread   
     * @Description:远程发送线程二级处理类   
     * @author: licheng_zhao 
     * @date:   2017年11月23日 上午8:30:43   
     *     
     * @Copyright: 2017-2027 www.idealinfo.com Inc. All rights reserved. 
     *
     */
    private class SendRomteActThread extends Thread
    {
        long groupid = 0l;
        long actTime = 0;

        public SendRomteActThread(long gid)
        {
            super("SendRomteActThread " + gid);
            this.groupid = gid;
        }

        @Override
        public void run ()
        {
            boolean runflag = true;
            while (runflag)
            {
                try
                {
                    try
                    {
                        if (ServerEnv.getInstance().getJobSchedulingQuerySystemSwitch())
                        {
                            Thread.sleep(5);
                        } else
                        {
                            Thread.sleep(SystemConfig.getSleepTime() * 2);
                        }
                        if (!remoteTime.isEmpty())
                        {
                            try
                            {
                                String flagStr = remoteTime.get(groupid + "," + this.getId());
                                if (!StringUtil.isEmptyStr(flagStr) && flagStr.split(",").length > 1)
                                {
                                    runflag = Boolean.parseBoolean(flagStr.split(",")[1]);
                                }
                            } catch (Exception e)
                            {
                                _log.error("SendRomteActThread check remoteTime error!", e);
                                runflag = true;
                            }
                        }
                        // 线程卡死，线程退出
                        if (!runflag)
                        {
                            _log.error("this thread stop ,thread id:" + this.getId() + ",group id is " + groupid
                                    + ",value is " + remoteTime.get(groupid + "," + this.getId()));
                            // 移除标记map
                            remoteTime.remove(Long.toString(groupid) + "," + Long.toString(this.getId()));
                            break;
                        }
                    } catch (InterruptedException e)
                    {
                        Thread.currentThread().interrupt();
                    }
                    ConcurrentHashMap<String, List> listmap = null;
                    long a = System.currentTimeMillis();
                    // 写入二级发送线程时间
                    remoteTime.put(Long.toString(groupid) + "," + Long.toString(this.getId()), String.valueOf(a));
                    try
                    {
                        listmap = EngineRepositotyJdbc.getInstance().getSendRemote(groupid);
                        sendWarnOrNotNew(listmap,groupid);
//                      //agent组没有agent产生告警 变为有agent时告警恢复开关
//                        boolean flag=ServerEnv.getInstance().getBooleanConfig("bh.agentgroup.noagent.warn.recoverwarn.swith", false);
//                        if(flag) {
//                          //sendWarnOrNot(listmap,groupid);
//                            ActivityRExecHelper.sendWarnOrNot(groupid);
//                        }
                        //ActivityRExecHelper.sendWarnOrNotNew(groupid);
                        
                    } catch (Exception e)
                    {
                        _log.error("error when get listOfSendRemote!");
                    }
                    String otherValue = "";
                    List<RepSendRemote> list = listmap.get("sendRomote");
                    List<SendRomoteAgent> listRomoteAgent = listmap.get("agentFree");
                    List proTypeList = listmap.get("protype");
                    long proType = (Long) proTypeList.get(0);
                    String groupName = (String) proTypeList.get(1);
                    if (list.isEmpty())
                    {
                        if (ServerEnv.getInstance().getJobSchedulingQuerySystemSwitch())
                        {
                            Thread.sleep(5);
                        } else
                        {
                            Thread.sleep(5000);
                        }
                    }
                    while (!listRomoteAgent.isEmpty())
                    {
                        if (list.isEmpty())
                        {
                            break;
                        }
                        try
                        {
                            actTime = System.currentTimeMillis();
                            int threadNum = list.size();
                            CountDownLatch threadSignal = new CountDownLatch(threadNum);
                            ExecutorService executor = Executors.newFixedThreadPool(threadNum);
                            String agentIp = "";
                            int agentPort = 0;
                            _log.info("start create third sendremoteThead:"+threadNum+"; group id :"+groupid);
                            for (int i = 0; i < threadNum; i++)
                            {
                                if (!listRomoteAgent.isEmpty())
                                {
                                    SendRomoteAgent sendRomoteAgent = listRomoteAgent.get(0);
                                    if (sendRomoteAgent.getAgentFreeNum() <= 0)
                                    {
                                        agentIp = "";
                                        agentPort = 0;
                                    } else
                                    {
                                        agentIp = sendRomoteAgent.getIagentIp();
                                        agentPort = sendRomoteAgent.getIagentPort();
                                        sendRomoteAgent.setAgentFreeNum(sendRomoteAgent.getAgentFreeNum() - 1);
                                    }
                                }
                                long singleTime = System.currentTimeMillis();
                                Runnable task = new SendRomoteActThread(threadSignal, list.get(i), groupid, a, agentIp,
                                        agentPort, proType, groupName);
                                executor.submit(task);
                                BigDecimal totalFee = new BigDecimal((System.currentTimeMillis()) - singleTime);
                                BigDecimal d100 = new BigDecimal(1000);
                                BigDecimal fee = totalFee.divide(d100, 2, 2);// 小数点2位
                                otherValue += String.valueOf(fee) + ",";
                            }
                            threadSignal.await();
                            executor.shutdown();
                            BigDecimal totalFee = new BigDecimal((System.currentTimeMillis()) - actTime);
                            BigDecimal d100 = new BigDecimal(1000);
                            BigDecimal fee = totalFee.divide(d100, 2, 2);// 小数点2位
                            otherValue += String.valueOf(fee) + ",";
                            SendMonitor.getInstance().getMapAgentGroupOtherValue().put(groupid, otherValue);
                            break;
                        } catch (Exception ex)
                        {
                            _log.error("Error when sendReomteAct and mes:" + ex.getMessage());
                        }
                    }
                    listmap.clear();
                } catch (Exception tt)
                {
                    _log.error("sendReomteAct error and next! GroupId:" + groupid + " ErrorInfo:" + tt.getMessage());
                }
            }
        }
        
//        private  void sendWarnOrNot(ConcurrentHashMap<String, List> listmap,long agentGroupid) {
//            
//            
//            Connection con = null;
//            try
//            {
//                con = DBResource.getConnection("sendWarnOrNot", _log, Constants.IEAI_IEAI);
//                //ActivityRExecHelper activityRExecHelper=new ActivityRExecHelper();
//                List list=listmap.get("agentFree");
//              //agent组有agent时判断是否产生恢复告警
//                boolean agentGroupHaveWarnFlag=ActivityRExecHelper.isagentGroupHaveWarn(agentGroupid,con);
//                if(list==null || list.isEmpty()) {
//                   if(!agentGroupHaveWarnFlag) {
//                     //agent组没有agent时且未产生过告警或告警已恢复时 告警
//                       ActivityRExecHelper.warnAgentGroupNoAgent(agentGroupid);  
//                   }
//                  
//                }else {
//                  
//                    if(agentGroupHaveWarnFlag) {
//                        //恢复告警
//                        ActivityRExecHelper.warnAgentGroupNoAgentToHave(agentGroupid);  
//                    }
//                }
//            }catch(Exception e) {
//                _log.error("sendWarnOrNot is error ",e);
//            }finally {
//                DBResource.closeConnection(con, "sendWarnOrNot", _log);
//            }
//        }
        
        
        private  void sendWarnOrNotNew(ConcurrentHashMap<String, List> listmap,long agentGroupid) {
            
            
            try
            {
                
                String warnFlag=ServerEnv.getInstance().getSysConfig("agentgroup.warn.recoverwarn", "not.warn");
//                if("not.warn".equals(warnFlag)) {
//                    return;
//                }else 
                if("warn.no.agent".equals(warnFlag)) {
                    sendWarnGroupNoAgent(agentGroupid);
                }else if("warn.no.useful.agent".equals(warnFlag)) {
                    sendWarnGroupNoUsefulAgent(listmap, agentGroupid);
                }
              
            }catch(Exception e) {
                _log.error("sendWarnOrNotNew is error ",e);
            }
        }
        
        private  void sendWarnGroupNoAgent(long agentGroupid) {
            
            
            Connection con = null;
            try
            {
                con = DBResource.getConnection("sendWarnGroupNoAgent", _log, Constants.IEAI_IEAI);
                boolean isGroupHaveAgebtFlag=ActivityRExecHelper.isagentGroupHaveAgent(agentGroupid,con);
              
              //agent组 是否已有告警且未恢复
                boolean agentGroupHaveWarnFlag=isagentGroupHaveWarn(agentGroupid,con);
                if(isGroupHaveAgebtFlag) {
                    if(agentGroupHaveWarnFlag) {
                        //恢复告警
                        ActivityRExecHelper.warnAgentGroupNoAgentToHave(agentGroupid);  
                    }
                  
                }else {
                    if(!agentGroupHaveWarnFlag) {
                        //agent组没有agent时且未产生过告警或告警已恢复时 告警
                          ActivityRExecHelper.warnAgentGroupNoAgent(agentGroupid);  
                      }
                  
                   
                }
            }catch(Exception e) {
                _log.error("sendWarnOrNot is error ",e);
            }finally {
                DBResource.closeConnection(con, "sendWarnOrNot", _log);
            }
        }
        
        private  void sendWarnGroupNoUsefulAgent(ConcurrentHashMap<String, List> listmap,long agentGroupid) {
            
            
            Connection con = null;
            try
            {
                con = DBResource.getConnection("sendWarnGroupNoUsefulAgent", _log, Constants.IEAI_IEAI);
                List list=listmap.get("agentFree");
              //agent组 是否已有告警且未恢复
                boolean agentGroupHaveWarnFlag=isagentGroupHaveWarn(agentGroupid,con);
                if(list==null || list.isEmpty()) {
                   if(!agentGroupHaveWarnFlag) {
                     //agent组没有可用agent时且未产生过告警或告警已恢复时 告警
                       ActivityRExecHelper.warnAgentGroupNoAgent(agentGroupid);  
                   }
                  
                }else {
                  
                    if(agentGroupHaveWarnFlag) {
                        //恢复告警
                        ActivityRExecHelper.warnAgentGroupNoAgentToHave(agentGroupid);  
                    }
                }
            }catch(Exception e) {
                _log.error("sendWarnOrNot is error ",e);
            }finally {
                DBResource.closeConnection(con, "sendWarnOrNot", _log);
            }
        }
        
        public  boolean isagentGroupHaveWarn (Long groupId,Connection conn)
        {

            PreparedStatement ps = null;
            ResultSet rset = null;
            boolean flag = false;
            try
            {
                
                String sql = " select imodulecode,itypecode from IEAI_WARN where iwarnid= (select max(iwarnid) from IEAI_WARN where iip= ? and imodulecode='platform' )   " ;
                ps = conn.prepareStatement(sql);
                ps.setString(1, groupId+"");
                rset = ps.executeQuery();

                while (rset.next())
                {                    
                    if("agentnotconnect".equals(rset.getString("itypecode"))) {
                        flag= true;
                    }
                    return flag;
                }
            } catch (Exception e)
            {
                _log.error("Engine.isagentGroupHaveWarn is error",e);
            }finally
            {
                DBResource.closePSRS(rset, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            }
            return flag;
        }
    }

    /**
     * 
     * @ClassName:  SendRomoteActThread   
     * @Description:远程发送三级线程处理类   
     * @author: licheng_zhao 
     * @date:   2017年11月23日 上午8:30:03   
     *     
     * @Copyright: 2017-2027 www.idealinfo.com Inc. All rights reserved. 
     *
     */
    private class SendRomoteActThread implements Runnable
    {
        private CountDownLatch threadsSignal;
        private RepSendRemote  bean;
        private long           groupid;
        private long           a;
        private String         agentIp;
        private int            agentPort;
        private long           proType;
        private String         groupName;

        public SendRomoteActThread(CountDownLatch threadsSignal, RepSendRemote bean, long groupid, long a,
                String agentIp, int agentPort, long proType, String groupName)
        {
            this.threadsSignal = threadsSignal;
            this.bean = bean;
            this.groupid = groupid;
            this.a = a;
            this.agentIp = agentIp;
            this.agentPort = agentPort;
            this.proType = proType;
            this.groupName = groupName;
        }

        @Override
        public void run ()
        {
            try
            {
                ExecAct execAct = new ExecAct();
                execAct.setFlowId(bean.getIflowId());
                execAct.setActName(bean.getIactName());
                try
                {
                    try
                    {
                        boolean exceptionControl = ServerEnv.getServerEnv().exceptionControl();

                        if (exceptionControl)
                        {
                            boolean sendflag = EngineRepositotyJdbc.getInstance()
                                    .getSendActNameState(bean.getIflowId());
                            if (sendflag)
                            {
                                boolean flag = EngineRepositotyJdbc.getInstance().getActState(bean.getIflowId(),
                                    bean.getIactName());
                                // _log.info("SENDREMORE:线程ID:" + groupid + " 判断该活动是否可以发送时间："
                                // + (System.currentTimeMillis() - a));
                                if (flag)
                                {
                                    // _log.info("SENDREMORE:sendRemoteThread exec flowid:" +
                                    // bean.getIflowId()
                                    // + " actName:" + bean.getIactName() + " groupid: " + groupid);
                                    int ret = ActivityRExecHelper.exec(execAct, bean.getIid(), bean.getIagentGroupId(),
                                        agentIp, agentPort, proType, groupName);
                                    // _log.info("sendRemoteThread SEND-END flowid:" +
                                    // bean.getIflowId() + " actName:"
                                    // + bean.getIactName());
                                    if (ret != Constants.QUEUEUP)
                                    {
                                        EngineRepositotyJdbc.getInstance().sendOverDate(bean);
                                    }
                                } else
                                {
                                    EngineRepositotyJdbc.getInstance().sendOverSendRemote(bean);
                                }
                            }
                        } else
                        {
                            boolean flag = EngineRepositotyJdbc.getInstance().getActState(bean.getIflowId(),
                                bean.getIactName());
                            // _log.info(
                            // "SENDREMORE:线程ID:" + groupid + " 判断该活动是否可以发送时间：" +
                            // (System.currentTimeMillis() - a));
                            if (flag)
                            {
                                // _log.info("SENDREMORE:sendRemoteThread exec flowid:" +
                                // bean.getIflowId() + " actName:"
                                // + bean.getIactName() + " groupid: " + groupid);
                                long aa = System.currentTimeMillis();
                                int ret = ActivityRExecHelper.exec(execAct, bean.getIid(), bean.getIagentGroupId(),
                                    agentIp, agentPort, proType, groupName);
                                // _log.info("sendRemoteThread SEND-END flowid:" + bean.getIflowId()
                                // + " actName:"
                                // + bean.getIactName());
                                long bb = System.currentTimeMillis();
                                if (ret != Constants.QUEUEUP)
                                {
                                    EngineRepositotyJdbc.getInstance().sendOverDate(bean);
                                }
                                // _log.info("SENDREMORE:sendRemoteThread END flowid:" +
                                // bean.getIflowId() + " actName:"
                                // + bean.getIactName() + " groupid: " + groupid);
                                // _log.info("SENDREMORE:线程ID:" + groupid + " 活动发送时间：" + (bb - aa) +
                                // " flowid:"
                                // + bean.getIflowId() + " actName:" + bean.getIactName());
                                // _log.info(
                                // "SENDREMORE:线程ID:" + groupid + " 发送后数据保存时间：" +
                                // (System.currentTimeMillis() - bb)
                                // + " flowid:" + bean.getIflowId() + " actName:" +
                                // bean.getIactName());
                            } else
                            {
                                EngineRepositotyJdbc.getInstance().sendOverSendRemote(bean);
                            }
                        }
                    } catch (Exception ee)
                    {
                        _log.error("check error :" + ee + " groupid: " + groupid);
                        throw new ActivityException(ee.getMessage());
                    }
                } catch (ActivityException e)
                {
                    _log.error("Error when saveData and mes:" + e.getMessage() + " and flowid:" + bean.getIflowId()
                            + " and requestid:" + bean.getIid() + " groupid: " + groupid);

                    EngineRepositotyJdbc.getInstance().delSendActExce(bean.getIflowId(), bean.getIid());

                    List execact = EngineRepository.getInstance().queryPendingExecActForException(bean.getIflowId(),
                        bean.getIexecActId());
                    execAct = (ExecAct) execact.get(0);
                    Workflow flow = Engine.getInstance().getWorkfow(execAct.getFlowId(), Constants.IEAI_IEAI);
                    BasicActElement actElem = flow.getActivity(execAct.getActId());
                    IExecContext execCtx = null;

                    execCtx = new DBExecContext(execAct.getFlowId(), execAct.getScopeId());
                    execCtx.setCurActElem(actElem);

                    _log.info("flowId: " + execAct.getFlowId() + " actName:" + execAct.getActName());
                    // Create and Save the error infomation of the last exception.
                    ExecError lastErr = new ExecError(e, execAct);
                    EngineRepository.getInstance().saveOrUpdateLastError(lastErr);

                    // Update the state of the activity. Mark it fail.
                    ActivityContextManager actCtxMgr = Engine.getInstance().getActCtxManager();
                    actCtxMgr.setLatestState(execAct.getFlowId(), execAct.getActId(), Constants.ACT_STATE_FAIL,
                        Constants.IEAI_IEAI);
                    execAct.setExcepted(true);

                    // Get Act output and save exception infomation in the output of
                    // act.
                    IExceptionHandingHelper exceptionHelper = new ExceptionHandingHelper();
                    exceptionHelper.saveExceptionOutput(execAct.getScopeId(), actElem.getActID(), e);

                    // Save the Execption in the state-data of the activity.
                    // And Update the infomation of ExecAct.
                    ActStateData actStateData = null;
                    if (null == execAct.getActStateData())
                    {
                        actStateData = new ActStateData();
                        actStateData.setObjectData(e);
                    } else
                    {
                        actStateData = execAct.getActStateData();
                        actStateData.setObjectData(e);
                    }
                    execAct.setActStateData(actStateData);
                    execAct.setState(ExecAct.PENDING);

                    int flowState = Engine.getInstance().getFlowInstance(execAct.getFlowId(), Constants.IEAI_IEAI)
                            .getStatus();
                    if (execAct.isSafeFirst() && !execAct.isInTransaction() && flowState == Constants.STATE_RECOVERING)
                    {
                        RuntimeEnv.setExecContext(execCtx);
                        Engine.getInstance().updateFlowState(execAct.getFlowId(), Constants.STATE_RUNNING,
                            Constants.IEAI_IEAI);
                    } else if (execAct.isSafeFirst() && !execAct.isInTransaction())
                    {
                        if (execAct.isForRecovery() && e instanceof AgentCommunicationException)
                        {
                            execAct.setForRecovery(false);
                        }
                        EngineRepository.getInstance().updateExecAct(execAct);
                    } else
                    {
                        RuntimeEnv.setExecContext(execCtx);
                    }
                }
            } catch (Exception ex)
            {
                _log.error("Error when many sendReomteAct and mes:" + ex + " groupid: " + groupid);
            } finally
            {
                threadsSignal.countDown();
                threadsSignal = null;
                bean = null;
                groupid = -1;
                a = -1;
                agentIp = null;
                agentPort = -1;
                proType = -1;
                groupName = null;
            }
        }
    }

    /**   
     * @ClassName:  CheckSendRemote   
     * @Description:添加二级线程监控内部类   
     * @author: txl 
     * @date:   2019年11月30日 上午11:00:25   
     *     
     * @Copyright: 2019-2027 www.idealinfo.com Inc. All rights reserved. 
     * 
     */
    private class CheckSendRemote extends Thread
    {
        public CheckSendRemote()
        {
            super("Check CheckSendRemote Thread.");
        }

        @Override
        public void run ()
        {
            while (true)
            {
                try
                {
                    String sendRemote = null;
                    long time = 0;
                    if (!remoteTime.isEmpty())
                    {
                        int min = 2;
                        _log.info("check sendRemoteActThead!map size is " + remoteTime.size());
                        for (Iterator<String> iter = remoteTime.keySet().iterator(); iter.hasNext();)
                        {
                            sendRemote = iter.next();
                            String value = remoteTime.get(sendRemote);
                            if (StringUtil.isEmptyStr(value))
                            {
                                continue;
                            }
                            if (value.split(",").length > 1)
                            {
                                time = Long.parseLong(remoteTime.get(sendRemote).split(",")[0]);
                            } else
                            {
                                time = Long.parseLong(remoteTime.get(sendRemote));
                            }
                            _log.info("check sendRemoteActThead!key is " + remoteTime + ",time is " + time);
                            if (((System.currentTimeMillis() - time) > min * 60 * 1000) && (time > 0))
                            {
                                // 判断线程是否已经打过卡死标记，防止短信重发
                                if (value.split(",").length < 2)
                                {
                                    // 重启线程
                                    map.remove(Long.parseLong(sendRemote.split(",")[0]));
                                    _log.info("remove SendRemoteActThread success!id is " + sendRemote);
                                }
                                _log.info("sendRemoteActThead upadte time over " + min + " min!key is " + remoteTime
                                        + ",time is " + time);
                                remoteTime.put(sendRemote, time + ",false");
                                _log.info("set SendRemoteActThread restart flag false,key is " + sendRemote);
                            }
                        }
                    }
                    Thread.sleep(60 * 1000);
                } catch (Throwable ex)
                {
                    _log.error("CheckSendRemote error!", ex);
                }
            }
        }
    }

    public Map<String, String> getRemoteTime ()
    {
        return remoteTime;
    }

    /**
     * 
     * @Title: startSendRemoteActThread   
     * @Description: 启动远程活动发送线程         
     * @author: licheng_zhao
     * @date:   2017年11月23日 上午8:28:52
     */
    public void startSendRemoteActThread ()
    {
        try
        {
            _serverIp = Environment.getInstance().getServerIP();
        } catch (UnknownHostException e)
        {
            _log.error(e);
        }
        boolean flag = false;
        Connection con = null;
        try
        {
            try
            {
                con = DBResource.getRConnection("startSendRemoteActThread", _log, Constants.IEAI_IEAI);
                _log.info("Check _safeSchedulerODSThread Thread initialization start succeeded!");
                if (!Scheduler.getInstance()._safeSchedulerODSThread.isAlive())
                {
                    Scheduler.getInstance()._safeSchedulerODSThread.start();
                }
                _log.info("Check _safeSchedulerODSThread Thread initialization end succeeded!");
                flag = EngineRepositotyJdbc.getInstance().isMainServer(_serverIp, Constants.MAIN_SERVER,
                    Constants.IEAI_IEAI, Constants.IEAI_GROUPTYPE, con);
                if (flag)
                {
                    QuartzMainServerThread.getInstance().start();
                    if(!dataCollectAutoControlThread.isAlive()){
                        Engine.checkThreadStart(Engine.inst.dataCollectAutoControlThread, Environment.DATACOLLECT_AUTOSTOPTASK,
                                Environment.FALSE);
                    }

                    if (!agentConcurrentMonitorThread.isAlive())
                    {
                        Engine.inst.agentConcurrentMonitorThread.start();
                        _log.info("AgentConcurrentMonitor start succeeded!");
                    }

                    if (!sendRomteMainThread.isAlive())
                    {
                        _log.info("IP:" + _serverIp + " is MainServer and will be stateed!");
                        _log.info("Check MaintainDbSelfState Thread initialization succeeded!");
                        Engine.inst.sendRomteMainThread.start();
                        _log.info("SendRomteMainThread start succeeded!");
                        EngineRepositotyJdbc.getInstance().saveMainServer(_serverIp, Constants.MAIN_SERVER,
                            Constants.IEAI_IEAI, Constants.IEAI_GROUPTYPE, con);
                    }
                    /**迁移挂起线程轮询线程--by txl 20180211**/
                    if (!Scheduler.getInstance()._safeSchedulerODS_New1Thread.isAlive())
                    {
                        _log.info("IP:" + _serverIp + " is MainServer and will be stateed!");
                        // _log.info("Check _safeSchedulerODSThread Thread initialization start
                        // succeeded!");
                        // Scheduler.getInstance()._safeSchedulerODSThread.start();
                        // _log.info("Check _safeSchedulerODSThread Thread initialization end
                        // succeeded!");
                        //Scheduler.getInstance()._safeSchedulerODS_New1Thread.start();
                    }
                }
                con.commit();
            } catch (RepositoryException e)
            {
                _log.error("when select isMainServer and result is false. mes:" + e.getMessage());
            } finally
            {
                DBResource.closeConnection(con, "startSendRemoteActThread", _log);
            }
        } catch (Exception ee)
        {
            _log.error("startSendRemoteActThread() is error2" + ee.getMessage());
        }
        _log.info("Entegor Server has been started successfully!");
    }

    /**
     * 
     * @Title: startSendRemoteActThreadRecover   
     * @Description: 失效备援时确定是否需要启动发送线程  
     * @param serverIp      
     * @author: licheng_zhao
     * @date:   2017年11月23日 上午8:28:24
     */
    public void startSendRemoteActThreadRecover ( String serverIp, int type, int groupType )
    {
        try
        {
            _serverIp = Environment.getInstance().getServerIP();
        } catch (UnknownHostException e)
        {
            _log.error(e);
        }
        boolean flag = false;
        Connection con = null;
        try
        {
            try
            {
                con = DBResource.getRConnection("startSendRemoteActThreadRecover", _log, Constants.IEAI_IEAI_BASIC);

                flag = EngineRepositotyJdbc.getInstance().isRebootMainServer(_serverIp, serverIp, Constants.MAIN_SERVER,
                    type, groupType, con);
                if (flag)
                {

                    QuartzMainServerThread.getInstance().start();
                    if(!dataCollectAutoControlThread.isAlive()){
                        Engine.checkThreadStart(Engine.inst.dataCollectAutoControlThread, Environment.DATACOLLECT_AUTOSTOPTASK,
                                Environment.FALSE);
                    }

                    if (!agentConcurrentMonitorThread.isAlive())
                    {
                        Engine.inst.agentConcurrentMonitorThread.start();
                        _log.info("AgentConcurrentMonitor start succeeded!");
                    }

                    if (!sendRomteMainThread.isAlive())
                    {
                        _log.info("IP:" + _serverIp + " is MainServer and will be stateed!");
                        _log.info("Recover Check MaintainDbSelfState Thread initialization succeeded!");
                        Engine.inst.sendRomteMainThread.start();
                        _log.info("Recover SendRomteActThread start succeeded!");
                        EngineRepositotyJdbc.getInstance().saveMainServer(serverIp, Constants.SERVER, type, groupType,
                            con);
                        EngineRepositotyJdbc.getInstance().saveMainServer(_serverIp, Constants.MAIN_SERVER, type,
                            groupType, con);
                    }
                    if (!Scheduler.getInstance()._safeSchedulerODS_New1Thread.isAlive())
                    {
                        // _log.info("Recover _safeSchedulerODSThread start succeeded!");
                        // Scheduler.getInstance()._safeSchedulerODSThread.start();
                        // _log.info("Recover _safeSchedulerODSThread end succeeded!");
                        // Scheduler.getInstance()._safeSchedulerODS_New1Thread.start();
                    }
                }
                con.commit();
            } catch (RepositoryException e)
            {
                _log.error("when recover select isMainServer and result is false. mes:" + e.getMessage());
                throw new RepositoryException(ServerError.ERR_DB_QUERY);
            } catch (SQLException e)
            {
                _log.error("isRebootMainServer() is error2" + e.getMessage());
                throw new RepositoryException(ServerError.ERR_DB_QUERY);
            } finally
            {
                DBResource.closeConnection(con, "startSendRemoteActThreadRecover", _log);
            }
        } catch (Exception ee)
        {
            _log.error("isRebootMainServer() is error2" + ee.getMessage());
        }
    }

    public void startMonitorTreads ()
    {
        Engine.inst.monitorThreads();

        // 心跳自身数据库维护
        Engine.inst.maintainDbSelfStateThread.start();
        _log.info("MonitorThread start succeeded!");
        // 心跳自身线程维护
        Engine.inst.rotationMaintainThread.start();
        _log.info("CheckThreadPool initialization succeeded!");

        try
        {
            Thread.sleep(5000);
        } catch (InterruptedException e)
        {
            Thread.currentThread().interrupt();
        }
        // 心跳自自我检查线程
        Engine.inst.hearbeatSelfCheckThread.start();
        _log.info("HearbeatSelfCheckThread start succeeded!");
        // 心跳守候线程
        Engine.inst.heartbeatMonitorThread.start();
        _log.info("heartbeatMonitorThread start succeeded!");
    }

    /**
     * 描述： 根据工程名和工作流名查询该工作流是不是主流程
     * 
     * @param prjName
     * @param flowName
     * <AUTHOR>
     * @return
     * 2018年1月31日 manxi_zhao迁移过来
     * @throws RepositoryException 
     */
    public boolean isMainFlow ( String prjName, String flowName ) throws RepositoryException
    {
        boolean isMain = false;
        for (int i = 0;; i++)
        {
            try
            {
                Connection con = null;
                PreparedStatement ps = null;
                ResultSet rs = null;
                int runningFlowNum = 0;
                String sql = "select count(1) from IEAI_EXCELMODEL WHERE IMAINPRONAME =? AND  IMAINLINENAME=?";
                String sql1 = "select count(1) from Ieai_Excelmodel_Daystart WHERE IPRONAME =? AND  IFLOWNAME=?";
                try
                {
                    con = DBResource.getConnection("isMainFlow", _log, Constants.IEAI_IEAI);
                    ps = con.prepareStatement(sql);
                    ps.setString(1, prjName);
                    ps.setString(2, flowName);
                    rs = ps.executeQuery();
                    if (rs.next())
                    {
                        runningFlowNum = rs.getInt(1);
                    }
                    if (runningFlowNum != 0)
                    {
                        isMain = true;
                    }
                    if (!isMain)
                    {
                        ps = con.prepareStatement(sql1);
                        ps.setString(1, prjName);
                        ps.setString(2, flowName);
                        rs = ps.executeQuery();
                        if (rs.next())
                        {
                            runningFlowNum = rs.getInt(1);
                        }
                        if (runningFlowNum != 0)
                        {
                            isMain = true;
                        }
                    }
                    break;
                } catch (SQLException e)
                {
                    _log.error(e);
                    throw new RepositoryException(ServerError.ERR_DB_EXEC);
                } finally
                {
                    DBResource.closeConn(con, rs, ps, "isMainFlow", _log);
                }
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
        return isMain;
    }

    /**
     * <AUTHOR>
     * @des:导入Excelmodel数据.
     * @datea:2017-4-10
     * @param
     * @param
     * @return
     * @throws ServerException
     */
    public boolean endFlowImportExcelModel ( long flowid ) throws ServerException
    {
        boolean flag = true;

        // 创建保存工具类对象
        ProjectSaveUtilBean projectSaveUtilBean = new ProjectSaveUtilBean();
        // 获取所有可用数据源
        List<DBSourceMonitor> dbList;
        int basicType = -1;
        Connection connection = null;
        
        try
        {
            dbList = ProjectManagerForMultiple.getInstance().getDBsourceList(Constants.IEAI_IEAI_BASIC);
            for (DBSourceMonitor dBSourceMonitor : dbList)
            {
                if (dBSourceMonitor.getBasic() == 1)
                {
                    connection = DBResource.getConnection("updatePrjAdp", _log, (int) dBSourceMonitor.getGroupId());
                    basicType =(int) dBSourceMonitor.getGroupId();
                    // 保存基线库数据库连接至工具类对象
                    projectSaveUtilBean.setBasicConnection(connection);
                    projectSaveUtilBean.setBasicConnectionName(dBSourceMonitor.getDbsourceName());
                    break;
                }
            }

            long prjId = IdGenerator.createIdForExecAct(RepProject.class, projectSaveUtilBean.getBasicConnection());
            projectSaveUtilBean.setPrjId(prjId);

        } catch (RepositoryException e2)
        {
            _log.error("Engine endFlowImportExcelModel is error" + e2);
        }

        Connection con = null;
        PreparedStatement ps = null;
        PreparedStatement ps1 = null;
        PreparedStatement psForRun = null;
        PreparedStatement psForQuery = null;
        PreparedStatement psupdatebasic = null;
        PreparedStatement psForDel = null;
        PreparedStatement psForUpdate = null;
        UserInfo userinfo = new UserInfo();
        ResultSet rs = null;
        ResultSet rs1 = null;
        ResultSet rs2 = null;
        ResultSet rs3 = null;
        boolean flagBasic = false;
        int countRun = 0;
        long iid = 0;
        String prjName = "";
        String flowName = "";
        String sqlQuery = "SELECT TY.IPROJECTNAME,TY.IFLOWNAME FROM IEAI_WORKFLOWINSTANCE TY WHERE TY.IFLOWID=? ";
        String sqlRun = "SELECT COUNT(TY.IFLOWID) AS FLOWCOUNT FROM IEAI_WORKFLOWINSTANCE TY WHERE TY.IFLOWID=?  AND TY.ISTATUS IN (0,8,15, 30, 40, 46, 47)";
        String sql = "SELECT T.USERNAME,T.USERID  FROM IEAI_BASIC_EXCELMODEL T WHERE T.IMAINPRONAME=? AND T.IMAINLINENAME=? AND T.ISTATE in(0,2) FOR UPDATE";
        String sqldeleteSupdata = "DELETE FROM IEAI_SUPDATA WHERE PROJECTNAME=? AND MAINLINE=? AND IVESIONID=? ";
        String sqlupdate = "UPDATE IEAI_BASIC_EXCELMODEL T SET T.ISTATE=1 WHERE T.IID=? AND T.IMAINPRONAME=? AND T.IMAINLINENAME =?";
        String sqlupdateState = "UPDATE IEAI_BASIC_EXCELMODEL T SET T.ISTATE=1 WHERE T.ISTATE=2 AND T.IMAINPRONAME=? AND T.IMAINLINENAME =?";
        String sqlBasicVersion = "SELECT DISTINCT T.IID FROM IEAI_BASIC_EXCELMODEL T WHERE T.IMAINPRONAME=? AND T.IMAINLINENAME =? AND T.ISTATE=0 ORDER BY T.IID ASC";
        try
        {
            con = DBResource.getConnection("endFlowImportExcelModel", _log, Constants.IEAI_IEAI_BASIC);
            psForRun = con.prepareStatement(sqlRun);
            psForRun.setLong(1, flowid);
            rs = psForRun.executeQuery();
            while (rs.next())
            {
                countRun = rs.getInt("FLOWCOUNT");
            }
            if (countRun <= 0)
            {

                psForQuery = con.prepareStatement(sqlQuery);
                psForQuery.setLong(1, flowid);
                rs1 = psForQuery.executeQuery();
                while (rs1.next())
                {
                    prjName = rs1.getString("IPROJECTNAME");
                    flowName = rs1.getString("IFLOWNAME");
                }

                ps = con.prepareStatement(sql);
                ps.setString(1, prjName);
                ps.setString(2, flowName);
                rs2 = ps.executeQuery();
                while (rs2.next())
                {
                    flagBasic = true;
                    userinfo.setFullName(rs2.getString("USERNAME"));
                    userinfo.setId(rs2.getLong("USERID"));
                    break;
                }
                if (flagBasic)
                {
                    ps1 = con.prepareStatement(sqlBasicVersion);
                    psupdatebasic = con.prepareStatement(sqlupdate);
                    psForDel = con.prepareStatement(sqldeleteSupdata);
                    ps1.setString(1, prjName);
                    ps1.setString(2, flowName);
                    rs3 = ps1.executeQuery();
                    UpLoadExcelManager.getInstance().importCopyExcelModelManager(prjName, flowName, con);
                    psForUpdate = con.prepareStatement(sqlupdateState);
                    psForUpdate.setString(1, prjName);
                    psForUpdate.setString(2, flowName);
                    psForUpdate.executeUpdate();
                    Map<String, List<ActInfoBean>> mapListbean = UpLoadTempExcleManager.getInstance()
                            .selectBasicExcelmodel(prjName, flowName, con);
                    while (rs3.next())
                    {
                        iid = rs3.getLong("IID");
                        UpLoadExcelManager.getInstance().saveUpdateSupDataExcelModel(prjName, flowName, iid, con,
                            basicType);
                        flag = UpLoadExcelManager.getInstance().startWorkFlowUploadExcel(con, iid, prjName, flowName,
                            mapListbean, basicType);
                        if (!flag)
                        {
                            break;
                        }

                        psupdatebasic.setLong(1, iid);
                        psupdatebasic.setString(2, prjName);
                        psupdatebasic.setString(3, flowName);
                        psupdatebasic.executeUpdate();

                        psForDel.setString(1, prjName);
                        psForDel.setString(2, flowName);
                        psForDel.setLong(3, iid);
                        psForDel.executeUpdate();
                    }
                    if (flag)
                    {
                        flag = UpLoadExcelManager.getInstance().uploadProjectExcelEnd(con, prjName, flowName, userinfo,
                            projectSaveUtilBean, basicType);
                    }
                    if (!flag)
                    {
                        throw new RepositoryException(ServerError.ERR_DB_ERROR);
                    }
                    con.commit();
                }
            } else
            {
                _log.info("endFlowImportExcelModel 要变更的主流程正在运行，变更没有生效，将按变更之前版本运行，projectName:" + prjName + " flowName:"
                        + flowName);
            }
        } catch (RepositoryException e)
        {
            try
            {
                con.rollback();
            } catch (SQLException e1)
            {
                _log.info("importExcelModel endFlowImportExcelModel RepositoryException is rollback error flowName:"
                        + flowName,e1);
            }
            flag = false;
            _log.error("Error when endFlowImportExcelModel RepositoryException import ExcelModel ! and flowName = "
                    + flowName,e);
            throw new ServerException(ServerError.ERR_DB_ERROR);
        } catch (SQLException e)
        {
            try
            {
                con.rollback();
            } catch (SQLException e1)
            {
                _log.error(
                    "importExcelModel endFlowImportExcelModel SQLException is rollback error flowName:" + flowName,e1);
            }
            flag = false;
            _log.error(
                "Error when SQLException endFlowImportExcelModel import ExcelModel ! and flowName = " + flowName,e);
            throw new ServerException(ServerError.ERR_DB_ERROR);
        } catch (Exception e)
        {
            try
            {
                con.rollback();
            } catch (SQLException e1)
            {
                _log.info("importExcelModel endFlowImportExcelModel Throwable is rollback error flowName:" + flowName);
            }
            flag = false;
            _log.error("Error when Throwable endFlowImportExcelModel import ExcelModel ! and flowName = " + flowName
                    + " messages: " , e);
            throw new ServerException(ServerError.ERR_DB_UNKNOW);
        } finally
        {
            DBResource.closeConn(con, rs, psForRun, "endFlowImportExcelModel", _log);
            DBResource.closePreparedStatement(ps, "endFlowImportExcelModel", _log);
            DBResource.closePreparedStatement(psForQuery, "endFlowImportExcelModel", _log);
            DBResource.closePreparedStatement(psupdatebasic, "endFlowImportExcelModel", _log);
            DBResource.closePSRS(rs3, psForDel, "endFlowImportExcelModel", _log);
            DBResource.closePSRS(rs1, ps1, "endFlowImportExcelModel", _log);
            DBResource.closeResultSet(rs2, "endFlowImportExcelModel", _log);
            DBResource.closePreparedStatement(psForUpdate, "endFlowImportExcelModel", _log);
            DBResource.closeConnection(connection, "endFlowImportExcelModel", _log);
        }
        return flag;
    }

    /**   
     * @ClassName:  AddClearFlowInfo   
     * @Description:数据清理，添加flowid信息线程 ，迁移工行功能
     * @author: yue_sun 
     * @date:   2018年5月3日 上午11:49:23   
     * @Copyright: 2018-2027 www.idealinfo.com Inc. All rights reserved. 
     */
    private class AddClearFlowInfo extends Thread
    {

        String[] flowInfo;
        int      dbType;

        public AddClearFlowInfo()
        {
            super("Check AddClearFlowInfo Flow Thread.");
        }

        public AddClearFlowInfo(String[] flowID, int type)
        {
            flowInfo = flowID;
            dbType = type;
        }

        @Override
        public void run ()
        {
            setStopFlow(flowInfo, Constants.STATE_KILLED, dbType);
        }
    }

    /**   
     * @Title: setStopFlow   
     * @Description: 工作流数据清理准备，工作流ID放到清理数组中, 工行迁移
     * @param flowId
     * @param state      
     * <AUTHOR> Jul 23, 2012
     * @updateauthor: yue_sun 
     * @date:   2018年5月3日 上午11:50:40   
     */
    public void setStopFlow ( String[] flowId, int state, int dbType )
    {
        if (dbType == Constants.IEAI_IEAI)
        {
            synchronized (stoppingIEAIFlows)
            {
                for (int i = 0; i < flowId.length; i++)
                {
                    if (state == Constants.STATE_FINISHED || state == Constants.STATE_KILLED)
                    {
                        stoppingIEAIFlows.add(flowId[i] + ":" + state);
                        stoppingIEAIFlows.notify();
                    }
                }
            }
        }
    }

    // add by yuxh 20180427
    public Workflow getWorkfow ( final long flowId ) throws ServerException
    {
        return resourceManager.getWorkflow(flowId, Constants.IEAI_IEAI_BASIC);
    }

    /**
     * <ul>
     * <li>Title:</li>
     * <li>Description:业务异常自动重试线程</li>
     * <li>Copyright: Copyright 2003</li>
     * <li>Company: ideal</li>
     * </ul>
     * 
     * <AUTHOR> move by txl
     * 
     * @date
     */
    private class ActivityRedo_Thread extends Thread
    {
        private boolean                flag           = false;
        private long                   lSleepTime     = 6000;
        private List<ActivityRedoBean> redoBean       = new ArrayList<ActivityRedoBean>();
        private String                 taskID         = null;
        ActivityReDoManager            actReDoManager = new ActivityReDoManager();

        @Override
        public void run ()
        {
            // 1.把符合重试条件的任务 逐一驱动一遍
            // 2.任务执行完成后，看改任务 是否已经不再满足重试的条件了，更新这个这个任务。

            while (flag)
            {
                try
                {
                    Thread.sleep(lSleepTime);
                    redoBean = actReDoManager.getPreParedRedoBeans();
                    if (null != redoBean)
                    {
                        for (int i = 0; i < redoBean.size(); i++)
                        {
                            ActivityRedoBean bean = redoBean.get(i);
                            // 添加自动重试结束时间和延时报警判断
                            // 判断活动是否是重试活动，并且重试结束时间是否到达，如果到达则跳过重试。 开始 add by yuyang start
                            ActivityReDoManager actRedoManger = new ActivityReDoManager();
                            boolean isOutTime = false;
                            int hour = 0;
                            int min = 0;
                            // 判断是否配置重试结束时间，如果配置的话，看配置时间是否大于当前时间，如果大于则正常走流程，如果小于当前时间则进行后续略过或异常处理
                            if (StringUtils.isNotBlank(bean.getRetryEndTime()))
                            {
                                String[] times = bean.getRetryEndTime().split(":");
                                if (times.length == 2)
                                {
                                    hour = Integer.valueOf(times[0]);
                                    min = Integer.valueOf(times[1]);
                                    Calendar cal = Calendar.getInstance();
                                    cal.set(Calendar.HOUR_OF_DAY, hour);
                                    cal.set(Calendar.MINUTE, min);
                                    isOutTime = cal.getTimeInMillis() < (new Date()).getTime();
                                }
                                if (isOutTime)
                                {
                                    // 获取正在运行的execact 通过flowid 然后对比actid 获取某一个
                                    ExecAct _execAct = null;
                                    List acts = EngineRepository.getInstance()
                                            .getPendingOrRunningExecActs(bean.getFlowid(), Constants.IEAI_IEAI);
                                    for (int b = 0; b < acts.size(); b++)
                                    {
                                        ExecAct act = (ExecAct) acts.get(b);
                                        if (act.getActId() == bean.getActid())
                                        {
                                            _execAct = act;
                                            break;
                                        }
                                    }
                                    ActivityContextManager actCtxMgr = Engine.getInstance().getActCtxManager();
                                    Workflow flow = Engine.getInstance().getWorkfow(bean.getFlowid());
                                    BasicActElement _actElem = flow.getActivity(bean.getActid());
                                    IExecContext _execCtx = new DBExecContext(_execAct.getFlowId(),
                                            _execAct.getScopeId());
                                    _execCtx.setCurActElem(_actElem);
                                    actRedoManger.deletFinishRedoAct(bean);
                                    if ("1".equals(bean.getAutoskip()))
                                    {
                                        List sucAct = new ArrayList();
                                        ExceptionHandingHelper helper = new ExceptionHandingHelper();
                                        sucAct = helper.doCalculatNextAct(_actElem, _execAct, _execCtx);
                                        // add by tao_ding 结构结束后更新本活动结束时间以后后续活动的运行开始标志时间 2014-2-24
                                        try
                                        {
                                            EngineRepositotyJdbc.getInstance().actStructEndtimeFlagtime(sucAct,
                                                _execAct, Constants.IEAI_IEAI);
                                        } catch (RepositoryException e)
                                        {
                                            _log.error(e);
                                            throw new ServerException(ServerError.ERR_DB_UPDATE);
                                        }
                                        if (sucAct.isEmpty())
                                        {
                                            Engine.getInstance().getBranchManager().endBranch(null, _execCtx, _execAct);
                                        } else
                                        {
                                            // update by tao_ding 2014-2-24 异常活动忽略后的后续活动处理
                                            for (int a = 0; a < sucAct.size(); a++)
                                            {
                                                try
                                                {
                                                    if (EngineRepositotyJdbc.getInstance()
                                                            .checkisMainpro(_execAct.getFlowId()))
                                                    {
                                                        ExecAct sucExecAct = new ExecAct(_execAct,
                                                                (BasicActElement) sucAct.get(a));
                                                        if (_actElem instanceof ExitActElement)
                                                        {
                                                            try
                                                            {
                                                                BranchInfo branchInfo = EngineRepository.getInstance()
                                                                        .getBranchInfo(_execAct.getScopeId());
                                                                if (branchInfo != null)
                                                                {
                                                                    branchInfo.setFinish(true);
                                                                    EngineRepository.getInstance()
                                                                            .updateBranchInfo(branchInfo);
                                                                }
                                                                StructInfo structInfo = EngineRepository.getInstance()
                                                                        .getUnFinishedStructInfo(
                                                                            _execCtx.getParentContext().getScopeId());
                                                                if (structInfo != null)
                                                                {
                                                                    structInfo.setFinished(true);
                                                                    EngineRepository.getInstance()
                                                                            .updateStructInfoBasic(structInfo);
                                                                }
                                                                RuntimeEnv.setExecContext(_execCtx.getParentContext());
                                                                sucExecAct.setScopeId(
                                                                    _execCtx.getParentContext().getScopeId());
                                                                sucExecAct.setExecContext(_execCtx.getParentContext());
                                                            } catch (RepositoryException e)
                                                            {
                                                                _log.info("error while get the branch info" + e);
                                                                throw new ServerException(e.getServerError());
                                                            }
                                                        }
                                                        sucExecAct.setState(ExecAct.PENDING);
                                                        scheduler.appendExecAct(sucExecAct, _execAct);
                                                    }
                                                } catch (RepositoryException e)
                                                {
                                                    _log.error(
                                                        "EngineRepositotyJdbc.getInstance().checkisMainpro is query error!"
                                                                + e.getMessage());
                                                }

                                                actCtxMgr.setLatestState(_execAct.getFlowId(), _execAct.getActId(),
                                                    Constants.ACT_STATE_FAIL_SKIPPED, Constants.IEAI_IEAI);
                                                _log.info("ReDoInfo:FlowID=" + bean.getFlowid() + ",ActID="
                                                        + bean.getActid() + ",CurRedoTimes =" + (bean.getRedocur() + 1)
                                                        + ", but redo endtime has come , so stop redo.");
                                                _log.info(
                                                    "  It Still happen Business Exception ! But it's config autoSkipped, so act state change skipped!");
                                            }
                                        }
                                    } else
                                    {
                                        actCtxMgr.setLatestState(_execAct.getFlowId(), _execAct.getActId(),
                                            Constants.ACT_STATE_FAIL, Constants.IEAI_IEAI);
                                        _log.info("ReDoInfo:FlowID=" + bean.getFlowid() + ",ActID=" + bean.getActid()
                                                + ",CurRedoTimes = MaxRedoTimes =" + (bean.getRedocur() + 1));
                                        _log.info("  It Still happen Business Exception !,So Redo stoped!");
                                        _execAct.setExcepted(true);
                                        // update start by yan_wang 增加是否参与耗时计算标识
                                        TimeFailFlow.getInstance().updateFlowFail(_execAct.getFlowId(),
                                            Constants.IEAI_IEAI);
                                        RepActRunInfo actinfo = RepActRunInfo.newActInfo(_execAct.getFlowId(), _execCtx,
                                            _actElem);
                                        actinfo.setIsCheck(1);
                                        WorkflowManager.getInstance().saveActInfo(actinfo);
                                        // update end by yan_wang
                                        // Get Act output and save exception infomation in the
                                        // output of
                                        // act.
                                        AgentCommunicationException ex = new AgentCommunicationException();
                                        ex.setDetailMessage("Business exceptions!");
                                        IExceptionHandingHelper exceptionHelper = new ExceptionHandingHelper();
                                        exceptionHelper.saveExceptionOutput(_execAct.getScopeId(), _actElem.getActID(),
                                            ex);
                                        // Save the Execption in the state-data of the activity.
                                        // And Update the infomation of ExecAct.
                                        ActStateData actStateData = null;
                                        try
                                        {
                                            if (null == _execAct.getActStateData())
                                            {
                                                actStateData = new ActStateData();

                                                actStateData.setObjectData(ex);

                                            } else
                                            {
                                                actStateData = _execAct.getActStateData();
                                                actStateData.setObjectData(ex);
                                            }
                                        } catch (UnsupportedEncodingException e)
                                        {
                                            e.printStackTrace();
                                        }
                                        _execAct.setActStateData(actStateData);
                                        _execAct.setState(ExecAct.PENDING);
                                        // add a check condition for efficiency
                                        // 改变活动状态变为running状态在首页显示 后台不会无限轮循
                                        int flowState = Engine.getInstance()
                                                .getFlowInstance(_execAct.getFlowId(), Constants.IEAI_IEAI).getStatus();
                                        if (_execAct.isSafeFirst() && !_execAct.isInTransaction()
                                                && flowState == Constants.STATE_RECOVERING)
                                        {
                                            RuntimeEnv.setExecContext(_execCtx);
                                            Engine.getInstance().updateFlowState(_execAct.getFlowId(),
                                                Constants.STATE_RUNNING, Constants.IEAI_IEAI);
                                        } else if (_execAct.isSafeFirst() && !_execAct.isInTransaction())
                                        {
                                            if (_execAct.isForRecovery())
                                            {
                                                _execAct.setForRecovery(false);
                                            }
                                            try
                                            {
                                                EngineRepository.getInstance().updateExecAct(_execAct);
                                            } catch (RepositoryException e)
                                            {
                                                e.printStackTrace();
                                            }
                                        } else
                                        {
                                            RuntimeEnv.setExecContext(_execCtx);
                                        }
                                        // 当活动出现异常时，停止超时检测 。 update by xibin_gong 20130801
                                        if (_execAct.isExcepted())
                                        {
                                            scheduler.stopTimeGuard(_execAct.getScopeId());
                                        }
                                    }
                                    boolean isDelayWarnning = false;
                                    long configTime = 0;
                                    if (StringUtils.isNotBlank(bean.getDelayTime()))
                                    {
                                        String[] time = bean.getDelayTime().split(":");
                                        if (time.length == 2)
                                        {
                                            hour = Integer.valueOf(time[0]);
                                            min = Integer.valueOf(time[1]);
                                            Calendar cal = Calendar.getInstance();
                                            cal.set(Calendar.HOUR_OF_DAY, hour);
                                            cal.set(Calendar.MINUTE, min);
                                            configTime = cal.getTimeInMillis();
                                            isDelayWarnning = configTime > (new Date()).getTime();
                                        }
                                    }
                                    if (isDelayWarnning)
                                    {
                                        SocketWarnAnomaly warn = new SocketWarnAnomaly();
                                        Project project = ProjectAdaptorCache.getInstance()
                                                .loadProjectByUuid(_execAct.getPrjuuid());
                                        Workflow workflow = project.getWorkflowByName(_execAct.getFlowName());
                                        BasicActElement actElement = workflow.getActivityByName(_execAct.getActName());
                                        String actDesc = actElement.getDescription();
                                        String ip = null;
                                        if (actElement instanceof ActivityElement)
                                        {
                                            ActivityElement actEle = (ActivityElement) actElement;
                                            if (actEle.getRemoteAgent() == null)
                                            {
                                                ip = actEle.getRemoteHost();
                                            } else
                                            {
                                                Resource resource = project.getResourceByName(actEle.getRemoteAgent());
                                                DefaultConfig config = (DefaultConfig) resource.getConfig();
                                                ip = (String) config.get("host");
                                            }
                                        }
                                        if (StringUtils.isBlank(ip))
                                        {
                                            try
                                            {
                                                ip = InetAddress.getLocalHost().getHostAddress();
                                            } catch (UnknownHostException e)
                                            {
                                                e.printStackTrace();
                                            }
                                        }
                                        long actStartTime = 0;
                                        ActivityContextManager _actCtxManager = new ActivityContextManager();
                                        ActivityRuntimeInfoCore activityRuntime = _actCtxManager.getActivityRuntime(
                                            IEAIRuntime.current().getFlowId(), actElement.getID());
                                        if (activityRuntime != null)
                                        {
                                            Date actStartDate = activityRuntime.getBeginExcTime();
                                            if (actStartDate != null)
                                            {
                                                actStartTime = actStartDate.getTime();
                                            }
                                        }
                                        String message = Environment.FAIL_BUSINESS_MSG;
                                        String warnmsg = "综合批处理管理平台报警：" + _execAct.getProjectName() + "中"
                                                + _execAct.getFlowName() + "下的" + _execAct.getActName() + "检测到"
                                                + SocketWarnAnomaly.YWYC + "，异常信息：" + message;
                                        try
                                        {
                                            warn.addDelayWarnning(_execAct.getProjectName(), _execAct.getFlowName(),
                                                _execAct.getActName(), SocketWarnAnomaly.YWYC, ip, actDesc,
                                                actStartTime, System.currentTimeMillis(), null, warnmsg, configTime);
                                        } catch (RepositoryException e)
                                        {
                                            e.printStackTrace();
                                        }
                                    } else
                                    {
                                        /* 此处为报警发送方法，因为不确定发送方式，暂时屏蔽 */
                                        // SendSocketWarnKL.sendSocketWarnOfBusinessException(_execAct);
                                        _log.info("send warn info for Execact!" + _execAct.getProjectName() + ":"
                                                + _execAct.getFlowName() + ":" + _execAct.getActName());
                                    }
                                    continue;
                                }
                            }
                            redo(bean.getFlowid(), bean.getActid(), bean.getTaskId());
                        }
                    }

                } catch (Throwable e)
                {
                    _log.error("ActivityRedo_Thread error :", e);
                }
            }

        }

        public ActivityRedo_Thread()
        {
            super("ActivityRedoThread");
            flag = true;
        }

        /**
         * <ul>
         * <li>Title:</li>
         * <li>Description:传入失败的 taskID，然后重试这个任务。其他什么业务逻辑都不做，单纯的一个调用操作.</li>
         * <li>Copyright: Copyright 2003</li>
         * <li>Company: ideal</li>
         * </ul>
         * 
         * <AUTHOR>
         * 
         * @date
         */
        public void redo ( long flowid, long actid, String taskID )
        {
            long taskId = Long.parseLong(taskID);

            WsErrorTaskOperation operation = new WsErrorTaskOperation();
            operation.setFlowState(0);
            int handlMethod = 1;

            operation.setHandlMethod(handlMethod);
            operation.setWaitingSecond(NumberUtils.toLong(""));
            String targetActIdParamName = null;
            ExceptionMunualHandingManager _mgr = ExceptionMunualHandingManager.getInstance();
            switch (handlMethod)
            {
                case Constants.ERRORTASK_HANDLING_METHOD_GOTO:
                {
                    targetActIdParamName = "gotoActivity";
                    break;
                }
                case Constants.ERRORTASK_HANDLING_METHOD_SKIP:
                {
                    targetActIdParamName = "succeedActivity";
                    break;
                }
            }
            try
            {
                _mgr.operate(operation.toCommons(), new UserInfo(), taskId, Constants.IEAI_IEAI);
            } catch (ServerException e)
            {
                _log.error("operate fail when bussiness redo() call _mgr.operate() method");
                _log.error(e);
            }
        }

    }

    /**
     * 
     * @Title: startFlowMultiActivities   
     * @param user
     * @param prjName
     * @param flowName
     * @param args
     * @param envVars
     * @param logConfig
     * @param instanceName
     * @param comment
     * @param forceEfficiencyPrior
     * @param starterSucElemes
     * @param startTime
     * @return
     * @throws ServerException      
     * @author: junyu_zhang 
     * @date:   2018年5月18日 下午4:48:02
     */
    public long startFlowMultiActivities ( UserInfo user, String prjName, String flowName, List args, Map envVars,
            WorkflowLogConfig logConfig, String instanceName, String comment, boolean forceEfficiencyPrior,
            List<BasicActElement> starterSucElemes, Date startTime ) throws ServerException
    {
        String[] mainlineName = null;
        try
        {
            mainlineName = WorkflowQueryManager.getInstance().getProjectMainlineName(prjName, flowName);
        } catch (RepositoryException e)
        {
            _log.info("startFlow is getProjectMainlineName get error projectName:" + prjName + " flowName:" + flowName);
        }
        long starttime = System.currentTimeMillis();
        if (null != mainlineName)
        {
            try
            {
                WorkFlowManager.getInstance().importExcelModel(mainlineName[0], mainlineName[1]);
            } catch (RepositoryException e)
            {
                _log.info("startFlow is importExcelModel get error projectName:" + prjName + " flowName:" + flowName);
            }
        }
        _log.info("启动工作流导入Excelmodel总耗时为：" + ((System.currentTimeMillis()) - starttime) + "projectName:" + prjName
                + " flowName:" + flowName);
        return startFlow(user, prjName, flowName, args, envVars, logConfig, instanceName, comment, forceEfficiencyPrior,
            starterSucElemes, startTime, false, 0);
    }

    public long startFlowStudio ( UserInfo user, String prjName, String flowName, List args, Map envVars,
            WorkflowLogConfig logConfig, String instanceName, String comment, boolean forceEfficiencyPrior,
            List<BasicActElement> starterSucElemes, Date startTime, boolean isStarter, long starterFlowId ,String mflowid, String[] checkedFlowID)
            throws ServerException
    {
        long flowId = 0;
        String system = null;

        for (int i = 0;; i++)
        {
            try
            {
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("startFlow", _log, Constants.IEAI_IEAI);
                    if (startTime != null && startTime.getTime() > System.currentTimeMillis())
                    {
                        throw new ServerException(ServerError.ERR_INVALID_FLOW_START_TIME);
                    }
                    String ip = "";
                    List addrs = new ArrayList();

                    long group = EngineRepository.getInstance().getGroupId(prjName, con);
                    if (group == 0)
                    {
                        _log.error("group not found project is：" + prjName + " ,flow name is: " + flowName);
                        throw new ServerException(ServerError.ERR_GROUP_NOT_FOUND);
                    }
                    if ("ExcelActExecModelSUS".equals(prjName))
                    {
                        ip = EngineRepository.getInstance().loadBalanceIP(group, addrs, con);
                    } else
                    {
                        boolean isM = WorkflowQueryManager.getInstance().isMainFlow(prjName, flowName, con);
                        if (isM)
                        {
                            _log.info("this flow is mainFlow ,flow name is:" + flowName + ",project is :" + prjName);
                            ip = EngineRepository.getInstance().loadBalanceIP(group, addrs, con);

                        } else
                        {
                            int count = WorkFlowManager.getInstance().getMutexActCount(flowName, prjName, con);
                            if (count > 0)
                            {
                                _log.error("mutex actname is running  is：projectName" + prjName + " ,flow name is: "
                                        + flowName);
                                throw new ServerException(ServerError.ERR_MUTEX_RUUNING);
                            }
                            ip = EngineRepository.getInstance().loadBalanceIP(group, addrs, con);
                        }
                    }

                    if ("" == ip)
                    {
                        if (SystemConfig.isHeartbeat())
                        {
                            _log.error("ip not found group is：" + group + " ,flow name is: " + flowName);
                            throw new ServerException(ServerError.ERR_SERVER_IP_NOT_FOUND);
                        } else
                            ip = getSNHostName();
                    }
                    system = WorkflowQueryManager.getInstance().getSystem(prjName, flowName, con);

                    boolean isDM = WorkFlowManager.getInstance().isDayStartMainFlow(prjName, flowName, con);
                    OffsetFreqBean offsetFreqBean = null;
                    if (isDM)
                    {
                        offsetFreqBean = WorkFlowManager.getInstance().getDayStartOffsetFreq(prjName, flowName, con);
                    } else
                    {
                        OffsetFreqBean dayStartBean = WorkFlowManager.getInstance().getMainProjectName(prjName,
                                flowName, con);
                        offsetFreqBean = WorkFlowManager.getInstance().getOffsetFreq(dayStartBean.getProjectName(),
                                dayStartBean.getFlowName(), con);
                    }

                    String prjUuid = resourceManager.getLatestPrjUuid(prjName, con, Constants.IEAI_IEAI);
                    Project project = resourceManager.getProjectByUuid(prjUuid);
                    flowId = IdGenerator.createIdForExecAct(RepWorkflowInstance.class, con);
                    long mainScopeId = IdGenerator.createIdForExecAct(RepBranchScope.class, con);

                    // about flow log
                    if (logConfig == null || logConfig.isEmpty())
                    {
                        try
                        {
                            logConfig = AppLogManager.getInstance().getFlowLogConfig(prjName, flowName, con);
                        } catch (RepositoryException ex)
                        {
                            throw new ServerException(ex.getServerError().getErrCode());
                        }
                    }


                    WorkflowInstance flowInstance  = EngineRepositotyJdbc.getInstance().getFlowInstance(Long.parseLong(mflowid)).toCommonsNew();

                    flowInstance.setFlowLogConfig(logConfig);
                    flowInstance.setStartUser(user);
                    flowInstance.setFlowId(flowId);
                    flowInstance.setMainScopeId(mainScopeId);
                    flowInstance.setStatus(0);

                    // init workflow infomation
                    Workflow flow = project.getWorkflowByName(flowInstance.getFlowName());

                   /* flowInstance.setAutoStart(flow.isAutoStartFlow());
                    flowInstance.setFlowComment(comment);
                    flowInstance.setFlowDefId(flow.getID());
                    flowInstance.setFlowDes(flow.getDescription());
                    flowInstance.setFlowId(flowId);
                    flowInstance.setFlowInsName(instanceName);
                    flowInstance.setFlowLogConfig(logConfig);
                    flowInstance.setFlowName(flowName);
                    flowInstance.setFlowPrior(flow.getPriority());
                    flowInstance.setHostName(ip);
                    flowInstance.setMainScopeId(mainScopeId);
                    flowInstance.setPrjUuid(prjUuid);
                    flowInstance.setProjectName(prjName);
                    flowInstance.setSafeFirst(true);
                    Date flowStartTime = startTime != null ? startTime : new Date();
                    flowInstance.setStartTime(flowStartTime);

                    flowInstance.setStartUser(user);
                    flowInstance.setStatus(Constants.STATE_RUNNING);
                    flowInstance.set_system(system);

                    flowInstance.setOffset(offsetFreqBean.getOffset());
                    flowInstance.setFreq(offsetFreqBean.getFreq());*/

                    EngineRepositotyJdbc.getInstance().saveFowInstance(flowInstance, con);

                    resourceManager.initFlowArgsAndEnvVarsForStartFlowHere(mflowid,flowId, project, flow, args, envVars, con);

                    actCtxManager.initFlow(flow, flowInstance, Constants.IEAI_IEAI, con);
                    // start workflow use branch manager

                    if (starterSucElemes == null)
                    {
                        EngineRepositotyJdbc.getInstance().saveFlowisStartMiddle(flowId, 0, 0, con);
                    } else
                    {
                        EngineRepositotyJdbc.getInstance().saveFlowisStartMiddle(flowId, 0, 1, con);
                    }
                    branchManager.startMultiBranchNew(flowId, mainScopeId, flowInstance.isSafeFirst(), flow,
                            starterSucElemes, checkedFlowID,con);


                    con.commit();
                    _log.info("the flow " + flowId + " is start success, The server is " + ip);
                    MonitorSystem ms = MonitorSystem.getInstance();
                } catch (SQLException e)
                {
                    // 失败后数据回滚
                    DBResource.rollback(con, ServerError.ERR_DB_INSERT, e,
                            Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                    String method = Thread.currentThread().getStackTrace()[1].getMethodName();
                    _log.error("Error when start Flow ! and flowName = " + flowName, e);
                    DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConnection(con, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                try
                {
                    DBRetryUtil.waitForNextTry(i, ex);
                } catch (RepositoryException e)
                {
                    _log.error("Error when start Flow ! and flowName = " + flowName, e);
                }
            }
        }
        return flowId;
    }

    /**
     * 
     * @Title: startFlow   
     * @Description: 启动工作流  
     * @param user
     * @param prjName
     * @param flowName
     * @param args
     * @param envVars
     * @param logConfig
     * @param instanceName
     * @param comment
     * @param forceEfficiencyPrior
     * @param starterSucElemes
     * @param startTime
     * @param isStarter
     * @param starterFlowId
     * @return
     * @throws ServerException      
     * @author: junyu_zhang 
     * @date:   2018年5月24日 上午10:17:25
     */
    public long startFlow ( UserInfo user, String prjName, String flowName, List args, Map envVars,
            WorkflowLogConfig logConfig, String instanceName, String comment, boolean forceEfficiencyPrior,
            List<BasicActElement> starterSucElemes, Date startTime, boolean isStarter, long starterFlowId )
            throws ServerException
    {
        long flowId = 0;
        String system = null;

        for (int i = 0;; i++)
        {
            try
            {
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("startFlow", _log, Constants.IEAI_IEAI);
                    if (startTime != null && startTime.getTime() > System.currentTimeMillis())
                    {
                        throw new ServerException(ServerError.ERR_INVALID_FLOW_START_TIME);
                    }
                    String ip = "";
                    List addrs = new ArrayList();

                    long group = EngineRepository.getInstance().getGroupId(prjName, con);
                    if (group == 0)
                    {
                        _log.error("group not found project is：" + prjName + " ,flow name is: " + flowName);
                        throw new ServerException(ServerError.ERR_GROUP_NOT_FOUND);
                    }
                    if ("ExcelActExecModelSUS".equals(prjName))
                    {
                        ip = EngineRepository.getInstance().loadBalanceIP(group, addrs, con);
                    } else
                    {
                        boolean isM = WorkflowQueryManager.getInstance().isMainFlow(prjName, flowName, con);
                        if (isM)
                        {
                            _log.info("this flow is mainFlow ,flow name is:" + flowName + ",project is :" + prjName);
                            ip = EngineRepository.getInstance().loadBalanceIP(group, addrs, con);

                        } else
                        {
                            int count = WorkFlowManager.getInstance().getMutexActCount(flowName, prjName, con);
                            if (count > 0)
                            {
                                _log.error("mutex actname is running  is：projectName" + prjName + " ,flow name is: "
                                        + flowName);
                                throw new ServerException(ServerError.ERR_MUTEX_RUUNING);
                            }
                            ip = EngineRepository.getInstance().loadBalanceIP(group, addrs, con);
                        }
                    }

                    if ("" == ip)
                    {
                        if (SystemConfig.isHeartbeat())
                        {
                            _log.error("ip not found group is：" + group + " ,flow name is: " + flowName);
                            throw new ServerException(ServerError.ERR_SERVER_IP_NOT_FOUND);
                        } else
                            ip = getSNHostName();
                    }
                    system = WorkflowQueryManager.getInstance().getSystem(prjName, flowName, con);

                    boolean isDM = WorkFlowManager.getInstance().isDayStartMainFlow(prjName, flowName, con);
                    OffsetFreqBean offsetFreqBean = null;
                    if (isDM)
                    {
                        offsetFreqBean = WorkFlowManager.getInstance().getDayStartOffsetFreq(prjName, flowName, con);
                    } else
                    {
                        OffsetFreqBean dayStartBean = WorkFlowManager.getInstance().getMainProjectName(prjName,
                            flowName, con);
                        offsetFreqBean = WorkFlowManager.getInstance().getOffsetFreq(dayStartBean.getProjectName(),
                            dayStartBean.getFlowName(), con);
                    }

                    String prjUuid = resourceManager.getLatestPrjUuid(prjName, con, Constants.IEAI_IEAI);
                    Project project = resourceManager.getProjectByUuid(prjUuid);
                    flowId = IdGenerator.createIdForExecAct(RepWorkflowInstance.class, con);
                    long mainScopeId = IdGenerator.createIdForExecAct(RepBranchScope.class, con);

                    // about flow log
                    if (logConfig == null || logConfig.isEmpty())
                    {
                        try
                        {
                            logConfig = AppLogManager.getInstance().getFlowLogConfig(prjName, flowName, con);
                        } catch (RepositoryException ex)
                        {
                            throw new ServerException(ex.getServerError().getErrCode());
                        }
                    }

                    WorkflowInstance flowInstance = new WorkflowInstance();

                    // init workflow infomation
                    Workflow flow = project.getWorkflowByName(flowName);

                    flowInstance.setAutoStart(flow.isAutoStartFlow());
                    flowInstance.setFlowComment(comment);
                    flowInstance.setFlowDefId(flow.getID());
                    flowInstance.setFlowDes(flow.getDescription());
                    flowInstance.setFlowId(flowId);
                    flowInstance.setFlowInsName(instanceName);
                    flowInstance.setFlowLogConfig(logConfig);
                    flowInstance.setFlowName(flowName);
                    flowInstance.setFlowPrior(flow.getPriority());
                    flowInstance.setHostName(ip);
                    flowInstance.setMainScopeId(mainScopeId);
                    flowInstance.setPrjUuid(prjUuid);
                    flowInstance.setProjectName(prjName);
                    flowInstance.setSafeFirst(true);
                    Date flowStartTime = startTime != null ? startTime : new Date();
                    flowInstance.setStartTime(flowStartTime);

                    flowInstance.setStartUser(user);
                    flowInstance.setStatus(Constants.STATE_RUNNING);
                    flowInstance.set_system(system);

                    flowInstance.setOffset(offsetFreqBean.getOffset());
                    flowInstance.setFreq(offsetFreqBean.getFreq());

                    EngineRepositotyJdbc.getInstance().saveFowInstance(flowInstance, con);

                    resourceManager.initFlowArgsAndEnvVars(flowId, project, flow, args, envVars, con);

                    actCtxManager.initFlow(flow, flowInstance, Constants.IEAI_IEAI, con);
                    // start workflow use branch manager

                    if (starterSucElemes == null)
                    {
                        EngineRepositotyJdbc.getInstance().saveFlowisStartMiddle(flowId, 0, 0, con);
                    } else
                    {
                        EngineRepositotyJdbc.getInstance().saveFlowisStartMiddle(flowId, 0, 1, con);
                    }
                    branchManager.startMultiBranch(flowId, mainScopeId, flowInstance.isSafeFirst(), flow,
                        starterSucElemes, con);
                    WorkflowQueryManager.getInstance().saveMainFlowACTPRENUM(flowId, instanceName, prjName, flowName,
                        starterSucElemes, con);

                    con.commit();
                    //拓扑节点：启动工作流  begin
                    TopoSendTriPoolThread.getInstance().addRequest(flowId, " ", 1, "0",
                            System.currentTimeMillis(), 1);
                    //拓扑节点：启动工作流  end
                    _log.info("the flow " + flowId + " is start success, The server is " + ip);
                    MonitorSystem ms = MonitorSystem.getInstance();
                } catch (SQLException e)
                {
                    // 失败后数据回滚
                    DBResource.rollback(con, ServerError.ERR_DB_INSERT, e,
                        Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                    String method = Thread.currentThread().getStackTrace()[1].getMethodName();
                    _log.error("Error when start Flow ! and flowName = " + flowName, e);
                    DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConnection(con, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                try
                {
                    DBRetryUtil.waitForNextTry(i, ex);
                } catch (RepositoryException e)
                {
                    _log.error("Error when start Flow ! and flowName = " + flowName, e);
                }
            }
        }
        return flowId;
    }

    /**
     * 
     * @Title: startFlow   
     * @Description: kunlun 启动工作流
     * @param user
     * @param prjName
     * @param flowName
     * @param args
     * @param envVars
     * @param logConfig
     * @param instanceName
     * @param comment
     * @param forceEfficiencyPrior
     * @return
     * @throws ServerException      
     * @author: junyu_zhang 
     * @date:   2018年5月18日 下午7:39:37
     */
    public long startFlow ( UserInfo user, String prjName, String flowName, List args, Map envVars,
            WorkflowLogConfig logConfig, String instanceName, String comment, boolean forceEfficiencyPrior )
            throws ServerException
    {
        return startFlow(user, prjName, flowName, args, envVars, logConfig, instanceName, comment, forceEfficiencyPrior,
            null, null);
    }

    /**
     * 
     * @Title: startFlow   
     * @Description: 启动工作流   
     * @param user
     * @param prjName
     * @param flowName
     * @param args
     * @param envVars
     * @param logConfig
     * @param instanceName
     * @param comment
     * @param forceEfficiencyPrior
     * @param starterSucElem
     * @param startTime
     * @return
     * @throws ServerException      
     * @author: junyu_zhang 
     * @date:   2018年5月18日 下午7:40:09
     */
    public long startFlow ( UserInfo user, String prjName, String flowName, List args, Map envVars,
            WorkflowLogConfig logConfig, String instanceName, String comment, boolean forceEfficiencyPrior,
            BasicActElement starterSucElem, Date startTime ) throws ServerException
    {
        if (!"ExcelActExecModelSUS".equals(prjName))
        {
            String[] mainlineName = null;
            try
            {
                mainlineName = WorkflowQueryManager.getInstance().getProjectMainlineName(prjName, flowName);
            } catch (RepositoryException e)
            {
                _log.info(
                    "startFlow is getProjectMainlineName get error projectName:" + prjName + " flowName:" + flowName);
            }
            long starttime = System.currentTimeMillis();
            if (null != mainlineName && StringUtils.isNotEmpty(mainlineName[0])
                    && StringUtils.isNotEmpty(mainlineName[1]))
            {
                try
                {
                    WorkFlowManager.getInstance().importExcelModel(mainlineName[0], mainlineName[1]);
                } catch (RepositoryException e)
                {
                    e.printStackTrace();
                }
            }
            _log.info("启动工作流导入Excelmodel总耗时为：" + ((System.currentTimeMillis()) - starttime) + "projectName:" + prjName
                    + " flowName:" + flowName);
        }
        _log.info("开始启动工作流 projectName:" + prjName + " flowName:" + flowName);
        return startFlow(user, prjName, flowName, args, envVars, logConfig, instanceName, comment, forceEfficiencyPrior,
            starterSucElem, startTime, false, 0);

    }

    /**
     * 
     * @Title: startFlow   
     * @Description: 启动工作流  
     * @param user
     * @param prjName
     * @param flowName
     * @param args
     * @param envVars
     * @param logConfig
     * @param instanceName
     * @param comment
     * @param forceEfficiencyPrior
     * @param starterSucElem
     * @param startTime
     * @param isStarter
     * @param starterFlowId
     * @return
     * @throws ServerException      
     * @author: junyu_zhang 
     * @date:   2018年5月18日 下午7:40:52
     */
    public long startFlow ( UserInfo user, String prjName, String flowName, List args, Map envVars,
            WorkflowLogConfig logConfig, String instanceName, String comment, boolean forceEfficiencyPrior,
            BasicActElement starterSucElem, Date startTime, boolean isStarter, long starterFlowId )
            throws ServerException
    {
        long flowId = 0;
        String system = null;
        for (int i = 0;; i++)
        {
            try
            {
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("startFlow", _log, Constants.IEAI_IEAI);
                    if (startTime != null && startTime.getTime() > System.currentTimeMillis())
                    {
                        throw new ServerException(ServerError.ERR_INVALID_FLOW_START_TIME);
                    }
                    String ip = "";
                    List addrs = new ArrayList();
                    long group = EngineRepository.getInstance().getGroupId(prjName, con);
                    if (group == 0)
                    {
                        _log.error("group not found project is：" + prjName + " ,flow name is: " + flowName);
                        throw new ServerException(ServerError.ERR_GROUP_NOT_FOUND);
                    }
                    if ("ExcelActExecModelSUS".equals(prjName))
                    {
                        ip = EngineRepository.getInstance().loadBalanceIP(group, addrs, con);
                    } else
                    {
                        boolean isM = WorkflowQueryManager.getInstance().isMainFlow(prjName, flowName, con);
                        if (isM)
                        {
                            ip = EngineRepository.getInstance().loadBalanceIPforMainFlow(group, addrs, con);

                        } else
                        {
                            int count = WorkFlowManager.getInstance().getMutexActCount(flowName, prjName, con);
                            if (count > 0)
                            {
                                _log.error("mutex actname is running  is：projectName" + prjName + " ,flow name is: "
                                        + flowName);
                                throw new ServerException(ServerError.ERR_MUTEX_RUUNING);
                            }
                            ip = EngineRepository.getInstance().loadBalanceIP(group, addrs, con);
                        }
                    }

                    if ("" == ip)
                    {
                        if (SystemConfig.isHeartbeat())
                        {
                            _log.error("ip not found group is：" + group + " ,flow name is: " + flowName);
                            throw new ServerException(ServerError.ERR_SERVER_IP_NOT_FOUND);
                        } else
                            ip = getSNHostName();
                    }
                    boolean isDM = WorkFlowManager.getInstance().isDayStartMainFlow(prjName, flowName, con);
                    OffsetFreqBean offsetFreqBean = null;
                    if (isDM)
                    {
                        offsetFreqBean = WorkFlowManager.getInstance().getDayStartOffsetFreq(prjName, flowName, con);
                        if (SystemConfig.isOther())
                        {
                            _log.info("startFlow is getDayStartOffsetFreq  prjName: " + prjName + " flowName: "
                                    + flowName + " offset: " + offsetFreqBean.getOffset() + " freq: "
                                    + offsetFreqBean.getFreq());
                        }
                    } else
                    {
                        OffsetFreqBean dayStartBean = WorkFlowManager.getInstance().getMainProjectName(prjName,
                            flowName, con);
                        offsetFreqBean = WorkFlowManager.getInstance().getOffsetFreq(dayStartBean.getProjectName(),
                            dayStartBean.getFlowName(), con);
                        if (SystemConfig.isOther())
                        {
                            _log.info("startFlow is getDayStartOffsetFreq  prjName: " + prjName + " flowName: "
                                    + flowName + " offset: " + offsetFreqBean.getOffset() + " freq: "
                                    + offsetFreqBean.getFreq() + " getProjectName: " + dayStartBean.getProjectName()
                                    + " getFlowName(): " + dayStartBean.getFlowName());
                        }
                    }
                    // add by zhaolicheng for 根据工程名、工作流名将已完成的工作流信息移到周期表中，删除工作流信息 2013.12.27
                    system = WorkflowQueryManager.getInstance().getSystem(prjName, flowName, con);
                    // end by zhaolicheng
                    String prjUuid = resourceManager.getLatestPrjUuid(prjName, con);
                    Project project = resourceManager.getProjectByUuid(prjUuid);
                    if (SystemConfig.isOther())
                    {
                        _log.info(
                            "startFlow is load getProjectByUuid  projectName:+" + prjName + " flowName:" + flowName);
                    }
                    if( Environment.getInstance().getXanxSwitch()&&isDM){//西安导入
                        String ISYSNAME="";
                        if (StringUtils.isEmpty(system))
                        {
                            ISYSNAME = WorkFlowManager.getInstance().isDayStartMainISYSNAMEFlow(prjName, flowName, con);
                            system = ISYSNAME;
                        }
                    }
                    flowId = IdGenerator.createIdForExecAct(RepWorkflowInstance.class, con);
                    long mainScopeId = IdGenerator.createIdForExecAct(RepBranchScope.class, con);

                    // about flow log
                    if (logConfig == null || logConfig.isEmpty())
                    {
                        logConfig = AppLogManager.getInstance().getFlowLogConfig(prjName, flowName, con);
                    }

                    WorkflowInstance flowInstance = new WorkflowInstance();

                    // init workflow infomation
                    Workflow flow = project.getWorkflowByName(flowName);
                    if (SystemConfig.isOther())
                    {
                        _log.info(
                            "startFlow is load getWorkflowByName  projectName:+" + prjName + " flowName:" + flowName);
                    }
                    try
                    {
                        flowInstance.setAutoStart(flow.isAutoStartFlow());
                        if (ServerEnv.getServerEnv().getAutoFlowFlag() && user.getId() == 0)
                        {
                            flowInstance.setAutoFlowFlag(Constants.AUTOFLOW_AUTO);
                        }
                    } catch (Exception e)
                    {
                        _log.error("start flow get flow.isAutoStartFlow() is null error " + e.getMessage());
                        ProjectManager.getInstance().reloadProjectByUuid(prjUuid);
                        project = ProjectAdaptorCache.getInstance().loadProjectByUuid(prjUuid);
                        flow = project.getWorkflowByName(flowName);
                    }
                    Long prjUpperId = ProjectManager.getInstance().getPrjUpperIdByPrjName(prjName, Constants.IEAI_IEAI);
                    Long prjLastId = ProjectManager.getInstance().getPrjLastIdByPrjName(prjName);
                    Long flowUpperId = ProjectManager.getInstance().getFlowUpperIdByFlowName(flowName, prjUpperId,
                        Constants.IEAI_IEAI);
                    Long flowLastId = ProjectManager.getInstance().getFlowLastIdByFlowName(flowName, prjUpperId);
                    flowInstance.setFlowComment(comment);
                    flowInstance.setFlowDefId(flow.getID());
                    flowInstance.setFlowDes(flow.getDescription());
                    flowInstance.setFlowId(flowId);
                    flowInstance.setFlowInsName(instanceName);
                    flowInstance.setFlowLogConfig(logConfig);
                    flowInstance.setFlowName(flowName);
                    flowInstance.setFlowPrior(flow.getPriority());
                    flowInstance.setHostName(ip);
                    flowInstance.setMainScopeId(mainScopeId);
                    flowInstance.setPrjUuid(prjUuid);
                    flowInstance.setProjectName(prjName);
                    flowInstance.setSafeFirst(true);

                    flowInstance.setPrjUpperId(prjUpperId);
                    flowInstance.setPrjId(prjLastId);
                    flowInstance.setFlowUpperId(flowUpperId);
                    flowInstance.setFlowOwnId(flowLastId);

                    Date flowStartTime = startTime != null ? startTime : new Date();
                    flowInstance.setStartTime(flowStartTime);

                    flowInstance.setStartUser(user);
                    flowInstance.setStatus(Constants.STATE_RUNNING);
                    flowInstance.set_system(system);

                    flowInstance.setOffset(offsetFreqBean.getOffset());
                    flowInstance.setFreq(offsetFreqBean.getFreq());

                    EngineRepositotyJdbc.getInstance().saveFowInstance(flowInstance, con);
                    resourceManager.initFlowArgsAndEnvVars(flowId, project, flow, args, envVars, con);
                    if (SystemConfig.isOther())
                    {
                        _log.info(
                            "startFlow is initFlowArgsAndEnvVars  projectName:+" + prjName + " flowName:" + flowName);
                    }
                    actCtxManager.initFlow(flow, flowInstance, Constants.IEAI_IEAI, con);
                    if (SystemConfig.isOther())
                    {
                        _log.info("startFlow is initFlow  projectName:+" + prjName + " flowName:" + flowName);
                    }
                    // start workflow use branch manager
                    if (starterSucElem == null)
                    {
                        EngineRepositotyJdbc.getInstance().saveFlowisStartMiddle(flowId, 0, 0, con);
                    } else
                    {
                        EngineRepositotyJdbc.getInstance().saveFlowisStartMiddle(flowId, 0, 1, con);
                    }
                    if (ServerEnv.getInstance().getReadTxtThreadStartFlag()) // 数仓平台系统开关，只有开启状态才执行数仓系统的假启动
                    {
                        if (!StringUtils.equals("SJCK_E_数据仓库系统", prjName) && !StringUtils.equals("数据仓库系统", prjName))
                        {
                            // 吉林农信数仓
                            boolean isExec = true;
                            boolean dataFlatSwitch = Boolean.parseBoolean(
                                EnvConfigReader.getInstance().getProperties(Environment.DATAMAPPING_ANALY_SWITCH,
                                    String.valueOf(Environment.DATAMAPPING_ANALY_SWITCH_DEFAULT)));
                            String dataFlat = EnvConfigReader.getInstance()
                                    .getProperties(Environment.DATA_PLATFORM_NAME, "");
                            if (dataFlatSwitch && null != dataFlat && !"".equals(dataFlat) && !"null".equals(dataFlat))
                            {
                                String[] prjAndflow = dataFlat.split(":");
                                if (prjAndflow.length == 2)
                                {
                                    if (prjAndflow[0].equals(prjName) && prjAndflow[1].equals(flowName))
                                    {
                                        _log.info("S2-dataFlatSwitch:" + dataFlatSwitch + ",prjAndflow:" + prjAndflow
                                                + ",prjName:" + prjName + ",flowName:" + flowName);
                                        isExec = false;
                                    }
                                }
                            }
                            if (isExec)
                            {
                                branchManager.startMainBranch(flowId, mainScopeId, flowInstance.isSafeFirst(), flow,
                                    starterSucElem, con);
                                if (SystemConfig.isOther())
                                {
                                    _log.info("startFlow is startMainBranch  projectName:+" + prjName + " flowName:"
                                            + flowName);
                                }
                                EngineRepositotyJdbc.getInstance().saveMainFlowACTPRENUM(flowId, instanceName, prjName,
                                    flowName, starterSucElem, con);
                                if (SystemConfig.isOther())
                                {
                                    _log.info("startFlow is saveMainFlowACTPRENUM  projectName:+" + prjName
                                            + " flowName:" + flowName);
                                }
                            }
                        }
                    } else
                    {
                        // 吉林农信数仓
                        boolean isExec = true;
                        boolean dataFlatSwitch = Boolean.parseBoolean(
                            EnvConfigReader.getInstance().getProperties(Environment.DATAMAPPING_ANALY_SWITCH,
                                String.valueOf(Environment.DATAMAPPING_ANALY_SWITCH_DEFAULT)));
                        String dataFlat = EnvConfigReader.getInstance().getProperties(Environment.DATA_PLATFORM_NAME,
                            "");
                        if (dataFlatSwitch && null != dataFlat && !"".equals(dataFlat) && !"null".equals(dataFlat))
                        {
                            String[] prjAndflow = dataFlat.split(":");
                            if (prjAndflow.length == 2)
                            {
                                if (prjAndflow[0].equals(prjName) && prjAndflow[1].equals(flowName))
                                {
                                    _log.info("dataFlatSwitch:" + dataFlatSwitch + ",prjAndflow:" + prjAndflow
                                            + ",prjName:" + prjName + ",flowName:" + flowName);
                                    isExec = false;
                                }
                            }
                        }
                        if (isExec)
                        {
                            branchManager.startMainBranch(flowId, mainScopeId, flowInstance.isSafeFirst(), flow,
                                starterSucElem, con);
                            if (SystemConfig.isOther())
                            {
                                _log.info(
                                    "startFlow is startMainBranch  projectName:+" + prjName + " flowName:" + flowName);
                            }
                            EngineRepositotyJdbc.getInstance().saveMainFlowACTPRENUM(flowId, instanceName, prjName,
                                flowName, starterSucElem, con);
                            if (SystemConfig.isOther())
                            {
                                _log.info("startFlow is saveMainFlowACTPRENUM  projectName:+" + prjName + " flowName:"
                                        + flowName);
                            }
                        }

                    }
                    //拓扑节点：启动工作流 begin
                    TopoSendTriPoolThread.getInstance().addRequest(flowId, " ", 1, "0",
                            System.currentTimeMillis(), 1);
                    //拓扑节点：启动工作流 end
                    con.commit();
                    _log.info("startFlow is start flow flowid: " + flowId + " prjName：" + prjName + " flowName:"
                            + flowName + " instanceName:" + instanceName);
                    if (ServerEnv.getInstance().getReadTxtThreadStartFlag()
                            && (StringUtils.equals("SJCK_E_数据仓库系统", prjName) || StringUtils.equals("数据仓库系统", prjName))
                            && StringUtils.equals("ALL", flowName)) // 数仓平台系统开关，只有开启状态才执行数仓系统的假启动
                    {
                        // 手动启动工作流时，当工作流名为ALL时，自动启动数仓工作流的线程运行
                        DBResource.closeConnection(con, "startFlow", _log);

                        DataWarehouseStartThread dwThread = new DataWarehouseStartThread(user, prjName, flowName, args,
                                envVars, logConfig, instanceName, comment, forceEfficiencyPrior, starterSucElem,
                                startTime, isStarter, starterFlowId);
                        dwThread.start();
                    }
                } catch (Exception e)
                {
                    // 失败后数据回滚
                    DBResource.rollback(con, ServerError.ERR_DB_INSERT, e,
                        Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                    String method = Thread.currentThread().getStackTrace()[1].getMethodName();
                    _log.error("Error when start Flow ! and flowName = " + flowName + " messages:" + e);
                    _log.error("Error when start Flow:", e);
                    DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConnection(con, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                try
                {
                    DBRetryUtil.waitForNextTry(i, ex);
                } catch (RepositoryException e)
                {
                    _log.error("Error when start Flow ! and flowName = " + flowName + " messages:" + e);
                }
            }
        }
        return flowId;
    }

    public void killFlow ( long flowId ) throws ServerException
    {
        //拓扑图节点：终止工作流 begin
        TopoSendTriPoolThread.getInstance().addRequest(flowId, "", 1, "4",
                System.currentTimeMillis(), 1);
        //拓扑图节点：终止工作流 end
        updateFlowState(flowId, Constants.STATE_KILLED, Constants.IEAI_IEAI);
        String[] flowInfo = new String[1];
        flowInfo[0] = String.valueOf(flowId);
        addClearFlowsThread = new AddClearFlowInfo(flowInfo, Constants.IEAI_IEAI);
        addClearFlowsThread.start();

    }

    /**
     * <li>Description:按照工程终止工作流</li>
     * 
     * <AUTHOR> 2015.9.8
     * @param user
     * @param
     * @throws ServerException return void
     */
    public void killProjectFlowByPrjName ( UserInfo user, String prjName, int dbType ) throws ServerException
    {
        List list = null;
        try
        {
            list = EngineRepositotyJdbc.getInstance().getFlowOfProjectAscByProjectName(prjName, dbType);
            if (null != list && !list.isEmpty())
            {
                long flowid = 0;
                for (int i = 0; i < list.size(); i++)
                {
                    flowid = Long.parseLong(String.valueOf(list.get(i)));
                    killFlow(user, flowid, dbType);
                }
            } else
            {
                throw new ServerException(ServerError.ERR_PRJ_NOT_RUNNING_FLOW);
            }
        } catch (RepositoryException e)
        {
            throw new ServerException(e.getServerError().getErrCode());
        }

    }

    public void resumeFlow ( long flowId ) throws ServerException
    {

        branchManager.resumeFlow(flowId, Constants.IEAI_IEAI);

    }

    /**
     * 
     * @ClassName:  AddClearFlowId   
     * @Description:增加数据清理线程，用于正常结束的工作流  
     * @author: xibin_gong 
     * @date:   2018年6月7日 下午1:12:21   
     *     
     * @Copyright: 2018-2027 www.idealinfo.com Inc. All rights reserved. 
     *
     */
    private class AddClearFlowId extends Thread
    {

        long flowInfo;
        int  flowState;

        public AddClearFlowId(long flowId, int state)
        {
            flowInfo = flowId;
            flowState = state;
        }

        public void run ()
        {
            synchronized (stoppingIEAIFlows)
            {
                stoppingIEAIFlows.add(String.valueOf(flowInfo + ":" + flowState));
                stoppingIEAIFlows.notify();
            }
        }
    }

    /**
     * 
     * <li>Description:浦发检查发送邮件线程是否启动</li> 
     * <AUTHOR>
     * 2019年9月17日 
     * @param inThread
     * return void
     */
    public static void checkSendBfMailThreadStart ( Thread inThread, String fileName, String switchName )
    {
        Properties properties = new Properties();
        String filePath = System.getProperty("IEAI_HOME", "NOT DEFINED!!!") + File.separator + "config" + File.separator
                + fileName;
        InputStream is = null;
        boolean stratFlag = false;
        try
        {
            is = new FileInputStream(filePath);
            properties.load(is);
            String mailSwitch = properties.getProperty(switchName);
            if (!"".equals(mailSwitch) && null != mailSwitch)
            {
                stratFlag = Boolean.parseBoolean(mailSwitch);
            }
        } catch (Exception e)
        {
            stratFlag = false;
            _log.error("checkSendBfMailThreadStart() get pf.sus.mail.switch is error: " + e.getMessage());
        }
        if (stratFlag)
        {
            inThread.start();
        }
    }

    public Thread getAsynchronyRuntimeThread ()
    {
        return this.asynchronyRuntimeThread;
    }

    public long preStartFlow ( UserInfo user, String prjName, String flowName, List args, Map envVars,
            WorkflowLogConfig logConfig, String instanceName, String comment, boolean forceEfficiencyPrior,
            BasicActElement starterSucElem, Date startTime, int type, int prjType, boolean isAdvance, String actoutData, String wait_start )
            throws ServerException, RepositoryException
    {

        return preStartFlow(user, prjName, flowName, args, envVars, logConfig, instanceName, null, comment,
            forceEfficiencyPrior, starterSucElem, startTime, type, prjType, isAdvance,actoutData,wait_start);
    }

    private long preStartFlow ( UserInfo user, String prjName, String flowName, List args, Map envVars,
            WorkflowLogConfig logConfig, String instanceName, String iworkItemid, String comment,
            boolean forceEfficiencyPrior, BasicActElement starterSucElem, Date startTime, int type, int prjType,
            boolean isAdvance, String actoutData, String wait_start) throws ServerException, RepositoryException
    {
        if (!"ExcelActExecModelSUS".equals(prjName) && !"ExcelActExecModelDR".equals(prjName))
        {
            String[] mainlineName = null;
            try
            {
                mainlineName = ProjectManager.getInstance().getProjectMainlineName(prjName, flowName);
            } catch (RepositoryException e)
            {
                _log.info(
                    "startFlow is getProjectMainlineName get error projectName:" + prjName + " flowName:" + flowName);
            }
            long starttime = System.currentTimeMillis();
            if (StringUtils.isNotEmpty(mainlineName[0]) && StringUtils.isNotEmpty(mainlineName[1]))
            {
                try
                {
                    WorkFlowManager.getInstance().importExcelModel(mainlineName[0], mainlineName[1]);
                } catch (RepositoryException e)
                {
                    _log.info(
                        "startFlow is importExcelModel get error projectName:" + prjName + " flowName:" + flowName);
                }
            }
            _log.info("启动工作流导入Excelmodel总耗时为：" + ((System.currentTimeMillis()) - starttime) + "projectName:" + prjName
                    + " flowName:" + flowName);
        }
        _log.info("开始启动工作流 projectName:" + prjName + " flowName:" + flowName);
        return pprestartFlow(user, prjName, flowName, args, envVars, logConfig, instanceName, iworkItemid, comment,
            forceEfficiencyPrior, starterSucElem, startTime, false, 0, type, prjType, isAdvance,actoutData,wait_start);
    }
    public long pprestartFlow ( UserInfo user, String prjName, String flowName, List args, Map envVars,
            WorkflowLogConfig logConfig, String instanceName, String workItemId, String comment,
            boolean forceEfficiencyPrior, BasicActElement starterSucElem, Date startTime, boolean isStarter,
            long starterFlowId, int type, int prjType, boolean isAdvance, String actoutData, String wait_start ) throws ServerException, RepositoryException
    {
        long flowId = 0;
        String system = null;
        Connection con = null;
        // 任务查询用 双人复核ID
        for (int i = 0;; i++)
        {
            try
            {
                try
                {
                    con = DBResource.getConnection("startFlow", _log, type);
                    WorkFlowManager manager = WorkFlowManager.getInstance();
                    if (startTime != null && startTime.getTime() > System.currentTimeMillis())
                    {
                        throw new ServerException(ServerError.ERR_INVALID_FLOW_START_TIME);
                    }
                    String ip = "";
                    List addrs = new ArrayList();

                    long group = EngineRepository.getInstance().getGroupId(prjName, con, prjType);
                    if (group == 0)
                    {
                        _log.error("group not found project is：" + prjName + " ,flow name is: " + flowName);
                        throw new ServerException(ServerError.ERR_GROUP_NOT_FOUND);
                    }
                    /* 判断是否主线 */
                    boolean isM = manager.isMainFlow(prjName, flowName, con);
                    if (isM)
                    {
                        ip = EngineRepository.getInstance().loadBalanceIP(group, addrs, con);
                    } else
                    {
                        /* 判断互斥作业 */
                        int count = manager.getMutexActCount(flowName, prjName, con);
                        if (count > 0)
                        {
                            _log.error(
                                "mutex actname is running  is：projectName" + prjName + " ,flow name is: " + flowName);
                            throw new ServerException(ServerError.ERR_MUTEX_RUUNING);
                        }
                        ip = EngineRepository.getInstance().loadBalanceIP(group, addrs, con);
                    }

                    if ("" == ip)
                    {
                        if (SystemConfig.isHeartbeat())
                        {
                            _log.error("ip not found group is：" + group + " ,flow name is: " + flowName);
                            throw new ServerException(ServerError.ERR_SERVER_IP_NOT_FOUND);
                        } else
                        {
                            ip = getSNHostName();
                        }
                    }
                    OffsetFreqBean offsetFreqBean = null;
                    boolean isDM = false;
                    /* 判断是否日启动 */
                    if (!"ExcelActExecModelSUS".equals(prjName))
                    {
                        isDM = manager.isDayStartMainFlow(prjName, flowName, con);

                        if (isDM)
                        {
                            offsetFreqBean = manager.getDayStartOffsetFreq(prjName, flowName, con);
                            if (SystemConfig.isOther())
                            {
                                _log.info("startFlow is getDayStartOffsetFreq  prjName: " + prjName + " flowName: "
                                        + flowName + " offset: " + offsetFreqBean.getOffset() + " freq: "
                                        + offsetFreqBean.getFreq());
                            }
                        } else
                        {
                            OffsetFreqBean dayStartBean = manager.getMainProjectName(prjName, flowName, con);
                            offsetFreqBean = manager.getOffsetFreq(dayStartBean.getProjectName(),
                                dayStartBean.getFlowName(), con);
                            if (SystemConfig.isOther())
                            {
                                _log.info("startFlow is getDayStartOffsetFreq  prjName: " + prjName + " flowName: "
                                        + flowName + " offset: " + offsetFreqBean.getOffset() + " freq: "
                                        + offsetFreqBean.getFreq() + " getProjectName: " + dayStartBean.getProjectName()
                                        + " getFlowName(): " + dayStartBean.getFlowName());
                            }
                        }
                        system = manager.getSystem(prjName, flowName, con);
                    }

                    String prjUuid = resourceManager.getLatestPrjUuid(prjName, con, prjType);
                    Project project = resourceManager.getProjectByUuid(prjUuid);
                    if (!"ExcelActExecModelSUS".equals(prjName))
                    {
                        if (StringUtils.isEmpty(system))
                        {
                            system = project.getSysname();
                        }
                    }
                    if( Environment.getInstance().getXanxSwitch()&&isDM){//西安导入
                        String ISYSNAME="";
                        if (StringUtils.isEmpty(system))
                        {
                            ISYSNAME = WorkFlowManager.getInstance().isDayStartMainISYSNAMEFlow(prjName, flowName, con);
                            system = ISYSNAME;
                        }
                    }
                    flowId = IdGenerator.createIdForExecAct(RepWorkflowInstance.class, con);
                    long mainScopeId = IdGenerator.createIdForExecAct(RepBranchScope.class, con);

                    WorkflowInstance flowInstance = new WorkflowInstance();

                    Long prjUpperId = ProjectManager.getInstance().getPrjUpperIdByPrjName(prjName, type);
                    Long prjLatestId = ProjectManager.getInstance().getPrjLatestIdByPrjName(prjName, type);
                    Long flowUpperId = ProjectManager.getInstance().getFlowUpperIdByFlowName(flowName, prjUpperId,
                        type);
                    Long flowLastId = ProjectManager.getInstance().getFlowLatestIdByFlowName(flowName, prjUpperId,
                        type);

                    // init workflow infomation
                    Workflow flow = project.getWorkflowByName(flowName);
                    flowInstance.setAutoStart(flow.isAutoStartFlow());
                    flowInstance.setFlowComment(comment);
                    flowInstance.setFlowDefId(flow.getID());
                    flowInstance.setFlowDes(flow.getDescription());
                    flowInstance.setFlowId(flowId);
                    flowInstance.setFlowInsName(instanceName);
                    flowInstance.setFlowLogConfig(logConfig);
                    flowInstance.setFlowName(flowName);
                    flowInstance.setFlowPrior(flow.getPriority());
                    flowInstance.setHostName(ip);
                    flowInstance.setMainScopeId(mainScopeId);
                    flowInstance.setPrjUuid(prjUuid);
                    flowInstance.setProjectName(prjName);
                    flowInstance.setSafeFirst(true);
                    flowInstance.setActoutData(actoutData);
                    // 日常操作——任务查询 显示参数功能 反写IEAI_DOUBLECHECK_WORKITEM.iflowid 双人复核ID
                    if (null != workItemId && !"".equals(workItemId))
                    {
                        flowInstance.setWorkItemId(workItemId);
                    }
                    Date flowStartTime = startTime != null ? startTime : new Date();
                    flowInstance.setStartTime(flowStartTime);

                    flowInstance.setStartUser(user);
                    flowInstance.setStatus(Constants.STATE_RUNNING);
                    if (isAdvance)
                    {
                        flowInstance.setStatus(-6);   // 预加载状态
                    }
                    if(wait_start != null && !"".equals(wait_start)) {
                        Long prest = timeToLong(wait_start.replaceAll("T", " "));
                        String execCronTime = "";
                        if (StringUtils.isNotBlank(wait_start))
                        {
                            String execTime = wait_start;
                            execTime = execTime.replaceAll("T", " ");
                            Date execDate = DateUtil.StrToDate(execTime);
                            execCronTime = CronDateUtils.getCron(execDate);
                          
                        } else
                        {
                           
                        }
                        
                        flowInstance.setStatus(55); 
                        StrategyBean bean = new StrategyBean();
                        bean.setIflowid(flowId);
                        bean.setServerip(ip);
                        bean.setCron(execCronTime);
                        bean.setPrestarttimestr(wait_start.replaceAll("T", " "));
                        bean.setIdbtype(new Long(prjType));
                        bean.setPrestarttime(prest);
                        bean.setJobClass("com.ideal.ieai.server.quartz.StartWorkflowJob");
                        GroupExecutionStrategyHandler.getInstance().addgroupExecutionStrategy(type, bean);
                        
                    }
                    flowInstance.set_system(system);
                    if (offsetFreqBean != null)
                    {
                        flowInstance.setOffset(offsetFreqBean.getOffset());
                        flowInstance.setFreq(offsetFreqBean.getFreq());
                    }

                    flowInstance.setPrjUpperId(prjUpperId);
                    flowInstance.setPrjId(prjLatestId);
                    flowInstance.setFlowUpperId(flowUpperId);
                    flowInstance.setFlowOwnId(flowLastId);
                    String butterflyversion = "";
                    if (envVars != null)
                    {
                        butterflyversion = envVars.get("butterflyversion") == null ? ""
                                : envVars.get("butterflyversion").toString();
                    }
                    flowInstance.setButterflyversion(butterflyversion);
                    if (ServerEnv.getInstance().getJobSchedulingQuerySystemSwitch())
                    {
                        if (flow.isUseCheckflag())
                        {
                            EngineRepositotyJdbc.getInstance().delOdsActFinishedFlag(prjName, flowName, instanceName,
                                con);
                        }
                    }
                    if (isDM)
                    {
                        EngineRepositotyJdbc.getInstance().saveFowInstanceDayStart(flowInstance, con);
                    } else
                    {
                        EngineRepositotyJdbc.getInstance().saveFowInstance(flowInstance, con);
                    }

                    resourceManager.initFlowArgsAndEnvVars(flowId, project, flow, args, envVars, con, type);

                    actCtxManager.initFlow(flow, flowInstance, type, con);

                    if (starterSucElem == null)
                    {
                        EngineRepositotyJdbc.getInstance().saveFlowisStartMiddle(flowId, 0, 0, con);
                    } else
                    {
                        EngineRepositotyJdbc.getInstance().saveFlowisStartMiddle(flowId, 0, 1, con);
                    }

                    branchManager.startMainBranch(flowId, mainScopeId, flowInstance.isSafeFirst(), flow, starterSucElem,
                        con);

                    EngineRepositotyJdbc.getInstance().saveMainFlowACTPRENUM(flowId, instanceName, prjName, flowName,
                        starterSucElem, con);
                    if(!StringUtils.isBlank(actoutData)) {
                        this.savePreactOutinfomation(flowId,actoutData,con);
                    }

                    con.commit();

                    _log.info("the flow " + flowId + " is start success, The server is " + ip);

                } catch (Exception e)
                {
                    // 失败后数据回滚
                    DBResource.rollback(con, ServerError.ERR_DB_UPDATE, e,
                        Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                    String method = Thread.currentThread().getStackTrace()[1].getMethodName();
                    _log.error("Error when start Flow ! and flowName = " + flowName, e);
                    DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConnection(con, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log.error("Error when start Flow ! and flowName = " + flowName, ex);
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
        return flowId;
    }
    public static Long timeToLong(String time) { 
        DateTimeFormatter ftf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime parse = LocalDateTime.parse(time, ftf);
        return LocalDateTime.from(parse).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    } 
    private void addExecutionStrategyTask (int type)
    {
        StrategyBean bean = new StrategyBean();
        
    }

    private void savePreactOutinfomation ( long flowId, String actoutData, Connection con ) 
    {
        JSONArray jsonArray = JSON.parseArray(actoutData);
        String sql = "insert into IEAI_RUN_GROUP_ACTINFO(IPARAMVALUE,IPARAMTYPE,IPARAMNAME,IACTNAME,IFLOWID) values(?,?,?,?,?)";
        int arrsize = jsonArray.size();
        Object params [][] = new Object[arrsize][5];
        for(int i =0 ; i<arrsize;i++) {
            params[i][0] = jsonArray.getJSONObject(i).get("IPARAMVALUE");
            params[i][1] = jsonArray.getJSONObject(i).get("IPARAMTYPE");
            params[i][2] = jsonArray.getJSONObject(i).get("IPARAMNAME");
            params[i][3] = jsonArray.getJSONObject(i).get("IACTNAME");
            params[i][4] = flowId;
            
        }
        QueryRunner qu = new QueryRunner();
        try
        {
            qu.batch(con, sql, params);
        } catch (SQLException e)
        {
            e.printStackTrace();
        }
        
        
    }

    public synchronized long startCloudFlow ( UserInfo user, String prjName, String flowName, List args, Map envVars,
            WorkflowLogConfig logConfig, String instanceName, String comment, boolean forceEfficiencyPrior,
            BasicActElement starterSucElem, Date startTime, int type, int prjType,Long iorderid,String iorderuuid ) throws ServerException
    {
        return startCloudFlow(user, prjName, flowName, args, envVars, logConfig, instanceName, comment, forceEfficiencyPrior,
            starterSucElem, startTime, false, 0, type, prjType,iorderid,iorderuuid);
    }
    public long startCloudFlow ( UserInfo user, String prjName, String flowName, List args, Map envVars,
            WorkflowLogConfig logConfig, String instanceName, String comment, boolean forceEfficiencyPrior,
            BasicActElement starterSucElem, Date startTime, boolean isStarter, long starterFlowId, int type,
            int prjType,Long iorderid,String iorderuuid ) throws ServerException
    {
        long flowId = 0;
        Connection con = null;
       
        try
        {
            con = DBResource.getConnection("startFlow", _log, type);
            if (startTime != null && startTime.getTime() > System.currentTimeMillis())
            {
                throw new ServerException(ServerError.ERR_INVALID_FLOW_START_TIME);
            }
            String ip = "";
            List addrs = new ArrayList();

            long group = EngineRepository.getInstance().getGroupId(prjName, con, prjType);
            if (group == Constants.IEAI_OTHER)
            {
                group = Constants.IEAI_PAAS;
            }
            if (group == 0)
            {
                _log.error("group not found project is：" + prjName + " ,flow name is: " + flowName);
                throw new ServerException(ServerError.ERR_GROUP_NOT_FOUND);
            }
            ip = EngineRepository.getInstance().loadBalanceIP(group, addrs, con);
            if ("" == ip)
            {
                if (SystemConfig.isHeartbeat())
                {
                    _log.error("ip not found group is：" + group + " ,flow name is: " + flowName);
                    throw new ServerException(ServerError.ERR_SERVER_IP_NOT_FOUND);
                } else
                    ip = getSNHostName();
            }
            String prjUuid = resourceManager.getLatestPrjUuid(prjName, con, prjType);
            Project project = resourceManager.getProjectByUuid(prjUuid);
            flowId = IdGenerator.createIdForExecAct(RepWorkflowInstance.class, con);
            long mainScopeId = IdGenerator.createIdForExecAct(RepBranchScope.class, con);


            WorkflowInstance flowInstance = new WorkflowInstance();

            // init workflow infomation
            Workflow flow = project.getWorkflowByName(flowName);

            flowInstance.setAutoStart(flow.isAutoStartFlow());
            flowInstance.setFlowComment(comment);
            flowInstance.setFlowDefId(flow.getID());
            flowInstance.setFlowDes(flow.getDescription());
            flowInstance.setFlowId(flowId);
            flowInstance.setFlowInsName(instanceName);
            flowInstance.setFlowLogConfig(logConfig);
            flowInstance.setFlowName(flowName);
            flowInstance.setFlowPrior(flow.getPriority());
            flowInstance.setHostName(ip);
            flowInstance.setMainScopeId(mainScopeId);
            flowInstance.setPrjUuid(prjUuid);
            flowInstance.setProjectName(prjName);
            flowInstance.setSafeFirst(true);
       
            Date flowStartTime = startTime != null ? startTime : new Date();
            flowInstance.setStartTime(flowStartTime);

            flowInstance.setStartUser(user);
            flowInstance.setStatus(Constants.STATE_RUNNING);
            flowInstance.setIorderid(iorderid==null?0l:iorderid);
            flowInstance.setIorderuuid(iorderuuid);
            EngineRepositotyJdbc.getInstance().saveFowInstance(flowInstance, con);

            resourceManager.initFlowArgsAndEnvVars(flowId, project, flow, args, envVars, con);

            actCtxManager.initFlow(flow, flowInstance, type, con);


            branchManager.startMainBranch(flowId, mainScopeId, flowInstance.isSafeFirst(), flow, starterSucElem, con);

            con.commit();

            _log.info("the flow " + flowId + " is start success, The server is " + ip);
        } catch (RepositoryException e)
        {
            _log.error("Error when start Flow ! and flowName = " + flowName);
            throw new ServerException(ServerError.ERR_DB_ERROR);
        } catch (SQLException e)
        {
            _log.error("Error when start Flow ! and flowName = " + flowName);
        } finally
        {
            try
            {
                DBResource.closeConnection(con, "startFlow", _log);
            } catch (Exception aa)
            {
                throw new ServerException(ServerError.ERR_DB_ERROR);
            }
        }
        return flowId;
    }
    public static class ImportWebstudioRejectexecution implements RejectedExecutionHandler{

        @Override
        public void rejectedExecution ( Runnable r, ThreadPoolExecutor executor )
        {
            _log.info(r.toString()+"rejected");
        }
        
    }
    
    public static class ImportWebstudioThreadFactory implements ThreadFactory{

        final AtomicInteger threadnum = new AtomicInteger(1);
        @Override
        public Thread newThread ( Runnable r )
        {
            Thread t =new Thread(r,"new_ImportWebstudioThread"+threadnum.getAndIncrement());
            _log.info("thread "+t.getName()+"has been created");
            return t;
        }
        
    }
    public ThreadPoolExecutor getImportWebstudioThreadPool() {
        return this.importWebstudioswitch;
    }
    
    /**   
     * @ClassName:  MissionarySystemThread   
     * @Description: 重庆农商变更超时终止功能
     * @author: zuochao_wang 
     * @date:   2021年6月28日 上午11:49:23   
     * @Copyright: 2018-2027 www.idealinfo.com Inc. All rights reserved. 
     */
    private class MissionarySystemThread extends Thread
    {
        public void run ()
        {
            while (true)
            {
                try
                {
                    //增加逻辑，如果配置了超时时间才执行自动终止逻辑，否则什么都不执行
                    double time =ServerEnv.getServerEnv().getEmMonitorAutoTerminateTime();
                    if(-111!=time){
                        List<Long> iidsList = null;
                        Long[] str = null;
                        Boolean success = false;
                        UserInfo userInfo = new UserInfo();
                        userInfo.setId(4L);
                        userInfo.setFullName("ideal");
                        String falgString = "变更场景超时自动终止";
                        int flag = 2;
                        try
                        {
                            iidsList = ReleaseMonitorManager.getInstance().getInstanceMap();
                        } catch (RepositoryException e1)
                        {
                            // TODO Auto-generated catch block
                            e1.printStackTrace();
                        }
                        if(iidsList!=null&&!iidsList.isEmpty()) {
                            str = iidsList.toArray(new Long[iidsList.size()]);
                        }
                        if(str!=null&&str.length != 0) {
                            try
                            {
                                success = ReleaseMonitorManager.getInstance().kill(userInfo,str,flag,falgString,Constants.IEAI_SUS);
                            } catch (RepositoryException e)
                            {
                                _log.error("变更超时轮询线程杀死进程异常："+e);
                                e.printStackTrace();
                            }
                        } 
                        if(success) {
                            Calendar c = Calendar.getInstance();
                            _log.info(c.get(c.HOUR_OF_DAY)+"点"+c.get(c.MINUTE)+","+"变更线程调用手工部署操作"+","+"操作实例id为："+StringUtils.join(str, ","));                    
                        }
                    }
                    Thread.sleep(1 * 1000L * 60); //1分钟
                } catch (InterruptedException ex)
                {
                    _log.error(ex);
                }
            }
        }

    }
    
    public String getWarnMessageNMG ( String proName, String flowName )
    {
        StringBuilder sb = new StringBuilder();
        sb.append("工程" + proName + "中" + flowName);
        sb.append("执行结束!");
        return sb.toString();
    }


    public void saveIsCompensate ( Long flowid, String isOther  ) throws RepositoryException
    {
        String sql = "update ieai_workflowinstance set ISCOMPENSATE =?  where IFLOWID=? ";
        for (int i = 0; i < 10; i++)
        {
            try
            {
                Connection con = null;
                PreparedStatement ps = null;
                con = DBResource.getConnection(Thread.currentThread().getStackTrace()[1].getMethodName(), _log, Constants.IEAI_IEAI);

                try
                {
                    ps = con.prepareStatement(sql);
                    ps.setString(1, isOther);
                    ps.setLong(2, flowid);
                    ps.executeUpdate();
                    con.commit();
                } catch (SQLException e)
                {
                    DBResource.rollback(con, ServerError.ERR_DB_INSERT, e, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                } finally
                {
                    DBResource.closePSConn(con, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ex, ServerError.ERR_DB_INSERT);
            }
        }
    }

    public String getIsCompensate ( Long flowid) throws RepositoryException
    {
        String sql = "select ISCOMPENSATE from ieai_workflowinstance  where IFLOWID=? ";
        String iScompensate="";
        for (int i = 0; i < 10; i++)
        {
            try
            {
                Connection con = null;
                PreparedStatement ps = null;
                ResultSet resultSet=null;
                con = DBResource.getConnection(Thread.currentThread().getStackTrace()[1].getMethodName(), _log, Constants.IEAI_IEAI);
                try
                {
                    ps = con.prepareStatement(sql);
                    ps.setLong(1, flowid);
                    resultSet=ps.executeQuery();
                    while(resultSet.next()){
                        iScompensate=resultSet.getString("ISCOMPENSATE");
                    }
                } catch (SQLException e)
                {
                    DBResource.rollback(con, ServerError.ERR_DB_INSERT, e, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                } finally
                {
                    DBResource.closeConn(con,resultSet, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ex, ServerError.ERR_DB_INSERT);
            }
        }
        return iScompensate;
    }

    /**
     * 根据用户名查询是否具有 工作流启动 -短信通知 按钮的权限
     * @param userId
     * @return
     * @throws RepositoryException
     */
    public boolean getPass(String userId) throws RepositoryException {
        String sql = "SELECT COUNT(*) NUM FROM IEAI_USER U LEFT JOIN ieai_userinherit H ON U.IID = H.IUSERID LEFT JOIN ieai_highoper_permission P ON H.IROLEID = P.IROLEID WHERE U.ILOGINNAME = ? AND P.IMENUBUTTONID = 10012 ";
        _log.info("userId:" + userId);
        if ("sa".equals(userId)) {
            return true;
        }
        int count = 0;
        boolean result = false;
        Connection con = null;
        PreparedStatement ps = null;
        ResultSet rs = null;

        try {
            con = DBResource.getConnection(Thread.currentThread().getStackTrace()[1].getMethodName(), _log, Constants.IEAI_IEAI_BASIC);
            ps = con.prepareStatement(sql);
            ps.setString(1, userId);
            rs = ps.executeQuery();
            while (rs.next()) {
                count = rs.getInt("NUM");
                if (count > 0) {
                    result = true;
                }
            }

        } catch (SQLException e) {
            _log.error("getPass is error", e);
        } finally {
            DBResource.closeConn(con,rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }
        return result;
    }

    /**
     * @ClassName:  UpdateRuninfoIstateThread
     * @Description:中信压力测试查询状态异常数据进行修改
     * @author: liushuai
     * @date:   2022年5月11日
     * @Copyright: 2018-2027 www.idealinfo.com Inc. All rights reserved.
     */
    private class UpdateRuninfoIstateThread extends Thread
    {
        public void run ()
        {
            while (true)
            {
                try
                {
                    List<String> iflowidList24 = null;
                    List<String> iflowidList25 = null;
                    long time = ServerEnv.getServerEnv().getupdateRuninfoStatePollingTime();
                    try
                    {
                        iflowidList24 = ReleaseMonitorManager.getInstance().getIflowid24();
                        iflowidList25 = ReleaseMonitorManager.getInstance().getIflowid25();
                        if(iflowidList24!=null&&!iflowidList24.isEmpty()) {
                            for(String id : iflowidList24) {
                                if(0!=Long.parseLong(id)) {
                                    boolean success = ReleaseMonitorManager.getInstance().updateRuninfo24(id);
                                    if(success) {
                                        _log.info("查询状态异常数据进行修改表IEAI_RUNINFO_INSTANC，IFLOWID："+id);
                                    }
                                }
                            }
                        }else {
                            _log.info("补偿线程：完成或终止状态，当前没有需要补偿的工作流集合");
                        }

                        if(iflowidList25!=null&&!iflowidList25.isEmpty()) {
                            for(String id : iflowidList25) {
                                if(0!=Long.parseLong(id)) {
                                    boolean success = ReleaseMonitorManager.getInstance().updateRuninfo25(id);
                                    if(success) {
                                        _log.info("查询状态异常数据进行修改表IEAI_RUNINFO_INSTANC，IFLOWID："+id);
                                    }
                                }
                            }
                        }else {
                            _log.info("补偿线程：灾难恢复状态，当前没有需要补偿的工作流集合");
                        }
                    } catch (RepositoryException e1)
                    {
                        e1.printStackTrace();
                    }

                    Thread.sleep(1 * 1000L * time);
                } catch (InterruptedException ex)
                {
                    _log.error(ex);
                }
            }
        }
    }
}
