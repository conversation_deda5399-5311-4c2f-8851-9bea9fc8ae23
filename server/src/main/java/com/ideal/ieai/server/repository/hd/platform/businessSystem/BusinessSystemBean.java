package com.ideal.ieai.server.repository.hd.platform.businessSystem;

import java.io.Serializable;

/**
 * 
 * 名称: BusinessSystemBean.java<br>
 * 描述: 业务系统实体类<br>
 * 类型: JAVA<br>
 * 最近修改时间:2015年10月13日<br>
 * 
 * <AUTHOR>
 */
public class BusinessSystemBean implements Serializable
{
    public BusinessSystemBean()
    {
    }
    private long   systemId;
    private String sysName;
    private String sysType;
    private int    sysState;
    private int    priority;
    private String sysDesc;
    private int    prjType;
    private String sysParam;
    private String sendCycle = "";
    private String mails = "";
    private Boolean    mailSendStatus = false;
    private int pkgid;
    private long   mailId;
    private String systemCode;
    private String systemNumber;
    
    private long userId;
    private String userName;
    
    private long    upid;
    private long    lastid;
    
    private int     mailStatus;
    private int dayType;
    private String summary = "";
    private int     freeze         = 0;
    private int     runPrj         = 0;

    private String  prjVersion     = "";
    private int     excelPrj       = 0;
    private String  icooment;
    private String  iuploadNumber;
    private String  iuploadTime;
    private String  iuploadUser;
    private String  oldSysName="";
    private String  ipmpSysName;//IPMP业务系统名称，浦发用
    private long    ipmpIid;//IEAI_IPMP_SYSTEM_RELATON主键
    private String  ipmpNoExistSysName;//IPMP平台不存在业务系统名称
    private String  iywxtiid;
    private String  sysCode;//IPMP系统编号
    private String  developer;//开发人员
    private String  department;//所属部门
    
    private String  cmdbSystemId;  //邮储cmdb同步使用
    private boolean existsRecord = false;
    private  int isNew;
    
    private Integer icansync;
    private String systemNumberCicd;//系统编号
    private String systemAbbreviation;//系统简称
    private String asyToUse;//系统负责人
    private String systemStatus;//系统状态   01规划，02在建，03运行，04改造，05下线，06停用
    private String ifContinuousIntegration;//是否持续集成   0否  1是
    private String ifAutoDeploy;//是否自动化部署  0否  1是
    private String isysbingagent;//业务系统与agent绑定关系 通过agent名称全匹配绑定
    private String asytouseManager;//配置管理员
    private String asytodetOffice;//科室
    private String systemOwnerTel;//系统负责人电话号

    private String handStart;//是否手工发起工程
    
    private String domainName;// 执行域名称

    public String getDomainName() {
        return domainName;
    }

    public void setDomainName(String domainName) {
        this.domainName = domainName;
    }

    public String getHandStart ()
    {
        return handStart;
    }

    public void setHandStart ( String handStart )
    {
        this.handStart = handStart;
    }

    public String getAsytouseManager ()
    {
        return asytouseManager;
    }
    public void setAsytouseManager ( String asytouseManager )
    {
        this.asytouseManager = asytouseManager;
    }
    public String getAsytodetOffice ()
    {
        return asytodetOffice;
    }
    public void setAsytodetOffice ( String asytodetOffice )
    {
        this.asytodetOffice = asytodetOffice;
    }
    public String getSystemNumberCicd ()
    {
        return systemNumberCicd;
    }
    public void setSystemNumberCicd ( String systemNumberCicd )
    {
        this.systemNumberCicd = systemNumberCicd;
    }
    public String getSystemAbbreviation ()
    {
        return systemAbbreviation;
    }
    public void setSystemAbbreviation ( String systemAbbreviation )
    {
        this.systemAbbreviation = systemAbbreviation;
    }
    public String getAsyToUse ()
    {
        return asyToUse;
    }
    public void setAsyToUse ( String asyToUse )
    {
        this.asyToUse = asyToUse;
    }
    public String getSystemStatus ()
    {
        return systemStatus;
    }
    public void setSystemStatus ( String systemStatus )
    {
        this.systemStatus = systemStatus;
    }
    public String getIfContinuousIntegration ()
    {
        return ifContinuousIntegration;
    }
    public void setIfContinuousIntegration ( String ifContinuousIntegration )
    {
        this.ifContinuousIntegration = ifContinuousIntegration;
    }
    public String getIfAutoDeploy ()
    {
        return ifAutoDeploy;
    }
    public void setIfAutoDeploy ( String ifAutoDeploy )
    {
        this.ifAutoDeploy = ifAutoDeploy;
    }

    public int getIsNew() {
        return isNew;
    }

    public void setIsNew(int isNew) {
        this.isNew = isNew;
    }

    public String getIcooment ()
    {
        return icooment;
    }

    public void setIcooment ( String icooment )
    {
        this.icooment = icooment;
    }

    public String getIuploadNumber ()
    {
        return iuploadNumber;
    }

    public void setIuploadNumber ( String iuploadNumber )
    {
        this.iuploadNumber = iuploadNumber;
    }

    public String getIuploadTime ()
    {
        return iuploadTime;
    }

    public void setIuploadTime ( String iuploadTime )
    {
        this.iuploadTime = iuploadTime;
    }

    public String getIuploadUser ()
    {
        return iuploadUser;
    }

    public void setIuploadUser ( String iuploadUser )
    {
        this.iuploadUser = iuploadUser;
    }
    public long getMailId ()
    {
        return mailId;
    }

    public void setMailId ( long mailId )
    {
        this.mailId = mailId;
    }

    public String getSendCycle ()
    {
        return sendCycle;
    }

    public void setSendCycle ( String sendCycle )
    {
        this.sendCycle = sendCycle;
    }

    public String getMails ()
    {
        return mails;
    }

    public void setMails ( String mails )
    {
        this.mails = mails;
    }

    public Boolean getMailSendStatus ()
    {
        return mailSendStatus;
    }

    public void setMailSendStatus ( Boolean mailSendStatus )
    {
        this.mailSendStatus = mailSendStatus;
    }

    public int getPrjType ()
    {
        return prjType;
    }

    public void setPrjType ( int prjType )
    {
        this.prjType = prjType;
    }

    public String getSysParam ()
    {
        return sysParam;
    }

    public void setSysParam ( String sysParam )
    {
        this.sysParam = sysParam;
    }

    public long getSystemId ()
    {
        return systemId;
    }

    public void setSystemId ( long systemId )
    {
        this.systemId = systemId;
    }

    public String getSysName ()
    {
        return sysName;
    }

    public void setSysName ( String sysName )
    {
        this.sysName = sysName;
    }

    public String getSysType ()
    {
        return sysType;
    }

    public void setSysType ( String sysType )
    {
        this.sysType = sysType;
    }

    public int getSysState ()
    {
        return sysState;
    }

    public void setSysState ( int sysState )
    {
        this.sysState = sysState;
    }

    public int getPriority ()
    {
        return priority;
    }

    public void setPriority ( int priority )
    {
        this.priority = priority;
    }

    public String getSysDesc ()
    {
        return sysDesc;
    }

    public void setSysDesc ( String sysDesc )
    {
        this.sysDesc = sysDesc;
    }

    public int getPkgid ()
    {
        return pkgid;
    }

    public void setPkgid ( int pkgid )
    {
        this.pkgid = pkgid;
    }

    public long getUpid ()
    {
        return upid;
    }

    public void setUpid ( long upid )
    {
        this.upid = upid;
    }

    public long getLastid ()
    {
        return lastid;
    }

    public void setLastid ( long lastid )
    {
        this.lastid = lastid;
    }

    public long getUserId ()
    {
        return userId;
    }

    public void setUserId ( long userId )
    {
        this.userId = userId;
    }

    public String getUserName ()
    {
        return userName;
    }

    public void setUserName ( String userName )
    {
        this.userName = userName;
    }
    
    public String getSystemCode ()
    {
        return systemCode;
    }

    public void setSystemCode ( String systemCode )
    {
        this.systemCode = systemCode;
    }

    public int getMailStatus ()
    {
        return mailStatus;
    }

    public void setMailStatus ( int mailStatus )
    {
        this.mailStatus = mailStatus;
    }

    public int getDayType ()
    {
        return dayType;
    }

    public void setDayType ( int dayType )
    {
        this.dayType = dayType;
    }

    public String getSummary ()
    {
        return summary;
    }

    public void setSummary ( String summary )
    {
        this.summary = summary;
    }

    public int getFreeze ()
    {
        return freeze;
    }

    public void setFreeze ( int freeze )
    {
        this.freeze = freeze;
    }

    public int getRunPrj ()
    {
        return runPrj;
    }

    public void setRunPrj ( int runPrj )
    {
        this.runPrj = runPrj;
    }

    public String getPrjVersion ()
    {
        return prjVersion;
    }

    public void setPrjVersion ( String prjVersion )
    {
        this.prjVersion = prjVersion;
    }

    public int getExcelPrj ()
    {
        return excelPrj;
    }

    public void setExcelPrj ( int excelPrj )
    {
        this.excelPrj = excelPrj;
    }

    public String getOldSysName ()
    {
        return oldSysName;
    }

    public void setOldSysName ( String oldSysName )
    {
        this.oldSysName = oldSysName;
    }

    public String getIpmpSysName ()
    {
        return ipmpSysName;
    }

    public void setIpmpSysName ( String ipmpSysName )
    {
        this.ipmpSysName = ipmpSysName;
    }

    public String getIpmpNoExistSysName ()
    {
        return ipmpNoExistSysName;
    }

    public void setIpmpNoExistSysName ( String ipmpNoExistSysName )
    {
        this.ipmpNoExistSysName = ipmpNoExistSysName;
    }

    public long getIpmpIid ()
    {
        return ipmpIid;
    }

    public void setIpmpIid ( long ipmpIid )
    {
        this.ipmpIid = ipmpIid;
    }

    public String getIywxtiid ()
    {
        return iywxtiid;
    }

    public void setIywxtiid ( String iywxtiid )
    {
        this.iywxtiid = iywxtiid;
    }

    public String getSystemNumber ()
    {
        return systemNumber;
    }

    public void setSystemNumber ( String systemNumber )
    {
        this.systemNumber = systemNumber;
    }

    public String getSysCode ()
    {
        return sysCode;
    }

    public void setSysCode ( String sysCode )
    {
        this.sysCode = sysCode;
    }

    public String getCmdbSystemId ()
    {
        return cmdbSystemId;
    }

    public void setCmdbSystemId ( String cmdbSystemId )
    {
        this.cmdbSystemId = cmdbSystemId;
    }

    public boolean isExistsRecord ()
    {
        return existsRecord;
    }

    public void setExistsRecord ( boolean existsRecord )
    {
        this.existsRecord = existsRecord;
    }

    public Integer getIcansync ()
    {
        return icansync;
    }

    public void setIcansync ( Integer icansync )
    {
        this.icansync = icansync;
    }

    public String getDeveloper ()
    {
        return developer;
    }

    public void setDeveloper ( String developer )
    {
        this.developer = developer;
    }

    public String getDepartment ()
    {
        return department;
    }

    public void setDepartment ( String department )
    {
        this.department = department;
    }

    public String getIsysbingagent() {
        return isysbingagent;
    }

    public void setIsysbingagent(String isysbingagent) {
        this.isysbingagent = isysbingagent;
    }

    public String getSystemOwnerTel() {
        return systemOwnerTel;
    }

    public void setSystemOwnerTel(String systemOwnerTel) {
        this.systemOwnerTel = systemOwnerTel;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("BusinessSystemBean{");
        sb.append("systemId=").append(systemId);
        sb.append(", sysName='").append(sysName).append('\'');
        sb.append(", sysType='").append(sysType).append('\'');
        sb.append(", systemCode='").append(systemCode).append('\'');
        sb.append(", systemNumber='").append(systemNumber).append('\'');
        sb.append(", developer='").append(developer).append('\'');
        sb.append(", department='").append(department).append('\'');
        sb.append(", systemOwnerTel='").append(systemOwnerTel).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
