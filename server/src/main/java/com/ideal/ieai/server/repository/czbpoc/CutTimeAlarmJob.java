package com.ideal.ieai.server.repository.czbpoc;

import org.apache.log4j.Logger;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

/**
 * 日切时间报警检查定时任务
 * 使用Quartz定时调度，在每天的日切时间点执行检查
 */
public class CutTimeAlarmJob implements Job {

    private static final Logger LOGGER = Logger.getLogger(CutTimeAlarmJob.class);
    private static final CutTimeAlarmChecker alarmChecker = new CutTimeAlarmChecker();

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        try {
            // 从JobDataMap中获取日切时间
            String cutTime = context.getJobDetail().getJobDataMap().getString("cutTime");
            if (cutTime == null || cutTime.trim().isEmpty()) {
                LOGGER.error("未找到日切时间参数，跳过执行");
                return;
            }

            LOGGER.info("开始执行日切时间报警检查任务，日切时间: " + cutTime);

            // 执行指定日切时间的报警检查
            alarmChecker.checkCutTimeAlarms(cutTime);
            LOGGER.info("日切时间报警检查任务执行完成，日切时间: " + cutTime);

        } catch (Exception e) {
            LOGGER.error("日切时间报警检查任务执行异常: " + e.getMessage(), e);
            throw new JobExecutionException("日切时间报警检查任务执行失败", e);
        }
    }
}
