/**
 * 
 */
package com.ideal.ieai.server.engine.execactivity;

import com.alibaba.fastjson.JSONObject;
import com.dhcc.itsm.cmdb.webservice.server.service.alarm.Alarm;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.commons.UserInfo;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.core.IEAIRuntime;
import com.ideal.ieai.core.MonitorEnv;
import com.ideal.ieai.core.activity.ActStateData;
import com.ideal.ieai.core.activity.ActivityException;
import com.ideal.ieai.core.element.Project;
import com.ideal.ieai.core.element.Workflow;
import com.ideal.ieai.core.element.workflow.ActivityElement;
import com.ideal.ieai.core.element.workflow.BasicActElement;
import com.ideal.ieai.core.element.workflow.CallWorkflowActElement;
import com.ideal.ieai.server.ServerException;
import com.ideal.ieai.server.SystemConfig;
import com.ideal.ieai.server.compare.repository.comparebase.CompareBaseManage;
import com.ideal.ieai.server.compare.repository.syncresult.SyncResultManage;
import com.ideal.ieai.server.emergency.repository.monitor.sendmessage.EMSendMonitorAlermTread;
import com.ideal.ieai.server.engine.Engine;
import com.ideal.ieai.server.engine.core.ActivityContextManager;
import com.ideal.ieai.server.engine.exceptionhandler.ExceptionHandingHelper;
import com.ideal.ieai.server.engine.exceptionhandler.IExceptionHandingHelper;
import com.ideal.ieai.server.engine.util.ExecError;
import com.ideal.ieai.server.engine.util.IExecContext;
import com.ideal.ieai.server.engine.util.RuntimeEnv;
import com.ideal.ieai.server.ieaikernel.ConfigReader;
import com.ideal.ieai.server.ieaikernel.PersonalityEnv;
import com.ideal.ieai.server.ieaikernel.ServerEnv;
import com.ideal.ieai.server.jobscheduling.bean.alarm.AlarmParmsBean;
import com.ideal.ieai.server.jobscheduling.bean.runtimevalid.RuntimeValidPart;
import com.ideal.ieai.server.jobscheduling.repository.activity.ActivityReDoManager;
import com.ideal.ieai.server.jobscheduling.repository.home.StringUtils;
import com.ideal.ieai.server.jobscheduling.repository.onsMessage.OnsProducerManager;
import com.ideal.ieai.server.jobscheduling.thread.ActRuntimeValThread;
import com.ideal.ieai.server.jobscheduling.util.DateUtil;
import com.ideal.ieai.server.platform.warnmanage.IeaiWarnModel;
import com.ideal.ieai.server.platform.warnmanage.WarningManage;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.commons.bean.TaskMailParamBean;
import com.ideal.ieai.server.repository.commons.excel.DailyOperationExcel;
import com.ideal.ieai.server.repository.commons.mail.SendEmailForApmManager;
import com.ideal.ieai.server.repository.commons.mail.SendEmailForAzManager;
import com.ideal.ieai.server.repository.commons.mail.SendEmailManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.engine.EngineRepository;
import com.ideal.ieai.server.repository.engine.EngineRepositotyJdbc;
import com.ideal.ieai.server.repository.hd.cicd.configitem.CI_CONFIGFILEService;
import com.ideal.ieai.server.repository.hd.cicd.configitem.ItemFlowEnum;
import com.ideal.ieai.server.repository.hd.ic.hcflow.HcRunAlarmRecordSaveThread;
import com.ideal.ieai.server.repository.hd.ic.hcwarn.HcSendWarnToInnerPlatformThread;
import com.ideal.ieai.server.repository.hd.releaseMonitor.ReleaseMonitorManager;
import com.ideal.ieai.server.repository.hd.resGrpState.ResGrpStateManager;
import com.ideal.ieai.server.repository.hd.shellinfo.ShellInfoManager;
import com.ideal.ieai.server.repository.hd.switchMonitor.EswitchRunModel;
import com.ideal.ieai.server.repository.hd.switchMonitor.SwitchMonitorManage;
import com.ideal.ieai.server.repository.lob.LobStorer;
import com.ideal.ieai.server.repository.monitor.*;
import com.ideal.ieai.server.repository.project.ProjectManager;
import com.ideal.ieai.server.repository.sus.flowstart.FlowstartinstversionSendMessService;
import com.ideal.ieai.server.repository.sus.testcenter.SusTestFlowInfoManager;
import com.ideal.ieai.server.repository.topo.toposys.TopoSendTriPoolThread;
import com.ideal.ieai.server.repository.warn.WarnConfigManager;
import com.ideal.ieai.server.repository.warn.WarnMessage;
import com.ideal.ieai.server.util.*;
import com.ideal.ieai.server.util.gznxSendMess.GznxSendMessThread;
import com.ideal.ieai.server.warn.HttpWarnMessage;
import com.ideal.util.StringUtil;
import org.apache.log4j.Logger;
import org.objectweb.jotm.Current;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <li>Title: AbstractActExecutor.java</li>
 * <li>Project: server</li>
 * <li>Package: com.ideal.ieai.server.engine.execactivity</li>
 * <li>Description: It is used to implement the common behavior of IActExecutor object.
 * <li>
 * <li>Copyright: Copyright (c) 2006</li>
 * <li>Company: IdealTechnologies</li>
 * <li>Created on 07-Jun-2006, 15:11:59</li>
 * 
 * <AUTHOR> liancheng_gu
 * @version iEAI v3.5
 */
public abstract class AbstractActExecutor implements IActExecutor
{
    private Thread              _thread        = null;

    // private boolean _killed = false;

    private String              _threadName    = null;

    private String              _oldThreadName = null;

    protected IExecContext      _execCtx       = null;

    protected ExecAct           _execAct       = null;

    protected BasicActElement   _actElem       = null;

    static private final Logger _log           = Logger.getLogger(AbstractActExecutor.class);

    /**
     * Constructor.
     * 
     * @param actElem
     */
    public AbstractActExecutor(ExecAct execAct, BasicActElement actElem, IExecContext execCtx)
    {
        _execCtx = execCtx;
        _execAct = execAct;
        _actElem = actElem;
        _threadName = actElem.getName();
    }

    // /**
    // * @see com.ideal.ieai.server.engine.execactivity.IActExecutor#kill()
    // */
    // synchronized public void kill ( Thread killerThread )
    // {
    // if (_killed == true)
    // {
    // return;
    // }
    // _killed = true;
    // if (_thread != null && _thread != killerThread)
    // {
    // _thread.stop();
    // }
    // }

    // /**
    // * @see com.ideal.ieai.server.engine.execactivity.IActExecutor#isKilled()
    // */
    // synchronized public boolean isKilled ()
    // {
    // return _killed;
    // }

    /**
     * @see com.ideal.ieai.server.engine.execactivity.IActExecutor#run()
     */
    public void run ()
    {
        try
        {
            synchronized (this)
            {
                // if (_killed == true)
                // {
                // return;
                // }
                _thread = Thread.currentThread();
                _oldThreadName = _thread.getName();
                _thread.setName(_threadName);
            }
            RuntimeEnv.setExecContext(_execCtx);
            // add a check condition for efficiency
            if (null != _execCtx && needSetupTransaction() && _execCtx.isInTransaction())
            { // Associate
              // current thread to transaction.
                Current.getCurrent().setPropagationContext(_execCtx.getTrCtx(), false);
            }
            try
            {
                // for time out process to delay act run.
                Date delayTo = _execAct.getDelayTo();
                if (delayTo != null && System.currentTimeMillis() < delayTo.getTime())
                {
                    Engine.getInstance().getScheduler().updateExecActAndMakeItPendingForIp(_execAct);
                } else
                {
                    try
                    {
                        //TODO 新流程调用子步骤426
                        localExec();
                    } catch (Throwable ex)
                    {
                        if ((ex.getClass().toString()).indexOf("java.lang.OutOfMemoryError") > -1)
                        {
                            try
                            {
                                if (MonitorEnv.getBooleanConfig("ENTEGORWARN", false)
                                        && MonitorEnv.getBooleanConfig("SYSMEMERR", false))
                                {
                                    WarningManager.getInstance().getOutOfMemoryError(ex.getMessage());
                                }
                            } catch (Throwable e)
                            {
                                _log.error("monitor OUTOFMEMORY:" + e);
                            }
                        }
                        throw new ActivityException(ex);
                    }
                }
            } catch (ActivityException ex)
            {
                _log.error("act is ActivityException", ex);
                _log.info("flowId: " + _execAct.getFlowId() + " actName:" + _execAct.getActName());
                long end = System.currentTimeMillis();

                // 监控活动系统异常 5是异常类型 系统异常 add by tao_ding 2010-7-21
                if (MonitorEnv.getBooleanConfig("ENTEGORWARN", false) && MonitorEnv.getBooleanConfig("SYSERR", false))
                {
                    String message = null;
                    if (null == ex.getMessage())
                    {
                        message = ex.getDetailMessage();
                    } else
                    {
                        message = ex.getMessage();
                    }

                    WarningManager.getInstance().getSysExc(_actElem, _execAct, message);
                    
                }
                if(IEAIRuntime.current().getProject().getProjectTpye() == Constants.IEAI_IEAI && 0 != _execAct.getFlowId()) {
//                    ActivityException exxxi= (ActivityException) ex.getCause();//ActivityException.AGENT_COMMUNICATION_EXCEPTION .equals(exxxi.getLocation()) &&
                    String iflag = Constants.ACT_STATE_FAIL;
                    if(_execAct.getFlag() == 5) {//flag等于5为业务异常
                        iflag = Constants.ACT_STATE_FAIL_BUSINESS;
                    }
                    TopoSendTriPoolThread.getInstance().addRequest(_execAct.getFlowId(), _execAct.getActName(), 0, iflag,
                            end, 1);

                }
                //如果是巡检工程，执行成功没有报错的情况下，去对记录表中的记录进行删除
                //添加开关是否走此逻辑
                try {
                    boolean hcAlarmStrategyToStophcSwitch = Environment.getInstance().getHcAlarmStrategyToStophcSwitch();
                    if(IEAIRuntime.current().getProject().getProjectTpye() == Constants.IEAI_HEALTH_INSPECTION && hcAlarmStrategyToStophcSwitch  ) {
                        ActivityException exxxi= (ActivityException) ex.getCause();
                        if(ActivityException.AGENT_COMMUNICATION_EXCEPTION .equals(exxxi.getLocation())) {
                            //调用线程存储
                            new HcRunAlarmRecordSaveThread(_execAct).start();
                        }
                    }
                }catch (Exception e) {
                    _log.error("巡检活动保存异常记录操作失败", e);
                }
                
                //进行巡检操作告警发送统一告警集成开始
                Boolean unifiledkg =  ServerEnv.getInstance().getBooleanConfig(ServerEnv.HC_WARN_SEND_UNIFIED_PLATFORM_SWITCH, false);
                _log.info("巡检发送统一内部告警集成开关："+unifiledkg+",当前工程类型："+IEAIRuntime.current().getProject().getProjectTpye()+"当前动作节点【"+_execAct.getActName()+"】");
                if(IEAIRuntime.current().getProject().getProjectTpye() == Constants.IEAI_HEALTH_INSPECTION && unifiledkg ) {
                    _log.info("进入巡检agent连接异常告警发送。。。。。");
                    try {
                        ActivityException exxi= (ActivityException) ex.getCause();
                        _log.info("当前location："+exxi.getLocation());
                        //agent 连接异常
                        if(ActivityException.AGENT_COMMUNICATION_EXCEPTION .equals(exxi.getLocation())) {
                            _log.info("确认是巡检agent连接异常告警发送。。。。。");
                            new HcSendWarnToInnerPlatformThread(null, null, 2, _execAct.getFlowId(),  _execAct.getActName(), exxi.getMessage()).start();
                        }
                        _log.info("完成巡检agent连接异常告警发送。。。。。");
                
                    }catch (Exception e) {
                        _log.info("发送统一告警集成出错！"+e.getMessage());
                    }
               }
                //进行巡检操作告警发送统一告警集成结束




                // 作业调度中添加shell接口业务异常后向监控平台报警。
                if (_execAct.getFlag() == 5 && ServerEnv.getServerEnv().isHttpWarnSwitch()
                        && IEAIRuntime.current().getProject().getProjectTpye() == Constants.IEAI_IEAI)
                {
                    HttpWarnMessage warnMsg = new HttpWarnMessage();
                    String host = Environment.getInstance().getServerIP();
                    String[] actInfo = WarningService.getInstance().getActInfoByActId(_execAct.getSerialNo());
                    long flowId = _execAct.getFlowId();
                    String prjName = "";
                    String flowName = "";
                    if (actInfo != null && actInfo.length > 0)
                    {
                        prjName = actInfo[2];
                        flowName = actInfo[1];
                    }
                    MonitorSystem ms = MonitorSystem.getInstance();
                    String agentIp = ms.getActAgentIp(flowId, _execAct.getRexecRequestId());
                    String warnMessage = "";
                    SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss:SSS");
                    warnMsg.sendMessage(host, null, format.format(new Date()), prjName, flowName, 3, warnMessage,
                        Constants.IEAI_IEAI,"作业调度");

                    /* 上海shellcmd接口异常自动调用Server服务器上的指定脚本并传参开始 */
                    // 作业调度上海银行告警信息开关
                    Boolean warningSHKG =  ServerEnv.getInstance().getBooleanConfig(ServerEnv.IEAI_WARNING_MONITOR_SHANGHAI_SWITCH, false);

                    /** add by xinglili 20220910 西安-调度平台与行内报警平台对接 */
                    Boolean warningXiAn =  ServerEnv.getInstance().getBooleanConfig(ServerEnv.IEAI_WARNING_MONITOR_XIAN_SWITCH, false);

                    if (warningSHKG) { // 组织上海银行告警信息
                        String warnSHMessage = "作业调度业务异常";
                        String instName = WarningService.getInstance().getShangHaiFlowInsName (flowId);
                        // 告警分组 project的 systemCode
                        String eventGroup = WarningService.getInstance().getShangHaiEventGroup(prjName,
                                Constants.IEAI_IEAI);
                        // 告警摘要
                        String eventName = WarningService.getInstance().getShangHaiEventName(prjName,
                                flowName,
                                instName,
                                warnSHMessage);

                        String lineContent = WarningService.getInstance().getShangHaiLineContent(flowId,flowName);
                        // 告警信息
                        String eventInfo = WarningService.getInstance().getShangHaiEventInfo(prjName,
                                flowName,
                                _execAct.getActName(),
                                warnSHMessage,
                                agentIp,
                                lineContent);

                        Integer eventLevel = WarningService.getInstance().getShangHaiEventLevel(prjName,flowName,_execAct.getActName());
                        if (eventLevel == null) {
                            eventLevel = 3;
                        }
                        String eventLevelS = "P" + eventLevel;
                        WarningInterfaceUtilsIEAI.callWarning(eventGroup, eventName, eventLevelS, host, eventInfo, new Date(),prjName,flowName,_execAct.getActName(),flowId,null,agentIp);
                        /* 上海shellcmd接口异常自动调用Server服务器上的指定脚本并传参结束 */
                    }  else if (warningXiAn) {
                        String platformName = "统一调度管理平台";//平台名称：统一调度管理平台
                        String actName = _execAct.getActName();//活动名称
                        // 告警触发为1，告警恢复为0
                        String warnLevel =  "1";//WarningService.getInstance().getXianEventErrorType(flowId);
                        String typeCode = "IEAI_SYSTEMEXCEPTION"; // 系统异常
                        //告警触发 告警恢复：
                        String lineContent = WarningService.getInstance().getXiAnLineContent(flowId,actName,"1");//最后一行返回值
                        StringBuilder sb = new StringBuilder(platformName);
                        sb.append(prjName + "中" + flowName);
                        sb.append("下的" +actName + "运行异常");
                        String warnMsgStr = sb.toString(); // 异常描述
                        WarningInterfaceUtilsIEAI.callWarning(lineContent, //最后一行返回值
                                                            typeCode ,      //报警类型
                                                            warnLevel,      // 告警触发为1，告警恢复为0
                                                            Environment.getInstance().getServerIP(), //主机IP
                                                            warnMsgStr,     // 异常描述
                                                            new Date(),
                                                            prjName,        //工程名称
                                                            flowName,
                                                            actName,        //活动名称
                                                            flowId,
                                                            null,
                                                            agentIp);
                        _log.info( "AbstractActExecutor--发送告警，工程名："+ prjName+ ",活动名："+ flowName + ",活动Id：" + flowId +
                                " ，告警类型："+ typeCode +
                                " ，告警warnId：无"+
                                " ，告警信息："+ warnMsg +
                                "，最后一行："+ lineContent );
                    } else {
                        WarningInterfaceUtilsIEAI.callWarning("IEAI", "IEAI_BUSINESSEXCEPTION", "five", host, "作业调度业务异常", new Date(),prjName,flowName,_execAct.getActName(),flowId,null,agentIp);
                    }

                }

                //交行发送报警
                if(_execAct.getFlag() == 5 && IEAIRuntime.current().getProject().getProjectTpye() == Constants.IEAI_IEAI
                        &&  ServerEnv.getInstance().getBooleanConfigNew2(Environment.IEAI_JH_WARNING_SWITCH, false)){
                
                    String[] actInfo = WarningService.getInstance().getActInfoByActId(_execAct.getSerialNo());
                    String prjName = "";
                    String flowName = "";
                    if (actInfo != null && actInfo.length > 0)
                    {
                        prjName = actInfo[2];
                        flowName = actInfo[1];
                    }
                    WarningInterfaceJHUtilsIEAI.callWarning(_execAct.getFlowId(),prjName,flowName,_execAct.getActName(),"作业调度业务异常");
                }
                // 应用变更自动化接口业务异常后向监控平台报警。
                int a = (int)IEAIRuntime.current().getProject().getProjectTpye();
                if ( _execAct.getFlag() == 5 && ServerEnv.getServerEnv().isHttpWarnSwitch()
                        && a == Constants.IEAI_SUS)
                {
                    HttpWarnMessage warnMsg = new HttpWarnMessage();
                    String host = Environment.getInstance().getServerIP();
                    String[] actInfo = WarningService.getInstance().getActInfoByActId(_execAct.getSerialNo());

                    
                    long iflowid = _execAct.getFlowId();
                    Map<String, String> phaseMap = EngineRepositotyJdbc.getInstance().getSUSPrjInfo(
                        iflowid);
                     String isysname = phaseMap.get("ISYSNAME");         //业务系统
                     String iruninsname = phaseMap.get("IRUNINSNAME");  //流程定制实例
                     String iactname = phaseMap.get("IACTNAME");//
                     String susActEnvName=phaseMap.get("susActEnvName"); //环境名称
                     warnMsg.setSusActEnvName(susActEnvName);
                     warnMsg.setSusActName(iactname);
                     warnMsg.setIflowid(_execAct.getFlowId());
                    
                    String warnMessage = "";
                    SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss:SSS");
                    warnMsg.sendMessage(host, null, format.format(new Date()), iruninsname, iactname, 3, warnMessage,
                        Constants.IEAI_SUS,"应用变更自动化");
                }

                // 应用变更,CICD福建农信自动略过。
                boolean fjnxNewFlag = Environment.getInstance().getBankSwitchIsFjnx();
                if ( _execAct.getFlag() == 5 && fjnxNewFlag && a == Constants.IEAI_SUS)
                {
                    try {
                        if (fjnxNewFlag){
                            if(EngineRepositotyJdbc.getInstance().isAutoSkip(_execAct.getFlowId())){
                                _log.info("福建农信自动略过");
                                long stepId= EngineRepositotyJdbc.getInstance().getRuninfoiid( _execAct.getFlowId()) ;
                                UserInfo userInfo =new UserInfo();
                                userInfo.setId(EngineRepositotyJdbc.getInstance().getStartUserid(_execAct.getFlowId()));
                                ReleaseMonitorManager.getInstance().deploymentOKStep(userInfo, _execAct.getFlowId(), stepId);
                            }
                        }
                    }catch (Exception e){
                        _log.error(e);
                        e.printStackTrace();
                    }
                }



                if (PersonalityEnv.isFlowDmMailSwitchValue()
                        && IEAIRuntime.current().getProject().getProjectTpye() == Constants.IEAI_DAILY_OPERATIONS)
                {
                    // 浦发:标准运维发送活动异常邮件
                    _log.info("SendEmailManager getInstanceIdByFlowId flowId is : " + _execAct.getFlowId()
                            + ",actName is:" + _execAct.getActName());
                    DailyOperationExcel fileExcelManager = new DailyOperationExcel();
                    TaskMailParamBean mailParam = fileExcelManager.getActFailContentBean(_execAct.getFlowId(),
                        _execAct.getActName(), Constants.IEAI_DAILY_OPERATIONS);
                    String taskName = mailParam.getTaskName();
                    String insName = mailParam.getEquiIp();
                    long taskId = mailParam.getTaskId();
                    _log.info("标准运维任务流程节点获取任务ID"+taskId);
                    String errorMsg = "标准运维任务【" + taskName + "】的设备【" + insName + "】下的活动【" + _execAct.getActName()
                            + "】出现异常，异常信息为:" + ex.getCause().getMessage();
                    int count= SendEmailManager.getInstance().queryFlowErrorCount(taskId);
                    if (count>0 || 0 == taskId){
                        _log.info("标准运维任务流程节点任务异常信息已保存");
                    }else{
                        SendEmailManager.getInstance().insertFlowError(taskId,_execAct.getFlowId(),_execAct.getActName(),errorMsg);
                        _log.info("标准运维任务流程节点任务异常信息保存成功");
                    }
                    boolean booleanConfig = ServerEnv.getInstance().getBooleanConfig(Environment.PF_TASK_DM_MAIL_SWITCH, false);
                    if (!booleanConfig){
                    SendEmailManager.getInstance().sendDmStandardTaskEmailThead(_execAct.getFlowId(), true,
                        _execAct.getActName(), errorMsg, 3, Constants.IEAI_DAILY_OPERATIONS);
                    }
                }

                if (Environment.getInstance().getBooleanConfig(PersonalityEnv.PF_TEST_SEND_MESSAGE, false)
                        && IEAIRuntime.current().getProject().getProjectTpye() == Constants.IEAI_SUS)
                {
                    //浦发:发送测试流程节点异常信息
                    ArrayList<Long> list = new ArrayList<Long>();
                    long insId = SendEmailManager.getInstance().getInstanceIdByFlowId(_execAct.getFlowId(),Constants.IEAI_SUS);
                    list.add(insId);
                    SusTestFlowInfoManager.getInstance().sendTestFlowInfo(list, 0);
                }
                // 日常操作添加shell接口业务异常后向监控平台报警。
                if (_execAct.getFlag() == 5 && ServerEnv.getServerEnv().isHttpWarnSwitch()
                        && IEAIRuntime.current().getProject().getProjectTpye() == Constants.IEAI_DAILY_OPERATIONS)
                {
                    HttpWarnMessage warnMsg = new HttpWarnMessage();
                    String host = Environment.getInstance().getServerIP();
                   
                    String[] actInfo = WarningService.getInstance().getDailyOperActInfoByActId(_execAct.getSerialNo(),
                        _execAct.getFlowId());
                    String prjName = "";
                    String flowName = "";
                    String insName = "";
                    if (actInfo != null && actInfo.length > 0)
                    {
                        prjName = actInfo[2];
                        flowName = actInfo[1];
                        insName = actInfo[3];
                    }
                    // 二级子流程的healthCheck下的业务执行脚本预期值不匹配异常
                    if (null != insName && !"".equals(insName))
                    {
                        String warnMessage = "";
                        warnMessage = actInfo[2] + "下的" + actInfo[1] + "流程下的" + actInfo[0] + "活动出现异常(任务名:" + insName
                                + ",工作流Id:" + _execAct.getFlowId() + ",启动人：" + actInfo[4] + ",审核人:" + actInfo[5] + ")";

                        // _log.info("日常操作报警信息：" + warnMessage);
                        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss:SSS");
                        warnMsg.sendMessage(host, null, format.format(new Date()), prjName, flowName, 3, warnMessage,
                            Constants.IEAI_DAILY_OPERATIONS, "日常操作");
                    }
                }

                // add start by wangnan 短信报警平台
                ConfigReader cfg = ConfigReader.getInstance();
                cfg.init();
                boolean smsSendSwitch = cfg.getBooleanProperties(Environment.SMS_SEND_SWITCH,
                    Environment.SMS_SEND_SWITCH_DEFAULT);
                _log.info("smsSendSwitch's flag is : " + smsSendSwitch);
                if (smsSendSwitch)
                {
                    // shellU系统异常、shellU业务异常 发送短信报警平台
                    // sendMessageOfException(_execAct, ex);
                    MessageSendMethod.getInstance().sendMessageOfSystemException(_execAct, ex);
                }
                // add end by wangnan 短信报警平台
                // add by yuxh 20180427 insert node_warning
                String prjName = "";
                String flowName = "";
                if (null == _execAct.getProjectName())
                {
                    _execAct.setProjectName(IEAIRuntime.current().getProjectName());
                }
                if (null == _execAct.getFlowName())
                {
                    _execAct.setFlowName(IEAIRuntime.current().getWorkflowName());
                }
                if (null == _execAct.getPrjuuid())
                {
                    _execAct.setPrjuuid(IEAIRuntime.current().getProject().getUuid());
                }
                if (SystemConfig.isAddWarningSwitch() && _execAct.getFlag() != 5)
                {
                      prjName = _execAct.getProjectName();
                      flowName = _execAct.getFlowName();
                      if(null == prjName){
                          prjName = IEAIRuntime.current().getProjectName();
                      }
                      if(null == flowName){
                          flowName = IEAIRuntime.current().getWorkflowName();
                      }
                    String actName = _execAct.getActName();
                    long flowId = _execAct.getFlowId();
                    MonitorSystem ms = MonitorSystem.getInstance();
                    int event = ms.AGENT_BREAK;
                    String[] actInfo = null;
                    String errMsg = "";
                    actInfo = ms.getActInfo(flowId, actName, _execAct.getRexecRequestId(), "", event, "");
                    actInfo[3] = ms.isEmptyFJ(prjName);
                    actInfo[4] = ms.isEmptyFJ(flowName);
                    actInfo[5] = ms.isEmptyFJ(actName);
                    ms.setEvent(event, actInfo, errMsg);
                    String agentIp = ms.getActAgentIp(flowId, _execAct.getRexecRequestId());
                    long nodeWarnId = ms.addWarning(actInfo[3], actInfo[4], actInfo[5], event, actInfo[8], flowId ,agentIp);
                    if(IEAIRuntime.current().getProject().getProjectTpye() == Constants.IEAI_IEAI){
                         //作业调度agent无法连接
                        if (Environment.getInstance().getHuiShangBankSwitch()) {
                            WarningInterfaceUtilsIEAI.callWarning("IEAI", "IEAI_SYSTEMEXCEPTION", "five", agentIp, "作业调度代理"+actInfo[7]+"无法正常连接", new Date(),prjName,flowName,actName,flowId,nodeWarnId,agentIp);
                        }
                        else {
                            WarningInterfaceUtilsIEAI.callWarning("IEAI", "IEAI_SYSTEMEXCEPTION", "five", Environment.getInstance().getServerIP(), "作业调度代理"+actInfo[7]+"无法正常连接", new Date(),prjName,flowName,actName,flowId,nodeWarnId,agentIp);
                        }

                        /** 渤海发送Mq消息 */
                        if (Environment.getInstance().getBhMqSendSwitch()) {
                            Map<String, String> map = OnsProducerManager.getInstance().sendMq("FAIL", flowId,actName, new Date());
                            String result = map.get("success");
                            /** cmd命令*/
//                            String cmd = "java -jar " + bhMqUrl + " FAIL " + "JobStart" + " " + iflowName + " "
//                                    + insName + " " + "JobEnd" + " " + proName;
                            _log.info("系统异常发送MQ消息命令! " );
//                            boolean result = OnsMessageManager.getInstance().execCmd(cmd);
                            if ("false".equals(result)) {
                                _log.error("工作流系统异常发送MQ消息失败");
                            }
                        }
                    }

                   
                } else if (SystemConfig.isAddWarningSwitch() && _execAct.getFlag() == 5)
                {
                    prjName = _execAct.getProjectName();
                    flowName = _execAct.getFlowName();
                    if(null == prjName){
                        prjName = IEAIRuntime.current().getProjectName();
                    }
                    if(null == flowName){
                        flowName = IEAIRuntime.current().getWorkflowName();
                    }
                    String actName = _execAct.getActName();
                    long flowId = _execAct.getFlowId();
                    MonitorSystem ms = MonitorSystem.getInstance();
                    int event = ms.ACT_BREAK;
                    String errMsg = Environment.getInstance().CAMA_USERTASK;
                    if ((!"ExcelActExecModelSUS".equals(_execAct.getProjectName())) && (!"ExcelActExecModelDR".equals(_execAct.getProjectName())))
                    {
                        String agentIp = ms.getActAgentIp(flowId, _execAct.getRexecRequestId());
                        long nodeWarnId = ms.addWarning(prjName, flowName, actName, event, errMsg, flowId ,agentIp);
                        if(IEAIRuntime.current().getProject().getProjectTpye() == Constants.IEAI_IEAI){
                            //作业调度业务异常
                            if (Environment.getInstance().getHuiShangBankSwitch()) {
                                WarningInterfaceUtilsIEAI.callWarning("IEAI", "IEAI_BUSINESSEXCEPTION", "five", agentIp, "作业调度业务异常", new Date(),prjName,flowName,actName,flowId,nodeWarnId,agentIp);
                            }
                            else {
                                WarningInterfaceUtilsIEAI.callWarning("IEAI", "IEAI_BUSINESSEXCEPTION", "five", Environment.getInstance().getServerIP(), "作业调度业务异常", new Date(),prjName,flowName,actName,flowId,nodeWarnId,agentIp);
                            }
                            /** 渤海发送Mq消息 */
                            if (Environment.getInstance().getBhMqSendSwitch()) {
                                //脚本获取内置环境变量活动名,数据日期,结束标识
                                // 作业名 flowName  子系统名 proName   实例名insName 工作流
                                WarningManage warningInterface = new WarningManage();
                                boolean Redo=false;
                                boolean  success=false;
                                if(!"".equals(actName)){
                                    Redo=  warningInterface.excRedoEstimate(flowId,actName);
                                    if(Redo){
                                        success=true;

                                    }
                                }
                                String result ="";
                                if(success){
                                    Map<String, String> map = OnsProducerManager.getInstance().sendMq("FAIL", flowId,actName, new Date());
                                    result=  map.get("success");
                                }

                                /** cmd命令*/
//                            String cmd = "java -jar " + bhMqUrl + " FAIL " + "JobStart" + " " + iflowName + " "
//                                    + insName + " " + "JobEnd" + " " + proName;
                                _log.info("系统异常发送MQ消息命令! " );
//                            boolean result = OnsMessageManager.getInstance().execCmd(cmd);
                                if ("false".equals(result)) {
                                    _log.error("工作流系统异常发送MQ消息失败");
                                }
                            }
                        }
                    }
                }
                // Create and Save the error infomation of the last exception.
                ExecError lastErr = new ExecError(ex, _execAct);
                EngineRepository.getInstance().saveOrUpdateLastError(lastErr);
                /*
                 * ShellInfoManager.getInstance().updateActStepIsFail(1, _execAct.getFlowId());
                 * _log.info(" 步骤名称："+_actElem.getName()+"出现异常 ,步骤实例ISFAIL状态更改为1。 工作流ID："+_execAct.
                 * getFlowId());
                 */

                String remoteip = (String) _actElem.getInputExprs().get("$normal.agentip");
                String remoteport="";
                if (!StringUtil.isEmptyStr(remoteip) && _actElem instanceof ActivityElement)
                {
                    if (remoteip.startsWith("$"))
                    {
                        Object ret = com.ideal.ieai.server.engine.expreval.ExprEvaluator.eval(remoteip);
                        remoteip = (String) ret;
                    }
                    if (!StringUtil.isEmptyStr(remoteip))
                    {
                        if (remoteip.startsWith("\""))
                        {
                            remoteip = remoteip.substring(1, remoteip.length() - 1);
                        }
                        String[]remoteipitem=remoteip.split(":");
                        remoteip = remoteipitem[0];
                        if(remoteipitem.length>1)
                        {
                            remoteport= remoteipitem[1];
                        }
                    }
                }

                try
                {
                    /**
                     * update by li_yang 为了IP1+IP2+...+IPx这种模式改造 如果当前的remoteip不是最后一个，不要置iisfail字段。
                     */
                    String flowInstance = ShellInfoManager.getInstance().getFlowInstance(_execAct.getFlowId());
                    flowInstance = flowInstance == null ? "" : flowInstance;
                    String[] ips = flowInstance.split("\\+");
                    boolean updateFailFlag = false;
                    if (ips.length > 1)
                    {
                        String lastIp = ips[ips.length - 1];
                        if (lastIp.equals(remoteip))
                        {
                            updateFailFlag = true;
                        }
                    } else
                    {
                        updateFailFlag = true;
                    }

                    if (updateFailFlag)
                    {
                        
                        // 变更状态 到ieai_runinfo_instance 1 代表异常
                        ShellInfoManager.getInstance().updateActStepIsFail(1, _execAct.getFlowId());
                        _log.info("当前remoteIp:" + remoteip + " 步骤名称：" + _actElem.getName()
                                + "出现异常 ,步骤实例ISFAIL状态更改为1。 工作流ID：" + _execAct.getFlowId());
                        
                    }
                } catch (RepositoryException e)
                {
                    _log.error("更改步骤实例表状态失败(1)，工作流ID：" + _execAct.getFlowId());
                }
                
                
                boolean operSendPlateFormSwitch = Environment.getInstance().getBooleanConfig(Environment.EM_SENDMONITORALERM_SWITCH, false);
                _log.info("应急操作异常发送告警平台开关状态为："+operSendPlateFormSwitch);
                if(IEAIRuntime.current().getProject().getProjectTpye() == Constants.IEAI_EMERGENCY_OPER && operSendPlateFormSwitch){
                    EMSendMonitorAlermTread thread = new EMSendMonitorAlermTread(_execCtx.getFlowId());
                    thread.start();
                   
                }

                if (_execAct.getFlag() != 5)
                {
                    // 存异常信息到 output中
                    LobStorer lo = new LobStorer();
                    lo.saveActOutputClob(_execAct.getFlowId(), 0, _actElem.getName(), Long.valueOf(_execAct.getFlag()),
                        null, 0l, new Date(), null, ex.getCause() + "\n" + lastErr.getErrorStackTrace(), "",
                        Constants.IEAI_IEAI_BASIC,"","");

                    _log.info("步骤名称：" + _actElem.getName() + " 出现异常 ,异常信息已存入异常表：" + _execAct.getFlowId());
                    
                    if("ExcelActExecModelDR".equals(IEAIRuntime.current().getProjectName())&&"ActExec_Check".equals(IEAIRuntime.current().getWorkflowName())){
                        try
                        {
                            ResGrpStateManager.getInstance().updateResState(_execAct.getFlowId(), 2, Constants.IEAI_EMERGENCY_SWITCH);
                        } catch (Exception e)
                        {
                            _log.error("get items state is error!",e);
                        }
                    }
                }
              //兴业银行tivoli告警
                if(Environment.getInstance().getCibTivoliSwitch() && 
                        IEAIRuntime.current().getProject().getProjectTpye() == Constants.IEAI_EMERGENCY_SWITCH) {
                    SendCibSyslog syslog = new SendCibSyslog();
                    StringBuilder sb = new StringBuilder();
                    Map<String,String> infoMap = ShellInfoManager.getInstance().getRunInfo(_execAct.getFlowId());
                    if(_execAct.getFlag() == 5) {
                        String mes = "系统名称："+infoMap.get("isysname").toString()+",步骤标识："+infoMap.get("iserner").toString()+
                                ",子步骤名称："+infoMap.get("iactname").toString()+"的步骤出现异常";
                        sb.append("severity=WARNING hostname= ").append("ip="+infoMap.get("iip")).append(" ");
                        sb.append("msg=\""+mes+"\"");
                        _log.info(sb.toString());
                    }else {
                        String mes = "系统名称："+infoMap.get("isysname").toString()+",步骤标识："+infoMap.get("iserner").toString()+
                                ",子步骤名称："+infoMap.get("iactname").toString()+"的步骤代理服务器无法正常连接";
                        sb.append("severity=CRITICAL hostname= ").append("ip="+infoMap.get("iip")).append(" ");
                        sb.append("msg=\""+mes+"\"");
                        _log.info(sb.toString());
                    }
                    _log.info(sb.toString());
                    syslog.sendsysLog(sb.toString());
                }
                if ( SendEmailManager.getInstance().getEmailSwitch()
                        && IEAIRuntime.current().getProject().getProjectTpye() == Constants.IEAI_SUS)
                {
                    //浦发:发送流程节点异常邮件
                    long insId = SendEmailManager.getInstance().getInstanceIdByFlowId(_execAct.getFlowId(),Constants.IEAI_SUS);
                    _log.info("SendEmailManager getInstanceIdByFlowId flowId is : " + _execAct.getFlowId() +",insId is:"+insId);
                    SendEmailManager.getInstance().sendFlowExceptionEmail(insId, true, ex.getMessage(),_execAct.getFlowId());
                }
                if ( ServerEnv.getInstance().getBooleanConfig(PersonalityEnv.PF_APM_MAIL_SWITCH, false))
                {
//                    浦发:应用维护流程异常邮件
                    long insId = SendEmailManager.getInstance().getInstanceIdByFlowId(_execAct.getFlowId(),Constants.IEAI_SUS);
                    _log.info("SendAPMEmailManager getInstanceIdByFlowId flowId is : " + _execAct.getFlowId() +",insId is:"+insId);
                    SendEmailForApmManager.getInstance().sendFlowExceptionEmail(insId, true, ex.getMessage(),_execAct.getFlowId());
                }
                
                if ( ServerEnv.getInstance().getBooleanConfig(PersonalityEnv.PF_AZ_MAIL_SWITCH, false) )
                {
//                    浦发:Az切换流程异常邮件
                    long insId = SendEmailManager.getInstance().getInstanceIdByFlowId(_execAct.getFlowId(),Constants.IEAI_AZ);
                    _log.info("SendAZEmailManager getInstanceIdByFlowId flowId is : " + _execAct.getFlowId() +",insId is:"+insId);
                    SendEmailForAzManager.getInstance().sendFlowExceptionEmail(insId, true, ex.getMessage(),_execAct.getFlowId());
                }
                
                if(Environment.getInstance().getBooleanConfig(PersonalityEnv.PF_SUSANDAPM_SENDALARM_SWITCH,false) && (IEAIRuntime.current().getProject().getProjectTpye() == Constants.IEAI_SUS || IEAIRuntime.current().getProject().getProjectTpye() == Constants.IEAI_APM)){
                    //浦发:应用更更-维护流程异常发送告警通知到睡得香平台
                    long insId = SendEmailManager.getInstance().getInstanceIdByFlowId(_execAct.getFlowId(),Constants.IEAI_SUS);
                    _log.info("SendEmailManager getInstanceIdByFlowId flowId is : " + _execAct.getFlowId() +",insId is:"+insId);
                    SendEmailManager.getInstance().sendWarningNotice4Spdb(insId, true);
                }
                
                if ( IEAIRuntime.current().getProject().getProjectTpye() == Constants.IEAI_SUS)
                {
                    //变更流程异常发送短信息
                    FlowstartinstversionSendMessService.getInstance().sendMessageSus3(_execAct.getFlowId(),Constants.IEAI_SUS);
//                    if ( ServerEnv.getInstance().getBooleanConfig(ServerEnv.SUS_SENDTOZHONGYUAN_SWITCH, false))
//                    {
//                        //中原:异步返回报错信息
//                        FlowstartinstversionSendErrMessService.getInstance().sendMessageSus1(_execAct.getFlowId(),Constants.IEAI_SUS);
//                    }
                    //记录告警信息
                    String isysname = "";         //业务系统
                    String iruninsname = "";  //流程定制实例
                    String iactname =  "";//活动名
                    Map<String, String> phaseMap =null;
                    int susType=WarningInterfaceUtilsSus.getSusType( _execAct.getFlowId());
                    if(susType==1)
                    {
                        //图形版
                        phaseMap = EngineRepositotyJdbc.getInstance().getSUSPrjInfo(
                            _execAct.getFlowId());
                    }else
                    {
                        //excel版
                        phaseMap =WarningInterfaceUtilsSus.getSUSPrjInfo( _execAct.getFlowId());
                    }
                    
                    if(phaseMap!=null)
                    {
                        isysname = phaseMap.get("ISYSNAME");         //业务系统
                        iruninsname = phaseMap.get("IRUNINSNAME");  //流程定制实例
                        iactname = phaseMap.get("IACTNAME");//活动名 
                    }
                    if(_execAct.getFlag() == 5)
                    {
                        List<Map<String,Object>> returnList = getTopoInfoNew(_execAct.getFlowId());
                        for (Map<String,Object> returnMap : returnList) {
                            try{
                                IeaiWarnModel iwm=new IeaiWarnModel();

                                iwm.setImodulecode("SUS");
                                iwm.setItypecode("SUS_BUSINESSEXCEPTIONS");
                                iwm.setStartTime(returnMap.get("istarttime").toString());
                                //启动用户，执行人
                                iwm.setInitUser(returnMap.get("ISTARTUSER").toString());
                                //异常步骤名
                                iwm.setiErrorStepName(_actElem.getName());
                                iwm.setIlevelcode( "five");
                                //说明
                                iwm.setDescription(returnMap.get("IRUNINSNAME").toString());
                                //单号
                                iwm.setCode(returnMap.get("IVERSION").toString());
                                //异常ip
                                iwm.setIip(remoteip);
                                //告警信息
                                iwm.setIwarnmsg("应用变更业务系统异常");
                                //报警时间
                                iwm.setIhappentime(new Date());
                                iwm.setIhappentimeStr(DateUtil.formatDateTime(new Date()));
                                //系统名称
                                iwm.setIbusinessName(isysname==null?"":isysname);
                                iwm.setIsystemname(isysname==null?"":isysname);
                                iwm.setIswitchName(iruninsname);
                                iwm.setIsonStepName(iactname);
                                _log.info("变更异常推送告警平台:[{description="+iwm.getDescription()+"code="+iwm.getCode()+"imodulecode="+iwm.getImodulecode()+"itypecode="+iwm.getItypecode()+"startTime="+iwm.getStartTime()+"execUser="+iwm.getExecUser()+"iErrorStepName="+iwm.getiErrorStepName()+"ilevelcode="+iwm.getIlevelcode()+"iip="+iwm.getIip()+"iwarnmsg="+iwm.getIwarnmsg()+"ihappentime="+iwm.getIhappentime()+"ihappentimeStr="+iwm.getIhappentimeStr()+"isonStepName="+iwm.getIsonStepName()+"iswitchName="+iwm.getIswitchName()+"isystemname="+iwm.getIsystemname()+"ibusinessName="+iwm.getIbusinessName()+"}]");

                                WarningInterfaceUtilsSus.callWarning(iwm);
                            }catch (Exception e){
                                _log.error("变更步骤告警配置未配置:"+e);
                            }
                        }
//                        WarningInterfaceUtilsSus.callWarning("SUS", "SUS_BUSINESSEXCEPTIONS", "five", remoteip, "应用变更业务系统异常", new Date()); 
                    }else
                    {
                        IeaiWarnModel iwm=new IeaiWarnModel();
                        iwm.setImodulecode("SUS");
                        iwm.setItypecode("SUS_AGENTREFUSE");
                        iwm.setIlevelcode( "five");
                        iwm.setIip(remoteip);
                        iwm.setIwarnmsg("应用变更代理"+remoteip+":"+remoteport+"无法正常连接");
                        iwm.setIhappentime(new Date());
                        iwm.setIhappentimeStr(DateUtil.formatDateTime(new Date()));
                        iwm.setIbusinessName(isysname==null?"":isysname);
                        iwm.setIsystemname(isysname==null?"":isysname);
                        iwm.setIswitchName(iruninsname);
                        iwm.setIsonStepName(iactname);
                        WarningInterfaceUtilsSus.callWarning(iwm);
//                        WarningInterfaceUtilsSus.callWarning("SUS", "SUS_AGENTREFUSE", "five", remoteip,  "应用变更代理"+remoteip+":"+remoteport+"无法正常连接", new Date());
                    }
                }
                
                if ( IEAIRuntime.current().getProject().getProjectTpye() == Constants.IEAI_EMERGENCY_SWITCH)
                {
                    WarningInterfaceUtilsEswitch warningInterfaceUtilsEswitch = new WarningInterfaceUtilsEswitch();
                    if(_execAct.getFlag() == 5)
                    {
                        IeaiWarnModel iwm=new IeaiWarnModel();
                        iwm.setImodulecode("eswitch");
                        iwm.setItypecode("eswitch_BUSINESSEXCEPTIONS");
                        iwm.setIlevelcode( "five");
                        iwm.setIip(remoteip);
                        iwm.setIwarnmsg("灾备切换业务系统异常");
                        iwm.setIhappentimeStr(DateUtil.formatDateTime(new Date()));
                        warningInterfaceUtilsEswitch.callWarning(iwm,_execAct.getFlowId());
                    }else
                    {
                        IeaiWarnModel iwm=new IeaiWarnModel();
                        iwm.setImodulecode("eswitch");
                        iwm.setItypecode("eswitch_AGENTREFUSE");
                        iwm.setIlevelcode( "five");
                        iwm.setIip(remoteip);
                        iwm.setIwarnmsg("灾备切换代理"+remoteip+":"+remoteport+"无法正常连接");
                        iwm.setIhappentimeStr(DateUtil.formatDateTime(new Date()));
                        warningInterfaceUtilsEswitch.callWarning(iwm,_execAct.getFlowId());
                    }
                    if (Environment.getInstance().getBooleanConfigNew("jc.sendalert", Environment.FALSE_BOOLEAN))
                    {
                        EswitchRunModel eswitchRunModel = SwitchMonitorManage.getInstance().getEswitchRunMessage(_execAct.getFlowId(), Constants.IEAI_EMERGENCY_SWITCH);
                        JSONObject jcMessage = new JSONObject();
                        jcMessage.put("severity", 3);
                        jcMessage.put("name", "灾备切换执行异常！");
                        jcMessage.put("description", "灾备切换,系统名称:" + eswitchRunModel.getIsysname() + ",切换方向:" +eswitchRunModel.getIswitchto() + "执行时发生异常。");
                        jcMessage.put("occur_time", System.currentTimeMillis());
                        jcMessage.put("entity_name", "灾备切换管理平台");
                        jcMessage.put("type", "event");
                        JcSendAlertUtils.getInstance().sendJcAlert(jcMessage.toJSONString());
                    }
                    if (Environment.getInstance().getBooleanConfigNew("gznx.sendSms", Environment.FALSE_BOOLEAN))//贵州农信 步骤异常 发短信
                    {
                        EswitchRunModel eswitchRunModel = SwitchMonitorManage.getInstance().getEswitchRunMessage(_execAct.getFlowId(), Constants.IEAI_EMERGENCY_SWITCH);
                        String mess = "灾备切换,系统名称:" + eswitchRunModel.getIsysname() + ",切换方向:" +eswitchRunModel.getIswitchto() + ",切换步骤："+eswitchRunModel.getIactname()+",执行时发生异常。请及时处理！";
                        GznxSendMessThread sd = new GznxSendMessThread(String.valueOf(_execAct.getFlowId()),mess);
                        sd.run();
                    }

                }
                if("CICD_ConfigItemTemplate".equals(IEAIRuntime.current().getProjectName())){
                    _execAct.setExcepted(false);
                    String actName = _execAct.getActName();
                    if("Read File".equals(actName)){
                        CI_CONFIGFILEService.getInstance().updateErorItemFlowstatusByFlow(_execAct,ItemFlowEnum.PULL);
                        _log.error("CICD_ConfigItemTemplate<=======采集异常=======>");
                    }else if ("Write File".equals(actName)){
                        CI_CONFIGFILEService.getInstance().updateErorItemFlowstatusByFlow(_execAct,ItemFlowEnum.PUSH);
                        _log.error("CICD_ConfigItemTemplate<=======推送异常=======>");
                    }else if("Compare File".equals(actName)){
                        CI_CONFIGFILEService.getInstance().updateErorItemFlowstatusByFlow(_execAct, ItemFlowEnum.SYNC);
                        _log.error("CICD_ConfigItemTemplate<=======同步异常=======>");
                    }
                }
                // Update the state of the activity. Mark it fail.
                ActivityContextManager actCtxMgr = Engine.getInstance().getActCtxManager();
                actCtxMgr.setLatestState(_execAct.getFlowId(), _execAct.getActId(), _execAct.getFlag() == 5 ? Constants.ACT_STATE_FAIL_BUSINESS:Constants.ACT_STATE_FAIL,
                    Constants.IEAI_IEAI_BASIC);
                _execAct.setExcepted(true);

                // update start by yan_wang 增加是否参与耗时计算标识

                // TimeFailFlow.getInstance().updateFlowFail(_execAct.getFlowId());

                // Map m = TimeFailFlow.getInstance().queryProNameAndFlowName(_execAct.getFlowId());

                // RepActRunInfo actinfo = RepActRunInfo.newActInfo(_execAct.getFlowId(), _execCtx,
                // _actElem);

                // actinfo.setPrjName((String)m.get("projectname"));

                // actinfo.setFlowName((String)m.get("flowname"));

                // actinfo.setIsCheck(1);

                // WorkflowManager.getInstance().saveActInfo(actinfo);

                // update end by yan_wang

                // Get Act output and save exception infomation in the output of act.
                IExceptionHandingHelper exceptionHelper = new ExceptionHandingHelper();
                exceptionHelper.saveExceptionOutput(_execAct.getScopeId(), _actElem.getActID(), ex);

                // Save the Execption in the state-data of the activity.
                // And Update the infomation of ExecAct.
                ActStateData actStateData = null;
                if (null == _execAct.getActStateData())
                {
                    actStateData = new ActStateData();
                    actStateData.setObjectData(ex);
                } else
                {
                    actStateData = _execAct.getActStateData();
                    actStateData.setObjectData(ex);
                }
                _execAct.setActStateData(actStateData);
                _execAct.setState(ExecAct.PENDING);
                // add a check condition for efficiency
                if (_execAct.isSafeFirst() && !_execAct.isInTransaction())
                {
                    EngineRepository.getInstance().updateExecAct(_execAct);
                } else
                {
                    RuntimeEnv.setExecContext(_execCtx);
                }
                // add by yuxh 20180427 福建报警平台
                if (SystemConfig.isMonitorSwitchFJ() && _execAct.getFlag() != 5)
                {
                    prjName = _execAct.getProjectName();
                    flowName = _execAct.getFlowName();
                    if(null == prjName){
                        prjName = IEAIRuntime.current().getProjectName();
                    }
                    if(null == flowName){
                        flowName = IEAIRuntime.current().getWorkflowName();
                    }
                    // 系统异常 发送福建报警平台
                    MonitorSystem ms = new MonitorSystem();
                    SendSocketWarnFJ.sendSocketWarnFJ(_execAct.getScopeId(),prjName,flowName,_execAct.getFlowId(), _execAct.getActName(),
                        _execAct.getRexecRequestId(), "", ms.AGENT_BREAK, ms.STATUS_ALERT,"","","");
                } else if (SystemConfig.isMonitorSwitchFJ() && _execAct.getFlag() == 5)
                {
                    prjName = _execAct.getProjectName();
                    flowName = _execAct.getFlowName();
                    if(null == prjName){
                        prjName = IEAIRuntime.current().getProjectName();
                    }
                    if(null == flowName){
                        flowName = IEAIRuntime.current().getWorkflowName();
                    }
                    // 失败：业务异常发送福建报警平台
                    MonitorSystem ms = new MonitorSystem();
                    SendSocketWarnFJ.sendSocketWarnFJ(_execAct.getScopeId(),prjName,flowName,_execAct.getFlowId(), _execAct.getActName(),
                        _execAct.getRexecRequestId(), "", ms.ACT_BREAK, ms.STATUS_ALERT,"","yw","");
                }
                /** 内蒙古报警平台 add by yuxh 20180618*/
                if (SystemConfig.isWebserviceWarnSwitchNMG() && _execAct.getFlag() != 5)
                {
                    MonitorSystem ms = MonitorSystem.getInstance();
                    int event = ms.AGENT_BREAK;
                    String type = "";
                    sendWarnMessageNMG(event, ms, type);

                } else if (SystemConfig.isWebserviceWarnSwitchNMG() && _execAct.getFlag() == 5)
                {

                    MonitorSystem ms = MonitorSystem.getInstance();
                    int event = ms.ACT_BREAK;
                    String type = "yw";
                    sendWarnMessageNMG(event, ms, type);
                }
                /** add by yuxh 20181109 中原银行报警发短信平台 */
                boolean ZYWarnSwitch = PersonalityEnv.isZyWarnSwitchValue();
                if (ZYWarnSwitch && _execAct.getFlag() != 5)
                {
                    prjName = _execAct.getProjectName();
                    flowName = _execAct.getFlowName();
                    if (null == prjName)
                    {
                        prjName = IEAIRuntime.current().getProjectName();
                    }
                    if (null == flowName)
                    {
                        flowName = IEAIRuntime.current().getWorkflowName();
                    }
                    MonitorSystem ms = new MonitorSystem();
                    SendSocketWarnZY.sendSocketWarnZY(_execAct.getScopeId(), prjName, flowName, _execAct.getFlowId(),
                        _execAct.getActName(), _execAct.getRexecRequestId(), "", ms.AGENT_BREAK, ms.STATUS_ALERT, "",
                        "", "");
                } else if (ZYWarnSwitch && _execAct.getFlag() == 5)
                {
                    prjName = _execAct.getProjectName();
                    flowName = _execAct.getFlowName();
                    if (null == prjName)
                    {
                        prjName = IEAIRuntime.current().getProjectName();
                    }
                    if (null == flowName)
                    {
                        flowName = IEAIRuntime.current().getWorkflowName();
                    }
                    // 失败：业务异常发送福建报警平台
                    MonitorSystem ms = new MonitorSystem();
                    SendSocketWarnZY.sendSocketWarnZY(_execAct.getScopeId(), prjName, flowName, _execAct.getFlowId(),
                        _execAct.getActName(), _execAct.getRexecRequestId(), "", ms.ACT_BREAK, ms.STATUS_ALERT, "",
                        "yw", "");
                }
                /** add by yuxh 20190820 重庆银行报警发短信平台 */
                boolean warnSwitchCq = PersonalityEnv.isCqWarnSwitchValue();
                if (warnSwitchCq && _execAct.getFlag() != 5) {
                    prjName = _execAct.getProjectName();
                    flowName = _execAct.getFlowName();
                    if (null == prjName) {
                        prjName = IEAIRuntime.current().getProjectName();
                    }
                    if (null == flowName) {
                        flowName = IEAIRuntime.current().getWorkflowName();
                    }
                    MonitorSystem ms = new MonitorSystem();
                    Workflow flow = Engine.getInstance().getWorkfow(_execAct.getFlowId());
                    boolean endFlow = true;
                    List activities = flow.getActivities();
                    for (Object o : activities) {
                        BasicActElement act = (BasicActElement) o;
                        if (act instanceof CallWorkflowActElement){
                            endFlow = false;
                        }
                    }
                    Map<String, String> callFlowInfo = getCallFlowMainId(_execAct.getFlowId());
                    String flowName1 = callFlowInfo.get("flowName");
                    if (StringUtils.isNotBlank(flowName1) && endFlow ) {
                        SendSocketWarnCQ.sendSocketWarnCQ(prjName, flowName, _execAct.getFlowId(),
                                _execAct.getActName(), _execAct.getRexecRequestId(), "", ms.AGENT_BREAK, ms.STATUS_ALERT, "",
                                "", "", flowName1);
                    } else {
                        SendSocketWarnCQ.sendSocketWarnCQ(prjName, flowName, _execAct.getFlowId(),
                                _execAct.getActName(), _execAct.getRexecRequestId(), "", ms.AGENT_BREAK, ms.STATUS_ALERT, "",
                                "", "", "");
                    }

                } else if (warnSwitchCq && _execAct.getFlag() == 5) {
                    prjName = _execAct.getProjectName();
                    flowName = _execAct.getFlowName();
                    if (null == prjName) {
                        prjName = IEAIRuntime.current().getProjectName();
                    }
                    if (null == flowName) {
                        flowName = IEAIRuntime.current().getWorkflowName();
                    }
                    // 失败：业务异常发送报警平台
                    MonitorSystem ms = new MonitorSystem();
                    Workflow flow = Engine.getInstance().getWorkfow(_execAct.getFlowId());
                    boolean endFlow = true;
                    List activities = flow.getActivities();
                    for (Object o : activities) {
                        BasicActElement act = (BasicActElement) o;
                        if (act instanceof CallWorkflowActElement){
                            endFlow = false;
                        }
                    }
                    Map<String, String> callFlowInfo = getCallFlowMainId(_execAct.getFlowId());
                    String flowName1 = callFlowInfo.get("flowName");
                    if (StringUtils.isNotBlank(flowName1) && endFlow ) {
                        SendSocketWarnCQ.sendSocketWarnCQ(prjName, flowName, _execAct.getFlowId(),
                                _execAct.getActName(), _execAct.getRexecRequestId(), "", ms.ACT_BREAK, ms.STATUS_ALERT, "",
                                "", "", flowName1);
                    } else {
                        SendSocketWarnCQ.sendSocketWarnCQ(prjName, flowName, _execAct.getFlowId(),
                                _execAct.getActName(), _execAct.getRexecRequestId(), "", ms.ACT_BREAK, ms.STATUS_ALERT, "",
                                "", "", "");
                    }

                }
                /** add by yuxh 20190917 日照银行报警发短信平台 */
                boolean rzWarnSwitch = PersonalityEnv.isRzWarnSwitchValue();
                if (rzWarnSwitch && _execAct.getFlag() != 5)
                {
                    prjName = _execAct.getProjectName();
                    flowName = _execAct.getFlowName();
                    if (null == prjName)
                    {
                        prjName = IEAIRuntime.current().getProjectName();
                    }
                    if (null == flowName)
                    {
                        flowName = IEAIRuntime.current().getWorkflowName();
                    }
                    MonitorSystem ms = new MonitorSystem();
                    AlarmParmsBean alarmParmsBean = new AlarmParmsBean();
                    alarmParmsBean.setPrjName(prjName);
                    alarmParmsBean.setFlowName(flowName);
                    alarmParmsBean.setFlowId(_execAct.getFlowId());
                    alarmParmsBean.setActName(_execAct.getActName());
                    alarmParmsBean.setReqId(_execAct.getRexecRequestId());
                    alarmParmsBean.setActDes("");
                    alarmParmsBean.setEvent(ms.AGENT_BREAK);
                    alarmParmsBean.setAlarmType(ms.STATUS_ALERT);
                    alarmParmsBean.setWarnMsg("");
                    alarmParmsBean.setFlag("");
                    alarmParmsBean.setWarnip("");
                    SendSocketWarnRZ.sendSocketWarnRZ(alarmParmsBean);
                } else if (rzWarnSwitch && _execAct.getFlag() == 5)
                {
                    prjName = _execAct.getProjectName();
                    flowName = _execAct.getFlowName();
                    if (null == prjName)
                    {
                        prjName = IEAIRuntime.current().getProjectName();
                    }
                    if (null == flowName)
                    {
                        flowName = IEAIRuntime.current().getWorkflowName();
                    }
                    // 失败：业务异常发送福建报警平台
                    MonitorSystem ms = new MonitorSystem();
                    AlarmParmsBean alarmParmsBean = new AlarmParmsBean();
                    alarmParmsBean.setPrjName(prjName);
                    alarmParmsBean.setFlowName(flowName);
                    alarmParmsBean.setFlowId(_execAct.getFlowId());
                    alarmParmsBean.setActName(_execAct.getActName());
                    alarmParmsBean.setReqId(_execAct.getRexecRequestId());
                    alarmParmsBean.setActDes("");
                    alarmParmsBean.setEvent(ms.ACT_BREAK);
                    alarmParmsBean.setAlarmType(ms.STATUS_ALERT);
                    alarmParmsBean.setWarnMsg("");
                    alarmParmsBean.setFlag("yw");
                    alarmParmsBean.setWarnip("");
                    SendSocketWarnRZ.sendSocketWarnRZ(alarmParmsBean);
                }
                /** add by yuxh 20191227 中银富登银行报警发短信平台 */
                boolean zyfdWarnSwitch = PersonalityEnv.isZyfdWarnSwitchValue();
                if (zyfdWarnSwitch && _execAct.getFlag() != 5)
                {
                    prjName = _execAct.getProjectName();
                    flowName = _execAct.getFlowName();
                    if (null == prjName)
                    {
                        prjName = IEAIRuntime.current().getProjectName();
                    }
                    if (null == flowName)
                    {
                        flowName = IEAIRuntime.current().getWorkflowName();
                    }
                    MonitorSystem ms = new MonitorSystem();
                    AlarmParmsBean alarmParmsBean = new AlarmParmsBean();
                    alarmParmsBean.setPrjName(prjName);
                    alarmParmsBean.setFlowName(flowName);
                    alarmParmsBean.setFlowId(_execAct.getFlowId());
                    alarmParmsBean.setActName(_execAct.getActName());
                    alarmParmsBean.setReqId(_execAct.getRexecRequestId());
                    alarmParmsBean.setActDes("");
                    alarmParmsBean.setEvent(ms.AGENT_BREAK);
                    alarmParmsBean.setAlarmType(ms.STATUS_ALERT);
                    alarmParmsBean.setWarnMsg("");
                    alarmParmsBean.setFlag("");
                    alarmParmsBean.setWarnip("");

                    SendSocketWarnZYFD.sendSocketWarnZYFD(alarmParmsBean);
                } else if (zyfdWarnSwitch && _execAct.getFlag() == 5)
                {
                    prjName = _execAct.getProjectName();
                    flowName = _execAct.getFlowName();
                    if (null == prjName)
                    {
                        prjName = IEAIRuntime.current().getProjectName();
                    }
                    if (null == flowName)
                    {
                        flowName = IEAIRuntime.current().getWorkflowName();
                    }
                    // 失败：业务异常
                    MonitorSystem ms = new MonitorSystem();
                    AlarmParmsBean alarmParmsBean = new AlarmParmsBean();
                    alarmParmsBean.setPrjName(prjName);
                    alarmParmsBean.setFlowName(flowName);
                    alarmParmsBean.setFlowId(_execAct.getFlowId());
                    alarmParmsBean.setActName(_execAct.getActName());
                    alarmParmsBean.setReqId(_execAct.getRexecRequestId());
                    alarmParmsBean.setActDes("");
                    alarmParmsBean.setEvent(ms.ACT_BREAK);
                    alarmParmsBean.setAlarmType(ms.STATUS_ALERT);
                    alarmParmsBean.setWarnMsg("");
                    alarmParmsBean.setFlag("yw");
                    alarmParmsBean.setWarnip("");
                    SendSocketWarnZYFD.sendSocketWarnZYFD(alarmParmsBean);
                }
                
                /** add by yangmingxing 20201126黑龙江银行报警发短信平台 */
                boolean warnSwitchHlj = PersonalityEnv.isHlJWarnSwitchValue();
                /**系统异常*/
                if (warnSwitchHlj && _execAct.getFlag() != 5)
                {
                    prjName = _execAct.getProjectName();
                    flowName = _execAct.getFlowName();
                    if (null == prjName)
                    {
                        prjName = IEAIRuntime.current().getProjectName();
                    }
                    if (null == flowName)
                    {
                        flowName = IEAIRuntime.current().getWorkflowName();
                    }
                 // 失败：系统异常发送报警平台
                    MonitorSystem ms = new MonitorSystem();
                    SendSocketWarnHLJ.sendSocketWarnHLJ(prjName, flowName, _execAct.getFlowId(),
                        _execAct.getActName(), _execAct.getRexecRequestId(), "", ms.AGENT_BREAK, ms.STATUS_ALERT, "",
                        "", "");
                } else if (warnSwitchHlj && _execAct.getFlag() == 5)
                {/**业务异常*/
                    prjName = _execAct.getProjectName();
                    flowName = _execAct.getFlowName();
                    if (null == prjName)
                    {
                        prjName = IEAIRuntime.current().getProjectName();
                    }
                    if (null == flowName)
                    {
                        flowName = IEAIRuntime.current().getWorkflowName();
                    }
                    // 失败：业务异常发送报警平台
                    MonitorSystem ms = new MonitorSystem();
                    SendSocketWarnHLJ.sendSocketWarnHLJ(prjName, flowName, _execAct.getFlowId(),
                        _execAct.getActName(), _execAct.getRexecRequestId(), "", ms.ACT_BREAK, ms.STATUS_ALERT, "",
                        "yw", "");
                }
                
                /** 添加自动重试和延时报警 by txl 2018-05-11**/
                // Update the state of the activity. Mark it fail.
                boolean isNotRedo = true; // 该活动默认不需要重试
                boolean isSysErr = false;
                ActivityReDoManager actRedoManger = new ActivityReDoManager();
                // start 2014-8-12 update by zhi_hou 区分agent链接不上异常与活动系统异常
                WarnConfigManager wcm = WarnConfigManager.getInstance();
                // 作业调度判断当活动是由agent端返回时出现异常后，活动状态为业务异常
                if (_execAct.getFlag() == 5
                        && IEAIRuntime.current().getProject().getProjectTpye() == Constants.IEAI_IEAI)
                {
                    /**将自动重试和忽略方法抽离 by txl 20180524**/
                    RedoActAndDelayWarn reDoWarn = new RedoActAndDelayWarn();
                    reDoWarn.retryAndDelayWarn(actCtxMgr, isNotRedo, actRedoManger, _execAct);
                    if (wcm.isSysWarnSwitch())
                    {
                        if (wcm.isSysWarnSwitch() && wcm.isActBreakSwitch())
                        {
                            MonitorAlertManager monitor = MonitorAlertManager.getInstance();
                            EntegorWarnManager ewm = new EntegorWarnManager();
                                    WarnMessage wm = new WarnMessage();
                                    wm = ewm.getActInfo(_execAct.getSerialNo());
                                    String[] users = null;
                                    try
                                    {
                                        users = wcm.getAddressFromDb(wm.getPrjName()).split(",");
                                        if (users == null)
                                        {
                                            _log.error("getAddress error at actAgentBreak get "
                                                    + wm.getPrjName() + " address is null");
                                            return;
                                        } else
                                        {
                                            monitor.actBreak(wm, wcm.ACT_BREAK, users);
                                        }
                                    } catch (Exception e)
                                    {
                                        _log.error("getAddress error at actBreak", e);
                                    }
                           }
                    }
                } else
                {
                    actCtxMgr.setLatestState(_execAct.getFlowId(), _execAct.getActId(), Constants.ACT_STATE_FAIL,
                        Constants.IEAI_IEAI);
                    if (wcm.isSysWarnSwitch())
                    {
                        if (wcm.isSysWarnSwitch() && wcm.isActBreakSwitch())
                        {
                            MonitorAlertManager monitor = MonitorAlertManager.getInstance();
                            EntegorWarnManager ewm = new EntegorWarnManager();
                            WarnMessage wm = new WarnMessage();
                            wm = ewm.getActInfo(_execAct.getFlowId(), _execAct.getActName(),
                                _execAct.getRexecRequestId(), _actElem.getDescription(), "系统异常");
                            String[] users = null;
                            try
                            {
                                users = wcm.getAddressFromDb(wm.getPrjName()).split(",");
                                if (users == null)
                                {
                                    _log.error("getAddress error at actAgentBreak get "
                                            + wm.getPrjName() + " address is null");
                                    return;
                                } else
                                {
                                    monitor.actSysErr(wm, wcm.ACT_SYSERR, users);
                                }
                            } catch (Exception e)
                            {
                                _log.error("getAddress error at actSysErr", e);
                            }
                            
                        }
                    }
                    isSysErr = true;
                    
                }
                // add start by wangnan for 更新作业调度报表基础数据
                // String projectName = _execAct.getProjectName();
                String projectName = IEAIRuntime.current().getProjectName();
                if (projectName != null && !"".equals(projectName))
                {
                    Project project = ProjectManager.getInstance().loadProject(projectName);
                    // 判断工程类型是否为作业调度
                    if (project.getProjectType().getProjectType() == Constants.PROJECT_TYPE_JOBSCHEDULING)
                    {
                        // 更新系统异常到errortask中
                        RuntimeValidPart runtimePart = new RuntimeValidPart();
                        // runtimePart.setErrorTaskId(bean.getTaskId().longValue());
                        runtimePart.setFlowId(_execAct.getFlowId());
                        runtimePart.setActId(_execAct.getActId());
                        Thread.sleep(5000);
                        ActRuntimeValThread thread = new ActRuntimeValThread(Constants.PART_UPDATE_TASK, runtimePart);
                        thread.start();
                    }
                }
                // add end by wangnan for 更新作业调度报表基础数据
                if("CompareTemplate".equals(IEAIRuntime.current().getProjectName())){
                    CompareBaseManage manage = new CompareBaseManage();
                    manage.updateErrorCompare(IEAIRuntime.current().getWorkflowName(), _execAct.getFlowId(),ex.getCause().toString());
                }
                new SyncResultManage().updateSyncDirResultFail( _execAct.getFlowId());
                /*if(IEAIRuntime.current().getProject().getProjectTpye() == Constants.IEAI_EMERGENCY_OPER && Environment.getInstance().getBooleanConfig(Environment.EM_SENDMONITORALERM_SWITCH, false)){
                    EMSendMonitorAlermTread thread = new EMSendMonitorAlermTread(_execCtx.getFlowId());
                    thread.start();
                }*/
            }
            synchronized (this)
            {
                _thread.setName(_oldThreadName);
                _thread = null;
            }

        } catch (Throwable ex)
        {
            // Update the state of the activity. Mark it fail.
            ActivityContextManager actCtxMgr = Engine.getInstance().getActCtxManager();
            boolean isNotRedo = true; // 该活动默认不需要重试
            boolean isSysErr = false;
            ActivityReDoManager actRedoManger = new ActivityReDoManager();
            // 监控活动系统异常 5是异常类型 系统异常 add by tao_ding 2010-7-21
            if (MonitorEnv.getBooleanConfig("ENTEGORWARN", false) && MonitorEnv.getBooleanConfig("SYSERR", false))
            {
                try
                {
                    WarningManager.getInstance().getSysExc(_actElem, _execAct, ex.getMessage());
                } catch (Exception e)
                {

                }
            }

            // 屏蔽kill工作流时可能抛出的异常 update by guanlong on 2009-09-03
            if (!(ex instanceof ThreadDeath))
            {

                _log.info("flowId: " + _execCtx.getFlowId() + " actName:" + _execAct.getActName());
                _log.error(ex.getMessage(), ex);
            }
            // end
        } finally
        {
            synchronized (this)
            {
                Engine.getInstance().getScheduler().releaseSchePoolCurNum();
                if (_thread != null)
                {
                    _thread.setName(_oldThreadName);
                    _thread = null;
                }
                // add a check condition for efficiency
                if (_execAct.isSafeFirst() && !_execAct.isInTransaction())
                {
                    RuntimeEnv.setExecContext(null);
                }
                if (null != _execCtx && needSetupTransaction() && _execCtx.isInTransaction())
                { // Disassociate
                  // current thread to transaction.
                    Current.getCurrent().setPropagationContext(null, false);
                }
                Engine.getInstance().getScheduler().releaseActExecutor(this);
                Engine.getInstance().getResourceManager().releaseResource(_execAct.isSafeFirst(),
                    _execAct.getScopeId());
            }
        }
    }



    public ExecAct getExecAct ()
    {
        return _execAct;
    }

    /**
     * The method is implemented by subclass to execute workflow.
     * 
     * @return
     * @throws RepositoryException 
     */
    abstract protected void localExec () throws ServerException, ActivityException, RepositoryException;

    /**
     * The method is used to if set transaction context.
     * 
     * @return
     */
    protected boolean needSetupTransaction ()
    {
        return true;
    }

    public void sendWarnMessageNMG ( int event, MonitorSystem ms, String type )
    {
        Alarm alarm = new Alarm();
        String prjName = _execAct.getProjectName();
        String flowName = _execAct.getFlowName();
        if (null == prjName)
        {
            prjName = IEAIRuntime.current().getProjectName();
        }
        if (null == flowName)
        {
            flowName = IEAIRuntime.current().getWorkflowName();
        }
        long flowId = _execAct.getFlowId();
        String actName = _execAct.getActName();
        alarm = ms.getWarnMessageNMG(_execAct.getRexecRequestId(), flowId, prjName, flowName, actName, event, type);
        SendWarnOfNMGThread thread = new SendWarnOfNMGThread(alarm);
        thread.start();
    }
    /**
     * <li>Description:查询运行表信息</li>
     * <AUTHOR>
     * 2021年4月16日
     * @param itaskid
     * @param conn
     * @return
     * @throws RepositoryException
     * return List
     */
    @SuppressWarnings("unused")
    public  List<Map<String,Object>>  getTopoInfoNew (long itaskid) throws RepositoryException {
        List<Map<String,Object>> returnList=new ArrayList<Map<String,Object>>();

        PreparedStatement rsStat = null;
        ResultSet rs = null;
        StringBuffer buffer = new StringBuffer();
        buffer.append("SELECT R.ISTARTUSER,R.IINSDES,R.IVERSION,R.IRUNINSNAME,R.ISTARTTIME STARTTIME FROM IEAI_RUN_INSTANCE R WHERE R.IID = (SELECT IRUNINSID FROM IEAI_RUNINFO_INSTANCE WHERE IFLOWID = ?) ");
        Connection conn = null;
        for (int i = 0; i < 10; i++)
        {
            try
            {
                conn = DBResource.getConnection("startWarn", _log, Constants.IEAI_SUS);
                _log.info("变更异常查询运行表数据");
                rsStat = conn.prepareStatement(buffer.toString());
                rsStat.setLong(1,itaskid);

                rs = rsStat.executeQuery();
                while (rs.next())
                {
                    Map<String, Object> sendMap = new HashMap<String, Object>();
                    
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    String dateStr = sdf.format(new Date(rs.getLong("STARTTIME")));
                    sendMap.put("istarttime", dateStr);
                    sendMap.put("ISTARTUSER", rs.getString("ISTARTUSER"));
                    sendMap.put("IVERSION", rs.getString("IVERSION"));
                    sendMap.put("IRUNINSNAME", rs.getString("IRUNINSNAME"));
                    /*
                     * sendMap.put("taskid", itaskid);
                     * sendMap.put("isusinstance", rs.getString("MODELNAME"));
                     * sendMap.put("ISYSNAME", rs.getString("ISYSNAME"));
                     * sendMap.put("IRUNINSNAME", rs.getString("IRUNINSNAME"));
                     * if (rs.getString("IINSDES") != null && !rs.getString("IINSDES").equals("")) {
                     * sendMap.put("IINSDES", rs.getString("IINSDES"));
                     * }else{
                     * sendMap.put("IINSDES", "null");
                     * }
                     * 
                     * sendMap.put("IVERSION", rs.getString("IVERSION"));
                     * 
                     * sendMap.put("STATE","运行中");
                     */

                    returnList.add(sendMap);
                    break;
                }
                _log.info("变更异常推送告警平台:"+returnList);
            } catch (Exception e)
            {
                _log.error("getTopoInfo is error ! " + e.getMessage());
                _log.error(buffer.toString());
                throw new RepositoryException(ServerError.ERR_DB_QUERY);
            } finally
            {
                DBResource.closeConn(conn,rs, rsStat, "getTopoInfo", _log);
            }
            break;
        }

        return returnList;
    }

    /**
     * 重庆  根据根据工作流Id获取调用Id
     *
     * @param flowId
     * @return
     * @throws RepositoryException
     */
    public Map<String, String> getCallFlowMainId(long flowId) {
        Map<String, String> map = new HashMap();
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        String sql = "SELECT C.IMAINFLOWID,W.IPROJECTNAME,W.IFLOWNAME FROM IEAI_CALLWORKFLOW_INFO C LEFT JOIN IEAI_WORKFLOWINSTANCE W ON W.IFLOWID = C.IMAINFLOWID " +
                " WHERE C.ICALLFLOWID = ?  ORDER BY  C.IMAINFLOWID DESC ";
        Connection con = null;
        PreparedStatement ps1 = null;
        ResultSet rs1 = null;

        try {
            con = DBResource.getConnection(method, _log, Constants.IEAI_IEAI);
            ps1 = con.prepareStatement(sql);
            ps1.setLong(1, flowId);

            rs1 = ps1.executeQuery();
            while (rs1.next()) {
                map.put("proName", rs1.getString("IPROJECTNAME"));
                map.put("flowName", rs1.getString("IFLOWNAME"));
                return map;
            }

        } catch (Exception e) {
            _log.error(method + " is error at OnsConsumerThread ", e);
        } finally {
            DBResource.closeConn(con, rs1, ps1, "getCallFlowMainId", _log);
        }
        return map;
    }


    
}
