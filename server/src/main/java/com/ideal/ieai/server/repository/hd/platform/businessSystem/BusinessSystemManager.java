package com.ideal.ieai.server.repository.hd.platform.businessSystem;

import com.ideal.dubbo.models.AgentModel;
import com.ideal.ieai.commons.Conscommon;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.commons.UserBasicInfo;
import com.ideal.ieai.commons.UserInfo;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.core.JudgeDB;
import com.ideal.ieai.core.pack.PackException;
import com.ideal.ieai.server.SQLUtil;
import com.ideal.ieai.server.ieaikernel.ConfigReader;
import com.ideal.ieai.server.ieaikernel.PersonalityEnv;
import com.ideal.ieai.server.ieaikernel.ServerEnv;
import com.ideal.ieai.server.poc.DBUtilsNew;
import com.ideal.ieai.server.repository.DBUtil;
import com.ideal.ieai.server.repository.ObjectStorer;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.commons.DBUtils;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.db.DBRetryUtil;
import com.ideal.ieai.server.repository.hd.cicd.syncsystem.SyncSystem;
import com.ideal.ieai.server.repository.hd.cicd.syncsystem.SyncSystemManager;
import com.ideal.ieai.server.repository.hd.cmdbSyncComInfo.CommonComBean;
import com.ideal.ieai.server.repository.hd.dataCenterOperation.DataCenterOperationManage;
import com.ideal.ieai.server.repository.hd.dataCenterOperation.model.ProjectBean;
import com.ideal.ieai.server.repository.hd.ic.businessInsConfig.BusinessInsConfigManager;
import com.ideal.ieai.server.repository.idgenerator.IdGenerator;
import com.ideal.ieai.server.repository.importexecl.InfoExeclServicesMultipleForEM;
import com.ideal.ieai.server.repository.importexecl.InfoExeclUtilBean;
import com.ideal.ieai.server.repository.inifile.IniFIleUtil;
import com.ideal.ieai.server.repository.log.app.AppLogManager;
import com.ideal.ieai.server.repository.log.app.IAppLogManager;
import com.ideal.ieai.server.repository.project.ProjectManager;
import com.ideal.ieai.server.repository.project.RepAdaptor;
import com.ideal.ieai.server.repository.project.RepAdaptorHistory;
import com.ideal.ieai.server.repository.project.RepProject;
import com.ideal.ieai.server.repository.project.RepProjectHistory;
import com.ideal.ieai.server.repository.sus.instanceConfig.ResultBean;
import com.ideal.ieai.server.repository.syncoperationsystem.SyncOperationSystemManager;
import com.ideal.ieai.server.timetask.repository.manage.TTManageManager;
import com.ideal.ieai.server.util.BeanFormatter;
import net.sf.hibernate.HibernateException;
import org.apache.commons.dbutils.QueryRunner;
import org.apache.commons.dbutils.handlers.BeanListHandler;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 
 * 名称: BusinessSystemManager.java<br>
 * 描述: 业务系统数据库操作类<br>
 * 类型: JAVA<br>
 * 最近修改时间:2015年10月13日<br>
 * 
 * <AUTHOR>
 */
public class BusinessSystemManager
{
    private static final Logger _log            = Logger.getLogger(BusinessSystemManager.class);

    private static String       sysNameStr      = "sysName";
    private static String       sysNameStrUpper = "SYSNAME";

    private IAppLogManager      _appLogManager  = AppLogManager.getInstance();

    public BusinessSystemManager()
    {

    }

    // 业务系统排序字段对应数组
    public static final Map<String, String> SYSORDERCLOUM_MAP = new HashMap<String, String>();
    static
    {
        SYSORDERCLOUM_MAP.put(sysNameStr, sysNameStrUpper);
        SYSORDERCLOUM_MAP.put("prjType", "PRJTYPE");
        SYSORDERCLOUM_MAP.put("sysType", "SYSTYPE");
        SYSORDERCLOUM_MAP.put("priority", "PRIORITY");
        SYSORDERCLOUM_MAP.put("sysDesc", "SYSDESC");
        SYSORDERCLOUM_MAP.put("sysParam", "SYSPARAM");
        SYSORDERCLOUM_MAP.put("systemCode", "ISYSTEMCODE");
        SYSORDERCLOUM_MAP.put("systemNumber", "ISYSTEMNUMBER");
        SYSORDERCLOUM_MAP.put("iid", "IID");
    }
    static private BusinessSystemManager _intance = new BusinessSystemManager();

    static public BusinessSystemManager getInstance ()
    {
        if (_intance == null)
        {
            _intance = new BusinessSystemManager();
        }
        return _intance;
    }

    /***
     * 
     * <li>Description:获取所有业务系统名</li> 
     * <AUTHOR>
     * 2015年10月13日 
     * @param businessSystemBeanForQuery
     * @return
     * @throws RepositoryException
     * return List<BusinessSystemBean>
     */
    public List<BusinessSystemBean> getBusinessSystemNameList ( BusinessSystemBeanForQuery businessSystemBeanForQuery )
            throws RepositoryException
    {
        List<BusinessSystemBean> list = new ArrayList<BusinessSystemBean>();
        String sql = "SELECT * FROM (SELECT DISTINCT(SYSNAME) AS SYSNAME,SYSTEMID AS SYSTEMID,PRJTYPE AS PRJTYPE  FROM IEAI_SYS T";
        String sqlWhere = " WHERE SYSTEMID>0 ";
        String sqlOrder = ") ORDER BY ASCII(SYSNAME)  ASC";
        // 灾备（4）还是巡检（5）
        if (businessSystemBeanForQuery.getPrjType() != 0)
        {
            sqlWhere = sqlWhere + " AND T.PRJTYPE =" + businessSystemBeanForQuery.getPrjType();
        }
        Map returnMap = this.getBusinessSystemNameListByRole(businessSystemBeanForQuery.getUserId(),
            businessSystemBeanForQuery.getLoginName());
        Boolean isNeedIn = (Boolean) returnMap.get("isNeedIn");
        List sysIdList = (List) returnMap.get("sysIdList");
        if (isNeedIn == true)
        {
            String sqlSysIdIn = DBUtil.getOracleSQLIn(sysIdList, sysIdList.size(), "SYSTEMID");
            sqlWhere = sqlWhere + " AND (" + sqlSysIdIn + ")";
        }

        BusinessSystemBean BusinessSystemBean = null;
        for (int i = 0; i < 10; i++)
        {
            if (isNeedIn == true && sysIdList.size() < 1)
            {
                break;
            }
            try
            {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("getBusinessSystemNameList", _log, Constants.IEAI_HEALTH_INSPECTION);
                    actStat = con.prepareStatement(sql + sqlWhere + sqlOrder);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        Integer prjType = actRS.getInt("PRJTYPE");
                        StringBuffer prjTypeString = new StringBuffer();
                        if (true == businessSystemBeanForQuery.getAppString())
                        {
                            prjTypeString.append("【");
                            if (4 == prjType.intValue())
                            {
                                prjTypeString.append("灾备切换");
                            } else if (5 == prjType.intValue())
                            {
                                prjTypeString.append("健康巡检");
                            }
                            prjTypeString.append("】");
                        }
                        BusinessSystemBean = new BusinessSystemBean();
                        BusinessSystemBean.setSysName(actRS.getString("SYSNAME") + prjTypeString);
                        BusinessSystemBean.setSystemId(actRS.getLong("SYSTEMID"));
                        list.add(BusinessSystemBean);

                    }
                } catch (SQLException e)
                {
                    _log.error("getBusinessSystemNameList is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, actRS, actStat, "getBusinessSystemNameList", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log.error("getBusinessSystemNameList method of BusinessSystemManager.class RepositoryException:"
                        + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return list;
    }

    public List<BusinessSystemBean> getBusinessSystemNameList_rebuild ( Long userId, int type )
            throws RepositoryException
    {
        List<BusinessSystemBean> resultlist = new ArrayList<BusinessSystemBean>();
        String sql = "SELECT IID, INAME FROM IEAI_PROJECT WHERE PROTYPE=? AND IPKGCONTENTID=0 ";
        String orderSql = " ORDER BY PROTYPE,INAME ";
        BusinessSystemBean BusinessSystemBean = null;
        for (int i = 0; i < 10; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("getBusinessSystemNameList", _log, type);
                    List list = DataCenterOperationManage.getInstance().getProject(con, Long.valueOf(userId),
                        Constants.IEAI_HEALTH_INSPECTION);
                    if (list.size() == 0)
                    {
                        list = DataCenterOperationManage.getInstance().getProject(con, Long.valueOf(userId),
                            Constants.IEAI_HEALTH_INSPECTION);
                    }
                    String projectids = "-1";
                    long allHD = Constants.IEAI_HEALTH_INSPECTION * -1;
                    String getProjectFilterCondition = "";
                    if (list != null && list.size() > 0)
                    {
                        boolean allHDPerm = false;
                        if (list.size() == 1)
                        {
                            ProjectBean p = (ProjectBean) list.get(0);
                            if (p.getLastId() == allHD)
                            {
                                allHDPerm = true;
                            }
                        }
                        if (!allHDPerm)
                        {
                            StringBuffer stringBuffer=new StringBuffer();
                            for (int n = 0; n < list.size(); n++)
                            {
                                if (n == 0)
                                {
                                    stringBuffer.append("?");
                                } else
                                {
                                    stringBuffer.append(",?");
                                }
                            }
                            getProjectFilterCondition = " AND  IID IN (" + stringBuffer.toString() + ")";
                        }
                        actStat = con.prepareStatement(sql + getProjectFilterCondition + orderSql);
                        actStat.setInt(1, Constants.IEAI_HEALTH_INSPECTION);
                        if (!allHDPerm){
                            for (int j = 0; j < list.size(); j++) {
                                ProjectBean p = (ProjectBean) list.get(j);
                                actStat.setLong(j+2,p.getLastId());
                            }
                        }
                        actRS = actStat.executeQuery();
                        while (actRS.next())
                        {
                            BusinessSystemBean = new BusinessSystemBean();
                            BusinessSystemBean.setSysName(actRS.getString("INAME"));
                            BusinessSystemBean.setSystemId(actRS.getLong("IID"));
                            resultlist.add(BusinessSystemBean);

                        }
                    }
                } catch (SQLException e)
                {
                    _log.error("getBusinessSystemNameList is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, actRS, actStat, "getBusinessSystemNameList", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log.error("getBusinessSystemNameList method of BusinessSystemManager.class RepositoryException:"
                        + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return resultlist;
    }
    public  Map<String, Object> getBusinessSystemSimpleList (String systemCode)
            throws RepositoryException
    {
        Map<String, Object> map = new HashMap<String, Object>();
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        Connection con = null;
        List resultlist = new ArrayList();
        StringBuilder sb = new StringBuilder(" ");
        sb.append("SELECT IID, INAME ,ISYSTEMCODE FROM IEAI_PROJECT WHERE IID>0 ") ;
        if(StringUtils.isNotBlank(systemCode)) {
            sb.append("and ISYSTEMCODE like '%" + systemCode + "%' ");
        }
        sb.append(" ORDER BY PROTYPE,INAME ");
        //BusinessSystemBean BusinessSystemBean = null;
        try
        {
            con = DBResource.getConnection("getBusinessSystemSimpleList", _log, Constants.IEAI_SUS);
            actStat = con.prepareStatement(sb.toString());
            
            actRS = actStat.executeQuery();
            while (actRS.next())
            {
                Map BusinessSystemBean = new HashMap();
                //BusinessSystemBean.setSystemId(actRS.getLong("IID"));
                BusinessSystemBean.put("iid", actRS.getLong("IID"));
                BusinessSystemBean.put("sysName", actRS.getString("INAME"));
                BusinessSystemBean.put("systemCode",actRS.getString("ISYSTEMCODE"));
                resultlist.add(BusinessSystemBean);

            }
        } catch (Exception e)
        {
            _log.error("getBusinessSystemSimpleList method of BusinessSystemManager.class RepositoryException:",e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        }finally
        {
            DBResource.closeConn(con, actRS, actStat, "getBusinessSystemSimpleList", _log);
        }
        map.put("dataList", resultlist);
        return map;
    }

    /***
     * 
     * <li>Description:获取业务系统级别</li> 
     * <AUTHOR>
     * 2015年10月23日 
     * @return
     * @throws RepositoryException
     * return List<Map>
     */
    public List<Map> getSysLvList () throws RepositoryException
    {
        List<Map> list = new ArrayList<Map>();
        String sql = "SELECT T.APPLVLID,T.APPLVL FROM IEAI_SYSLV T ";
        String sqlOrder = " ORDER BY T.APPLVLID  ASC";
        for (int i = 0; i < 10; i++)
        {
            try
            {
                list = getSysLvListChild(sql, sqlOrder);
                break;
            } catch (RepositoryException ex)
            {
                _log.error("getSysLvList method of BusinessSystemManager.class RepositoryException:" + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return list;
    }

    private List<Map> getSysLvListChild ( String sql, String sqlOrder ) throws RepositoryException
    {
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        Connection con = null;
        List<Map> reslist = new ArrayList<Map>();
        try
        {
            con = DBResource.getConnection("getSysLvList", _log, Constants.IEAI_HEALTH_INSPECTION);
            actStat = con.prepareStatement(sql + sqlOrder);
            actRS = actStat.executeQuery();
            while (actRS.next())
            {
                Map sysLvNameMap = new HashMap();
                sysLvNameMap.put("applvlId", actRS.getLong("APPLVLID"));
                sysLvNameMap.put("applvl", actRS.getString("APPLVL"));
                reslist.add(sysLvNameMap);
            }
        } catch (SQLException e)
        {
            _log.error("getSysLvList is error ! " + e.getMessage());
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closeConn(con, actRS, actStat, "getSysLvList", _log);
        }
        return reslist;
    }
    
    /***
     * 
     * <li>Description:系统分类查询方法</li> 
     * <AUTHOR>
     * 2017年4月25日 
     * @param modelType
     * @return
     * @throws RepositoryException
     * return List<BusinessSystemBean>
     */
    public List<BusinessSystemBean> getSysLvList ( int modelType ) throws RepositoryException
    {
        String sql = "SELECT T.APPLVLID,T.APPLVL FROM IEAI_SYSLV T ";
        List<BusinessSystemBean> datalist = new ArrayList<BusinessSystemBean>();
        try
        {
            QueryRunner qr = new QueryRunner(DBManager.getInstance().getDBSource(modelType));
            datalist = qr.query(sql, new BeanListHandler<BusinessSystemBean>(BusinessSystemBean.class));
        } catch (SQLException e)
        {
            e.printStackTrace();
        }
        return datalist;
    }

    /***
     * 
     * <li>Description:查询业务系统列表</li> 
     * <AUTHOR>
     * 2015年10月13日 
     * @param businessSystemBeanForQuery
     * @return
     * @throws RepositoryException
     * return Map
     */
    public Map getBusinessSystemList ( BusinessSystemBeanForQuery businessSystemBeanForQuery )
            throws RepositoryException
    {
        Map map = new HashMap();
        List res = new ArrayList();
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        // 后台排序处理 begin yunpeng_zhang
        String orderString = " ORDER BY ASCII(SYSNAME) \n";
        if (null != businessSystemBeanForQuery.getProperty() && !"".equals(businessSystemBeanForQuery.getProperty()))
        {
            String orderName = SYSORDERCLOUM_MAP.get(businessSystemBeanForQuery.getProperty());
            if (null == orderName || "".equals(orderName))
            {
                orderString = " ORDER BY ASCII(SYSNAME) \n";
            } else if ("PRIORITY".equals(orderName) || "PRJTYPE".equals(orderName))
            {
                orderString = " ORDER BY  " + orderName + "\n" + businessSystemBeanForQuery.getDirection();
            } else
            {
                orderString = " ORDER BY  ASCII(" + orderName + ")\n" + businessSystemBeanForQuery.getDirection();
            }

        }
        String sqlWhere = " WHERE T.SYSTEMID>0  ";
        // String orderString = " ORDER BY T.SYSTEMID DESC ";
        String sqlPageString = "SELECT * FROM (SELECT ROW_NUMBER() OVER (" + orderString
                + ") AS ROW_NUMBER,T.* FROM IEAI_SYS T";
        String sqlPageStringEnd = ") A WHERE A.ROW_NUMBER  <=? AND A.ROW_NUMBER >  ?  ";
        String sqlCount = "SELECT COUNT(*) AS NUM FROM  IEAI_SYS T ";
        Map returnMap = this.getBusinessSystemNameListByRole(businessSystemBeanForQuery.getUserId(),
            businessSystemBeanForQuery.getLoginName());
        Boolean isNeedIn = (Boolean) returnMap.get("isNeedIn");
        List sysIdList = (List) returnMap.get("sysIdList");
        if (isNeedIn == true)
        {
            String sqlSysIdIn = DBUtil.getOracleSQLIn(sysIdList, sysIdList.size(), "t.SYSTEMID");
            sqlWhere = sqlWhere + " AND (" + sqlSysIdIn + ")";
        }
        List<Map> sysLvlist = this.getSysLvList();
        for (int i = 0; i < 10; i++)
        {
            int count = 0;
            if (isNeedIn == true && sysIdList.size() < 1)
            {
                map.put("total", count);
                map.put("dataList", res);
                break;
            }
            try
            {
                try
                {

                    conn = DBResource.getConnection("getBusinessSystemList", _log, Constants.IEAI_HEALTH_INSPECTION);

                    // 业务系统名
                    if (businessSystemBeanForQuery.getSysName() != null
                            && !"".equals(businessSystemBeanForQuery.getSysName()))
                    {
                        sqlWhere = sqlWhere + " AND T.SYSNAME LIKE '%" + businessSystemBeanForQuery.getSysName() + "%'";
                    }
                    // 灾备（4）还是巡检（5）
                    if (businessSystemBeanForQuery.getPrjType() != 0)
                    {
                        sqlWhere = sqlWhere + " AND T.PRJTYPE =" + businessSystemBeanForQuery.getPrjType();
                    }

                    sqlPageString = sqlPageString + sqlWhere + orderString + sqlPageStringEnd;
                    sqlCount = sqlCount + sqlWhere;

                    actStat = conn.prepareStatement(sqlPageString);
                    actStat.setInt(1, businessSystemBeanForQuery.getStart() + businessSystemBeanForQuery.getLimit());
                    actStat.setInt(2, businessSystemBeanForQuery.getStart());

                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        BusinessSystemBean bean = new BusinessSystemBean();
                        bean.setSystemId(actRS.getLong("SYSTEMID"));
                        bean.setSysName(actRS.getString("SYSNAME"));
                        bean.setSysState(actRS.getInt("SYSSTATE"));
                        // bean.setSysType(actRS.getString("SYSTYPE"));
                        for (Map sysLvlMap : sysLvlist)
                        {
                            if (sysLvlMap.get("applvlId").toString().equals(String.valueOf(actRS.getLong("SYSTYPE"))))
                            {
                                bean.setSysType(sysLvlMap.get("applvl").toString());
                                break;
                            }
                        }
                        bean.setSysDesc(actRS.getString("SYSDESC"));
                        bean.setPriority(actRS.getInt("PRIORITY"));
                        bean.setPrjType(actRS.getInt("PRJTYPE"));
                        bean.setSysParam(actRS.getString("SYSPARAM"));
                        bean.setMails(actRS.getString("MAILS"));
                        bean.setSendCycle(actRS.getString("SENDCYCLE"));
                        Integer status = actRS.getInt("SENDSTATUS");
                        bean.setMailSendStatus(status > 0);

                        res.add(bean);
                    }

                    actStat = conn.prepareStatement(sqlCount);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        count = actRS.getInt("NUM");
                    }
                    map.put("total", count);
                    map.put("dataList", res);
                } catch (SQLException e)
                {
                    // TODO Auto-generated catch block
                    _log.error(
                        "getBusinessSystemList method of BusinessSystemManager.class SQLException:" + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, actRS, actStat, "getBusinessSystemList", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log.error("getBusinessSystemList method of BusinessSystemManager.class RepositoryException:"
                        + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }

        return map;
    }

    public Map getBusinessSystemList_rebuild_OLD ( BusinessSystemBeanForQuery businessSystemBeanForQuery, int type )
            throws RepositoryException
    {
        Map map = new HashMap();
        List res = new ArrayList();
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        // 后台排序处理 begin yunpeng_zhang
        String orderString = " ORDER BY PROTYPE DESC\n";
        if (null != businessSystemBeanForQuery.getProperty() && !"".equals(businessSystemBeanForQuery.getProperty()))
        {
            String orderName = SYSORDERCLOUM_MAP.get(businessSystemBeanForQuery.getProperty());
            if (null == orderName || "".equals(orderName))
            {
                orderString = " ORDER BY PROTYPE DESC \n";
            } else if ("PRIORITY".equals(orderName) || "PRJTYPE".equals(orderName))
            {
                if (orderName.equals("PRJTYPE"))
                {
                    orderName = "PROTYPE";
                }
                orderString = " ORDER BY  " + orderName + "\n" + businessSystemBeanForQuery.getDirection();
            } else
            {
                if (orderName.equals("SYSNAME"))
                {
                    orderName = "INAME";
                }
                orderString = " ORDER BY  ASCII(" + orderName + ")\n" + businessSystemBeanForQuery.getDirection();
            }

        }
        String sqlWhere = " WHERE T.IID>0 AND T.IID=T.ILATESTID  ";
        // String orderString = " ORDER BY T.SYSTEMID DESC ";
        String sqlPageString = "SELECT * FROM (SELECT ROW_NUMBER() OVER (" + orderString
                + ") AS ROW_NUMBER,T.* FROM IEAI_PROJECT T";
        String sqlPageStringEnd = ") A WHERE A.ROW_NUMBER  <=? AND A.ROW_NUMBER >  ?  ";
        String sqlCount = "SELECT COUNT(*) AS NUM FROM  IEAI_PROJECT T ";
        // Map returnMap = this.getBusinessSystemNameListByRole(
        // businessSystemBeanForQuery.getUserId(), businessSystemBeanForQuery.getLoginName());
        // Boolean isNeedIn = (Boolean) returnMap.get("isNeedIn");
        // List sysIdList = (List) returnMap.get("sysIdList");
        // if (isNeedIn == true)
        // {
        // String sqlSysIdIn = DBUtil.getOracleSQLIn(sysIdList, sysIdList.size(), "t.SYSTEMID");
        // sqlWhere = sqlWhere + " AND (" + sqlSysIdIn + ")";
        // }
        List<Map> sysLvlist = this.getSysLvList();
        for (int i = 0; i < 10; i++)
        {
            int count = 0;
            // if (isNeedIn == true && sysIdList.size() < 1)
            // {
            // map.put("total", count);
            // map.put("dataList", res);
            // break;
            // }
            try
            {
                try
                {

                    conn = DBResource.getConnection("getBusinessSystemList", _log, type);

                    // 业务系统名
                    if (businessSystemBeanForQuery.getSysName() != null
                            && !"".equals(businessSystemBeanForQuery.getSysName()))
                    {
                        String prjName = businessSystemBeanForQuery.getSysName();
                        if (prjName.contains("_") || prjName.contains("%"))
                        {
                            prjName = prjName.replaceAll("_", "/_");
                            prjName = prjName.replaceAll("%", "/%");
                        }
                        sqlWhere = sqlWhere + " AND T.INAME LIKE '%" + prjName + "%' escape '/'";
                    }

                    sqlPageString = sqlPageString + sqlWhere + orderString + sqlPageStringEnd;
                    sqlCount = sqlCount + sqlWhere;

                    actStat = conn.prepareStatement(sqlPageString);
                    actStat.setInt(1, businessSystemBeanForQuery.getStart() + businessSystemBeanForQuery.getLimit());
                    actStat.setInt(2, businessSystemBeanForQuery.getStart());

                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        BusinessSystemBean bean = new BusinessSystemBean();
                        bean.setSystemId(actRS.getLong("IID"));
                        bean.setSysName(actRS.getString("INAME"));
                        bean.setPrjType(actRS.getInt("PROTYPE"));
                        bean.setPkgid(actRS.getInt("IPKGCONTENTID"));
                        // bean.setSysState(actRS.getInt("SYSSTATE"));
                        // bean.setSysType(actRS.getString("SYSTYPE"));
                        /*
                         * for (Map sysLvlMap : sysLvlist)
                         * {
                         * if (sysLvlMap.get("applvlId").toString()
                         * .equals(String.valueOf(actRS.getLong("SYSTYPE"))))
                         * {
                         * bean.setSysType(sysLvlMap.get("applvl").toString());
                         * break;
                         * }
                         * }
                         */
                        /*
                         * bean.setSysDesc(actRS.getString("SYSDESC"));
                         * bean.setPriority(actRS.getInt("PRIORITY"));
                         * bean.setPrjType(actRS.getInt("PRJTYPE"));
                         * bean.setSysParam(actRS.getString("SYSPARAM"));
                         * bean.setMails(actRS.getString("MAILS"));
                         * bean.setSendCycle(actRS.getString("SENDCYCLE"));
                         * Integer status = actRS.getInt("SENDSTATUS");
                         * bean.setMailSendStatus(status>0);
                         */

                        res.add(bean);
                    }

                    actStat = conn.prepareStatement(sqlCount);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        count = actRS.getInt("NUM");
                    }
                    map.put("total", count);
                    map.put("dataList", res);
                } catch (SQLException e)
                {
                    // TODO Auto-generated catch block
                    _log.error(
                        "getBusinessSystemList method of BusinessSystemManager.class SQLException:" + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, actRS, actStat, "getBusinessSystemList", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log.error("getBusinessSystemList method of BusinessSystemManager.class RepositoryException:"
                        + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }

        return map;
    }

    public BusinessSystemBean getProInfo ( long prjId, int type ) throws RepositoryException
    {
        Map map = new HashMap();
        List res = new ArrayList();

        String sql = "select * from IEAI_PROJECT_INFO WHERE IID=?";
        String sql2 = "SELECT T.SUMMARY, T.DAY_TYPE FROM IEAI_SENDMAIL T WHERE IID=?";
        List<Map> sysLvlist = this.getSysLvList();
        BusinessSystemBean bean = null;
        for (int i = 0; i < 10; i++)
        {
            int count = 0;
            try
            {
                Connection conn = null;
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                PreparedStatement ps = null;
                ResultSet rs = null;
                try
                {
                    conn = DBResource.getConnection("getProInfo", _log, type);
                    actStat = conn.prepareStatement(sql);
                    actStat.setLong(1, prjId);
                    actRS = actStat.executeQuery();
                    bean = new BusinessSystemBean();
                    while (actRS.next())
                    {
                        bean.setSysDesc(actRS.getString("SYSDESC"));
                        bean.setPriority(actRS.getInt("PRIORITY"));
                        for (Map sysLvlMap : sysLvlist)
                        {
                            if (sysLvlMap.get("applvlId").toString().equals(String.valueOf(actRS.getLong("SYSTYPE"))))
                            {
                                bean.setSysType(sysLvlMap.get("applvl").toString());
                                break;
                            }
                        }
                        bean.setSysParam(actRS.getString("SYSPARAM"));
                        bean.setMails(actRS.getString("MAILS"));
                        bean.setSendCycle(actRS.getString("SENDCYCLE"));
                        Integer status = actRS.getInt("SENDSTATUS");
                        bean.setMailSendStatus(status > 0);
                  //      bean.setHandStart(actRS.getString("ISHANDSTART"));
                    }

                    // 查询主题等
                    ps = conn.prepareStatement(sql2);
                    ps.setLong(1, prjId);
                    rs = ps.executeQuery();
                    while (rs.next())
                    {
                        bean.setSummary(rs.getString("SUMMARY"));
                        bean.setDayType(rs.getInt("DAY_TYPE"));
                    }

                } catch (SQLException e)
                {
                    _log.error("getProInfo method of BusinessSystemManager.class SQLException:" + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closePSRS(rs, ps, "getProInfo", _log);
                    DBResource.closeConn(conn, actRS, actStat, "getProInfo", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log.error("getProInfo method of BusinessSystemManager.class RepositoryException:" + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }

        return bean;
    }

    /***
     * 
     * <li>Description:查询所属设备列表</li> 
     * <AUTHOR>
     * 2015年10月16日 
     * @param businessSystemBeanForQuery
     * @param type
     * @return
     * @throws RepositoryException
     * return Map
     */
    public Map getComputerList ( BusinessSystemBeanForQuery businessSystemBeanForQuery, int type )
            throws RepositoryException
    {
        Map map = new HashMap();
        List res = new ArrayList();
        Connection conn = null;
        ArrayList<Object> agentIpList = new ArrayList<>();
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        String[] computerIps =null;
        String  agentipStr = "";
        for (int i = 0; i < 10; i++)
        {
            try
            {
                try
                {
                    int itype = (type == Constants.IEAI_HEALTH_INSPECTION) ? Constants.IEAI_HEALTH_INSPECTION : type;
                    String order = " ORDER BY T1.IP ";
                    Map<String, String> alias = new HashMap<String, String>();
                    alias.put("cpName", "T1.CPNAME");
                    alias.put("ip", "T1.IP");
                    if (itype == Constants.IEAI_TIMINGTASK || itype == Constants.IEAI_EMERGENCY_SWITCH)
                    {
                        alias.put("cpName", "IAGENT_NAME");
                        alias.put("ip", "T1.IAGENT_IP");
                        order = " ORDER BY T1.IAGENT_IP ";
                    }
                    order = DBUtils.getOrderBySql(businessSystemBeanForQuery.getSortMap(), order, alias);
                    conn = DBResource.getConnection("getComputerList", _log, itype);
                    String ipaddrStr="";
                    if(StringUtils.isNotEmpty(businessSystemBeanForQuery.getIpAddr())){
                        ipaddrStr=" and IAGENT_IP like ?";
                    }
                    int count = 0;
                    long systemId = businessSystemBeanForQuery.getSysIdForQuery();
                    String sqlWhere = " ON TA.IAGENT_IP = T1.AGENTIP AND T1.CPPORT = TA.IAGENT_PORT LEFT JOIN IEAI_OS OS ON OS.OSID = T1 .CPOS  WHERE  T.COMPUTERID=T1.CPID AND T.SYSTEMID>0 ";
                    String orderString = order;

                    String sqlPageString = "SELECT * FROM (SELECT ROW_NUMBER() OVER (" + order
                            + ") AS ROW_NUMBER,T1.CPID,T1.IAGENTINFO_ID, T1.CPNAME,T1.IP ,T.SYSPARAM,T.SYSTEMID,TA.IAGENT_AZNAME AS TAIAGENT_AZNAME,TA.IAGENT_NETID AS TAIAGENT_NETID,OS.OSNAME FROM IEAI_SYS_RELATION T,IEAI_COMPUTER_LIST T1 LEFT JOIN IEAI_AGENTINFO TA ";
                    String sqlPageStringEnd = ") A WHERE A.ROW_NUMBER  <=? AND A.ROW_NUMBER >  ?  ";
                    String sqlCount = "SELECT COUNT(*) AS NUM FROM  IEAI_SYS_RELATION T,IEAI_COMPUTER_LIST T1 LEFT JOIN IEAI_AGENTINFO TA ";
                    // 定时任务类型,从agentinfo表出出
                    if (itype == Constants.IEAI_TIMINGTASK || itype == Constants.IEAI_EMERGENCY_SWITCH)
                    {
                        sqlWhere = " WHERE  T.COMPUTERID=T1.IAGENTINFO_ID  AND T.SYSTEMID>0 "+ipaddrStr+" ";
                        sqlPageString = "SELECT * FROM (SELECT ROW_NUMBER() OVER (ORDER BY T1.IAGENTINFO_ID DESC) AS ROW_NUMBER,T1.IAGENTINFO_ID AS CPID, T1.IAGENT_IP AS IP, T1.IAGENT_NAME ,T1.IOS_NAME AS OSNAME,T.SYSTEMID FROM IEAI_SYS_RELATION T,IEAI_AGENTINFO T1 ";
                        sqlCount = "SELECT COUNT(*) AS NUM FROM  IEAI_SYS_RELATION T,IEAI_AGENTINFO T1 ";
                    }
                    // 支持mysql
                    if (3 == JudgeDB.IEAI_DB_TYPE)
                    {
                        sqlPageString = " SELECT T1.CPID, T1.CPNAME,T1.IP ,T1.IAGENTINFO_ID ,T.SYSPARAM,T.SYSTEMID,TA.IAGENT_AZNAME AS TAIAGENT_AZNAME,TA.IAGENT_NETID AS TAIAGENT_NETID,OS.OSNAME FROM IEAI_SYS_RELATION T,IEAI_COMPUTER_LIST T1 LEFT JOIN IEAI_AGENTINFO TA ";
                        sqlPageStringEnd = " limit ?,? ";
                        sqlCount = "SELECT COUNT(*) AS NUM FROM  IEAI_SYS_RELATION T,IEAI_COMPUTER_LIST T1 LEFT JOIN IEAI_AGENTINFO TA ";
                        // 定时任务类型，从agentinfo表出
                        if (itype == Constants.IEAI_TIMINGTASK || itype == Constants.IEAI_EMERGENCY_SWITCH)
                        {
                            sqlWhere = " WHERE  T.COMPUTERID=T1.IAGENTINFO_ID AND T.SYSTEMID>0  ";
                            sqlPageString = "SELECT T1.IAGENTINFO_ID AS CPID, T1.IAGENT_IP AS IP,T1.IAGENT_NAME AS IAGENT_NAME,T1.IOS_NAME AS OSNAME,T.SYSTEMID FROM IEAI_SYS_RELATION T,IEAI_AGENTINFO T1 ";
                            sqlCount = "SELECT COUNT(*) AS NUM FROM  IEAI_SYS_RELATION T,IEAI_AGENTINFO T1 ";
                        }
                    }
                    sqlWhere = sqlWhere + "AND T.SYSTEMID =? ";
                    // 如果两个输入框都有值，则按IP段查询
                    if (businessSystemBeanForQuery.getIpBetweenLongValue() > 0
                            && businessSystemBeanForQuery.getIpEndLongValue() > 0)
                    {
                        // ip段起
                        sqlWhere = sqlWhere + " AND T1.IPALIAS >=" + businessSystemBeanForQuery.getIpBetweenLongValue();
                        // ip段至
                        sqlWhere = sqlWhere + " AND T1.IPALIAS <=" + businessSystemBeanForQuery.getIpEndLongValue();
                    } else
                    {
                        // 如果只有一个输入框都有值，则按IP模糊查询
                        if (businessSystemBeanForQuery != null && null != businessSystemBeanForQuery.getIpBetween()
                                && !"".equals(businessSystemBeanForQuery.getIpBetween()))
                        {
                            if (itype == Constants.IEAI_TIMINGTASK || itype == Constants.IEAI_EMERGENCY_SWITCH)
                            {
                                sqlWhere = sqlWhere + " AND T1.IAGENT_IP LIKE '%"
                                        + businessSystemBeanForQuery.getIpBetween() + "%' ";
                            } else
                            {
                                sqlWhere = sqlWhere + " AND T1.IP LIKE '%" + businessSystemBeanForQuery.getIpBetween()
                                        + "%' ";
                            }
                        }
                        if (businessSystemBeanForQuery != null && null != businessSystemBeanForQuery.getIpEnd()
                                && !"".equals(businessSystemBeanForQuery.getIpEnd()))
                        {
                            if (itype == Constants.IEAI_TIMINGTASK || itype == Constants.IEAI_EMERGENCY_SWITCH)
                            {
                                sqlWhere = sqlWhere + " AND T1.IAGENT_IP LIKE '%"
                                        + businessSystemBeanForQuery.getIpEnd() + "%' ";
                            } else
                            {
                                sqlWhere = sqlWhere + " AND T1.IP LIKE '%" + businessSystemBeanForQuery.getIpEnd()
                                        + "%' ";
                            }
                        }
                        if (businessSystemBeanForQuery != null && null != businessSystemBeanForQuery.getPubDesc_sm()){
                            if (StringUtils.isNotEmpty(businessSystemBeanForQuery.getPubDesc_sm()))
                            {
                                 computerIps = businessSystemBeanForQuery.getPubDesc_sm().split("\n");
                                String ipWhere = "and T1.IP in (";
                                for (String computerIp : computerIps)
                                {
                                    ipWhere += "\'" + computerIp + "\',";
                                }
                                ipWhere = ipWhere.substring(0, ipWhere.length() - 1);
                                ipWhere += ")";
                                sqlWhere += ipWhere;
                            }

                        }
                    }

                    if (businessSystemBeanForQuery.getCpName() != null
                            && !businessSystemBeanForQuery.getCpName().equals(""))
                    {
                        if (itype == Constants.IEAI_TIMINGTASK || itype == Constants.IEAI_EMERGENCY_SWITCH)
                        {
                            sqlWhere = sqlWhere + " AND LOWER(T1.IAGENT_NAME) LIKE '%"
                                    + businessSystemBeanForQuery.getCpName().toLowerCase() + "%' ";
                        } else
                        {
                            sqlWhere = sqlWhere + " AND LOWER(T1.CPNAME) LIKE '%"
                                    + businessSystemBeanForQuery.getCpName().toLowerCase() + "%' ";
                        }
                    }
                    if (businessSystemBeanForQuery.getCpName() != null
                            && !businessSystemBeanForQuery.getCpName().equals(""))
                    {
                        if (itype == Constants.IEAI_TIMINGTASK || itype == Constants.IEAI_EMERGENCY_SWITCH)
                        {
                            sqlWhere = sqlWhere + " AND LOWER(T1.IAGENT_NAME) LIKE '%"
                                    + businessSystemBeanForQuery.getCpName().toLowerCase() + "%' ";
                        }
                    }
                    if (StringUtils.isNotBlank(businessSystemBeanForQuery.getOsName()))
                    {
                        if (itype == Constants.IEAI_TIMINGTASK || itype == Constants.IEAI_EMERGENCY_SWITCH)
                        {
                            sqlWhere = sqlWhere + " AND LOWER(T1.IOS_NAME) LIKE '%"
                                    + businessSystemBeanForQuery.getOsName().toLowerCase() + "%' ";
                        }else{
                            sqlWhere = sqlWhere + " AND LOWER(OS.OSNAME) LIKE '%"
                                    + businessSystemBeanForQuery.getOsName().toLowerCase() + "%' ";
                        }
                    }

                    sqlPageString = sqlPageString + sqlWhere + orderString + sqlPageStringEnd;
                    sqlCount = sqlCount + sqlWhere;

                    actStat = conn.prepareStatement(sqlPageString);
                    int index = 0;
                    if(StringUtils.isNotEmpty(businessSystemBeanForQuery.getIpAddr())){
                        actStat.setString(++index, "%"+businessSystemBeanForQuery.getIpAddr()+"%");
                    }
                    actStat.setLong(++index, systemId);
                    if (3 == JudgeDB.IEAI_DB_TYPE)// mysql
                    {
                        actStat.setInt(++index, businessSystemBeanForQuery.getStart());
                        actStat.setInt(++index, businessSystemBeanForQuery.getLimit());
                    } else
                    {
                        actStat.setInt(++index,
                            businessSystemBeanForQuery.getStart() + businessSystemBeanForQuery.getLimit());
                        actStat.setInt(++index, businessSystemBeanForQuery.getStart());
                    }
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        Map computerMap = new HashMap();

                        if (itype == Constants.IEAI_TIMINGTASK || itype == Constants.IEAI_EMERGENCY_SWITCH)
                        {
                            computerMap.put("cpId", actRS.getLong("CPID"));
                            computerMap.put("ip", actRS.getString("IP"));
                            computerMap.put("cpName", actRS.getString("IAGENT_NAME"));
                            computerMap.put("os", actRS.getString("OSNAME"));
                            computerMap.put("systemid", actRS.getLong("SYSTEMID"));
                        } else if (itype == Constants.IEAI_HEALTH_INSPECTION)
                        {
                            computerMap.put("cpName", actRS.getString("CPNAME"));
                            computerMap.put("cpId", actRS.getLong("CPID"));
                            computerMap.put("sysParam", actRS.getString("SYSPARAM"));
                            computerMap.put("ip", actRS.getString("IP"));
                            computerMap.put("iagentinfo_id", actRS.getLong("iagentinfo_id"));
                            computerMap.put("os", actRS.getString("OSNAME"));
                            computerMap.put("systemid", actRS.getLong("SYSTEMID"));
                        } else if (itype == Constants.IEAI_SUS)
                        {
                            computerMap.put("cpName", actRS.getString("CPNAME"));
                            computerMap.put("cpId", actRS.getLong("CPID"));
                            computerMap.put("sysParam", actRS.getString("SYSPARAM"));
                            computerMap.put("ip", actRS.getString("IP"));
                            computerMap.put("iagentinfo_id", actRS.getLong("iagentinfo_id"));
                            computerMap.put("systemid", actRS.getLong("SYSTEMID"));
                            String iazName = actRS.getString("TAIAGENT_AZNAME");
                            if (null != iazName && !"".equals(iazName) && !"null".equals(iazName))
                            {
                                computerMap.put("iazName", iazName);
                            } else
                            {
                                computerMap.put("iazName", "");
                            }
                            computerMap.put("iazNetId", actRS.getString("TAIAGENT_NETID"));
                            computerMap.put("os", actRS.getString("OSNAME"));
                        } else if (itype == Constants.IEAI_APM)
                        {
                            computerMap.put("cpName", actRS.getString("CPNAME"));
                            computerMap.put("cpId", actRS.getLong("CPID"));
                            computerMap.put("sysParam", actRS.getString("SYSPARAM"));
                            computerMap.put("ip", actRS.getString("IP"));
                            computerMap.put("iagentinfo_id", actRS.getLong("iagentinfo_id"));
                            computerMap.put("systemid", actRS.getLong("SYSTEMID"));
                            String iazName = actRS.getString("TAIAGENT_AZNAME");
                            if (null != iazName && !"".equals(iazName) && !"null".equals(iazName))
                            {
                                computerMap.put("iazName", iazName);
                            } else
                            {
                                computerMap.put("iazName", "");
                            }
                            computerMap.put("iazNetId", actRS.getString("TAIAGENT_NETID"));
                            computerMap.put("os", actRS.getString("OSNAME"));
                        } else if (itype == Constants.IEAI_AZ)
                        {

                            computerMap.put("cpName", actRS.getString("CPNAME"));
                            computerMap.put("cpId", actRS.getLong("CPID"));
                            computerMap.put("sysParam", actRS.getString("SYSPARAM"));
                            computerMap.put("ip", actRS.getString("IP"));
                            computerMap.put("iagentinfo_id", actRS.getLong("iagentinfo_id"));
                            computerMap.put("systemid", actRS.getLong("SYSTEMID"));
                            String iazName = actRS.getString("TAIAGENT_AZNAME");
                            if (null != iazName && !"".equals(iazName) && !"null".equals(iazName))
                            {
                                computerMap.put("iazName", iazName);
                            } else
                            {
                                computerMap.put("iazName", "");
                            }
                            computerMap.put("iazNetId", actRS.getString("TAIAGENT_NETID"));
                            computerMap.put("os", actRS.getString("OSNAME"));
                        }
                        agentIpList.add(actRS.getString("IP"));
                        res.add(computerMap);
                    }
                    if (null != businessSystemBeanForQuery.getPubDesc_sm() && !"".equals(businessSystemBeanForQuery.getPubDesc_sm())){
                        for (String agentIp : computerIps)
                        {
                            if (!agentIpList.contains(agentIp))
                            {
                                agentipStr += agentIp + "\n";
                            }
                        }
                        if (StringUtils.isNotEmpty(agentipStr))
                        {
                            agentipStr = agentipStr.substring(0, agentipStr.length() - 1);
                        }

                        map.put("agentIpStr", agentipStr);
                        map.put("success", true);
                    }
                    actStat = conn.prepareStatement(sqlCount);
                    index = 0;
                    if(StringUtils.isNotEmpty(businessSystemBeanForQuery.getIpAddr())){
                        actStat.setString(++index, "%"+businessSystemBeanForQuery.getIpAddr()+"%");
                    }
                    actStat.setLong(++index, systemId);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        count = actRS.getInt("NUM");
                    }
                    map.put("total", count);
                    map.put("dataList", res);
                } catch (SQLException e)
                {
                    _log.error("getComputerList method of BusinessSystemManager.class SQLException:" + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, actRS, actStat, "getComputerList", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log.error(
                    "getComputerList method of BusinessSystemManager.class RepositoryException:" + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }

        return map;
    }

    /**
     * 
     * @Title: getComputerListNoSelected
     * @Description: TODO(查询待添加设备列表)
     * @param businessSystemBeanForQuery
     * @return
     * @throws RepositoryException
     * @return Map 返回类型
     * @throws
     * @变更记录 2015年10月16日 yunpeng_zhang
     */
    public Map getComputerListNoSelected ( BusinessSystemBeanForQuery businessSystemBeanForQuery, int type,
            String dataCenter ,String batchComputerName) throws RepositoryException
    {
        Map map = new HashMap();
        List res = new ArrayList();
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        for (int i = 0; i < 10; i++)
        {
            try
            {
                try
                {
                    int itype = type == Constants.IEAI_HEALTH_INSPECTION ? Constants.IEAI_HEALTH_INSPECTION : type;
                    conn = DBResource.getConnection("getComputerListNoSelected", _log,
                        Constants.IEAI_HEALTH_INSPECTION);
                    int count = 0;
                    long systemId = businessSystemBeanForQuery.getSysIdForQuery();
                    String sqlWhere = " ON TA.IAGENT_IP = T.AGENTIP AND T.CPPORT = TA.IAGENT_PORT LEFT JOIN IEAI_OS OS ON OS.OSID = T.CPOS   WHERE T.CPID>0 ";
                    if (itype == Constants.IEAI_TIMINGTASK || itype == Constants.IEAI_EMERGENCY_SWITCH)
                    {
                        sqlWhere = "WHERE T.IAGENTINFO_ID>0  ";
                    }
                    String order = "  ORDER BY T.IP ";
                    Map<String, String> alias = new HashMap<String, String>();
                    alias.put("cpName", "T.CPNAME");
                    alias.put("ip", "T.IP");
                    if (itype == Constants.IEAI_TIMINGTASK || itype == Constants.IEAI_EMERGENCY_SWITCH)
                    {
                        alias.put("cpName", "IAGENT_NAME");
                        alias.put("ip", "T.IAGENT_IP");
                        order = "  ORDER BY T.IAGENT_IP ";
                    }
                    order = DBUtils.getOrderBySql(businessSystemBeanForQuery.getSortMap(), order, alias);
                    String orderString = order;

                    String sqlPageString = "SELECT * FROM (SELECT ROW_NUMBER() OVER (" + order
                            + ") AS ROW_NUMBER,T.*,TA.IAGENT_AZNAME AS TAIAGENT_AZNAME,TA.IAGENT_NETID AS TAIAGENT_NETID,OS.OSNAME  FROM IEAI_COMPUTER_LIST T LEFT JOIN IEAI_AGENTINFO TA ";
                    String sqlPageStringEnd = ") A WHERE A.ROW_NUMBER  <=? AND A.ROW_NUMBER >  ?  ";
                    String sqlCount = "SELECT COUNT(*) AS NUM FROM  IEAI_COMPUTER_LIST T LEFT JOIN IEAI_AGENTINFO TA ";
                    if (itype == Constants.IEAI_TIMINGTASK || itype == Constants.IEAI_EMERGENCY_SWITCH)
                    {
                        sqlPageString = "SELECT * FROM (SELECT ROW_NUMBER() OVER (ORDER BY T.IAGENTINFO_ID DESC) AS ROW_NUMBER,T.*,OS.OSNAME FROM IEAI_AGENTINFO T LEFT JOIN IEAI_OS OS ON T.IOS_NAME = OS.OSNAME ";
                        sqlCount = "SELECT COUNT(*) AS NUM FROM  IEAI_AGENTINFO T LEFT JOIN IEAI_OS OS ON T.IOS_NAME = OS.OSNAME ";
                    }

                    if (3 == JudgeDB.IEAI_DB_TYPE)
                    {
                        sqlPageString = "SELECT T.*,TA.IAGENT_AZNAME AS TAIAGENT_AZNAME,TA.IAGENT_NETID AS TAIAGENT_NETID,(SELECT OSNAME FROM IEAI_OS OS WHERE OS.OSID IS NOT NULL AND  OS.OSID=T.CPOS) AS OSNAME  FROM IEAI_COMPUTER_LIST T LEFT JOIN IEAI_AGENTINFO TA ";
                        sqlPageStringEnd = " limit ?, ? ";
                        sqlCount = "SELECT COUNT(*) AS NUM FROM  IEAI_COMPUTER_LIST T LEFT JOIN IEAI_AGENTINFO TA  ";
                        if (itype == Constants.IEAI_TIMINGTASK || itype == Constants.IEAI_EMERGENCY_SWITCH)
                        {
                            sqlPageString = "SELECT T.*,OS.OSNAME FROM IEAI_AGENTINFO T LEFT JOIN IEAI_OS OS ON T.IOS_NAME = OS.OSNAME ";
                            sqlCount = "SELECT COUNT(*) AS NUM FROM  IEAI_AGENTINFO T LEFT JOIN IEAI_OS OS ON T.IOS_NAME = OS.OSNAME ";
                        }
                    }

                    if (itype == Constants.IEAI_HEALTH_INSPECTION)
                    {
                        sqlWhere = sqlWhere
                                + "AND T.CPID NOT IN (SELECT T1.COMPUTERID FROM IEAI_SYS_RELATION T1 WHERE T1.SYSTEMID=?)";
                    } else if (itype == Constants.IEAI_TIMINGTASK || itype == Constants.IEAI_EMERGENCY_SWITCH)
                    {
                        sqlWhere = sqlWhere
                                + "AND T.IAGENTINFO_ID NOT IN (SELECT T1.COMPUTERID FROM IEAI_SYS_RELATION T1 WHERE T1.SYSTEMID=?)";
                    } else if (itype == Constants.IEAI_SUS)
                    {
                        sqlWhere = sqlWhere
                                + "AND T.CPID NOT IN (SELECT T1.COMPUTERID FROM IEAI_SYS_RELATION T1 WHERE T1.SYSTEMID=?)";
                    } else if (itype == Constants.IEAI_APM)
                    {
                        sqlWhere = sqlWhere
                                + "AND T.CPID NOT IN (SELECT T1.COMPUTERID FROM IEAI_SYS_RELATION T1 WHERE T1.SYSTEMID=?)";
                    } else if (itype == Constants.IEAI_AZ)
                    {
                        sqlWhere = sqlWhere
                                + "AND T.CPID NOT IN (SELECT T1.COMPUTERID FROM IEAI_SYS_RELATION T1 WHERE T1.SYSTEMID=?)";
                    }

                    // 如果两个输入框都有值，则按IP段查询
                    if (businessSystemBeanForQuery.getIpBetweenLongValue() > 0
                            && businessSystemBeanForQuery.getIpEndLongValue() > 0)
                    {
                        // ip段起
                        sqlWhere = sqlWhere + " AND T.IPALIAS >=" + businessSystemBeanForQuery.getIpBetweenLongValue();
                        // ip段至
                        sqlWhere = sqlWhere + " AND T.IPALIAS <=" + businessSystemBeanForQuery.getIpEndLongValue();
                    } else
                    {
                        // 如果只有一个输入框都有值，则按IP模糊查询
                        if (businessSystemBeanForQuery != null && null != businessSystemBeanForQuery.getIpBetween()
                                && !"".equals(businessSystemBeanForQuery.getIpBetween()))
                        {
                            if (itype == Constants.IEAI_TIMINGTASK || itype == Constants.IEAI_EMERGENCY_SWITCH)
                            {
                                sqlWhere = sqlWhere + " AND T.IAGENT_IP LIKE '%"
                                        + businessSystemBeanForQuery.getIpBetween() + "%' ";
                            } else
                            {
                                sqlWhere = sqlWhere + " AND T.IP LIKE '%" + businessSystemBeanForQuery.getIpBetween()
                                        + "%' ";
                            }
                        }
                        if (businessSystemBeanForQuery != null && null != businessSystemBeanForQuery.getIpEnd()
                                && !"".equals(businessSystemBeanForQuery.getIpEnd()))
                        {
                            if (itype == Constants.IEAI_TIMINGTASK || itype == Constants.IEAI_EMERGENCY_SWITCH)
                            {
                                sqlWhere = sqlWhere + " AND T.IAGENT_IP LIKE '%" + businessSystemBeanForQuery.getIpEnd()
                                        + "%' ";
                            } else
                            {
                                sqlWhere = sqlWhere + " AND T.IP LIKE '%" + businessSystemBeanForQuery.getIpEnd()
                                        + "%' ";
                            }
                        }
                    }

                    if (businessSystemBeanForQuery.getCpName() != null
                            && !businessSystemBeanForQuery.getCpName().equals(""))
                    {
                        if (itype == Constants.IEAI_TIMINGTASK || itype == Constants.IEAI_EMERGENCY_SWITCH)
                        {
                            sqlWhere = sqlWhere + " AND LOWER(T.IAGENT_NAME) LIKE '%"
                                    + businessSystemBeanForQuery.getCpName().toLowerCase() + "%' ";
                        } else
                        {
                            sqlWhere = sqlWhere + " AND LOWER(T.CPNAME) LIKE '%"
                                    + businessSystemBeanForQuery.getCpName().toLowerCase() + "%' ";
                        }
                    }
                    if (StringUtils.isNotBlank(businessSystemBeanForQuery.getOsName()))
                    {
                        if (itype == Constants.IEAI_TIMINGTASK || itype == Constants.IEAI_EMERGENCY_SWITCH)
                        {
                            sqlWhere = sqlWhere + " AND LOWER(OS.OSNAME) LIKE '%"
                                    + businessSystemBeanForQuery.getOsName().toLowerCase() + "%' ";
                            } else
                        {
                            sqlWhere = sqlWhere + " AND LOWER(OS.OSNAME) LIKE '%"
                                    + businessSystemBeanForQuery.getOsName().toLowerCase() + "%' ";
                        }
                            
                    }

                    if (StringUtils.isNotBlank(dataCenter) && (itype == Constants.IEAI_TIMINGTASK))
                    {
                        sqlWhere += " AND T.IDCID = '" + dataCenter + "'";
                    }

                    if(StringUtils.isNotBlank(batchComputerName)) {
                        if (itype == Constants.IEAI_TIMINGTASK || itype == Constants.IEAI_EMERGENCY_SWITCH){
                            sqlWhere += " AND (" + Conscommon.getInSqlWithString(batchComputerName.split("\n"),"T.IAGENT_IP",1000)+") ";
                        }else{
                            sqlWhere += " AND (" + Conscommon.getInSqlWithString(batchComputerName.split("\n"),"T.IP",1000)+") ";
                        }
                    }

                    sqlPageString = sqlPageString + sqlWhere + orderString + sqlPageStringEnd;
                    sqlCount = sqlCount + sqlWhere;

                    actStat = conn.prepareStatement(sqlPageString);
                    if (itype == Constants.IEAI_TIMINGTASK) {
                        if (Environment.getInstance().isBhAlarmSendWarnSwitch()) {
                            _log.info("system unselected agent sql：" + sqlPageString);
                        }
                    }

                    int index = 0;
                    actStat.setLong(++index, systemId);

                    if (3 == JudgeDB.IEAI_DB_TYPE)
                    {
                        // mysql
                        actStat.setInt(++index, businessSystemBeanForQuery.getStart());
                        actStat.setInt(++index, businessSystemBeanForQuery.getLimit());
                    } else
                    {
                        actStat.setInt(++index,
                            businessSystemBeanForQuery.getStart() + businessSystemBeanForQuery.getLimit());
                        actStat.setInt(++index, businessSystemBeanForQuery.getStart());
                    }
                    actRS = actStat.executeQuery();
                    int ini = 1;
                    while (actRS.next())
                    {
                        Map computerMap = new HashMap();
                        if (itype == Constants.IEAI_HEALTH_INSPECTION)
                        {
                            computerMap.put("cpName", actRS.getString("CPNAME"));
                            computerMap.put("cpId", actRS.getLong("CPID"));
                            computerMap.put("ip", actRS.getString("IP"));
                            computerMap.put("os", actRS.getString("OSNAME"));
                        } else if (itype == Constants.IEAI_TIMINGTASK || itype == Constants.IEAI_EMERGENCY_SWITCH)
                        {
                            computerMap.put("cpName", actRS.getString("IAGENT_NAME"));
                            computerMap.put("cpId", actRS.getLong("IAGENTINFO_ID"));
                            computerMap.put("ip", actRS.getString("IAGENT_IP"));
                            computerMap.put("os", actRS.getString("IOS_NAME"));
                            if (Environment.getInstance().isBhAlarmSendWarnSwitch()) {
                                _log.info("i: " + (ini++) + ", bean: " + computerMap.toString());
                            }
                        } else if (itype == Constants.IEAI_SUS)
                        {
                            computerMap.put("cpName", actRS.getString("CPNAME"));
                            computerMap.put("cpId", actRS.getLong("CPID"));
                            computerMap.put("ip", actRS.getString("IP"));
                            String iazName = actRS.getString("TAIAGENT_AZNAME");
                            if (null != iazName && !"".equals(iazName) && !"null".equals(iazName))
                            {
                                computerMap.put("iazName", iazName);
                            } else
                            {
                                computerMap.put("iazName", "");
                            }
                            computerMap.put("iazNetId", actRS.getString("TAIAGENT_NETID"));
                            computerMap.put("os", actRS.getString("OSNAME"));
                        } else if (itype == Constants.IEAI_APM)
                        {
                            computerMap.put("cpName", actRS.getString("CPNAME"));
                            computerMap.put("cpId", actRS.getLong("CPID"));
                            computerMap.put("ip", actRS.getString("IP"));
                            String iazName = actRS.getString("TAIAGENT_AZNAME");
                            if (null != iazName && !"".equals(iazName) && !"null".equals(iazName))
                            {
                                computerMap.put("iazName", iazName);
                            } else
                            {
                                computerMap.put("iazName", "");
                            }
                            computerMap.put("iazNetId", actRS.getString("TAIAGENT_NETID"));
                            computerMap.put("os", actRS.getString("OSNAME"));
                        } else if (itype == Constants.IEAI_AZ)
                        {
                            computerMap.put("cpName", actRS.getString("CPNAME"));
                            computerMap.put("cpId", actRS.getLong("CPID"));
                            computerMap.put("ip", actRS.getString("IP"));
                            String iazName = actRS.getString("TAIAGENT_AZNAME");
                            if (null != iazName && !"".equals(iazName) && !"null".equals(iazName))
                            {
                                computerMap.put("iazName", iazName);
                            } else
                            {
                                computerMap.put("iazName", "");
                            }
                            computerMap.put("iazNetId", actRS.getString("TAIAGENT_NETID"));
                            computerMap.put("os", actRS.getString("OSNAME"));
                        }
                        res.add(computerMap);
                    }
                    actStat = conn.prepareStatement(sqlCount);
                    index = 0;
                    actStat.setLong(++index, systemId);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        count = actRS.getInt("NUM");
                    }
                    map.put("total", count);
                    map.put("dataList", res);
                    if (itype == Constants.IEAI_TIMINGTASK) {
                        if (Environment.getInstance().isBhAlarmSendWarnSwitch()) {
                            _log.info("dataList size ：" + res.size());
                        }
                    }
                } catch (SQLException e)
                {
                    _log.error("getComputerListNoSelected method of BusinessSystemManager.class SQLException:"
                            + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, actRS, actStat, "getComputerListNoSelected", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log.error("getComputerListNoSelected method of BusinessSystemManager.class RepositoryException:"
                        + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }

        return map;
    }

    /***
     * 
     * <li>Description:删除业务系统及设备关系</li> 
     * <AUTHOR>
     * 2015年11月3日 
     * @param deleteIds
     * @param userName
     * @return
     * @throws RepositoryException
     * return boolean
     */
    public boolean deleteBusinessSystem ( Long[] deleteIds, String userName ) throws RepositoryException
    {
        boolean returnValue = true;
        Connection conn = null;
        PreparedStatement actStat = null;
        if (null != deleteIds && deleteIds.length > 0)
        {
            for (int i = 0; i < 10; i++)
            {
                try
                {
                    try
                    {

                        conn = DBResource.getConnection("deleteBusinessSystem", _log, Constants.IEAI_HEALTH_INSPECTION);
                        // 删除业务系统表
                        String sql1 = "DELETE  FROM IEAI_PROJECT WHERE IID IN (" + StringUtils.join(deleteIds, ",")
                                + ")";
                        actStat = conn.prepareStatement(sql1);
                        actStat.executeUpdate();
                        // 删除业务系统与设备关联表
                        String sql2 = "DELETE  FROM IEAI_SYS_RELATION WHERE SYSTEMID IN ("
                                + StringUtils.join(deleteIds, ",") + ")";
                        actStat = conn.prepareStatement(sql2);
                        actStat.executeUpdate();
                        // 删除业务系统与角色关联表
                        String sql3 = "DELETE  FROM IEAI_SYS_PERMISSION WHERE IPROID IN ("
                                + StringUtils.join(deleteIds, ",") + ")";
                        actStat = conn.prepareStatement(sql3);
                        actStat.executeUpdate();
                        // 按业务系统ID删除 检查项与服务器关联表和检查点表
                        BusinessInsConfigManager.getInstance().deleteCICPbySystemId(deleteIds, conn);
                        // 按业务系统ID删除(删除业务系统时) 一级表中的检查项配置
                        IniFIleUtil.deleteCheckConfigBySysId(deleteIds, conn);
                        conn.commit();
                        _log.info("deleteBusinessSystem {systemId=" + StringUtils.join(deleteIds, ",") + "} by user:"
                                + userName);

                    } catch (SQLException e)
                    {
                        returnValue = false;
                        DBResource.rollback(conn, ServerError.ERR_DB_DELETE, e, "deleteBusinessSystem", _log);
                        _log.error("deleteBusinessSystem method of BusinessSystemManager.class SQLException:"
                                + e.getMessage());
                        throw new RepositoryException(ServerError.ERR_DB_DELETE);
                    } finally
                    {
                        DBResource.closePSConn(conn, actStat, "deleteBusinessSystem", _log);
                    }
                    break;
                } catch (RepositoryException ex)
                {
                    returnValue = false;
                    DBResource.rollback(conn, ServerError.ERR_DB_DELETE, ex, "deleteBusinessSystem", _log);
                    _log.error("deleteBusinessSystem method of BusinessSystemManager.class RepositoryException:"
                            + ex.getMessage());
                    DBResource.throwRepositoryException(i, ServerError.ERR_DB_DELETE);
                }
            }
        }

        return returnValue;
    }

    /***
     * 
     * <li>Description:删除业务系统关联的设备</li> 
     * <AUTHOR>
     * 2015年10月30日
     * @param deleteIds
     * @param systemId
     * @param userName
     * @return
     * @throws RepositoryException
     * return boolean
     */
    public boolean deleteBusinessSystemRelation ( Long[] deleteIds, long systemId, String userName )
            throws RepositoryException
    {
        boolean returnValue = true;
        Connection conn = null;
        PreparedStatement actStat = null;
        if (null != deleteIds && deleteIds.length > 0)
        {
            for (int i = 0; i < 10; i++)
            {
                try
                {
                    try
                    {

                        conn = DBResource.getConnection("deleteBusinessSystemRelation", _log,
                            Constants.IEAI_HEALTH_INSPECTION);
                        String sql2 = "DELETE  FROM IEAI_SYS_RELATION WHERE  SYSTEMID=? AND COMPUTERID  IN ("
                                + StringUtils.join(deleteIds, ",") + ")";
                        actStat = conn.prepareStatement(sql2);
                        int index = 0;
                        actStat.setLong(++index, systemId);
                        actStat.executeUpdate();
                        // 按业务系统ID和服务器ID删除 检查项与服务器关联表和检查点表
                        BusinessInsConfigManager.getInstance().deleteCICPbySystemIdAndCpIds(deleteIds, systemId, conn);
                        // 按业务系统ID和设备ID删除 一级表中的检查项配置
                        IniFIleUtil.deleteCheckConfigBySysIdAndMeid(deleteIds, systemId, conn);
                        conn.commit();
                        _log.info("deleteBusinessSystemRelation {systemId=" + systemId + ",computerId="
                                + StringUtils.join(deleteIds, ",") + "} by user:" + userName);

                    } catch (SQLException e)
                    {
                        returnValue = false;
                        DBResource.rollback(conn, ServerError.ERR_DB_DELETE, e, "deleteBusinessSystemRelation", _log);
                        _log.error("deleteBusinessSystemRelation method of BusinessSystemManager.class SQLException:"
                                + e.getMessage());
                        throw new RepositoryException(ServerError.ERR_DB_DELETE);
                    } finally
                    {
                        DBResource.closePSConn(conn, actStat, "deleteBusinessSystemRelation", _log);
                    }
                    break;
                } catch (RepositoryException ex)
                {
                    returnValue = false;
                    DBResource.rollback(conn, ServerError.ERR_DB_DELETE, ex, "deleteBusinessSystemRelation", _log);
                    _log.error("deleteBusinessSystemRelation method of BusinessSystemManager.class RepositoryException:"
                            + ex.getMessage());
                    DBResource.throwRepositoryException(i, ServerError.ERR_DB_DELETE);
                }
            }
        }

        return returnValue;
    }

    /***
     * 
     * <li>Description:修改MERGE INTO用法时检查发现停用</li> 
     * <AUTHOR>
     * 2016年07月28日
     * @param deleteIds
     * @param systemId
     * @param userName
     * @return
     * @throws RepositoryException
     * return boolean
     */
    // public boolean saveBusinessSystem ( List<Map<String, Object>> businessSystems, String
    // userName )
    // throws RepositoryException
    // {
    // boolean returnValue = true;
    // Connection conn = null;
    // PreparedStatement actStat = null;
    // PreparedStatement actStat2 = null;
    // long sysTime = System.currentTimeMillis();
    // List<Map> sysLvlist = this.getSysLvList();
    // if (null != businessSystems && businessSystems.size() > 0)
    // {
    // for (int i = 0; i < 10; i++)
    // {
    // try
    // {
    // try
    // {
    //
    // String sql = "MERGE INTO IEAI_SYS D USING "
    // + "(SELECT ? AS SYSTEMID FROM IDUAL) S ON "
    // + "(D.SYSTEMID = S.SYSTEMID) WHEN MATCHED THEN "
    // + "UPDATE SET D.SYSTYPE=?,D.PRIORITY=?,D.SYSPARAM
    // =?,D.SYSDESC=?,D.UPUSER=?,D.UPTIME=FUN_GET_DATE_NUMBER_NEW(CURRENT TIMESTAMP,8
    // ),D.SENDCYCLE=?,D.MAILS=?,D.SENDSTATUS=? "
    // + "WHEN NOT MATCHED THEN "
    // + "INSERT
    // (SYSTEMID,SYSNAME,SYSTYPE,PRIORITY,PRJTYPE,SYSPARAM,SYSDESC,CRTUSER,CRTTIME,SENDCYCLE,MAILS,SENDSTATUS)
    // "
    // + "VALUES(?,?,?,?,?,?,?,?,FUN_GET_DATE_NUMBER_NEW(CURRENT TIMESTAMP,8 ),?,?,? )";
    // String sql2 = "UPDATE IEAI_SYS_RELATION T SET T.SYSPARAM=(?) WHERE T.SYSTEMID=?";
    // conn = DBResource.getConnection("saveBusinessSystem", _log,Constants.IEAI_INFOCOLLECTION);
    // actStat = conn.prepareStatement(sql);
    // actStat2 = conn.prepareStatement(sql2);
    // for (int z = 0; z < businessSystems.size(); z++)
    // {
    // Map<String, Object> bsMap = businessSystems.get(z);
    // String sysName = (String) bsMap.get("sysName");
    // String sysType = (String) bsMap.get("sysType");
    // Long sysTypeNO = null;
    // for (Map sysLvlMap : sysLvlist)
    // {
    // if (sysLvlMap.get("applvl").equals(sysType))
    // {
    // sysTypeNO = Long.valueOf(sysLvlMap.get("applvlId").toString());
    // break;
    // }
    // }
    // Integer priority = (Integer) bsMap.get("priority");
    // String sysDesc = (String) bsMap.get("sysDesc");
    // String sysParam = (String) bsMap.get("sysParam");
    // Integer prjType = (Integer) bsMap.get("prjType");
    // String sendCycle = (String) bsMap.get("sendCycle");
    // String mails = (String) bsMap.get("mails");
    // Boolean sendStatus = (Boolean) bsMap.get("mailSendStatus");
    // Long systemId = (Long) Long.parseLong(bsMap.get("systemId").toString());
    // int index = 0;
    // actStat.setLong(++index, systemId);
    // actStat.setLong(++index, sysTypeNO);
    // actStat.setInt(++index, priority);
    // actStat.setString(++index, sysParam == null ? "" : sysParam.trim());
    // actStat.setString(++index, sysDesc == null ? "" : sysDesc.trim());
    // actStat.setString(++index, userName);
    // actStat.setString(++index, sendCycle);
    // actStat.setString(++index, mails);
    // actStat.setInt(++index, sendStatus == true?1:0);
    //// actStat.setLong(++index, sysTime);
    //
    // actStat.setLong(++index, IdGenerator.createId("IEAI_SYS", conn));
    // actStat.setString(++index, sysName == null ? "" : sysName.trim());
    // actStat.setLong(++index, sysTypeNO);
    // actStat.setInt(++index, priority);
    // actStat.setInt(++index, prjType);
    // actStat.setString(++index, sysParam == null ? "" : sysParam.trim());
    // actStat.setString(++index, sysDesc == null ? "" : sysDesc.trim());
    // actStat.setString(++index, userName);
    // actStat.setString(++index, sendCycle);
    // actStat.setString(++index, mails);
    // actStat.setInt(++index, sendStatus == true?1:0);
    //// actStat.setLong(++index, sysTime);
    // actStat.addBatch();
    //
    // actStat2.setString(1, sysParam == null ? "" : sysParam.trim());
    // actStat2.setLong(2, systemId);
    // actStat2.addBatch();
    // if(systemId>0)
    // {
    // _log.info("updateBusinessSystem {systemId="
    // + systemId + ",priority=" + priority + ",prjType="+prjType+",sysParam="+sysParam+" } by
    // user:"+userName);
    // }
    //
    // }
    // actStat.executeBatch();
    // actStat2.executeBatch();
    // conn.commit();
    //
    // } catch (SQLException e)
    // {
    // returnValue = false;
    // DBResource.rollback(conn, ServerError.ERR_DB_INSERT, e,
    // "saveBusinessSystem", _log);
    // _log.error("saveBusinessSystem method of BusinessSystemManager.class SQLException:"
    // + e.getMessage());
    // throw new RepositoryException(ServerError.ERR_DB_INSERT);
    // } finally
    // {
    // DBResource.closePreparedStatement(actStat2, "saveBusinessSystem", _log);
    // DBResource.closePSConn(conn, actStat, "saveBusinessSystem", _log);
    // }
    // break;
    // } catch (RepositoryException ex)
    // {
    // returnValue = false;
    // DBResource.rollback(conn, ServerError.ERR_DB_INSERT, ex, "saveBusinessSystem",
    // _log);
    // _log.error("saveBusinessSystem method of BusinessSystemManager.class RepositoryException:"
    // + ex.getMessage());
    // DBResource.throwRepositoryException(i, ServerError.ERR_DB_INSERT);
    // }
    // }
    // }
    //
    // return returnValue;
    // }
    /**
     * <li>Description:保存业务 系统方法 重构</li> 
     * <AUTHOR>
     * 2016年7月6日 
     * @param businessSystems
     * @param userName
     * @param type
     * @return
     * @throws RepositoryException
     * return boolean
     */
    public boolean saveBusinessSystem_rebuild ( List<Map<String, Object>> businessSystems, String userName, long userId,
            int type ) throws RepositoryException
    {
        boolean returnValue = true;
        Connection conn = null;
        PreparedStatement actStat = null;
        PreparedStatement actStat2 = null;
        PreparedStatement actStatUpdate = null;
        List<Map> sysLvlist = this.getSysLvList();
        if (null != businessSystems && businessSystems.size() > 0)
        {
            for (int i = 0; i < 10; i++)
            {
                try
                {
                    try
                    {
                        String sql_insert_project = "INSERT INTO IEAI_PROJECT(IID,INAME,IPKGCONTENTID,IGROUPID,PROTYPE,IUPLOADUSERID,IUPLOADUSER,ILATESTID,IUPPERID,ISYSTEMCODE) VALUES(?,?,?,?,?,?,?,?,?,?)";
                        String sql_insert_project_info = "INSERT INTO IEAI_PROJECT_INFO(IID,SENDCYCLE, MAILS,SENDSTATUS,SYSTYPE,SYSDESC,SYSPARAM,PRIORITY) VALUES(?,?,?,?,?,?,?,?)";
                        String sql_update_project = "UPDATE IEAI_PROJECT SET ISYSTEMCODE = ? WHERE IID = ?";
                        conn = DBResource.getConnection("saveBusinessSystem_rebuild", _log, type);
                        actStat = conn.prepareStatement(sql_insert_project);
                        actStat2 = conn.prepareStatement(sql_insert_project_info);
                        actStatUpdate = conn.prepareStatement(sql_update_project);

                        boolean isH = false;
                        for (int z = 0; z < businessSystems.size(); z++)
                        {
                            Map<String, Object> bsMap = businessSystems.get(z);
                            if (!existSBusinessSystem(bsMap, type))
                            {
                                long iid = IdGenerator.createIdForExecAct(RepProject.class, conn);
                                Long prjType = Long.valueOf(String.valueOf(bsMap.get("prjType")));
                                actStat.setLong(1, iid);
                                actStat.setString(2, String.valueOf(bsMap.get("sysName")));
                                actStat.setLong(3, 0);
                                actStat.setLong(4, prjType);
                                actStat.setLong(5, prjType);
                                actStat.setLong(6, userId);
                                actStat.setString(7, userName);
                                actStat.setLong(8, iid);
                                actStat.setLong(9, iid);
                                actStat.setString(10, String.valueOf(bsMap.get("systemCode")));
                                actStat.addBatch();
                                if (Constants.IEAI_HEALTH_INSPECTION == prjType)
                                {
                                    isH = true;
                                    Integer priority = (Integer) bsMap.get("priority");
                                    String sendCycle = (String) bsMap.get("sendCycle");
                                    String mails = (String) bsMap.get("mails");
                                    Boolean sendStatus = (Boolean) bsMap.get("mailSendStatus");
                                    String sysDesc = (String) bsMap.get("sysDesc");
                                    String sysParam = (String) bsMap.get("sysParam");
                                    actStat2.setLong(1, iid);
                                    actStat2.setString(2, sendCycle);
                                    actStat2.setString(3, mails);
                                    actStat2.setLong(4, sendStatus == true ? 1 : 0);
                                    String sysType = (String) bsMap.get("sysType");
                                    Long sysTypeNO = 0L;
                                    for (Map sysLvlMap : sysLvlist)
                                    {
                                        if (sysLvlMap.get("applvl").equals(sysType))
                                        {
                                            sysTypeNO = Long.valueOf(sysLvlMap.get("applvlId").toString());
                                            break;
                                        }
                                    }
                                    actStat2.setLong(5, sysTypeNO);
                                    actStat2.setString(6, sysDesc == null ? "" : sysDesc.trim());
                                    actStat2.setString(7, sysParam == null ? "" : sysParam.trim());
                                    actStat2.setLong(8, priority);
                                    actStat2.addBatch();

                                }
                            } else
                            {
                                Long iid = (Long) Long.parseLong(bsMap.get("systemId").toString());
                                actStatUpdate.setString(1, String.valueOf(bsMap.get("systemCode")));
                                actStatUpdate.setLong(2, iid);
                                actStatUpdate.addBatch();
                            }
                        }
                        actStat.executeBatch();
                        if (isH)
                        {
                            actStat2.executeBatch();
                        }
                        actStatUpdate.executeBatch();
                        conn.commit();

                    } catch (SQLException e)
                    {
                        returnValue = false;
                        DBResource.rollback(conn, ServerError.ERR_DB_INSERT, e, "saveBusinessSystem_rebuild", _log);
                        _log.error("saveBusinessSystem_rebuild method of BusinessSystemManager.class SQLException:"
                                + e.getMessage());
                        throw new RepositoryException(ServerError.ERR_DB_INSERT);
                    } finally
                    {
                        DBResource.closePreparedStatement(actStatUpdate, "updateBusinessSystem_rebuild", _log);
                        DBResource.closePreparedStatement(actStat2, "saveBusinessSystem_rebuild", _log);
                        DBResource.closePSConn(conn, actStat, "saveBusinessSystem_rebuild", _log);
                    }
                    break;
                } catch (RepositoryException ex)
                {
                    returnValue = false;
                    DBResource.rollback(conn, ServerError.ERR_DB_INSERT, ex, "saveBusinessSystem_rebuild", _log);
                    _log.error("saveBusinessSystem_rebuild method of BusinessSystemManager.class RepositoryException:"
                            + ex.getMessage());
                    DBResource.throwRepositoryException(i, ServerError.ERR_DB_INSERT);
                }
            }
        }

        return returnValue;
    }

    public Map<String, Object> organizeBSSql ( List<BusinessSystemBean> stepInfos, Long userId, Connection baseConn )
            throws RepositoryException
    {
        Map<String, Object> res = new HashMap<String, Object>();
        boolean isSuccess = true;
        List<String> exeSqls = new ArrayList<String>();
        List<String> rollbackSqls = new ArrayList<String>();
        boolean pfIpmpSystemSwitch = ServerEnv.getInstance().getBooleanConfig(PersonalityEnv.PF_IPMP_SYSTEM_BAND_SWITCH,
            false);
        StringBuilder ipmpNoExistSys = new StringBuilder();

        try
        {

            // String execSqlTemplate = " UPDATE IEAI_INSTANCEINFO T SET
            // T.ICONNER={iconner},T.IACTDES='{iactdes}',T.IREMINFO='{ireminfo}',T.IPRENER='{iprener}',T.IIP='{iip}',T.IPORT={iport},T.IEXECUSER='{iexecuser}',T.ISHELLSCRIPT='{ishellscript}',T.ISHELLPATH='{ishellpath}',T.IEXPECEINFO='{iexpeceinfo}',T.IACTTYPE={iacttype},T.ISYSTYPE={isystype},T.IISLOADENV={iisloadenv},T.ITIMEOUT={itimeout},T.ISWITCHSYSTYPE={iswitchsystype},T.IPARACHECK='{iparacheck}',T.IPARASWITCH='{iparaswitch}',T.IPARASWITCHFORCE='{iparaswitchforce}',T.IISDISABLE={iisdisable},T.IMODELTYPE='{imodeltype}',T.IRETNVALEXCEPION='{iretnvalexcepion}',T.IREDOABLE={iredoable},T.ICONNERNAME='{iconnername}',T.ICENTER='{icenter}'
            // WHERE T.IID={iid} ";
            String execSqlTemplate = " INSERT INTO IEAI_PROJECT(IID,INAME,IPKGCONTENTID,IGROUPID,PROTYPE,IUPLOADUSERID,IUPLOADUSER,ILATESTID,IUPPERID ,ISYSTEMCODE,ISYSTEMNUMBER,ICANSYNC,DEVELOPER,DEPARTMENT,ISYSBINGAGENT,ICICDSYS_CODE,ISYSTEM_OWNER,ISYSTEM_OWNER_TEL)"
                    + " VALUES({systemId},'{sysName}',{pkgid},{prjType},{prjType},{userId},'{userName}',{systemId},{systemId},'{systemCode}','{systemNumber}','{icansync}','{developer}','{department}','{isysbingagent}','{systemNumberCicd}','{asyToUse}','{systemOwnerTel}' )";
            String execSqlTemplate_info = " INSERT INTO IEAI_PROJECT_INFO(IID,PRIORITY) VALUES({systemId},1)";
            String execSqlTemplateIpmp = "  INSERT INTO IEAI_IPMP_SYSTEM_RELATON(IID,IPROJECTID,IPMPSYSNAME,ISYSCODE) VALUES({ipmpIid},{systemId},'{ipmpSysName}','{sysCode}') ";

            String delbs = "DELETE FROM IEAI_PROJECT WHERE IID={systemId}";
            String delbs_INFO = " DELETE FROM IEAI_PROJECT_INFO WHERE IID={systemId}";
            String delbsIpmp = " DELETE FROM IEAI_IPMP_SYSTEM_RELATON WHERE IID={ipmpIid} ";
 
            String sql_update_project = "UPDATE IEAI_PROJECT SET ISYSTEMCODE = '{systemCode}' , INAME='{sysName}', ISYSTEMNUMBER = '{systemNumber}' ,ICANSYNC = '{icansync}',DEVELOPER = '{developer}',DEPARTMENT = '{department}' ,isysbingagent = '{isysbingagent}',ICICDSYS_CODE = '{systemNumberCicd}',ISYSTEM_OWNER = '{asyToUse}',ISYSTEM_OWNER_TEL = '{systemOwnerTel}' WHERE IID = {systemId}";
            String sql_update_timetaskproject = "UPDATE IEAI_PROJECT SET ISYSTEMCODE = '{systemCode}', ISYSTEMNUMBER = '{systemNumber}',INAME='{sysName}',ICANSYNC = '{icansync}',DEVELOPER = '{developer}',DEPARTMENT = '{department}'  ,ICICDSYS_CODE = '{systemNumberCicd}',ISYSTEM_OWNER = '{asyToUse}',ISYSTEM_OWNER_TEL = '{systemOwnerTel}'  WHERE IID = {systemId}";
            String sqlUpdateIpmp = " UPDATE IEAI_IPMP_SYSTEM_RELATON SET IPMPSYSNAME = '{ipmpSysName}',ISYSCODE = '{sysCode}'  WHERE IPROJECTID = {systemId} ";

            String sql_update_instance = "UPDATE IEAI_INSTANCE_VERSION SET IINSTANCENAME ='{sysName}' WHERE IINSTANCENAME = '{oldSysName}'";
            String sql_update_instance_info = "UPDATE IEAI_INSTANCEINFO SET IINSTANCENAME ='{sysName}' WHERE IINSTANCENAME = '{oldSysName}'";
            
            String update_sys_relation_info = " UPDATE ieai_sys_relation SET SYSNAME='{sysName}' where systemid={systemId} ";
            
            for (BusinessSystemBean stepInfo : stepInfos)
            {

                //null时出现超长问题
                if(stepInfo.getIcansync()==null  ){
                    stepInfo.setIcansync(1);
                }

                if (stepInfo.getSystemId() <= 0)
                {
                    long iid = IdGenerator.createIdForExecAct(RepProject.class, baseConn);
                    stepInfo.setSystemId(iid);

                    BeanFormatter<BusinessSystemBean> insertbf = new BeanFormatter<BusinessSystemBean>(execSqlTemplate);
                    String insert = insertbf.format(stepInfo);
                    exeSqls.add(insert);

                    if (Constants.IEAI_HEALTH_INSPECTION == stepInfo.getPrjType())
                    {
                        BeanFormatter<BusinessSystemBean> info = new BeanFormatter<BusinessSystemBean>(
                                execSqlTemplate_info);
                        String insert_info = info.format(stepInfo);
                        exeSqls.add(insert_info);

                        BeanFormatter<BusinessSystemBean> delbf = new BeanFormatter<BusinessSystemBean>(delbs_INFO);
                        String del_info_sql = delbf.format(stepInfo);
                        rollbackSqls.add(del_info_sql);
                    }

                    BeanFormatter<BusinessSystemBean> delbf = new BeanFormatter<BusinessSystemBean>(delbs);
                    String del = delbf.format(stepInfo);
                    rollbackSqls.add(del);

                    if (pfIpmpSystemSwitch && (Constants.IEAI_SUS == stepInfo.getPrjType() ||
                            Constants.IEAI_APM == stepInfo.getPrjType()
                            || Constants.IEAI_AZ == stepInfo.getPrjType()))
                    {
                        _log.info("IpmpNoExistSysName：" + stepInfo.getIpmpNoExistSysName());
                        if (SyncOperationSystemManager.checkEmpty(stepInfo.getIpmpNoExistSysName()))
                        {
                            if (ipmpNoExistSys.length() > 0 && !"null".equals(ipmpNoExistSys.toString())
                                    && !"".equals(ipmpNoExistSys.toString()))
                            {
                                ipmpNoExistSys.append(",");
                                ipmpNoExistSys.append(stepInfo.getIpmpNoExistSysName());
                            } else
                            {
                                ipmpNoExistSys.append(stepInfo.getIpmpNoExistSysName());
                            }
                        }
                        if (SyncOperationSystemManager.checkEmpty(stepInfo.getIpmpSysName()))
                        {
                            long ipmpIid = IdGenerator.createId("IEAI_IPMP_SYSTEM_RELATON", baseConn);
                            stepInfo.setIpmpIid(ipmpIid);
                            BeanFormatter<BusinessSystemBean> insertIpmpBean = new BeanFormatter<>(execSqlTemplateIpmp);
                            String insertIpmp = insertIpmpBean.format(stepInfo);
                            exeSqls.add(insertIpmp);

                            BeanFormatter<BusinessSystemBean> delIpmpBean = new BeanFormatter<>(delbsIpmp);
                            String delIpmp = delIpmpBean.format(stepInfo);
                            rollbackSqls.add(delIpmp);
                        }
                    }
                } else
                {

                    if (Constants.IEAI_TIMINGTASK == stepInfo.getPrjType())
                    {
                        BeanFormatter<BusinessSystemBean> upbf = new BeanFormatter<BusinessSystemBean>(
                                sql_update_timetaskproject);
                        String up_sql = upbf.format(stepInfo);
                        exeSqls.add(up_sql);

                        BusinessSystemBean oldBen = getBusinessSystemForBack(stepInfo.getSystemId(), baseConn);
                        BeanFormatter<BusinessSystemBean> up = new BeanFormatter<BusinessSystemBean>(
                                sql_update_timetaskproject);
                        String del_info_sql = up.format(oldBen);
                        rollbackSqls.add(del_info_sql);

                    } else
                    {
                        BeanFormatter<BusinessSystemBean> upbf = new BeanFormatter<BusinessSystemBean>(
                                sql_update_project);
                        String up_sql = upbf.format(stepInfo);
                        exeSqls.add(up_sql);

                        try
                        {
                            // 应用变更，应用维护在 业务系统编辑开关的开启时，允许更新数据
                            boolean isPrjEditSusSwitch = ServerEnv.getInstance()
                                    .getBooleanConfig(Environment.PRJ_EDIT_SUS_SWITCH, false);
                            String projType = stepInfo.getSysType();
                            if (isPrjEditSusSwitch)

                            {
                                BusinessSystemBean oldSystemBean = BusinessSystemManager.getInstance()
                                        .getProinfoOne(stepInfo.getSystemId(), baseConn);
                                oldSystemBean.setOldSysName(oldSystemBean.getSysName());
                                oldSystemBean.setSysName(stepInfo.getSysName());

                                // 更新版本版本主表
                                BeanFormatter<BusinessSystemBean> upbf_version = new BeanFormatter<BusinessSystemBean>(
                                        sql_update_instance);
                                String update_vesion = upbf_version.format(oldSystemBean);
                                exeSqls.add(update_vesion);

                                // 更新版本版本子表
                                BeanFormatter<BusinessSystemBean> upbf_info = new BeanFormatter<BusinessSystemBean>(
                                        sql_update_instance_info);
                                String update_Info = upbf_info.format(oldSystemBean);
                                exeSqls.add(update_Info);
                                
                                // 更新ieai_sys_relation表业务系统名
                                //String update_sys_relation_sql = " UPDATE ieai_sys_relation SET SYSNAME='"+oldSystemBean.getSysName()+"' where systemid="+oldSystemBean.getSystemId()+" ";
                                BeanFormatter<BusinessSystemBean> usys_info = new BeanFormatter<BusinessSystemBean>(
                                        update_sys_relation_info);
                                String syssql = usys_info.format(oldSystemBean);
                                
                                exeSqls.add(syssql);
                                
                            }
                        } catch (Exception e)
                        {
                            _log.error("edit sus data is error："+e.getMessage());
                        }

                    }

                    if (pfIpmpSystemSwitch && (Constants.IEAI_SUS == stepInfo.getPrjType() ||
                            Constants.IEAI_APM == stepInfo.getPrjType()
                            || Constants.IEAI_AZ == stepInfo.getPrjType()))
                    {
                        _log.info("IpmpNoExistSysName1：" + stepInfo.getIpmpNoExistSysName());
                        if (SyncOperationSystemManager.checkEmpty(stepInfo.getIpmpNoExistSysName()))
                        {
                            if (ipmpNoExistSys.length() > 0 && !"null".equals(ipmpNoExistSys.toString())
                                    && !"".equals(ipmpNoExistSys.toString()))
                            {
                                ipmpNoExistSys.append(",");
                                ipmpNoExistSys.append(stepInfo.getIpmpNoExistSysName());
                            } else
                            {
                                ipmpNoExistSys.append(stepInfo.getIpmpNoExistSysName());
                            }
                        }
                        BusinessSystemBean oldIpmpSystemBean = getIpmpProinfoOne(stepInfo.getSystemId(), baseConn);
                        if (oldIpmpSystemBean.getSystemId() > 0)
                        {
                            BeanFormatter<BusinessSystemBean> updateIpmpBean = new BeanFormatter<>(sqlUpdateIpmp);
                            String insertIpmp = updateIpmpBean.format(stepInfo);
                            exeSqls.add(insertIpmp);

                            BeanFormatter<BusinessSystemBean> delIpmpBean = new BeanFormatter<>(sqlUpdateIpmp);
                            String delIpmp = delIpmpBean.format(oldIpmpSystemBean);
                            rollbackSqls.add(delIpmp);
                        } else
                        {
                            if (SyncOperationSystemManager.checkEmpty(stepInfo.getIpmpSysName()))
                            {
                                long ipmpIid = IdGenerator.createId("IEAI_IPMP_SYSTEM_RELATON", baseConn);
                                stepInfo.setIpmpIid(ipmpIid);
                                BeanFormatter<BusinessSystemBean> insertIpmpBean = new BeanFormatter<>(
                                        execSqlTemplateIpmp);
                                String insertIpmp = insertIpmpBean.format(stepInfo);
                                exeSqls.add(insertIpmp);

                                BeanFormatter<BusinessSystemBean> delIpmpBean = new BeanFormatter<>(delbsIpmp);
                                String delIpmp = delIpmpBean.format(stepInfo);
                                rollbackSqls.add(delIpmp);
                            }
                        }
                    }
                }

                // 自动赋权
                InfoExeclUtilBean infoExeclUtilBean = new InfoExeclUtilBean();
                RepProject insertProject = new RepProject();
                insertProject.setIupperId(Long.valueOf(stepInfo.getSystemId()));
                infoExeclUtilBean.setInsertProject(insertProject);
                infoExeclUtilBean.setExeSqls(exeSqls);
                infoExeclUtilBean.setRollbackSqls(rollbackSqls);
                UserInfo user = new UserInfo();
                user.setId(userId);
                InfoExeclServicesMultipleForEM.getInstance().buildIsextend(baseConn, user, infoExeclUtilBean);

            }

        } catch (RepositoryException e)
        {
            e.printStackTrace();
            isSuccess = false;
        } catch (Exception e)
        {
            e.printStackTrace();
            isSuccess = false;
        }
        res.put("success", isSuccess);
        res.put("exeSqls", exeSqls);
        res.put("rollbackSqls", rollbackSqls);
        res.put("ipmpNoExistSys", String.valueOf(ipmpNoExistSys));
        return res;
    }

    public Map<String, Object> organizeBSSqlForUpdate ( List<Map<String, Object>> stepInfos, Connection baseConn )
    {
        Map<String, Object> res = new HashMap<>();
        boolean isSuccess = true;
        List<String> exeSqls = new ArrayList<String>();
        List<String> rollbackSqls = new ArrayList<String>();

        String updateSql = "UPDATE IEAI_PROJECT SET ISYSTEMCODE = ";
        String whereSql = " WHERE IID = ";
        try
        {
            for (Map<String, Object> obj : stepInfos)
            {
                String tempUpdate = updateSql + "'"+obj.get("IINSTID").toString()+"'" + whereSql + obj.get("IID");
                String rollback = updateSql +  (obj.get("ISYSTEMCODE") == null ? null : ("'"+obj.get("ISYSTEMCODE").toString()+"'") ) + whereSql + obj.get("IID");
                 exeSqls.add(tempUpdate);
                rollbackSqls.add(rollback);
            }
        } catch (Exception e)
        {
            _log.error("organizeBSSqlForUpdate error " + e.getMessage());
            isSuccess = false;
        }
        res.put("success", isSuccess);
        res.put("exeSqls", exeSqls);
        res.put("rollbackSqls", rollbackSqls);
        return res;
    }

    /**
     * <li>Description:光大业务系统维护同步功能</li> 
     * <AUTHOR>
     * 2021年5月19日 
     * @param resList
     * @return
     * @throws RepositoryException
     * return Boolean
     */
    public Boolean insertXmlFile ( List<BusinessSystemBean> resList, int type ) throws RepositoryException
    {
        boolean flag = false;
        String sqlSelect = "SELECT INAME from IEAI_PROJECT  WHERE IYWXTIID = ?";
        String sqlUpdate = "UPDATE IEAI_PROJECT SET ISYSTEMCODE = ? ,INAME = ? WHERE IYWXTIID = ?";
        String sqlParam = " INSERT INTO IEAI_PROJECT(IID,INAME,IPKGCONTENTID,IGROUPID,PROTYPE,IUPLOADUSERID,IUPLOADUSER,ILATESTID,IUPPERID ,ISYSTEMCODE,IYWXTIID)"
                + " VALUES(?,?,?,?,?,?,?,?,?,?,?)";
        for (int i = 0;; i++)
        {
            try
            {
                Connection conn = null;
                PreparedStatement psRes = null;
                PreparedStatement psParam = null;
                PreparedStatement psParams = null;
                ResultSet instanceRS = null;
                try
                {
                    conn = DBResource.getConnection("insertXmlFile", _log, type);
                    psRes = conn.prepareStatement(sqlSelect);
                    psParam = conn.prepareStatement(sqlParam);
                    psParams = conn.prepareStatement(sqlUpdate);
                    BusinessSystemBean resBean = null;
                    for (int j = 0; j < resList.size(); j++)
                    {
                        String systemNameString = "";
                        resBean = resList.get(j);
                        if (StringUtils.isNotBlank(resBean.getIywxtiid()))
                        {

                            psRes.setString(1, resBean.getIywxtiid());
                            instanceRS = psRes.executeQuery();
                            while (instanceRS.next())
                            {
                                systemNameString = instanceRS.getString("INAME");
                            }
                            if (StringUtils.isBlank(systemNameString))
                            {
                                long iidP = IdGenerator.createId("IEAI_PROJECT", conn);
                                psParam.setLong(1, iidP);
                                psParam.setString(2, resBean.getSysName());
                                psParam.setLong(3, resBean.getPkgid());
                                psParam.setInt(4, resBean.getPrjType());
                                psParam.setInt(5, resBean.getPrjType());
                                psParam.setLong(6, resBean.getUserId());
                                psParam.setString(7, resBean.getUserName());
                                psParam.setLong(8, iidP);
                                psParam.setLong(9, iidP);
                                psParam.setString(10, resBean.getSystemCode());
                                psParam.setString(11, resBean.getIywxtiid());
                                psParam.executeUpdate();
                            } else
                            {
                                psParams.setString(1, resBean.getSystemCode());
                                psParams.setString(2, resBean.getSysName());
                                psParams.setString(3, resBean.getIywxtiid());
                                psParams.executeUpdate();
                            }
                        }
                    }
                    conn.commit();
                    flag = true;
                } catch (SQLException e)
                {
                    _log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_INSERT);
                } finally
                {
                    DBResource.closePreparedStatement(psParam,
                        Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                    DBResource.closePreparedStatement(psParams,
                        Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                    DBResource.closePreparedStatement(psRes, Thread.currentThread().getStackTrace()[1].getMethodName(),
                        _log);
                    DBResource.closeConnection(conn, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
        return flag;
    }

    /**
     * <li>Description:验证业务是否已经在表中存在，用于验证记录是insert或者update</li> 
     * <AUTHOR>
     * 2017年6月16日 
     * @param bean
     * @param type
     * @return
     * @throws RepositoryException
     * return boolean
     */
    public boolean existSBusinessSystem ( Map<String, Object> bsMap, int type ) throws RepositoryException
    {
        boolean result = true;
        String sql = "select count(*) EXISTCOUNT from ieai_project where 1=1 \r\n" + "and iid = ?";
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        if (bsMap != null && bsMap.size() > 0)
        {
            Long iid = (Long) Long.parseLong(bsMap.get("systemId").toString());
            if (iid == -1) // 当iid = -1时，说明该记录为新增记录
            {
                return false;
            }
            try
            {
                conn = DBResource.getConnection("saveBusinessSystem_rebuild", _log, type);
                actStat = conn.prepareStatement(sql);

                actStat.setLong(1, iid);// bsMap.get("systemId")
                actRS = actStat.executeQuery();
                while (actRS.next())
                {
                    if (actRS.getLong("EXISTCOUNT") <= 0)
                    {
                        result = false;
                    }
                }
            } catch (SQLException e)
            {
                result = false;
                DBResource.rollback(conn, ServerError.ERR_DB_QUERY, e, "existSBusinessSystem", _log);
                _log.error("existSBusinessSystem method of BusinessSystemManager.class SQLException:" + e.getMessage());
                throw new RepositoryException(ServerError.ERR_DB_QUERY);
            } finally
            {
                DBResource.closePSConn(conn, actStat, "existSBusinessSystem", _log);
            }
        }
        return result;
    }

    /**
     * <li>Description:</li> 
     * <AUTHOR>
     * 2016年7月11日 
     * @param bean
     * @param type
     * @return
     * @throws RepositoryException
     * return boolean
     */
    public boolean saveProInfo ( BusinessSystemBean bean, int type ) throws RepositoryException
    {
        boolean returnValue = true;
        Connection conn = null;
        PreparedStatement actStat2 = null;
        List<Map> sysLvlist = this.getSysLvList();
        for (int i = 0; i < 10; i++)
        {
            try
            {
                PreparedStatement ps = null;
                PreparedStatement cntPS = null;
                ResultSet cntRS = null;
                try
                {
                    BusinessSystemBean systemBean = BusinessSystemManager.getInstance()
                            .getProinfoOne(bean.getSystemId(), type);
                    // String sql_update_project_info = "UPDATE IEAI_PROJECT_INFO SET SENDCYCLE=?,
                    // MAILS=?,SENDSTATUS=?,SYSTYPE=?,SYSDESC=?, SYSPARAM=?,PRIORITY=? WHERE IID=?";
                    String sql_update_project_info = "UPDATE  IEAI_PROJECT_INFO SET SENDSTATUS=?,SYSTYPE=?,SYSDESC=?, SYSPARAM=?,PRIORITY=? WHERE IID=?";
                    String sql_insert_project_info = "INSERT INTO IEAI_PROJECT_INFO ( SENDSTATUS, SYSTYPE, SYSDESC, SYSPARAM, PRIORITY, IID) VALUES( ?, ?, ?, ?, ?, ?)";

                    conn = DBResource.getConnection("saveBusinessSystem_rebuild", _log, type);
                    if (systemBean == null)
                    {
                        actStat2 = conn.prepareStatement(sql_insert_project_info);
                    } else
                    {
                        actStat2 = conn.prepareStatement(sql_update_project_info);
                    }
                    Integer priority = (Integer) bean.getPriority();
                    String sendCycle = bean.getSendCycle();
                    String mails = bean.getMails();
                    Boolean sendStatus = bean.getMailSendStatus();
                    String sysDesc = bean.getSysDesc();
                    String sysParam = bean.getSysParam();
                    // actStat2.setString(1, sendCycle);
                    // actStat2.setString(2, mails);
                    actStat2.setLong(1, sendStatus == true ? 1 : 0);
                    String sysType = bean.getSysType();
                    Long sysTypeNO = 0L;
                    for (Map sysLvlMap : sysLvlist)
                    {
                        if (sysLvlMap.get("applvl").equals(sysType))
                        {
                            sysTypeNO = Long.valueOf(sysLvlMap.get("applvlId").toString());
                            break;
                        }
                    }
                    actStat2.setLong(2, sysTypeNO);
                    actStat2.setString(3, sysDesc == null ? "" : sysDesc.trim());
                    actStat2.setString(4, sysParam == null ? "" : sysParam.trim());
                    actStat2.setLong(5, priority);
//                    actStat2.setString(6, bean.getHandStart());
                    actStat2.setLong(6, bean.getSystemId());
                    actStat2.executeUpdate();

                    /*
                     * //test///////////////////////////
                     * //保存ieai_sendmail表
                     * int dayType = bean.getDayType();
                     * String plantime = sendCycle;
                     * 
                     * String sqlQry = "SELECT COUNT(IID) AS COU FROM IEAI_SENDMAIL WHERE IID=?";
                     * String sqlUpdate =
                     * "UPDATE IEAI_SENDMAIL SET SYSTEMID=?, SYSNAME=?, MAILS=?, SUMMARY =?, PLANNING_TIME=?, MSWITCH=?, DAY_TYPE=? WHERE IID=?"
                     * ;
                     * String sqlInsert =
                     * "INSERT INTO IEAI_SENDMAIL (STATUS, IID, SYSTEMID, SYSNAME, MAILS, SUMMARY, PLANNING_TIME ,MSWITCH,DAY_TYPE) VALUES (?, ?, ?, ?, ?, ?, ?, ? ,?)"
                     * ;
                     * cntPS = conn.prepareStatement(sqlQry);
                     * cntPS.setLong(1, bean.getSystemId());
                     * cntRS = cntPS.executeQuery();
                     * long cntNum = 0;
                     * if(cntRS.next())
                     * {
                     * cntNum = cntRS.getLong("COU");
                     * }
                     * if(cntNum>0)
                     * {
                     * //update
                     * ps = conn.prepareStatement(sqlUpdate);
                     * ps.setLong(1, bean.getSystemId());
                     * ps.setString(2, bean.getSysName());
                     * ps.setString(3, mails);
                     * ps.setString(4, bean.getSummary());
                     * ps.setTimestamp(5,new Timestamp(2000, 1, 1,
                     * Integer.parseInt(plantime.split(":")[0]),
                     * Integer.parseInt(plantime.split(":")[1]),
                     * Integer.parseInt(plantime.split(":")[2]), 0));
                     * ps.setInt(6, sendStatus == true ? 1 : 0);
                     * ps.setInt(7,dayType);
                     * ps.setLong(8, bean.getSystemId());
                     * }else
                     * {
                     * //insert
                     * ps = conn.prepareStatement(sqlInsert);
                     * ps.setLong(1, 1);//未发送
                     * ps.setLong(2, bean.getSystemId());
                     * ps.setLong(3, bean.getSystemId());
                     * ps.setString(4, bean.getSysName());
                     * ps.setString(5, mails);
                     * ps.setString(6, bean.getSummary());
                     * ps.setTimestamp(7,new Timestamp(2000, 1, 1,
                     * Integer.parseInt(plantime.split(":")[0]),
                     * Integer.parseInt(plantime.split(":")[1]),
                     * Integer.parseInt(plantime.split(":")[2]), 0));
                     * ps.setInt(8, sendStatus == true ? 1 : 0);
                     * ps.setInt(9,dayType);
                     * }
                     * ps.executeUpdate();
                     */
                    /////////////////////////////////

                    conn.commit();

                } catch (SQLException e)
                {
                    returnValue = false;
                    DBResource.rollback(conn, ServerError.ERR_DB_UPDATE, e, "saveProInfo", _log);
                    _log.error("saveProInfo method of BusinessSystemManager.class SQLException:" + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_INSERT);
                } finally
                {
                    DBResource.closePSRS(cntRS, cntPS, "saveProInfo", _log);
                    DBResource.closePreparedStatement(ps, "saveProInfo", _log);
                    DBResource.closePSConn(conn, actStat2, "saveProInfo", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                returnValue = false;
                DBResource.rollback(conn, ServerError.ERR_DB_INSERT, ex, "saveProInfo", _log);
                _log.error("saveProInfo method of BusinessSystemManager.class RepositoryException:" + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_INSERT);
            }
        }

        return returnValue;
    }

    /***
     * 
     * <li>Description:保存业务系统与设备的关联关系</li> 
     * <AUTHOR>
     * 2016年07月28日
     * @param bean
     * @param type
     * @return
     * @throws RepositoryException
     * return boolean
     */
    // public boolean saveBusinessSystemRelation ( List<Map<String, Object>> businessSystems,
    // long systemId, String userName ) throws RepositoryException
    // {
    // boolean returnValue = true;
    // Connection conn = null;
    // PreparedStatement actStat = null;
    // if (null != businessSystems && businessSystems.size() > 0)
    // {
    // for (int i = 0; i < 10; i++)
    // {
    // try
    // {
    // try
    // {
    // String sql = "MERGE INTO IEAI_SYS_RELATION D USING "
    // + "(SELECT T.SYSTEMID AS SYSTEMID ,T2.CPID AS COMPUTERID,T2.IP AS IP,1 AS
    // COMPUTERSTATE,T.SYSSTATE AS SYSSTATE,T.SYSPARAM AS SYSPARAM,T2.CENTERNAME AS
    // CENTERNAME,T.SYSNAME AS SYSNAME,T.PRJTYPE AS PRJTYPE FROM IEAI_SYS T,IEAI_COMPUTER_LIST T2
    // WHERE 1=1 AND T.SYSTEMID=? AND T2.CPID=?) S "
    // + "ON (D.SYSTEMID = S.SYSTEMID AND D.COMPUTERID=S.COMPUTERID) "
    // + "WHEN NOT MATCHED THEN INSERT
    // (SYSTEMID,COMPUTERID,IP,COMPUTERSTATE,SYSSTATE,SYSPARAM,CENTERNAME,SYSNAME,PRJTYPE) VALUES
    // (S.SYSTEMID,S.COMPUTERID,S.IP,S.COMPUTERSTATE,S.SYSSTATE,S.SYSPARAM,S.CENTERNAME,S.SYSNAME,S.PRJTYPE)";
    // // String sql =
    // // "INSERT INTO IEAI_SYS_RELATION
    // (SYSID,COMPUTERID,IP,COMPUTERSTATE,SYSSTATE,SYSPARAM,CENTERNAME,SYSNAME) "
    // // +
    // // "SELECT t.sysid,t2.id,t2.ip,t2.state,t.SYSSTATE,t.SYSPARAM,t2.centername,t.sysname FROM
    // IEAI_SYS t,IEAI_COMPUTER_LIST t2 WHERE 1=1 and t.sysid=? and t2.id=?";
    // conn = DBResource.getConnection("saveBusinessSystemRelation",
    // _log,Constants.IEAI_INFOCOLLECTION);
    // actStat = conn.prepareStatement(sql);
    // StringBuffer ComputerIds = new StringBuffer();
    // for (int z = 0; z < businessSystems.size(); z++)
    // {
    // Map<String, Object> bsMap = businessSystems.get(z);
    // Long computerId = (Long) Long.parseLong(bsMap.get("cpId").toString());
    // int index = 0;
    // actStat.setLong(++index, systemId);
    // actStat.setLong(++index, computerId);
    // actStat.addBatch();
    // ComputerIds.append(computerId + ",");
    // }
    // actStat.executeBatch();
    // conn.commit();
    // _log.info("saveBusinessSystemRelation {systemId="
    // + systemId + ",computerId=" + ComputerIds.toString() + "} by user:"+userName);
    //
    // } catch (SQLException e)
    // {
    // returnValue = false;
    // DBResource.rollback(conn, ServerError.ERR_DB_INSERT, e,
    // "saveBusinessSystemRelation", _log);
    // _log.error("saveBusinessSystemRelation method of BusinessSystemManager.class SQLException:"
    // + e.getMessage());
    // throw new RepositoryException(ServerError.ERR_DB_INSERT);
    // } finally
    // {
    // DBResource.closePSConn(conn, actStat, "saveBusinessSystemRelation", _log);
    // }
    // break;
    // } catch (RepositoryException ex)
    // {
    // returnValue = false;
    // DBResource.rollback(conn, ServerError.ERR_DB_INSERT, ex,
    // "saveBusinessSystemRelation", _log);
    // _log.error("saveBusinessSystemRelation method of BusinessSystemManager.class
    // RepositoryException:"
    // + ex.getMessage());
    // DBResource.throwRepositoryException(i, ServerError.ERR_DB_INSERT);
    // }
    // }
    // }
    //
    // return returnValue;
    // }
    public boolean saveBusinessSystemRelation_rebuild ( List<Map<String, Object>> businessSystems, long systemId,
            String userName, int type ) throws RepositoryException
    {
        boolean returnValue = true;
        Connection conn = null;
        PreparedStatement actStat = null;
        PreparedStatement psCnt = null;
        ResultSet rsCnt = null;
        if (null != businessSystems && businessSystems.size() > 0)
        {
            String sqlQry = " SELECT COUNT(*) AS NUM FROM IEAI_SYS_RELATION WHERE SYSTEMID=? AND COMPUTERID=? ";
            String sqlInsert = " INSERT INTO IEAI_SYS_RELATION (SYSTEMID,COMPUTERID,IP,COMPUTERSTATE,SYSSTATE,CENTERNAME,SYSNAME,PRJTYPE) "
                    + " SELECT  T.IID AS SYSTEMID , T2.CPID AS COMPUTERID, T2.IP AS IP, 1 AS COMPUTERSTATE, NULL AS SYSSTATE, T2.CENTERNAME AS CENTERNAME, T.INAME AS SYSNAME, T.PROTYPE AS PRJTYPE  FROM  IEAI_PROJECT  T, IEAI_COMPUTER_LIST T2   WHERE 1=1 AND T.IID=?  AND T2.CPID=? ";
            for (int i = 0; i < 10; i++)
            {
                try
                {
                    try
                    {
                        // String sql = " MERGE INTO IEAI_SYS_RELATION D USING ("
                        // +" SELECT"
                        // +" T.IID AS SYSTEMID ,"
                        // +" T2.CPID AS COMPUTERID,"
                        // +" T2.IP AS IP,"
                        // +" 1 AS COMPUTERSTATE,"
                        // +" NULL AS SYSSTATE,"
                        // +" T2.CENTERNAME AS CENTERNAME,"
                        // +" T.INAME AS SYSNAME,"
                        // +" T.PROTYPE AS PRJTYPE"
                        // +" FROM "
                        // +" IEAI_PROJECT T,"
                        // +" IEAI_COMPUTER_LIST T2 "
                        // +" WHERE 1=1 AND T.IID=? AND T2.CPID=?) "
                        // +" S ON (D.SYSTEMID = S.SYSTEMID AND D.COMPUTERID=S.COMPUTERID)"
                        // +" WHEN NOT MATCHED THEN INSERT"
                        // +"
                        // (SYSTEMID,COMPUTERID,IP,COMPUTERSTATE,SYSSTATE,CENTERNAME,SYSNAME,PRJTYPE)
                        // VALUES
                        // (S.SYSTEMID,S.COMPUTERID,S.IP,S.COMPUTERSTATE,S.SYSSTATE,S.CENTERNAME,S.SYSNAME,S.PRJTYPE)";

                        conn = DBResource.getConnection("saveBusinessSystemRelation_rebuild", _log, type);

                        // actStat = conn.prepareStatement(sql);
                        psCnt = conn.prepareStatement(sqlQry);
                        actStat = conn.prepareStatement(sqlInsert);

                        StringBuffer ComputerIds = new StringBuffer();
                        for (int z = 0; z < businessSystems.size(); z++)
                        {
                            Map<String, Object> bsMap = businessSystems.get(z);
                            Long computerId = (Long) Long.parseLong(bsMap.get("cpId").toString());
                            // int index = 0;
                            // actStat.setLong(++index, systemId);
                            // actStat.setLong(++index, computerId);
                            // actStat.addBatch();
                            // ComputerIds.append(computerId + ",");
                            // 查询是否存在
                            psCnt.setLong(1, systemId);
                            psCnt.setLong(2, computerId);
                            rsCnt = psCnt.executeQuery();
                            int num = 0;
                            if (rsCnt.next())
                            {
                                num = rsCnt.getInt("NUM");
                            }
                            if (num > 0)
                            {
                                // do nothing
                            } else
                            {
                                // insert
                                int index = 0;
                                actStat.setLong(++index, systemId);
                                actStat.setLong(++index, computerId);
                                actStat.addBatch();
                                ComputerIds.append(computerId + ",");
                            }

                        }
                        actStat.executeBatch();
                        conn.commit();
                        // _log.info("saveBusinessSystemRelation_rebuild {systemId=" + systemId + ",computerId="
                        // + ComputerIds.toString() + "} by user:" + userName);

                    } catch (SQLException e)
                    {
                        returnValue = false;
                        DBResource.rollback(conn, ServerError.ERR_DB_INSERT, e, "saveBusinessSystemRelation_rebuild",
                            _log);
                        _log.error(
                            "saveBusinessSystemRelation_rebuild method of BusinessSystemManager.class SQLException:"
                                    + e.getMessage());
                        throw new RepositoryException(ServerError.ERR_DB_INSERT);
                    } finally
                    {
                        DBResource.closePSRS(rsCnt, psCnt, "saveBusinessSystemRelation_rebuild", _log);
                        DBResource.closePSConn(conn, actStat, "saveBusinessSystemRelation_rebuild", _log);
                    }
                    break;
                } catch (RepositoryException ex)
                {
                    returnValue = false;
                    DBResource.rollback(conn, ServerError.ERR_DB_INSERT, ex, "saveBusinessSystemRelation_rebuild",
                        _log);
                    _log.error(
                        "saveBusinessSystemRelation_rebuild method of BusinessSystemManager.class RepositoryException:"
                                + ex.getMessage());
                    DBResource.throwRepositoryException(i, ServerError.ERR_DB_INSERT);
                }
            }
        }

        return returnValue;
    }

    /**
     * <li>Description:信息采集--任务启动--服务器信息--选择设备--添加设备</li> 
     * <AUTHOR>
     * 2016年7月27日 
     * @param businessSystems
     * @param systemId
     * @param userName
     * @return
     * @throws RepositoryException
     * return boolean
     */
    public boolean saveBusinessSystemRelation_rebuild_ForIC ( List<Map<String, Object>> businessSystems, long systemId,
            String userName ) throws RepositoryException
    {
        boolean returnValue = true;
        Connection conn = null;
        PreparedStatement actStat = null;
        if (null != businessSystems && businessSystems.size() > 0)
        {
            for (int i = 0; i < 10; i++)
            {
                try
                {
                    try
                    {
                        String sql = "MERGE INTO IEAI_SYS_RELATION D USING ( "
                                + " SELECT  T.IID AS SYSTEMID , T2.CPID AS COMPUTERID, T2.IP AS IP, 1 AS COMPUTERSTATE, NULL AS SYSSTATE, "
                                + " NULL AS SYSPARAM, T2.CENTERNAME AS CENTERNAME, T.INAME AS SYSNAME, T.PROTYPE AS PRJTYPE  FROM  IEAI_PROJECT  T, IEAI_COMPUTER_LIST T2  WHERE 1=1 AND T.IID=?  AND T2.CPID=? "
                                + " )  S ON (D.SYSTEMID = S.SYSTEMID AND D.COMPUTERID=S.COMPUTERID)  WHEN NOT MATCHED THEN INSERT  (SYSTEMID,COMPUTERID,IP,COMPUTERSTATE,SYSSTATE,SYSPARAM,CENTERNAME,SYSNAME,PRJTYPE) VALUES (S.SYSTEMID,S.COMPUTERID,S.IP,S.COMPUTERSTATE,S.SYSSTATE,S.SYSPARAM,S.CENTERNAME,S.SYSNAME,S.PRJTYPE)";
                        conn = DBResource.getConnection("saveBusinessSystemRelation_rebuild_ForIC", _log,
                            Constants.IEAI_HEALTH_INSPECTION);
                        actStat = conn.prepareStatement(sql);
                        StringBuffer ComputerIds = new StringBuffer();
                        for (int z = 0; z < businessSystems.size(); z++)
                        {
                            Map<String, Object> bsMap = businessSystems.get(z);
                            Long computerId = (Long) Long.parseLong(bsMap.get("cpId").toString());
                            int index = 0;
                            actStat.setLong(++index, systemId);
                            actStat.setLong(++index, computerId);
                            // actStat.setLong(++index, systemId);
                            actStat.addBatch();
                            ComputerIds.append(computerId + ",");
                        }
                        actStat.executeBatch();
                        conn.commit();
                        _log.info("saveBusinessSystemRelation_rebuild_ForIC {systemId=" + systemId + ",computerId="
                                + ComputerIds.toString() + "} by user:" + userName);

                    } catch (SQLException e)
                    {
                        returnValue = false;
                        DBResource.rollback(conn, ServerError.ERR_DB_INSERT, e,
                            "saveBusinessSystemRelation_rebuild_ForIC", _log);
                        _log.error(
                            "saveBusinessSystemRelation_rebuild_ForIC method of BusinessSystemManager.class SQLException:"
                                    + e.getMessage());
                        throw new RepositoryException(ServerError.ERR_DB_INSERT);
                    } finally
                    {
                        DBResource.closePSConn(conn, actStat, "saveBusinessSystemRelation_rebuild_ForIC", _log);
                    }
                    break;
                } catch (RepositoryException ex)
                {
                    returnValue = false;
                    DBResource.rollback(conn, ServerError.ERR_DB_INSERT, ex, "saveBusinessSystemRelation_rebuild_ForIC",
                        _log);
                    _log.error(
                        "saveBusinessSystemRelation_rebuild_ForIC method of BusinessSystemManager.class RepositoryException:"
                                + ex.getMessage());
                    DBResource.throwRepositoryException(i, ServerError.ERR_DB_INSERT);
                }
            }
        }

        return returnValue;
    }

    /***
     * 
     * <li>Description:判断业务系统名是否存在</li> 
     * <AUTHOR>
     * 2015年10月16日
     * @param businessSystems
     * @return
     * @throws RepositoryException
     * return List<String>
     */
    public List<String> checkBusinessSystemName ( List<Map<String, Object>> businessSystems ) throws RepositoryException
    {
        List<String> returnList = new ArrayList<String>();
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        if (null != businessSystems && businessSystems.size() > 0)
        {
            for (int i = 0; i < 10; i++)
            {
                try
                {
                    try
                    {
                        String sql = "SELECT 1 AS sys FROM IEAI_PROJECT T WHERE T.INAME=?  ";
                        conn = DBResource.getConnection("checkBusinessSystemName", _log,
                            Constants.IEAI_HEALTH_INSPECTION);
                        actStat = conn.prepareStatement(sql);
                        for (int z = 0; z < businessSystems.size(); z++)
                        {
                            Map<String, Object> bsMap = businessSystems.get(z);
                            String sysName = (String) bsMap.get("sysName");
                            actStat.setString(1, sysName);
                            actRS = actStat.executeQuery();
                            if (actRS.next())
                            {
                                returnList.add(sysName);
                                break;
                            }
                        }

                    } catch (SQLException e)
                    {
                        _log.error("checkBusinessSystemName method of BusinessSystemManager.class SQLException:"
                                + e.getMessage());
                        throw new RepositoryException(ServerError.ERR_DB_QUERY);
                    } finally
                    {
                        DBResource.closeConn(conn, actRS, actStat, "checkBusinessSystemName", _log);
                    }
                    break;
                } catch (RepositoryException ex)
                {
                    _log.error("checkBusinessSystemName method of BusinessSystemManager.class RepositoryException:"
                            + ex.getMessage());
                    DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
                }
            }
        }

        return returnList;
    }

    /**
     * 
     * @Title: checkBusinessSystemNameAndProtype   
     * @Description: 判断相同类型，业务系统名是否存在
     * @param businessSystems
     * @return
     * @throws RepositoryException      
     * @author: manxi_zhao. 
     * @date:   2017年9月6日 上午9:01:20
     */
    public List<String> checkBusinessSystemNameAndProtype ( List<Map<String, Object>> businessSystems )
            throws RepositoryException
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        List<String> returnList = new ArrayList<String>();
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        if (null != businessSystems && businessSystems.size() > 0)
        {
            for (int i = 0; i < 10; i++)
            {
                try
                {
                    try
                    {
                        String sql = "SELECT 1 AS sys FROM IEAI_PROJECT T WHERE T.INAME=?  " + " and PROTYPE=?  ";
                        conn = DBResource.getConnection(method, _log, Constants.IEAI_HEALTH_INSPECTION);
                        actStat = conn.prepareStatement(sql);
                        for (int z = 0; z < businessSystems.size(); z++)
                        {
                            Map<String, Object> bsMap = businessSystems.get(z);
                            String sysName = (String) bsMap.get("sysName");
                            actStat.setString(1, sysName);
                            int prjType = Integer.parseInt(String.valueOf(bsMap.get("prjType")));
                            actStat.setInt(2, prjType);
                            actRS = actStat.executeQuery();
                            if (actRS.next())
                            {
                                returnList.add(sysName);
                                break;
                            }
                        }

                    } catch (SQLException e)
                    {
                        _log.error(method + " method of BusinessSystemManager.class SQLException:" + e.getMessage());
                        throw new RepositoryException(ServerError.ERR_DB_QUERY);
                    } finally
                    {
                        DBResource.closeConn(conn, actRS, actStat, method, _log);
                    }
                    break;
                } catch (RepositoryException ex)
                {
                    _log.error(
                        method + " method of BusinessSystemManager.class RepositoryException:" + ex.getMessage());
                    DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
                }
            }
        }

        return returnList;
    }
    
    /**
     * 
     * @Title: checkSystemCodeNew   
     * @Description: 判断相同类型，系统编码是否存在
     * @param businessSystems
     * @return
     * @throws RepositoryException      
     * @author: liqiang. 
     * @date:   2021年10月11日 上午10:18:08
     */
    public List<String> checkSystemCodeNew ( List<Map<String, Object>> businessSystems ) throws RepositoryException
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        List<String> returnList = new ArrayList<String>();
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        if (null != businessSystems && businessSystems.size() > 0)
        {
            for (int i = 0; i < 10; i++)
            {
                try
                {
                    try
                    {
                        String sql = "SELECT INAME,ISYSTEMNUMBER FROM IEAI_PROJECT T WHERE T.ISYSTEMNUMBER=?  " + " and PROTYPE=?  ";
                        conn = DBResource.getConnection(method, _log, Constants.IEAI_IEAI_BASIC);
                        actStat = conn.prepareStatement(sql);
                        for (int z = 0; z < businessSystems.size(); z++)
                        {
                            Map<String, Object> bsMap = businessSystems.get(z);
                            String sysName = (String) bsMap.get("sysName");
                            String systemNumber = (String) bsMap.get("systemNumber");
                            actStat.setString(1, systemNumber);
                            int prjType = Integer.parseInt(String.valueOf(bsMap.get("prjType")));
                            actStat.setInt(2, prjType);
                            actRS = actStat.executeQuery();
                            if (actRS.next())
                            {
                                if(!sysName.equals(actRS.getString("INAME"))){
                                    returnList.add(systemNumber);
                                    break;
                                }
                            }
                        }

                    } catch (SQLException e)
                    {
                        _log.error(method + " method of BusinessSystemManager.class SQLException:" + e.getMessage());
                        throw new RepositoryException(ServerError.ERR_DB_QUERY);
                    } finally
                    {
                        DBResource.closeConn(conn, actRS, actStat, method, _log);
                    }
                    break;
                } catch (RepositoryException ex)
                {
                    _log.error(
                        method + " method of BusinessSystemManager.class RepositoryException:" + ex.getMessage());
                    DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
                }
            }
        }

        return returnList;
    }

    /**
     * 
     * @Title: checkTimeTaskSystemNameAndProtype   
     * @Description:  判断类型为定时任务相同类型，业务系统名是否存在   
     * @param businessSystems
     * @return
     * @throws RepositoryException      
     * @author: haijiao_dong 
     * @date:   2019年6月10日 上午8:09:35
     */
    public List<String> checkTimeTaskSystemNameAndProtype ( List<Map<String, Object>> businessSystems )
            throws RepositoryException
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        List<String> returnList = new ArrayList<String>();
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        PreparedStatement actStat1 = null;
        ResultSet actRS1 = null;
        if (null != businessSystems && !businessSystems.isEmpty())
        {
            for (int i = 0;; i++)
            {
                try
                {
                    try
                    {
                        String sql = "SELECT 1 AS sys FROM IEAI_PROJECT T WHERE T.INAME=?  " + " and PROTYPE=?  ";
                        String sqlu = "SELECT 1 AS sys FROM IEAI_PROJECT T WHERE T.INAME=?  "
                                + " and PROTYPE=? AND  T.IID <>?  ";
                        conn = DBResource.getConnection(method, _log, Constants.IEAI_TIMINGTASK);
                        actStat = conn.prepareStatement(sql);
                        actStat1 = conn.prepareStatement(sqlu);
                        for (int z = 0; z < businessSystems.size(); z++)
                        {

                            Map<String, Object> bsMap = businessSystems.get(z);
                            String sysName = (String) bsMap.get("sysName");
                            if (-1 != (Long) Long.parseLong(bsMap.get("systemId").toString()))
                            {// 修改
                                actStat1.setString(1, sysName);
                                actStat1.setInt(2, Constants.IEAI_TIMINGTASK);
                                actStat1.setLong(3, Long.parseLong(bsMap.get("systemId").toString()));
                                actRS1 = actStat1.executeQuery();
                                if (actRS1.next())
                                {
                                    returnList.add(sysName);
                                    break;
                                }
                            } else
                            {
                                actStat.setString(1, sysName);
                                actStat.setInt(2, Constants.IEAI_TIMINGTASK);
                                actRS = actStat.executeQuery();
                                if (actRS.next())
                                {
                                    returnList.add(sysName);
                                    break;
                                }
                            }

                        }

                    } catch (SQLException e)
                    {
                        _log.error(method + " method of BusinessSystemManager.class SQLException:" + e.getMessage());
                        throw new RepositoryException(ServerError.ERR_DB_QUERY);
                    } finally
                    {
                        DBResource.closePSRS(actRS1, actStat1, method, _log);
                        DBResource.closeConn(conn, actRS, actStat, method, _log);
                    }
                    break;
                } catch (RepositoryException ex)
                {
                    _log.error(
                        method + " method of BusinessSystemManager.class RepositoryException:" + ex.getMessage());
                    DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
                }
            }
        }

        return returnList;
    }

    /***
     * 
     * <li>Description:通过用户id获取可操作的业务系统id</li> 
     * <AUTHOR>
     * 2015年10月19日 
     * @param userId
     * @param loginName
     * @return
     * @throws RepositoryException
     * return Map
     */
    public Map getBusinessSystemNameListByRole ( String userId, String loginName ) throws RepositoryException
    {

        Map returnMap = new HashMap();
        boolean isNeedIn = true;// 是否需要In查询条件
        if (UserBasicInfo.SA.equals(loginName))
        {
            isNeedIn = false;
            returnMap.put("isNeedIn", isNeedIn);
            return returnMap;
        }
        String sql = "SELECT T2.ISYSID AS SYSTEMID  FROM IEAI_USERINHERIT T ,IEAI_SYS_PERMISSION T2 WHERE T.IROLEID=T2.IROLEID AND T2.IPERMISSION=1 AND  T.IUSERID=? ";

        String sql1 = "SELECT T.SYSTEMID AS SYSTEMID FROM IEAI_SYS T WHERE T.PRJTYPE=4 ";

        String sql2 = "SELECT T.SYSTEMID AS SYSTEMID FROM IEAI_SYS T WHERE T.PRJTYPE=5 ";

        for (int i = 0; i < 10; i++)
        {
            try
            {
                List<Long> list = new ArrayList<Long>();
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("getBusinessSystemNameListByRole", _log,
                        Constants.IEAI_HEALTH_INSPECTION);
                    actStat = con.prepareStatement(sql);
                    actStat.setLong(1, Long.parseLong(userId));
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        if (!list.contains(actRS.getLong("SYSTEMID")))
                        {
                            list.add(actRS.getLong("SYSTEMID"));
                        }
                    }
                    Long allzb = new Long(-4);
                    Long allxj = new Long(-5);
                    if (list.contains(allzb) && list.contains(allxj))
                    {
                        isNeedIn = false;
                        returnMap.put("isNeedIn", isNeedIn);
                        return returnMap;
                    }
                    // 如果包含id为-4的角色，则将所有灾备业务系统取出
                    else if (list.contains(allzb))
                    {
                        actStat = con.prepareStatement(sql1);
                        actRS = actStat.executeQuery();
                        while (actRS.next())
                        {
                            if (!list.contains(actRS.getLong("SYSTEMID")))
                            {
                                list.add(actRS.getLong("SYSTEMID"));
                            }
                        }
                    }
                    // 如果包含id为-5的角色，则将所有巡检业务系统取出
                    else if (list.contains(allxj))
                    {
                        actStat = con.prepareStatement(sql2);
                        actRS = actStat.executeQuery();
                        while (actRS.next())
                        {
                            if (!list.contains(actRS.getLong("SYSTEMID")))
                            {
                                list.add(actRS.getLong("SYSTEMID"));
                            }
                        }
                    }
                    returnMap.put("sysIdList", list);
                } catch (SQLException e)
                {
                    _log.error("getBusinessSystemNameListByRole is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, actRS, actStat, "getBusinessSystemNameListByRole", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log.error("getBusinessSystemNameListByRole method of BusinessSystemManager.class RepositoryException:"
                        + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        returnMap.put("isNeedIn", isNeedIn);
        return returnMap;
    }

    /**
     * 
     * <li>Description: 为邮件发送提供单个业务系统的邮件数据</li> 
     * <AUTHOR>
     * 2016年6月12日 
     * @param sysid
     * @return
     * @throws RepositoryException
     * return BusinessSystemBean
     */
    public BusinessSystemBean queryOneBusiness ( Long sysid, int type ) throws RepositoryException
    {
        BusinessSystemBean bean = new BusinessSystemBean();
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        String sql = "SELECT S.SYSNAME,S.MAILS FROM IEAI_SENDMAIL S  WHERE  S.SYSTEMID = ?";
        for (int i = 0; i < 10; i++)
        {
            try
            {
                try
                {
                    conn = DBResource.getConnection("queryOneBusiness", _log, type);
                    actStat = conn.prepareStatement(sql);
                    actStat.setLong(1, sysid);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        bean.setSystemId(sysid);
                        bean.setSysName(actRS.getString("SYSNAME"));
                        bean.setMails(actRS.getString("MAILS"));
                    }
                } catch (SQLException e)
                {
                    _log.error("queryOneBusiness method of BusinessSystemManager.class SQLException:" + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, actRS, actStat, "queryOneBusiness", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log.error(
                    "queryOneBusiness method of BusinessSystemManager.class RepositoryException:" + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }

        return bean;
    }

    /***
     * 
     * <li>Description:根据当前登录人获取当前登录人邮箱</li> 
     * <AUTHOR>
     * 2020年10月27日 
     * @param sysid
     * @param type
     * @return
     * @throws RepositoryException
     * return BusinessSystemBean
     */
    public BusinessSystemBean queryLoginNameEmail ( String loginName, int type ) throws RepositoryException
    {
        BusinessSystemBean bean = new BusinessSystemBean();
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        String sql = "SELECT IEMAIL FROM IEAI_USER WHERE ILOGINNAME = ?";
        for (int i = 0; i < 10; i++)
        {
            try
            {
                try
                {
                    conn = DBResource.getConnection("queryLoginNameEmail", _log, type);
                    actStat = conn.prepareStatement(sql);
                    actStat.setString(1, loginName);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        bean.setMails(actRS.getString("IEMAIL"));
                    }
                } catch (SQLException e)
                {
                    _log.error(
                        "queryLoginNameEmail method of BusinessSystemManager.class SQLException:" + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, actRS, actStat, "queryLoginNameEmail", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log.error(
                    "queryLoginNameEmail method of BusinessSystemManager.class RepositoryException:" + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }

        return bean;
    }

    /**
     * 
     * <li>Description:将可以发送邮件的数据转移到临时表</li> 
     * <AUTHOR>
     * 2016年6月12日 
     * @param conn
     * @return
     * @throws RepositoryException
     * return BusinessSystemBean
     */
    public BusinessSystemBean findSendSysData ( Connection conn ) throws RepositoryException
    {
        BusinessSystemBean bean = new BusinessSystemBean();
        CallableStatement call = null;
        for (int i = 0; i < 10; i++)
        {
            try
            {
                try
                {
                    call = conn.prepareCall("{call PROC_FIND_SEND_MAILS()}");
                    call.execute();
                } catch (SQLException e)
                {
                    _log.error("findSendSysData method of BusinessSystemManager.class SQLException:" + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeCallableStatement(call, "findSendSysData", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log.error(
                    "findSendSysData method of BusinessSystemManager.class RepositoryException:" + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return bean;
    }

    /**
     * 
     * <li>Description:将所有已经发送完成的状态进行初始化</li> 
     * <AUTHOR>
     * 2016年6月13日 
     * @return
     * @throws RepositoryException
     * return BusinessSystemBean
     */
    public BusinessSystemBean initMailSendStatus () throws RepositoryException
    {
        BusinessSystemBean bean = new BusinessSystemBean();
        Connection conn = null;
        PreparedStatement actStat = null;
        String sql = "UPDATE IEAI_SENDMAIL SET STATUS =1 WHERE STATUS <>0";
        for (int i = 0; i < 10; i++)
        {
            try
            {
                try
                {
                    conn = DBResource.getConnection("initMailSendStatus", _log, Constants.IEAI_HEALTH_INSPECTION);
                    actStat = conn.prepareStatement(sql);
                    actStat.executeUpdate();
                    conn.commit();
                } catch (SQLException e)
                {
                    _log.error(
                        "initMailSendStatus method of BusinessSystemManager.class SQLException:" + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closePSConn(conn, actStat, "initMailSendStatus", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log.error(
                    "initMailSendStatus method of BusinessSystemManager.class RepositoryException:" + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return bean;
    }

    /**
     * 
     * <li>Description:返回临时表中要进行邮件发送的数据</li> 
     * <AUTHOR>
     * 2016年6月12日 
     * @param conn
     * @return
     * @throws RepositoryException
     * return List<BusinessSystemBean>
     */
    public List<BusinessSystemBean> querySendData ( Connection conn ) throws RepositoryException
    {
        List<BusinessSystemBean> rets = new ArrayList<BusinessSystemBean>();
        BusinessSystemBean bean = new BusinessSystemBean();
        ResultSet actRS = null;
        PreparedStatement actStat = null;
        String sql = "SELECT B.IID, B.SYSTEMID, B.SYSNAME, B.MAILS, B.PLANNING_TIME, B.STATUS FROM (SELECT SYSTEMID, MAX(PLANNING_TIME) AS PLANTIME FROM IEAI_SENDMAIL WHERE PLANNING_TIME <= CURRENT TIME GROUP BY SYSTEMID) A, IEAI_SENDMAIL B WHERE B.SYSTEMID = A.SYSTEMID AND B.PLANNING_TIME = A.PLANTIME AND B.STATUS = 1";
        for (int i = 0; i < 10; i++)
        {
            try
            {
                try
                {
                    actStat = conn.prepareStatement(sql);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        bean = new BusinessSystemBean();
                        bean.setMailId(actRS.getLong("IID"));
                        bean.setSystemId(actRS.getLong("SYSTEMID"));
                        bean.setMails(actRS.getString("MAILS"));
                        bean.setSysName(actRS.getString("SYSNAME"));
                        rets.add(bean);
                    }
                } catch (SQLException e)
                {
                    _log.error("querySendData method of BusinessSystemManager.class SQLException:" + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closePSRS(actRS, actStat, "querySendData", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log.error(
                    "querySendData method of BusinessSystemManager.class RepositoryException:" + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return rets;
    }

    /**
     * 
     * <li>Description:更新已将发送完毕的数据</li> 
     * <AUTHOR>
     * 2016年6月12日 
     * @param conn
     * @param systemid
     * @return
     * @throws RepositoryException
     * return Integer
     */
    public Integer updateSendStatusToOld ( Connection conn ) throws RepositoryException
    {
        Integer ret = null;
        PreparedStatement actStat = null;
        String sql = "UPDATE IEAI_SYS A SET A.SENDSTATUS = 2 WHERE A.SYSTEMID = (SELECT DISTINCT B.SYSTEMID FROM TMP_IEAI_SENDMAIL B WHERE A.SYSTEMID = B.SYSTEMID)";
        for (int i = 0; i < 10; i++)
        {
            try
            {
                try
                {
                    actStat = conn.prepareStatement(sql);
                    ret = actStat.executeUpdate();
                } catch (SQLException e)
                {
                    _log.error(
                        "updateSendStatusToOld method of BusinessSystemManager.class SQLException:" + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closePreparedStatement(actStat, "updateSendStatusToOld", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log.error("updateSendStatusToOld method of BusinessSystemManager.class RepositoryException:"
                        + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return ret;
    }

    public Integer updateSendStatusToOld ( BusinessSystemBean bean ) throws RepositoryException
    {
        Integer ret = null;
        Connection conn = null;
        PreparedStatement actStat = null;
        String sql = "UPDATE IEAI_SENDMAIL SET STATUS = 2 WHERE IID = ?";
        for (int i = 0; i < 10; i++)
        {
            try
            {
                try
                {
                    conn = DBResource.getConnection("updateSendStatusToOld", _log, Constants.IEAI_HEALTH_INSPECTION);
                    actStat = conn.prepareStatement(sql);
                    actStat.setLong(1, bean.getMailId());
                    ret = actStat.executeUpdate();
                    conn.commit();
                } catch (SQLException e)
                {
                    _log.error(
                        "updateSendStatusToOld method of BusinessSystemManager.class SQLException:" + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closePSConn(conn, actStat, "updateSendStatusToOld", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log.error("updateSendStatusToOld method of BusinessSystemManager.class RepositoryException:"
                        + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return ret;
    }

    /***
     * 
     * <li>Description:判断业务系统（灾备）是否正在运行中</li> 
     * <AUTHOR>
     * 2015年12月1日 
     * @param deleteIds
     * @return
     * @throws RepositoryException
     * return Map<String,Object>
     */
    public Map<String, Object> chekcBusinessSystemRuning ( Long[] deleteIds ) throws RepositoryException
    {
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        Map<String, Object> resp = new HashMap<String, Object>();
        if (null != deleteIds && deleteIds.length > 0)
        {
            for (int i = 0; i < 10; i++)
            {
                try
                {
                    try
                    {

                        List<String> systemNameList = new ArrayList<String>();
                        conn = DBResource.getConnection("chekcBusinessSystemRuning", _log,
                            Constants.IEAI_HEALTH_INSPECTION);
                        String sql1 = "SELECT * FROM IEAI_SYS T WHERE  T.SYSTEMID IN ("
                                + StringUtils.join(deleteIds, ",") + ")";
                        actStat = conn.prepareStatement(sql1);
                        actRS = actStat.executeQuery();
                        while (actRS.next())
                        {
                            systemNameList.add(actRS.getString("SYSNAME"));
                        }
                        if (systemNameList.size() > 0)
                        {
                            String[] arr = (String[]) systemNameList.toArray(new String[systemNameList.size()]);
                            String sql2 = "SELECT T.IPROJECTNAME FROM IEAI_WORKFLOWINSTANCE T WHERE T.ISTATUS IN(0,8) AND T.IPROJECTNAME IN ('"
                                    + StringUtils.join(arr, ",") + "')";
                            actStat = conn.prepareStatement(sql2);
                            actRS = actStat.executeQuery();
                            while (actRS.next())
                            {
                                resp.put("success", false);
                                resp.put("message", "【" + actRS.getString("IPROJECTNAME") + "】业务系统正在运行，不可以删除！");
                                return resp;
                            }
                        }

                    } catch (SQLException e)
                    {
                        _log.error("chekcBusinessSystemRuning method of BusinessSystemManager.class SQLException:"
                                + e.getMessage());
                        throw new RepositoryException(ServerError.ERR_DB_QUERY);
                    } finally
                    {
                        DBResource.closeConn(conn, actRS, actStat, "chekcBusinessSystemRuning", _log);
                    }
                    break;
                } catch (RepositoryException ex)
                {
                    _log.error("chekcBusinessSystemRuning method of BusinessSystemManager.class RepositoryException:"
                            + ex.getMessage());
                    DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
                }
            }
        }
        resp.put("success", true);
        resp.put("message", null);

        return resp;
    }

    /***
     * 
     * <li>Description:判断业务系统（作业调度）是否正在运行中</li> 
     * <AUTHOR>
     * 2017-06-15迁移 
     * @param deleteIds
     * @param type
     * @return
     * @throws RepositoryException
     * return Map<String,Object>
     */
    public Map<String, Object> chekcBusinessSystemRuningForUT ( Long[] deleteIds, int type ) throws RepositoryException
    {
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        Map<String, Object> resp = new HashMap<String, Object>();
        if (null != deleteIds && deleteIds.length > 0)
        {
            for (int i = 0; i < 10; i++)
            {
                try
                {
                    try
                    {

                        List<String> systemNameList = new ArrayList<String>();
                        conn = DBResource.getConnection("chekcBusinessSystemRuningForUT", _log, type);
                        // String sql1 = "SELECT * FROM IEAI_SYS T WHERE T.SYSTEMID IN ("
                        String sql1 = "SELECT * FROM IEAI_PROJECT T WHERE  T.IID IN ("
                                + StringUtils.join(deleteIds, ",") + ")";
                        sql1 += " AND T.PROTYPE= " + type;
                        actStat = conn.prepareStatement(sql1);
                        actRS = actStat.executeQuery();
                        while (actRS.next())
                        {
                            // systemNameList.add(actRS.getString("SYSNAME"));
                            systemNameList.add(actRS.getString("INAME"));
                        }
                        if (systemNameList.size() > 0)
                        {
                            String[] arr = (String[]) systemNameList.toArray(new String[systemNameList.size()]);
                            String sql2 = "SELECT T.IPROJECTNAME FROM IEAI_WORKFLOWINSTANCE T WHERE T.ISTATUS IN(0,8) AND T.IPROJECTNAME IN ('"
                                    + StringUtils.join(arr, ",") + "')";
                            actStat = conn.prepareStatement(sql2);
                            actRS = actStat.executeQuery();
                            while (actRS.next())
                            {
                                resp.put("success", false);
                                resp.put("message", "【" + actRS.getString("IPROJECTNAME") + "】业务系统正在运行，不可以删除！");
                                return resp;
                            }
                        }

                    } catch (SQLException e)
                    {
                        _log.error("chekcBusinessSystemRuningForUT method of BusinessSystemManager.class SQLException:"
                                + e.getMessage());
                        throw new RepositoryException(ServerError.ERR_DB_QUERY);
                    } finally
                    {
                        DBResource.closeConn(conn, actRS, actStat, "chekcBusinessSystemRuningForUT", _log);
                    }
                    break;
                } catch (RepositoryException ex)
                {
                    _log.error(
                        "chekcBusinessSystemRuningForUT method of BusinessSystemManager.class RepositoryException:"
                                + ex.getMessage());
                    DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
                }
            }
        }
        resp.put("success", true);
        resp.put("message", null);

        return resp;
    }

    /**   
     * @Title: sqlWhereBusinessSystemList   
     * @Description: 降低复杂度   
     * @param businessSystemBeanForQuery
     * @return      
     * @author: Sayai 
     * @date:   2019年1月21日 下午4:22:18   
     */
    private String sqlWhereBusinessSystemList ( BusinessSystemBeanForQuery businessSystemBeanForQuery,List<String> ls )
    {
        String sqlWhere = "  WHERE (PJ.IUPPERID=QX.IPROID OR  PJ.PROTYPE=( DECODE (-QX.IPROID, 2, 7,-QX.IPROID))  OR PJ.PROTYPE= DECODE (-QX.IPROID, 2,8 ,-QX.IPROID))"
                + "  AND PJ.IID>0  AND PJ.IID=PJ.ILATESTID  ";
        if (JudgeDB.IEAI_DB_TYPE == 3)
        {
            sqlWhere = "  WHERE (PJ.IUPPERID=QX.IPROID OR  PJ.PROTYPE=( IF (-QX.IPROID=2, 7,-QX.IPROID))  OR PJ.PROTYPE=  IF(-QX.IPROID=2,8 ,-QX.IPROID))"
                    + "  AND PJ.IID>0  AND PJ.IID=PJ.ILATESTID  ";

        }
        // 业务系统名
        if (businessSystemBeanForQuery.getSysName() != null && !"".equals(businessSystemBeanForQuery.getSysName()))
        {
            String prjName = businessSystemBeanForQuery.getSysName();
            if (prjName.contains("_") || prjName.contains("%") || prjName.contains("'"))
            {
                prjName = prjName.replaceAll("_", "/_");
                prjName = prjName.replaceAll("%", "/%");
                prjName = prjName.replaceAll("\'", "\\''");
            }
            if (ServerEnv.getInstance().getJobSchedulingQuerySystemSwitch())
            {
                // 锦州版本根据工程名或业务系统查询
                sqlWhere = sqlWhere + " AND ((UPPER(PJ.INAME) LIKE ? escape '/') OR (UPPER(PJ.ISYSNAME) LIKE ? escape '/') ) ";
                ls.add("%"+prjName.toUpperCase()+"%");ls.add("%"+prjName.toUpperCase()+"%");
            } else
            {
                sqlWhere = sqlWhere + " AND UPPER(PJ.INAME) LIKE ? escape '/'";
                ls.add("%"+prjName.toUpperCase()+"%");
            }
        }
        if (ServerEnv.getInstance().getJobSchedulingQuerySystemSwitch())
        {
            // 锦州版本查询工程ID最大的数据
            sqlWhere = sqlWhere + " AND PJ.IID IN(SELECT MAX(IID) FROM IEAI_PROJECT GROUP BY INAME) ";
        }
        if(Environment.getInstance().getBooleanConfig(Environment.FJNX_SYNC_SYSTEM_SWITCH, false)){
            if (businessSystemBeanForQuery.getPrjType() != 0)
            {
                sqlWhere = sqlWhere + "  AND  PJ.PROTYPE=?  AND IPKGCONTENTID=0";
            } else
            {
                sqlWhere = sqlWhere + "  AND ( PJ.PROTYPE=1 OR PJ.PROTYPE=7 OR PJ.PROTYPE=8 OR IPKGCONTENTID=0 ) ";
            }
        }else{
            if (businessSystemBeanForQuery.getPrjType() != 0)
            {
                sqlWhere = sqlWhere + "  AND  PJ.PROTYPE=? ";
            } else
            {
                sqlWhere = sqlWhere + "  AND ( PJ.PROTYPE=1 OR PJ.PROTYPE=7 OR PJ.PROTYPE=8 OR IPKGCONTENTID=0 ) ";
            }
        }

        return sqlWhere;
    }

    /**   
     * @Title: sqlQueryBusinessSystemList   
     * @Description: 拆分复杂度后细粒度方法，改方法主要为组织查询SQL使用。   
     * @param businessSystemBeanForQuery
     * @param sqlWhere
     * @return      
     * @author: Sayai 
     * @date:   2019年1月21日 下午3:37:09   
     */
    private String sqlQueryBusinessSystemList ( BusinessSystemBeanForQuery businessSystemBeanForQuery, String sqlWhere )
    {
        String orderString = " ORDER BY PROTYPE DESC,INAME ASC\n";
        if (null != businessSystemBeanForQuery.getProperty() && !"".equals(businessSystemBeanForQuery.getProperty()))
        {
            String orderName = SYSORDERCLOUM_MAP.get(businessSystemBeanForQuery.getProperty());
            if (StringUtils.isEmpty(orderName))
            {
                orderString = " ORDER BY PROTYPE DESC \n";
            } else if ("PRIORITY".equals(orderName) || "PRJTYPE".equals(orderName) || "IID".equals(orderName))
            {
                if (orderName.equals("PRJTYPE"))
                {
                    orderName = "PROTYPE";
                }
                orderString = " ORDER BY  " + orderName + "\n" + businessSystemBeanForQuery.getDirection();
            } else
            {
                if (orderName.equals("SYSNAME"))
                {
                    orderName = "INAME";
                }
                orderString = " ORDER BY  ASCII(" + orderName + ")\n" + businessSystemBeanForQuery.getDirection();
            }

        }
        String sqlPageString = " SELECT * FROM ( SELECT ROW_NUMBER() OVER( " + orderString + " ) AS ROWNUM,T.*  FROM ("
                + "   SELECT DISTINCT(PJ.INAME),PJ.IUPPERID,IID ,PROTYPE ,IPKGCONTENTID,ISYSTEMCODE,ISYSTEMNUMBER,IFREEZED,PJ.IMAJVER,PJ.IMINVER,PJ.ICOMMENT,PJ.IUPLOADNUM,PJ.IUPLOADTIME,PJ.IUPLOADUSERID,PJ.ICANSYNC ,PJ.DEVELOPER,PJ.DEPARTMENT,PJ.ISYSBINGAGENT,PJ.ICICDSYS_CODE,PJ.ISYSTEM_OWNER,PJ.ISYSTEM_OWNER_TEL FROM IEAI_PROJECT PJ ,("
                + "                              SELECT IPROID "
                + "                              FROM IEAI_USERINHERIT UR,IEAI_ROLE R,IEAI_SYS_PERMISSION SP"
                + "                              WHERE UR.IROLEID=R.IID "
                + "                              AND R.IID=SP.IROLEID "
                + "                              AND UR.IUSERID=? "
                + "                              AND SP.IPERMISSION=1) QX";
        String sqlPageStringEnd = " )  T ) A  WHERE A.ROWNUM <=? AND A.ROWNUM > ?";
        sqlPageString = sqlPageString + sqlWhere + sqlPageStringEnd;
        if (JudgeDB.IEAI_DB_TYPE == 3)
        {
            sqlPageString = " SELECT DISTINCT(PJ.INAME),PJ.IUPPERID,IID ,PROTYPE ,IPKGCONTENTID,ISYSTEMCODE,ISYSTEMNUMBER,IFREEZED,PJ.IMAJVER,PJ.IMINVER,PJ.ICOMMENT,PJ.IUPLOADNUM,PJ.IUPLOADTIME,PJ.IUPLOADUSERID,PJ.ICANSYNC ,PJ.DEVELOPER,PJ.DEPARTMENT,PJ.ISYSBINGAGENT,PJ.ICICDSYS_CODE,PJ.ISYSTEM_OWNER,PJ.ISYSTEM_OWNER_TEL FROM IEAI_PROJECT PJ ,("
                    + "                             SELECT IPROID "
                    + "                             FROM IEAI_USERINHERIT UR,IEAI_ROLE R,IEAI_SYS_PERMISSION SP"
                    + "                             WHERE UR.IROLEID=R.IID "
                    + "                             AND R.IID=SP.IROLEID "
                    + "                             AND UR.IUSERID=? "
                    + "                             AND SP.IPERMISSION=1) QX";
            sqlPageStringEnd = " LIMIT ?,?";
            sqlPageString = sqlPageString + sqlWhere + orderString + sqlPageStringEnd;
        } else if (DBManager.Orcl_Faimily())
        {
            sqlPageString = " SELECT * FROM ( SELECT ROW_NUMBER() OVER( " + orderString + " ) rownum_,T.*  FROM ("
                    + " SELECT DISTINCT(PJ.INAME),PJ.IUPPERID,IID ,PROTYPE ,IPKGCONTENTID,ISYSTEMCODE,ISYSTEMNUMBER,IFREEZED,PJ.IMAJVER,PJ.IMINVER,PJ.ICOMMENT,PJ.IUPLOADNUM,PJ.IUPLOADTIME,PJ.IUPLOADUSERID,PJ.ICANSYNC ,PJ.DEVELOPER,PJ.DEPARTMENT,PJ.ISYSBINGAGENT,PJ.ICICDSYS_CODE,PJ.ISYSTEM_OWNER,PJ.ISYSTEM_OWNER_TEL FROM IEAI_PROJECT PJ ,("
                    + "                             SELECT IPROID "
                    + "                             FROM IEAI_USERINHERIT UR,IEAI_ROLE R,IEAI_SYS_PERMISSION SP"
                    + "                             WHERE UR.IROLEID=R.IID "
                    + "                             AND R.IID=SP.IROLEID "
                    + "                             AND UR.IUSERID=? "
                    + "                             AND SP.IPERMISSION=1) QX";
            sqlPageStringEnd = " )  T ) A  WHERE A.rownum_ <=? AND A.rownum_ > ?";
            sqlPageString = sqlPageString + sqlWhere + sqlPageStringEnd;
        }
        return sqlPageString;
    }

    /**   
     * @Title: sqlCountBusinessSystemList   
     * @Description: 拆分复杂度后细粒度方法，改方法主要为组织计算记录数的SQL使用。   
     * @param sqlWhere
     * @return      
     * @author: Sayai 
     * @date:   2019年1月21日 下午3:38:44   
     */
    private String sqlCountBusinessSystemList ( String sqlWhere )
    {
        return "SELECT COUNT(INAME) AS NUM FROM (SELECT  distinct(PJ.INAME),IID,PROTYPE AS NUM  FROM IEAI_PROJECT PJ ,("
                + "                            SELECT IPROID "
                + "                            FROM IEAI_USERINHERIT UR,IEAI_ROLE R,IEAI_SYS_PERMISSION SP"
                + "                            WHERE UR.IROLEID=R.IID "
                + "                            AND R.IID=SP.IROLEID " + "                            AND UR.IUSERID=? "
                + "                            AND SP.IPERMISSION=1) QX" + sqlWhere + " )co ";
    }

    /**   
     * @Title: childrenGetBusinessSystemListRebuild   
     * @Description:拆分复杂度后细粒度方法，主要负责进行查询，和对查询的对象处理。   
     * @param businessSystemBeanForQuery
     * @param conn
     * @param sqlPageString
     * @param sqlCount
     * @return
     * @throws RepositoryException      
     * @author: Sayai 
     * @date:   2019年1月21日 下午3:39:52   
     */
    private Map childrenGetBusinessSystemListRebuild ( BusinessSystemBeanForQuery businessSystemBeanForQuery,
            Connection conn, String sqlPageString, String sqlCount ,List<String> ls) throws RepositoryException
    {
        Map map = new HashMap();
        List res = new ArrayList();
        int count = 0;
        PreparedStatement actStat = null;
        PreparedStatement actStat2 = null;
        ResultSet actRS = null;
        ResultSet actRS2 = null;
        boolean hsSendMessage = Environment.getInstance().getHSBankSwitch();
        try
        {
            actStat = conn.prepareStatement(sqlPageString);
            actStat2 = conn.prepareStatement(sqlCount);
            if (ServerEnv.getInstance().getJobSchedulingQuerySystemSwitch())
            {
                // 锦州版本查询所有数据
                businessSystemBeanForQuery.setStart(0);
                businessSystemBeanForQuery.setLimit(100000);
            }
            int c=0;
            actStat.setLong(++c, Long.valueOf(businessSystemBeanForQuery.getUserId()));
            actStat2.setLong(c, Long.valueOf(businessSystemBeanForQuery.getUserId()));
            for (int i = 0; i < ls.size(); i++) {
                actStat.setString(++c,ls.get(i));
                actStat2.setString(c,ls.get(i));
            }
            if (businessSystemBeanForQuery.getPrjType() != 0){
                actStat.setInt(++c, businessSystemBeanForQuery.getPrjType());
                actStat2.setInt(c, businessSystemBeanForQuery.getPrjType());
            }
            if (JudgeDB.IEAI_DB_TYPE == 3)
            {
                    actStat.setInt(++c, businessSystemBeanForQuery.getStart());
                    actStat.setInt(++c, businessSystemBeanForQuery.getLimit());
            } else
            {
                actStat.setInt(++c, businessSystemBeanForQuery.getStart() + businessSystemBeanForQuery.getLimit());
                actStat.setInt(++c, businessSystemBeanForQuery.getStart());
            }
            actRS = actStat.executeQuery();
            while (actRS.next())
            {
                BusinessSystemBean bean = new BusinessSystemBean();
                bean.setSystemId(actRS.getLong("IID"));
                bean.setSysName(actRS.getString("INAME"));
                bean.setPrjType(actRS.getInt("PROTYPE"));
                bean.setPkgid(actRS.getInt("IPKGCONTENTID"));
                bean.setSystemCode(actRS.getString("ISYSTEMCODE"));
                bean.setSystemNumber(actRS.getString("ISYSTEMNUMBER"));
                bean.setUpid(actRS.getLong("IUPPERID"));// added
                bean.setDeveloper(actRS.getString("DEVELOPER"));
                bean.setDepartment(actRS.getString("DEPARTMENT"));
                /** 一体化运维4.7.9 业务系统维护功能相关代码 by yue_sun on 2018-03-13 start **/
                bean.setPrjVersion(String.valueOf(actRS.getLong("IMAJVER")) + "." + actRS.getLong("IMINVER"));
                bean.setFreeze(actRS.getInt("IFREEZED")); // 判断是否为冻结状态,0为正常状态，1为冻结状态
                // 验证工程是否为作业调度类型，如果不是，直接略过，判断为非excel上传工程
                if (actRS.getInt("PROTYPE") == 1)
                {
                    bean.setExcelPrj(isExcelUploadProject(actRS.getString("INAME"), Constants.IEAI_IEAI)); // 判断是否为excel上传的工程
                } else
                {
                    bean.setExcelPrj(0);
                }

                bean.setRunPrj(getRunningFlowIdsOfProjectForIEAI(actRS.getString("INAME")) ? 1 : 0); // 判断工程下是否存在正在运行的工作流，如果有则不可以删除记录
                bean.setIcooment(actRS.getString("ICOMMENT"));
                bean.setIuploadNumber(actRS.getString("IUPLOADNUM"));
                bean.setIuploadTime(actRS.getString("IUPLOADTIME"));
                bean.setIuploadUser("");
                if (ServerEnv.getInstance().getJobSchedulingQuerySystemSwitch())
                {
                    // 锦州版本查询上传者
                    if (actRS.getLong("IUPLOADUSERID") > 0)
                    {
                        String sql = "SELECT IFULLNAME AS NAME FROM IEAI_USER WHERE IID=?";
                        List<Object> paramList = new ArrayList<Object>();
                        paramList.add(actRS.getLong("IUPLOADUSERID"));
                        List<ResultBean> lr = SQLUtil.executeSqlReturnListParam(conn, sql, paramList, new ResultBean());
                        for (ResultBean rb : lr)
                        {
                            bean.setIuploadUser(rb.getName());
                        }
                    }
                }
                if (actRS.getInt("PROTYPE") == 16 || actRS.getInt("PROTYPE") == 40 || actRS.getInt("PROTYPE") == 3)
                {
                    Map<String,String> ipmpDataMap = getIpmpSysName(bean.getSystemId(), conn);
                    bean.setIpmpSysName(ipmpDataMap.get("ipmpSysName"));
                    bean.setSysCode(ipmpDataMap.get("ipmpSysCode"));
                }
                if(Environment.getInstance().getBooleanConfig(Environment.FJNX_SYNC_SYSTEM_SWITCH, false)) {
                    SyncSystemManager manage = new SyncSystemManager();
                    SyncSystem syncSys = manage.getProjectSync(bean.getSystemId());
                    bean.setSystemNumberCicd(syncSys.getSystemNumber());
                    bean.setSystemAbbreviation(syncSys.getSystemAbbreviation());
                    bean.setAsyToUse(syncSys.getAsyToUse());
                    bean.setSystemStatus(syncSys.getSystemStatus());
                    bean.setIfContinuousIntegration(syncSys.getIfContinuousIntegration());
                    bean.setIfAutoDeploy(syncSys.getIfAutoDeploy());
                    bean.setAsytouseManager(syncSys.getAsytouseManager());
                    bean.setAsytodetOffice(syncSys.getAsytodetOffice());
                }
                if(hsSendMessage){
                    bean.setAsyToUse(actRS.getString("ISYSTEM_OWNER"));
                    bean.setSystemOwnerTel(actRS.getString("ISYSTEM_OWNER_TEL"));
                }
                bean.setSystemNumberCicd(actRS.getString("ICICDSYS_CODE"));
                bean.setIcansync(actRS.getInt("ICANSYNC"));
                bean.setIsysbingagent(actRS.getString("ISYSBINGAGENT"));
                // 执行域名称
                bean.setDomainName(this.getDomainName(bean.getSystemId()));
                res.add(bean);
            }
            actRS2 = actStat2.executeQuery();
            while (actRS2.next())
            {
                count = actRS2.getInt("NUM");
            }
            map.put("total", count);
            map.put("dataList", res);
        } catch (SQLException e)
        {
            _log.error(e.getMessage(), e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            String methodName = Thread.currentThread().getStackTrace()[1].getMethodName();
            DBResource.closeResultSet(actRS, methodName, _log);
            DBResource.closeResultSet(actRS2, methodName, _log);
            DBResource.closePreparedStatement(actStat, methodName, _log);
            DBResource.closePreparedStatement(actStat2, methodName, _log);
        }
        return map;
    }

    /**
     * 通过系统ID查询执行域名称
     * @param systemId 系统ID
     * @return 执行域名称
     */
    public String getDomainName (long systemId)
    {
        String name = "";
        PreparedStatement ps = null;
        ResultSet rs = null;
        Connection conn = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_SUS);
            String sql = "SELECT m.INAME FROM IEAI_DOMAIN_MANAGEMENT m INNER JOIN ieai_project p ON m.IID = p.IDOMAINID WHERE p.IID = ?";
            ps = conn.prepareStatement(sql);
            ps.setLong(1, systemId);
            rs = ps.executeQuery();
            while (rs.next())
            {
                name = rs.getString("INAME");
            }
        } catch (Exception e)
        {
            _log.error("BusinessSystemManager.getDomainName is error", e);
        } finally
        {
            DBResource.closeConn(conn, rs, ps, "getDomainName", _log);
        }
        return name;
    }
    
    /**   
     * @Title: getBusinessSystemListRebuild   
     * @Description: 原方法复杂度46，重构改方法。 
     *   
     * @param businessSystemBeanForQuery
     * @param type
     * @return
     * @throws RepositoryException      
     * @author: Sayai 
     * @date:   2019年1月21日 下午3:25:24   
     */
    public Map getBusinessSystemListRebuild ( BusinessSystemBeanForQuery businessSystemBeanForQuery, int type )
            throws RepositoryException
    {
        List<String> ls=new ArrayList<>();
        String methodName = Thread.currentThread().getStackTrace()[1].getMethodName();
        Map map = new HashMap();
        String sqlWhere = sqlWhereBusinessSystemList(businessSystemBeanForQuery,ls);
        String sqlPageString = sqlQueryBusinessSystemList(businessSystemBeanForQuery, sqlWhere);
        String sqlCount = this.sqlCountBusinessSystemList(sqlWhere);

        Connection conn = null;
        for (int i = 0; i < 10; i++)
        {
            try
            {
                conn = DBResource.getConnection(methodName, _log, type);
                map = childrenGetBusinessSystemListRebuild(businessSystemBeanForQuery, conn, sqlPageString, sqlCount,ls);
                break;
            } catch (RepositoryException ex)
            {
                _log.error(ex.getMessage(), ex);
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            } finally
            {
                DBResource.closeConnection(conn, methodName, _log);
            }
        }

        return map;
    }


    /**
     * @Description: 光大猫扑获取获取用户权限下的业务系统
     * @Param: [businessSystemBeanForQuery, type]
     * @return: java.util.Map
     * @Author: zuochao_wang
     * @Date: 2022/7/27 10:29
     */
    public Map getBusinessSystemListGDMP ( BusinessSystemBeanForQuery businessSystemBeanForQuery, int type )
            throws RepositoryException
    {
        String methodName = Thread.currentThread().getStackTrace()[1].getMethodName();
        Map map = new HashMap();
        String sqlWhere = sqlWhereBusinessSystemListGD(businessSystemBeanForQuery);
        String sqlPageString = sqlQueryBusinessSystemListGD(businessSystemBeanForQuery, sqlWhere);
        String sqlCount = this.sqlCountBusinessSystemListGD(sqlWhere);

        Connection conn = null;
        for (int i = 0; i < 10; i++)
        {
            try
            {
                conn = DBResource.getConnection(methodName, _log, type);
                map = getBusinessListGDMP(businessSystemBeanForQuery, conn, sqlPageString, sqlCount);
                break;
            } catch (RepositoryException ex)
            {
                _log.error(ex.getMessage(), ex);
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            } finally
            {
                DBResource.closeConnection(conn, methodName, _log);
            }
        }

        return map;
    }

    /**
     * @Description:  光大猫扑获取获取用户权限下的业务系统
     * @Param: [businessSystemBeanForQuery, conn, sqlPageString, sqlCount]
     * @return: java.util.Map
     * @Author: zuochao_wang
     * @Date: 2022/7/27 10:32
     */
    private Map getBusinessListGDMP ( BusinessSystemBeanForQuery businessSystemBeanForQuery,Connection conn, String sqlPageString, String sqlCount ) throws RepositoryException
    {
        List<Map<String,Object>> res = new ArrayList<Map<String,Object>>();
        Map<String,Object> map = new HashMap<String,Object>();
        int count = 0;
        PreparedStatement actStat = null;
        PreparedStatement actStat2 = null;
        ResultSet actRS = null;
        ResultSet actRS2 = null;
        try
        {
            actStat = conn.prepareStatement(sqlPageString);
            actStat.setLong(1, Long.valueOf(businessSystemBeanForQuery.getUserId()));
            if (businessSystemBeanForQuery.getPrjType() != 0)
            {
                actStat.setInt(2, businessSystemBeanForQuery.getPrjType());
                actStat.setInt(3, businessSystemBeanForQuery.getStart() + businessSystemBeanForQuery.getLimit());
                actStat.setInt(4, businessSystemBeanForQuery.getStart());
            } else
            {
                actStat.setInt(2, businessSystemBeanForQuery.getStart() + businessSystemBeanForQuery.getLimit());
                actStat.setInt(3, businessSystemBeanForQuery.getStart());
            }
            actRS = actStat.executeQuery();
            while (actRS.next())
            {
                Map<String,Object> map1 = new HashMap<String,Object>();
                map1.put("sysName",actRS.getString("INAME")==null?"":actRS.getString("INAME"));
                map1.put("sysCode",actRS.getString("ISYSTEMCODE")==null?"":actRS.getString("ISYSTEMCODE"));
                res.add(map1);
            }
            actStat2 = conn.prepareStatement(sqlCount);
            if (businessSystemBeanForQuery.getPrjType() != 0)
            {
                actStat2.setLong(1, Long.valueOf(businessSystemBeanForQuery.getUserId()));
                actStat2.setInt(2, businessSystemBeanForQuery.getPrjType());
            } else
            {
                actStat2.setLong(1, Long.valueOf(businessSystemBeanForQuery.getUserId()));
            }
            actRS2 = actStat2.executeQuery();
            while (actRS2.next())
            {
                count = actRS2.getInt("NUM");
            }
            map.put("total", count);
            map.put("dataList", res);
        } catch (SQLException e)
        {
            _log.error(e.getMessage(), e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            String methodName = Thread.currentThread().getStackTrace()[1].getMethodName();
            DBResource.closeResultSet(actRS, methodName, _log);
            DBResource.closeResultSet(actRS2, methodName, _log);
            DBResource.closePreparedStatement(actStat, methodName, _log);
            DBResource.closePreparedStatement(actStat2, methodName, _log);
        }
        return map;
    }

    /**
     * @Description: 光大特殊获取sql ISYSCODE字段
     * @Param: [businessSystemBeanForQuery, sqlWhere]
     * @return: java.lang.String
     * @Author: zuochao_wang
     * @Date: 2022/7/27 10:40
     */
    private String sqlQueryBusinessSystemListGD ( BusinessSystemBeanForQuery businessSystemBeanForQuery, String sqlWhere )
    {
        String orderString = " ORDER BY PROTYPE DESC,INAME ASC\n";
        if (null != businessSystemBeanForQuery.getProperty() && !"".equals(businessSystemBeanForQuery.getProperty()))
        {
            String orderName = SYSORDERCLOUM_MAP.get(businessSystemBeanForQuery.getProperty());
            if (StringUtils.isEmpty(orderName))
            {
                orderString = " ORDER BY PROTYPE DESC \n";
            } else if ("PRIORITY".equals(orderName) || "PRJTYPE".equals(orderName) || "IID".equals(orderName))
            {
                if (orderName.equals("PRJTYPE"))
                {
                    orderName = "PROTYPE";
                }
                orderString = " ORDER BY  " + orderName + "\n" + businessSystemBeanForQuery.getDirection();
            } else
            {
                if (orderName.equals("SYSNAME"))
                {
                    orderName = "INAME";
                }
                orderString = " ORDER BY  ASCII(" + orderName + ")\n" + businessSystemBeanForQuery.getDirection();
            }

        }
        String sqlPageString = " SELECT * FROM ( SELECT ROW_NUMBER() OVER( " + orderString + " ) AS ROWNUM,T.*  FROM ("
                + "   SELECT DISTINCT(PJ.INAME),PJ.IUPPERID,IID ,PROTYPE ,IPKGCONTENTID,ISYSTEMCODE,ISYSTEMNUMBER,IFREEZED,PJ.IMAJVER,PJ.IMINVER,PJ.ICOMMENT,PJ.IUPLOADNUM,PJ.IUPLOADTIME,PJ.IUPLOADUSERID,PJ.ISYSCODE FROM IEAI_PROJECT PJ ,("
                + "                              SELECT IPROID "
                + "                              FROM IEAI_USERINHERIT UR,IEAI_ROLE R,IEAI_SYS_PERMISSION SP"
                + "                              WHERE UR.IROLEID=R.IID "
                + "                              AND R.IID=SP.IROLEID "
                + "                              AND UR.IUSERID=? "
                + "                              AND SP.IPERMISSION=1) QX";
        String sqlPageStringEnd = " )  T ) A  WHERE A.ROWNUM <=? AND A.ROWNUM > ?";
        sqlPageString = sqlPageString + sqlWhere + sqlPageStringEnd;
        if (JudgeDB.IEAI_DB_TYPE == 3)
        {
            sqlPageString = " SELECT DISTINCT(PJ.INAME),PJ.IUPPERID,IID ,PROTYPE ,IPKGCONTENTID,ISYSTEMCODE,ISYSTEMNUMBER,IFREEZED,PJ.IMAJVER,PJ.IMINVER,PJ.ICOMMENT,PJ.IUPLOADNUM,PJ.IUPLOADTIME,PJ.IUPLOADUSERID,PJ.ISYSCODE FROM IEAI_PROJECT PJ ,("
                    + "                             SELECT IPROID "
                    + "                             FROM IEAI_USERINHERIT UR,IEAI_ROLE R,IEAI_SYS_PERMISSION SP"
                    + "                             WHERE UR.IROLEID=R.IID "
                    + "                             AND R.IID=SP.IROLEID "
                    + "                             AND UR.IUSERID=? "
                    + "                             AND SP.IPERMISSION=1) QX";
            sqlPageStringEnd = " LIMIT ?,?";
            sqlPageString = sqlPageString + sqlWhere + orderString + sqlPageStringEnd;
        } else if (DBManager.Orcl_Faimily())
        {
            sqlPageString = " SELECT * FROM ( SELECT ROW_NUMBER() OVER( " + orderString + " ) rownum_,T.*  FROM ("
                    + " SELECT DISTINCT(PJ.INAME),PJ.IUPPERID,IID ,PROTYPE ,IPKGCONTENTID,ISYSTEMCODE,ISYSTEMNUMBER,IFREEZED,PJ.IMAJVER,PJ.IMINVER,PJ.ICOMMENT,PJ.IUPLOADNUM,PJ.IUPLOADTIME,PJ.IUPLOADUSERID,PJ.ISYSCODE FROM IEAI_PROJECT PJ ,("
                    + "                             SELECT IPROID "
                    + "                             FROM IEAI_USERINHERIT UR,IEAI_ROLE R,IEAI_SYS_PERMISSION SP"
                    + "                             WHERE UR.IROLEID=R.IID "
                    + "                             AND R.IID=SP.IROLEID "
                    + "                             AND UR.IUSERID=? "
                    + "                             AND SP.IPERMISSION=1) QX";
            sqlPageStringEnd = " )  T ) A  WHERE A.rownum_ <=? AND A.rownum_ > ?";
            sqlPageString = sqlPageString + sqlWhere + sqlPageStringEnd;
        }
        return sqlPageString;
    }

    /**
     * @Description: 光大查询sqlwhere条件方法重构
     * @Param: [businessSystemBeanForQuery]
     * @return: java.lang.String
     * @Author: zuochao_wang
     * @Date: 2022/7/27 13:15
     */
    private String sqlWhereBusinessSystemListGD ( BusinessSystemBeanForQuery businessSystemBeanForQuery )
    {
        String sqlWhere = "  WHERE (PJ.IUPPERID=QX.IPROID OR  PJ.PROTYPE=( DECODE (-QX.IPROID, 2, 7,-QX.IPROID))  OR PJ.PROTYPE= DECODE (-QX.IPROID, 2,8 ,-QX.IPROID))"
                + "  AND PJ.IID>0  AND PJ.IID=PJ.ILATESTID  ";
        // 业务系统名
        if (businessSystemBeanForQuery.getSysName() != null && !"".equals(businessSystemBeanForQuery.getSysName()))
        {
            String prjName = businessSystemBeanForQuery.getSysName();
            if (prjName.contains("_") || prjName.contains("%"))
            {
                prjName = prjName.replaceAll("_", "/_");
                prjName = prjName.replaceAll("%", "/%");
            }
            sqlWhere = sqlWhere + " AND UPPER(PJ.INAME) LIKE '%" + prjName.toUpperCase() + "%' escape '/'";
        }
        if (businessSystemBeanForQuery.getPrjType() != 0)
        {
            sqlWhere = sqlWhere + "  AND  PJ.PROTYPE=?  ";
        } else
        {
            sqlWhere = sqlWhere + "  AND ( PJ.PROTYPE=1 OR PJ.PROTYPE=7 OR PJ.PROTYPE=8 OR IPKGCONTENTID=0 ) ";
        }
        return sqlWhere;
    }

    /**
     * @Description: 光大查询业务系统的数量
     * @Param: [sqlWhere]
     * @return: java.lang.String
     * @Author: zuochao_wang
     * @Date: 2022/7/27 13:16
     */
    private String sqlCountBusinessSystemListGD ( String sqlWhere )
    {
        return "SELECT COUNT(INAME) AS NUM FROM (SELECT  distinct(PJ.INAME),IID,PROTYPE AS NUM  FROM IEAI_PROJECT PJ ,("
                + "                            SELECT IPROID "
                + "                            FROM IEAI_USERINHERIT UR,IEAI_ROLE R,IEAI_SYS_PERMISSION SP"
                + "                            WHERE UR.IROLEID=R.IID "
                + "                            AND R.IID=SP.IROLEID " + "                            AND UR.IUSERID=? "
                + "                            AND SP.IPERMISSION=1) QX" + sqlWhere + " )co ";
    }

    /**
     * <li>Description:组织删除 业务系统SQL</li> 
     * <AUTHOR>
     * 2016年10月24日 
     * @param stepInfos
     * @param baseConn
     * @return
     * @throws RepositoryException
     * return Map
     */
    public Map organizeBSDELSql ( Long[] deleteIds, Connection baseConn ) throws RepositoryException
    {
        Map res = new HashMap();
        boolean isSuccess = true;
        List<String> exeSqls = new ArrayList<String>();
        List<String> rollbackSqls = new ArrayList<String>();
        // for (BusinessSystemBean step : stepInfos)
        // {
        // long iid = IdGenerator.createIdForExecAct(RepProject.class, baseConn);
        // step.setSystemId(iid);
        // }
        try
        {
            // IEAI_SYS_RELATION
            String insert_sql_relation = " INSERT INTO IEAI_SYS_RELATION (SYSTEMID,COMPUTERID,SYSNAME,IP,COMPUTERSTATE,SYSSTATE,SYSPARAM,CENTERNAME,JOBSTATE,PRJTYPE,SDID) "
                    + " VALUES ({systemId}, {computerId}, '{sysName}', '{ip}', {computerState}, {sysState}, '{sysParam}', '{centerName}', {jobState},{prjType},{sdid})";
            List<SysRelationBean> oldreLationList = this.getRelationList_ForDelete(deleteIds, baseConn);
            for (SysRelationBean bean : oldreLationList)
            {
                BeanFormatter<SysRelationBean> info = new BeanFormatter<SysRelationBean>(insert_sql_relation);
                String insert_info = info.format(bean);
                rollbackSqls.add(insert_info);
            }
            String del_sql_relation = "DELETE FROM IEAI_SYS_RELATION WHERE SYSTEMID IN (SELECT PRJ.IID FROM IEAI_PROJECT PRJ WHERE PRJ.IUPPERID IN ("
                    + StringUtils.join(deleteIds, ",") + "))";
            exeSqls.add(del_sql_relation);

            // IEAI_PROJECT
            List<BusinessSystemBean> oldPrjList = getBusinessSystemList_Fordelete(deleteIds, baseConn);
            String insertl_sql = "INSERT INTO IEAI_PROJECT(IID,INAME,IPKGCONTENTID,IGROUPID,PROTYPE,IUPLOADUSERID,IUPLOADUSER,ILATESTID,IUPPERID)"
                    + " VALUES({systemId},'{sysName}',0,{prjType},{prjType},{userId},'{userName}',{upid},{lastid})";

            for (BusinessSystemBean bean : oldPrjList)
            {
                BeanFormatter<BusinessSystemBean> info = new BeanFormatter<BusinessSystemBean>(insertl_sql);
                String insert_info = info.format(bean);
                rollbackSqls.add(insert_info);
            }
            // String del_sql = "DELETE FROM IEAI_PROJECT T WHERE T.IID IN (" +
            // StringUtils.join(deleteIds, ",") + ")";
            String del_sql = "DELETE  FROM IEAI_PROJECT WHERE IUPPERID IN (" + StringUtils.join(deleteIds, ",") + ")";
            exeSqls.add(del_sql);

            // //应急等模块相关表
            // // 3.保存IEAI_SUS_BASIC_INFO表数据
            // String insert_sql3 = "INSERT INTO IEAI_SUS_BASIC_INFO(IID, IINSTANCEID,
            // IINSTANCENAME, IPARAMETER, IPARAVALUE,IDES) "
            // +"
            // VALUES({iId},{iInstanceId},'{iInstanceName}','{iParmeter}','{iParavalue}','{iDes}')";
            // List<BasicInfoBean> oldBasicInfoList = getBasicInfoList_Fordelete(deleteIds,
            // baseConn);
            // for (BasicInfoBean bean : oldBasicInfoList)
            // {
            // BeanFormatter<BasicInfoBean> info = new BeanFormatter<BasicInfoBean>(insert_sql3);
            // String insert_info = info.format(bean);
            // rollbackSqls.add(insert_info);
            // }
            // // 4.保存IEAI_INSTANCEINFO表数据
            // List<InstanceInfoBean> oldInsInfoList = getInstanceInfoList_Fordelete(deleteIds,
            // baseConn);
            // String insert_sql4 = "INSERT INTO IEAI_INSTANCEINFO(IID, IINSTANCEID, IINSTANCENAME,
            // ISERNER, ICONNER, "
            // + "ICONNERNAME, IPRENER, IACTNAME, IACTDES, "
            // + "IACTTYPE, IREMINFO, IIPNAME, IMODELTYPE, IIP, "
            // + "IPORT, ISYSTYPE, IEXECUSER, ISHELLSCRIPT, "
            // + "IISLOADENV, ISHELLPATH, ITIMEOUT, IPARAMETER, "
            // + "IEXPECEINFO, IEXCEPTINFO, IREDOABLE, IISDISABLE, "
            // + "ISWITCHSYSTYPE,IPARACHECK, IPARASWITCH, IPARASWITCHFORCE, "
            // + "IPKGNAME, ICENTER, IRETNVALEXCEPION "
            // + ",ITYPE,IMODELNAME,IMODELVERSION,ISCRIPTID,IMXGRAPHID "//added
            // + ") "
            // + "VALUES({iId},{iInstanceId},'{iInstanceName}',{iSerner},{iConner},"
            // + "'{iConnerName}','{iPerner}','{iActName}','{iActDes}',"
            // + "{iActType},'{iRemInfo}','{iIpName}','{iModelType}','{iIp}',"
            // + "{iPort},{iSysType},'{iExecUser}','{iShellScript}',"
            // + "{iIsLoadEnv},'{iShellPath}',{iTimeout},'{iParameter}',"
            // + "'{iExpectInfo}','{iExceptInfo}',{iRedoable},{iIsDisable},"
            // + "{iSwitchType},'{iParaCheck}','{iParaSwitch}','{iParaSwitchForce}',"
            // + "'{iPkgName}','{iCenter}','{iRetnValException}'"
            // + ",{iType},'{iModelName}',{iModelVersion},{iScriptId},{iMXGraphId}"//added
            // + ")";
            // for (InstanceInfoBean bean : oldInsInfoList)
            // {
            // BeanFormatter<InstanceInfoBean> info = new
            // BeanFormatter<InstanceInfoBean>(insert_sql4);
            // String insert_info = info.format(bean);
            // rollbackSqls.add(insert_info);
            // }
            // // 5.保存IEAI_INSTANCE_VERSION表数据
            // List<InstanceVersionBean> oldInsVerList = getInstanceVersionList_Fordelete(deleteIds,
            // baseConn);
            // String insert_sql5 = "INSERT INTO IEAI_INSTANCE_VERSION(IID, IINSTANCENAME, IVERSION,
            // IEDITVERSION, IVERSIONALIAS,"
            // +" ISYSTYPE, IDES, ISYSID, ITIME, ISWITCHID, IPROJECTID, IIEAISYSID,
            // IENVNAME,IUPPERID "
            // +",IISMXGRAPH,ISVALIDATE,IBUSNES_SYS_IID "//added
            // +") "
            // +" VALUES({iId},'{iInstanceName}',{iVersion},{iEditVersion},'{iVersionAlias}',"
            // +"{iSysType},'{iDes}',{iSysId},{iTime},{iSwitchId},{iProjectId},{iIeaiSysId},'{iEnvName}',{iUpperId}"
            // +",{iIsMXGraph},{isValidate},{iBusnesSysIid}"
            // +")";
            // for (InstanceVersionBean bean : oldInsVerList)
            // {
            // BeanFormatter<InstanceVersionBean> info = new
            // BeanFormatter<InstanceVersionBean>(insert_sql5);
            // String insert_info = info.format(bean);
            // rollbackSqls.add(insert_info);
            // }
            // //删除语句
            // //3,IEAI_SUS_BASIC_INFO
            // String del_sql3 = "DELETE FROM IEAI_SUS_BASIC_INFO WHERE IINSTANCEID IN (SELECT IID
            // FROM IEAI_INSTANCE_VERSION VER WHERE VER.IUPPERID IN ("+ StringUtils.join(deleteIds,
            // ",") +") )";
            // //4,IEAI_INSTANCEINFO
            // String del_sql4 = "DELETE FROM IEAI_INSTANCEINFO WHERE IINSTANCEID IN (SELECT IID
            // FROM IEAI_INSTANCE_VERSION VER WHERE VER.IUPPERID IN ("+ StringUtils.join(deleteIds,
            // ",") +") )";
            // //5,IEAI_INSTANCEINFO
            // String del_sql5 = "DELETE FROM IEAI_INSTANCE_VERSION WHERE IUPPERID IN
            // ("+StringUtils.join(deleteIds, ",") +")";
            // exeSqls.add(del_sql3);
            // exeSqls.add(del_sql4);
            // exeSqls.add(del_sql5);
        } catch (Exception e)
        {
            e.printStackTrace();
            isSuccess = false;
        }
        res.put("success", isSuccess);
        res.put("exeSqls", exeSqls);
        res.put("rollbackSqls", rollbackSqls);
        return res;
    }

    private List<SysRelationBean> getRelationList_ForDelete ( Long[] deleteIds, Connection con )
            throws RepositoryException
    {
        List<SysRelationBean> list = new ArrayList<SysRelationBean>();
        SysRelationBean bean = null;
        String sql = " SELECT * FROM IEAI_SYS_RELATION R WHERE R.SYSTEMID IN (SELECT PRJ.IID FROM IEAI_PROJECT PRJ WHERE PRJ.IUPPERID IN ("
                + StringUtils.join(deleteIds, ",") + ")) ";
        for (int i = 0; i < 10; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                try
                {
                    actStat = con.prepareStatement(sql);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        bean = new SysRelationBean();

                        bean.setSystemId(actRS.getLong("SYSTEMID"));
                        bean.setComputerId(actRS.getLong("COMPUTERID"));
                        bean.setSysName(actRS.getString("SYSNAME"));
                        bean.setSysName(actRS.getString("IP"));
                        bean.setComputerState(actRS.getInt("COMPUTERSTATE"));
                        bean.setSysState(actRS.getInt("SYSSTATE"));
                        bean.setSysParam(actRS.getString("SYSPARAM"));
                        bean.setCenterName(actRS.getString("CENTERNAME"));
                        bean.setJobState(actRS.getLong("JOBSTATE"));
                        bean.setPrjType(actRS.getInt("PRJTYPE"));
                        bean.setSdid(actRS.getInt("SDID"));

                        list.add(bean);
                    }

                } catch (SQLException e)
                {
                    _log.error("getRelationList_ForDelete is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closePSRS(actRS, actStat, "getRelationList_ForDelete", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return list;
    }

    public List<BusinessSystemBean> getBusinessSystemList_Fordelete ( /* Long[] prjids, */ Long[] iupperIds,
            Connection conn ) throws RepositoryException
    {
        List<BusinessSystemBean> resultlist = new ArrayList<BusinessSystemBean>();
        String sql = "SELECT  IID,INAME,IUPLOADUSER,IUPLOADUSERID,IGROUPID,PROTYPE,IUPPERID,ILATESTID FROM IEAI_PROJECT"
                // + " WHERE IID IN(" +StringUtils.join(prjids, ",") + ") ";
                + " WHERE IUPPERID IN(" + StringUtils.join(iupperIds, ",") + ")  ";

        BusinessSystemBean bean = null;
        for (int i = 0; i < 10; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                try
                {
                    actStat = conn.prepareStatement(sql);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        bean = new BusinessSystemBean();
                        bean.setSystemId(actRS.getLong("IID"));
                        bean.setSysName(actRS.getString("INAME"));
                        bean.setUserName(actRS.getString("IUPLOADUSER"));
                        bean.setUserId(actRS.getLong("IUPLOADUSERID"));
                        bean.setPrjType(actRS.getInt("PROTYPE"));
                        bean.setUpid(actRS.getLong("IUPPERID"));
                        bean.setLastid(actRS.getLong("ILATESTID"));
                        resultlist.add(bean);

                    }
                } catch (SQLException e)
                {
                    _log.error("getBusinessSystemList_Fordelete is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closePSRS(actRS, actStat, "getBusinessSystemList_Fordelete", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log.error("getBusinessSystemList_Fordelete method of BusinessSystemManager.class RepositoryException:"
                        + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return resultlist;
    }

    public Map<String, Object> organizeBSSqlSystemRelation ( List<Map<String, Object>> businessSystems, long systemId,
            String userName, int opersystype, Connection conn ) throws RepositoryException
    {
        Map<String, Object> res = new HashMap<String, Object>();
        boolean isSuccess = true;
        List<String> exeSqls = new ArrayList<String>();
        List<String> rollbackSqls = new ArrayList<String>();

        if (!CollectionUtils.isEmpty(businessSystems))
        {
            for (int i = 0; i < 10; i++)
            {
                try
                {
                    StringBuilder computerIds = new StringBuilder();
                    for (int z = 0; z < businessSystems.size(); z++)
                    {
                        Map<String, Object> bsMap = businessSystems.get(z);
                        Long computerId = (Long) Long.parseLong(bsMap.get("cpId").toString());
                        long sdid = IdGenerator.createId("IEAI_SYS_RELATION_SDID", conn);
                        String sql1 = "  INSERT INTO   IEAI_SYS_RELATION  (SYSTEMID,COMPUTERID,IP,COMPUTERSTATE,SYSSTATE,CENTERNAME,SYSNAME,PRJTYPE ,SDID)"
                                + "  (" + " SELECT" + "  T.IID AS SYSTEMID ," + " T2.CPID AS COMPUTERID,"
                                + " T2.IP AS IP," + " 1 AS COMPUTERSTATE," + " NULL AS SYSSTATE,"
                                + " T2.CENTERNAME AS CENTERNAME," + " T.INAME AS SYSNAME," + " T.PROTYPE AS PRJTYPE ,"
                                + sdid + "  FROM " + " IEAI_PROJECT  T," + " IEAI_COMPUTER_LIST T2 "
                                + "  WHERE 1=1 AND T.IID=" + systemId + "  AND T2.CPID=" + computerId + ") ";
                        if (opersystype == Constants.IEAI_TIMINGTASK || opersystype == Constants.IEAI_EMERGENCY_SWITCH)
                        {
                            sql1 = "  INSERT INTO   IEAI_SYS_RELATION  (SYSTEMID,COMPUTERID,IP,COMPUTERSTATE,SYSSTATE,CENTERNAME,SYSNAME,PRJTYPE)"
                                    + "  (" + " SELECT" + "  T.IID AS SYSTEMID ," + " T2.IAGENTINFO_ID AS COMPUTERID,"
                                    + " T2.IAGENT_IP AS IP," + " 1 AS COMPUTERSTATE," + " NULL AS SYSSTATE,"
                                    + " '' AS CENTERNAME," + " T.INAME AS SYSNAME," + " T.PROTYPE AS PRJTYPE"
                                    + "  FROM " + " IEAI_PROJECT  T," + " IEAI_AGENTINFO T2 " + "  WHERE 1=1 AND T.IID="
                                    + systemId + "  AND T2.IAGENTINFO_ID=" + computerId + ") ";
                        }
                        exeSqls.add(sql1);

                        String delSql = "DELETE FROM IEAI_SYS_RELATION WHERE SYSTEMID=" + systemId + " AND COMPUTERID="
                                + computerId;
                        rollbackSqls.add(delSql);
                        computerIds.append(computerId + ",");
                        // 绑定业务系统设备时, 同时在业务系统绑定的设备组上绑定该设备
                        /* setGroupMappingSql(conn, systemId, computerId, exeSqls, rollbackSqls); */
                    }
                    /**_log.info("saveBusinessSystemRelation_rebuild {systemId=" + systemId + ",computerId="
                            + computerIds.toString() + "} by user:" + userName);*/
                    break;
                } catch (Exception ex)
                {
                    isSuccess = false;
                    DBResource.throwRepositoryException(i, ServerError.ERR_DB_INSERT);
                }
            }
        }
        res.put("success", isSuccess);
        res.put("exeSqls", exeSqls);
        res.put("rollbackSqls", rollbackSqls);
        return res;
    }

    /**   
     * @Title: setGroupMappingSql   
     * @Description: 绑定业务系统设备时, 同时在业务系统绑定的设备组上绑定该设备   
     * @param conn
     * @param systemId
     * @param computerId
     * @param exeSqls
     * @param rollbackSqls
     * @throws RepositoryException      
     * @author: yue_sun
     * @date:   2020-03-25 17:06:39  
     */
    public void setGroupMappingSql ( Connection conn, long systemId, Long computerId, List<String> exeSqls,
            List<String> rollbackSqls ) throws RepositoryException
    {
        List<EquGroupBusinessMiddle> groupList = getEquipmentBindBusinessAll(conn, systemId, Constants.IEAI_IEAI_BASIC);
        for (EquGroupBusinessMiddle middleBean : groupList)
        {
            long iid = IdGenerator.createId("IEAI_COMPUTER_GROUP_MAPPING", conn);
            String insertSqlMapping = "INSERT INTO IEAI_COMPUTER_GROUP_MAPPING(IID,ICPID,IGID) values(" + iid + ","
                    + computerId + "," + middleBean.getEquGroupId() + ") ";
            exeSqls.add(insertSqlMapping);

            // 回滚SQL
            String deleteSqlMapping = "DELETE FROM IEAI_COMPUTER_GROUP_MAPPING WHERE IGID=" + middleBean.getEquGroupId()
                    + " AND ICPID IN(" + computerId + ") ";
            rollbackSqls.add(deleteSqlMapping);
        }
    }

    public List<EquGroupBusinessMiddle> getEquipmentBindBusinessAll ( Connection conn, long systemId, int dbType )
    {
        String sql = "SELECT IID,EQUGROUPID as EQUGROUPID,BUSINESSID as BUSINESSID,BUSINESSNAME as BUSINESSNAME,PROTYPE FROM  IEAI_EQU_GROUP_BUSINESS T WHERE T.BUSINESSID =? ";
        DBUtilsNew utils = new DBUtilsNew(dbType);
        Object[] objtdata = { systemId };
        return utils.list(conn, sql, objtdata, new EquGroupBusinessMiddle());
    }

    public Map<String, Object> organizeBSSqlSystemRelation_Del ( Long[] deleteIds, long systemId, String userName,
            Connection baeConn ) throws RepositoryException, SQLException
    {

        Map<String, Object> res = new HashMap<String, Object>();
        boolean isSuccess = true;
        List<String> exeSqls = new ArrayList<String>();
        List<String> rollbackSqls = new ArrayList<String>();

        if (null != deleteIds && deleteIds.length > 0)
        {
            for (int i = 0; i < 10; i++)
            {
                try
                {
                    StringBuffer ComputerIds = new StringBuffer();
                    List<Long> cids = new ArrayList<Long>();
                    List<Long> sids = new ArrayList<Long>();
                    for (int z = 0; z < deleteIds.length; z++)
                    {
                        Long computerId = deleteIds[z];
                        String delSql = "DELETE FROM IEAI_SYS_RELATION WHERE SYSTEMID=" + systemId + " AND COMPUTERID="
                                + computerId;
                        exeSqls.add(delSql);
                        cids.add(computerId);
                        sids.add(systemId);
                        // 组织回退SQL
                        ComputerIds.append(computerId + ",");

                        // 根据传入的id查找是否是定时任务类型
                        long servertype = TTManageManager.getInstance().getServerType(systemId);
                        if (servertype == Constants.IEAI_TIMINGTASK)
                        {

                            // 更新SQL
                            String updateSql1 = "update IEAI_TIMETASK_INFO set ip=replace(ip ,'" + computerId
                                    + ",','') " + "  where  GID in  (" + systemId + ") " + " AND ip like '" + computerId
                                    + ",%' ";

                            String updateSql2 = "update IEAI_TIMETASK_INFO set ip=replace(ip ,'," + computerId
                                    + "','') " + " where GID in (" + systemId + ") " + " and ip like '%," + computerId
                                    + "' ";

                            String updateSql3 = "update IEAI_TIMETASK_INFO set ip=replace(ip ,'" + computerId + "','') "
                                    + " where  GID in (" + systemId + ") " + " and ip like '%" + computerId + "%'";

                            String updateSql4 = "update IEAI_TIMETASK_INFO set ip=replace(ip ,'," + computerId
                                    + ",',',') " + " where  GID in (" + systemId + ") " + " and ip LIKE  '%,"
                                    + computerId + ",%' ";

                            exeSqls.add(updateSql1);
                            exeSqls.add(updateSql2);
                            exeSqls.add(updateSql4);
                            exeSqls.add(updateSql3);

                            boolean ttAuditShow = ConfigReader.getInstance()
                                    .getBooleanProperties(Environment.TIMETASK_MANAGE_PAGE_BUTTON, false);
                            if (ttAuditShow)
                            {
                                // 更新SQL
                                String ttupdateSql1 = "update ieai_timetask_audit set iip=replace(iip ,'" + computerId
                                        + ",','') " + "  where  IGID in  (" + systemId + ") " + " AND iip like '"
                                        + computerId + ",%' ";
                                String ttupdateSql2 = "update ieai_timetask_audit set iip=replace(iip ,'," + computerId
                                        + "','') " + " where IGID in (" + systemId + ") " + " and iip like '%,"
                                        + computerId + "' ";

                                String ttupdateSql3 = "update ieai_timetask_audit set iip=replace(iip ,'" + computerId
                                        + "','') " + " where  IGID in (" + systemId + ") " + " and iip like '%"
                                        + computerId + "%'";

                                String ttupdateSql4 = "update ieai_timetask_audit set iip=replace(iip ,'," + computerId
                                        + ",',',') " + " where  IGID in (" + systemId + ") " + " and iip LIKE  '%,"
                                        + computerId + ",%' ";
                                exeSqls.add(ttupdateSql1);
                                exeSqls.add(ttupdateSql2);
                                exeSqls.add(ttupdateSql4);
                                exeSqls.add(ttupdateSql3);
                            }

                            // 组织回滚SQL
                            // IEAI_COMPUTER_LIST
                            String updateSqlOld = "";
                            String baksql1 = " update IEAI_TIMETASK_INFO ";
                            String baksql2 = " where  GID in (" + systemId + ")" + " and (ip like '" + computerId
                                    + ",%'" + " or ip like '%," + computerId + "' " + " or ip like '%" + computerId
                                    + "%' " + " or ip LIKE  '%," + computerId + ",%' ) ";
                            if (JudgeDB.IEAI_DB_TYPE == 3) // mysql
                            {
                                updateSqlOld = baksql1 + " set ip=replace(ip ,ip,concat(ip , '," + computerId + "')) "
                                        + baksql2;
                            } else
                            {// oracle
                                updateSqlOld = baksql1 + " set ip=replace(ip ,ip,ip || '," + computerId + "') "
                                        + baksql2;
                            }

                            rollbackSqls.add(updateSqlOld);

                            if (ttAuditShow)
                            {
                                String ttupdateSqlOld = "";
                                String ttbaksql1 = " update ieai_timetask_audit ";
                                String ttbaksql2 = " where  IGID in (" + systemId + ")" + " and (iip like '"
                                        + computerId + ",%'" + " or iip like '%," + computerId + "' "
                                        + " or iip like '%" + computerId + "%' " + " or iip LIKE  '%," + computerId
                                        + ",%' ) ";
                                if (JudgeDB.IEAI_DB_TYPE == 3) // mysql
                                {
                                    ttupdateSqlOld = ttbaksql1 + " set iip=replace(iip ,iip,concat(iip , ',"
                                            + computerId + "')) " + ttbaksql2;
                                } else
                                {// oracle
                                    ttupdateSqlOld = ttbaksql1 + " set iip=replace(iip ,iip,iip || '," + computerId
                                            + "') " + ttbaksql2;
                                }

                                rollbackSqls.add(ttupdateSqlOld);
                            }
                        }
                    }
                    List<SysRelationBean> oldList = getRelationListForBack(StringUtils.join(cids, ","),
                        StringUtils.join(sids, ","), baeConn);
                    String sql_back = "  INSERT INTO   IEAI_SYS_RELATION  (SYSTEMID,COMPUTERID,IP,COMPUTERSTATE,SYSSTATE,CENTERNAME,SYSNAME,PRJTYPE ,SDID)"
                            + "VALUES({systemId},{computerId},'{ip}',{computerState},{sysState},'{centerName}','{sysName}',{prjType},{sdid} )";
                    for (SysRelationBean bean : oldList)
                    {
                        BeanFormatter<SysRelationBean> insertbf = new BeanFormatter<SysRelationBean>(sql_back);
                        String insert = insertbf.format(bean);
                        rollbackSqls.add(insert);
                    }
                    _log.info("organizeBSSqlSystemRelation_Del {systemId=" + systemId + ",computerId="
                            + ComputerIds.toString() + "} by user:" + userName);
                    break;
                } catch (Exception ex)
                {
                    isSuccess = false;
                    DBResource.throwRepositoryException(i, ServerError.ERR_DB_INSERT);
                }
            }
        }
        res.put("success", isSuccess);
        res.put("exeSqls", exeSqls);
        res.put("rollbackSqls", rollbackSqls);
        return res;
    }

    private List<SysRelationBean> getRelationListForBack ( String cpids, String systemids, Connection con )
            throws RepositoryException
    {
        List<SysRelationBean> list = new ArrayList<SysRelationBean>();
        SysRelationBean bean = null;
        String sql = " SELECT * FROM IEAI_SYS_RELATION where computerid in ( " + cpids + " )  and  SYSTEMID IN ("
                + systemids + ")";

        PreparedStatement actStat = null;
        ResultSet actRS = null;
        try
        {
            actStat = con.prepareStatement(sql);
            actRS = actStat.executeQuery();
            while (actRS.next())
            {
                bean = new SysRelationBean();

                bean.setSystemId(actRS.getLong("SYSTEMID"));
                bean.setComputerId(actRS.getLong("COMPUTERID"));
                bean.setSysName(actRS.getString("SYSNAME"));
                bean.setIp(actRS.getString("IP"));
                bean.setComputerState(actRS.getInt("COMPUTERSTATE"));
                bean.setSysState(actRS.getInt("SYSSTATE"));
                bean.setSysParam(actRS.getString("SYSPARAM"));
                bean.setCenterName(actRS.getString("CENTERNAME"));
                bean.setJobState(actRS.getLong("JOBSTATE"));
                bean.setPrjType(actRS.getInt("PRJTYPE"));
                bean.setSdid(actRS.getInt("SDID"));

                list.add(bean);
            }

        } catch (SQLException e)
        {
            _log.error("getRelationListForBack is error ! " + e.getMessage());
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(actRS, actStat, "getRelationListForBack", _log);
        }
        return list;
    }

    public Map<String, Object> chekcBusinessSystemUsedForTT ( Long[] iupperIds, int type ) throws RepositoryException
    {
        Map<String, Object> resp = new HashMap<String, Object>();
        if (null != iupperIds && iupperIds.length > 0)
        {
            String sql = "SELECT PRJ.INAME AS SYSNAME,COUNT(INFO.ID) AS COU FROM IEAI_TIMETASK_INFO INFO, IEAI_PROJECT PRJ WHERE INFO.GID = PRJ.IID "
                    + "AND PRJ.IUPPERID IN(" + StringUtils.join(iupperIds, ",") + ") GROUP BY PRJ.INAME ";
            for (int i = 0; i < 10; i++)
            {
                try
                {
                    Connection conn = null;
                    PreparedStatement actStat = null;
                    ResultSet actRS = null;
                    try
                    {
                        List<String> systemNameList = new ArrayList<String>();
                        conn = DBResource.getConnection("chekcBusinessSystemUsedForTT", _log, type);
                        actStat = conn.prepareStatement(sql);
                        actRS = actStat.executeQuery();
                        while (actRS.next())
                        {
                            String sysName = actRS.getString("SYSNAME");
                            long cnt = actRS.getLong("COU");
                            if (cnt > 0)
                            {
                                resp.put("success", false);
                                resp.put("message", "【" + sysName + "】业务系统已被绑定，不可以删除！");
                                return resp;
                            }
                        }
                    } catch (SQLException e)
                    {
                        _log.error("chekcBusinessSystemUsedForTT method of BusinessSystemManager.class SQLException:"
                                + e.getMessage());
                        throw new RepositoryException(ServerError.ERR_DB_QUERY);
                    } finally
                    {
                        DBResource.closeConn(conn, actRS, actStat, "chekcBusinessSystemUsedForTT", _log);
                    }
                    break;
                } catch (RepositoryException ex)
                {
                    _log.error(
                        "chekcBusinessSystemRuningForUT method of BusinessSystemManager.class RepositoryException:"
                                + ex.getMessage());
                    DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
                }
            }
        }
        resp.put("success", true);
        resp.put("message", null);

        return resp;
    }

    /***
     * 
     * <li>Description:冻结与解冻业务系统维护</li> 
     * <AUTHOR>
     * 2018年3月9日 
     * @param PrjName
     * @param isFreeze
     * @param user
     * @param dbType
     * @throws RepositoryException
     * return void
     */
    public void freezeProject ( String PrjName, Boolean isFreeze, UserInfo user, int dbType ) throws RepositoryException
    {
        updatePrjAdpFlagForIEAI(Constants.PRJ, isFreeze, PrjName, user, dbType);
        _appLogManager.logFreezePrjAdp(PrjName, user, true);
    }

    /***
     * 
     * <li>Description:冻结与解冻业务系统维护功能[只冻结与解冻作业调度下的工程]</li> 
     * <AUTHOR>
     * 2018年3月9日 
     * @param type
     * @param isFreeze
     * @param prjAdpName
     * @param user
     * @param dbType
     * @return
     * @throws RepositoryException
     * return boolean
     */
    public boolean updatePrjAdpFlagForIEAI ( short type, boolean isFreeze, String prjAdpName, UserInfo user,
            int dbType ) throws RepositoryException
    {
        boolean result = false;
        // check
        String action = null;// check action name for log
        if (type == Constants.PRJ)
        {
            if (isFreeze)
            {
                action = Constants.PROJ_FREEZE;
            } else
            {
                action = Constants.PROJ_UNFREEZE;
            }
        } else
        {
            if (isFreeze)
            {
                action = Constants.ADP_FREEZE;
            } else
            {
                action = Constants.ADP_UNFREEZE;
            }
        }

        Object persist = null;
        if (type == Constants.PRJ)
        {
            RepProject prj = getProjectForIEAI(prjAdpName, dbType);
            if (prj.isFreezed() != isFreeze)
            {
                prj.setFreezed(isFreeze);
                prj.setFreezeUserFullName(user.getFullName());
                try
                {
                    // 冻结与解冻工程的方法，仅限作业调度
                    updatePrjFreezeState(isFreeze, user.getFullName(), user.getId(), prj.getId());
                } catch (Exception e)
                {
                    _log.error("updatePrjFreezeState is error" + e.getMessage());
                }
            }
            if (isFreeze)
            {
                prj.setFreezeUser(user);
            }
            persist = prj;
        } else
        {
            RepAdaptor adp = getAdaptorForIEAI(prjAdpName, dbType);
            if (adp.isFreezed() != isFreeze)
            {
                adp.setFreezed(isFreeze);
                ObjectStorer.updateWithRetry(adp);
            }
            if (isFreeze)
            {
                adp.setFreezeUser(user);
            }
            persist = adp;

        }

        addLogHistory(type, persist, action, user);
        return result;
    }

    /**   
     * @Title: updatePrjFreezeState   
     * @Description: 修改工程冻结与解冻状态的方法(仅限作业调度，加入多写)         
     * @author: yue_sun 
     * @date:   2018年4月13日 下午1:30:29   
     */
    public void updatePrjFreezeState ( boolean isFreeze, String freezeUser, long freezeUserId, long prjId )
            throws Exception
    {
        String sql = "";
        long iFreezeState = 0;
        // 判断状态，0为正常状态，1为冻结状态
        if (isFreeze)
        {
            iFreezeState = 1;
        }
        Map<String, Connection> mapCon = new HashMap<String, Connection>();
        Map<String, String> mapConSuccess = new HashMap<String, String>();
        int i = 0;
        try
        {
            mapCon = ProjectManager.getInstance().getMapDBsourceList();
        } catch (PackException e3)
        {
            _log.error("updatePrjFreezeState method of BusinessSystemManager.class：多写获取数据源失败");
            e3.printStackTrace();
        }
        if (mapCon.isEmpty())
        {
        } else
        {
            for (Map.Entry<String, Connection> mapConnection : mapCon.entrySet())
            {
                Connection conn = null;
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                conn = mapConnection.getValue();
                try
                {
                    try
                    {
                        sql = "update ieai_project set ifreezed = ?,ifreezeuser=?,ifreezeuserid=? where iid = ? ";
                        actStat = conn.prepareStatement(sql);
                        actStat.setLong(1, iFreezeState);
                        actStat.setString(2, freezeUser);
                        actStat.setLong(3, freezeUserId);
                        actStat.setLong(4, prjId);
                        actStat.executeUpdate();
                        conn.commit();
                        i++;
                        mapConSuccess.put(mapConnection.getKey(), mapConnection.getKey());
                    } catch (Exception e)
                    {
                        e.printStackTrace();
                        _log.error("updatePrjFreezeState method of BusinessSystemManager.class SQLException:"
                                + e.getMessage());
                        _log.error("updatePrjFreezeState method of BusinessSystemManager.class：多写获取数据源失败");
                        throw new RepositoryException(ServerError.ERR_DB_QUERY);
                    } finally
                    {
                        DBResource.closeConn(conn, actRS, actStat, "deleteAlarmInfo", _log);
                    }
                } catch (RepositoryException ex)
                {
                    _log.error("updatePrjFreezeState method of BusinessSystemManager.class RepositoryException:"
                            + ex.getMessage());
                    DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
                }
            }
        }
    }

    /***
     * 
     * <li>Description:获取作业调度下的工程中的信息</li> 
     * <AUTHOR>
     * 2018年3月9日  
     * @param projectName
     * @param dbType
     * @return
     * @throws RepositoryException
     * return RepProject
     */
    public RepProject getProjectForIEAI ( String projectName, int dbType ) throws RepositoryException
    {
        RepProject repProject = new RepProject();
        int count = 0;
        String sql = "select t.iid,t.ipkgcontentId,t.iname,t.imajver,t.iminver,t.ifreezed,t.iuploaduser,t.icomment,t.iuploadnum,t.iuuid,"
                + " t.iuploadtime,t.ifreezeuser,t.ifreezeuserid,t.iuploaduserid,t.igroupid from ieai_project t"
                + " where t.protype = 1 and t.iid = t.ILATESTID  and t.iname = ? order by t.iname";
        for (int i = 0;; i++)
        {
            try
            {

                Connection conn = null;
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                try
                {
                    List<String> systemNameList = new ArrayList<String>();
                    conn = DBResource.getConnection("getProjectForIEAI", _log, dbType);
                    actStat = conn.prepareStatement(sql);
                    actStat.setString(1, projectName);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        count++;
                        repProject.setId(actRS.getLong("iid"));
                        repProject.setPkgContentId(actRS.getLong("ipkgcontentId"));
                        repProject.setName(actRS.getString("iname"));
                        repProject.setMajorVersion(actRS.getInt("imajver"));
                        repProject.setMinorVersion(actRS.getInt("iminver"));
                        repProject.setFreezed(actRS.getInt("ifreezed") == 1 ? true : false);
                        UserInfo uploadUser = new UserInfo();
                        uploadUser.setId(actRS.getLong("iuploaduserid"));
                        uploadUser.setFullName(actRS.getString("iuploaduser"));
                        repProject.setUploadUser(uploadUser);
                        repProject.setComment(actRS.getString("icomment"));
                        repProject.setUploadNum(actRS.getInt("iuploadnum"));
                        repProject.setUuid(actRS.getString("iuuid"));
                        repProject.setUploadTime(actRS.getTimestamp("iuploadtime"));
                        UserInfo freezeUser = new UserInfo();
                        freezeUser.setId(actRS.getLong("ifreezeuserid"));
                        freezeUser.setFullName(actRS.getString("ifreezeuser"));
                        repProject.setFreezeUser(freezeUser);
                        repProject.setUploadUserId(actRS.getLong("iuploaduserid"));
                        repProject.setGroupId(actRS.getLong("igroupid"));
                    }
                } catch (SQLException e)
                {
                    _log.error(
                        "getProjectForIEAI method of BusinessSystemManager.class SQLException:" + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, actRS, actStat, "getProjectForIEAI", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log.error(
                    "getProjectForIEAI method of BusinessSystemManager.class RepositoryException:" + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        if (count == 0)
        {
            /** project not exist */
            _log.info("project not exist ");
            throw new RepositoryException(ServerError.ERR_PRJ_NOT_EXIST);
        }

        return repProject;
    }

    /***
     * 
     * <li>Description:判断工程下是否有正在运行的工作流</li> 
     * <AUTHOR>
     * 2018年3月9日 
     * @param projectName
     * @return
     * @throws RepositoryException
     * return Boolean
     */
    private Boolean getRunningFlowIdsOfProjectForIEAI ( String projectName ) throws RepositoryException
    {
        List qlist = new ArrayList();
        for (int i = 0;; i++)
        {
            try
            {
                Connection conn = null;
                Statement stmt = null;
                ResultSet rset = null;
                try
                {
                    String sql = "select flowinfo.iflowId from ieai_workflowinstance flowinfo where "
                            + " ( flowinfo.istatus = " + Constants.STATE_RUNNING + " or flowinfo.istatus = "
                            + Constants.STATE_LISTENING + " or flowinfo.istatus = " + Constants.STATE_RECOVERING
                            + " or flowinfo.istatus = " + Constants.STATE_PAUSED + " or flowinfo.istatus = "
                            + Constants.STATE_STOPPING + ") and flowinfo.iprojectName = '" + projectName + "'";

                    conn = DBResource.getConnection("getRunningFlowIdsOfProjectForIEAI", _log, Constants.IEAI_IEAI);
                    stmt = conn.createStatement();
                    rset = stmt.executeQuery(sql);
                    while (rset.next())
                    {
                        qlist.add(new Long(rset.getLong("iflowid")));
                    }
                    break;
                } catch (SQLException e)
                {
                    _log.error("getRunningFlowIdsOfProjectForIEAI method of BusinessSystemManager.class SQLException:"
                            + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, rset, stmt, "getRunningFlowIdsOfProjectForIEAI", _log);
                }
            } catch (RepositoryException ex)
            {
                _log.error(
                    "getRunningFlowIdsOfProjectForIEAI method of BusinessSystemManager.class RepositoryException:"
                            + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }

        }
        if (qlist.size() > 0)
        {
            return true;
        } else
        {
            return false;
        }
    }

    /**
     * get the lastest adaptor according to adaptor's name
     * 
     * @param adaptorName adaptor name
     * @param hibSession hibernate session
     * @return instance of adaptor ,if not exist ,throw RespositoeyException
     * @throws HibernateException
     * @throws RepositoryException
     */
    private RepAdaptor getAdaptorForIEAI ( String adaptorName, int dbType ) throws RepositoryException
    {
        RepAdaptor repAdaptor = new RepAdaptor();
        int count = 0;
        String sql = "select t.iid,t.ipkgcontentId,t.iname,t.majver,t.iminver,t.ifreezed,t.icomment,t.ifreezeUser"
                + " t.iuploadnum,t.iuuid,t.iuploadTime,t.iuploaduser,t.iuploaduserid,t.ifreezeuserid"
                + " from ieai_adaptor t where t.iname = ? ";
        for (int i = 0; i < 10; i++)
        {
            try
            {

                Connection conn = null;
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                try
                {
                    List<String> systemNameList = new ArrayList<String>();
                    conn = DBResource.getConnection("getProjectForIEAI", _log, dbType);
                    actStat = conn.prepareStatement(sql);
                    actStat.setString(1, adaptorName);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        count++;
                        repAdaptor.setId(actRS.getLong("iid"));
                        repAdaptor.setPkgContentId(actRS.getLong("ipkgcontentId"));
                        repAdaptor.setName(actRS.getString("iname"));
                        repAdaptor.setMajorVersion(actRS.getInt("imajver"));
                        repAdaptor.setMinorVersion(actRS.getInt("iminver"));
                        repAdaptor.setFreezed(actRS.getInt("ifreezed") == 1 ? true : false);
                        UserInfo uploadUser = new UserInfo();
                        uploadUser.setId(actRS.getLong("iuploaduserid"));
                        uploadUser.setFullName(actRS.getString("iuploaduser"));
                        repAdaptor.setUploadUser(uploadUser);
                        repAdaptor.setComment(actRS.getString("icomment"));
                        repAdaptor.setUploadNum(actRS.getInt("iuploadnum"));
                        repAdaptor.setUuid(actRS.getString("iuuid"));
                        repAdaptor.setUploadTime(actRS.getTimestamp("iuploadtime"));
                        UserInfo freezeUser = new UserInfo();
                        freezeUser.setId(actRS.getLong("ifreezeuserid"));
                        freezeUser.setFullName(actRS.getString("ifreezeuser"));
                        repAdaptor.setFreezeUser(freezeUser);
                        repAdaptor.setUploadUserId(actRS.getLong("iuploaduserid"));
                    }
                } catch (SQLException e)
                {
                    _log.error(
                        "getAdaptorForIEAI method of BusinessSystemManager.class SQLException:" + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, actRS, actStat, "getAdaptorForIEAI", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log.error(
                    "getAdaptorForIEAI method of BusinessSystemManager.class RepositoryException:" + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        if (count == 0)
        {
            _log.info("Adaptor does not exist in database!");
            throw new RepositoryException(ServerError.ERR_ADP_NOT_EXIST);
        }
        return repAdaptor;
    }

    /***
     * 
     * <li>Description:判断是否为excel上传的工程</li> 
     * <AUTHOR>
     * 2018年3月13日 
     * @param prjName
     * @param dbType
     * @return
     * @throws RepositoryException
     * return int
     */
    public int isExcelUploadProject ( String prjName, int dbType ) throws RepositoryException
    {
        int result = 0;
        int mainProjectCount = isExcelUploadMainProject(prjName, dbType);
        int mainLineProCount = isExcelUploadMainLineProject(prjName, dbType);
        int childrenProCount = isExcelUploadChildrenProject(prjName, dbType);

        if (mainProjectCount > 0 || mainLineProCount > 0 || childrenProCount > 0)
        {
            result = 1;
        }
        return result;
    }

    /***
     * 
     * <li>Description:验证是否excel上传主工程</li> 
     * <AUTHOR>
     * 2018年3月13日 
     * @param adaptorName
     * @param dbType
     * @return
     * @throws RepositoryException
     * return int
     */
    private int isExcelUploadMainProject ( String adaptorName, int dbType ) throws RepositoryException
    {
        int count = 0;
        String sql = "SELECT count(1) as count  FROM ieai_excelmodel t where t.imainproname = ? ";
        for (int i = 0; i < 10; i++)
        {
            try
            {

                Connection conn = null;
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                try
                {
                    List<String> systemNameList = new ArrayList<String>();
                    conn = DBResource.getConnection("isExcelUploadMainProject", _log, dbType);
                    actStat = conn.prepareStatement(sql);
                    actStat.setString(1, adaptorName);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        if (actRS.getInt("count") > 0)
                        {
                            count = 1;
                        }
                    }

                } catch (SQLException e)
                {
                    _log.error("isExcelUploadMainProject method of BusinessSystemManager.class SQLException:"
                            + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, actRS, actStat, "isExcelUploadMainProject", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log.error("isExcelUploadMainProject method of BusinessSystemManager.class RepositoryException:"
                        + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return count;
    }

    /***
     * 
     * <li>Description:验证是否excel上传主线工程</li> 
     * <AUTHOR>
     * 2018年3月13日 
     * @param adaptorName
     * @param dbType
     * @return
     * @throws RepositoryException
     * return int
     */
    private int isExcelUploadMainLineProject ( String adaptorName, int dbType ) throws RepositoryException
    {
        int count = 0;
        String sql = "SELECT count(1) as count  FROM ieai_excelmodel t where t.imainlinename = ? ";
        for (int i = 0; i < 10; i++)
        {
            try
            {

                Connection conn = null;
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                try
                {
                    List<String> systemNameList = new ArrayList<String>();
                    conn = DBResource.getConnection("isExcelUploadMainLineProject", _log, dbType);
                    actStat = conn.prepareStatement(sql);
                    actStat.setString(1, adaptorName);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        if (actRS.getInt("count") > 0)
                        {
                            count = 1;
                        }
                    }

                } catch (SQLException e)
                {
                    _log.error("isExcelUploadMainLineProject method of BusinessSystemManager.class SQLException:"
                            + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, actRS, actStat, "isExcelUploadMainLineProject", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log.error("isExcelUploadMainLineProject method of BusinessSystemManager.class RepositoryException:"
                        + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return count;
    }

    /***
     * 
     * <li>Description:验证是否excel上传子工程</li> 
     * <AUTHOR>
     * 2018年3月13日 
     * @param adaptorName
     * @param dbType
     * @return
     * @throws RepositoryException
     * return int
     */
    private int isExcelUploadChildrenProject ( String adaptorName, int dbType ) throws RepositoryException
    {
        int count = 0;
        String sql = "SELECT count(1) as count  FROM ieai_excelmodel t where t.ichildproname = ? ";
        for (int i = 0; i < 10; i++)
        {
            try
            {

                Connection conn = null;
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                try
                {
                    List<String> systemNameList = new ArrayList<String>();
                    conn = DBResource.getConnection("isExcelUploadChildrenProject", _log, dbType);
                    actStat = conn.prepareStatement(sql);
                    actStat.setString(1, adaptorName);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        if (actRS.getInt("count") > 0)
                        {
                            count = 1;
                        }
                    }

                } catch (SQLException e)
                {
                    _log.error("isExcelUploadChildrenProject method of BusinessSystemManager.class SQLException:"
                            + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, actRS, actStat, "isExcelUploadChildrenProject", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log.error("isExcelUploadChildrenProject method of BusinessSystemManager.class RepositoryException:"
                        + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return count;
    }

    /** 一体化运维4.7.9 业务系统维护功能相关代码 by yue_sun on 2018-03-13 end **/

    /**
     * to add log history of project or an adaptor
     * 
     * @param hibSession
     * @param type one value of PRJ | ADP
     * @param projAdp project or adaptor
     * @param action
     */
    private void addLogHistory ( short type, Object projAdp, String action, UserInfo user ) throws RepositoryException
    {

        Serializable persist = null;
        if (Constants.PRJ == type)
        {
            RepProjectHistory history = new RepProjectHistory();
            history.setAction(action);
            history.setActionTime(System.currentTimeMillis());
            history.setProjectName(((RepProject) projAdp).getName());
            history.setUploadNum(((RepProject) projAdp).getUploadNum());
            history.setUuid(((RepProject) projAdp).getUuid());
            history.setUser(user);
            persist = history;
        } else
        {
            RepAdaptorHistory history = new RepAdaptorHistory();
            history.setAction(action);
            history.setActionTime(System.currentTimeMillis());
            history.setAdaptorName(((RepAdaptor) projAdp).getName());
            history.setUploadNum(((RepAdaptor) projAdp).getUploadNum());
            history.setUuid(((RepAdaptor) projAdp).getUuid());
            history.setUser(user);
            persist = history;
        }
        ObjectStorer.saveWithRetry(persist);
    }

    /**   
     * @Title: getComputerListForSPDB   
     * @Description:  浦发银行 平台管理 业务系统维护 查询设备
     * @param businessSystemBeanForQuery
     * @param filter
     * @param type
     * @return
     * @throws RepositoryException      
     * @author: yuhe_zhang
     * @date:   2019年1月4日 下午3:53:38   
     */
    public Map<String, Object> getComputerListForSPDB ( BusinessSystemBeanForQuery businessSystemBeanForQuery,
            AgentModel filter, int type ) throws RepositoryException
    {
        Map map = new HashMap();
        List res = new ArrayList();
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        String dateV = null;
        for (int i = 0;; i++)
        {

            try
            {
                try
                {

                    int itype = (type == Constants.IEAI_HEALTH_INSPECTION) ? Constants.IEAI_HEALTH_INSPECTION : type;
                    conn = DBResource.getConnection("getComputerListForSPDB", _log, itype);
                    int count = 0;
                    long systemId = businessSystemBeanForQuery.getSysIdForQuery();
                    String sqlWhere = " WHERE T.SYSTEMID =? ";
                    String orderString = " ORDER BY T1.IPALIAS ";

                    if (filter.getAgentIp() != null && !"".equals(filter.getAgentIp()))
                    {
                        sqlWhere = sqlWhere + " and t1.IP like '%" + filter.getAgentIp() + "%'";
                    }

                    if (filter.getHostName() != null && !"".equals(filter.getHostName()))
                    {
                        sqlWhere = sqlWhere + " and t2.ICOM_NAME like '%" + filter.getHostName() + "%'";
                    }
                    if (filter.getCtype() != null && !"".equals(filter.getCtype()))
                    {
                        sqlWhere = sqlWhere + " and VE.ICTID =" + filter.getCtype() + "";
                    }
                    if (filter.getCname() != null && !"".equals(filter.getCname()))
                    {
                        sqlWhere = sqlWhere + " and VE.CMID =" + filter.getCname() + "";
                    }

                    if (filter.getSysAdmin() != null && !"".equals(filter.getSysAdmin()))
                    {
                        sqlWhere = sqlWhere + " and VE.SYSADMIN like '%" + filter.getSysAdmin() + "%'";
                    }
                    if (filter.getCenterName() != null && !"".equals(filter.getCenterName()))
                    {
                        sqlWhere = sqlWhere + " and VE.CENTERNAME = '" + filter.getCenterName() + "'";
                    }

                    if (filter.getAppAdmin() != null && !"".equals(filter.getAppAdmin()))
                    {
                        sqlWhere = sqlWhere + " and VE.APPADMIN like '%" + filter.getAppAdmin() + "%'";
                    }
                    if (filter.getSystemInfo() != null && !"".equals(filter.getSystemInfo()))
                    {
                        sqlWhere = sqlWhere + " and VE.SYSTEMINFO like '%" + filter.getSystemInfo() + "%'";
                    }

                    // if (filter.getOsType() != null && !"".equals(filter.getOsType()))
                    // {
                    // sqlWhere = sqlWhere + " and t.IOS_NAME like '%" + filter.getOsType() + "%'";
                    // }
                    // 信息采集过来的
                    // 操作系统版本 VE.OS_VERSION
                    if (filter.getOsVersion() != null && !"".equals(filter.getOsVersion()))
                    {
                        sqlWhere = sqlWhere + " and VE.OS_VERSION like '%" + filter.getOsVersion() + "%'";
                    }

                    // 数据库版本 VE.DB_VERSION
                    if (filter.getDbVersion() != null && !"".equals(filter.getDbVersion()))
                    {
                        sqlWhere = sqlWhere + " and VE.DB_VERSION like '%" + filter.getDbVersion() + "%'";
                    }
                    // 中间件版本 VE.MIDDLEWARE_VERSION
                    if (filter.getMiddlewareVersion() != null && !"".equals(filter.getMiddlewareVersion()))
                    {
                        sqlWhere = sqlWhere + " and VE.MIDDLEWARE_VERSION like '%" + filter.getMiddlewareVersion()
                                + "%'";
                    }

                    // 操作系统类型 VE.OS_TYPE AS OSTYPE
                    if (filter.getOsType() != null && !"".equals(filter.getOsType()))
                    {
                        sqlWhere = sqlWhere + " and VE.OS_TYPE  like'%" + filter.getOsType() + "%'";
                    }
                    // 中间件类型 VE.MIDDLEWARE_TYPE
                    if (filter.getMiddlewareType() != null && !"".equals(filter.getMiddlewareType()))
                    {
                        sqlWhere = sqlWhere + " and VE.MIDDLEWARE_TYPE like '%" + filter.getMiddlewareType() + "%'";
                    }
                    // 数据库类型 VE.db_type
                    if (filter.getDbType() != null && !"".equals(filter.getDbType()))
                    {
                        sqlWhere = sqlWhere + " and VE.db_type  like '%" + filter.getDbType() + "%'";
                    }

                    // Oracle db2
                    if (DBManager.Orcl_Faimily() || JudgeDB.IEAI_DB_TYPE == 2)
                    {
                        dateV = " FUN_GET_DATE_STRING(t2.Icreatetime,8,'YYYY-MM-DD') as ICREATETIME, ";
                    } else
                    {// mysql
                        dateV = " FROM_UNIXTIME(t2.icreatetime/1000,'%Y-%m-%d') as ICREATETIME, ";
                    }

                    String sqlPageString = "SELECT * FROM (SELECT  ROW_NUMBER() OVER (ORDER BY T1.CPID DESC) AS ROW_NUMBER,T1.CPID, T1.CPNAME,T1.IP ,T.SYSPARAM, t2.ICOM_NAME  as HOSTNAME,T2.ISTART_USER,"
                            + dateV
                            + "VE.SYSADMIN AS SYSADMIN, VE.APPADMIN AS APPADMIN, VE.SYSTEMINFO AS SYSTEMINFO, VE.CENTERNAME, VE.OS_TYPE AS OSTYPE ,  VE.OS_VERSION, VE.RUN_DAYS, VE.IS_HA, VE.DB_VERSION, VE.DB_TYPE,VE.MIDDLEWARE_VERSION,VE.MIDDLEWARE_TYPE FROM IEAI_COMPUTER_LIST T1 JOIN IEAI_SYS_RELATION T ON T.COMPUTERID = T1.CPID LEFT JOIN IEAI_AGENTINFO T2 ON T1.IP = T2.IAGENT_IP AND T1.IAGENTUP_ID = T2.IAGENTINFO_ID LEFT JOIN VE_WHITELIST_EQUINFO VE ON T1.CPID = VE.CPID ";
                    String sqlPageStringEnd = ") A WHERE A.ROW_NUMBER  <=? AND A.ROW_NUMBER >  ?  ";
                    String sqlCount = "SELECT COUNT(1) AS NUM FROM  IEAI_COMPUTER_LIST T1 JOIN IEAI_SYS_RELATION T ON T.COMPUTERID = T1.CPID LEFT JOIN IEAI_AGENTINFO T2 ON T1.IP = T2.IAGENT_IP AND T1.IAGENTUP_ID = T2.IAGENTINFO_ID  LEFT JOIN VE_WHITELIST_EQUINFO VE ON T1.CPID = VE.CPID ";
                    // 定时任务类型,从agentinfo表出出
                    if (itype == Constants.IEAI_TIMINGTASK)
                    {
                        sqlPageString = "SELECT * FROM (SELECT ROW_NUMBER() OVER (ORDER BY T2.IAGENTINFO_ID DESC) AS ROW_NUMBER,T2.IAGENTINFO_ID AS CPID, T2.IAGENT_IP AS IP,T2.ICOM_NAME as HOSTNAME,T2.ISTART_USER,"
                                + dateV
                                + "VE.SYSADMIN AS SYSADMIN, VE.APPADMIN AS APPADMIN, VE.SYSTEMINFO AS SYSTEMINFO, VE.CENTERNAME, VE.OS_TYPE AS OSTYPE ,  VE.OS_VERSION, VE.RUN_DAYS, VE.IS_HA, VE.DB_VERSION, VE.DB_TYPE,VE.MIDDLEWARE_VERSION,VE.MIDDLEWARE_TYPE  FROM IEAI_AGENTINFO T2  JOIN IEAI_SYS_RELATION T ON  T.COMPUTERID=T2.IAGENTINFO_ID LEFT JOIN IEAI_COMPUTER_LIST T1 ON T1.IP = T2.IAGENT_IP  AND T1.IAGENTUP_ID = T2.IAGENTINFO_ID LEFT JOIN VE_WHITELIST_EQUINFO VE ON T1.CPID = VE.CPID ";
                        sqlCount = "SELECT COUNT(1) AS NUM FROM  IEAI_AGENTINFO T2  JOIN IEAI_SYS_RELATION T ON  T.COMPUTERID=T2.IAGENTINFO_ID LEFT JOIN IEAI_COMPUTER_LIST T1 ON T1.IP = T2.IAGENT_IP AND T1.IAGENTUP_ID = T2.IAGENTINFO_ID LEFT JOIN VE_WHITELIST_EQUINFO VE ON T1.CPID = VE.CPID ";
                    }
                    // 支持mysql
                    if (JudgeDB.MYSQL == JudgeDB.IEAI_DB_TYPE)
                    {
                        sqlPageString = " SELECT DISTINCT T1.CPID, T1.CPNAME,T1.IP ,T.SYSPARAM,t2.ICOM_NAME as HOSTNAME,T2.ISTART_USER,"
                                + dateV
                                + "VE.SYSADMIN AS SYSADMIN, VE.APPADMIN AS APPADMIN, VE.SYSTEMINFO AS SYSTEMINFO, VE.CENTERNAME, VE.OS_TYPE AS OSTYPE ,  VE.OS_VERSION, VE.RUN_DAYS, VE.IS_HA, VE.DB_VERSION,VE.DB_TYPE, VE.MIDDLEWARE_VERSION,VE.MIDDLEWARE_TYPE FROM IEAI_COMPUTER_LIST T1 JOIN IEAI_SYS_RELATION T ON T.COMPUTERID = T1.CPID LEFT JOIN IEAI_AGENTINFO T2 ON T1.IP = T2.IAGENT_IP AND T1.IAGENTUP_ID = T2.IAGENTINFO_ID LEFT JOIN VE_WHITELIST_EQUINFO VE ON T1.CPID = VE.CPID ";
                        sqlPageStringEnd = " limit ?,? ";
                        sqlCount = "SELECT COUNT(1) AS NUM FROM  IEAI_COMPUTER_LIST T1 JOIN IEAI_SYS_RELATION T ON T.COMPUTERID = T1.CPID LEFT JOIN IEAI_AGENTINFO T2 ON T1.IP = T2.IAGENT_IP AND T1.IAGENTUP_ID = T2.IAGENTINFO_ID LEFT JOIN VE_WHITELIST_EQUINFO VE ON T1.CPID = VE.CPID ";
                        // 定时任务类型，从agentinfo表出
                        if (itype == Constants.IEAI_TIMINGTASK)
                        {
                            sqlPageString = "SELECT T2.IAGENTINFO_ID AS CPID, T2.IAGENT_IP AS IP,T2.ICOM_NAME as HOSTNAME,T2.ISTART_USER, "
                                    + dateV
                                    + "VE.SYSADMIN AS SYSADMIN, VE.APPADMIN AS APPADMIN, VE.SYSTEMINFO AS SYSTEMINFO, VE.CENTERNAME, VE.OS_TYPE AS OSTYPE ,  VE.OS_VERSION, VE.RUN_DAYS, VE.IS_HA, VE.DB_VERSION,VE.DB_TYPE, VE.MIDDLEWARE_VERSION,VE.MIDDLEWARE_TYPE  FROM IEAI_AGENTINFO T2  JOIN IEAI_SYS_RELATION T ON  T.COMPUTERID=T2.IAGENTINFO_ID LEFT JOIN IEAI_COMPUTER_LIST T1 ON T1.IP = T2.IAGENT_IP AND T1.IAGENTUP_ID = T2.IAGENTINFO_ID LEFT JOIN VE_WHITELIST_EQUINFO VE ON T1.CPID = VE.CPID ";
                            sqlCount = "SELECT COUNT(1) AS NUM FROM  IEAI_AGENTINFO T2  JOIN IEAI_SYS_RELATION T ON  T.COMPUTERID=T2.IAGENTINFO_ID LEFT JOIN IEAI_COMPUTER_LIST T1 ON T1.IP = T2.IAGENT_IP AND T1.IAGENTUP_ID = T2.IAGENTINFO_ID LEFT JOIN VE_WHITELIST_EQUINFO VE ON T1.CPID = VE.CPID ";
                        }
                    }
                    // sqlWhere = sqlWhere + "AND T.SYSTEMID =? ";
                    sqlPageString = sqlPageString + sqlWhere + orderString + sqlPageStringEnd;
                    sqlCount = sqlCount + sqlWhere;

                    actStat = conn.prepareStatement(sqlPageString);
                    int index = 0;
                    actStat.setLong(++index, systemId);
                    if (3 == JudgeDB.IEAI_DB_TYPE)// mysql
                    {
                        actStat.setInt(++index, businessSystemBeanForQuery.getStart());
                        actStat.setInt(++index, businessSystemBeanForQuery.getLimit());
                    } else
                    {
                        actStat.setInt(++index,
                            businessSystemBeanForQuery.getStart() + businessSystemBeanForQuery.getLimit());
                        actStat.setInt(++index, businessSystemBeanForQuery.getStart());
                    }
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        Map computerMap = new HashMap();

                        if (itype == Constants.IEAI_TIMINGTASK)
                        {
                            computerMap.put("cpId", actRS.getLong("CPID"));
                            computerMap.put("agentIp", actRS.getString("IP"));

                            computerMap.put("osVersion", actRS.getString("OS_VERSION")); // 操作系统版本
                            computerMap.put("runDays", actRS.getString("RUN_DAYS"));  // 运行天数
                            computerMap.put("isHa", actRS.getString("IS_HA")); // 是否双机
                            computerMap.put("dbVersion", actRS.getString("DB_VERSION")); // 数据库版本
                            computerMap.put("dbType", actRS.getString("DB_TYPE"));
                            computerMap.put("middlewareVersion", actRS.getString("MIDDLEWARE_VERSION"));// 中间件版本
                            computerMap.put("icreateTime", actRS.getString("ICREATETIME")); // 纳管时间
                            // computerMap.put("sysName", actRS.getString("SYSNAME"));
                            // computerMap.put("appName", actRS.getString("APPNAME"));
                            computerMap.put("hostName", actRS.getString("HOSTNAME"));
                            computerMap.put("osType", actRS.getString("OSTYPE")); // 系统类型 信息采集来的
                            computerMap.put("startUser", actRS.getString("ISTART_USER")); // 纳管 启动人
                            computerMap.put("centerName", actRS.getString("CENTERNAME"));
                            computerMap.put("sysAdmin",
                                actRS.getString("SYSADMIN") == null ? "" : actRS.getString("SYSADMIN"));
                            computerMap.put("appAdmin",
                                actRS.getString("APPADMIN") == null ? "" : actRS.getString("APPADMIN"));
                            computerMap.put("systemInfo",
                                actRS.getString("SYSTEMINFO") == null ? "" : actRS.getString("SYSTEMINFO"));
                            computerMap.put("middlewareType",
                                actRS.getString("MIDDLEWARE_TYPE") == null ? "" : actRS.getString("MIDDLEWARE_TYPE")); // 中间件类型
                        } else if (itype == Constants.IEAI_HEALTH_INSPECTION || itype == Constants.IEAI_SUS
                                || itype == Constants.IEAI_APM)
                        {
                            computerMap.put("cpName", actRS.getString("CPNAME"));
                            computerMap.put("cpId", actRS.getLong("CPID"));
                            computerMap.put("sysParam", actRS.getString("SYSPARAM"));
                            computerMap.put("agentIp", actRS.getString("IP"));

                            computerMap.put("osVersion", actRS.getString("OS_VERSION")); // 操作系统版本
                            computerMap.put("runDays", actRS.getString("RUN_DAYS"));  // 运行天数
                            computerMap.put("isHa", actRS.getString("IS_HA")); // 是否双机
                            computerMap.put("dbVersion", actRS.getString("DB_VERSION")); // 数据库版本
                            computerMap.put("dbType", actRS.getString("DB_TYPE"));
                            computerMap.put("middlewareVersion", actRS.getString("MIDDLEWARE_VERSION"));// 中间件版本
                            computerMap.put("icreateTime", actRS.getString("ICREATETIME")); // 纳管时间
                            // computerMap.put("sysName", actRS.getString("SYSNAME"));
                            // computerMap.put("appName", actRS.getString("APPNAME"));
                            computerMap.put("hostName", actRS.getString("HOSTNAME"));
                            computerMap.put("osType", actRS.getString("OSTYPE")); // 系统类型 信息采集来的
                            computerMap.put("startUser", actRS.getString("ISTART_USER")); // 纳管 启动人
                            computerMap.put("centerName", actRS.getString("CENTERNAME"));
                            computerMap.put("sysAdmin",
                                actRS.getString("SYSADMIN") == null ? "" : actRS.getString("SYSADMIN"));
                            computerMap.put("appAdmin",
                                actRS.getString("APPADMIN") == null ? "" : actRS.getString("APPADMIN"));
                            computerMap.put("systemInfo",
                                actRS.getString("SYSTEMINFO") == null ? "" : actRS.getString("SYSTEMINFO"));
                            computerMap.put("middlewareType",
                                actRS.getString("MIDDLEWARE_TYPE") == null ? "" : actRS.getString("MIDDLEWARE_TYPE")); // 中间件类型
                        }

                        res.add(computerMap);
                    }
                    actStat = conn.prepareStatement(sqlCount);
                    index = 0;
                    actStat.setLong(++index, systemId);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        count = actRS.getInt("NUM");
                    }
                    map.put("total", count);
                    map.put("dataList", res);

                } catch (SQLException e)
                {
                    _log.error("getComputerListForSPDB method of BusinessSystemManager.class SQLException:", e);
                    // throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, actRS, actStat, "getComputerListForSPDB", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log.error("getComputerListForSPDB method of BusinessSystemManager.class RepositoryException:", ex);
                try
                {
                    DBRetryUtil.waitForNextTry(i, ex);
                } catch (RepositoryException e)
                {
                    _log.error("getComputerListForSPDB method of BusinessSystemManager.class RepositoryException:", ex);
                    throw e;
                }
            }

        }

        return map;
    }

    /**   
     * @Title: getHostNameForBusSysSPDB   
     * @Description:  浦发需求 业务系统维护查询主机名列表
     * @param businessSystemBeanForQuery
     * @param type
     * @return
     * @throws RepositoryException      
     * @author: yuhe_zhang 
     * @date:   2019年1月14日 下午2:54:36   
     */
    public Map<String, Object> getHostNameForBusSysSPDB ( BusinessSystemBeanForQuery businessSystemBeanForQuery,
            int type ) throws RepositoryException
    {
        Map map = new HashMap();
        List res = new ArrayList();
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        for (int i = 0;; i++)
        {
            try
            {
                try
                {
                    int itype = (type == Constants.IEAI_HEALTH_INSPECTION) ? Constants.IEAI_HEALTH_INSPECTION : type;
                    conn = DBResource.getConnection(Thread.currentThread().getStackTrace()[1].getMethodName(), _log,
                        itype);
                    long systemId = businessSystemBeanForQuery.getSysIdForQuery();
                    String sqlWhere = " WHERE T.SYSTEMID =? ";
                    String orderString = " ORDER BY t2.ICOM_NAME ASC ";

                    String sqlPageString = "SELECT  DISTINCT t2.ICOM_NAME  as HOSTNAME FROM  "
                            + " IEAI_COMPUTER_LIST T1 JOIN IEAI_SYS_RELATION T ON T.COMPUTERID = T1.CPID LEFT JOIN IEAI_AGENTINFO T2 ON T1.IP = T2.IAGENT_IP AND T1.IAGENTUP_ID = T2.IAGENTINFO_ID LEFT JOIN VE_WHITELIST_EQUINFO VE ON T1.CPID = VE.CPID ";
                    // 定时任务类型,从agentinfo表出出
                    if (itype == Constants.IEAI_TIMINGTASK)
                    {
                        sqlPageString = "SELECT DISTINCT T2.ICOM_NAME as HOSTNAME FROM"
                                + " IEAI_AGENTINFO T2  JOIN IEAI_SYS_RELATION T ON  T.COMPUTERID=T2.IAGENTINFO_ID LEFT JOIN IEAI_COMPUTER_LIST T1 ON T1.IP = T2.IAGENT_IP AND T1.IAGENTUP_ID = T2.IAGENTINFO_ID LEFT JOIN VE_WHITELIST_EQUINFO VE ON T1.CPID = VE.CPID ";
                    }
                    sqlPageString = sqlPageString + sqlWhere + orderString;
                    actStat = conn.prepareStatement(sqlPageString);
                    int index = 0;
                    actStat.setLong(++index, systemId);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        Map computerMap = new HashMap();
                        computerMap.put("hostName", actRS.getString("HOSTNAME"));
                        res.add(computerMap);
                    }
                    map.put("dataList", res);
                } catch (SQLException e)
                {
                    _log.error(Thread.currentThread().getStackTrace()[1].getMethodName()
                            + "  method of BusinessSystemManager.class SQLException:",
                        e);
                } finally
                {
                    DBResource.closeConn(conn, actRS, actStat,
                        Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName()
                        + " method of BusinessSystemManager.class RepositoryException:",
                    ex);
                try
                {
                    DBRetryUtil.waitForNextTry(i, ex);
                } catch (RepositoryException e)
                {
                    _log.error(Thread.currentThread().getStackTrace()[1].getMethodName()
                            + " method of BusinessSystemManager.class RepositoryException:",
                        ex);
                    throw e;
                }
            }
        }
        return map;
    }

    /**   
     * @Title: getIpForBusSysSPDB   
     * @Description:  浦发需求 业务系统维护查询IP列表
     * @param businessSystemBeanForQuery
     * @param type
     * @return
     * @throws RepositoryException      
     * @author: yuhe_zhang 
     * @date:   2019年1月14日 下午2:54:36   
     */
    public Map<String, Object> getIpForBusSysSPDB ( BusinessSystemBeanForQuery businessSystemBeanForQuery, int type )
            throws RepositoryException
    {
        Map map = new HashMap();
        List res = new ArrayList();
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        for (int i = 0;; i++)
        {
            try
            {
                try
                {
                    int itype = (type == Constants.IEAI_HEALTH_INSPECTION) ? Constants.IEAI_HEALTH_INSPECTION : type;
                    conn = DBResource.getConnection(Thread.currentThread().getStackTrace()[1].getMethodName(), _log,
                        itype);
                    long systemId = businessSystemBeanForQuery.getSysIdForQuery();
                    String sqlWhere = " WHERE T.SYSTEMID =? ";
                    // String orderString = " ORDER BY T1.IP ASC ";

                    String sqlPageString = "SELECT  DISTINCT T1.IP FROM  "
                            + " IEAI_COMPUTER_LIST T1 JOIN IEAI_SYS_RELATION T ON T.COMPUTERID = T1.CPID LEFT JOIN IEAI_AGENTINFO T2 ON T1.IP = T2.IAGENT_IP AND T1.IAGENTUP_ID = T2.IAGENTINFO_ID LEFT JOIN VE_WHITELIST_EQUINFO VE ON T1.CPID = VE.CPID ";
                    // 定时任务类型,从agentinfo表出出
                    if (itype == Constants.IEAI_TIMINGTASK)
                    {
                        sqlPageString = "SELECT DISTINCT T2.IAGENT_IP as IP FROM"
                                + " IEAI_AGENTINFO T2  JOIN IEAI_SYS_RELATION T ON  T.COMPUTERID=T2.IAGENTINFO_ID LEFT JOIN IEAI_COMPUTER_LIST T1 ON T1.IP = T2.IAGENT_IP AND T1.IAGENTUP_ID = T2.IAGENTINFO_ID LEFT JOIN VE_WHITELIST_EQUINFO VE ON T1.CPID = VE.CPID ";
                    }
                    sqlPageString = sqlPageString + sqlWhere;
                    actStat = conn.prepareStatement(sqlPageString);
                    int index = 0;
                    actStat.setLong(++index, systemId);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        Map computerMap = new HashMap();
                        computerMap.put("agentIp", actRS.getString("IP"));
                        res.add(computerMap);
                    }
                    map.put("dataList", res);
                } catch (SQLException e)
                {
                    _log.error(Thread.currentThread().getStackTrace()[1].getMethodName()
                            + "  method of BusinessSystemManager.class SQLException:",
                        e);
                } finally
                {
                    DBResource.closeConn(conn, actRS, actStat,
                        Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName()
                        + " method of BusinessSystemManager.class RepositoryException:",
                    ex);
                try
                {
                    DBRetryUtil.waitForNextTry(i, ex);
                } catch (RepositoryException e)
                {
                    _log.error(Thread.currentThread().getStackTrace()[1].getMethodName()
                            + " method of BusinessSystemManager.class RepositoryException:",
                        ex);
                    throw e;
                }
            }
        }
        return map;
    }

    /**   
     * @Title: getSysAdminForBusSysSPDB   
     * @Description:  浦发需求 业务系统维护查询系统管理员列表
     * @param businessSystemBeanForQuery
     * @param type
     * @return
     * @throws RepositoryException      
     * @author: yuhe_zhang 
     * @date:   2019年1月14日 下午2:54:36   
     */
    public Map<String, Object> getSysAdminForBusSysSPDB ( BusinessSystemBeanForQuery businessSystemBeanForQuery,
            int type ) throws RepositoryException
    {
        Map map = new HashMap();
        List res = new ArrayList();
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        for (int i = 0;; i++)
        {
            try
            {
                try
                {
                    int itype = (type == Constants.IEAI_HEALTH_INSPECTION) ? Constants.IEAI_HEALTH_INSPECTION : type;
                    conn = DBResource.getConnection(Thread.currentThread().getStackTrace()[1].getMethodName(), _log,
                        itype);
                    long systemId = businessSystemBeanForQuery.getSysIdForQuery();
                    String sqlWhere = " WHERE T.SYSTEMID =? ";
                    String orderString = " ORDER BY VE.SYSADMIN ASC ";

                    String sqlPageString = "SELECT  DISTINCT  VE.SYSADMIN   AS SYSADMIN FROM  "
                            + " IEAI_COMPUTER_LIST T1 JOIN IEAI_SYS_RELATION T ON T.COMPUTERID = T1.CPID LEFT JOIN IEAI_AGENTINFO T2 ON T1.IP = T2.IAGENT_IP AND T1.IAGENTUP_ID = T2.IAGENTINFO_ID LEFT JOIN VE_WHITELIST_EQUINFO VE ON T1.CPID = VE.CPID ";
                    // 定时任务类型,从agentinfo表出出
                    if (itype == Constants.IEAI_TIMINGTASK)
                    {
                        sqlPageString = "SELECT DISTINCT  VE.SYSADMIN   AS SYSADMIN FROM"
                                + " IEAI_AGENTINFO T2  JOIN IEAI_SYS_RELATION T ON  T.COMPUTERID=T2.IAGENTINFO_ID LEFT JOIN IEAI_COMPUTER_LIST T1 ON T1.IP = T2.IAGENT_IP AND T1.IAGENTUP_ID = T2.IAGENTINFO_ID LEFT JOIN VE_WHITELIST_EQUINFO VE ON T1.CPID = VE.CPID ";
                    }
                    sqlPageString = sqlPageString + sqlWhere + orderString;
                    actStat = conn.prepareStatement(sqlPageString);
                    int index = 0;
                    actStat.setLong(++index, systemId);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        String sysAdmin = actRS.getString("SYSADMIN"); // 该字段为分号分隔的串，要转换成不包含分号的
                        String[] ss = StringUtils.split(sysAdmin, ";");
                        if (!ArrayUtils.isEmpty(ss))
                        {
                            for (String tempString : ss)
                            {
                                Map<String, String> computerMap = new HashMap<String, String>();
                                computerMap.put("sysAdmin", tempString);
                                res.add(computerMap);
                            }
                        }
                    }
                    map.put("dataList", res);
                } catch (SQLException e)
                {
                    _log.error(Thread.currentThread().getStackTrace()[1].getMethodName()
                            + "  method of BusinessSystemManager.class SQLException:",
                        e);
                } finally
                {
                    DBResource.closeConn(conn, actRS, actStat,
                        Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName()
                        + " method of BusinessSystemManager.class RepositoryException:",
                    ex);
                try
                {
                    DBRetryUtil.waitForNextTry(i, ex);
                } catch (RepositoryException e)
                {
                    _log.error(Thread.currentThread().getStackTrace()[1].getMethodName()
                            + " method of BusinessSystemManager.class RepositoryException:",
                        ex);
                    throw e;
                }
            }
        }
        return map;
    }

    /**   
     * @Title: getSystemNameForBusSysSPDB   
     * @Description:  浦发需求 业务系统维护查询信息系统名称列表
     * @param businessSystemBeanForQuery
     * @param type
     * @return
     * @throws RepositoryException      
     * @author: yuhe_zhang 
     * @date:   2019年1月14日 下午2:54:36   
     */
    public Map<String, Object> getSystemNameForBusSysSPDB ( BusinessSystemBeanForQuery businessSystemBeanForQuery,
            int type ) throws RepositoryException
    {
        Map map = new HashMap();
        List res = new ArrayList();
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        for (int i = 0;; i++)
        {
            try
            {
                try
                {
                    int itype = (type == Constants.IEAI_HEALTH_INSPECTION) ? Constants.IEAI_HEALTH_INSPECTION : type;
                    conn = DBResource.getConnection(Thread.currentThread().getStackTrace()[1].getMethodName(), _log,
                        itype);
                    long systemId = businessSystemBeanForQuery.getSysIdForQuery();
                    String sqlWhere = " WHERE T.SYSTEMID =? ";
                    String orderString = " ORDER BY VE.SYSTEMINFO ASC ";

                    String sqlPageString = "SELECT  DISTINCT VE.SYSTEMINFO AS SYSTEMINFO FROM  "
                            + " IEAI_COMPUTER_LIST T1 JOIN IEAI_SYS_RELATION T ON T.COMPUTERID = T1.CPID LEFT JOIN IEAI_AGENTINFO T2 ON T1.IP = T2.IAGENT_IP AND T1.IAGENTUP_ID = T2.IAGENTINFO_ID LEFT JOIN VE_WHITELIST_EQUINFO VE ON T1.CPID = VE.CPID ";
                    // 定时任务类型,从agentinfo表出出
                    if (itype == Constants.IEAI_TIMINGTASK)
                    {
                        sqlPageString = "SELECT DISTINCT VE.SYSTEMINFO AS SYSTEMINFO FROM"
                                + " IEAI_AGENTINFO T2  JOIN IEAI_SYS_RELATION T ON  T.COMPUTERID=T2.IAGENTINFO_ID LEFT JOIN IEAI_COMPUTER_LIST T1 ON T1.IP = T2.IAGENT_IP AND T1.IAGENTUP_ID = T2.IAGENTINFO_ID LEFT JOIN VE_WHITELIST_EQUINFO VE ON T1.CPID = VE.CPID ";
                    }
                    sqlPageString = sqlPageString + sqlWhere + orderString;
                    actStat = conn.prepareStatement(sqlPageString);
                    int index = 0;
                    actStat.setLong(++index, systemId);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        Map computerMap = new HashMap();
                        computerMap.put("systemName", actRS.getString("SYSTEMINFO"));
                        res.add(computerMap);
                    }
                    map.put("dataList", res);
                } catch (SQLException e)
                {
                    _log.error(Thread.currentThread().getStackTrace()[1].getMethodName()
                            + "  method of BusinessSystemManager.class SQLException:",
                        e);
                } finally
                {
                    DBResource.closeConn(conn, actRS, actStat,
                        Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName()
                        + " method of BusinessSystemManager.class RepositoryException:",
                    ex);
                try
                {
                    DBRetryUtil.waitForNextTry(i, ex);
                } catch (RepositoryException e)
                {
                    _log.error(Thread.currentThread().getStackTrace()[1].getMethodName()
                            + " method of BusinessSystemManager.class RepositoryException:",
                        ex);
                    throw e;
                }
            }
        }
        return map;
    }

    public Map<String, Object> getComputerListNoSelectedForSPDB ( BusinessSystemBeanForQuery businessSystemBeanForQuery,
            AgentModel filter, int type ) throws RepositoryException
    {
        Map map = new HashMap();
        List res = new ArrayList();
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        String dateV = null;
        for (int i = 0;; i++)
        {

            try
            {
                try
                {

                    int itype = (type == Constants.IEAI_HEALTH_INSPECTION) ? Constants.IEAI_HEALTH_INSPECTION : type;
                    conn = DBResource.getConnection("getComputerListNoSelectedForSPDB", _log, itype);
                    int count = 0;
                    long systemId = businessSystemBeanForQuery.getSysIdForQuery();
                    String sqlWhere = "WHERE 1=1";
                    String orderString = " ORDER BY T1.IPALIAS ";

                    if (filter.getAgentIp() != null && !"".equals(filter.getAgentIp()))
                    {
                        sqlWhere = sqlWhere + " and t1.IP like '%" + filter.getAgentIp() + "%'";
                    }

                    if (filter.getHostName() != null && !"".equals(filter.getHostName()))
                    {
                        sqlWhere = sqlWhere + " and t2.ICOM_NAME like '%" + filter.getHostName() + "%'";
                    }
                    if (filter.getCtype() != null && !"".equals(filter.getCtype()))
                    {
                        sqlWhere = sqlWhere + " and VE.ICTID =" + filter.getCtype() + "";
                    }
                    if (filter.getCname() != null && !"".equals(filter.getCname()))
                    {
                        sqlWhere = sqlWhere + " and VE.CMID =" + filter.getCname() + "";
                    }

                    if (filter.getSysAdmin() != null && !"".equals(filter.getSysAdmin()))
                    {
                        sqlWhere = sqlWhere + " and VE.SYSADMIN like '%" + filter.getSysAdmin() + "%'";
                    }
                    if (filter.getCenterName() != null && !"".equals(filter.getCenterName()))
                    {
                        sqlWhere = sqlWhere + " and VE.CENTERNAME = '" + filter.getCenterName() + "'";
                    }

                    if (filter.getAppAdmin() != null && !"".equals(filter.getAppAdmin()))
                    {
                        sqlWhere = sqlWhere + " and VE.APPADMIN like '%" + filter.getAppAdmin() + "%'";
                    }
                    if (filter.getSystemInfo() != null && !"".equals(filter.getSystemInfo()))
                    {
                        sqlWhere = sqlWhere + " and VE.SYSTEMINFO like '%" + filter.getSystemInfo() + "%'";
                    }

                    // if (filter.getOsType() != null && !"".equals(filter.getOsType()))
                    // {
                    // sqlWhere = sqlWhere + " and t.IOS_NAME like '%" + filter.getOsType() + "%'";
                    // }
                    // 信息采集过来的
                    // 操作系统版本 VE.OS_VERSION
                    if (filter.getOsVersion() != null && !"".equals(filter.getOsVersion()))
                    {
                        sqlWhere = sqlWhere + " and VE.OS_VERSION like '%" + filter.getOsVersion() + "%'";
                    }

                    // 数据库版本 VE.DB_VERSION
                    if (filter.getDbVersion() != null && !"".equals(filter.getDbVersion()))
                    {
                        sqlWhere = sqlWhere + " and VE.DB_VERSION like '%" + filter.getDbVersion() + "%'";
                    }
                    // 中间件版本 VE.MIDDLEWARE_VERSION
                    if (filter.getMiddlewareVersion() != null && !"".equals(filter.getMiddlewareVersion()))
                    {
                        sqlWhere = sqlWhere + " and VE.MIDDLEWARE_VERSION like '%" + filter.getMiddlewareVersion()
                                + "%'";
                    }

                    // 操作系统类型 VE.OS_TYPE AS OSTYPE
                    if (filter.getOsType() != null && !"".equals(filter.getOsType()))
                    {
                        sqlWhere = sqlWhere + " and VE.OS_TYPE  like'%" + filter.getOsType() + "%'";
                    }
                    // 中间件类型 VE.MIDDLEWARE_TYPE
                    if (filter.getMiddlewareType() != null && !"".equals(filter.getMiddlewareType()))
                    {
                        sqlWhere = sqlWhere + " and VE.MIDDLEWARE_TYPE like '%" + filter.getMiddlewareType() + "%'";
                    }
                    // 数据库类型 VE.db_type
                    if (filter.getDbType() != null && !"".equals(filter.getDbType()))
                    {
                        sqlWhere = sqlWhere + " and VE.db_type  like '%" + filter.getDbType() + "%'";
                    }

                    // Oracle db2
                    if (DBManager.Orcl_Faimily() || JudgeDB.IEAI_DB_TYPE == 2)
                    {
                        dateV = " FUN_GET_DATE_STRING(t2.Icreatetime,8,'YYYY-MM-DD') as ICREATETIME, ";
                    } else
                    {// mysql
                        dateV = " FROM_UNIXTIME(t2.icreatetime/1000,'%Y-%m-%d') as ICREATETIME, ";
                    }

                    String sqlPageString = "SELECT * FROM (SELECT  ROW_NUMBER() OVER (ORDER BY T1.CPID DESC) AS ROW_NUMBER,T1.CPID, T1.CPNAME,T1.IP ,  t2.ICOM_NAME  as HOSTNAME,T2.ISTART_USER,"
                            + dateV
                            + "VE.SYSADMIN AS SYSADMIN, VE.APPADMIN AS APPADMIN, VE.SYSTEMINFO AS SYSTEMINFO, VE.CENTERNAME, VE.OS_TYPE AS OSTYPE ,  VE.OS_VERSION, VE.RUN_DAYS, VE.IS_HA, VE.DB_VERSION, VE.DB_TYPE,VE.MIDDLEWARE_VERSION,VE.MIDDLEWARE_TYPE FROM IEAI_COMPUTER_LIST T1  LEFT JOIN IEAI_AGENTINFO T2 ON T1.IP = T2.IAGENT_IP AND T1.IAGENTUP_ID = T2.IAGENTINFO_ID LEFT JOIN VE_WHITELIST_EQUINFO VE ON T1.CPID = VE.CPID ";
                    String sqlPageStringEnd = ") A WHERE A.ROW_NUMBER  <=? AND A.ROW_NUMBER >  ?  ";
                    String sqlCount = "SELECT COUNT(1) AS NUM FROM  IEAI_COMPUTER_LIST T1 LEFT JOIN IEAI_AGENTINFO T2 ON T1.IP = T2.IAGENT_IP AND T1.IAGENTUP_ID = T2.IAGENTINFO_ID LEFT JOIN VE_WHITELIST_EQUINFO VE ON T1.CPID = VE.CPID ";
                    // 定时任务类型,从agentinfo表出出
                    if (itype == Constants.IEAI_TIMINGTASK)
                    {
                        sqlPageString = "SELECT * FROM (SELECT ROW_NUMBER() OVER (ORDER BY T2.IAGENTINFO_ID DESC) AS ROW_NUMBER,T2.IAGENTINFO_ID AS CPID, T2.IAGENT_IP AS IP,T2.ICOM_NAME as HOSTNAME,T2.ISTART_USER,"
                                + dateV
                                + "VE.SYSADMIN AS SYSADMIN, VE.APPADMIN AS APPADMIN, VE.SYSTEMINFO AS SYSTEMINFO, VE.CENTERNAME, VE.OS_TYPE AS OSTYPE ,  VE.OS_VERSION, VE.RUN_DAYS, VE.IS_HA, VE.DB_VERSION, VE.DB_TYPE,VE.MIDDLEWARE_VERSION,VE.MIDDLEWARE_TYPE  FROM IEAI_AGENTINFO T2  LEFT JOIN IEAI_COMPUTER_LIST T1 ON T1.IP = T2.IAGENT_IP AND T1.IAGENTUP_ID = T2.IAGENTINFO_ID LEFT JOIN VE_WHITELIST_EQUINFO VE ON T1.CPID = VE.CPID ";
                        sqlCount = "SELECT COUNT(1) AS NUM FROM  IEAI_AGENTINFO T2  LEFT JOIN IEAI_COMPUTER_LIST T1 ON T1.IP = T2.IAGENT_IP AND T1.IAGENTUP_ID = T2.IAGENTINFO_ID LEFT JOIN VE_WHITELIST_EQUINFO VE ON T1.CPID = VE.CPID ";
                    }
                    // 支持mysql
                    if (JudgeDB.MYSQL == JudgeDB.IEAI_DB_TYPE)
                    {
                        sqlPageString = " SELECT DISTINCT T1.CPID, T1.CPNAME,T1.IP ,t2.ICOM_NAME as HOSTNAME,T2.ISTART_USER,"
                                + dateV
                                + "VE.SYSADMIN AS SYSADMIN, VE.APPADMIN AS APPADMIN, VE.SYSTEMINFO AS SYSTEMINFO, VE.CENTERNAME, VE.OS_TYPE AS OSTYPE ,  VE.OS_VERSION, VE.RUN_DAYS, VE.IS_HA, VE.DB_VERSION,VE.DB_TYPE, VE.MIDDLEWARE_VERSION,VE.MIDDLEWARE_TYPE FROM IEAI_COMPUTER_LIST T1  LEFT JOIN IEAI_AGENTINFO T2 ON T1.IP = T2.IAGENT_IP AND T1.IAGENTUP_ID = T2.IAGENTINFO_ID LEFT JOIN VE_WHITELIST_EQUINFO VE ON T1.CPID = VE.CPID ";
                        sqlPageStringEnd = " limit ?,? ";
                        sqlCount = "SELECT COUNT(1) AS NUM FROM  IEAI_COMPUTER_LIST T1 LEFT JOIN IEAI_AGENTINFO T2 ON T1.IP = T2.IAGENT_IP AND T1.IAGENTUP_ID = T2.IAGENTINFO_ID LEFT JOIN VE_WHITELIST_EQUINFO VE ON T1.CPID = VE.CPID ";
                        // 定时任务类型，从agentinfo表出
                        if (itype == Constants.IEAI_TIMINGTASK)
                        {
                            sqlPageString = "SELECT T2.IAGENTINFO_ID AS CPID, T2.IAGENT_IP AS IP,T2.ICOM_NAME as HOSTNAME,T2.ISTART_USER, "
                                    + dateV
                                    + "VE.SYSADMIN AS SYSADMIN, VE.APPADMIN AS APPADMIN, VE.SYSTEMINFO AS SYSTEMINFO, VE.CENTERNAME, VE.OS_TYPE AS OSTYPE ,  VE.OS_VERSION, VE.RUN_DAYS, VE.IS_HA, VE.DB_VERSION,VE.DB_TYPE, VE.MIDDLEWARE_VERSION,VE.MIDDLEWARE_TYPE  FROM IEAI_AGENTINFO T2 LEFT JOIN IEAI_COMPUTER_LIST T1 ON T1.IP = T2.IAGENT_IP AND T1.IAGENTUP_ID = T2.IAGENTINFO_ID LEFT JOIN VE_WHITELIST_EQUINFO VE ON T1.CPID = VE.CPID ";
                            sqlCount = "SELECT COUNT(1) AS NUM FROM  IEAI_AGENTINFO T2 LEFT JOIN IEAI_COMPUTER_LIST T1 ON T1.IP = T2.IAGENT_IP AND T1.IAGENTUP_ID = T2.IAGENTINFO_ID LEFT JOIN VE_WHITELIST_EQUINFO VE ON T1.CPID = VE.CPID ";
                        }
                    }

                    if (itype == Constants.IEAI_HEALTH_INSPECTION || itype == Constants.IEAI_SUS
                            || itype == Constants.IEAI_APM)
                    {
                        sqlWhere = sqlWhere
                                + " AND  NOT EXISTS (SELECT 1 FROM IEAI_SYS_RELATION T3 WHERE  T3.SYSTEMID=? AND T1.CPID=T3.COMPUTERID)";
                    } else if (itype == Constants.IEAI_TIMINGTASK)
                    {
                        sqlWhere = sqlWhere
                                + " AND NOT EXISTS  (SELECT 1 FROM IEAI_SYS_RELATION T3 WHERE T3.SYSTEMID=? AND T2.IAGENTINFO_ID = T3.COMPUTERID)";
                    }

                    sqlPageString = sqlPageString + sqlWhere + orderString + sqlPageStringEnd;
                    sqlCount = sqlCount + sqlWhere;

                    actStat = conn.prepareStatement(sqlPageString);
                    int index = 0;
                    actStat.setLong(++index, systemId);
                    if (3 == JudgeDB.IEAI_DB_TYPE)// mysql
                    {
                        actStat.setInt(++index, businessSystemBeanForQuery.getStart());
                        actStat.setInt(++index, businessSystemBeanForQuery.getLimit());
                    } else
                    {
                        actStat.setInt(++index,
                            businessSystemBeanForQuery.getStart() + businessSystemBeanForQuery.getLimit());
                        actStat.setInt(++index, businessSystemBeanForQuery.getStart());
                    }
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        Map computerMap = new HashMap();

                        if (itype == Constants.IEAI_TIMINGTASK)
                        {
                            computerMap.put("cpId", actRS.getLong("CPID"));
                            computerMap.put("agentIp", actRS.getString("IP"));

                            computerMap.put("osVersion", actRS.getString("OS_VERSION")); // 操作系统版本
                            computerMap.put("runDays", actRS.getString("RUN_DAYS"));  // 运行天数
                            computerMap.put("isHa", actRS.getString("IS_HA")); // 是否双机
                            computerMap.put("dbVersion", actRS.getString("DB_VERSION")); // 数据库版本
                            computerMap.put("dbType", actRS.getString("DB_TYPE"));
                            computerMap.put("middlewareVersion", actRS.getString("MIDDLEWARE_VERSION"));// 中间件版本
                            computerMap.put("icreateTime", actRS.getString("ICREATETIME")); // 纳管时间
                            // computerMap.put("sysName", actRS.getString("SYSNAME"));
                            // computerMap.put("appName", actRS.getString("APPNAME"));
                            computerMap.put("hostName", actRS.getString("HOSTNAME"));
                            computerMap.put("osType", actRS.getString("OSTYPE")); // 系统类型 信息采集来的
                            computerMap.put("startUser", actRS.getString("ISTART_USER")); // 纳管 启动人
                            computerMap.put("centerName", actRS.getString("CENTERNAME"));
                            computerMap.put("sysAdmin",
                                actRS.getString("SYSADMIN") == null ? "" : actRS.getString("SYSADMIN"));
                            computerMap.put("appAdmin",
                                actRS.getString("APPADMIN") == null ? "" : actRS.getString("APPADMIN"));
                            computerMap.put("systemInfo",
                                actRS.getString("SYSTEMINFO") == null ? "" : actRS.getString("SYSTEMINFO"));
                            computerMap.put("middlewareType",
                                actRS.getString("MIDDLEWARE_TYPE") == null ? "" : actRS.getString("MIDDLEWARE_TYPE")); // 中间件类型
                        } else if (itype == Constants.IEAI_HEALTH_INSPECTION || itype == Constants.IEAI_SUS
                                || itype == Constants.IEAI_APM)
                        {
                            computerMap.put("cpName", actRS.getString("CPNAME"));
                            computerMap.put("cpId", actRS.getLong("CPID"));
                            computerMap.put("agentIp", actRS.getString("IP"));

                            computerMap.put("osVersion", actRS.getString("OS_VERSION")); // 操作系统版本
                            computerMap.put("runDays", actRS.getString("RUN_DAYS"));  // 运行天数
                            computerMap.put("isHa", actRS.getString("IS_HA")); // 是否双机
                            computerMap.put("dbVersion", actRS.getString("DB_VERSION")); // 数据库版本
                            computerMap.put("dbType", actRS.getString("DB_TYPE"));
                            computerMap.put("middlewareVersion", actRS.getString("MIDDLEWARE_VERSION"));// 中间件版本
                            computerMap.put("icreateTime", actRS.getString("ICREATETIME")); // 纳管时间
                            // computerMap.put("sysName", actRS.getString("SYSNAME"));
                            // computerMap.put("appName", actRS.getString("APPNAME"));
                            computerMap.put("hostName", actRS.getString("HOSTNAME"));
                            computerMap.put("osType", actRS.getString("OSTYPE")); // 系统类型 信息采集来的
                            computerMap.put("startUser", actRS.getString("ISTART_USER")); // 纳管 启动人
                            computerMap.put("centerName", actRS.getString("CENTERNAME"));
                            computerMap.put("sysAdmin",
                                actRS.getString("SYSADMIN") == null ? "" : actRS.getString("SYSADMIN"));
                            computerMap.put("appAdmin",
                                actRS.getString("APPADMIN") == null ? "" : actRS.getString("APPADMIN"));
                            computerMap.put("systemInfo",
                                actRS.getString("SYSTEMINFO") == null ? "" : actRS.getString("SYSTEMINFO"));
                            computerMap.put("middlewareType",
                                actRS.getString("MIDDLEWARE_TYPE") == null ? "" : actRS.getString("MIDDLEWARE_TYPE")); // 中间件类型
                        }

                        res.add(computerMap);
                    }
                    actStat = conn.prepareStatement(sqlCount);
                    index = 0;
                    actStat.setLong(++index, systemId);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        count = actRS.getInt("NUM");
                    }
                    map.put("total", count);
                    map.put("dataList", res);

                } catch (SQLException e)
                {
                    _log.error("getComputerListNoSelectedForSPDB method of BusinessSystemManager.class SQLException:",
                        e);
                } finally
                {
                    DBResource.closeConn(conn, actRS, actStat, "getComputerListNoSelectedForSPDB", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log.error(
                    "getComputerListNoSelectedForSPDB method of BusinessSystemManager.class RepositoryException:", ex);
                try
                {
                    DBRetryUtil.waitForNextTry(i, ex);
                } catch (RepositoryException e)
                {
                    _log.error(
                        "getComputerListNoSelectedForSPDB method of BusinessSystemManager.class RepositoryException:",
                        ex);
                    throw e;
                }
            }

        }
        return map;
    }

    /**   
    * @Title: getHostNameForBusSysNoSelectedSPDB   
    * @Description:  浦发需求 业务系统维护查询主机名列表
    * @param businessSystemBeanForQuery
    * @param type
    * @return
    * @throws RepositoryException      
    * @author: yuhe_zhang 
    * @date:   2019年1月14日 下午2:54:36   
    */
    public Map<String, Object> getHostNameForBusSysNoSelectedSPDB (
            BusinessSystemBeanForQuery businessSystemBeanForQuery, int type ) throws RepositoryException
    {
        Map map = new HashMap();
        List res = new ArrayList();
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        for (int i = 0;; i++)
        {
            try
            {
                try
                {
                    int itype = (type == Constants.IEAI_HEALTH_INSPECTION) ? Constants.IEAI_HEALTH_INSPECTION : type;
                    conn = DBResource.getConnection(Thread.currentThread().getStackTrace()[1].getMethodName(), _log,
                        itype);
                    long systemId = businessSystemBeanForQuery.getSysIdForQuery();
                    String sqlWhere = " WHERE 1=1 ";
                    String orderString = " ORDER BY t2.ICOM_NAME ";

                    String sqlPageString = "SELECT DISTINCT t2.ICOM_NAME AS HOSTNAME FROM "
                            + " IEAI_COMPUTER_LIST T1 LEFT JOIN IEAI_AGENTINFO T2 ON T1.IP = T2.IAGENT_IP AND T1.IAGENTUP_ID = T2.IAGENTINFO_ID LEFT JOIN VE_WHITELIST_EQUINFO VE   ON T1.CPID = VE.CPID ";
                    // 定时任务类型,从agentinfo表出出
                    if (itype == Constants.IEAI_TIMINGTASK)
                    {
                        sqlPageString = "SELECT DISTINCT t2.ICOM_NAME AS HOSTNAME FROM"
                                + " IEAI_AGENTINFO T2 LEFT JOIN IEAI_COMPUTER_LIST T1 ON T1.IP = T2.IAGENT_IP AND T1.IAGENTUP_ID = T2.IAGENTINFO_ID LEFT JOIN VE_WHITELIST_EQUINFO VE ON T1.CPID = VE.CPID ";
                    }

                    if (itype == Constants.IEAI_HEALTH_INSPECTION || itype == Constants.IEAI_SUS
                            || itype == Constants.IEAI_APM)
                    {
                        sqlWhere = sqlWhere
                                + "AND  NOT EXISTS (SELECT 1 FROM IEAI_SYS_RELATION T3 WHERE  T3.SYSTEMID=? AND T1.CPID=T3.COMPUTERID)";
                    } else if (itype == Constants.IEAI_TIMINGTASK)
                    {
                        sqlWhere = sqlWhere
                                + "AND NOT EXISTS  (SELECT 1 FROM IEAI_SYS_RELATION T3 WHERE T3.SYSTEMID=? AND T2.IAGENTINFO_ID = T3.COMPUTERID)";
                    }

                    sqlPageString = sqlPageString + sqlWhere + orderString;
                    actStat = conn.prepareStatement(sqlPageString);
                    int index = 0;
                    actStat.setLong(++index, systemId);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        Map computerMap = new HashMap();
                        computerMap.put("hostName", actRS.getString("HOSTNAME"));
                        res.add(computerMap);
                    }
                    map.put("dataList", res);
                } catch (SQLException e)
                {
                    _log.error(Thread.currentThread().getStackTrace()[1].getMethodName()
                            + "  method of BusinessSystemManager.class SQLException:",
                        e);
                } finally
                {
                    DBResource.closeConn(conn, actRS, actStat,
                        Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName()
                        + " method of BusinessSystemManager.class RepositoryException:",
                    ex);
                try
                {
                    DBRetryUtil.waitForNextTry(i, ex);
                } catch (RepositoryException e)
                {
                    _log.error(Thread.currentThread().getStackTrace()[1].getMethodName()
                            + " method of BusinessSystemManager.class RepositoryException:",
                        ex);
                    throw e;
                }
            }
        }
        return map;
    }

    /**   
    * @Title: getIpForBusSysNoSelectedSPDB   
    * @Description:  浦发需求 业务系统维护查询IP列表
    * @param businessSystemBeanForQuery
    * @param type
    * @return
    * @throws RepositoryException      
    * @author: yuhe_zhang 
    * @date:   2019年1月14日 下午2:54:36   
    */
    public Map<String, Object> getIpForBusSysNoSelectedSPDB ( BusinessSystemBeanForQuery businessSystemBeanForQuery,
            int type ) throws RepositoryException
    {
        Map map = new HashMap();
        List res = new ArrayList();
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        for (int i = 0;; i++)
        {
            try
            {
                try
                {
                    int itype = (type == Constants.IEAI_HEALTH_INSPECTION) ? Constants.IEAI_HEALTH_INSPECTION : type;
                    conn = DBResource.getConnection(Thread.currentThread().getStackTrace()[1].getMethodName(), _log,
                        itype);
                    long systemId = businessSystemBeanForQuery.getSysIdForQuery();
                    String sqlWhere = " WHERE 1=1 ";
                    // String orderString = " ORDER BY T1.IP ASC ";

                    String sqlPageString = "SELECT DISTINCT T1.IP FROM "
                            + " IEAI_COMPUTER_LIST T1 LEFT JOIN IEAI_AGENTINFO T2 ON T1.IP = T2.IAGENT_IP AND T1.IAGENTUP_ID = T2.IAGENTINFO_ID LEFT JOIN VE_WHITELIST_EQUINFO VE ON T1.CPID = VE.CPID ";
                    // 定时任务类型,从agentinfo表出出
                    if (itype == Constants.IEAI_TIMINGTASK)
                    {
                        sqlPageString = "SELECT DISTINCT T2.IAGENT_IP AS IP FROM"
                                + " IEAI_AGENTINFO T2 LEFT JOIN IEAI_COMPUTER_LIST T1 ON T1.IP = T2.IAGENT_IP AND T1.IAGENTUP_ID = T2.IAGENTINFO_ID LEFT JOIN VE_WHITELIST_EQUINFO VE ON T1.CPID = VE.CPID ";
                    }

                    if (itype == Constants.IEAI_HEALTH_INSPECTION || itype == Constants.IEAI_SUS
                            || itype == Constants.IEAI_APM)
                    {
                        sqlWhere = sqlWhere
                                + "AND  NOT EXISTS (SELECT 1 FROM IEAI_SYS_RELATION T3 WHERE  T3.SYSTEMID=? AND T1.CPID=T3.COMPUTERID)";
                    } else if (itype == Constants.IEAI_TIMINGTASK)
                    {
                        sqlWhere = sqlWhere
                                + "AND NOT EXISTS  (SELECT 1 FROM IEAI_SYS_RELATION T3 WHERE T3.SYSTEMID=? AND T2.IAGENTINFO_ID = T3.COMPUTERID)";
                    }

                    sqlPageString = sqlPageString + sqlWhere;
                    actStat = conn.prepareStatement(sqlPageString);
                    int index = 0;
                    actStat.setLong(++index, systemId);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        Map computerMap = new HashMap();
                        computerMap.put("agentIp", actRS.getString("IP"));
                        res.add(computerMap);
                    }
                    map.put("dataList", res);
                } catch (SQLException e)
                {
                    _log.error(Thread.currentThread().getStackTrace()[1].getMethodName()
                            + "  method of BusinessSystemManager.class SQLException:",
                        e);
                } finally
                {
                    DBResource.closeConn(conn, actRS, actStat,
                        Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName()
                        + " method of BusinessSystemManager.class RepositoryException:",
                    ex);
                try
                {
                    DBRetryUtil.waitForNextTry(i, ex);
                } catch (RepositoryException e)
                {
                    _log.error(Thread.currentThread().getStackTrace()[1].getMethodName()
                            + " method of BusinessSystemManager.class RepositoryException:",
                        ex);
                    throw e;
                }
            }
        }
        return map;
    }

    /**   
    * @Title: getSysAdminForBusSysNoSelectedSPDB   
    * @Description:  浦发需求 业务系统维护查询系统管理员列表
    * @param businessSystemBeanForQuery
    * @param type
    * @return
    * @throws RepositoryException      
    * @author: yuhe_zhang 
    * @date:   2019年1月14日 下午2:54:36   
    */
    public Map<String, Object> getSysAdminForBusSysNoSelectedSPDB (
            BusinessSystemBeanForQuery businessSystemBeanForQuery, int type ) throws RepositoryException
    {
        Map map = new HashMap();
        List res = new ArrayList();
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        for (int i = 0;; i++)
        {
            try
            {
                try
                {
                    int itype = (type == Constants.IEAI_HEALTH_INSPECTION) ? Constants.IEAI_HEALTH_INSPECTION : type;
                    conn = DBResource.getConnection(Thread.currentThread().getStackTrace()[1].getMethodName(), _log,
                        itype);
                    long systemId = businessSystemBeanForQuery.getSysIdForQuery();
                    String sqlWhere = " WHERE 1=1 ";
                    String orderString = " ORDER BY VE.SYSADMIN ASC";

                    String sqlPageString = "SELECT DISTINCT VE.SYSADMIN AS SYSADMIN FROM "
                            + " IEAI_COMPUTER_LIST T1 LEFT JOIN IEAI_AGENTINFO T2 ON T1.IP = T2.IAGENT_IP AND T1.IAGENTUP_ID = T2.IAGENTINFO_ID LEFT JOIN VE_WHITELIST_EQUINFO VE   ON T1.CPID = VE.CPID ";
                    // 定时任务类型,从agentinfo表出出
                    if (itype == Constants.IEAI_TIMINGTASK)
                    {
                        sqlPageString = "SELECT DISTINCT VE.SYSADMIN AS SYSADMIN FROM"
                                + " IEAI_AGENTINFO T2 LEFT JOIN IEAI_COMPUTER_LIST T1 ON T1.IP = T2.IAGENT_IP AND T1.IAGENTUP_ID = T2.IAGENTINFO_ID LEFT JOIN VE_WHITELIST_EQUINFO VE ON T1.CPID = VE.CPID ";
                    }

                    if (itype == Constants.IEAI_HEALTH_INSPECTION || itype == Constants.IEAI_SUS
                            || itype == Constants.IEAI_APM)
                    {
                        sqlWhere = sqlWhere
                                + "AND  NOT EXISTS (SELECT 1 FROM IEAI_SYS_RELATION T3 WHERE  T3.SYSTEMID=? AND T1.CPID=T3.COMPUTERID)";
                    } else if (itype == Constants.IEAI_TIMINGTASK)
                    {
                        sqlWhere = sqlWhere
                                + "AND NOT EXISTS  (SELECT 1 FROM IEAI_SYS_RELATION T3 WHERE T3.SYSTEMID=? AND T2.IAGENTINFO_ID = T3.COMPUTERID)";
                    }

                    sqlPageString = sqlPageString + sqlWhere + orderString;
                    actStat = conn.prepareStatement(sqlPageString);
                    int index = 0;
                    actStat.setLong(++index, systemId);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        String sysAdmin = actRS.getString("SYSADMIN"); // 该字段为分号分隔的串，要转换成不包含分号的
                        String[] ss = StringUtils.split(sysAdmin, ";");
                        if (!ArrayUtils.isEmpty(ss))
                        {
                            for (String tempString : ss)
                            {
                                Map<String, String> computerMap = new HashMap<String, String>();
                                computerMap.put("sysAdmin", tempString);
                                res.add(computerMap);
                            }
                        }
                    }
                    map.put("dataList", res);
                } catch (SQLException e)
                {
                    _log.error(Thread.currentThread().getStackTrace()[1].getMethodName()
                            + "  method of BusinessSystemManager.class SQLException:",
                        e);
                } finally
                {
                    DBResource.closeConn(conn, actRS, actStat,
                        Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName()
                        + " method of BusinessSystemManager.class RepositoryException:",
                    ex);
                try
                {
                    DBRetryUtil.waitForNextTry(i, ex);
                } catch (RepositoryException e)
                {
                    _log.error(Thread.currentThread().getStackTrace()[1].getMethodName()
                            + " method of BusinessSystemManager.class RepositoryException:",
                        ex);
                    throw e;
                }
            }
        }
        return map;
    }

    /**   
    * @Title: getSystemNameForBusSysNoSelectedSPDB   
    * @Description:  浦发需求 业务系统维护查询信息系统名称列表
    * @param businessSystemBeanForQuery
    * @param type
    * @return
    * @throws RepositoryException      
    * @author: yuhe_zhang 
    * @date:   2019年1月14日 下午2:54:36   
    */
    public Map<String, Object> getSystemNameForBusSysNoSelectedSPDB (
            BusinessSystemBeanForQuery businessSystemBeanForQuery, int type ) throws RepositoryException
    {
        Map map = new HashMap();
        List res = new ArrayList();
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        for (int i = 0;; i++)
        {
            try
            {
                try
                {
                    int itype = (type == Constants.IEAI_HEALTH_INSPECTION) ? Constants.IEAI_HEALTH_INSPECTION : type;
                    conn = DBResource.getConnection(Thread.currentThread().getStackTrace()[1].getMethodName(), _log,
                        itype);
                    long systemId = businessSystemBeanForQuery.getSysIdForQuery();
                    String sqlWhere = " WHERE 1=1 ";
                    String orderString = " ORDER BY VE.SYSTEMINFO ";

                    String sqlPageString = "SELECT DISTINCT VE.SYSTEMINFO AS SYSTEMINFO FROM "
                            + " IEAI_COMPUTER_LIST T1 LEFT JOIN IEAI_AGENTINFO T2 ON T1.IP = T2.IAGENT_IP AND T1.IAGENTUP_ID = T2.IAGENTINFO_ID LEFT JOIN VE_WHITELIST_EQUINFO VE   ON T1.CPID = VE.CPID ";
                    // 定时任务类型,从agentinfo表出出
                    if (itype == Constants.IEAI_TIMINGTASK)
                    {
                        sqlPageString = "SELECT DISTINCT VE.SYSTEMINFO AS SYSTEMINFO FROM"
                                + " IEAI_AGENTINFO T2 LEFT JOIN IEAI_COMPUTER_LIST T1 ON T1.IP = T2.IAGENT_IP AND T1.IAGENTUP_ID = T2.IAGENTINFO_ID LEFT JOIN VE_WHITELIST_EQUINFO VE ON T1.CPID = VE.CPID ";
                    }

                    if (itype == Constants.IEAI_HEALTH_INSPECTION || itype == Constants.IEAI_SUS
                            || itype == Constants.IEAI_APM)
                    {
                        sqlWhere = sqlWhere
                                + "AND NOT EXISTS (SELECT 1 FROM IEAI_SYS_RELATION T3 WHERE  T3.SYSTEMID=? AND T1.CPID=T3.COMPUTERID)";
                    } else if (itype == Constants.IEAI_TIMINGTASK)
                    {
                        sqlWhere = sqlWhere
                                + "AND NOT EXISTS  (SELECT 1 FROM IEAI_SYS_RELATION T3 WHERE T3.SYSTEMID=? AND T2.IAGENTINFO_ID = T3.COMPUTERID)";
                    }

                    sqlPageString = sqlPageString + sqlWhere + orderString;
                    actStat = conn.prepareStatement(sqlPageString);
                    int index = 0;
                    actStat.setLong(++index, systemId);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        Map computerMap = new HashMap();
                        computerMap.put("systemName", actRS.getString("SYSTEMINFO"));
                        res.add(computerMap);
                    }
                    map.put("dataList", res);
                } catch (SQLException e)
                {
                    _log.error(Thread.currentThread().getStackTrace()[1].getMethodName()
                            + "  method of BusinessSystemManager.class SQLException:",
                        e);
                } finally
                {
                    DBResource.closeConn(conn, actRS, actStat,
                        Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName()
                        + " method of BusinessSystemManager.class RepositoryException:",
                    ex);
                try
                {
                    DBRetryUtil.waitForNextTry(i, ex);
                } catch (RepositoryException e)
                {
                    _log.error(Thread.currentThread().getStackTrace()[1].getMethodName()
                            + " method of BusinessSystemManager.class RepositoryException:",
                        ex);
                    throw e;
                }
            }
        }
        return map;
    }

    /**
     * 
     * @Title: getSysConfigUser   
     * @Description: 通过业务系统id获取是否绑定用户  
     * @param sysId
     * @param type
     * @return
     * @throws RepositoryException      
     * @author: Administrator 
     * @date:   2019年3月13日 下午4:22:26
     */
    public List getSysConfigUser ( String sysId, int type ) throws RepositoryException
    {
        List list = new ArrayList();
        String methodName = Thread.currentThread().getStackTrace()[1].getMethodName();
        Map map = new HashMap();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        String sql = " SELECT DISTINCT u.iid FROM  ieai_user u ,ieai_sys_permission p,ieai_userinherit h  WHERE u.iid=h.iuserid and p.iroleid=h.iroleid AND p.IPERMISSION=1 AND p.iproid=?";
        for (int i = 0; i < 10; i++)
        {
            try
            {
                conn = DBResource.getConnection(methodName, _log, type);
                ps = conn.prepareStatement(sql);
                ps.setLong(1, Long.parseLong(sysId));
                rs = ps.executeQuery();
                while (rs.next())
                {
                    list.add(rs.getLong("iid"));
                }
                break;
            } catch (Exception ex)
            {
                _log.error(ex.getMessage(), ex);
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            } finally
            {
                DBResource.closeConn(conn, rs, ps, methodName, _log);
            }
        }

        return list;
    }

    /**
     * 
     * @Title: getSysConfigGUser   
     * @Description: 通过业务系统id获取是否绑定用户组
     * @param sysId
     * @param type
     * @return
     * @throws RepositoryException      
     * @author: Administrator 
     * @date:   2019年3月13日 下午4:21:59
     */
    public List getSysConfigGUser ( String sysId, int type ) throws RepositoryException
    {
        List list = new ArrayList();
        String methodName = Thread.currentThread().getStackTrace()[1].getMethodName();
        Map map = new HashMap();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        String sql = " SELECT DISTINCT g.igroupid FROM  ieai_user u ,ieai_sys_permission p,ieai_userinherit h,ieai_user_group_relation g  WHERE u.iid=h.iuserid AND  p.iroleid=h.iroleid AND g.iuserid=u.iid AND p.IPERMISSION=1 AND p.iproid=?";
        for (int i = 0; i < 10; i++)
        {
            try
            {
                conn = DBResource.getConnection(methodName, _log, type);
                ps = conn.prepareStatement(sql);
                ps.setLong(1, Long.parseLong(sysId));
                rs = ps.executeQuery();
                while (rs.next())
                {
                    list.add(rs.getLong("igroupid"));
                }
                break;
            } catch (Exception ex)
            {
                _log.error(ex.getMessage(), ex);
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            } finally
            {
                DBResource.closeConn(conn, rs, ps, methodName, _log);
            }
        }

        return list;
    }

    private BusinessSystemBean getBusinessSystemForBack ( Long systemid, Connection con ) throws RepositoryException
    {
        List<SysRelationBean> list = new ArrayList<SysRelationBean>();
        BusinessSystemBean bean = null;
        String sql = " SELECT * FROM IEAI_PROJECT where iid =?  ";

        PreparedStatement actStat = null;
        ResultSet actRS = null;
        try
        {
            actStat = con.prepareStatement(sql);
            actStat.setLong(1, systemid);
            actRS = actStat.executeQuery();
            while (actRS.next())
            {
                bean = new BusinessSystemBean();

                bean.setSystemId(actRS.getLong("IID"));
                bean.setSysName(actRS.getString("INAME"));
                bean.setSystemCode(actRS.getString("ISYSTEMCODE"));

                bean.setIcansync(actRS.getInt("ICANSYNC"));
            }

        } catch (SQLException e)
        {
            _log.error("getRelationListForBack is error ! " + e.getMessage());
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(actRS, actStat, "getRelationListForBack", _log);
        }
        return bean;
    }

    public BusinessSystemBean getProinfoOne ( long sysid, int type ) throws RepositoryException
    {
        BusinessSystemBean bean = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        Connection conn = null;
        try
        {
            conn = DBResource.getConnection("getProinfoOne", _log, type);
            String sql = "select * from IEAI_PROJECT_INFO where IID=?";
            ps = conn.prepareStatement(sql);
            ps.setLong(1, sysid);
            rs = ps.executeQuery();
            while (rs.next())
            {
                bean = new BusinessSystemBean();
                bean.setPriority(rs.getInt("PRIORITY"));
            }
        } catch (Exception e)
        {
            _log.error("BusinessSystemManager.getProinfoOne is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closeConn(conn, rs, ps, "getProinfoOne", _log);
        }
        return bean;
    }

    public BusinessSystemBean getProinfoOne ( long sysid, Connection conn ) throws RepositoryException
    {
        BusinessSystemBean bean = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            String sql = "select * from IEAI_PROJECT where IID=?";
            ps = conn.prepareStatement(sql);
            ps.setLong(1, sysid);
            rs = ps.executeQuery();
            while (rs.next())
            {
                bean = new BusinessSystemBean();
                bean.setSystemId(rs.getLong("IID"));
                bean.setSysName(rs.getString("INAME"));

                bean.setIcansync(rs.getInt("ICANSYNC"));
            }
        } catch (Exception e)
        {
            _log.error("BusinessSystemManager.getProinfoOne is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getProinfoOne", _log);
        }
        return bean;
    }

    /**
     * 
     * <li>Description:获取绑定IPMP业务系统名称</li> 
     * <AUTHOR>
     * 2021年3月2日 
     * @param systemid
     * @param con
     * @return
     * @throws RepositoryException
     * return String
     */
    public Map<String, String> getIpmpSysName ( Long systemid, Connection con ) throws RepositoryException
    {
        Map<String, String> map = new HashMap();
        String sql = " SELECT IPMPSYSNAME,ISYSCODE FROM IEAI_IPMP_SYSTEM_RELATON WHERE IPROJECTID = ? ";
        String ipmpSysName = "";
        String ipmpSysCode = "";
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        try
        {
            actStat = con.prepareStatement(sql);
            actStat.setLong(1, systemid);
            actRS = actStat.executeQuery();
            while (actRS.next())
            {
                ipmpSysName = actRS.getString("IPMPSYSNAME");
                ipmpSysCode = actRS.getString("ISYSCODE");
            }

            map.put("ipmpSysName", ipmpSysName);
            map.put("ipmpSysCode", ipmpSysCode);
        } catch (SQLException e)
        {
            _log.error("getIpmpSysName is error ! " + e.getMessage());
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(actRS, actStat, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }
        return map;
    }

    /**
     * 
     * <li>Description:获取绑定IPMP系统信息，回退多写</li> 
     * <AUTHOR>
     * 2021年3月4日 
     * @param sysid
     * @param conn
     * @return
     * @throws RepositoryException
     * return BusinessSystemBean
     */
    public BusinessSystemBean getIpmpProinfoOne ( long sysid, Connection conn ) throws RepositoryException
    {
        BusinessSystemBean bean = new BusinessSystemBean();
        ;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            String sql = " select IPMPSYSNAME from IEAI_IPMP_SYSTEM_RELATON where IPROJECTID = ? ";
            ps = conn.prepareStatement(sql);
            ps.setLong(1, sysid);
            rs = ps.executeQuery();
            while (rs.next())
            {
                bean.setSystemId(sysid);
                bean.setIpmpSysName(rs.getString("IPMPSYSNAME"));
            }
        } catch (Exception e)
        {
            _log.error("BusinessSystemManager.getIpmpProinfoOne is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }
        return bean;
    }

    public List getSysConfigUserForUser ( String sysId, int type ) throws RepositoryException
    {
        List list = new ArrayList();
        String methodName = Thread.currentThread().getStackTrace()[1].getMethodName();
        Map map = new HashMap();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        String sql = " SELECT DISTINCT u.iid FROM  ieai_user u ,ieai_sys_permission p,ieai_userinherit h  WHERE u.iid=h.iuserid and p.iroleid=h.iroleid  and p.iroleid in (select r.iid from  ieai_role r where r.iname = to_char(u.iid) and r.ishidden=1) AND p.IPERMISSION=1 AND p.iproid=?";
        try
        {
            conn = DBResource.getConnection(methodName, _log, type);
            ps = conn.prepareStatement(sql);
            ps.setLong(1, Long.parseLong(sysId));
            rs = ps.executeQuery();
            while (rs.next())
            {
                list.add(rs.getLong("iid"));
            }
        } catch (Exception ex)
        {
            _log.error(ex.getMessage(), ex);
        } finally
        {
            DBResource.closeConn(conn, rs, ps, methodName, _log);
        }

        return list;
    }

    public List getAgentListBySysId ( long systemId, int type ) throws RepositoryException
    {
        List list = new ArrayList();
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        for (int i = 0; i < 10; i++)
        {
            try
            {
                int itype = type == Constants.IEAI_HEALTH_INSPECTION ? Constants.IEAI_HEALTH_INSPECTION : type;
                conn = DBResource.getConnection("getAgentListBySysId", _log,
                        Constants.IEAI_HEALTH_INSPECTION);
                String sqlWhere = " ON TA.IAGENT_IP = T.AGENTIP AND T.CPPORT = TA.IAGENT_PORT WHERE T.CPID>0 ";
                if (itype == Constants.IEAI_TIMINGTASK || itype == Constants.IEAI_EMERGENCY_SWITCH)
                {
                    sqlWhere = "WHERE T.IAGENTINFO_ID>0  ";
                }

                String sqlPageString = "SELECT T.*,TA.IAGENT_AZNAME AS TAIAGENT_AZNAME,TA.IAGENT_NETID AS TAIAGENT_NETID FROM IEAI_COMPUTER_LIST T LEFT JOIN IEAI_AGENTINFO TA ";
                if (itype == Constants.IEAI_TIMINGTASK || itype == Constants.IEAI_EMERGENCY_SWITCH)
                {
                    sqlPageString = "SELECT T.* FROM IEAI_AGENTINFO T ";
                }

                if (3 == JudgeDB.IEAI_DB_TYPE)
                {
                    sqlPageString = "SELECT T.*,TA.IAGENT_AZNAME AS TAIAGENT_AZNAME,TA.IAGENT_NETID AS TAIAGENT_NETID FROM IEAI_COMPUTER_LIST T LEFT JOIN IEAI_AGENTINFO TA ";
                    if (itype == Constants.IEAI_TIMINGTASK || itype == Constants.IEAI_EMERGENCY_SWITCH)
                    {
                        sqlPageString = "SELECT T.* FROM IEAI_AGENTINFO T ";
                    }
                }

                if (itype == Constants.IEAI_HEALTH_INSPECTION)
                {
                    sqlWhere = sqlWhere
                            + "AND T.CPID NOT IN (SELECT T1.COMPUTERID FROM IEAI_SYS_RELATION T1 WHERE T1.SYSTEMID=?)";
                } else if (itype == Constants.IEAI_TIMINGTASK || itype == Constants.IEAI_EMERGENCY_SWITCH)
                {
                    sqlWhere = sqlWhere
                            + "AND T.IAGENTINFO_ID NOT IN (SELECT T1.COMPUTERID FROM IEAI_SYS_RELATION T1 WHERE T1.SYSTEMID=?)";
                } else if (itype == Constants.IEAI_SUS)
                {
                    sqlWhere = sqlWhere
                            + "AND T.CPID NOT IN (SELECT T1.COMPUTERID FROM IEAI_SYS_RELATION T1 WHERE T1.SYSTEMID=?)";
                } else if (itype == Constants.IEAI_APM)
                {
                    sqlWhere = sqlWhere
                            + "AND T.CPID NOT IN (SELECT T1.COMPUTERID FROM IEAI_SYS_RELATION T1 WHERE T1.SYSTEMID=?)";
                } else if (itype == Constants.IEAI_AZ)
                {
                    sqlWhere = sqlWhere
                            + "AND T.CPID NOT IN (SELECT T1.COMPUTERID FROM IEAI_SYS_RELATION T1 WHERE T1.SYSTEMID=?)";
                }

                sqlPageString = sqlPageString + sqlWhere;
                actStat = conn.prepareStatement(sqlPageString);
                actStat.setLong(1, systemId);
                actRS = actStat.executeQuery();
                while (actRS.next())
                {
                    if (itype == Constants.IEAI_TIMINGTASK || itype == Constants.IEAI_EMERGENCY_SWITCH)
                    {
                        list.add(actRS.getString("IAGENT_IP"));
                    }else{
                        list.add(actRS.getString("IP"));
                    }
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            } catch (SQLException e)
            {
                _log.error("getAgentListBySysId is error ! " ,e);
                throw new RepositoryException(ServerError.ERR_DB_QUERY);
            } finally
            {
                DBResource.closeConn(conn, actRS, actStat, "getAgentListBySysId", _log);
            }
        }
        return list;
    }



    
    /**
     * 
     * <li>Description:获业务系统是否自动纳管标识</li> 
     * <AUTHOR>
     * 2023年2月13日 
     * @param sysid
     * @return
     * @throws RepositoryException
     * return Long
     */
    public Long getProjectIcanSync ( Long sysid ) throws RepositoryException
    {

        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rset = null;
        long icanSync = -1;
        try
        {
            conn = DBResource.getConnection("getProjectIcanSync", _log, Constants.IEAI_IEAI_BASIC);
            String sql = "SELECT ICANSYNC  FROM IEAI_PROJECT WHERE IID = ? ";
            ps = conn.prepareStatement(sql);
            ps.setLong(1, sysid);
            rset = ps.executeQuery();

            while (rset.next())
            {
                icanSync = rset.getLong("ICANSYNC");
            }
        } catch (Exception e)
        {
            _log.error("BusinessSystemManager.getProjectIcanSync Error", e);
        } finally
        {
            DBResource.closeConn(conn, rset, ps, "getProjectIcanSync", _log);
        }
        return icanSync;
    }
    
    public boolean isDayStartMainFlow ( String prjName) throws RepositoryException
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        boolean isMain = false;
        Connection con = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        int runningFlowNum = 0;
        String sql = "select count(1) from ieai_excelmodel_daystart WHERE iproname =? ";
        try
        {
            con = DBResource.getConnection(method, _log, Constants.IEAI_IEAI_BASIC);
            ps = con.prepareStatement(sql);
            ps.setString(1, prjName);
            rs = ps.executeQuery();
            if (rs.next())
            {
                runningFlowNum = rs.getInt(1);
            }
            if (runningFlowNum != 0)
            {
                isMain = true;
            }
        } catch (SQLException e)
        {
            DBResource.rollback(con, ServerError.ERR_DB_QUERY, e, method, _log);
        } finally
        {
            DBResource.closeConn(con,rs,ps,method,_log);
        }
        return isMain;
    }
    
    public boolean isMainPro ( String prjName) throws RepositoryException
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        boolean isMain = false;
        Connection con = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        int runningFlowNum = 0;
        String sql = "select count(1) from IEAI_EXCELMODEL WHERE imainproname =? ";
        try
        {
            con = DBResource.getConnection(method, _log, Constants.IEAI_IEAI_BASIC);
            ps = con.prepareStatement(sql);
            ps.setString(1, prjName);
            rs = ps.executeQuery();
            if (rs.next())
            {
                runningFlowNum = rs.getInt(1);
            }
            if (runningFlowNum != 0)
            {
                isMain = true;
            }
        } catch (SQLException e)
        {
            DBResource.rollback(con, ServerError.ERR_DB_QUERY, e, method, _log);
        } finally
        {
            DBResource.closeConn(con,rs,ps,method,_log);
        }
        return isMain;
    }
    
    public boolean isChildPro ( String prjName) throws RepositoryException
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        boolean isMain = false;
        Connection con = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        int runningFlowNum = 0;
        String sql = "select count(1) from IEAI_EXCELMODEL WHERE ichildproname =? ";
        try
        {
            con = DBResource.getConnection(method, _log, Constants.IEAI_IEAI_BASIC);
            ps = con.prepareStatement(sql);
            ps.setString(1, prjName);
            rs = ps.executeQuery();
            if (rs.next())
            {
                runningFlowNum = rs.getInt(1);
            }
            if (runningFlowNum != 0)
            {
                isMain = true;
            }
        } catch (SQLException e)
        {
            DBResource.rollback(con, ServerError.ERR_DB_QUERY, e, method, _log);
        } finally
        {
            DBResource.closeConn(con,rs,ps,method,_log);
        }
        return isMain;
    }
    
    public void deleteDayStartPro ( String proName ) throws RepositoryException
    {
        
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        for (int i = 0;; i++)
        {
            try
            {
                String sql = "delete from ieai_excelmodel_daystart where iproname=?";
                Connection conn = null;
                PreparedStatement ps = null;
                try
                {
                    conn = DBResource.getConnection(method, _log, Constants.IEAI_IEAI_BASIC);
                    ps = conn.prepareStatement(sql);
                    ps.setString(1, proName);
                    ps.executeUpdate();
                    conn.commit();
                } catch (SQLException e)
                {
                    _log.error(method, e);
                    DBResource.rollback(conn, ServerError.ERR_DB_DELETE, e, method, _log);
                    throw new RepositoryException(ServerError.ERR_DB_INIT);
                } finally
                {
                    DBResource.closePSConn(conn, ps, method, _log);
                }
                break;
            } catch (RepositoryException e)
            {
                DBRetryUtil.waitForNextTry(i, e);
            }
        }
    }
    
    public void deleteMainPro ( String proName ) throws RepositoryException
    {
        
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        for (int i = 0;; i++)
        {
            try
            {
                String sqlPreCopy = "delete from ieai_actpre_copy where iprojectname=?";
                String sqlPre = "delete from ieai_actpre where iprojectname=?";
                String sqlSuccCopy = "delete from ieai_actsucc_copy where iprojectname=?";
                String sqlSucc = "delete from ieai_actsucc where iprojectname=?";
                String sqlModelCopy = "delete from ieai_excelmodel_copy where imainproname=?";
                String sqlModel = "delete from ieai_excelmodel where imainproname=?";
                Connection conn = null;
                PreparedStatement ps1 = null;
                PreparedStatement ps2 = null;
                PreparedStatement ps3 = null;
                PreparedStatement ps4 = null;
                PreparedStatement ps5 = null;
                PreparedStatement ps6 = null;
                try
                {
                    conn = DBResource.getConnection(method, _log, Constants.IEAI_IEAI_BASIC);
                    ps1 = conn.prepareStatement(sqlPreCopy);
                    ps1.setString(1, proName);
                    ps1.executeUpdate();
                    
                    ps2 = conn.prepareStatement(sqlPre);
                    ps2.setString(1, proName);
                    ps2.executeUpdate();
                    
                    ps3 = conn.prepareStatement(sqlSuccCopy);
                    ps3.setString(1, proName);
                    ps3.executeUpdate();
                    
                    ps4 = conn.prepareStatement(sqlSucc);
                    ps4.setString(1, proName);
                    ps4.executeUpdate();
                    
                    ps5 = conn.prepareStatement(sqlModelCopy);
                    ps5.setString(1, proName);
                    ps5.executeUpdate();
                    
                    ps6 = conn.prepareStatement(sqlModel);
                    ps6.setString(1, proName);
                    ps6.executeUpdate();
                    conn.commit();
                } catch (SQLException e)
                {
                    _log.error(method, e);
                    DBResource.rollback(conn, ServerError.ERR_DB_DELETE, e, method, _log);
                    throw new RepositoryException(ServerError.ERR_DB_INIT);
                } finally
                {
                    DBResource.closePreparedStatement(ps1, method, _log);
                    DBResource.closePreparedStatement(ps2, method, _log);
                    DBResource.closePreparedStatement(ps3, method, _log);
                    DBResource.closePreparedStatement(ps4, method, _log);
                    DBResource.closePreparedStatement(ps5, method, _log);
                    DBResource.closePreparedStatement(ps6, method, _log);
                    DBResource.closeConnection(conn, method, _log);
                }
                break;
            } catch (RepositoryException e)
            {
                DBRetryUtil.waitForNextTry(i, e);
            }
        }
    }
    
    public void deleteChildPro ( String proName ) throws RepositoryException
    {
        
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        for (int i = 0;; i++)
        {
            try
            {
                String sqlPreCopy = "delete from ieai_actpre_copy where ichildprojectname=?";
                String sqlPre = "delete from ieai_actpre where ichildprojectname=?";
                String sqlSuccCopy = "delete from ieai_actsucc_copy where ichildprojectname=?";
                String sqlSucc = "delete from ieai_actsucc where ichildprojectname=?";
                String sqlModelCopy = "delete from ieai_excelmodel_copy where ichildproname=?";
                String sqlModel = "delete from ieai_excelmodel where ichildproname=?";
                Connection conn = null;
                PreparedStatement ps1 = null;
                PreparedStatement ps2 = null;
                PreparedStatement ps3 = null;
                PreparedStatement ps4 = null;
                PreparedStatement ps5 = null;
                PreparedStatement ps6 = null;
                try
                {
                    conn = DBResource.getConnection(method, _log, Constants.IEAI_IEAI_BASIC);
                    ps1 = conn.prepareStatement(sqlPreCopy);
                    ps1.setString(1, proName);
                    ps1.executeUpdate();
                    
                    ps2 = conn.prepareStatement(sqlPre);
                    ps2.setString(1, proName);
                    ps2.executeUpdate();
                    
                    ps3 = conn.prepareStatement(sqlSuccCopy);
                    ps3.setString(1, proName);
                    ps3.executeUpdate();
                    
                    ps4 = conn.prepareStatement(sqlSucc);
                    ps4.setString(1, proName);
                    ps4.executeUpdate();
                    
                    ps5 = conn.prepareStatement(sqlModelCopy);
                    ps5.setString(1, proName);
                    ps5.executeUpdate();
                    
                    ps6 = conn.prepareStatement(sqlModel);
                    ps6.setString(1, proName);
                    ps6.executeUpdate();
                    conn.commit();
                } catch (SQLException e)
                {
                    _log.error(method, e);
                    DBResource.rollback(conn, ServerError.ERR_DB_DELETE, e, method, _log);
                    throw new RepositoryException(ServerError.ERR_DB_INIT);
                } finally
                {
                    DBResource.closePreparedStatement(ps1, method, _log);
                    DBResource.closePreparedStatement(ps2, method, _log);
                    DBResource.closePreparedStatement(ps3, method, _log);
                    DBResource.closePreparedStatement(ps4, method, _log);
                    DBResource.closePreparedStatement(ps5, method, _log);
                    DBResource.closePreparedStatement(ps6, method, _log);
                    DBResource.closeConnection(conn, method, _log);
                }
                break;
            } catch (RepositoryException e)
            {
                DBRetryUtil.waitForNextTry(i, e);
            }
        }
    }



    public Long getSameProjectNameId ( Long systemId,short type ) throws RepositoryException
    {
        Long newSystemId = -1L;
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        String sql = "select p.iid from ieai_project p  where  p.protype=7  and  exists (select 1 from ieai_project pp where  p.iname  = pp.iname and pp.iid=? and pp.protype=3 )";
        try
        {
            conn = DBResource.getConnection(method, _log, type);
            ps = conn.prepareStatement(sql);
            ps.setLong(1, systemId);
            rs = ps.executeQuery();
            while(rs.next()){
                newSystemId = rs.getLong("iid");
            }
        } catch (SQLException e)
        {
            _log.error(method, e);
            DBResource.rollback(conn, ServerError.ERR_DB_DELETE, e, method, _log);
            throw new RepositoryException(ServerError.ERR_DB_INIT);
        } finally
        {
            DBResource.closeConn(conn,rs,ps, method, _log);
        }
            return newSystemId;
        }
    public Map<Long,String> getProjectMap(){
        Connection con = null;
        PreparedStatement ps = null;
        ResultSet rs= null;
        Map<Long,String> map = new HashMap<>();
        String sql = "select iid,systype from IEAI_PROJECT_INFO";
        try {
            con = DBResource.getConnection("getProjectMap",_log,Constants.IEAI_HEALTH_INSPECTION);
            ps = con.prepareStatement(sql);
            rs = ps.executeQuery();
            while(rs.next()){
                long iid = rs.getLong("iid");
                Long  systype = rs.getLong("systype");
                String type ="";
                if (1 ==systype){
                    type = "重保系统";
                }else if(2==systype){
                    type = "一类系统";
                }else if(3==systype){
                    type = "二类系统";
                }else if(4== systype){
                    type = "三类系统";
                }
                map.put(iid,type);
            }
        } catch (RepositoryException e) {
            e.printStackTrace();
        } catch (SQLException throwables) {
            throwables.printStackTrace();
        }finally{
            DBResource.closeConn(con,rs,ps,"",_log);
        }
        return map;
    }

    /**
     * 福建农信-根据系统编号查询系统id
     * @param deleteSysCodess
     * @param itype
     * @return
     * @throws RepositoryException
     */
    public Long[] queryBusinessSystemIds(String deleteSysCodess,int itype) throws RepositoryException {
        List<Long> list = new ArrayList();
        PreparedStatement ps = null;
        ResultSet rs = null;
        Long[] deleteIds = null;
        Connection conn = null;
        try
        {
            String sql = "SELECT IID FROM IEAI_PROJECT WHERE ICICDSYS_CODE IN (?)";
            conn = DBManager.getInstance().getJdbcConnection(itype);
            ps = conn.prepareStatement(sql);
            ps.setString(1, deleteSysCodess);
            rs = ps.executeQuery();
            while (rs.next())
            {
                list.add(rs.getLong("IID"));
            }
            if (!list.isEmpty()) {
                deleteIds = new Long[list.size()];
                deleteIds = list.toArray(deleteIds);
            }
        } catch (Exception e)
        {
            _log.error("BusinessSystemManager.queryBusinessSystemIds is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closeConn(conn,rs, ps, "queryBusinessSystemIds", _log);
        }
        return deleteIds;
    }

    /**
     * 同步计算机信息到业务系统中
     * 此方法首先从业务系统中获取所有业务系统的信息，然后与传入的计算机信息列表进行匹配和处理
     * 如果有有效的匹配和更新，将更新后的业务系统信息保存回数据库
     *
     * @param combeanList 包含计算机信息的列表，用于与业务系统信息进行匹配和更新
     * @throws Exception 如果在获取业务系统列表或更新业务系统信息时发生错误，将抛出异常
     */
    public void syncComputerInfo(List<CommonComBean> combeanList) throws Exception {
        List<BusinessSystemBean> businessSystemList = getBusinessSystemList();
        _log.info("businessSystemList is "+businessSystemList);
        if (CollectionUtils.isEmpty(businessSystemList)) {
            return;
        }

        List<BusinessSystemBean> updatedBusinessSystemList = businessSystemList.stream()
                .filter(this::isValidBusinessSystem)
                .map(bsb -> processFirstMatchingCommonComBean(bsb, combeanList))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(updatedBusinessSystemList)) {
            _log.info("update business system info size is "+updatedBusinessSystemList.size());
            _log.info("update business system info is "+updatedBusinessSystemList);
            updateBusinessSystem(updatedBusinessSystemList);
        }
    }

    /**
     * 处理并返回第一个匹配的CommonComBean
     * 该方法遍历CommonComBean列表，寻找其appSysName与systemBean的sysName相匹配的项
     * 当找到匹配项时，对systemBean进行相应处理并返回处理结果
     * 如果没有找到匹配项，则返回null
     *
     * @param systemBean 业务系统豆，包含系统信息
     * @param combeanList CommonComBean列表，用于查找匹配项
     * @return 如果找到匹配项，则返回处理后的BusinessSystemBean，否则返回null
     */
    private BusinessSystemBean processFirstMatchingCommonComBean(BusinessSystemBean systemBean, List<CommonComBean> combeanList) {
        // 获取系统名称
        String sysName = systemBean.getSysName();
        _log.info("system name is "+sysName);
        // 遍历CommonComBean列表
        for (CommonComBean commonComBean : combeanList) {
            // 检查系统名称是否包含应用系统名称
            String systemInfo = Optional.ofNullable(commonComBean).map(CommonComBean::getSystemInfo).orElse("");
            _log.info("system info is "+systemInfo);
            if (!systemInfo.isEmpty()&&sysName.contains(systemInfo)) {
                _log.info("find matching commonComBean is "+commonComBean);
                // 如果包含，处理当前的CommonComBean并返回结果
                return processCommonComBean(systemBean, commonComBean);
            }
        }
        // 如果没有找到匹配项，返回null
        return null;
    }

    /**
     * 检查业务系统是否有效
     * 有效性通过检查系统名称是否非空来判断
     *
     * @param businessSystemBean 业务系统实体类
     * @return 如果系统名称非空，则返回true，表示业务系统有效；否则返回false
     */
    private boolean isValidBusinessSystem(BusinessSystemBean businessSystemBean) {
        _log.info("business system is "+businessSystemBean);
        return StringUtils.isNotBlank(businessSystemBean.getSysName());
    }

    /**
     * 处理通用组件Bean并更新业务系统Bean
     * 此方法主要负责根据通用组件Bean中的应用管理员信息，提取相关用户信息，并用这些信息更新业务系统Bean
     *
     * @param businessSystemBean 业务系统Bean，包含业务系统的基本信息
     * @param commonComBean 通用组件Bean，包含应用管理员等通用配置信息
     * @return 更新后的业务系统Bean，如果应用管理员列表为空，则返回null
     */
    private BusinessSystemBean processCommonComBean(BusinessSystemBean businessSystemBean, CommonComBean commonComBean) {
        // 获取应用管理员列表
        String systemName = commonComBean.getSystemInfo();
        List<String> appAdmins = commonComBean.getAppAdmins();
        _log.info("sysName is "+systemName+",app admins is "+appAdmins);
        // 如果应用管理员列表为空，则直接返回null，表示无需进一步处理
        if (CollectionUtils.isEmpty(appAdmins)) {
            return null;
        }

        // 初始化集合，用于存储管理员的用户名、部门和电话号码，以避免重复
        Set<String> userNames = new HashSet<>();
        Set<String> departments = new HashSet<>();
        Set<String> telphones = new HashSet<>();

        // 处理每个应用管理员，提取并收集他们的相关信息
        appAdmins.stream()
                .filter(StringUtils::isNotBlank)
                .flatMap(admin -> Arrays.stream(admin.split(";")))
                .filter(StringUtils::isNotBlank)
                .forEach(s -> {
                    _log.info("sysName is "+systemName+",admin is "+s);
                    List<UserInfoHs> userInfos = getUserInfo(extractName(s));
                    _log.info("sysName is "+systemName+",user info is "+userInfos);
                    // 遍历用户信息，收集用户名、部门和电话号码
                    emptyIfNull(userInfos).forEach(userInfo -> {
                        userNames.add(userInfo.getFullName());
                        departments.add(userInfo.getIDEPARTMENT());
                        telphones.add(userInfo.getITELEPHONE());
                    });
                });

        // 根据收集到的信息创建并返回更新后的业务系统Bean
        return createUpdatedBusinessSystemBean(businessSystemBean, userNames, departments, telphones);
    }

    /**
     * 根据输入信息获取用户列表
     * 如果输入为空或仅包含空白字符，则返回空列表
     * 如果输入为登录名称，则通过登录名称查询用户信息
     * 如果输入为全名格式（假设格式为“全名(其他信息)”），则通过全名查询用户信息
     *
     * @param stringStringSimpleEntry 用户输入的字符串，可能为登录名称或全名
     * @return 包含用户信息的列表，如果找不到则返回空列表
     */
    private List<UserInfoHs> getUserInfo(AbstractMap.SimpleEntry<String, String> stringStringSimpleEntry) {
        // 检查输入是否为空或仅包含空白字符，如果是，则返回空列表
        if (stringStringSimpleEntry == null || StringUtils.isAnyBlank(stringStringSimpleEntry.getKey(), stringStringSimpleEntry.getValue())) {
            return Collections.emptyList();
        }

        // 默认将输入视为登录名称
        String key = stringStringSimpleEntry.getKey().trim();
        String value = stringStringSimpleEntry.getValue().trim();
        _log.info("key is "+key);
        _log.info("value is "+value);

        // 如果登录名称为空或仅包含空白字符，则表示输入应为全名格式
        if ("fullName".equals(value)) {
            // 从输入中提取全名部分，假设格式为“全名(其他信息)”
            String fullName = key.contains("(") ? key.split("\\(", 2)[0].trim() : key;
            // 根据全名查询用户信息
            return getUserInfoByFullName(fullName);
        } else {
            // 根据登录名称查询用户信息
            return getUserInfoByLoginName(key);
        }
    }


    /**
     * 根据原始的BusinessSystemBean和新的用户、部门、电话号码集合创建一个更新后的BusinessSystemBean
     *
     * @param originalBean 原始的BusinessSystemBean
     * @param userNames 新的用户名称集合
     * @param departments 新的部门名称集合
     * @param telphones 新的电话号码集合
     * @return 如果有更新则返回更新后的BusinessSystemBean，否则返回null
     */
    private BusinessSystemBean createUpdatedBusinessSystemBean(BusinessSystemBean originalBean, Set<String> userNames, Set<String> departments, Set<String> telphones) {
        // 标志位用于指示是否需要更新Bean
        _log.info("createUpdatedBusinessSystemBean userNames is "+userNames +" departments is "+departments +" telphones is "+telphones);
        boolean a = false;
        boolean b = false;
        boolean c = false;

        // 创建一个新的BusinessSystemBean并设置其系统ID
        BusinessSystemBean updatedBean = new BusinessSystemBean();
        updatedBean.setSystemId(originalBean.getSystemId());

        // 将用户名称集合转换为字符串，并检查是否需要更新
        String asyToUse = userNames.stream().collect(Collectors.joining(","));
        if (StringUtils.isNotBlank(asyToUse) && !asyToUse.equals(originalBean.getAsyToUse())) {
            a = true;
            updatedBean.setAsyToUse(asyToUse);
        }

        // 将部门名称集合转换为字符串，并检查是否需要更新
        String department = departments.stream().collect(Collectors.joining(","));
        if (StringUtils.isNotBlank(department) && !department.equals(originalBean.getDepartment())) {
            b = true;
            updatedBean.setDepartment(department);
        }

        // 将电话号码集合转换为字符串，并检查是否需要更新
        String systemOwnerTel = telphones.stream().collect(Collectors.joining(","));
        if (StringUtils.isNotBlank(systemOwnerTel) && !systemOwnerTel.equals(originalBean.getSystemOwnerTel())) {
            c = true;
            updatedBean.setSystemOwnerTel(systemOwnerTel);
        }

        // 根据标志位判断是否返回更新后的Bean
        return (a || b || c) ? updatedBean : null;
    }

    /**
     * 更新业务系统信息
     *
     * 该方法接收一个业务系统豆列表作为参数，通过数据库连接更新业务系统信息
     * 它主要处理的是根据业务系统列表中的信息，构建相应的SQL更新语句，并执行批量更新操作
     *
     * @param businessSystemList 业务系统豆列表，包含需要更新的业务系统信息
     * @throws Exception 如果数据库操作失败或出现其他错误，抛出此异常
     */
    public void updateBusinessSystem(List<BusinessSystemBean> businessSystemList) throws Exception {
        // 获取数据库连接
        _log.info("updateBusinessSystem start");
        try (Connection conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_SUS)) {
            // 遍历业务系统列表，为每个业务系统生成并执行更新SQL
            for (BusinessSystemBean businessSystemBean : businessSystemList) {
                String sql = buildUpdateSQL(businessSystemBean);
                try (PreparedStatement ps = conn.prepareStatement(sql)) {
                    setParameters(ps, businessSystemBean);
                    ps.executeUpdate();
                } catch (Exception e) {
                    // 捕获异常，记录错误日志，回滚事务，并重新抛出异常
                    _log.error("Error updating business system " + businessSystemBean.getSystemId(), e);
                    DBResource.rollback(conn, Constants.IEAI_SUS, e, "updateBusinessSystem", _log);
                    throw e;
                }
            }
            // 提交事务
            conn.commit();
        } catch (Exception e) {
            // 捕获异常，记录错误日志，回滚事务，并重新抛出异常
            _log.error("Error updating business system ", e);
            throw e;
        }
        _log.info("updateBusinessSystem end");
    }

    /**
     * 根据业务系统信息构建更新SQL语句
     * 此方法旨在根据提供的业务系统信息，动态生成一个更新数据库记录的SQL语句
     * 它会根据业务系统对象中的非空字段来决定更新数据库中的哪些列
     *
     * @param businessSystemBean 一个包含业务系统信息的对象，用于生成更新SQL
     * @return 返回一个字符串，包含了根据业务系统信息动态生成的更新SQL语句
     */
    private String buildUpdateSQL(BusinessSystemBean businessSystemBean) {
        List<String> columns = new ArrayList<>();
        if (StringUtils.isNotBlank(businessSystemBean.getDepartment())) {
            columns.add("DEPARTMENT");
        }
        if (StringUtils.isNotBlank(businessSystemBean.getAsyToUse())) {
            columns.add("ISYSTEM_OWNER");
        }
        if (StringUtils.isNotBlank(businessSystemBean.getSystemOwnerTel())) {
            columns.add("ISYSTEM_OWNER_TEL");
        }

        StringBuilder sqlBuilder = new StringBuilder("update IEAI_PROJECT set ");
        for (int i = 0; i < columns.size(); i++) {
            if (i > 0) {
                sqlBuilder.append(", ");
            }
            sqlBuilder.append(columns.get(i)).append(" = ?");
        }
        sqlBuilder.append(" where IID=?");
        return sqlBuilder.toString();
    }

    /**
     * 为预编译语句设置参数
     *
     * @param ps                  预编译语句
     * @param businessSystemBean 业务系统信息对象
     * @throws Exception 如果设置参数时发生异常
     */
    private void setParameters(PreparedStatement ps, BusinessSystemBean businessSystemBean) throws Exception {
        List<Object> values = new ArrayList<>();
        if (StringUtils.isNotBlank(businessSystemBean.getDepartment())) {
            values.add(businessSystemBean.getDepartment());
        }
        if (StringUtils.isNotBlank(businessSystemBean.getAsyToUse())) {
            values.add(businessSystemBean.getAsyToUse());
        }
        if (StringUtils.isNotBlank(businessSystemBean.getSystemOwnerTel())) {
            values.add(businessSystemBean.getSystemOwnerTel());
        }

        for (int i = 0; i < values.size(); i++) {
            ps.setObject(i + 1, values.get(i));
        }
        ps.setLong(values.size() + 1, businessSystemBean.getSystemId());
    }


    /**
     * 获取业务系统列表
     *
     * 本方法通过查询数据库，获取所有业务系统的基本信息，并以列表形式返回
     * 使用了try-with-resources语句自动管理数据库连接和资源的关闭，避免资源泄露
     *
     * @return 包含所有业务系统信息的列表
     * @throws RuntimeException 如果数据库查询过程中发生错误，则抛出运行时异常
     */
    public List<BusinessSystemBean> getBusinessSystemList() {
        // 预分配初始容量为100的列表，用于存储业务系统信息
        List<BusinessSystemBean> list = new ArrayList<>(100);

        // SQL查询语句，用于从数据库中获取业务系统列表
        String sql = "select IID, INAME, DEPARTMENT, ISYSTEM_OWNER, ISYSTEM_OWNER_TEL from IEAI_PROJECT where iid > ? and IUUID is null order by iid desc";

        // 使用try-with-resources自动管理数据库连接和PreparedStatement资源
        try (Connection conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_SUS);
             PreparedStatement ps = conn.prepareStatement(sql)) {
            ps.setLong(1, 0);

            // 使用try-with-resources自动管理ResultSet资源
            try (ResultSet rs = ps.executeQuery()) {
                // 遍历查询结果，将每条记录封装为BusinessSystemBean对象，并添加到列表中
                while (rs.next()) {
                    BusinessSystemBean bean = new BusinessSystemBean();
                    bean.setSystemId(rs.getLong("IID"));
                    bean.setSysName(rs.getString("INAME"));
                    bean.setAsyToUse(rs.getString("ISYSTEM_OWNER"));
                    bean.setSystemOwnerTel(rs.getString("ISYSTEM_OWNER_TEL"));
                    bean.setDepartment(rs.getString("DEPARTMENT"));
                    list.add(bean);
                }
            }
        } catch (Exception e) {
            // 日志记录异常信息，并抛出自定义异常
            _log.error("BusinessSystemManager.getBusinessSystemList is error", e);
            throw new RuntimeException("Failed to retrieve business system list", e);
        }

        // 返回包含所有业务系统信息的列表
        return list;
    }

    /**
     * 从输入字符串中提取名称
     * 如果输入字符串为空或null，返回空字符串
     * 如果输入字符串中包含括号，且括号内有内容，则返回括号内的内容，否则返回括号前的字符串
     *
     * @param input 输入的字符串，可能包含中文名称和括号内的名称
     * @return 提取的名称字符串
     */
    public AbstractMap.SimpleEntry<String, String> extractName(String input) {
        // 检查输入字符串是否为空或null
        if (input == null || input.isEmpty()) {
            // 如果为空或null，返回空字符串
            return new AbstractMap.SimpleEntry<>("", "");
        }
        String bracketContent = "";

        // 查找输入字符串中的第一个左括号的位置
        int startIndex = input.indexOf('(');
        // 查找输入字符串中的第一个右括号的位置
        int endIndex = input.indexOf(')');

        // 如果找到了一对括号，并且右括号在左括号之后
        if (startIndex != -1 && endIndex != -1 && endIndex > startIndex) {
            // 获取括号之间的内容并去除首尾空格
            bracketContent = input.substring(startIndex + 1, endIndex).trim();
            // 如果括号间的内容非空，则返回该内容
            if (!bracketContent.isEmpty()) {
                return new AbstractMap.SimpleEntry<>(bracketContent, "loginName");
            }
        }

        // 如果没有括号或者括号内没有值，返回中文名称
        return new AbstractMap.SimpleEntry<>(input.trim(), "fullName");
    }

    /**
     * 获取用户信息
     * @param loginName
     * @return
     */
    public List<UserInfoHs> getUserInfoByLoginName(String loginName)
    {
        List<UserInfoHs> list = new ArrayList();
        PreparedStatement ps = null;
        ResultSet rs = null;
        Connection conn = null;
        try
        {
            String sql = "select IID,ILOGINNAME,IFULLNAME,IDEPARTMENT,ITELEPHONE from IEAI_USER where ILOGINNAME = ?";
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_SUS);
            ps = conn.prepareStatement(sql);
            ps.setString(1, loginName);
            rs = ps.executeQuery();
            while (rs.next())
            {
                UserInfoHs bean = new UserInfoHs();
                bean.setIid(rs.getLong("IID"));
                bean.setLoginName(rs.getString("ILOGINNAME"));
                bean.setFullName(rs.getString("IFULLNAME"));
                bean.setIDEPARTMENT(rs.getString("IDEPARTMENT"));
                bean.setITELEPHONE(rs.getString("ITELEPHONE"));
                list.add(bean);
            }
        } catch (Exception e)
        {
            _log.error("BusinessSystemManager.getUserInfo is error", e);
        } finally
        {
            DBResource.closeConn(conn,rs, ps, "getUserInfo", _log);
        }
        return list;
    }

    /**
     * 获取用户信息
     * @param fullName
     * @return
     */
    public List<UserInfoHs> getUserInfoByFullName(String fullName)
    {
        List<UserInfoHs> list = new ArrayList();
        PreparedStatement ps = null;
        ResultSet rs = null;
        Connection conn = null;
        try
        {
            String sql = "select IID,ILOGINNAME,IFULLNAME,IDEPARTMENT,ITELEPHONE from IEAI_USER where IFULLNAME = ?";
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_SUS);
            ps = conn.prepareStatement(sql);
            ps.setString(1, fullName);
            rs = ps.executeQuery();
            while (rs.next())
            {
                UserInfoHs bean = new UserInfoHs();
                bean.setIid(rs.getLong("IID"));
                bean.setLoginName(rs.getString("ILOGINNAME"));
                bean.setFullName(rs.getString("IFULLNAME"));
                bean.setIDEPARTMENT(rs.getString("IDEPARTMENT"));
                bean.setITELEPHONE(rs.getString("ITELEPHONE"));
                list.add(bean);
            }
        } catch (Exception e)
        {
            _log.error("BusinessSystemManager.getUserInfo is error", e);
        } finally
        {
            DBResource.closeConn(conn,rs, ps, "getUserInfo", _log);
        }
        return list;
    }

    /**
     * 如果给定的列表为null，则返回一个空列表，否则返回原列表
     * 此方法的主要作用是避免在处理列表时出现NullPointerException
     * 它提供了一种安全的方式来处理可能为null的列表，确保程序的鲁棒性
     *
     * @param list 可能为null的列表
     * @return 如果列表为null，则返回空列表；否则返回原列表
     */
    public <T> List<T> emptyIfNull(List<T> list) {
        if(list == null){
            return Collections.emptyList();
        }
        return list;
    }


    public Map<String,Object> saveBusinessAndComputerData(BussComputerBean bean,Connection baseConn) {
        Map<String,Object> retParamMap = new HashMap();
        /**
         * 1.通过业务系统编码判断是否存在该系统，存在更新系统简称
         * 2.判断是否存在设备组信息，不存在新建，存在不处理
         * 3.判断业务系统和设备组中，是否绑定设备，没有绑定进行绑定设备
         * 4.判断设备组中是否绑定业务系统，没有绑定进行绑定
         * 5.判断设备组和设备是否绑定，未绑定进行绑定操作
         */
        boolean isSuccess = true;
        List<String> exeSqls = new ArrayList<String>();
        List<String> rollbackSqls = new ArrayList<String>();
        PreparedStatement ps1 = null;
        ResultSet rs1 = null;
        PreparedStatement ps2 = null;
        ResultSet rs2 = null;
        PreparedStatement ps3 = null;
        ResultSet rs3 = null;
        try {
            /**业务系统维护处理 begin*/
            String queryBuss = "SELECT IID,INAME,ISYSTEMNUMBER FROM IEAI_PROJECT WHERE ISYSTEMCODE=?  AND IGROUPID=7";
            String systemNumber="";
            //业务系统ID
            long bussId = 0l;
            //业务系统名称
            String bussName="";
            ps1 = baseConn.prepareStatement(queryBuss);
            ps1.setString(1,bean.getSystemCode());
            rs1 = ps1.executeQuery();
            while (rs1.next()){
                bussId = rs1.getLong("IID");
                bussName = rs1.getString("INAME");
                systemNumber = rs1.getString("ISYSTEMNUMBER");
            }
            if (StringUtils.isNotBlank(systemNumber)){
                //判断业务系统简称是否一致，一致不更新了不一致更新
                if (!systemNumber.equals(bean.getSystemNumber())){
                    //更新
                    String updateBussSql = "update IEAI_PROJECT set ISYSTEMNUMBER='"+bean.getSystemNumber()+"' where ISYSTEMCODE='"+bean.getSystemCode()+"'";
                    exeSqls.add(updateBussSql);
                    //回滚
                    String rollbackBuss = "update IEAI_PROJECT set ISYSTEMNUMBER='"+systemNumber+"' where ISYSTEMCODE='"+bean.getSystemCode()+"'";
                    BeanFormatter<BussComputerBean> bf2 = new BeanFormatter<>(rollbackBuss);
                    String rbProjectSql = bf2.format(bean);
                    rollbackSqls.add(rbProjectSql);
                }
            }else {
                //不存在业务系统新增
                bussName = bean.getBussAndComputerName();
                bussId = IdGenerator.createId("IEAI_PROJECT", baseConn);
                String insertBussSql = "insert into ieai_project (iid,ipkgcontentid,iname,iuploaduser,iuploaduserid,igroupid,protype,iupperid,ilatestid,isvalidate,ISYSTEMCODE,isystemnumber,icansync) values"
                        + "("+bussId+",0,'"+bean.getBussAndComputerName()+"','"+bean.getFullName()+"',"+bean.getUserId()+",7,7,"+bussId+","+bussId+",1,'"+bean.getSystemCode()+"','"+bean.getSystemNumber()+"',1)";
                exeSqls.add(insertBussSql);
                String rbProjectSql = " DELETE FROM IEAI_PROJECT WHERE IID= ";
                rollbackSqls.add(rbProjectSql + bussId);
            }
            /**设备组处理 begin*/
            //设备组ID
            long compGroupId = 0l;
            String queryComp = "SELECT iid FROM IEAI_COMPUTER_GROUP where grouptype=7 and igroupname=?";
            ps2 = baseConn.prepareStatement(queryComp);
            ps2.setString(1,bean.getBussAndComputerName());
            rs2 = ps2.executeQuery();
            while (rs2.next()){
                compGroupId = rs2.getLong("IID");
            }
            if (compGroupId < 1){
                //不存在设备组 建立设备组
                compGroupId = IdGenerator.createId("IEAI_COMPUTER_GROUP", baseConn);
                String insertCompSql = "insert into IEAI_COMPUTER_GROUP(iid,igroupname,igroupdes,grouptype) values ("+compGroupId+",'"+bean.getBussAndComputerName()+"','"+bean.getComputerDesc()+"',7)";
                exeSqls.add(insertCompSql);
                String rbComputerGroup = " delete from IEAI_COMPUTER_GROUP where iid= ";
                rollbackSqls.add(rbComputerGroup + compGroupId);
            }
            /**设备处理 begin*/
            //设备ID
            long compId = 0l;
            String querycompList = "SELECT CPID FROM  IEAI_COMPUTER_LIST WHERE IP = ?";
            ps3 = baseConn.prepareStatement(querycompList);
            ps3.setString(1,bean.getIp());
            rs3 = ps3.executeQuery();
            while (rs3.next()){
                compId = rs3.getLong("CPID");
            }
            /**业务系统维护绑定设备处理 begin*/
            String querySysRel = "SELECT IP FROM IEAI_SYS_RELATION WHERE SYSTEMID = ? AND COMPUTERID=? AND PRJTYPE=7";
            int relCount = SQLUtil.toCount(baseConn,querySysRel,new Object[]{bussId,compId});
            if (relCount < 1){
                //插入业务系统维护中业务系统与设备关系表数据
                long sdid = IdGenerator.createId("IEAI_SYS_RELATION_SDID", baseConn);
                String insertSysRel = "INSERT INTO IEAI_SYS_RELATION (SYSTEMID,COMPUTERID,IP,COMPUTERSTATE,SYSSTATE,CENTERNAME,SYSNAME,PRJTYPE ,SDID)"
                        + "  (SELECT  T.IID AS SYSTEMID , T2.CPID AS COMPUTERID,T2.IP AS IP, 1 AS COMPUTERSTATE, NULL AS SYSSTATE,T2.CENTERNAME AS CENTERNAME, T.INAME AS SYSNAME, T.PROTYPE AS PRJTYPE ," + sdid
                        + "  FROM  IEAI_PROJECT  T, IEAI_COMPUTER_LIST T2 "
                        + "  WHERE  T.IID=" + bussId + "  AND T2.CPID=" + compId + ") ";
                String rbSysRelation = "DELETE FROM IEAI_SYS_RELATION WHERE SYSTEMID=" + bussId + " AND COMPUTERID="+ compId;
                exeSqls.add(insertSysRel);
                rollbackSqls.add(rbSysRelation);
            }
            /**设备分组绑定设备处理 begin*/
            String queryGroupMapp = "SELECT IID FROM IEAI_COMPUTER_GROUP_MAPPING WHERE IGID=? AND ICPID=?";
            int groupMapCount = SQLUtil.toCount(baseConn,queryGroupMapp,new Object[]{compGroupId,compId});
            if (groupMapCount < 1){
                //插入设备组绑定设备关系
                long groupMapId = IdGenerator.createId("IEAI_COMPUTER_GROUP_MAPPING", baseConn);
                String isMapping = "INSERT INTO IEAI_COMPUTER_GROUP_MAPPING(IID,ICPID,IGID) values(" + groupMapId + "," + compId + "," + compGroupId + ") ";
                String rbMapping = "DELETE FROM IEAI_COMPUTER_GROUP_MAPPING WHERE IGID=" + compGroupId + " AND ICPID = " + compId ;
                exeSqls.add(isMapping);
                rollbackSqls.add(rbMapping);
            }
            /**设备分组绑定业务系统处理 begin*/
            String queryGroupBus = "SELECT IID FROM IEAI_EQU_GROUP_BUSINESS WHERE BUSINESSID=? AND EQUGROUPID=?";
            int groupBusCount = SQLUtil.toCount(baseConn,queryGroupBus,new Object[]{bussId,compGroupId});
            if (groupBusCount < 1){
                //插入设备组绑定业务系统关系
                long groupBusId = IdGenerator.createId("IEAI_EQU_GROUP_BUSINESS", baseConn);
                String isGroupBus = " INSERT INTO  IEAI_EQU_GROUP_BUSINESS (IID, EQUGROUPID, BUSINESSID, BUSINESSNAME, PROTYPE) VALUES ( "+groupBusId+", "+compGroupId+", "+bussId+", '"+bussName+"', 7 ) ";
                String rbGroupBus = " DELETE FROM IEAI_EQU_GROUP_BUSINESS WHERE IID= ";
                exeSqls.add(isGroupBus);
                rollbackSqls.add(rbGroupBus+groupBusId);
            }
        } catch (Exception e){
            e.printStackTrace();
            isSuccess = false;
        }finally {
            DBResource.closePSRS(rs3,ps3,"saveBusinessAndComputerData",_log);
            DBResource.closePSRS(rs2,ps2,"saveBusinessAndComputerData",_log);
            DBResource.closePSRS(rs1,ps1,"saveBusinessAndComputerData",_log);
        }
        retParamMap.put("success", isSuccess);
        retParamMap.put("exeSqls", exeSqls);
        retParamMap.put("rollbackSqls", rollbackSqls);
        return retParamMap;
    }
}
