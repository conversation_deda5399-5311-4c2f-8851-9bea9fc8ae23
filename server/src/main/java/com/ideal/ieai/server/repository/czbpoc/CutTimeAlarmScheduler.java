package com.ideal.ieai.server.repository.czbpoc;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import org.apache.log4j.Logger;
import org.quartz.*;
import org.quartz.impl.StdSchedulerFactory;
import org.quartz.impl.matchers.GroupMatcher;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Calendar;

/**
 * 日切时间报警调度器
 * 负责根据配置的日切时间创建和管理Quartz定时任务
 */
public class CutTimeAlarmScheduler {

    private static final Logger LOGGER = Logger.getLogger(CutTimeAlarmScheduler.class);
    private static final String DATA_DATE_FLOP_TABLE = "IEAI_DATA_DATE_FLOP";
    private static final String JOB_GROUP = "CutTimeAlarmGroup";
    private static final String TRIGGER_GROUP = "CutTimeAlarmTriggerGroup";
    
    private static CutTimeAlarmScheduler instance;
    private Scheduler scheduler;
    private final CutTimeAlarmChecker alarmChecker = new CutTimeAlarmChecker();

    private CutTimeAlarmScheduler() {
        try {
            scheduler = StdSchedulerFactory.getDefaultScheduler();
        } catch (SchedulerException e) {
            LOGGER.error("初始化Quartz调度器失败: " + e.getMessage(), e);
        }
    }

    public static synchronized CutTimeAlarmScheduler getInstance() {
        if (instance == null) {
            instance = new CutTimeAlarmScheduler();
        }
        return instance;
    }

    /**
     * 启动日切时间报警调度
     * 在服务启动时调用此方法
     */
    public void startCutTimeAlarmScheduling() {
        try {
            if (!scheduler.isStarted()) {
                scheduler.start();
                LOGGER.info("Quartz调度器已启动");
            }

            // 获取所有配置的日切时间
            Set<String> cutTimes = getAllCutTimes();
            
            if (cutTimes.isEmpty()) {
                LOGGER.info("未找到配置的日切时间，跳过调度任务创建");
                return;
            }

            Date serverStartTime = new Date();
            LOGGER.info("服务启动时间: " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(serverStartTime));

            // 为每个不同的日切时间创建定时任务
            for (String cutTime : cutTimes) {
                createCutTimeAlarmJob(cutTime, serverStartTime);
            }

        } catch (Exception e) {
            LOGGER.error("启动日切时间报警调度失败: " + e.getMessage(), e);
        }
    }

    /**
     * 停止日切时间报警调度
     */
    public void stopCutTimeAlarmScheduling() {
        try {
            if (scheduler != null && scheduler.isStarted()) {
                scheduler.shutdown(true);
                LOGGER.info("日切时间报警调度器已停止");
            }
        } catch (SchedulerException e) {
            LOGGER.error("停止日切时间报警调度器失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取所有配置的日切时间
     */
    private Set<String> getAllCutTimes() {
        Set<String> cutTimes = new HashSet<>();
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        try {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);
            String sql = "SELECT DISTINCT icut_time FROM " + DATA_DATE_FLOP_TABLE + 
                        " WHERE icut_time IS NOT NULL AND icut_time != ''";
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery();

            while (rs.next()) {
                String cutTime = rs.getString("icut_time");
                if (cutTime != null && !cutTime.trim().isEmpty()) {
                    cutTimes.add(cutTime.trim());
                }
            }

            LOGGER.info("找到 " + cutTimes.size() + " 个不同的日切时间配置: " + cutTimes);

        } catch (Exception e) {
            LOGGER.error("获取日切时间配置失败: " + e.getMessage(), e);
        } finally {
            DBResource.closeConn(conn, rs, stmt, "getAllCutTimes", LOGGER);
        }

        return cutTimes;
    }

    /**
     * 为指定的日切时间创建定时任务
     */
    private void createCutTimeAlarmJob(String cutTime, Date serverStartTime) {
        try {
            // 解析日切时间
            String[] timeParts = cutTime.split(":");
            if (timeParts.length < 2) {
                LOGGER.error("日切时间格式不正确: " + cutTime);
                return;
            }

            int hour = Integer.parseInt(timeParts[0]);
            int minute = Integer.parseInt(timeParts[1]);

            // 构建今天的日切时间点
            Calendar todayCutTime = Calendar.getInstance();
            todayCutTime.set(Calendar.HOUR_OF_DAY, hour);
            todayCutTime.set(Calendar.MINUTE, minute);
            todayCutTime.set(Calendar.SECOND, 0);
            todayCutTime.set(Calendar.MILLISECOND, 0);

            String jobName = "CutTimeAlarmJob_" + cutTime.replace(":", "");
            String triggerName = "CutTimeAlarmTrigger_" + cutTime.replace(":", "");

            // 创建Job并设置日切时间参数
            JobDetail job = JobBuilder.newJob(CutTimeAlarmJob.class)
                    .withIdentity(jobName, JOB_GROUP)
                    .usingJobData("cutTime", cutTime)
                    .build();

            // 判断是否需要立即执行一次
            boolean needImmediateCheck = serverStartTime.after(todayCutTime.getTime());

            if (needImmediateCheck) {
                LOGGER.info("服务启动时间晚于日切时间 " + cutTime + "，将立即执行一次检查");
                // 立即执行一次检查
                alarmChecker.checkCutTimeAlarms(cutTime);

                // 设置明天的日切时间为首次执行时间
                todayCutTime.add(Calendar.DAY_OF_MONTH, 1);
            }

            // 创建每天定时执行的触发器
            Trigger trigger = TriggerBuilder.newTrigger()
                    .withIdentity(triggerName, TRIGGER_GROUP)
                    .startAt(todayCutTime.getTime())
                    .withSchedule(SimpleScheduleBuilder.simpleSchedule()
                            .withIntervalInHours(24)  // 每24小时执行一次
                            .repeatForever())
                    .build();

            // 调度任务
            scheduler.scheduleJob(job, trigger);
            
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            LOGGER.info("已创建日切时间报警任务: " + cutTime + 
                       ", 下次执行时间: " + sdf.format(todayCutTime.getTime()) +
                       (needImmediateCheck ? " (已立即执行一次)" : ""));

        } catch (Exception e) {
            LOGGER.error("创建日切时间报警任务失败，日切时间: " + cutTime + ", 错误: " + e.getMessage(), e);
        }
    }

    /**
     * 重新加载日切时间配置
     * 当日切时间配置发生变化时调用此方法
     */
    public void reloadCutTimeAlarmScheduling() {
        try {
            // 清除现有的所有日切时间报警任务
            clearAllCutTimeAlarmJobs();
            
            // 重新创建任务
            startCutTimeAlarmScheduling();
            
            LOGGER.info("日切时间报警调度配置已重新加载");
            
        } catch (Exception e) {
            LOGGER.error("重新加载日切时间报警调度配置失败: " + e.getMessage(), e);
        }
    }

    /**
     * 清除所有日切时间报警任务
     */
    private void clearAllCutTimeAlarmJobs() {
        try {
            // 获取所有属于日切时间报警组的任务
            Set<JobKey> jobKeys = scheduler.getJobKeys(GroupMatcher.jobGroupEquals(JOB_GROUP));
            
            for (JobKey jobKey : jobKeys) {
                scheduler.deleteJob(jobKey);
                LOGGER.debug("已删除日切时间报警任务: " + jobKey.getName());
            }
            
            LOGGER.info("已清除 " + jobKeys.size() + " 个日切时间报警任务");
            
        } catch (SchedulerException e) {
            LOGGER.error("清除日切时间报警任务失败: " + e.getMessage(), e);
        }
    }

    /**
     * 添加单个日切时间的定时任务
     * @param cutTime 日切时间，格式为HH:mm
     */
    public void addCutTimeAlarmJob(String cutTime) {
        if (cutTime == null || cutTime.trim().isEmpty()) {
            LOGGER.warn("日切时间为空，跳过添加任务");
            return;
        }

        try {
            Date serverStartTime = new Date();
            createCutTimeAlarmJob(cutTime.trim(), serverStartTime);
            LOGGER.info("成功添加日切时间报警任务: " + cutTime);
        } catch (Exception e) {
            LOGGER.error("添加日切时间报警任务失败，日切时间: " + cutTime + ", 错误: " + e.getMessage(), e);
        }
    }

    /**
     * 移除单个日切时间的定时任务
     * @param cutTime 日切时间，格式为HH:mm
     */
    public void removeCutTimeAlarmJob(String cutTime) {
        if (cutTime == null || cutTime.trim().isEmpty()) {
            LOGGER.warn("日切时间为空，跳过移除任务");
            return;
        }

        try {
            String jobName = "CutTimeAlarmJob_" + cutTime.replace(":", "");
            JobKey jobKey = new JobKey(jobName, JOB_GROUP);

            if (scheduler.checkExists(jobKey)) {
                scheduler.deleteJob(jobKey);
                LOGGER.info("成功移除日切时间报警任务: " + cutTime);
            } else {
                LOGGER.debug("日切时间报警任务不存在，无需移除: " + cutTime);
            }
        } catch (Exception e) {
            LOGGER.error("移除日切时间报警任务失败，日切时间: " + cutTime + ", 错误: " + e.getMessage(), e);
        }
    }

    /**
     * 更新单个日切时间的定时任务
     * @param oldCutTime 原日切时间
     * @param newCutTime 新日切时间
     */
    public void updateCutTimeAlarmJob(String oldCutTime, String newCutTime) {
        try {
            // 如果新旧时间相同，无需更新
            if (oldCutTime != null && oldCutTime.equals(newCutTime)) {
                return;
            }

            // 移除旧任务
            if (oldCutTime != null && !oldCutTime.trim().isEmpty()) {
                removeCutTimeAlarmJob(oldCutTime);
            }

            // 添加新任务
            if (newCutTime != null && !newCutTime.trim().isEmpty()) {
                addCutTimeAlarmJob(newCutTime);
            }

            LOGGER.info("成功更新日切时间报警任务: " + oldCutTime + " -> " + newCutTime);
        } catch (Exception e) {
            LOGGER.error("更新日切时间报警任务失败，原时间: " + oldCutTime + ", 新时间: " + newCutTime + ", 错误: " + e.getMessage(), e);
        }
    }

    /**
     * 检查指定日切时间的任务是否存在
     * @param cutTime 日切时间
     * @return true表示任务存在，false表示不存在
     */
    public boolean isCutTimeAlarmJobExists(String cutTime) {
        if (cutTime == null || cutTime.trim().isEmpty()) {
            return false;
        }

        try {
            String jobName = "CutTimeAlarmJob_" + cutTime.replace(":", "");
            JobKey jobKey = new JobKey(jobName, JOB_GROUP);
            return scheduler.checkExists(jobKey);
        } catch (Exception e) {
            LOGGER.error("检查日切时间报警任务是否存在失败: " + e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取所有当前活跃的日切时间
     * @return 日切时间集合
     */
    public Set<String> getActiveCutTimes() {
        Set<String> cutTimes = new HashSet<>();
        try {
            Set<JobKey> jobKeys = scheduler.getJobKeys(GroupMatcher.jobGroupEquals(JOB_GROUP));
            for (JobKey jobKey : jobKeys) {
                String jobName = jobKey.getName();
                if (jobName.startsWith("CutTimeAlarmJob_")) {
                    String cutTime = jobName.substring("CutTimeAlarmJob_".length());
                    // 将HHMM格式转换回HH:mm格式
                    if (cutTime.length() == 4) {
                        cutTime = cutTime.substring(0, 2) + ":" + cutTime.substring(2);
                        cutTimes.add(cutTime);
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error("获取活跃日切时间失败: " + e.getMessage(), e);
        }
        return cutTimes;
    }

    /**
     * 获取调度器状态信息
     */
    public String getSchedulerStatus() {
        try {
            if (scheduler == null) {
                return "调度器未初始化";
            }
            
            StringBuilder status = new StringBuilder();
            status.append("调度器状态: ").append(scheduler.isStarted() ? "运行中" : "已停止").append("\n");
            
            Set<JobKey> jobKeys = scheduler.getJobKeys(GroupMatcher.jobGroupEquals(JOB_GROUP));
            status.append("活跃任务数: ").append(jobKeys.size()).append("\n");
            
            for (JobKey jobKey : jobKeys) {
                List<? extends Trigger> triggers = scheduler.getTriggersOfJob(jobKey);
                if (!triggers.isEmpty()) {
                    Date nextFireTime = triggers.get(0).getNextFireTime();
                    status.append("任务 ").append(jobKey.getName())
                          .append(" 下次执行: ").append(nextFireTime != null ? 
                          new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(nextFireTime) : "未知")
                          .append("\n");
                }
            }
            
            return status.toString();
            
        } catch (SchedulerException e) {
            return "获取调度器状态失败: " + e.getMessage();
        }
    }
}
