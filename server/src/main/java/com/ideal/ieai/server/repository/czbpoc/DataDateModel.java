package com.ideal.ieai.server.repository.czbpoc;

public class DataDateModel
{
    private long   iid;
    private String isystemName;
    private String idataDate;
    private String   iupdateTime;
    private String icallTime;
    private String ibusinessDate;
    private String ioffset;
    private String icutTime;  // 日切时间，格式HH:mm

    public String getIcallTime() {
        return icallTime;
    }

    public void setIcallTime(String icallTime) {
        this.icallTime = icallTime;
    }

    public String getIbusinessDate() {
        return ibusinessDate;
    }

    public void setIbusinessDate(String ibusinessDate) {
        this.ibusinessDate = ibusinessDate;
    }

    public String getIoffset() {
        return ioffset;
    }

    public void setIoffset(String ioffset) {
        this.ioffset = ioffset;
    }

    public long getIid() {
        return iid;
    }

    public void setIid(long iid) {
        this.iid = iid;
    }

    public String getIsystemName() {
        return isystemName;
    }

    public void setIsystemName(String isystemName) {
        this.isystemName = isystemName;
    }

    public String getIdataDate() {
        return idataDate;
    }

    public void setIdataDate(String idataDate) {
        this.idataDate = idataDate;
    }

    public String getIupdateTime() {
        return iupdateTime;
    }

    public void setIupdateTime(String iupdateTime) {
        this.iupdateTime = iupdateTime;
    }

    public String getIcutTime() {
        return icutTime;
    }

    public void setIcutTime(String icutTime) {
        this.icutTime = icutTime;
    }
}
