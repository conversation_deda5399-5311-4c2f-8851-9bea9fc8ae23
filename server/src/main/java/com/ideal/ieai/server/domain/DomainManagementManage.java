package com.ideal.ieai.server.domain;

import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.core.JudgeDB;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBResource;
import org.apache.log4j.Logger;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * @ClassName:  DomainManagementManage
 * @Description: 域管理数据访问层
 * @author: system
 * @date:   2024年12月19日
 *
 * @Copyright: 2024-2027 www.idealinfo.com Inc. All rights reserved.
 *
 */
public class DomainManagementManage
{
    private Logger log = Logger.getLogger(DomainManagementManage.class);
    
    public List getDomainManagementList ( Connection conn, String sql, Integer start, Integer limit ) throws RepositoryException
    {
        List list = new ArrayList();
        PreparedStatement ps = null;
        ResultSet rs = null;
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try
        {
            ps = conn.prepareStatement(sql);
            if (JudgeDB.IEAI_DB_TYPE == 3)
            {
                ps.setInt(1, start);
                ps.setInt(2, limit);
            } else
            {
                ps.setInt(1, start);
                ps.setInt(2, start + limit);
            }
            rs = ps.executeQuery();
            while (rs.next())
            {
                DomainManagementModel model = new DomainManagementModel();
                model.setIid(rs.getLong("iid"));
                model.setIname(rs.getString("iname"));
                model.setIdesc(rs.getString("idesc"));
                model.setIcreatorid(rs.getLong("icreatorid"));
                model.setIcreatorname(rs.getString("icreatorname"));
                model.setIcreatetime(format.format(rs.getLong("icreatetime")));
                list.add(model);
            }
        } catch (Exception e)
        {
            log.error("DomainManagementManage.getDomainManagementList is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getDomainManagementList", log);
        }
        return list;
    }
    
    public int getDomainManagementCount ( Connection conn, String sql ) throws RepositoryException
    {
        int count = 0;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next())
            {
                count = rs.getInt("countNum");
            }
        } catch (Exception e)
        {
            log.error("DomainManagementManage.getDomainManagementCount is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getDomainManagementCount", log);
        }
        return count;
    }
    
    public DomainManagementModel getDomainManagementOne ( long iid, Connection conn) throws RepositoryException
    {
        DomainManagementModel model = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            String sql = "select iid, iname, idesc, icreatorid, icreatorname, icreatetime from ieai_domain_management where iid=?";
            ps = conn.prepareStatement(sql);
            ps.setLong(1, iid);
            rs = ps.executeQuery();
            while (rs.next())
            {
                model = new DomainManagementModel();
                model.setIid(rs.getLong("iid"));
                model.setIname(rs.getString("iname"));
                model.setIdesc(rs.getString("idesc"));
                model.setIcreatorid(rs.getLong("icreatorid"));
                model.setIcreatorname(rs.getString("icreatorname"));
                model.setIcreatetime(String.valueOf(rs.getLong("icreatetime")));
            }
        } catch (Exception e)
        {
            log.error("DomainManagementManage.getDomainManagementOne is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getDomainManagementOne", log);
        }
        return model;
    }
    
    // ==================== 服务器相关方法 ====================
    
    public List getDomainServerList(Connection conn, String sql, Integer start, Integer limit) throws RepositoryException {
        List list = new ArrayList();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = conn.prepareStatement(sql);
            if (JudgeDB.IEAI_DB_TYPE == 3) {
                ps.setInt(1, start);
                ps.setInt(2, limit);
            } else {
                ps.setInt(1, start);
                ps.setInt(2, start + limit);
            }
            rs = ps.executeQuery();
            while (rs.next()) {
                DomainServerModel model = new DomainServerModel();
                model.setIid(rs.getLong("iid"));
                model.setDomainId(rs.getLong("domainId"));
                model.setServerId(rs.getLong("serverId"));
                model.setServerIp(rs.getString("serverIp"));
                model.setServerName(rs.getString("serverName"));
                model.setServerPort(rs.getString("serverPort"));
                list.add(model);
            }
        } catch (Exception e) {
            log.error("DomainManagementManage.getDomainServerList is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally {
            DBResource.closePSRS(rs, ps, "getDomainServerList", log);
        }
        return list;
    }
    
    public int getDomainServerCount(Connection conn, String sql) throws RepositoryException {
        int count = 0;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next()) {
                count = rs.getInt("countNum");
            }
        } catch (Exception e) {
            log.error("DomainManagementManage.getDomainServerCount is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally {
            DBResource.closePSRS(rs, ps, "getDomainServerCount", log);
        }
        return count;
    }
    
    public List getAvailableServerList(Connection conn, String sql, Integer start, Integer limit) throws RepositoryException {
        List list = new ArrayList();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = conn.prepareStatement(sql);
            if (JudgeDB.IEAI_DB_TYPE == 3) {
                ps.setInt(1, start);
                ps.setInt(2, limit);
            } else {
                ps.setInt(1, start);
                ps.setInt(2, start + limit);
            }
            rs = ps.executeQuery();
            while (rs.next()) {
                DomainServerModel model = new DomainServerModel();
                model.setIid(rs.getLong("iid"));
                model.setServerIp(rs.getString("serverIp"));
                model.setServerName(rs.getString("serverName"));
                model.setServerPort(rs.getString("serverPort"));
                list.add(model);
            }
        } catch (Exception e) {
            log.error("DomainManagementManage.getAvailableServerList is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally {
            DBResource.closePSRS(rs, ps, "getAvailableServerList", log);
        }
        return list;
    }
    
    public int getAvailableServerCount(Connection conn, String sql) throws RepositoryException {
        int count = 0;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next()) {
                count = rs.getInt("countNum");
            }
        } catch (Exception e) {
            log.error("DomainManagementManage.getAvailableServerCount is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally {
            DBResource.closePSRS(rs, ps, "getAvailableServerCount", log);
        }
        return count;
    }
    
    // ==================== 业务系统相关方法 ====================
    
    public List getDomainBusinessList(Connection conn, String sql, Integer start, Integer limit) throws RepositoryException {
        List list = new ArrayList();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = conn.prepareStatement(sql);
            if (JudgeDB.IEAI_DB_TYPE == 3) {
                ps.setInt(1, start);
                ps.setInt(2, limit);
            } else {
                ps.setInt(1, start);
                ps.setInt(2, start + limit);
            }
            rs = ps.executeQuery();
            while (rs.next()) {
                DomainBusinessModel model = new DomainBusinessModel();
                model.setIid(rs.getLong("iid"));
                model.setDomainId(rs.getLong("domainId"));
                model.setBusinessId(rs.getLong("businessId"));
                model.setBusinessName(rs.getString("businessName"));
                model.setBusinessCode(rs.getString("businessCode"));
                list.add(model);
            }
        } catch (Exception e) {
            log.error("DomainManagementManage.getDomainBusinessList is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally {
            DBResource.closePSRS(rs, ps, "getDomainBusinessList", log);
        }
        return list;
    }
    
    public int getDomainBusinessCount(Connection conn, String sql) throws RepositoryException {
        int count = 0;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next()) {
                count = rs.getInt("countNum");
            }
        } catch (Exception e) {
            log.error("DomainManagementManage.getDomainBusinessCount is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally {
            DBResource.closePSRS(rs, ps, "getDomainBusinessCount", log);
        }
        return count;
    }
    
    public List getAvailableBusinessList(Connection conn, String sql, Integer start, Integer limit) throws RepositoryException {
        List list = new ArrayList();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = conn.prepareStatement(sql);
            if (JudgeDB.IEAI_DB_TYPE == 3) {
                ps.setInt(1, start);
                ps.setInt(2, limit);
            } else {
                ps.setInt(1, start);
                ps.setInt(2, start + limit);
            }
            rs = ps.executeQuery();
            while (rs.next()) {
                DomainBusinessModel model = new DomainBusinessModel();
                model.setIid(rs.getLong("iid"));
                model.setBusinessName(rs.getString("businessName"));
                model.setBusinessCode(rs.getString("businessCode"));
                model.setType(rs.getInt("PROTYPE"));
                list.add(model);
            }
        } catch (Exception e) {
            log.error("DomainManagementManage.getAvailableBusinessList is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally {
            DBResource.closePSRS(rs, ps, "getAvailableBusinessList", log);
        }
        return list;
    }
    
    public int getAvailableBusinessCount(Connection conn, String sql) throws RepositoryException {
        int count = 0;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next()) {
                count = rs.getInt("countNum");
            }
        } catch (Exception e) {
            log.error("DomainManagementManage.getAvailableBusinessCount is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally {
            DBResource.closePSRS(rs, ps, "getAvailableBusinessCount", log);
        }
        return count;
    }
}
