package com.ideal.ieai.server.toposerver.thread;

import com.ideal.ieai.core.Environment;
import com.ideal.ieai.server.ieaikernel.ConfigReader;
import com.ideal.ieai.server.ieaikernel.ServerEnv;
import com.ideal.ieai.server.repository.topo.avgtime.AvgTimeManager;
import com.ideal.ieai.server.repository.topo.toposys.TopoMidDataManager;
import org.apache.log4j.Logger;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * <ul>
 * <li>Title: AvgTimeThread.java</li>
 * <li>Description:计算活动平均耗时并写入耗时表</li>
 * <li>Copyright: Copyright 2003</li>
 * <li>Company: ideal</li>
 * </ul>
 * 
 * <AUTHOR> 
 * 
 */

public class AvgTimeThread extends Thread
{

    private Logger _log = Logger.getLogger("topoLog");

    /**
     * 是否计算平均耗时的开关
     */
    @Override
    public void run ()
    {
        _log.info("AvgTimeThread  initialization succeeded!");
        while (true)
        {
            getMethod();
            try
            {
                Thread.sleep(120000);
            } catch (Exception e)
            {
                e.printStackTrace();
            }
        }

//        try
//        {
//            Thread.sleep(5000);
//        } catch (Exception e1)
//        {
//            e1.printStackTrace();
//        }
//        while (true)
//        {
//            boolean openswitch = false;
//            ConfigReader cr = ConfigReader.getInstance();
//            long specTime = getNextRuntime().getTime();
//            String setTime = "";
//            String thour = "";
//            String tminute = "";
//            try
//            {
//                cr.init();
//                openswitch = cr.getBooleanProperties(ServerEnv.AVG_SWITCH, false);// getSwitch();
//                setTime = cr.getProperties(ServerEnv.AVG_SETTTIME, "00:01");
//                // AvgTimeManager manager = new AvgTimeManager();
//                // setTime = manager.isConfig(ServerEnv.AVG_SETTTIME, "00:01");
//                thour = setTime.split(":")[0];
//                tminute = setTime.split(":")[1];
//                Calendar calendar = Calendar.getInstance();
//                calendar.set(Calendar.HOUR_OF_DAY, Integer.parseInt(thour));
//                calendar.set(Calendar.MINUTE, Integer.parseInt(tminute));
//                calendar.set(Calendar.SECOND, 0);
//                specTime = calendar.getTime().getTime();
//            } catch (IOException e2)
//            {
//                e2.printStackTrace();
//            }
//            // 取运行时间参数
//            long now = System.currentTimeMillis();
//            Calendar delay = Calendar.getInstance();
//            if (now < specTime)
//            {
//                try
//                {
//                    _log.info("AvgTime  switch  is " + openswitch + " ! sleep:" + (specTime - now));
//                    Thread.sleep(specTime - now);
//                } catch (Exception e)
//                {
//                    _log.info("error when sleep in AvgTimeThread!" + e);
//                }
//                if (openswitch)
//                {
//                    try
//                    {
//                        cr.init();
//                    } catch (IOException e1)
//                    {
//                        _log.error("init WarnConfig error!Maybe the file not exits!", e1);
//                    }
//                    getMethod();
//                    try
//                    {
//                        Thread.sleep(60000);
//                    } catch (Exception e)
//                    {
//                        e.printStackTrace();
//                    }
//                }
//            } else
//            {
//                delay.setTimeInMillis(specTime);
//                delay.add(Calendar.DATE, 1);
//                try
//                {
//                    _log.info(
//                        "AvgTime  switch  is " + openswitch + " ! next sleep:" + ((delay.getTimeInMillis()) - now));
//                    Thread.sleep(delay.getTimeInMillis() - now);
//                } catch (Exception e)
//                {
//                    _log.info("error when sleep in AvgTimeThread!" + e);
//                }
//                if (openswitch)
//                {
//                    getMethod();
//                    try
//                    {
//                        Thread.sleep(60000);
//                    } catch (Exception e)
//                    {
//                        e.printStackTrace();
//                    }
//                }
//            }
//        }
    }

    public Date getNextRuntime ()
    {
        String setTime = "";
        String thour = "";
        String tminute = "";
        AvgTimeManager manager = new AvgTimeManager();
        setTime = manager.isConfig(ServerEnv.AVG_SETTTIME, "00:01");
        thour = setTime.split(":")[0];
        tminute = setTime.split(":")[1];
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, Integer.parseInt(thour));
        calendar.set(Calendar.MINUTE, Integer.parseInt(tminute));
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTime();
    }

    /**
     * getMethod:计算平均耗时和设置报警阀值. <br/>
     * 
     * <AUTHOR>
     * @return void
     * @since JDK 1.4
     */
    private void getMethod ()
    {
        _log.info("start getAvgTime");
        AvgTimeManager manager = new AvgTimeManager();
        boolean failNumStatic = false;
        /* 最多统计条数 */
        int maxItem;
        ConfigReader cr = ConfigReader.getInstance();
        try
        {
            cr.init();
        } catch (IOException e1)
        {
            e1.printStackTrace();
        }
        String analyModel = cr.getProperties(ServerEnv.TOPO_ANALY_MODEL, String.valueOf(ServerEnv.TOPO_ANALYMODEL_DEFAULT));
        boolean sysRuntimeSwitch = cr.getBooleanProperties("sysAvgTimeClose", false);
        String bankSwitch = cr.getProperties(ServerEnv.BANK_SWITCH, String.valueOf(ServerEnv.BANK_SWITCH_DEFAULT));
        if ("1".equals(bankSwitch))
        {
            maxItem = Integer.parseInt(manager.isConfig(Environment.AVG_ACTRUN_DAY, "180"));
        } else
        {
            maxItem = Integer.parseInt(manager.isConfig(Environment.AVG_ACTRUN_MONTH, "18"));
        }
        maxItem = Integer.parseInt(manager.isConfig(Environment.AVG_ACTRUN_DAY, "180"));
        failNumStatic = cr.getProperties(Environment.AVG_ACTRUN_FAILNUMSTATIC, "false").equals("true");
        // 平均耗时计算
        _log.info("Start statis AvgTime!");
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, -maxItem);// 日期减 如果不够减会将月变动
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        long validDay = calendar.getTimeInMillis();
        boolean hisCon = ServerEnv.getInstance().getBooleanConfig(ServerEnv.HISTORY_SWITCH,
            ServerEnv.HISTORY_SWITCH_DEFAULT);
        if (sysRuntimeSwitch)
        {
            TopoMidDataManager topoMidDataManager = new TopoMidDataManager();
            boolean topoSwitch = topoMidDataManager.getActTpins();
            if(topoSwitch){
                manager.getCountAvgSys(validDay);
            }else{
                manager.staticAvgTimeSys(validDay);
            }
        }
        if ("2".equals(analyModel))
        {
            manager.staticAvgTime(validDay, 0, 0, 0, 0, 0, 0, hisCon);
        } else if ("3".equals(analyModel))
        {
            int monthtime = Integer.parseInt(cr.getProperties(Environment.AVG_NUM_MONTH, "3"));
            int weektime = Integer.parseInt(cr.getProperties(Environment.AVG_NUM_WEEK, "5"));
            int daytime = Integer.parseInt(cr.getProperties(Environment.AVG_NUM_DAY, "7"));
            int deftime = Integer.parseInt(cr.getProperties(Environment.AVG_NUM_DEF, "7"));
            manager.staticAvgTime(maxItem, 0, monthtime, weektime, daytime, deftime, 2, hisCon);
        } else
        {
            int monthtime = Integer.parseInt(cr.getProperties(Environment.AVG_NUM_MONTH, "3"));
            int weektime = Integer.parseInt(cr.getProperties(Environment.AVG_NUM_WEEK, "5"));
            int daytime = Integer.parseInt(cr.getProperties(Environment.AVG_NUM_DAY, "7"));
            int deftime = Integer.parseInt(cr.getProperties(Environment.AVG_NUM_DEF, "7"));

            boolean showName = Boolean.parseBoolean(cr.getProperties(ServerEnv.LAST_DEEP_CALLACTNAME,
                String.valueOf(ServerEnv.LAST_DEEP_CALLACTNAME_DEFAULT)));
            if (showName)
            {
                manager.staticAvgTime(maxItem, 0, monthtime, weektime, daytime, deftime, 4, hisCon);
            } else
            {
                manager.staticAvgTime(maxItem, 0, monthtime, weektime, daytime, deftime, 1, hisCon);
            }
            // 平均报错数计算
            long startfailMini = System.currentTimeMillis();
            _log.info("start fail num switch is" + failNumStatic);
            if (failNumStatic)
            {
                manager.staticAvgFailNum();
            }
            _log.info("end fail num time is " + (System.currentTimeMillis() - startfailMini));
        }
        _log.info("End statis AvgTime!");
    }

    public boolean getSwitch () throws IOException
    {
        AvgTimeManager manager = new AvgTimeManager();
        boolean avgTime = Boolean.parseBoolean(manager.isConfig(ServerEnv.AVG_SWITCH, "false"));
        _log.info("AvgTimeThread switch:" + avgTime);
        return avgTime;

    }

    /**
     * @param args 测试方法
     */
    public static void main ( String[] args )
    {
        try
        {

            SimpleDateFormat dft = new SimpleDateFormat("yyyyMMdd");
            Date beginDate = new Date();
            Calendar date = Calendar.getInstance();
            date.setTime(beginDate);
            date.set(Calendar.DATE, date.get(Calendar.DATE) - 1 - 1);
            int strdate = Integer.parseInt(dft.format(date.getTime()));

            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DATE, -20);// 日期减 如果不够减会将月变动
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            int mill = Integer.parseInt(dft.format(calendar.getTime()));

            long validDay = calendar.getTimeInMillis();
            AvgTimeThread thread = new AvgTimeThread();
            thread.run();
        } catch (Exception e2)
        {
            e2.printStackTrace();
        }
    }
}
