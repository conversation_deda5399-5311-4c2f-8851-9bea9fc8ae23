package com.ideal.ieai.server.domain;

public class ProjectModel {

    private long iid;
    private String iname;
    private String isyscode;
    private long idomainId;
    private double protype;
    private long iupperId;
    private long ilatestId;

    public long getIid() {
        return iid;
    }

    public void setIid(long iid) {
        this.iid = iid;
    }

    public String getIname() {
        return iname;
    }

    public void setIname(String iname) {
        this.iname = iname;
    }

    public String getIsyscode() {
        return isyscode;
    }

    public void setIsyscode(String isyscode) {
        this.isyscode = isyscode;
    }

    public long getIdomainId() {
        return idomainId;
    }

    public void setIdomainId(long idomainId) {
        this.idomainId = idomainId;
    }

    public double getProtype() {
        return protype;
    }

    public void setProtype(double protype) {
        this.protype = protype;
    }

    public long getIupperId() {
        return iupperId;
    }

    public void setIupperId(long iupperId) {
        this.iupperId = iupperId;
    }

    public long getIlatestId() {
        return ilatestId;
    }

    public void setIlatestId(long ilatestId) {
        this.ilatestId = ilatestId;
    }
}
