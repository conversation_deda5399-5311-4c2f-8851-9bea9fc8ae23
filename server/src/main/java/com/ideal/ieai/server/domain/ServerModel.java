package com.ideal.ieai.server.domain;

public class ServerModel {

    private long id;
    private String ip;
    private long groupId;
    private int state;
    private String lastTime;
    private String des;
    private int dispose;
    private String icpu;
    private String imemory;
    private long iexeTaskCnt;
    private int imainServer;
    private long inetworkServer;
    private String idisk;
    private long idomainId;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public long getGroupId() {
        return groupId;
    }

    public void setGroupId(long groupId) {
        this.groupId = groupId;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public String getLastTime() {
        return lastTime;
    }

    public void setLastTime(String lastTime) {
        this.lastTime = lastTime;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }

    public int getDispose() {
        return dispose;
    }

    public void setDispose(int dispose) {
        this.dispose = dispose;
    }

    public String getIcpu() {
        return icpu;
    }

    public void setIcpu(String icpu) {
        this.icpu = icpu;
    }

    public String getImemory() {
        return imemory;
    }

    public void setImemory(String imemory) {
        this.imemory = imemory;
    }

    public long getIexeTaskCnt() {
        return iexeTaskCnt;
    }

    public void setIexeTaskCnt(long iexeTaskCnt) {
        this.iexeTaskCnt = iexeTaskCnt;
    }

    public int getImainServer() {
        return imainServer;
    }

    public void setImainServer(int imainServer) {
        this.imainServer = imainServer;
    }

    public long getInetworkServer() {
        return inetworkServer;
    }

    public void setInetworkServer(long inetworkServer) {
        this.inetworkServer = inetworkServer;
    }

    public String getIdisk() {
        return idisk;
    }

    public void setIdisk(String idisk) {
        this.idisk = idisk;
    }

    public long getIdomainId() {
        return idomainId;
    }

    public void setIdomainId(long idomainId) {
        this.idomainId = idomainId;
    }
}
