package com.ideal.ieai.server.jobscheduling.util.taskdownloadbean;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class ActRelationInfo implements Serializable
{
    private String              id;                                   // 工程ID

    private String              projectname;                          // 工程名称

    private String              flowName;                             // 工作流名称

    private String              actName;                              // 活动名称

    private String              actID;

    private String              derivaed;                             // 导出

    private int                 pageSize;                            // 每页显示信息个数

    private int                 curPage;                             // 当前页数

    private boolean             canPage;                             // 是否分页

    private String              nextAct;                              // 后续活动

    private String              flowDes;                              // 工作流描述

    private String              flowNote;                             // 工作流备注

    private String              flowDef;                              // 活动defid

    // 所属系统类型
    private String              system;
    // 业务异常自动重试
    private String              redo;

    private List<Long>          preActID     = new ArrayList<Long>(0);

    private List<Long>          nextActID    = new ArrayList<Long>(0);

    private List<Long>          preActIndex  = new ArrayList<Long>(0);

    private List<Long>          nextActIndex = new ArrayList<Long>(0);
    private String              childproName;                         // 子系统名称
    private String              actDesc;                              // 作业描述
    private String              succAct;                              // 触发作业
    private String              preAct;                               // 依赖作业
    private String              flag;                                 // 是否删除
    private String              okFilePath;                           // OK文件绝对路径
    private String              okFileWeek;                           // OK文件检测周期
    private String              shellHouse;                           // 脚本外壳
    private String              shellName;                            // 脚本名称
    private String              outPara;                              // 输出参数
    private String              lastLine;                             // 脚本执行成功最后一行打印值
    private String              agentGroup;                           // Agent资源组
    private String              agentCheckGroup;                      // Agent资源组

    private String              mainLineName;                         // 主线名称
    private String              headFlag;                             // 头尾标记

    private String              weights;
    private String              priority;

    // APT 接口 update by liyang
    private String              aptGroupName;                         // APT组名称
    private String              aptFileName;                          // APT文件名称
    private String              isdb2;
    private String              db2ip;
    private String              aptResGroupName;                      // APT资源组名称
    private String              isDisabled;                           // 是否禁用
                                                                       // gg增加重试、是否是组模式、日历、分值条件，活动记录变量
    private String              agentInfo;

    private boolean             isAgentGroup = true;

    private String              delayTime;

    private String              branchCondition;

    private Map<String, String> allActMap;

    private String              reTryCount;

    private String              calendName;

    private int                 reTryTime;
    // 重试结束时间
    private String              reTryEndTime;
    // 自动忽略
    private String              skip;

    private String              delayWarnning;

    private String              succActNo;                              // 触发作业序号名
    private String              preActNo;                               // 依赖作业序号名

    private String           sequenceNumber;

    private String           actParams;

    private String           Jobtype;

    private List<String>              preActList;  //依赖作业列表
    private List<String>           succActList;  //触发作业列表
    private String              changePerson ;  //变更人
    private String              taskType;

    public String getChangePerson() {
        return changePerson;
    }

    public void setChangePerson(String changePerson) {
        this.changePerson = changePerson;
    }

    public String getTaskType() {
        return taskType;
    }

    public void setTaskType(String taskType) {
        this.taskType = taskType;
    }

    public void setAgentGroup(boolean agentGroup) {
        isAgentGroup = agentGroup;
    }

    public List<String> getPreActList() {
        return preActList;
    }

    public void setPreActList(List<String> preActList) {
        this.preActList = preActList;
    }

    public List<String> getSuccActList() {
        return succActList;
    }

    public void setSuccActList(List<String> succActList) {
        this.succActList = succActList;
    }

    public String getJobtype() {
        return Jobtype;
    }

    public void setJobtype(String jobtype) {
        Jobtype = jobtype;
    }

    public String getActParams ()
    {
        return actParams;
    }

    public void setActParams ( String actParams )
    {
        this.actParams = actParams;
    }

    public String getSequenceNumber ()
    {
        return sequenceNumber;
    }

    public void setSequenceNumber ( String sequenceNumber )
    {
        this.sequenceNumber = sequenceNumber;
    }

    public String getSuccActNo ()
    {
        return succActNo;
    }

    public void setSuccActNo ( String succActNo )
    {
        this.succActNo = succActNo;
    }

    public String getPreActNo ()
    {
        return preActNo;
    }

    public void setPreActNo ( String preActNo )
    {
        this.preActNo = preActNo;
    }

    private String days;//DAYS:指定批量日期执行1-31
    
    private String wdays;//WDAYS:指定星期执行0-6，批量对应的星期一至星期日
    
    private String logic;//LOGIC:条件逻辑符AND/OR，AND多个条件均满足，OR多个条件满足一个即可

    private String              month;//MONTH:指定月份执行1-12

    private String              performUser;  //执行用户
    private String              endTag;  //结束标识
    private int              userTask;  //人工提醒

    public int getUserTask() {
        return userTask;
    }

    public void setUserTask(int userTask) {
        this.userTask = userTask;
    }

    public String getEndTag() {
        return endTag;
    }

    public void setEndTag(String endTag) {
        this.endTag = endTag;
    }

    private List<String>          preActName     = new ArrayList<String>();

    private List<String>          nextActName    = new ArrayList<String>();

    public List<String> getPreActName ()
    {
        return preActName;
    }

    public void setPreActName ( List<String> preActName )
    {
        this.preActName = preActName;
    }

    public List<String> getNextActName ()
    {
        return nextActName;
    }

    public void setNextActName ( List<String> nextActName )
    {
        this.nextActName = nextActName;
    }

    public String getVirtualName() {
        return VirtualName;
    }

    public void setVirtualName(String virtualName) {
        VirtualName = virtualName;
    }

    private String              VirtualName;  //虚拟组名称
    public String getPerformUser() {
        return performUser;
    }

    public void setPerformUser(String performUser) {
        this.performUser = performUser;
    }
    public String getJoblist() {
        return Joblist;
    }

    public void setJoblist(String joblist) {
        Joblist = joblist;
    }
    // 作业序号
    private String              actNo;
    public String getActNo ()
    {
        return actNo;
    }

    public void setActNo ( String actNo )
    {
        this.actNo = actNo;
    }
    private String              Joblist;  //作业列表

    public String getOkFileName() {
        return okFileName;
    }

    public void setOkFileName(String okFileName) {
        this.okFileName = okFileName;
    }

    private String           okFileName;  //ok文件名称
    public String getDays ()
    {
        return days;
    }

    public void setDays ( String days )
    {
        this.days = days;
    }

    public String getWdays ()
    {
        return wdays;
    }

    public void setWdays ( String wdays )
    {
        this.wdays = wdays;
    }

    public String getLogic ()
    {
        return logic;
    }

    public void setLogic ( String logic )
    {
        this.logic = logic;
    }

    public String getMonth ()
    {
        return month;
    }

    public void setMonth ( String month )
    {
        this.month = month;
    }
    public int getReTryTime ()
    {
        return reTryTime;
    }

    public void setReTryTime ( int reTryTime )
    {
        this.reTryTime = reTryTime;
    }

    public String getReTryEndTime ()
    {
        return reTryEndTime;
    }

    public void setReTryEndTime ( String reTryEndTime )
    {
        this.reTryEndTime = reTryEndTime;
    }

    public String getSkip ()
    {
        return skip;
    }

    public void setSkip ( String skip )
    {
        this.skip = skip;
    }

    public String getDelayWarnning ()
    {
        return delayWarnning;
    }

    public void setDelayWarnning ( String delayWarnning )
    {
        this.delayWarnning = delayWarnning;
    }

    public String getAgentInfo ()
    {
        return agentInfo;
    }

    public void setAgentInfo ( String agentInfo )
    {
        this.agentInfo = agentInfo;
    }

    public boolean isAgentGroup ()
    {
        return isAgentGroup;
    }

    public void setIsAgentGroup ( boolean isAgentGroup )
    {
        this.isAgentGroup = isAgentGroup;
    }

    public String getDelayTime ()
    {
        return delayTime;
    }

    public void setDelayTime ( String delayTime )
    {
        this.delayTime = delayTime;
    }

    public String getBranchCondition ()
    {
        return branchCondition;
    }

    public void setBranchCondition ( String branchCondition )
    {
        this.branchCondition = branchCondition;
    }

    public Map<String, String> getAllActMap ()
    {
        return allActMap;
    }

    public void setAllActMap ( Map<String, String> allActMap )
    {
        this.allActMap = allActMap;
    }

    public String getReTryCount ()
    {
        return reTryCount;
    }

    public void setReTryCount ( String reTryCount )
    {
        this.reTryCount = reTryCount;
    }

    public String getCalendName ()
    {
        return calendName;
    }

    public void setCalendName ( String calendName )
    {
        this.calendName = calendName;
    }

    public String getAgentCheckGroup ()
    {
        return agentCheckGroup;
    }

    public void setAgentCheckGroup ( String agentCheckGroup )
    {
        this.agentCheckGroup = agentCheckGroup;
    }

    public String getIsDisabled ()
    {
        return isDisabled;
    }

    public void setIsDisabled ( String isDisabled )
    {
        this.isDisabled = isDisabled;
    }

    public String getAptGroupName ()
    {
        return aptGroupName;
    }

    public void setAptGroupName ( String aptGroupName )
    {
        this.aptGroupName = aptGroupName;
    }

    public String getAptFileName ()
    {
        return aptFileName;
    }

    public void setAptFileName ( String aptFileName )
    {
        this.aptFileName = aptFileName;
    }

    public String getIsdb2 ()
    {
        return isdb2;
    }

    public void setIsdb2 ( String isdb2 )
    {
        this.isdb2 = isdb2;
    }

    public String getDb2ip ()
    {
        return db2ip;
    }

    public void setDb2ip ( String db2ip )
    {
        this.db2ip = db2ip;
    }

    public String getAptResGroupName ()
    {
        return aptResGroupName;
    }

    public void setAptResGroupName ( String aptResGroupName )
    {
        this.aptResGroupName = aptResGroupName;
    }

    // end update

    public String getId ()
    {
        return id;
    }

    public void setId ( String id )
    {
        this.id = id;
    }

    public String getProjectname ()
    {
        return projectname;
    }

    public void setProjectname ( String projectname )
    {
        this.projectname = projectname;
    }

    public String getFlowName ()
    {
        return flowName;
    }

    public void setFlowName ( String flowName )
    {
        this.flowName = flowName;
    }

    public String getActName ()
    {
        return actName;
    }

    public void setActName ( String actName )
    {
        this.actName = actName;
    }

    public String getActID ()
    {
        return actID;
    }

    public void setActID ( String actID )
    {
        this.actID = actID;
    }

    public String getDerivaed ()
    {
        return derivaed;
    }

    public void setDerivaed ( String derivaed )
    {
        this.derivaed = derivaed;
    }

    public int getPageSize ()
    {
        return pageSize;
    }

    public void setPageSize ( int pageSize )
    {
        this.pageSize = pageSize;
    }

    public int getCurPage ()
    {
        return curPage;
    }

    public void setCurPage ( int curPage )
    {
        this.curPage = curPage;
    }

    public boolean isCanPage ()
    {
        return canPage;
    }

    public void setCanPage ( boolean canPage )
    {
        this.canPage = canPage;
    }

    public String getNextAct ()
    {
        return nextAct;
    }

    public void setNextAct ( String nextAct )
    {
        this.nextAct = nextAct;
    }

    public String getFlowDes ()
    {
        return flowDes;
    }

    public void setFlowDes ( String flowDes )
    {
        this.flowDes = flowDes;
    }

    public String getFlowNote ()
    {
        return flowNote;
    }

    public void setFlowNote ( String flowNote )
    {
        this.flowNote = flowNote;
    }

    public String getFlowDef ()
    {
        return flowDef;
    }

    public void setFlowDef ( String flowDef )
    {
        this.flowDef = flowDef;
    }

    public List<Long> getPreActID ()
    {
        return preActID;
    }

    public void setPreActID ( List<Long> preActID )
    {
        this.preActID = preActID;
    }

    public List<Long> getNextActID ()
    {
        return nextActID;
    }

    public void setNextActID ( List<Long> nextActID )
    {
        this.nextActID = nextActID;
    }

    public List<Long> getPreActIndex ()
    {
        return preActIndex;
    }

    public void setPreActIndex ( List<Long> preActIndex )
    {
        this.preActIndex = preActIndex;
    }

    public List<Long> getNextActIndex ()
    {
        return nextActIndex;
    }

    public void setNextActIndex ( List<Long> nextActIndex )
    {
        this.nextActIndex = nextActIndex;
    }

    public String getChildproName ()
    {
        return childproName;
    }

    public void setChildproName ( String childproName )
    {
        this.childproName = childproName;
    }

    public String getActDesc ()
    {
        return actDesc;
    }

    public void setActDesc ( String actDesc )
    {
        this.actDesc = actDesc;
    }

    public String getSuccAct ()
    {
        return succAct;
    }

    public void setSuccAct ( String succAct )
    {
        this.succAct = succAct;
    }

    public String getPreAct ()
    {
        return preAct;
    }

    public void setPreAct ( String preAct )
    {
        this.preAct = preAct;
    }

    public String getFlag ()
    {
        return flag;
    }

    public void setFlag ( String flag )
    {
        this.flag = flag;
    }

    public String getOkFilePath ()
    {
        return okFilePath;
    }

    public void setOkFilePath ( String okFilePath )
    {
        this.okFilePath = okFilePath;
    }

    public String getOkFileWeek ()
    {
        return okFileWeek;
    }

    public void setOkFileWeek ( String okFileWeek )
    {
        this.okFileWeek = okFileWeek;
    }

    public String getShellHouse ()
    {
        return shellHouse;
    }

    public void setShellHouse ( String shellHouse )
    {
        this.shellHouse = shellHouse;
    }

    public String getShellName ()
    {
        return shellName;
    }

    public void setShellName ( String shellName )
    {
        this.shellName = shellName;
    }

    public String getOutPara ()
    {
        return outPara;
    }

    public void setOutPara ( String outPara )
    {
        this.outPara = outPara;
    }

    public String getLastLine ()
    {
        return lastLine;
    }

    public void setLastLine ( String lastLine )
    {
        this.lastLine = lastLine;
    }

    public String getAgentGroup ()
    {
        return agentGroup;
    }

    public void setAgentGroup ( String agentGroup )
    {
        this.agentGroup = agentGroup;
    }

    public String getMainLineName ()
    {
        return mainLineName;
    }

    public void setMainLineName ( String mainLineName )
    {
        this.mainLineName = mainLineName;
    }

    public String getHeadFlag ()
    {
        return headFlag;
    }

    public void setHeadFlag ( String headFlag )
    {
        this.headFlag = headFlag;
    }

    public String getSystem ()
    {
        return system;
    }

    public void setSystem ( String system )
    {
        this.system = system;
    }

    public String getRedo ()
    {
        return redo;
    }

    public void setRedo ( String redo )
    {
        this.redo = redo;
    }

    public String getWeights ()
    {
        return weights;
    }

    public void setWeights ( String weights )
    {
        this.weights = weights;
    }

    public String getPriority ()
    {
        return priority;
    }

    public void setPriority ( String priority )
    {
        this.priority = priority;
    }
}
