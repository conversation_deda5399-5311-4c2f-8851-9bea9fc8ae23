package com.ideal.ieai.server.repository.db;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.core.JudgeDB;
import com.ideal.ieai.core.MonitorEnv;
import com.ideal.ieai.core.util.Jcrypt;
import com.ideal.ieai.server.ieaikernel.ServerEnv;
import com.ideal.ieai.server.jobscheduling.repository.platformmonitor.dbmonitor.DBMonitorBean;
import com.ideal.ieai.server.jobscheduling.util.DateUtil;
import com.ideal.ieai.server.platform.warnmanage.IeaiWarnModel;
import com.ideal.ieai.server.repository.DBUtil;
import com.ideal.ieai.server.repository.HibernateUtil;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.dbsource.DBSourceMgr;
import com.ideal.ieai.server.repository.monitor.SendCibSyslog;
import com.ideal.ieai.server.repository.monitor.WarningManager;
import com.ideal.ieai.server.util.WarningInterfaceUtilsPlatform;
import org.apache.commons.dbcp.BasicDataSource;
import org.apache.log4j.Logger;

import java.io.File;
import java.io.FileInputStream;
import java.math.BigDecimal;
import java.sql.*;
import java.util.Date;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <p>
 * Title: ieai server
 * </p>
 * <p>
 * Description:
 * </p>
 * <p>
 * Copyright: Copyright (c) 2003
 * </p>
 * <p>
 * Company: Ideal Technologies Inc.
 * </p>
 *
 * <AUTHOR> Ming
 * @version 3.0
 */

public final class DBManager
{
    /**
     * Logger for this class
     */
    private static final Logger _log = Logger.getLogger(DBManager.class);

    private DBManager()
    {
    }

    public static DBManager getInstance ()
    {
        if (null == _instance)
        {
            _instance = new DBManager();
        }
        return _instance;
    }
    public Connection getJdbcConnectWithoutLimit() throws SQLException {
        return _jdbcDataSource.getConnection();
    }
    /**
     *
     * @return JDBCConfig
     */
    public static DBConfig getDBConfig ()
    {
        return _dbCfg;
    }

    /**
     * Checks if the database specified by config is normal. This method was added by ywd at
     * 2003-4-12.
     *
     * @param config
     * @return true if the database is normal; false otherwise.
     */
    static public final void isDBNormal ( DBConfig config ) throws DBException
    {
        String driverClassPath = ServerEnv.getServerEnv().getIEAIHome() + "/lib";

        String datasource = config.getUrl();
        String driverClassName = config.getDriverClass();
        String userId = config.getDbUser();
        String passwd = config.getDbPassword();

        if (null == datasource || "".equals(datasource))
        {
            throw new DBException("Incorrect database config :db.url must config ");
        }

        if (null == driverClassName || "".equals(driverClassName))
        {
            throw new DBException("Incorrect database Driver class name:db.driver must config");
        }

        Class driverClass = null;
        DBDriverClassLoader _dbDriverClsLoader = DBDriverClassLoader.getDBDriverClassLoader(driverClassPath);
        try
        {
            driverClass = _dbDriverClsLoader.loadClass(driverClassName);
        } catch (ClassNotFoundException ex1)
        {
            throw new DBException("Failed loading JDBC Driver class:" + driverClassName, ex1);
        }

        if (!com.ideal.util.JDBCDriverWrapper.registerDriver(driverClass))
        {
            throw new DBException("Error registering JDBC Driver class:" + driverClassName);
        }

        try
        {
            java.util.Properties info = new java.util.Properties();
            info.put("user", userId);
            info.put("password", passwd);
            Connection con = DriverManager.getConnection(datasource, userId, passwd);
            if (con == null)
            {
                throw new DBException("can not get Connection from Database");
            }
            con.close();
        } catch (SQLException ex)
        {
            throw new DBException(ex.getMessage());
        }
    }

    /**
     * Initializes DBManager with the specified JDBCConfig. Notice this method can only be used with
     * classes in the same package.
     *
     * @param dbCfg JDBC config information
     * @throws DBException
     *
     */
    public static final synchronized void initDB ( DBConfig dbCfg, int from ) throws DBException
    {
        _log.info("Initializing DB layer...");
        String driverPath = ServerEnv.getServerEnv().getIEAIHome() + "/lib";
        String driverClassName = dbCfg.getDriverClass();
        _log.info("驱动名称:" + driverClassName);
        Class driverClass = null;
        DBDriverClassLoader dbDriverClsLoader = DBDriverClassLoader.getDBDriverClassLoader(driverPath);
        try
        {
            driverClass = dbDriverClsLoader.loadClass(driverClassName);
        } catch (ClassNotFoundException ex1)
        {
            throw new DBException("Failed loading JDBC Driver class:" + driverClassName, ex1);
        }
        if (!com.ideal.util.JDBCDriverWrapper.registerDriver(driverClass))
        {
            throw new DBException("Error registering JDBC Driver class:" + driverClassName);
        }

        try
        {
            DBManager.requestFrom = from;
            initDBConfigOrg();
            initConnectionPool(dbCfg);
        } catch (Exception ex)
        {
            throw new DBException(ex.getMessage());
        }
        // boolean hisCon = ServerEnv.getInstance().getBooleanConfig(ServerEnv.HISTORY_SWITCH,
        // ServerEnv.HISTORY_SWITCH_DEFAULT);
        // if (hisCon)
        // {
        // try
        // {
        // initSecondConnectionPool();
        // } catch (Exception ex)
        // {
        // _topoLog.debug("init secondDB error!");
        // }
        // }
        _dbCfg = dbCfg;

        // now call HibernateUtil.getInstance to initialize hibernate
        if (null == HibernateUtil.getInstance())
        {
            throw new DBException("Error initializing Hibernate!");
        }

        _log.info("DB layer initialization succeeded!");
    }

    /**
     * @Title: setBasicDBifNeeded
     * @Description: 首次没有设置数据源的库进行默认设置
     * @param dbCfg
     * @throws RepositoryException
     * @author: kaijia_ma
     * @date:   2017年11月1日 下午1:41:57
     */
    public void setBasicDBifNeeded ( DBConfig dbCfg ) throws RepositoryException
    {
        Connection con = null;
        PreparedStatement ps = null;
        PreparedStatement ps2 = null;
        ResultSet rs = null;

        DBSourceMgr mgr = new DBSourceMgr();

        String method = "setBasicDBifNeeded";

        if (mgr.isFirst())
        {
            // 组织更新语句
            String updateSQL1 = " UPDATE IEAI_DBSOURCE SET ISERVERIP=?, IDBURL=?, IDBUSER=?, IDBPASS=?";

            String updateSQL2 = " UPDATE IEAI_DBSOURCE SET IISBASIC=1 WHERE IDBSOURCEID=1 ";
            try
            {
                String serverIp = Environment.getInstance().getServerIP();
                con = DBResource.getConnection(method, _log, Constants.IEAI_IEAI_BASIC);
                ps2 = con.prepareStatement(updateSQL1);
                ps2.setString(1, serverIp);
                ps2.setString(2, dbCfg.getUrl());
                ps2.setString(3, dbCfg.getDbUser());
                ps2.setString(4, dbCfg.getDbPassword());
                ps2.executeUpdate();

                ps = con.prepareStatement(updateSQL2);
                ps.executeUpdate();

                con.commit();
            } catch (Exception e)
            {
                _log.error("UPDATE IEAI_DBSOURCE error", e);
                throw new RepositoryException(ServerError.ERR_DB_UPDATE, e);
            } finally
            {
                DBResource.closePreparedStatement(ps2, method, _log);
                DBResource.closeConn(con, rs, ps, method, _log);
            }
        }

    }

    /**
     * 新方法，获取连接时传递不同系统参数，Constants.IEAI_IEAI_BASIC = 0; // 基础信息源 IEAI_IEAI = 1; // 作业调度
     * IEAI_INFOCOLLECTION = 2; // 信息采集 IEAI_SUS = 3; // 变更管理 IEAI_EMERGENCY_SWITCH = 4; // 灾备切换
     * IEAI_TIMINGTASK = 5; // 定时任务 IEAI_EMERGENCY_OPER = 6; // 应急操作 IEAI_HEALTH_INSPECTION = 7; //
     * 健康巡检 IEAI_OTHER = 8; // 其他 <li>Description:</li>
     *
     * <AUTHOR> 2016年5月26日
     * @param type
     * @return
     * @throws DBException return Connection
     */

    public Connection getJdbcConnection ( int type ) throws DBException
    {
        // Establish connection
        // int type = 0;
        Connection con = null;
        long start = System.currentTimeMillis();
        try
        {
            // Update by liyang
            switch (type)
            {
                case Constants.IEAI_IEAI_BASIC:
                    try
                    {
                        con = new LoggableConnection(_jdbcDataSource.getConnection());
                        break;
                    } catch (SQLInvalidAuthorizationSpecException e)
                    {
                        File ieaiConfig = new File(ServerEnv.getServerEnv().getSysConfigFileName());
                        if (!ieaiConfig.exists())
                        {
                            throw new DBException("DBManager - getJdbcConnection() - Entegor.config 配置文件不存在");
                        }

                        FileInputStream fio = new java.io.FileInputStream(
                                ServerEnv.getServerEnv().getSysConfigFileName());
                        Properties config = new Properties();
                        config.load(fio);
                        fio.close();
                        String password = config.getProperty(ServerEnv.DB_PASSWD);
                        String dePw = Jcrypt.decrypt(password);
                        _jdbcDataSource.close();
                        _jdbcDataSource.setPassword(dePw);
                        con = new LoggableConnection(_jdbcDataSource.getConnection());
                        ServerEnv.getInstance().setSysConfig(ServerEnv.DB_PASSWD, dePw);
                        System.out.println("重新设置了数据库密码");
                    }
                case Constants.IEAI_OPM:
                    try
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        if (null == _ieaiOPM)
                        {
                            dfc = getServerDbResource(type, 1);
                            if (null != dfc)
                            {
                                key = type + "--" + dfc.get_url() + "--ieai--" + dfc.get_dbUser();
                            }
                            if (_dataSourceMap.containsKey(key))
                            {
                                _ieaiOPM = _dataSourceMap.get(key);
                            } else
                            {
                                dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                                // dfc = getServerDbResource(type,1);
                                if (null != dfc)
                                {
                                    dbCfg.setDbPassword(dfc.get_dbPassword());
                                    dbCfg.setDbUser(dfc.get_dbUser());
                                    dbCfg.setUrl(dfc.get_url());
                                    initDBSource(dbCfg, key, type);
                                } else
                                {
                                    _log.info("数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                                }
                            }
                        }
                        con = _ieaiOPM.getConnection();
                    } catch (SQLInvalidAuthorizationSpecException e)
                    {
                        _log.info("getDataSource ieai is error! type:" + type);
                    }
                    break;
                case Constants.IEAI_LOOPHOLE_MANAGE:
                    try
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        if (null == _ieaiLoopholeManage)
                        {
                            dfc = getServerDbResource(type, 1);
                            if (null != dfc)
                            {
                                key = type + "--" + dfc.get_url() + "--ieai--" + dfc.get_dbUser();
                            }
                            if (_dataSourceMap.containsKey(key))
                            {
                                _ieaiLoopholeManage = _dataSourceMap.get(key);
                            } else
                            {
                                dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                                // dfc = getServerDbResource(type,1);
                                if (null != dfc)
                                {
                                    dbCfg.setDbPassword(dfc.get_dbPassword());
                                    dbCfg.setDbUser(dfc.get_dbUser());
                                    dbCfg.setUrl(dfc.get_url());
                                    initDBSource(dbCfg, key, type);
                                } else
                                {
                                    _log.info("数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                                }
                            }
                        }
                        con = _ieaiLoopholeManage.getConnection();
                    } catch (SQLInvalidAuthorizationSpecException e)
                    {
                        _log.info("getDataSource ieai is error! type:" + type);
                    }
                    break;
                case Constants.IEAI_IEAI:
                    try
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        if (null == _ieaiDataSource)
                        {
                            dfc = getServerDbResource(type, 1);
                            if (null != dfc)
                            {
                                key = type + "--" + dfc.get_url() + "--ieai--" + dfc.get_dbUser();
                            }
                            if (_dataSourceMap.containsKey(key))
                            {
                                _ieaiDataSource = _dataSourceMap.get(key);
                            } else
                            {
                                dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                                // dfc = getServerDbResource(type,1);
                                if (null != dfc)
                                {
                                    dbCfg.setDbPassword(dfc.get_dbPassword());
                                    dbCfg.setDbUser(dfc.get_dbUser());
                                    dbCfg.setUrl(dfc.get_url());
                                    initDBSource(dbCfg, key, type);
                                } else
                                {
                                    _log.info("数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                                }
                            }
                        }
                        long starttime = System.currentTimeMillis();
                        con = _ieaiDataSource.getConnection();
                        DBMonitorBean.getInstance().setJdbcMaxValue(DBManager.getInstance().getMaxActiveJDBC());
                        DBMonitorBean.getInstance().setJdbcNowCount(DBManager.getInstance().getNumberActive_JDBC());
                        BigDecimal totalFee = new BigDecimal((System.currentTimeMillis()) - starttime);
                        BigDecimal d100 = new BigDecimal(1000);
                        BigDecimal fee = totalFee.divide(d100, 2, 2);// 小数点2位
                        DBMonitorBean.getInstance().setJdbcGetTime(String.valueOf(fee));
                    } catch (SQLInvalidAuthorizationSpecException e)
                    {
                        _log.info("getDataSource ieai is error! type:" + type);
                    }
                    break;
                case Constants.IEAI_INFOCOLLECTION:
                    try
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        if (null == _infoCollectionDataSource)
                        {
                            dfc = getServerDbResource(type, 1);
                            if (null != dfc)
                            {
                                key = type + "--" + dfc.get_url() + "--ieai--" + dfc.get_dbUser();
                            }
                            if (_dataSourceMap.containsKey(key))
                            {
                                _infoCollectionDataSource = _dataSourceMap.get(key);
                            } else
                            {
                                dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                                // dfc = getServerDbResource(type,1);
                                if (null != dfc)
                                {
                                    dbCfg.setDbPassword(dfc.get_dbPassword());
                                    dbCfg.setDbUser(dfc.get_dbUser());
                                    dbCfg.setUrl(dfc.get_url());
                                    initDBSource(dbCfg, key, type);
                                } else
                                {
                                    _log.info("数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                                }
                            }
                        }
                        con = _infoCollectionDataSource.getConnection();
                    } catch (SQLInvalidAuthorizationSpecException e)
                    {
                        _log.info("getDataSource infoclloction is error! type:" + type);
                    }
                    break;
                case Constants.IEAI_SUS:
                    try
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        if (null == _autoDataSource)
                        {
                            dfc = getServerDbResource(type, 1);
                            if (null != dfc)
                            {
                                key = type + "--" + dfc.get_url() + "--ieai--" + dfc.get_dbUser();
                            }
                            if (_dataSourceMap.containsKey(key))
                            {
                                _autoDataSource = _dataSourceMap.get(key);
                            } else
                            {
                                dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                                // dfc = getServerDbResource(type,1);
                                if (null != dfc)
                                {
                                    dbCfg.setDbPassword(dfc.get_dbPassword());
                                    dbCfg.setDbUser(dfc.get_dbUser());
                                    dbCfg.setUrl(dfc.get_url());
                                    initDBSource(dbCfg, key, type);
                                } else
                                {
                                    _log.info("数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                                }
                            }
                        }
                        con = _autoDataSource.getConnection();
                    } catch (SQLInvalidAuthorizationSpecException e)
                    {
                        _log.info("getDataSource SUS is error! type:" + type);
                    }
                    break;
                case Constants.IEAI_EMERGENCY_SWITCH:
                    try
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        if (null == _disRecDataSource)
                        {
                            dfc = getServerDbResource(type, 1);
                            if (null != dfc)
                            {
                                key = type + "--" + dfc.get_url() + "--ieai--" + dfc.get_dbUser();
                            }
                            if (_dataSourceMap.containsKey(key))
                            {
                                _disRecDataSource = _dataSourceMap.get(key);
                            } else
                            {
                                dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                                if (null != dfc)
                                {
                                    dbCfg.setDbPassword(dfc.get_dbPassword());
                                    dbCfg.setDbUser(dfc.get_dbUser());
                                    dbCfg.setUrl(dfc.get_url());
                                    initDBSource(dbCfg, key, type);
                                } else
                                {
                                    _log.info("数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                                }
                            }
                        }
                        con = _disRecDataSource.getConnection();
                    } catch (SQLInvalidAuthorizationSpecException e)
                    {
                        _log.info("getDataSource DISREC is error! type:" + type);
                    }
                    break;
                case Constants.IEAI_TIMINGTASK:
                    try
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        if (null == _timeTaskDataSource)
                        {
                            dfc = getServerDbResource(type, 1);
                            if (null != dfc)
                            {
                                key = type + "--" + dfc.get_url() + "--ieai--" + dfc.get_dbUser();
                            }
                            if (_dataSourceMap.containsKey(key))
                            {
                                _timeTaskDataSource = _dataSourceMap.get(key);
                            } else
                            {
                                dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                                // dfc = getServerDbResource(type,1);
                                if (null != dfc)
                                {
                                    dbCfg.setDbPassword(dfc.get_dbPassword());
                                    dbCfg.setDbUser(dfc.get_dbUser());
                                    dbCfg.setUrl(dfc.get_url());
                                    initDBSource(dbCfg, key, type);
                                } else
                                {
                                    _log.info("数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                                }
                            }
                        }
                        con = _timeTaskDataSource.getConnection();
                    } catch (SQLInvalidAuthorizationSpecException e)
                    {
                        _log.info("getDataSource timeTask is error! type:" + type);
                    }
                    break;
                case Constants.IEAI_EMERGENCY_OPER:
                    try
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        if (null == _emergencyOperDataSource)
                        {
                            dfc = getServerDbResource(type, 1);
                            if (null != dfc)
                            {
                                key = type + "--" + dfc.get_url() + "--ieai--" + dfc.get_dbUser();
                            }
                            if (_dataSourceMap.containsKey(key))
                            {
                                _emergencyOperDataSource = _dataSourceMap.get(key);
                            } else
                            {
                                dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                                // dfc = getServerDbResource(type,1);
                                if (null != dfc)
                                {
                                    dbCfg.setDbPassword(dfc.get_dbPassword());
                                    dbCfg.setDbUser(dfc.get_dbUser());
                                    dbCfg.setUrl(dfc.get_url());
                                    initDBSource(dbCfg, key, type);
                                } else
                                {
                                    _log.info("数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                                }
                            }
                        }
                        con = _emergencyOperDataSource.getConnection();
                    } catch (SQLInvalidAuthorizationSpecException e)
                    {
                        _log.info("getDataSource emergencyOper is error! type:" + type);
                    }
                    break;

                case Constants.IEAI_INFO_COLLECT:
                    try
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        if (null == _icSource)
                        {
                            dfc = getServerDbResource(type, 1);
                            if (null != dfc)
                            {
                                key = type + "--" + dfc.get_url() + "--ieai--" + dfc.get_dbUser();
                            }
                            if (_dataSourceMap.containsKey(key))
                            {
                                _icSource = _dataSourceMap.get(key);
                            } else
                            {
                                dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                                if (null != dfc)
                                {
                                    dbCfg.setDbPassword(dfc.get_dbPassword());
                                    dbCfg.setDbUser(dfc.get_dbUser());
                                    dbCfg.setUrl(dfc.get_url());
                                    initDBSource(dbCfg, key, type);
                                } else
                                {
                                    _log.info("数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                                }
                            }
                        }
                        con = _icSource.getConnection();
                    } catch (SQLInvalidAuthorizationSpecException e)
                    {
                        _log.info("getDataSource dischargenum is error! type:" + type);
                    }
                    break;

                case Constants.IEAI_FSH:
                    try
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        if (null == _fshDataSource)
                        {
                            dfc = getServerDbResource(type, 1);
                            if (null != dfc)
                            {
                                key = type + "--" + dfc.get_url() + "--ieai--" + dfc.get_dbUser();
                            }
                            if (_dataSourceMap.containsKey(key))
                            {
                                _fshDataSource = _dataSourceMap.get(key);
                            } else
                            {
                                dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                                if (null != dfc)
                                {
                                    dbCfg.setDbPassword(dfc.get_dbPassword());
                                    dbCfg.setDbUser(dfc.get_dbUser());
                                    dbCfg.setUrl(dfc.get_url());
                                    initDBSource(dbCfg, key, type);
                                } else
                                {
                                    _log.info("数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                                }
                            }
                        }
                        con = _fshDataSource.getConnection();
                    } catch (SQLInvalidAuthorizationSpecException e)
                    {
                        _log.info("getDataSource dischargenum is error! type:" + type);
                    }
                    break;

                case Constants.IEAI_HEALTH_INSPECTION:
                    try
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        if (null == _healthInspectionDataSource)
                        {
                            dfc = getServerDbResource(type, 1);
                            if (null != dfc)
                            {
                                key = type + "--" + dfc.get_url() + "--ieai--" + dfc.get_dbUser();
                            } else
                            {
                                dfc = getServerDbResource(Constants.IEAI_INFOCOLLECTION, 1);
                                if (null != dfc)
                                {
                                    key = dfc.get_url() + "--ieai--" + dfc.get_dbUser();
                                }
                            }
                            if (_dataSourceMap.containsKey(key))
                            {
                                _healthInspectionDataSource = _dataSourceMap.get(key);
                            } else
                            {
                                dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                                if (null != dfc)
                                {
                                    dbCfg.setDbPassword(dfc.get_dbPassword());
                                    dbCfg.setDbUser(dfc.get_dbUser());
                                    dbCfg.setUrl(dfc.get_url());
                                    initDBSource(dbCfg, key, type);
                                } else
                                {
                                    _log.info("数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                                }
                            }
                        }
                        con = _healthInspectionDataSource.getConnection();
                    } catch (SQLInvalidAuthorizationSpecException e)
                    {
                        _log.info("getDataSource healthInspection is error! type:" + type);
                    }
                    break;
                case Constants.IEAI_DAILY_OPERATIONS:
                    try
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        if (null == _dailyOperationsDataSource)
                        {
                            dfc = getServerDbResource(type, 1);
                            if (null != dfc)
                            {
                                key = type + "--" + dfc.get_url() + "--ieai--" + dfc.get_dbUser();
                            } else
                            {
                                dfc = getServerDbResource(Constants.IEAI_INFOCOLLECTION, 1);
                                if (null != dfc)
                                {
                                    key = dfc.get_url() + "--ieai--" + dfc.get_dbUser();
                                }
                            }
                            if (_dataSourceMap.containsKey(key))
                            {
                                _dailyOperationsDataSource = _dataSourceMap.get(key);
                            } else
                            {
                                dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                                if (null != dfc)
                                {
                                    dbCfg.setDbPassword(dfc.get_dbPassword());
                                    dbCfg.setDbUser(dfc.get_dbUser());
                                    dbCfg.setUrl(dfc.get_url());
                                    initDBSource(dbCfg, key, type);
                                } else
                                {
                                    _log.info("数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                                }
                            }
                        }
                        con = _dailyOperationsDataSource.getConnection();
                    } catch (SQLInvalidAuthorizationSpecException e)
                    {
                        _log.info("getDataSource infoclloction is error! type:" + type);
                    }
                    break;
                case Constants.IEAI_SCRIPT_SERVICE:
                    try
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        if (null == _scriptServiceDataSource)
                        {
                            dfc = getServerDbResource(type, 1);
                            if (null != dfc)
                            {
                                key = type + "--" + dfc.get_url() + "--ieai--" + dfc.get_dbUser();
                            }
                            if (_dataSourceMap.containsKey(key))
                            {
                                _scriptServiceDataSource = _dataSourceMap.get(key);
                            } else
                            {
                                ServerEnv  sev = ServerEnv.getServerEnv();
                                dbCfg = DBUtil.getDBConfig(sev);
                                if (null != dfc)
                                {
                                    dbCfg.setDbPassword(dfc.get_dbPassword());
                                    dbCfg.setDbUser(dfc.get_dbUser());
                                    dbCfg.setUrl(dfc.get_url());

                                    //设置脚本服务化连接池 回收参数
                                    if(Objects.nonNull(sev.getSysConfig("dbpool.custom.timeBetweenEvictionRunsMillis.provider"))){
                                        // 在空闲连接回收器线程运行期间休眠的时间值,以毫秒为单位. 如果设置为非正数,则不运行空闲连接回收器线程， 默认30秒，也就是30秒运行一次空闲连接回收器
                                        dbCfg.getPoolConfig().setTimeBetweenEvictionRunsMillis(sev.getSysConfig("dbpool.custom.timeBetweenEvictionRunsMillis.provider"),30000);
                                    }
                                    if(Objects.nonNull(sev.getSysConfig("dbpool.custom.minEvictableIdleTimeMillis.provider"))){
                                        //连接在池中保持空闲而不被空闲连接回收器线程(如果有)回收的最小时间值，单位毫秒 默认  30分钟
                                        dbCfg.getPoolConfig().setMinEvictableIdleTimeMillis( sev.getSysConfig("dbpool.custom.minEvictableIdleTimeMillis.provider"),1800000);
                                    }
                                    if(Objects.nonNull(sev.getSysConfig("dbpool.custom.numTestsPerEvictionRun.provider"))){
                                        // 在每次空闲连接回收器线程(如果有)运行时检查的连接数量 默认10
                                        dbCfg.getPoolConfig().setNumTestsPerEvictionRun(sev.getSysConfig("dbpool.custom.numTestsPerEvictionRun.provider"),10);
                                    }
                                    if(Objects.nonNull(sev.getSysConfig("dbpool.custom.testWhileIdle.provider"))){
                                        //指明连接是否被空闲连接回收器(如果有)进行检验.如果检测失败,则连接将被从池中去除.注意: 设置为true后如果要生效,validationQuery参数必须设置为非空字符串
                                        dbCfg.getPoolConfig().setTestWhileIdle(sev.getSysConfig("dbpool.custom.testWhileIdle.provider"),false);
                                    }
                                    if(Objects.nonNull(sev.getSysConfig("dbpool.custom.validationQuer.provider"))){
                                        //#验证使用的sql语句
                                        dbCfg.getPoolConfig().setValidationQuery(sev.getSysConfig("dbpool.custom.validationQuer.provider"));
                                    }
                                    initDBSource(dbCfg, key, type);
                                } else
                                {
                                    _log.info("数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                                }
                            }
                        }
                        con = _scriptServiceDataSource.getConnection();
                    } catch (SQLInvalidAuthorizationSpecException e)
                    {
                        _log.info("getDataSource scriptService is error! type:" + type);
                    }
                    break;
                case Constants.IEAI_SERVER_START_AND_STOP:
                    try
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        if (null == _disServiceDataSource)
                        {
                            dfc = getServerDbResource(type, 1);
                            if (null != dfc)
                            {
                                key = type + "--" + dfc.get_url() + "--ieai--" + dfc.get_dbUser();
                            }
                            if (_dataSourceMap.containsKey(key))
                            {
                                _disServiceDataSource = _dataSourceMap.get(key);
                            } else
                            {
                                dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                                if (null != dfc)
                                {
                                    dbCfg.setDbPassword(dfc.get_dbPassword());
                                    dbCfg.setDbUser(dfc.get_dbUser());
                                    dbCfg.setUrl(dfc.get_url());
                                    initDBSource(dbCfg, key, type);
                                } else
                                {
                                    _log.info("数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                                }
                            }
                        }
                        con = _disServiceDataSource.getConnection();
                    } catch (SQLInvalidAuthorizationSpecException e)
                    {
                        _log.info("getDataSource SERVER START AND STOP is error! type:" + type);
                    }
                    break;
                case Constants.IEAI_COMPARE:
                    try
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        if (null == _compareServiceDataSource)
                        {
                            dfc = getServerDbResource(type, 1);
                            if (null != dfc)
                            {
                                key = type + "--" + dfc.get_url() + "--ieai--" + dfc.get_dbUser();
                            }
                            if (_dataSourceMap.containsKey(key))
                            {
                                _compareServiceDataSource = _dataSourceMap.get(key);
                            } else
                            {
                                dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                                if (null != dfc)
                                {
                                    dbCfg.setDbPassword(dfc.get_dbPassword());
                                    dbCfg.setDbUser(dfc.get_dbUser());
                                    dbCfg.setUrl(dfc.get_url());
                                    initDBSource(dbCfg, key, type);
                                } else
                                {
                                    _log.info("数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                                }
                            }
                        }
                        con = _compareServiceDataSource.getConnection();
                    } catch (SQLInvalidAuthorizationSpecException e)
                    {
                        _log.info("getDataSource COMPARE is error! type:" + type);
                    }
                    break;
                case Constants.IEAI_CMDB:
                    try
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        if (null == _cmdbServiceDataSource)
                        {
                            dfc = getServerDbResource(type, 1);
                            if (null != dfc)
                            {
                                key = type + "--" + dfc.get_url() + "--ieai--" + dfc.get_dbUser();
                            }
                            if (_dataSourceMap.containsKey(key))
                            {
                                _cmdbServiceDataSource = _dataSourceMap.get(key);
                            } else
                            {
                                dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                                if (null != dfc)
                                {
                                    dbCfg.setDbPassword(dfc.get_dbPassword());
                                    dbCfg.setDbUser(dfc.get_dbUser());
                                    dbCfg.setUrl(dfc.get_url());
                                    initDBSource(dbCfg, key, type);
                                } else
                                {
                                    _log.info("数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                                }
                            }
                        }
                        con = _cmdbServiceDataSource.getConnection();
                    } catch (SQLInvalidAuthorizationSpecException e)
                    {
                        _log.info("getDataSource CMDB is error! type:" + type);
                    }
                    break;
                case Constants.IEAI_APM:
                    try
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        if (null == _apmDataSource)
                        {
                            dfc = getServerDbResource(type, 1);
                            if (null != dfc)
                            {
                                key = type + "--" + dfc.get_url() + "--ieai--" + dfc.get_dbUser();
                            }
                            if (_dataSourceMap.containsKey(key))
                            {
                                _apmDataSource = _dataSourceMap.get(key);
                            } else
                            {
                                dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                                // dfc = getServerDbResource(type,1);
                                if (null != dfc)
                                {
                                    dbCfg.setDbPassword(dfc.get_dbPassword());
                                    dbCfg.setDbUser(dfc.get_dbUser());
                                    dbCfg.setUrl(dfc.get_url());
                                    initDBSource(dbCfg, key, type);
                                } else
                                {
                                    _log.info("数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                                }
                            }
                        }
                        con = _apmDataSource.getConnection();
                    } catch (SQLInvalidAuthorizationSpecException e)
                    {
                        _log.info("getDataSource APM is error! type:" + type);
                    }
                    break;
                case Constants.IEAI_TOPO:
                    try
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        if (null == _topoServiceDataSource)
                        {
                            dfc = getServerDbResource(type, 1);
                            if (null != dfc)
                            {
                                key = type + "--" + dfc.get_url() + "--ieai--" + dfc.get_dbUser();
                            }
                            if (_dataSourceMap.containsKey(key))
                            {
                                _topoServiceDataSource = _dataSourceMap.get(key);
                            } else
                            {
                                dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                                if (null != dfc)
                                {
                                    dbCfg.setDbPassword(dfc.get_dbPassword());
                                    dbCfg.setDbUser(dfc.get_dbUser());
                                    dbCfg.setUrl(dfc.get_url());
                                    initDBSource(dbCfg, key, type);
                                } else
                                {
                                    _log.info("数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                                }
                            }
                        }
                        con = _topoServiceDataSource.getConnection();
                    } catch (SQLInvalidAuthorizationSpecException e)
                    {
                        _log.info("getDataSource topo is error! type:" + type);
                    }
                    break;
                case Constants.IEAI_DATA_COLLECT:
                    try
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        if (null == _dataCollectDataSource)
                        {
                            dfc = getServerDbResource(type, 1);
                            if (null != dfc)
                            {
                                key = type + "--" + dfc.get_url() + "--ieai--" + dfc.get_dbUser();
                            }
                            if (_dataSourceMap.containsKey(key))
                            {
                                _dataCollectDataSource = _dataSourceMap.get(key);
                            } else
                            {
                                dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                                if (null != dfc)
                                {
                                    dbCfg.setDbPassword(dfc.get_dbPassword());
                                    dbCfg.setDbUser(dfc.get_dbUser());
                                    dbCfg.setUrl(dfc.get_url());
                                    initDBSource(dbCfg, key, type);
                                } else
                                {
                                    _log.info("数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                                }
                            }
                        }
                        con = _dataCollectDataSource.getConnection();
                    } catch (SQLInvalidAuthorizationSpecException e)
                    {
                        _log.info("getDataSource COMPARE is error! type:" + type);
                    }
                    break;
                case Constants.IEAI_PAAS:
                    try
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        if (null == _paasDataSource)
                        {
                            dfc = getServerDbResource(type, 1);
                            if (null != dfc)
                            {
                                key = type + "--" + dfc.get_url() + "--ieai--" + dfc.get_dbUser();
                            }
                            if (_dataSourceMap.containsKey(key))
                            {
                                _paasDataSource = _dataSourceMap.get(key);
                            } else
                            {
                                dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                                if (null != dfc)
                                {
                                    dbCfg.setDbPassword(dfc.get_dbPassword());
                                    dbCfg.setDbUser(dfc.get_dbUser());
                                    dbCfg.setUrl(dfc.get_url());
                                    initDBSource(dbCfg, key, type);
                                } else
                                {
                                    _log.info("数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                                }
                            }
                        }
                        con = _paasDataSource.getConnection();
                    } catch (SQLInvalidAuthorizationSpecException e)
                    {
                        _log.info("getDataSource PAAS is error! type:" + type);
                    }
                    break;

                case Constants.IEAI_ROUTINE_TASKS:
                    try
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        if (null == _routineTasksDataSource)
                        {
                            dfc = getServerDbResource(type, 1);
                            if (null != dfc)
                            {
                                key = type + "--" + dfc.get_url() + "--ieai--" + dfc.get_dbUser();
                            }
                            if (_dataSourceMap.containsKey(key))
                            {
                                _routineTasksDataSource = _dataSourceMap.get(key);
                            } else
                            {
                                dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                                if (null != dfc)
                                {
                                    dbCfg.setDbPassword(dfc.get_dbPassword());
                                    dbCfg.setDbUser(dfc.get_dbUser());
                                    dbCfg.setUrl(dfc.get_url());
                                    initDBSource(dbCfg, key, type);
                                } else
                                {
                                    _log.info("数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                                }
                            }
                        }
                        con = _routineTasksDataSource.getConnection();
                    } catch (SQLInvalidAuthorizationSpecException e)
                    {
                        _log.info("getDataSource Routine Tasks is error! type:" + type);
                    }
                    break;

                case Constants.IEAI_AZ:
                    try
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        if (null == _azDataSource)
                        {
                            dfc = getServerDbResource(type, 1);
                            if (null != dfc)
                            {
                                key = type + "--" + dfc.get_url() + "--ieai--" + dfc.get_dbUser();
                            }
                            if (_dataSourceMap.containsKey(key))
                            {
                                _azDataSource = _dataSourceMap.get(key);
                            } else
                            {
                                dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                                if (null != dfc)
                                {
                                    dbCfg.setDbPassword(dfc.get_dbPassword());
                                    dbCfg.setDbUser(dfc.get_dbUser());
                                    dbCfg.setUrl(dfc.get_url());
                                    initDBSource(dbCfg, key, type);
                                } else
                                {
                                    _log.info("数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                                }
                            }
                        }
                        con = _azDataSource.getConnection();
                    } catch (SQLInvalidAuthorizationSpecException e)
                    {
                        _log.info("getDataSource AZ is error! type:" + type);
                    }
                    break;
                case Constants.IEAI_AZ_GROUP:
                    try
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        if (null == _az_group_DataSource)
                        {
                            dfc = getServerDbResource(type, 1);
                            if (null != dfc)
                            {
                                key = type + "--" + dfc.get_url() + "--ieai--" + dfc.get_dbUser();
                            }
                            if (_dataSourceMap.containsKey(key))
                            {
                                _az_group_DataSource = _dataSourceMap.get(key);
                            } else
                            {
                                dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                                if (null != dfc)
                                {
                                    dbCfg.setDbPassword(dfc.get_dbPassword());
                                    dbCfg.setDbUser(dfc.get_dbUser());
                                    dbCfg.setUrl(dfc.get_url());
                                    initDBSource(dbCfg, key, type);
                                } else
                                {
                                    _log.info("数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                                }
                            }
                        }
                        con = _az_group_DataSource.getConnection();
                    } catch (SQLInvalidAuthorizationSpecException e)
                    {
                        _log.info("getDataSource GROUP_AZ is error! type:" + type);
                    }
                    break;
                case Constants.IEAI_SUS_GROUP:
                    try
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        if (null == _sus_group_DataSource)
                        {
                            dfc = getServerDbResource(type, 1);
                            if (null != dfc)
                            {
                                key = type + "--" + dfc.get_url() + "--ieai--" + dfc.get_dbUser();
                            }
                            if (_dataSourceMap.containsKey(key))
                            {
                                _sus_group_DataSource = _dataSourceMap.get(key);
                            } else
                            {
                                dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                                if (null != dfc)
                                {
                                    dbCfg.setDbPassword(dfc.get_dbPassword());
                                    dbCfg.setDbUser(dfc.get_dbUser());
                                    dbCfg.setUrl(dfc.get_url());
                                    initDBSource(dbCfg, key, type);
                                } else
                                {
                                    _log.info("数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                                }
                            }
                        }
                        con = _sus_group_DataSource.getConnection();
                    } catch (SQLInvalidAuthorizationSpecException e)
                    {
                        _log.info("getDataSource group_sus is error! type:" + type);
                    }
                    break;
                case Constants.IEAI_TOOLS:
                    try
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        if (null == _toolsDataSource)
                        {
                            dfc = getServerDbResource(type, 1);
                            if (null != dfc)
                            {
                                key = type + "--" + dfc.get_url() + "--ieai--" + dfc.get_dbUser();
                            }
                            if (_dataSourceMap.containsKey(key))
                            {
                                _toolsDataSource = _dataSourceMap.get(key);
                            } else
                            {
                                dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                                if (null != dfc)
                                {
                                    dbCfg.setDbPassword(dfc.get_dbPassword());
                                    dbCfg.setDbUser(dfc.get_dbUser());
                                    dbCfg.setUrl(dfc.get_url());
                                    initDBSource(dbCfg, key, type);
                                } else
                                {
                                    _log.info("数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                                }
                            }
                        }
                        con = _toolsDataSource.getConnection();
                    } catch (SQLInvalidAuthorizationSpecException e)
                    {
                        _log.info("getDataSource IEAI_TOOLS is error! type:" + type);
                    }
                    break;
                case Constants.IEAI_SYS_CHANGE:
                    try
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        if (null == _sysChangeDataSource)
                        {
                            dfc = getServerDbResource(type, 1);
                            if (null != dfc)
                            {
                                key = type + "--" + dfc.get_url() + "--ieai--" + dfc.get_dbUser();
                            }
                            if (_dataSourceMap.containsKey(key))
                            {
                                _sysChangeDataSource = _dataSourceMap.get(key);
                            } else
                            {
                                dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                                if (null != dfc)
                                {
                                    dbCfg.setDbPassword(dfc.get_dbPassword());
                                    dbCfg.setDbUser(dfc.get_dbUser());
                                    dbCfg.setUrl(dfc.get_url());
                                    initDBSource(dbCfg, key, type);
                                } else
                                {
                                    _log.info("数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                                }
                            }
                        }
                        con = _sysChangeDataSource.getConnection();
                    } catch (SQLInvalidAuthorizationSpecException e)
                    {
                        _log.info("getDataSource Routine Tasks is error! type:" + type);
                    }
                    break;
                case Constants.IEAI_EMS:
                    try
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        if (null == _emsDataSource)
                        {
                            dfc = getServerDbResource(type, 1);
                            if (null != dfc)
                            {
                                key = type + "--" + dfc.get_url() + "--ieai--" + dfc.get_dbUser();
                            }
                            if (_dataSourceMap.containsKey(key))
                            {
                                _emsDataSource = _dataSourceMap.get(key);
                            } else
                            {
                                dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                                if (null != dfc)
                                {
                                    dbCfg.setDbPassword(dfc.get_dbPassword());
                                    dbCfg.setDbUser(dfc.get_dbUser());
                                    dbCfg.setUrl(dfc.get_url());
                                    initDBSource(dbCfg, key, type);
                                } else
                                {
                                    _log.info("数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                                }
                            }
                        }
                        con = _emsDataSource.getConnection();
                    } catch (SQLInvalidAuthorizationSpecException e)
                    {
                        _log.info("getDataSource IEAI_EMS is error! type:" + type);
                    }
                    break;
                case Constants.IEAI_BMI:
                    try
                    {
                        String key = " ";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        if (null == _bmiDataSource)
                        {
                            dfc = getServerDbResource(type, 1);
                            if (null != dfc)
                            {
                                key = type + "--" + dfc.get_url() + "--ieai--" + dfc.get_dbUser();
                            }
                            if (_dataSourceMap.containsKey(key))
                            {
                                _bmiDataSource = _dataSourceMap.get(key);
                            } else
                            {
                                dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                                if (null != dfc)
                                {
                                    dbCfg.setDbPassword(dfc.get_dbPassword());
                                    dbCfg.setDbUser(dfc.get_dbUser());
                                    dbCfg.setUrl(dfc.get_url());
                                    initDBSource(dbCfg, key, type);
                                } else
                                {
                                    _log.info("数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                                }
                            }
                        }
                        con = _bmiDataSource.getConnection();
                    } catch (SQLInvalidAuthorizationSpecException e)
                    {
                        _log.info("getDataSource IEAI_BMI is error! type:" + type);
                    }
                    break;
                case Constants.IEAI_SCRIPT_DBAAS:
                    boolean dbaasCon = ServerEnv.getInstance().getBooleanConfigNew2("dbaas.conn.alone", false);
                    if (dbaasCon)
                    {
                        try
                        {
                            String key = "";
                            DBSourceMonitor dfc = null;
                            DBConfig dbCfg = null;
                            if (null == _dbaasDirDataSource)
                            {
                                dfc = getServerDbResource(type, 1);
                                if (null != dfc)
                                {
                                    key = type + "--" + dfc.get_url() + "--ieai--" + dfc.get_dbUser();
                                }
                                if (_dataSourceMap.containsKey(key))
                                {
                                    _dbaasDirDataSource = _dataSourceMap.get(key);
                                } else
                                {
                                    dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                                    if (null != dfc)
                                    {
                                        dbCfg.setDbPassword(dfc.get_dbPassword());
                                        dbCfg.setDbUser(dfc.get_dbUser());
                                        dbCfg.setUrl(dfc.get_url());
                                        initDBSource(dbCfg, key, type);
                                    } else
                                    {
                                        _log.info("数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                                        // key = type + "--" + dbCfg.getUrl() + "--ieai--" + dbCfg.getDbUser();
                                        // if (_dataSourceMap.containsKey(key))
                                        // {
                                        // _dbaasDirDataSource = _dataSourceMap.get(key);
                                        // } else
                                        // {
                                        // initDBSource(dbCfg, key, type);
                                        // }
                                    }
                                }
                            }
                            con = _dbaasDirDataSource.getConnection();
                        } catch (SQLInvalidAuthorizationSpecException e)
                        {
                            _log.info("getDataSource IEAI_SCRIPT_DBAAS is error! type:" + type);
                        }
                    } else
                    {
                        con = _jdbcDataSource.getConnection();
                    }
                    break;
                case Constants.IEAI_SHUTDOWN_MAINTAIN:
                    try
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        if (null == _shutdownMaintainDataSource)
                        {
                            dfc = getServerDbResource(type, 1);
                            if (null != dfc)
                            {
                                key = type + "--" + dfc.get_url() + "--ieai--" + dfc.get_dbUser();
                            }
                            if (_dataSourceMap.containsKey(key))
                            {
                                _shutdownMaintainDataSource = _dataSourceMap.get(key);
                            } else
                            {
                                dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                                if (null != dfc)
                                {
                                    dbCfg.setDbPassword(dfc.get_dbPassword());
                                    dbCfg.setDbUser(dfc.get_dbUser());
                                    dbCfg.setUrl(dfc.get_url());
                                    initDBSource(dbCfg, key, type);
                                } else
                                {
                                    _log.info("数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                                }
                            }
                        }
                        con = _shutdownMaintainDataSource.getConnection();
                    } catch (SQLInvalidAuthorizationSpecException e)
                    {
                        _log.info("getDataSource Routine Tasks is error! type:" + type);
                    }
                    break;
                default:
                    _log.info("getDataSource getdefault dbsource! type:" + type);
                    con = _jdbcDataSource.getConnection();
                    break;
            }
            con = new LoggableConnection(con);
            con.setAutoCommit(false);
            con.setTransactionIsolation(Connection.TRANSACTION_READ_COMMITTED);
//            if((System.currentTimeMillis()-start)>1000)
//            {
//                _log.info("getJdbcConnection time:"+(System.currentTimeMillis()-start) +" ; jdbc type" + type +" ; numnow :" + DBManager.getInstance().getDBSNumberActive(type));
//                StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
//                                // 打印堆栈跟踪
//                System.out.println("--- 线程堆栈跟踪 ---");
//                for (StackTraceElement element : stackTrace) {
//                    System.out.println(element);
//                }
//            }
            return con;
        } catch (SQLException e)
        {
            if (type != Constants.IEAI_IEAI_BASIC)
            {
                checkDbConnection(type);
            }
            _log.error("error getting jdbc connection dbmanager 1417 row",e);
            getDBMonitor("getJdbcConnection", e.getMessage());
            // 自身告警集成
            try
            {
                if (Environment.getInstance().getCibTivoliSwitch())
                {
                    SendCibSyslog syslog = new SendCibSyslog();
                    String serverip = Environment.getInstance().getServerIP();
                    StringBuilder sb = new StringBuilder();
                    sb.append("severity=CRITICAL hostname= ").append("ip=" + serverip).append(" ")
                            .append("msg=\"" + "get DBConnection Error " + e.getMessage() + "\"");
                    _log.info(sb.toString());
                    syslog.sendsysLog(sb.toString());
                }
                String serverip = Environment.getInstance().getServerIP();
                IeaiWarnModel iwm = new IeaiWarnModel();
                iwm.setImodulecode("platform");
                iwm.setItypecode("DBConnection");
                iwm.setIlevelcode("five");
                iwm.setIip(serverip);
                iwm.setIwarnmsg("get DBConnection Error " + e.getMessage());
                iwm.setIhappentimeStr(DateUtil.formatDateTime(new Date()));

                WarningInterfaceUtilsPlatform warningInterfaceUtilsPlatform = new WarningInterfaceUtilsPlatform();
                warningInterfaceUtilsPlatform.callWarning(iwm, true);
            } catch (Exception e1)
            {
                _log.error("send Warn error", e1);
            }
            throw new DBException(e.getMessage());
        } catch (Exception e)
        {
            // 自身告警集成
            try
            {
                if (Environment.getInstance().getCibTivoliSwitch())
                {
                    SendCibSyslog syslog = new SendCibSyslog();
                    String serverip = Environment.getInstance().getServerIP();
                    StringBuilder sb = new StringBuilder();
                    sb.append("severity=CRITICAL hostname= ").append("ip=" + serverip).append(" ")
                            .append("msg=\"" + "get DBConnection Error " + e.getMessage() + "\"");
                    _log.info(sb.toString());
                    syslog.sendsysLog(sb.toString());
                }
                String serverip = Environment.getInstance().getServerIP();
                IeaiWarnModel iwm = new IeaiWarnModel();
                iwm.setImodulecode("platform");
                iwm.setItypecode("DBConnection");
                iwm.setIlevelcode("five");
                iwm.setIip(serverip);
                iwm.setIwarnmsg("get DBConnection Error " + e.getMessage());
                iwm.setIhappentimeStr(DateUtil.formatDateTime(new Date()));

                WarningInterfaceUtilsPlatform warningInterfaceUtilsPlatform = new WarningInterfaceUtilsPlatform();
                warningInterfaceUtilsPlatform.callWarning(iwm, true);
            } catch (Exception e1)
            {
                _log.error("send Warn error", e1);
            }
            throw new DBException(e.getMessage());
        }
    }

    // public Connection getStoreConnection () throws DBException
    // {
    // // Establish connection
    // Connection con = null;
    // try
    // {
    // con = _storeDataSource.getConnection();
    // con.setAutoCommit(false);
    // return con;
    // } catch (SQLException e)
    // {
    // _log.error("error getting jdbc connection dbmanager 214 row");
    // throw new DBException(e.getMessage());
    // } catch (Exception e)
    // {
    // throw new DBException(e.getMessage());
    // }
    // }
    /**
     * * 新方法，获取连接时传递不同系统参数，Constants.IEAI_IEAI_BASIC = 0; // 基础信息源 IEAI_IEAI = 1; // 作业调度
     * IEAI_INFOCOLLECTION = 2; // 信息采集 IEAI_SUS = 3; // 变更管理 IEAI_EMERGENCY_SWITCH = 4; // 灾备切换
     * IEAI_TIMINGTASK = 5; // 定时任务 IEAI_EMERGENCY_OPER = 6; // 应急操作 IEAI_HEALTH_INSPECTION = 7; //
     * 健康巡检 IEAI_OTHER = 8; // 其他 <li>Description:</li>
     *
     * <AUTHOR> 2016年5月26日
     * @param type
     * @return
     * @throws DBException return Connection
     */
    public Connection getSchedulerConnection ( int type ) throws DBException
    {
        long starttime = System.currentTimeMillis();
        Connection con = null;
        con = getJdbcConnection(type);
        DBMonitorBean.getInstance().setSchMaxValue(DBManager.getInstance().getMaxActiveSchedule());
        DBMonitorBean.getInstance().setSchNowCount(DBManager.getInstance().getNumberActive_Schedule());
        BigDecimal totalFee = new BigDecimal((System.currentTimeMillis()) - starttime);
        BigDecimal d100 = new BigDecimal(1000);
        BigDecimal fee = totalFee.divide(d100, 2, 2);// 小数点2位
        DBMonitorBean.getInstance().setSchGetTime(String.valueOf(fee));
        try
        {
            con.setTransactionIsolation(Connection.TRANSACTION_READ_COMMITTED);
        } catch (SQLException e)
        {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return con;
    }

    private static void initConnectionPool ( DBConfig dbCfg ) throws Exception
    {

        DBPoolConfig poolConfig = dbCfg.getPoolConfig();
        BasicDataSource jdbcDataSource = new BasicDataSource();

        jdbcDataSource.setDriverClassName(dbCfg.getDriverClass());
        jdbcDataSource.setUrl(dbCfg.getUrl());
        jdbcDataSource.setUsername(dbCfg.getDbUser());
        jdbcDataSource.setPassword(dbCfg.getDbPassword());

        jdbcDataSource.setInitialSize(poolConfig.getInitSize());
        jdbcDataSource.setMaxActive(poolConfig.getMaxActive());
        jdbcDataSource.setMaxIdle(poolConfig.getMaxIdle());
        jdbcDataSource.setMinIdle(poolConfig.getMinIdle());
        jdbcDataSource.setMaxWait(30000);
        jdbcDataSource.setTestOnBorrow(poolConfig.getTestOnBorrow());
        if (poolConfig.getTestOnBorrow())
        {
            //jdbcDataSource.setValidationQuery(poolConfig.getValidationQuery());
        }

        jdbcDataSource.setRemoveAbandoned(poolConfig.isRemoveAbandoned());
        jdbcDataSource.setRemoveAbandonedTimeout(poolConfig.getRemoveAbandonedTimeout());
        jdbcDataSource.setLogAbandoned(poolConfig.isLogAbandoned());
        _jdbcDataSource = jdbcDataSource;
        _log.info("JDBC datasource initialization succeeded!");
        DBMonitorBean.getInstance().setJdbcInitValue(poolConfig.getInitSize());
        DBMonitorBean.getInstance().setJdbcMaxValue(poolConfig.getMaxActive());
        DBMonitorBean.getInstance().setJdbcMaxFree(poolConfig.getMaxIdle());
        DBMonitorBean.getInstance().setJdbcMinFree(poolConfig.getMinIdle());
        DBMonitorBean.getInstance().setSchInitValue(poolConfig.getInitSize());
        DBMonitorBean.getInstance().setSchMaxValue(poolConfig.getMaxActive());
        DBMonitorBean.getInstance().setSchMaxFree(poolConfig.getMaxIdle());
        DBMonitorBean.getInstance().setSchMinFree(poolConfig.getMinIdle());
        // BasicDataSource storeDataSource = new BasicDataSource();
        //
        // storeDataSource.setDriverClassName(dbCfg.getDriverClass());
        // storeDataSource.setUrl(dbCfg.getUrl());
        // storeDataSource.setUsername(dbCfg.getDbUser());
        // storeDataSource.setPassword(dbCfg.getDbPassword());
        //
        // storeDataSource.setInitialSize(poolConfig.getInitSize());
        // storeDataSource.setMaxActive(poolConfig.getMaxActive());
        // storeDataSource.setMaxIdle(poolConfig.getMaxIdle());
        // storeDataSource.setMinIdle(poolConfig.getMinIdle());
        // storeDataSource.setMaxWait(-1);
        //
        // _storeDataSource = storeDataSource;

        // BasicDataSource schedulerDataSource = new BasicDataSource();
        //
        // schedulerDataSource.setDriverClassName(dbCfg.getDriverClass());
        // schedulerDataSource.setUrl(dbCfg.getUrl());
        // schedulerDataSource.setUsername(dbCfg.getDbUser());
        // schedulerDataSource.setPassword(dbCfg.getDbPassword());
        //
        // schedulerDataSource.setInitialSize(poolConfig.getInitSize());
        // schedulerDataSource.setMaxActive(poolConfig.getMaxActive());
        // schedulerDataSource.setMaxIdle(poolConfig.getMaxIdle());
        // schedulerDataSource.setMinIdle(poolConfig.getMinIdle());
        // schedulerDataSource.setMaxWait(30000);
        //
        // schedulerDataSource.setRemoveAbandoned(poolConfig.isRemoveAbandoned());
        // schedulerDataSource.setRemoveAbandonedTimeout(poolConfig.getRemoveAbandonedTimeout());
        // schedulerDataSource.setLogAbandoned(poolConfig.isLogAbandoned());
        // _schedulerDataSource = schedulerDataSource;

        // dbprop.setProperty("CHARSET", dbCharset);
    }

    public static void initJdbcConnectionPool ( DBConfig dbCfg ) throws Exception
    {
        DBPoolConfig poolConfig = dbCfg.getPoolConfig();
        BasicDataSource jdbcDataSource = new BasicDataSource();

        jdbcDataSource.setDriverClassName(dbCfg.getDriverClass());
        jdbcDataSource.setUrl(dbCfg.getUrl());
        jdbcDataSource.setUsername(dbCfg.getDbUser());
        jdbcDataSource.setPassword(dbCfg.getDbPassword());

        jdbcDataSource.setInitialSize(poolConfig.getInitSize());
        jdbcDataSource.setMaxActive(poolConfig.getMaxActive());
        jdbcDataSource.setMaxIdle(poolConfig.getMaxIdle());
        jdbcDataSource.setMinIdle(poolConfig.getMinIdle());
        jdbcDataSource.setMaxWait(5000);
        jdbcDataSource.setTestWhileIdle(poolConfig.getTestWhileIdle());
        jdbcDataSource.setTestOnBorrow(poolConfig.getTestOnBorrow());
        jdbcDataSource.setTestOnReturn(poolConfig.getTestOnReturn());
        if (poolConfig.getTimeBetweenEvictionRunsMillis() != 0)
        {
            jdbcDataSource.setTimeBetweenEvictionRunsMillis(poolConfig.getTimeBetweenEvictionRunsMillis());
        }

        if (poolConfig.getNumTestsPerEvictionRun() != 0)
        {
            jdbcDataSource.setNumTestsPerEvictionRun(poolConfig.getNumTestsPerEvictionRun());
        }
        if (poolConfig.getTestOnReturn())
        {
            jdbcDataSource.setValidationQuery(poolConfig.getValidationQuery());
        }

        _jdbcDataSource = jdbcDataSource;
        _log.info("JDBC datasource initialization succeeded!");
    }

    public int getDBSNumberActive ( int type )
    {
        int num = 0;
        switch (type)
        {
            case Constants.IEAI_IEAI_BASIC:
                if (null != _jdbcDataSource)
                {
                    num = _jdbcDataSource.getNumActive();
                } else
                {
                    num = 0;
                }
                break;
            case Constants.IEAI_OPM:
                if (null != _ieaiOPM)
                {
                    num = _ieaiOPM.getNumActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_LOOPHOLE_MANAGE :
                if (null != _ieaiLoopholeManage)
                {
                    num = _ieaiLoopholeManage.getNumActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_IEAI:
                if (null != _ieaiDataSource)
                {
                    num = _ieaiDataSource.getNumActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_INFOCOLLECTION:
                if (null != _infoCollectionDataSource)
                {
                    num = _infoCollectionDataSource.getNumActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_SUS:
                if (null != _autoDataSource)
                {
                    num = _autoDataSource.getNumActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_EMERGENCY_SWITCH:
                if (null != _disRecDataSource)
                {
                    num = _disRecDataSource.getNumActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_TIMINGTASK:
                if (null != _timeTaskDataSource)
                {
                    num = _timeTaskDataSource.getNumActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_EMERGENCY_OPER:
                if (null != _emergencyOperDataSource)
                {
                    num = _emergencyOperDataSource.getNumActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_HEALTH_INSPECTION:
                if (null != _healthInspectionDataSource)
                {
                    num = _healthInspectionDataSource.getNumActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_DAILY_OPERATIONS:
                if (null != _dailyOperationsDataSource)
                {
                    num = _dailyOperationsDataSource.getNumActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_SCRIPT_SERVICE:
                if (null != _scriptServiceDataSource)
                {
                    num = _scriptServiceDataSource.getNumActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_SERVER_START_AND_STOP:
                if (null != _disServiceDataSource)
                {
                    num = _disServiceDataSource.getNumActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_COMPARE:
                if (null != _compareServiceDataSource)
                {
                    num = _compareServiceDataSource.getNumActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_CMDB:
                if (null != _cmdbServiceDataSource)
                {
                    num = _cmdbServiceDataSource.getNumActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_PAAS:
                if (null != _paasDataSource)
                {
                    num = _paasDataSource.getNumActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_ROUTINE_TASKS:
                if (null != _routineTasksDataSource)
                {
                    num = _routineTasksDataSource.getNumActive();
                } else
                {
                    num = -1;
                }
                break;

            case Constants.IEAI_APM:
                if (null != _apmDataSource)
                {
                    num = _apmDataSource.getNumActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_TOPO:
                if (null != _topoServiceDataSource)
                {
                    num = _topoServiceDataSource.getNumActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_DATA_COLLECT:
                if (null != _dataCollectDataSource)
                {
                    num = _dataCollectDataSource.getNumActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_AZ:
                if (null != _azDataSource)
                {
                    num = _azDataSource.getNumActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_AZ_GROUP:
                if (null != _az_group_DataSource)
                {
                    num = _az_group_DataSource.getNumActive();
                } else
                {
                    num = -1;
                }
                break;

            case Constants.IEAI_SUS_GROUP:
                if (null != _sus_group_DataSource)
                {
                    num = _sus_group_DataSource.getNumActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_TOOLS:
                if (null != _toolsDataSource)
                {
                    num = _toolsDataSource.getNumActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_SYS_CHANGE:
                if (null != _sysChangeDataSource)
                {
                    num = _sysChangeDataSource.getNumActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_EMS:
                if (null != _emsDataSource)
                {
                    num = _emsDataSource.getNumActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_BMI:
                if (null != _bmiDataSource)
                {
                    num = _bmiDataSource.getNumActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_SCRIPT_DBAAS:
                boolean dbaasCon = ServerEnv.getInstance().getBooleanConfigNew2("dbaas.conn.alone", false);
                if (!dbaasCon)
                {
                    num = -1;
                } else
                {
                    if (null != _dbaasDirDataSource)
                    {
                        num = _dbaasDirDataSource.getNumActive();
                    } else
                    {
                        num = -1;
                    }
                }
                break;
            case Constants.IEAI_SHUTDOWN_MAINTAIN:
                if (null != _shutdownMaintainDataSource)
                {
                    num = _shutdownMaintainDataSource.getNumActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_FSH:
                if (null != _fshDataSource)
                {
                    num = _fshDataSource.getNumActive();
                } else
                {
                    num = -1;
                }
                break;
            default:
                num = -1;
                break;
        }
        // return _schedulerDataSource.getNumActive();
        return num;
    }

    public int getMaxDBSActive ( int type )
    {
        int num = 0;
        // return _schedulerDataSource.getMaxActive();
        switch (type)
        {
            case Constants.IEAI_IEAI_BASIC:
                if (null != _jdbcDataSource)
                {
                    num = _jdbcDataSource.getMaxActive();
                } else
                {
                    num = 0;
                }
                break;
            case Constants.IEAI_OPM:
                if (null != _ieaiOPM)
                {
                    num = _ieaiOPM.getMaxActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_LOOPHOLE_MANAGE :
                if (null != _ieaiLoopholeManage)
                {
                    num = _ieaiLoopholeManage.getMaxActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_IEAI:
                if (null != _ieaiDataSource)
                {
                    num = _ieaiDataSource.getMaxActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_INFOCOLLECTION:
                if (null != _infoCollectionDataSource)
                {
                    num = _infoCollectionDataSource.getMaxActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_SUS:
                if (null != _autoDataSource)
                {
                    num = _autoDataSource.getMaxActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_EMERGENCY_SWITCH:
                if (null != _disRecDataSource)
                {
                    num = _disRecDataSource.getMaxActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_TIMINGTASK:
                if (null != _timeTaskDataSource)
                {
                    num = _timeTaskDataSource.getMaxActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_EMERGENCY_OPER:
                if (null != _emergencyOperDataSource)
                {
                    num = _emergencyOperDataSource.getMaxActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_HEALTH_INSPECTION:
                if (null != _healthInspectionDataSource)
                {
                    num = _healthInspectionDataSource.getMaxActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_DAILY_OPERATIONS:
                if (null != _dailyOperationsDataSource)
                {
                    num = _dailyOperationsDataSource.getMaxActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_SCRIPT_SERVICE:
                if (null != _scriptServiceDataSource)
                {
                    num = _scriptServiceDataSource.getMaxActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_SERVER_START_AND_STOP:
                if (null != _disServiceDataSource)
                {
                    num = _disServiceDataSource.getMaxActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_COMPARE:
                if (null != _compareServiceDataSource)
                {
                    num = _compareServiceDataSource.getMaxActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_CMDB:
                if (null != _cmdbServiceDataSource)
                {
                    num = _cmdbServiceDataSource.getMaxActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_APM:
                if (null != _apmDataSource)
                {
                    num = _apmDataSource.getMaxActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_TOPO:
                if (null != _topoServiceDataSource)
                {
                    num = _topoServiceDataSource.getMaxActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_DATA_COLLECT:
                if (null != _dataCollectDataSource)
                {
                    num = _dataCollectDataSource.getMaxActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_AZ:
                if (null != _azDataSource)
                {
                    num = _azDataSource.getMaxActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_PAAS:
                if (null != _paasDataSource)
                {
                    num = _paasDataSource.getMaxActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_ROUTINE_TASKS:
                if (null != _routineTasksDataSource)
                {
                    num = _routineTasksDataSource.getMaxActive();
                } else
                {
                    num = -1;
                }
                break;

            case Constants.IEAI_AZ_GROUP:
                if (null != _az_group_DataSource)
                {
                    num = _az_group_DataSource.getMaxActive();
                } else
                {
                    num = -1;
                }
                break;

            case Constants.IEAI_SUS_GROUP:
                if (null != _sus_group_DataSource)
                {
                    num = _sus_group_DataSource.getMaxActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_TOOLS:
                if (null != _toolsDataSource)
                {
                    num = _toolsDataSource.getMaxActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_SYS_CHANGE:
                if (null != _sysChangeDataSource)
                {
                    num = _sysChangeDataSource.getMaxActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_EMS:
                if (null != _emsDataSource)
                {
                    num = _emsDataSource.getMaxActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_BMI:
                if (null != _bmiDataSource)
                {
                    num = _bmiDataSource.getMaxActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_SCRIPT_DBAAS:
                boolean dbaasCon = ServerEnv.getInstance().getBooleanConfigNew2("dbaas.conn.alone", false);
                if (!dbaasCon)
                {
                    num = -1;
                } else
                {
                    if (null != _dbaasDirDataSource)
                    {
                        num = _dbaasDirDataSource.getMaxActive();
                    } else
                    {
                        num = -1;
                    }
                }
                break;
            case Constants.IEAI_SHUTDOWN_MAINTAIN:
                if (null != _shutdownMaintainDataSource)
                {
                    num = _shutdownMaintainDataSource.getMaxActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_FSH:
                if (null != _fshDataSource)
                {
                    num = _fshDataSource.getMaxActive();
                } else
                {
                    num = -1;
                }
                break;
            default:
                num = -1;
                break;
        }
        return num;
    }

    public int getDBSNumberIdle ( int type )
    {
        int num = 0;
        switch (type)
        {
            case Constants.IEAI_IEAI_BASIC:
                if (null != _jdbcDataSource)
                {
                    num = _jdbcDataSource.getNumIdle();
                } else
                {
                    num = 0;
                }
                break;
            case Constants.IEAI_OPM:
                if (null != _ieaiOPM)
                {
                    num = _ieaiOPM.getNumIdle();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_LOOPHOLE_MANAGE:
                if (null != _ieaiLoopholeManage)
                {
                    num = _ieaiLoopholeManage.getNumIdle();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_IEAI:
                if (null != _ieaiDataSource)
                {
                    num = _ieaiDataSource.getNumIdle();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_INFOCOLLECTION:
                if (null != _infoCollectionDataSource)
                {
                    num = _infoCollectionDataSource.getNumIdle();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_SUS:
                if (null != _autoDataSource)
                {
                    num = _autoDataSource.getNumIdle();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_EMERGENCY_SWITCH:
                if (null != _disRecDataSource)
                {
                    num = _disRecDataSource.getNumIdle();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_HEALTH_INSPECTION:
                if (null != _healthInspectionDataSource)
                {
                    num = _healthInspectionDataSource.getNumIdle();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_DAILY_OPERATIONS:
                if (null != _dailyOperationsDataSource)
                {
                    num = _dailyOperationsDataSource.getNumIdle();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_TIMINGTASK:
                if (null != _timeTaskDataSource)
                {
                    num = _timeTaskDataSource.getNumIdle();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_EMERGENCY_OPER:
                if (null != _emergencyOperDataSource)
                {
                    num = _emergencyOperDataSource.getNumIdle();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_SCRIPT_SERVICE:
                if (null != _scriptServiceDataSource)
                {
                    num = _scriptServiceDataSource.getNumIdle();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_SERVER_START_AND_STOP:
                if (null != _disServiceDataSource)
                {
                    num = _disServiceDataSource.getNumIdle();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_COMPARE:
                if (null != _compareServiceDataSource)
                {
                    num = _compareServiceDataSource.getNumIdle();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_CMDB:
                if (null != _cmdbServiceDataSource)
                {
                    num = _cmdbServiceDataSource.getNumIdle();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_APM:
                if (null != _apmDataSource)
                {
                    num = _apmDataSource.getNumIdle();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_TOPO:
                if (null != _topoServiceDataSource)
                {
                    num = _topoServiceDataSource.getNumIdle();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_DATA_COLLECT:
                if (null != _dataCollectDataSource)
                {
                    num = _dataCollectDataSource.getNumIdle();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_PAAS:
                if (null != _paasDataSource)
                {
                    num = _paasDataSource.getNumIdle();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_ROUTINE_TASKS:
                if (null != _routineTasksDataSource)
                {
                    num = _routineTasksDataSource.getNumIdle();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_AZ:
                if (null != _azDataSource)
                {
                    num = _azDataSource.getNumIdle();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_SHUTDOWN_MAINTAIN:
                if (null != _shutdownMaintainDataSource)
                {
                    num = _shutdownMaintainDataSource.getNumActive();
                } else
                {
                    num = -1;
                }
                break;
            case Constants.IEAI_FSH:
                if (null != _fshDataSource)
                {
                    num = _fshDataSource.getNumActive();
                } else
                {
                    num = -1;
                }
                break;
            default:
                num = -1;
                break;
        }
        return num;
    }

    public int getNumberActive_Schedule ()
    {
        // return _schedulerDataSource.getNumActive();
        return 20;
    }

    public int getMaxActiveSchedule ()
    {
        // return _schedulerDataSource.getMaxActive();
        return 200;
    }

    public int getNumberActive_JDBC ()
    {
        return _jdbcDataSource.getNumActive();
    }

    public int getNumberIdle_JDBC ()
    {
        return _jdbcDataSource.getNumIdle();
    }

    public int getMaxActiveJDBC ()
    {
        return _jdbcDataSource.getMaxActive();
    }

    public Map<String, BasicDataSource> getDBSourceMap ()
    {
        return _dataSourceMap;
    }

    public Map<Integer, String> getDBSourceFlagMap ()
    {
        return _dataSourceFlag;
    }

    /**
     * <AUTHOR>
     * @des:数据库资源异常
     * @datea:2010-7-22
     * @param dbPool 数据库连接池
     * @param message 异常描述
     */
    public void getDBMonitor ( String dbPool, String message )
    {
        if (MonitorEnv.getBooleanConfig("ENTEGORWARN", false) && MonitorEnv.getBooleanConfig("DBERR", false))
        {
            WarningManager.getInstance().getDBMonitor(dbPool + message);
        }
    }

    /**
     *
     * <li>Description:为脱离Server环境独立测试程序使用的数据库连接. 正常情况下，关联查询该方法时，应该没有任何非测试程序使用该方法。</li>
     *
     * <AUTHOR> 2015年12月8日
     * @return return Connection
     */
    // public Connection getConnForClass ()
    // {
    // Connection conn = null;
    // try
    // {
    // System.out.println("正在连接数据库..........");
    // Class.forName("com.ibm.db2.jcc.DB2Driver");// 加载mysql驱动程序类
    // String url = "jdbc:db2://***************:60000/aomsdb";// url为连接字符串
    // String user = "db2inst1";// 数据库用户名
    // String pwd = "entegor";// 数据库密码
    // conn = (Connection) DriverManager.getConnection(url, user, pwd);
    // System.out.println("数据库连接成功！！！");
    // } catch (Exception e)
    // {
    // System.out.println(e.getMessage());
    // // e.printStackTrace();
    // }
    // return conn;
    // }

    // private static BasicDataSource _dataSource;
    private static BasicDataSource              _jdbcDataSource;
    // private static BasicDataSource _storeDataSource;
    private static BasicDataSource              _schedulerDataSource;

    private static DBManager                    _instance;

    /**
     * true if DBManager has been initialized; false otherwise.
     */

    private static DBConfig                     _dbCfg;

    static public final String                  STR_ENC                  = "UTF8";

    static public final int                     DIRECT                   = 0;
    static public final int                     LOB                      = 1;

    private static Map<String, BasicDataSource> _dataSourceMap           = new HashMap<String, BasicDataSource>();
    private static Map                          _dataSourceFlag          = new HashMap();

    private static BasicDataSource              _ieaiOPM;
    private static BasicDataSource              _ieaiLoopholeManage;
    private static BasicDataSource              _ieaiDataSource;                                                                                           // 作业调度
    private static BasicDataSource              _infoCollectionDataSource;                                                                       // 信息采集
    private static BasicDataSource              _autoDataSource;                                                                                           // 变更
    private static BasicDataSource              _disRecDataSource;                                                                                       // 灾备
    private static BasicDataSource              _disServiceDataSource;                                                                               // 应用启停
    private static BasicDataSource              _emergencyOperDataSource = null;                                                             // 应急
    private static BasicDataSource              _healthInspectionDataSource;                                      // 健康巡检
    private static BasicDataSource              _dailyOperationsDataSource;                                       // 日常操作
    private static BasicDataSource              _scriptServiceDataSource = null;                                                             // 脚本服务化
    private static BasicDataSource              _timeTaskDataSource;                                                                                   // 定时任务
    private static BasicDataSource              _compareServiceDataSource;                                                                       // 一致性比对
    private static BasicDataSource              _cmdbServiceDataSource;                                                                             // cmdb配置管理
    private static BasicDataSource              _apmDataSource;                                                                                             // 应用维护
    private static BasicDataSource              _topoServiceDataSource;                                                                             // cmdb配置管理
    private static BasicDataSource              _icSource;
    private static BasicDataSource              _dataCollectDataSource;     // 数据采集
    private static BasicDataSource              _azDataSource;              // AZ切换
    private static BasicDataSource              _fshDataSource;              // fsh
    private static BasicDataSource              _sus_group_DataSource;
    private static BasicDataSource              _az_group_DataSource;
    private static BasicDataSource              _paasDataSource;

    private static BasicDataSource              _routineTasksDataSource;  // 例行任务

    private static BasicDataSource              _toolsDataSource;                                                                               // 工具箱
    private static BasicDataSource              _sysChangeDataSource;                                                                               // 系统变更
    private static BasicDataSource              _emsDataSource;             // 外管报送
    private static BasicDataSource              _bmiDataSource;             // 裸机安装
    private static BasicDataSource              _dbaasDirDataSource;                                                                               // 应用启停

    private static BasicDataSource              _shutdownMaintainDataSource; //关机维护

    public static final synchronized void initDBSource ( DBConfig dbCfg, String key, int type ) throws DBException
    {
        if (!_dataSourceMap.containsKey(key))
        {
            _log.info("Initializing DB layer type:" + type + "...");

            String driverPath = ServerEnv.getServerEnv().getIEAIHome() + "/lib";
            String driverClassName = dbCfg.getDriverClass();
            _log.info("驱动名称:" + driverClassName);
            Class driverClass = null;
            DBDriverClassLoader _dbDriverClsLoader = DBDriverClassLoader.getDBDriverClassLoader(driverPath);
            try
            {
                driverClass = _dbDriverClsLoader.loadClass(driverClassName);
            } catch (ClassNotFoundException ex1)
            {
                throw new DBException("Failed loading JDBC Driver class:" + driverClassName, ex1);
            }
            if (!com.ideal.util.JDBCDriverWrapper.registerDriver(driverClass))
            {
                throw new DBException("Error registering JDBC Driver class:" + driverClassName);
            }

            try
            {
                initOthConnectionPool(dbCfg, type);
                _log.info("DB layer initialization succeeded!");
            } catch (Exception ex)
            {
                _log.error("initDBSource", ex);
                throw new DBException(ex.getMessage());
            }
        } else
        {
            try
            {
                switch (type)
                {
                    case Constants.IEAI_OPM:
                        _ieaiOPM = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_LOOPHOLE_MANAGE:
                        _ieaiLoopholeManage = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_IEAI:
                        _ieaiDataSource = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_INFOCOLLECTION:
                        _infoCollectionDataSource = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_SUS:
                        _autoDataSource = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_EMERGENCY_SWITCH:
                        _disRecDataSource = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_TIMINGTASK:
                        _timeTaskDataSource = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_EMERGENCY_OPER:
                        _emergencyOperDataSource = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_HEALTH_INSPECTION:
                        _healthInspectionDataSource = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_DAILY_OPERATIONS:
                        _dailyOperationsDataSource = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_SCRIPT_SERVICE:
                        _scriptServiceDataSource = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_SERVER_START_AND_STOP:
                        _disServiceDataSource = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_COMPARE:
                        _compareServiceDataSource = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_CMDB:
                        _cmdbServiceDataSource = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_APM:
                        _apmDataSource = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_TOPO:
                        _topoServiceDataSource = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_INFO_COLLECT:
                        _icSource = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_DATA_COLLECT:
                        _dataCollectDataSource = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_AZ:
                        _azDataSource = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_PAAS:
                        _paasDataSource = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_ROUTINE_TASKS:
                        _routineTasksDataSource = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_AZ_GROUP:
                        _az_group_DataSource = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_SUS_GROUP:
                        _sus_group_DataSource = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_TOOLS:
                        _toolsDataSource = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_SYS_CHANGE:
                        _sysChangeDataSource = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_EMS:
                        _emsDataSource = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_BMI:
                        _bmiDataSource = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_SCRIPT_DBAAS:
                        _dbaasDirDataSource = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_SHUTDOWN_MAINTAIN:
                        _shutdownMaintainDataSource = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_FSH:
                        _fshDataSource = _dataSourceMap.get(key);
                        break;

                }
            } catch (Exception e)
            {
                _log.info(" initdb-source is failer, type: " + type, e);
                throw new DBException(e.getMessage());
            }
        }
    }

    /**
     *
     * <li>Description: 按模块类型获取数据源
     * 增加该方法，目前主要为POC开发，快速实现增删改查方法使用。
     * 当正式投入生产后，可能会需要思考连接泄露方面的问题。</li>
     * <AUTHOR>
     * 2017年3月25日
     * @param modelType
     * @return
     * return BasicDataSource
     */
    public BasicDataSource getDBSource ( int modelType )
    {
        BasicDataSource ret = null;

        // 增加一步数据源的初始化，避免未初始化就进行数据源的获取
        try
        {
            this.getDbConnection(modelType);
        } catch (DBException e)
        {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        switch (modelType)
        {
            case Constants.IEAI_IEAI_BASIC:
                ret = _jdbcDataSource;
                break;
            case Constants.IEAI_OPM:
                ret = _ieaiOPM;
                break;
            case Constants.IEAI_LOOPHOLE_MANAGE :
                ret = _ieaiLoopholeManage;
                break;
            case Constants.IEAI_IEAI:
                ret = _ieaiDataSource;
                break;
            case Constants.IEAI_INFOCOLLECTION:
                ret = _infoCollectionDataSource;
                break;
            case Constants.IEAI_SUS:
                ret = _autoDataSource;
                break;
            case Constants.IEAI_EMERGENCY_SWITCH:
                ret = _disRecDataSource;
                break;
            case Constants.IEAI_TIMINGTASK:
                ret = _timeTaskDataSource;
                break;
            case Constants.IEAI_EMERGENCY_OPER:
                ret = _emergencyOperDataSource;
                break;

            case Constants.IEAI_INFO_COLLECT:
                ret = _icSource;
                break;
            case Constants.IEAI_PAAS:
                ret = _paasDataSource;
                break;
            case Constants.IEAI_ROUTINE_TASKS:
                ret = _routineTasksDataSource;
                break;
            case Constants.IEAI_HEALTH_INSPECTION:
                ret = _healthInspectionDataSource;
                break;
            case Constants.IEAI_DAILY_OPERATIONS:
                ret = _dailyOperationsDataSource;
                break;
            case Constants.IEAI_SCRIPT_SERVICE:
                ret = _scriptServiceDataSource;
                break;
            case Constants.IEAI_SERVER_START_AND_STOP:
                ret = _disServiceDataSource;
                break;
            case Constants.IEAI_COMPARE:
                ret = _compareServiceDataSource;
                break;
            case Constants.IEAI_CMDB:
                ret = _cmdbServiceDataSource;
                break;
            case Constants.IEAI_APM:
                ret = _apmDataSource;
                break;
            case Constants.IEAI_TOPO:
                ret = _topoServiceDataSource;
                break;
            case Constants.IEAI_DATA_COLLECT:
                ret = _dataCollectDataSource;
                break;
            case Constants.IEAI_AZ:
                ret = _azDataSource;
                break;
            case Constants.IEAI_SUS_GROUP:
                ret = _sus_group_DataSource;
                break;
            case Constants.IEAI_AZ_GROUP:
                ret = _az_group_DataSource;
                break;
            case Constants.IEAI_TOOLS:
                ret = _toolsDataSource;
                break;
            case Constants.IEAI_SYS_CHANGE:
                ret = _sysChangeDataSource;
                break;
            case Constants.IEAI_EMS:
                ret = _emsDataSource;
                break;
            case Constants.IEAI_BMI:
                ret = _bmiDataSource;
                break;
            case Constants.IEAI_SCRIPT_DBAAS:
                ret = _dbaasDirDataSource;
                break;
            case Constants.IEAI_SHUTDOWN_MAINTAIN:
                ret = _shutdownMaintainDataSource;
                break;
            case Constants.IEAI_FSH:
                ret = _fshDataSource;
                break;
            default:
            {
                _dataSourceMap.size();
                ret = _timeTaskDataSource;
            }
        }
        return ret;
    }

    public static void dbsourceConfig ( BasicDataSource jdbcDataSource, DBPoolConfig poolConfig, int type )
    {
        int initSize = ServerEnv.getInstance().getIntConfig("dbpool.other.initSize", 0);
        int maxActive = ServerEnv.getInstance().getIntConfig("dbpool.other.maxActive", 0);
        int maxIdle = ServerEnv.getInstance().getIntConfig("dbpool.other.maxIdle", 0);
        int minIdle = ServerEnv.getInstance().getIntConfig("dbpool.other.minIdle", 0);
        if (initSize == 0)
        {
            initSize = poolConfig.getInitSize() / 5;
        }
        if (maxActive == 0)
        {
            maxActive = poolConfig.getMaxActive() / 5;
        }
        if (maxIdle == 0)
        {
            maxIdle = poolConfig.getMaxIdle() / 5;
            if (maxIdle <= DB_INIT_MAXIDLE)
            {
                maxIdle = DB_INIT_MAXIDLE;
            }
        }
        if (minIdle == 0)
        {
            minIdle = poolConfig.getMinIdle() / 5;
            if (minIdle <= 2)
            {
                minIdle = DB_INIT_MINIDLE;
            }
        }
        String key = String.valueOf(type);
        if (dbCfgMap.containsKey(key))
        {
            DBConfig dbc = dbCfgMap.get(key);
            if (null != dbc)
            {
                DBPoolConfig dbpc = dbc.getPoolConfig();
                if (null != dbpc)
                {
                    if (dbpc.getInitSize() != 0)
                    {
                        initSize = dbpc.getInitSize();
                    }
                    if (dbpc.getMaxActive() != 0)
                    {
                        maxActive = dbpc.getMaxActive();
                    }
                    if (dbpc.getMaxIdle() != 0)
                    {
                        maxIdle = dbpc.getMaxIdle();
                    }
                    if (dbpc.getMinIdle() != 0)
                    {
                        minIdle = dbpc.getMinIdle();
                    }
                }
            }
        }
        jdbcDataSource.setInitialSize(initSize);
        jdbcDataSource.setMaxActive(maxActive);
        jdbcDataSource.setMaxIdle(maxIdle);
        jdbcDataSource.setMinIdle(minIdle);
    }

    /**
     * 初始化数据源 <li>Description:</li>
     *
     * <AUTHOR> 2016年5月20日
     * @param dbCfg
     * @param type
     * @throws Exception return void
     */
    private static void initOthConnectionPool ( DBConfig dbCfg, int type ) throws Exception
    {
        String key = type + "--" + dbCfg.getUrl() + "--ieai--" + dbCfg.getDbUser();
        if ("".equals(key))
        {
            throw new DBException("数据库-数据源表中没有获取对应数据源!type:" + type);
        }
        if (!_dataSourceMap.containsKey(key))
        {
            BasicDataSource jdbcDataSource = new BasicDataSource();
            DBPoolConfig poolConfig = dbCfg.getPoolConfig();
            jdbcDataSource.setDriverClassName(dbCfg.getDriverClass());
            jdbcDataSource.setUrl(dbCfg.getUrl());
            jdbcDataSource.setUsername(dbCfg.getDbUser());
            jdbcDataSource.setPassword(dbCfg.getDbPassword());
            jdbcDataSource.setTestOnBorrow(poolConfig.getTestOnBorrow());
            if (dbCfg.getPoolConfig().getTestOnBorrow())
            {
                jdbcDataSource.setValidationQuery(poolConfig.getValidationQuery());
            }

            dbsourceConfig(jdbcDataSource, poolConfig, type);

            jdbcDataSource.setMaxWait(30000);

            jdbcDataSource.setRemoveAbandoned(poolConfig.isRemoveAbandoned());
            jdbcDataSource.setRemoveAbandonedTimeout(poolConfig.getRemoveAbandonedTimeout());
            jdbcDataSource.setLogAbandoned(poolConfig.isLogAbandoned());
            //provider 数据库连接池配置，增加空闲连接回收器参数
            if (DBManager.requestFrom == 1)
            {
                jdbcDataSource.setTestWhileIdle(poolConfig.getTestWhileIdle());
                jdbcDataSource.setMinEvictableIdleTimeMillis(poolConfig.getMinEvictableIdleTimeMillis());
                jdbcDataSource.setTimeBetweenEvictionRunsMillis(poolConfig.getTimeBetweenEvictionRunsMillis());
                jdbcDataSource.setNumTestsPerEvictionRun(poolConfig.getNumTestsPerEvictionRun());
                jdbcDataSource.setValidationQuery(poolConfig.getValidationQuery());
            }

            Connection conn = null;
            try
            {
                switch (type)
                {
                    case Constants.IEAI_OPM:
                        conn = jdbcDataSource.getConnection();
                        _ieaiOPM = jdbcDataSource;
                        _dataSourceMap.put(key, _ieaiOPM);
                        _dataSourceFlag.put(Constants.IEAI_OPM, key);
                        break;
                    case Constants.IEAI_LOOPHOLE_MANAGE :
                        conn = jdbcDataSource.getConnection();
                        _ieaiLoopholeManage = jdbcDataSource;
                        _dataSourceMap.put(key, _ieaiLoopholeManage);
                        _dataSourceFlag.put(Constants.IEAI_LOOPHOLE_MANAGE , key);
                        break;
                    case Constants.IEAI_SUS_GROUP:
                        conn = jdbcDataSource.getConnection();
                        _sus_group_DataSource = jdbcDataSource;
                        _dataSourceMap.put(key, _sus_group_DataSource);
                        _dataSourceFlag.put(Constants.IEAI_SUS_GROUP, key);
                        break;
                    case Constants.IEAI_AZ_GROUP:
                        conn = jdbcDataSource.getConnection();
                        _az_group_DataSource = jdbcDataSource;
                        _dataSourceMap.put(key, _az_group_DataSource);
                        _dataSourceFlag.put(Constants.IEAI_AZ_GROUP, key);
                        break;
                    case Constants.IEAI_IEAI:
                        conn = jdbcDataSource.getConnection();
                        _ieaiDataSource = jdbcDataSource;
                        _dataSourceMap.put(key, _ieaiDataSource);
                        _dataSourceFlag.put(Constants.IEAI_IEAI, key);
                        break;
                    case Constants.IEAI_INFOCOLLECTION:
                        conn = jdbcDataSource.getConnection();
                        _infoCollectionDataSource = jdbcDataSource;
                        _dataSourceMap.put(key, _infoCollectionDataSource);
                        _dataSourceFlag.put(Constants.IEAI_INFOCOLLECTION, key);
                        break;
                    case Constants.IEAI_SUS:
                        conn = jdbcDataSource.getConnection();
                        _autoDataSource = jdbcDataSource;
                        _dataSourceMap.put(key, _autoDataSource);
                        _dataSourceFlag.put(Constants.IEAI_SUS, key);
                        break;
                    case Constants.IEAI_EMERGENCY_SWITCH:
                        conn = jdbcDataSource.getConnection();
                        _disRecDataSource = jdbcDataSource;
                        _dataSourceMap.put(key, _disRecDataSource);
                        _dataSourceFlag.put(Constants.IEAI_EMERGENCY_SWITCH, key);
                        break;
                    case Constants.IEAI_TIMINGTASK:
                        conn = jdbcDataSource.getConnection();
                        _timeTaskDataSource = jdbcDataSource;
                        _dataSourceMap.put(key, _timeTaskDataSource);
                        _dataSourceFlag.put(Constants.IEAI_TIMINGTASK, key);
                        break;
                    case Constants.IEAI_EMERGENCY_OPER:
                        conn = jdbcDataSource.getConnection();
                        _emergencyOperDataSource = jdbcDataSource;
                        _dataSourceMap.put(key, _emergencyOperDataSource);
                        _dataSourceFlag.put(Constants.IEAI_EMERGENCY_OPER, key);
                        break;
                    case Constants.IEAI_HEALTH_INSPECTION:
                        conn = jdbcDataSource.getConnection();
                        _healthInspectionDataSource = jdbcDataSource;
                        _dataSourceMap.put(key, _healthInspectionDataSource);
                        _dataSourceFlag.put(Constants.IEAI_HEALTH_INSPECTION, key);
                        break;
                    case Constants.IEAI_DAILY_OPERATIONS:
                        conn = jdbcDataSource.getConnection();
                        _dailyOperationsDataSource = jdbcDataSource;
                        _dataSourceMap.put(key, _dailyOperationsDataSource);
                        _dataSourceFlag.put(Constants.IEAI_DAILY_OPERATIONS, key);
                        break;
                    case Constants.IEAI_SCRIPT_SERVICE:
                        conn = jdbcDataSource.getConnection();
                        _scriptServiceDataSource = jdbcDataSource;
                        _dataSourceMap.put(key, _scriptServiceDataSource);
                        _dataSourceFlag.put(Constants.IEAI_SCRIPT_SERVICE, key);
                        break;
                    case Constants.IEAI_SERVER_START_AND_STOP:
                        conn = jdbcDataSource.getConnection();
                        _disServiceDataSource = jdbcDataSource;
                        _dataSourceMap.put(key, _disServiceDataSource);
                        _dataSourceFlag.put(Constants.IEAI_SERVER_START_AND_STOP, key);
                        break;
                    case Constants.IEAI_COMPARE:
                        conn = jdbcDataSource.getConnection();
                        _compareServiceDataSource = jdbcDataSource;
                        _dataSourceMap.put(key, _compareServiceDataSource);
                        _dataSourceFlag.put(Constants.IEAI_COMPARE, key);
                        break;
                    case Constants.IEAI_CMDB:
                        conn = jdbcDataSource.getConnection();
                        _cmdbServiceDataSource = jdbcDataSource;
                        _dataSourceMap.put(key, _cmdbServiceDataSource);
                        _dataSourceFlag.put(Constants.IEAI_CMDB, key);
                        break;
                    case Constants.IEAI_APM:
                        conn = jdbcDataSource.getConnection();
                        _apmDataSource = jdbcDataSource;
                        _dataSourceMap.put(key, _apmDataSource);
                        _dataSourceFlag.put(Constants.IEAI_APM, key);
                        break;
                    case Constants.IEAI_TOPO:
                        conn = jdbcDataSource.getConnection();
                        _topoServiceDataSource = jdbcDataSource;
                        _dataSourceMap.put(key, _topoServiceDataSource);
                        _dataSourceFlag.put(Constants.IEAI_TOPO, key);
                        break;

                    case Constants.IEAI_INFO_COLLECT:
                        conn = jdbcDataSource.getConnection();
                        _icSource = jdbcDataSource;
                        _dataSourceMap.put(key, _icSource);
                        _dataSourceFlag.put(Constants.IEAI_INFO_COLLECT, key);
                        break;
                    case Constants.IEAI_DATA_COLLECT:
                        conn = jdbcDataSource.getConnection();
                        _dataCollectDataSource = jdbcDataSource;
                        _dataSourceMap.put(key, _dataCollectDataSource);
                        _dataSourceFlag.put(Constants.IEAI_DATA_COLLECT, key);
                        break;
                    case Constants.IEAI_AZ:
                        conn = jdbcDataSource.getConnection();
                        _azDataSource = jdbcDataSource;
                        _dataSourceMap.put(key, _azDataSource);
                        _dataSourceFlag.put(Constants.IEAI_AZ, key);
                        break;
                    case Constants.IEAI_PAAS:
                        conn = jdbcDataSource.getConnection();
                        _paasDataSource = jdbcDataSource;
                        _dataSourceMap.put(key, _paasDataSource);
                        _dataSourceFlag.put(Constants.IEAI_PAAS, key);
                        break;

                    case Constants.IEAI_ROUTINE_TASKS:
                        conn = jdbcDataSource.getConnection();
                        _routineTasksDataSource = jdbcDataSource;
                        _dataSourceMap.put(key, _routineTasksDataSource);
                        _dataSourceFlag.put(Constants.IEAI_ROUTINE_TASKS, key);
                        break;

                    case Constants.IEAI_FSH:
                        conn = jdbcDataSource.getConnection();
                        _fshDataSource = jdbcDataSource;
                        _dataSourceMap.put(key, _fshDataSource);
                        _dataSourceFlag.put(Constants.IEAI_FSH, key);
                        break;
                    case Constants.IEAI_TOOLS:
                        conn = jdbcDataSource.getConnection();
                        _toolsDataSource = jdbcDataSource;
                        _dataSourceMap.put(key, _toolsDataSource);
                        _dataSourceFlag.put(Constants.IEAI_TOOLS, key);
                        break;
                    case Constants.IEAI_SYS_CHANGE:
                        conn = jdbcDataSource.getConnection();
                        _sysChangeDataSource = jdbcDataSource;
                        _dataSourceMap.put(key, _sysChangeDataSource);
                        _dataSourceFlag.put(Constants.IEAI_SYS_CHANGE, key);
                        break;
                    case Constants.IEAI_EMS:
                        conn = jdbcDataSource.getConnection();
                        _emsDataSource = jdbcDataSource;
                        _dataSourceMap.put(key, _emsDataSource);
                        _dataSourceFlag.put(Constants.IEAI_EMS, key);
                        break;
                    case Constants.IEAI_BMI:
                        conn = jdbcDataSource.getConnection();
                        _bmiDataSource = jdbcDataSource;
                        _dataSourceMap.put(key, _bmiDataSource);
                        _dataSourceFlag.put(Constants.IEAI_BMI, key);
                        break;
                    case Constants.IEAI_SCRIPT_DBAAS:
                        conn = jdbcDataSource.getConnection();
                        _dbaasDirDataSource = jdbcDataSource;
                        _dataSourceMap.put(key, _dbaasDirDataSource);
                        _dataSourceFlag.put(Constants.IEAI_SCRIPT_DBAAS, key);
                        break;
                    case Constants.IEAI_SHUTDOWN_MAINTAIN:
                        conn = jdbcDataSource.getConnection();
                        _shutdownMaintainDataSource = jdbcDataSource;
                        _dataSourceMap.put(key, _shutdownMaintainDataSource);
                        _dataSourceFlag.put(Constants.IEAI_SHUTDOWN_MAINTAIN, key);
                        break;
                }
            } catch (Exception e)
            {
                _log.info(" initOthConnectionPool initdb-source is failer,type:" + type, e);
                if (jdbcDataSource != null)
                {
                    jdbcDataSource.close();
                }
                throw new DBException(e.getMessage());
            } finally
            {
                if (conn != null)
                {
                    conn.close();
                }
            }
            _log.info("Datasource  type:" + type + " initialization succeeded!");
        } else
        {
            try
            {
                switch (type)
                {
                    case Constants.IEAI_OPM:
                        _ieaiOPM = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_LOOPHOLE_MANAGE :
                        _ieaiLoopholeManage = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_IEAI:
                        _ieaiDataSource = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_INFOCOLLECTION:
                        _infoCollectionDataSource = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_SUS:
                        _autoDataSource = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_EMERGENCY_SWITCH:
                        _disRecDataSource = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_TIMINGTASK:
                        _timeTaskDataSource = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_EMERGENCY_OPER:
                        _emergencyOperDataSource = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_HEALTH_INSPECTION:
                        _healthInspectionDataSource = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_DAILY_OPERATIONS:
                        _dailyOperationsDataSource = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_SCRIPT_SERVICE:
                        _scriptServiceDataSource = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_SERVER_START_AND_STOP:
                        _disServiceDataSource = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_APM:
                        _apmDataSource = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_TOPO:
                        _topoServiceDataSource = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_INFO_COLLECT:
                        _icSource = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_AZ:
                        _azDataSource = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_AZ_GROUP:
                        _az_group_DataSource = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_SUS_GROUP:
                        _sus_group_DataSource = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_TOOLS:
                        _toolsDataSource = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_SYS_CHANGE:
                        _sysChangeDataSource = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_EMS:
                        _emsDataSource = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_BMI:
                        _bmiDataSource = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_SCRIPT_DBAAS:
                        _dbaasDirDataSource = _dataSourceMap.get(key);
                        break;
                    case Constants.IEAI_SHUTDOWN_MAINTAIN :
                        _shutdownMaintainDataSource = _dataSourceMap.get(key);
                        break;
                }
            } catch (Exception e)
            {
                _log.info("initOthConnectionPool initdb-source is failer,type:" + type, e);
                throw new DBException(e.getMessage());
            }
        }
    }

    /**
     * 从数据源表中查询数据源信息 <li>Description:</li> method 0,获取按钮时使用，1：数据源初始化时使用
     *
     * <AUTHOR> 2016年5月20日
     * @param type
     * @return return DBSourceMonitor
     * @throws DBException
     */
    public static DBSourceMonitor getServerDbResource ( int type, int method ) throws DBException
    {
        DBSourceMonitor dfc = null;
        String sql = "SELECT ISERVERIP,IDBURL,IDBUSER,IDBPASS,IISBASIC  FROM IEAI_DBSOURCE WHERE IGROUPMESSGEID=? ";
        Connection con = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            con = _jdbcDataSource.getConnection();
            ps = con.prepareStatement(sql);
            ps.setInt(1, type);
            rs = ps.executeQuery();
            while (rs.next())
            {
                if (method == 0)
                {
                    dfc = new DBSourceMonitor();
                    // dfc.setDriverClass("");
                    dfc.set_url(rs.getString("IDBURL"));
                    dfc.set_dbUser(rs.getString("IDBUSER"));
                    // dfc.set_dbPassword(rs.getString("IDBPASS"));
                    String pwd = rs.getString("IDBPASS");
                    String pw = "";
                    if (!"".equals(pwd) && null != pwd && !"null".equals(pwd) && 1 == 2)
                    {
                        pw = Jcrypt.decrypt(rs.getString("IDBPASS"));
                    } else
                    {
                        pw = pwd;
                    }
                    dfc.set_dbPassword(pw);
                } else
                {
                    if (!"".equals(rs.getString("IDBURL")) && null != rs.getString("IDBURL"))
                    {
                        dfc = new DBSourceMonitor();
                        // dfc.setDriverClass("");
                        dfc.set_url(rs.getString("IDBURL"));
                        dfc.set_dbUser(rs.getString("IDBUSER"));
                        // dfc.set_dbPassword(rs.getString("IDBPASS"));
                        String pwd = rs.getString("IDBPASS");
                        String pw = "";
                        if (!"".equals(pwd) && null != pwd && !"null".equals(pwd) && 1 == 2)
                        {
                            pw = Jcrypt.decrypt(rs.getString("IDBPASS"));
                        } else
                        {
                            pw = pwd;
                        }
                        dfc.set_dbPassword(pw);
                    }
                }
            }
        } catch (SQLException e1)
        {
            dfc = null;
            e1.printStackTrace();
            throw new DBException(e1.getMessage());
        } finally
        {
            if (null != con)
            {
                try
                {
                    con.close();
                } catch (SQLException e)
                {
                    e.printStackTrace();
                    throw new DBException(e.getMessage());
                }
            }
        }
        return dfc;
    }

    public boolean checkDbConnection ( int type ) throws DBException
    {
        boolean flag = false;
        Connection con = null;
        try
        {
            DBSourceMonitor dsm = getServerDbResource(type, 1);
            String key = "";
            String pwd = "";
            if (null != dsm)
            {
                key = type + "--" + dsm.get_url() + "--ieai--" + dsm.get_dbUser();
                pwd = dsm.get_dbPassword();
            }
            switch (type)
            {
                case Constants.IEAI_OPM:
                    try
                    {
                        if (_dataSourceMap.containsKey(key))
                        {
                            if (null != _ieaiOPM)
                            {
                                if (pwd.equals(_ieaiOPM.getPassword()))
                                {
                                    con = _ieaiOPM.getConnection();
                                } else
                                {

                                    _ieaiOPM.close();
                                    _ieaiOPM = null;
                                    // 用户修改密码，删除map中的source，在新初始化一个新的进入map
                                    // 看是否是公用数据源，不是可直接删除，是还需要看表中是否还存在该记录
                                    _dataSourceMap.remove(key);
                                    _dataSourceFlag.remove(Constants.IEAI_OPM);
                                    // 初始化新的数据源
                                    con = this.getJdbcConnection(type);
                                }
                            }
                        } else
                        {
                            if (null != _ieaiOPM)
                            {// 证明该数据源修改用户名或者url，
                                String oldKey = (String) _dataSourceFlag.get(Constants.IEAI_OPM);
                                _ieaiOPM.close();
                                _ieaiOPM = null;
                                _dataSourceFlag.remove(Constants.IEAI_OPM);
                                _dataSourceMap.remove(oldKey);
                            }
                            con = this.getJdbcConnection(type);
                        }
                        flag = true;
                    } catch (Exception e)
                    {
                        flag = false;
                        throw new DBException(e.getMessage());
                    }
                    break;
                case Constants.IEAI_LOOPHOLE_MANAGE :
                    try
                    {
                        if (_dataSourceMap.containsKey(key))
                        {
                            if (null != _ieaiLoopholeManage)
                            {
                                if (pwd.equals(_ieaiLoopholeManage.getPassword()))
                                {
                                    con = _ieaiLoopholeManage.getConnection();
                                } else
                                {

                                    _ieaiLoopholeManage.close();
                                    _ieaiLoopholeManage = null;
                                    // 用户修改密码，删除map中的source，在新初始化一个新的进入map
                                    // 看是否是公用数据源，不是可直接删除，是还需要看表中是否还存在该记录
                                    _dataSourceMap.remove(key);
                                    _dataSourceFlag.remove(Constants.IEAI_LOOPHOLE_MANAGE );
                                    // 初始化新的数据源
                                    con = this.getJdbcConnection(type);
                                }
                            }
                        } else
                        {
                            if (null != _ieaiLoopholeManage)
                            {// 证明该数据源修改用户名或者url，
                                String oldKey = (String) _dataSourceFlag.get(Constants.IEAI_LOOPHOLE_MANAGE );
                                _ieaiLoopholeManage.close();
                                _ieaiLoopholeManage = null;
                                _dataSourceFlag.remove(Constants.IEAI_LOOPHOLE_MANAGE );
                                _dataSourceMap.remove(oldKey);
                            }
                            con = this.getJdbcConnection(type);
                        }
                        flag = true;
                    } catch (Exception e)
                    {
                        flag = false;
                        throw new DBException(e.getMessage());
                    }
                    break;
                case Constants.IEAI_IEAI:
                    try
                    {
                        if (_dataSourceMap.containsKey(key))
                        {
                            if (null != _ieaiDataSource)
                            {
                                if (pwd.equals(_ieaiDataSource.getPassword()))
                                {
                                    con = _ieaiDataSource.getConnection();
                                } else
                                {

                                    _ieaiDataSource.close();
                                    _ieaiDataSource = null;
                                    // 用户修改密码，删除map中的source，在新初始化一个新的进入map
                                    // 看是否是公用数据源，不是可直接删除，是还需要看表中是否还存在该记录
                                    _dataSourceMap.remove(key);
                                    // 初始化新的数据源
                                    con = this.getJdbcConnection(type);
                                }
                            }
                        } else
                        {
                            if (null != _ieaiDataSource)
                            {// 证明该数据源修改用户名或者url，
                                String oldKey = (String) _dataSourceFlag.get(Constants.IEAI_IEAI);
                                _ieaiDataSource.close();
                                _ieaiDataSource = null;
                                _dataSourceFlag.remove(Constants.IEAI_IEAI);
                                _dataSourceMap.remove(oldKey);
                            }
                            con = this.getJdbcConnection(type);
                        }
                        flag = true;
                    } catch (Exception e)
                    {
                        flag = false;
                        throw new DBException(e.getMessage());
                    }
                    break;
                case Constants.IEAI_INFOCOLLECTION:
                    try
                    {
                        if (_dataSourceMap.containsKey(key))
                        {
                            if (null != _infoCollectionDataSource)
                            {
                                if (pwd.equals(_infoCollectionDataSource.getPassword()))
                                {
                                    con = _infoCollectionDataSource.getConnection();
                                } else
                                {

                                    _infoCollectionDataSource.close();
                                    _infoCollectionDataSource = null;
                                    // 用户修改密码，删除map中的source，在新初始化一个新的进入map
                                    // 看是否是公用数据源，不是可直接删除，是还需要看表中是否还存在该记录
                                    _dataSourceMap.remove(key);
                                    // 初始化新的数据源
                                    con = this.getJdbcConnection(type);
                                }
                            }
                        } else
                        {
                            if (null != _infoCollectionDataSource)
                            {// 证明该数据源修改用户名或者url，
                                String oldKey = (String) _dataSourceFlag.get(Constants.IEAI_INFOCOLLECTION);
                                _infoCollectionDataSource.close();
                                _infoCollectionDataSource = null;
                                _dataSourceFlag.remove(Constants.IEAI_INFOCOLLECTION);
                                _dataSourceMap.remove(oldKey);
                            }
                            con = this.getJdbcConnection(type);
                        }
                        flag = true;
                    } catch (Exception e)
                    {
                        flag = false;
                        throw new DBException(e.getMessage());
                    }
                    break;
                case Constants.IEAI_SUS:
                    try
                    {
                        if (_dataSourceMap.containsKey(key))
                        {
                            if (null != _autoDataSource)
                            {
                                if (pwd.equals(_autoDataSource.getPassword()))
                                {
                                    con = _autoDataSource.getConnection();
                                } else
                                {

                                    _autoDataSource.close();
                                    _autoDataSource = null;
                                    // 用户修改密码，删除map中的source，在新初始化一个新的进入map
                                    // 看是否是公用数据源，不是可直接删除，是还需要看表中是否还存在该记录
                                    _dataSourceMap.remove(key);
                                    // 初始化新的数据源
                                    con = this.getJdbcConnection(type);
                                }
                            }
                        } else
                        {
                            if (null != _autoDataSource)
                            {// 证明该数据源修改用户名或者url，
                                String oldKey = (String) _dataSourceFlag.get(Constants.IEAI_SUS);
                                _autoDataSource.close();
                                _autoDataSource = null;
                                _dataSourceFlag.remove(Constants.IEAI_SUS);
                                _dataSourceMap.remove(oldKey);
                            }
                            con = this.getJdbcConnection(type);
                        }
                        flag = true;
                    } catch (Exception e)
                    {
                        flag = false;
                        throw new DBException(e.getMessage());
                    }
                    break;
                case Constants.IEAI_EMERGENCY_SWITCH:
                    try
                    {
                        if (_dataSourceMap.containsKey(key))
                        {
                            if (null != _disRecDataSource)
                            {
                                if (pwd.equals(_disRecDataSource.getPassword()))
                                {
                                    con = _disRecDataSource.getConnection();
                                } else
                                {
                                    _disRecDataSource.close();
                                    _disRecDataSource = null;
                                    // 用户修改密码，删除map中的source，在新初始化一个新的进入map
                                    // 看是否是公用数据源，不是可直接删除，是还需要看表中是否还存在该记录
                                    _dataSourceMap.remove(key);
                                    // 初始化新的数据源
                                    con = this.getJdbcConnection(type);
                                }
                            }
                        } else
                        {

                            if (null != _disRecDataSource)
                            {// 证明该数据源修改用户名或者url，
                                String oldKey = (String) _dataSourceFlag.get(Constants.IEAI_EMERGENCY_SWITCH);
                                _disRecDataSource.close();
                                _disRecDataSource = null;
                                _dataSourceFlag.remove(Constants.IEAI_EMERGENCY_SWITCH);
                                _dataSourceMap.remove(oldKey);
                            }
                            con = this.getJdbcConnection(type);
                        }
                        flag = true;
                    } catch (Exception e)
                    {
                        flag = false;
                        throw new DBException(e.getMessage());
                    }
                    break;
                case Constants.IEAI_TIMINGTASK:
                    try
                    {
                        if (_dataSourceMap.containsKey(key))
                        {
                            if (null != _timeTaskDataSource)
                            {
                                if (pwd.equals(_timeTaskDataSource.getPassword()))
                                {
                                    con = _timeTaskDataSource.getConnection();
                                } else
                                {

                                    _timeTaskDataSource.close();
                                    _timeTaskDataSource = null;
                                    // 用户修改密码，删除map中的source，在新初始化一个新的进入map
                                    // 看是否是公用数据源，不是可直接删除，是还需要看表中是否还存在该记录
                                    _dataSourceMap.remove(key);
                                    // 初始化新的数据源
                                    con = this.getJdbcConnection(type);
                                }
                            }
                        } else
                        {
                            if (null != _timeTaskDataSource)
                            {// 证明该数据源修改用户名或者url，
                                String oldKey = (String) _dataSourceFlag.get(Constants.IEAI_TIMINGTASK);
                                _timeTaskDataSource.close();
                                _timeTaskDataSource = null;
                                _dataSourceFlag.remove(Constants.IEAI_TIMINGTASK);
                                _dataSourceMap.remove(oldKey);
                            }
                            con = this.getJdbcConnection(type);

                        }
                        flag = true;
                    } catch (Exception e)
                    {
                        flag = false;
                        throw new DBException(e.getMessage());
                    }
                    break;
                case Constants.IEAI_EMERGENCY_OPER:
                    try
                    {
                        if (_dataSourceMap.containsKey(key))
                        {
                            if (null != _emergencyOperDataSource)
                            {
                                if (pwd.equals(_emergencyOperDataSource.getPassword()))
                                {
                                    con = _emergencyOperDataSource.getConnection();
                                } else
                                {
                                    _emergencyOperDataSource.close();
                                    _emergencyOperDataSource = null;
                                    // 用户修改密码，删除map中的source，在新初始化一个新的进入map
                                    // 看是否是公用数据源，不是可直接删除，是还需要看表中是否还存在该记录
                                    _dataSourceMap.remove(key);
                                    // 初始化新的数据源
                                    con = this.getJdbcConnection(type);
                                }
                            }
                        } else
                        {
                            if (null != _emergencyOperDataSource)
                            {// 证明该数据源修改用户名或者url，
                                String oldKey = (String) _dataSourceFlag.get(Constants.IEAI_EMERGENCY_OPER);
                                _emergencyOperDataSource.close();
                                _emergencyOperDataSource = null;
                                _dataSourceFlag.remove(Constants.IEAI_EMERGENCY_OPER);
                                _dataSourceMap.remove(oldKey);
                            }
                            con = this.getJdbcConnection(type);
                        }
                        flag = true;
                    } catch (Exception e)
                    {
                        flag = false;
                        throw new DBException(e.getMessage());
                    }
                    break;
                case Constants.IEAI_HEALTH_INSPECTION:
                    try
                    {
                        if (_dataSourceMap.containsKey(key))
                        {
                            if (null != _healthInspectionDataSource)
                            {
                                if (pwd.equals(_healthInspectionDataSource.getPassword()))
                                {
                                    con = _healthInspectionDataSource.getConnection();
                                } else
                                {

                                    _healthInspectionDataSource.close();
                                    _healthInspectionDataSource = null;
                                    // 用户修改密码，删除map中的source，在新初始化一个新的进入map
                                    // 看是否是公用数据源，不是可直接删除，是还需要看表中是否还存在该记录
                                    _dataSourceMap.remove(key);
                                    // 初始化新的数据源
                                    con = this.getJdbcConnection(type);
                                }
                            }
                        } else
                        {
                            if (null != _healthInspectionDataSource)
                            {// 证明该数据源修改用户名或者url，
                                String oldKey = (String) _dataSourceFlag.get(Constants.IEAI_HEALTH_INSPECTION);
                                _healthInspectionDataSource.close();
                                _healthInspectionDataSource = null;
                                _dataSourceFlag.remove(Constants.IEAI_HEALTH_INSPECTION);
                                _dataSourceMap.remove(oldKey);
                            }
                            con = this.getJdbcConnection(type);
                        }
                        flag = true;
                    } catch (Exception e)
                    {
                        flag = false;
                        throw new DBException(e.getMessage());
                    }
                    break;
                case Constants.IEAI_DAILY_OPERATIONS:
                    try
                    {
                        if (_dataSourceMap.containsKey(key))
                        {
                            if (null != _dailyOperationsDataSource)
                            {
                                if (pwd.equals(_dailyOperationsDataSource.getPassword()))
                                {
                                    con = _dailyOperationsDataSource.getConnection();
                                } else
                                {

                                    _dailyOperationsDataSource.close();
                                    _dailyOperationsDataSource = null;
                                    // 用户修改密码，删除map中的source，在新初始化一个新的进入map
                                    // 看是否是公用数据源，不是可直接删除，是还需要看表中是否还存在该记录
                                    _dataSourceMap.remove(key);
                                    // 初始化新的数据源
                                    con = this.getJdbcConnection(type);
                                }
                            }
                        } else
                        {
                            if (null != _dailyOperationsDataSource)
                            {// 证明该数据源修改用户名或者url，
                                String oldKey = (String) _dataSourceFlag.get(Constants.IEAI_DAILY_OPERATIONS);
                                _dailyOperationsDataSource.close();
                                _dailyOperationsDataSource = null;
                                _dataSourceFlag.remove(Constants.IEAI_DAILY_OPERATIONS);
                                _dataSourceMap.remove(oldKey);
                            }
                            con = this.getJdbcConnection(type);
                        }
                        flag = true;
                    } catch (Exception e)
                    {
                        flag = false;
                        throw new DBException(e.getMessage());
                    }
                    break;
                case Constants.IEAI_SCRIPT_SERVICE:
                    try
                    {
                        if (_dataSourceMap.containsKey(key))
                        {
                            if (null != _scriptServiceDataSource)
                            {
                                if (pwd.equals(_scriptServiceDataSource.getPassword()))
                                {
                                    con = _scriptServiceDataSource.getConnection();
                                } else
                                {
                                    _scriptServiceDataSource.close();
                                    _scriptServiceDataSource = null;
                                    // 用户修改密码，删除map中的source，在新初始化一个新的进入map
                                    // 看是否是公用数据源，不是可直接删除，是还需要看表中是否还存在该记录
                                    _dataSourceMap.remove(key);
                                    // 初始化新的数据源
                                    con = this.getJdbcConnection(type);
                                }
                            }
                        } else
                        {
                            if (null != _scriptServiceDataSource)
                            {// 证明该数据源修改用户名或者url，
                                String oldKey = (String) _dataSourceFlag.get(Constants.IEAI_SCRIPT_SERVICE);
                                _scriptServiceDataSource.close();
                                _scriptServiceDataSource = null;
                                _dataSourceFlag.remove(Constants.IEAI_SCRIPT_SERVICE);
                                _dataSourceMap.remove(oldKey);
                            }
                            con = this.getJdbcConnection(type);
                        }
                        flag = true;
                    } catch (Exception e)
                    {
                        flag = false;
                        throw new DBException(e.getMessage());
                    }
                    break;
                // 添加应用启停初始化数据源
                case Constants.IEAI_SERVER_START_AND_STOP:
                    try
                    {
                        if (_dataSourceMap.containsKey(key))
                        {
                            if (null != _disServiceDataSource)
                            {
                                if (pwd.equals(_disServiceDataSource.getPassword()))
                                {
                                    con = _disServiceDataSource.getConnection();
                                } else
                                {
                                    _disServiceDataSource.close();
                                    _disServiceDataSource = null;
                                    // 用户修改密码，删除map中的source，在新初始化一个新的进入map
                                    // 看是否是公用数据源，不是可直接删除，是还需要看表中是否还存在该记录
                                    _dataSourceMap.remove(key);
                                    // 初始化新的数据源
                                    con = this.getJdbcConnection(type);
                                }
                            }
                        } else
                        {

                            if (null != _disServiceDataSource)
                            {// 证明该数据源修改用户名或者url，
                                String oldKey = (String) _dataSourceFlag.get(Constants.IEAI_SERVER_START_AND_STOP);
                                _disServiceDataSource.close();
                                _disServiceDataSource = null;
                                _dataSourceFlag.remove(Constants.IEAI_SERVER_START_AND_STOP);
                                _dataSourceMap.remove(oldKey);
                            }
                            con = this.getJdbcConnection(type);
                        }
                        flag = true;
                    } catch (Exception e)
                    {
                        flag = false;
                        throw new DBException(e.getMessage());
                    }
                    break;
                case Constants.IEAI_COMPARE:
                    try
                    {
                        if (_dataSourceMap.containsKey(key))
                        {
                            if (null != _compareServiceDataSource)
                            {
                                if (pwd.equals(_compareServiceDataSource.getPassword()))
                                {
                                    con = _compareServiceDataSource.getConnection();
                                } else
                                {
                                    _compareServiceDataSource.close();
                                    _compareServiceDataSource = null;
                                    // 用户修改密码，删除map中的source，在新初始化一个新的进入map
                                    // 看是否是公用数据源，不是可直接删除，是还需要看表中是否还存在该记录
                                    _dataSourceMap.remove(key);
                                    // 初始化新的数据源
                                    con = this.getJdbcConnection(type);
                                }
                            }
                        } else
                        {

                            if (null != _compareServiceDataSource)
                            {// 证明该数据源修改用户名或者url，
                                String oldKey = (String) _dataSourceFlag.get(Constants.IEAI_COMPARE);
                                _compareServiceDataSource.close();
                                _compareServiceDataSource = null;
                                _dataSourceFlag.remove(Constants.IEAI_COMPARE);
                                _dataSourceMap.remove(oldKey);
                            }
                            con = this.getJdbcConnection(type);
                        }
                        flag = true;
                    } catch (Exception e)
                    {
                        flag = false;
                        throw new DBException(e.getMessage());
                    }
                    break;
                case Constants.IEAI_CMDB:
                    try
                    {
                        if (_dataSourceMap.containsKey(key))
                        {
                            if (null != _cmdbServiceDataSource)
                            {
                                if (pwd.equals(_cmdbServiceDataSource.getPassword()))
                                {
                                    con = _cmdbServiceDataSource.getConnection();
                                } else
                                {
                                    _cmdbServiceDataSource.close();
                                    _cmdbServiceDataSource = null;
                                    // 用户修改密码，删除map中的source，在新初始化一个新的进入map
                                    // 看是否是公用数据源，不是可直接删除，是还需要看表中是否还存在该记录
                                    _dataSourceMap.remove(key);
                                    // 初始化新的数据源
                                    con = this.getJdbcConnection(type);
                                }
                            }
                        } else
                        {

                            if (null != _cmdbServiceDataSource)
                            {// 证明该数据源修改用户名或者url，
                                String oldKey = (String) _dataSourceFlag.get(Constants.IEAI_CMDB);
                                _cmdbServiceDataSource.close();
                                _cmdbServiceDataSource = null;
                                _dataSourceFlag.remove(Constants.IEAI_CMDB);
                                _dataSourceMap.remove(oldKey);
                            }
                            con = this.getJdbcConnection(type);
                        }
                        flag = true;
                    } catch (Exception e)
                    {
                        flag = false;
                        throw new DBException(e.getMessage());
                    }
                    break;
                case Constants.IEAI_PAAS:
                    try
                    {
                        if (_dataSourceMap.containsKey(key))
                        {
                            if (null != _paasDataSource)
                            {
                                if (pwd.equals(_paasDataSource.getPassword()))
                                {
                                    con = _paasDataSource.getConnection();
                                } else
                                {
                                    _paasDataSource.close();
                                    _paasDataSource = null;
                                    // 用户修改密码，删除map中的source，在新初始化一个新的进入map
                                    // 看是否是公用数据源，不是可直接删除，是还需要看表中是否还存在该记录
                                    _dataSourceMap.remove(key);
                                    // 初始化新的数据源
                                    con = this.getJdbcConnection(type);
                                }
                            }
                        } else
                        {

                            if (null != _paasDataSource)
                            {// 证明该数据源修改用户名或者url，
                                String oldKey = (String) _dataSourceFlag.get(Constants.IEAI_PAAS);
                                _paasDataSource.close();
                                _paasDataSource = null;
                                _dataSourceFlag.remove(Constants.IEAI_PAAS);
                                _dataSourceMap.remove(oldKey);
                            }
                            con = this.getJdbcConnection(type);
                        }
                        flag = true;
                    } catch (Exception e)
                    {
                        flag = false;
                        throw new DBException(e.getMessage());
                    }
                    break;
                case Constants.IEAI_ROUTINE_TASKS:
                    try
                    {
                        if (_dataSourceMap.containsKey(key))
                        {
                            if (null != _routineTasksDataSource)
                            {
                                if (pwd.equals(_routineTasksDataSource.getPassword()))
                                {
                                    con = _routineTasksDataSource.getConnection();
                                } else
                                {
                                    _routineTasksDataSource.close();
                                    _routineTasksDataSource = null;
                                    // 用户修改密码，删除map中的source，在新初始化一个新的进入map
                                    // 看是否是公用数据源，不是可直接删除，是还需要看表中是否还存在该记录
                                    _dataSourceMap.remove(key);
                                    // 初始化新的数据源
                                    con = this.getJdbcConnection(type);
                                }
                            }
                        } else
                        {

                            if (null != _routineTasksDataSource)
                            {// 证明该数据源修改用户名或者url，
                                String oldKey = (String) _dataSourceFlag.get(Constants.IEAI_ROUTINE_TASKS);
                                _routineTasksDataSource.close();
                                _routineTasksDataSource = null;
                                _dataSourceFlag.remove(Constants.IEAI_ROUTINE_TASKS);
                                _dataSourceMap.remove(oldKey);
                            }
                            con = this.getJdbcConnection(type);
                        }
                        flag = true;
                    } catch (Exception e)
                    {
                        flag = false;
                        throw new DBException(e.getMessage());
                    }
                    break;

                case Constants.IEAI_APM:
                    try
                    {
                        if (_dataSourceMap.containsKey(key))
                        {
                            if (null != _apmDataSource)
                            {
                                if (pwd.equals(_apmDataSource.getPassword()))
                                {
                                    con = _apmDataSource.getConnection();
                                } else
                                {
                                    _apmDataSource.close();
                                    _apmDataSource = null;
                                    // 用户修改密码，删除map中的source，在新初始化一个新的进入map
                                    // 看是否是公用数据源，不是可直接删除，是还需要看表中是否还存在该记录
                                    _dataSourceMap.remove(key);
                                    // 初始化新的数据源
                                    con = this.getJdbcConnection(type);
                                }
                            }
                        } else
                        {

                            if (null != _apmDataSource)
                            {// 证明该数据源修改用户名或者url，
                                String oldKey = (String) _dataSourceFlag.get(Constants.IEAI_APM);
                                _apmDataSource.close();
                                _apmDataSource = null;
                                _dataSourceFlag.remove(Constants.IEAI_APM);
                                _dataSourceMap.remove(oldKey);
                            }
                            con = this.getJdbcConnection(type);
                        }
                        flag = true;
                    } catch (Exception e)
                    {
                        flag = false;
                        throw new DBException(e.getMessage());
                    }
                    break;
                case Constants.IEAI_INFO_COLLECT:
                    try
                    {
                        if (_dataSourceMap.containsKey(key))
                        {
                            if (null != _icSource)
                            {
                                if (pwd.equals(_icSource.getPassword()))
                                {
                                    con = _icSource.getConnection();
                                } else
                                {
                                    _icSource.close();
                                    _icSource = null;
                                    // 用户修改密码，删除map中的source，在新初始化一个新的进入map
                                    // 看是否是公用数据源，不是可直接删除，是还需要看表中是否还存在该记录
                                    _dataSourceMap.remove(key);
                                    // 初始化新的数据源
                                    con = this.getJdbcConnection(type);
                                }
                            }
                        } else
                        {
                            if (null != _icSource)
                            {// 证明该数据源修改用户名或者url，
                                String oldKey = (String) _dataSourceFlag.get(Constants.IEAI_INFO_COLLECT);
                                _icSource.close();
                                _icSource = null;
                                _dataSourceFlag.remove(Constants.IEAI_INFO_COLLECT);
                                _dataSourceMap.remove(oldKey);
                            }
                            con = this.getJdbcConnection(type);
                        }
                        flag = true;
                    } catch (Exception e)
                    {
                        flag = false;
                        throw new DBException(e.getMessage());
                    }
                    break;

                case Constants.IEAI_TOPO:
                    try
                    {
                        if (_dataSourceMap.containsKey(key))
                        {
                            if (null != _topoServiceDataSource)
                            {
                                if (pwd.equals(_topoServiceDataSource.getPassword()))
                                {
                                    con = _topoServiceDataSource.getConnection();
                                } else
                                {
                                    _topoServiceDataSource.close();
                                    _topoServiceDataSource = null;
                                    // 用户修改密码，删除map中的source，在新初始化一个新的进入map
                                    // 看是否是公用数据源，不是可直接删除，是还需要看表中是否还存在该记录
                                    _dataSourceMap.remove(key);
                                    // 初始化新的数据源
                                    con = this.getJdbcConnection(type);
                                }
                            }
                        } else
                        {

                            if (null != _topoServiceDataSource)
                            {// 证明该数据源修改用户名或者url，
                                String oldKey = (String) _dataSourceFlag.get(Constants.IEAI_TOPO);
                                _topoServiceDataSource.close();
                                _topoServiceDataSource = null;
                                _dataSourceFlag.remove(Constants.IEAI_TOPO);
                                _dataSourceMap.remove(oldKey);
                            }
                            con = this.getJdbcConnection(type);
                        }
                        flag = true;
                    } catch (Exception e)
                    {
                        flag = false;
                        throw new DBException(e.getMessage());
                    }
                    break;

                case Constants.IEAI_DATA_COLLECT:
                    try
                    {
                        if (_dataSourceMap.containsKey(key))
                        {
                            if (null != _dataCollectDataSource)
                            {
                                if (pwd.equals(_dataCollectDataSource.getPassword()))
                                {
                                    con = _dataCollectDataSource.getConnection();
                                } else
                                {
                                    _dataCollectDataSource.close();
                                    _dataCollectDataSource = null;
                                    // 用户修改密码，删除map中的source，在新初始化一个新的进入map
                                    // 看是否是公用数据源，不是可直接删除，是还需要看表中是否还存在该记录
                                    _dataSourceMap.remove(key);
                                    // 初始化新的数据源
                                    con = this.getJdbcConnection(type);
                                }
                            }
                        } else
                        {

                            if (null != _dataCollectDataSource)
                            {// 证明该数据源修改用户名或者url，
                                String oldKey = (String) _dataSourceFlag.get(Constants.IEAI_DATA_COLLECT);
                                _dataCollectDataSource.close();
                                _dataCollectDataSource = null;
                                _dataSourceFlag.remove(Constants.IEAI_DATA_COLLECT);
                                _dataSourceMap.remove(oldKey);
                            }
                            con = this.getJdbcConnection(type);
                        }
                        flag = true;
                    } catch (Exception e)
                    {
                        flag = false;
                        throw new DBException(e.getMessage());
                    }
                    break;
                case Constants.IEAI_AZ:
                    try
                    {
                        if (_dataSourceMap.containsKey(key))
                        {
                            if (null != _azDataSource)
                            {
                                if (pwd.equals(_azDataSource.getPassword()))
                                {
                                    con = _azDataSource.getConnection();
                                } else
                                {
                                    _azDataSource.close();
                                    _azDataSource = null;
                                    // 用户修改密码，删除map中的source，在新初始化一个新的进入map
                                    // 看是否是公用数据源，不是可直接删除，是还需要看表中是否还存在该记录
                                    _dataSourceMap.remove(key);
                                    // 初始化新的数据源
                                    con = this.getJdbcConnection(type);
                                }
                            }
                        } else
                        {

                            if (null != _azDataSource)
                            {// 证明该数据源修改用户名或者url，
                                String oldKey = (String) _dataSourceFlag.get(Constants.IEAI_AZ);
                                _azDataSource.close();
                                _azDataSource = null;
                                _dataSourceFlag.remove(Constants.IEAI_AZ);
                                _dataSourceMap.remove(oldKey);
                            }
                            con = this.getJdbcConnection(type);
                        }
                        flag = true;
                    } catch (Exception e)
                    {
                        flag = false;
                        throw new DBException(e.getMessage());
                    }
                    break;
                case Constants.IEAI_AZ_GROUP:
                    try
                    {
                        if (_dataSourceMap.containsKey(key))
                        {
                            if (null != _dataCollectDataSource)
                            {
                                if (pwd.equals(_dataCollectDataSource.getPassword()))
                                {
                                    con = _dataCollectDataSource.getConnection();
                                } else
                                {
                                    _dataCollectDataSource.close();
                                    _dataCollectDataSource = null;
                                    // 用户修改密码，删除map中的source，在新初始化一个新的进入map
                                    // 看是否是公用数据源，不是可直接删除，是还需要看表中是否还存在该记录
                                    _dataSourceMap.remove(key);
                                    // 初始化新的数据源
                                    con = this.getJdbcConnection(type);
                                }
                            }
                        } else
                        {

                            if (null != _dataCollectDataSource)
                            {// 证明该数据源修改用户名或者url，
                                String oldKey = (String) _dataSourceFlag.get(Constants.IEAI_AZ_GROUP);
                                _dataCollectDataSource.close();
                                _dataCollectDataSource = null;
                                _dataSourceFlag.remove(Constants.IEAI_AZ_GROUP);
                                _dataSourceMap.remove(oldKey);
                            }
                            con = this.getJdbcConnection(type);
                        }
                        flag = true;
                    } catch (Exception e)
                    {
                        flag = false;
                        throw new DBException(e.getMessage());
                    }
                    break;
                case Constants.IEAI_SUS_GROUP:
                    try
                    {
                        if (_dataSourceMap.containsKey(key))
                        {
                            if (null != _dataCollectDataSource)
                            {
                                if (pwd.equals(_dataCollectDataSource.getPassword()))
                                {
                                    con = _dataCollectDataSource.getConnection();
                                } else
                                {
                                    _dataCollectDataSource.close();
                                    _dataCollectDataSource = null;
                                    // 用户修改密码，删除map中的source，在新初始化一个新的进入map
                                    // 看是否是公用数据源，不是可直接删除，是还需要看表中是否还存在该记录
                                    _dataSourceMap.remove(key);
                                    // 初始化新的数据源
                                    con = this.getJdbcConnection(type);
                                }
                            }
                        } else
                        {

                            if (null != _dataCollectDataSource)
                            {// 证明该数据源修改用户名或者url，
                                String oldKey = (String) _dataSourceFlag.get(Constants.IEAI_SUS_GROUP);
                                _dataCollectDataSource.close();
                                _dataCollectDataSource = null;
                                _dataSourceFlag.remove(Constants.IEAI_SUS_GROUP);
                                _dataSourceMap.remove(oldKey);
                            }
                            con = this.getJdbcConnection(type);
                        }
                        flag = true;
                    } catch (Exception e)
                    {
                        flag = false;
                        throw new DBException(e.getMessage());
                    }
                    break;
                case Constants.IEAI_TOOLS:
                    try
                    {
                        if (_dataSourceMap.containsKey(key))
                        {
                            if (null != _toolsDataSource)
                            {
                                if (pwd.equals(_toolsDataSource.getPassword()))
                                {
                                    con = _toolsDataSource.getConnection();
                                } else
                                {
                                    _toolsDataSource.close();
                                    _toolsDataSource = null;
                                    // 用户修改密码，删除map中的source，在新初始化一个新的进入map
                                    // 看是否是公用数据源，不是可直接删除，是还需要看表中是否还存在该记录
                                    _dataSourceMap.remove(key);
                                    // 初始化新的数据源
                                    con = this.getJdbcConnection(type);
                                }
                            }
                        } else
                        {

                            if (null != _toolsDataSource)
                            {// 证明该数据源修改用户名或者url，
                                String oldKey = (String) _dataSourceFlag.get(Constants.IEAI_TOOLS);
                                _toolsDataSource.close();
                                _toolsDataSource = null;
                                _dataSourceFlag.remove(Constants.IEAI_TOOLS);
                                _dataSourceMap.remove(oldKey);
                            }
                            con = this.getJdbcConnection(type);
                        }
                        flag = true;
                    } catch (Exception e)
                    {
                        flag = false;
                        throw new DBException(e.getMessage());
                    }
                    break;
                case Constants.IEAI_SYS_CHANGE:
                    try
                    {
                        if (_dataSourceMap.containsKey(key))
                        {
                            if (null != _sysChangeDataSource)
                            {
                                if (pwd.equals(_sysChangeDataSource.getPassword()))
                                {
                                    con = _sysChangeDataSource.getConnection();
                                } else
                                {
                                    _sysChangeDataSource.close();
                                    _sysChangeDataSource = null;
                                    // 用户修改密码，删除map中的source，在新初始化一个新的进入map
                                    // 看是否是公用数据源，不是可直接删除，是还需要看表中是否还存在该记录
                                    _dataSourceMap.remove(key);
                                    // 初始化新的数据源
                                    con = this.getJdbcConnection(type);
                                }
                            }
                        } else
                        {

                            if (null != _sysChangeDataSource)
                            {// 证明该数据源修改用户名或者url，
                                String oldKey = (String) _dataSourceFlag.get(Constants.IEAI_SYS_CHANGE);
                                _sysChangeDataSource.close();
                                _sysChangeDataSource = null;
                                _dataSourceFlag.remove(Constants.IEAI_SYS_CHANGE);
                                _dataSourceMap.remove(oldKey);
                            }
                            con = this.getJdbcConnection(type);
                        }
                        flag = true;
                    } catch (Exception e)
                    {
                        flag = false;
                        throw new DBException(e.getMessage());
                    }
                    break;
                case Constants.IEAI_EMS:
                    try
                    {
                        if (_dataSourceMap.containsKey(key))
                        {
                            if (null != _emsDataSource)
                            {
                                if (pwd.equals(_emsDataSource.getPassword()))
                                {
                                    con = _emsDataSource.getConnection();
                                } else
                                {
                                    _emsDataSource.close();
                                    _emsDataSource = null;
                                    // 用户修改密码，删除map中的source，在新初始化一个新的进入map
                                    // 看是否是公用数据源，不是可直接删除，是还需要看表中是否还存在该记录
                                    _dataSourceMap.remove(key);
                                    // 初始化新的数据源
                                    con = this.getJdbcConnection(type);
                                }
                            }
                        } else
                        {

                            if (null != _emsDataSource)
                            {// 证明该数据源修改用户名或者url，
                                String oldKey = (String) _dataSourceFlag.get(Constants.IEAI_EMS);
                                _emsDataSource.close();
                                _emsDataSource = null;
                                _dataSourceFlag.remove(Constants.IEAI_EMS);
                                _dataSourceMap.remove(oldKey);
                            }
                            con = this.getJdbcConnection(type);
                        }
                        flag = true;
                    } catch (Exception e)
                    {
                        flag = false;
                        throw new DBException(e.getMessage());
                    }
                    break;
                case Constants.IEAI_BMI:
                    try
                    {
                        if (_dataSourceMap.containsKey(key))
                        {
                            if (null != _bmiDataSource)
                            {
                                if (pwd.equals(_bmiDataSource.getPassword()))
                                {
                                    con = _bmiDataSource.getConnection();
                                } else
                                {
                                    _bmiDataSource.close();
                                    _bmiDataSource = null;
                                    // 用户修改密码，删除map中的source，在新初始化一个新的进入map
                                    // 看是否是公用数据源，不是可直接删除，是还需要看表中是否还存在该记录
                                    _dataSourceMap.remove(key);
                                    // 初始化新的数据源
                                    con = this.getJdbcConnection(type);
                                }
                            }
                        } else
                        {

                            if (null != _bmiDataSource)
                            {// 证明该数据源修改用户名或者url，
                                String oldKey = (String) _dataSourceFlag.get(Constants.IEAI_BMI);
                                _bmiDataSource.close();
                                _bmiDataSource = null;
                                _dataSourceFlag.remove(Constants.IEAI_BMI);
                                _dataSourceMap.remove(oldKey);
                            }
                            con = this.getJdbcConnection(type);
                        }
                        flag = true;
                    } catch (Exception e)
                    {
                        flag = false;
                        throw new DBException(e.getMessage());
                    }
                    break;
                case Constants.IEAI_SCRIPT_DBAAS:
                    try
                    {
                        if (_dataSourceMap.containsKey(key))
                        {
                            if (null != _dbaasDirDataSource)
                            {
                                if (pwd.equals(_dbaasDirDataSource.getPassword()))
                                {
                                    con = _dbaasDirDataSource.getConnection();
                                } else
                                {
                                    _dbaasDirDataSource.close();
                                    _dbaasDirDataSource = null;
                                    // 用户修改密码，删除map中的source，在新初始化一个新的进入map
                                    // 看是否是公用数据源，不是可直接删除，是还需要看表中是否还存在该记录
                                    _dataSourceMap.remove(key);
                                    // 初始化新的数据源
                                    con = this.getJdbcConnection(type);
                                }
                            }
                        } else
                        {

                            if (null != _dbaasDirDataSource)
                            {// 证明该数据源修改用户名或者url，
                                String oldKey = (String) _dataSourceFlag.get(Constants.IEAI_SCRIPT_DBAAS);
                                _dbaasDirDataSource.close();
                                _dbaasDirDataSource = null;
                                _dataSourceFlag.remove(Constants.IEAI_SCRIPT_DBAAS);
                                _dataSourceMap.remove(oldKey);
                            }
                            con = this.getJdbcConnection(type);
                        }
                        flag = true;
                    } catch (Exception e)
                    {
                        flag = false;
                        throw new DBException(e.getMessage());
                    }
                    break;
                case Constants.IEAI_SHUTDOWN_MAINTAIN :
                    try
                    {
                        if (_dataSourceMap.containsKey(key))
                        {
                            if (null != _shutdownMaintainDataSource)
                            {
                                if (pwd.equals(_shutdownMaintainDataSource.getPassword()))
                                {
                                    con = _shutdownMaintainDataSource.getConnection();
                                } else
                                {
                                    _shutdownMaintainDataSource.close();
                                    _shutdownMaintainDataSource = null;
                                    // 用户修改密码，删除map中的source，在新初始化一个新的进入map
                                    // 看是否是公用数据源，不是可直接删除，是还需要看表中是否还存在该记录
                                    _dataSourceMap.remove(key);
                                    _dataSourceFlag.remove(Constants.IEAI_SHUTDOWN_MAINTAIN );
                                    // 初始化新的数据源
                                    con = this.getJdbcConnection(type);
                                }
                            }
                        } else
                        {
                            if (null != _shutdownMaintainDataSource)
                            {// 证明该数据源修改用户名或者url，
                                String oldKey = (String) _dataSourceFlag.get(Constants.IEAI_SHUTDOWN_MAINTAIN );
                                _shutdownMaintainDataSource.close();
                                _shutdownMaintainDataSource = null;
                                _dataSourceFlag.remove(Constants.IEAI_SHUTDOWN_MAINTAIN );
                                _dataSourceMap.remove(oldKey);
                            }
                            con = this.getJdbcConnection(type);
                        }
                        flag = true;
                    } catch (Exception e)
                    {
                        flag = false;
                        throw new DBException(e.getMessage());
                    }
                    break;
                case Constants.IEAI_FSH:
                    try
                    {
                        if (_dataSourceMap.containsKey(key))
                        {
                            if (null != _fshDataSource)
                            {
                                if (pwd.equals(_fshDataSource.getPassword()))
                                {
                                    con = _fshDataSource.getConnection();
                                } else
                                {
                                    _fshDataSource.close();
                                    _fshDataSource = null;
                                    // 用户修改密码，删除map中的source，在新初始化一个新的进入map
                                    // 看是否是公用数据源，不是可直接删除，是还需要看表中是否还存在该记录
                                    _dataSourceMap.remove(key);
                                    // 初始化新的数据源
                                    con = this.getJdbcConnection(type);
                                }
                            }
                        } else
                        {
                            if (null != _fshDataSource)
                            {// 证明该数据源修改用户名或者url，
                                String oldKey = (String) _dataSourceFlag.get(Constants.IEAI_FSH);
                                _fshDataSource.close();
                                _fshDataSource = null;
                                _dataSourceFlag.remove(Constants.IEAI_FSH);
                                _dataSourceMap.remove(oldKey);
                            }
                            con = this.getJdbcConnection(type);
                        }
                        flag = true;
                    } catch (Exception e)
                    {
                        flag = false;
                        throw new DBException(e.getMessage());
                    }
                    break;
            }
            return flag;
        } catch (Exception e)
        {
            try
            {
                if (null != con)
                {
                    con.rollback();
                }
            } catch (SQLException e1)
            {
                e1.printStackTrace();
            }
            throw new DBException(e.getMessage());
        } finally
        {
            try
            {
                if (null != con)
                {
                    con.close();
                }
            } catch (SQLException e)
            {
                e.printStackTrace();
            }
        }
    }

    /**
     * 移除被清理的数据源或者 数据源为空，清理内存中的记录信息<li>Description:</li>
     *
     * <AUTHOR> 2016年5月20日
     * @param type
     * @return
     * @throws DBException return boolean
     */
    public boolean removeDbSource ( int type ) throws DBException
    {
        boolean flag = false;
        try
        {
            String key = "";
            // DBSourceMonitor dsm = getServerDbResource(type, 1);
            // if (null != dsm)
            // {
            // key = dsm.get_url() + "--ieai--" + dsm.get_dbUser();
            // }

            switch (type)
            {
                case Constants.IEAI_OPM:
                    if (null != _ieaiOPM)
                    {
                        key = type + "--" + _ieaiOPM.getUrl() + "--ieai--" + _ieaiOPM.getUsername();
                        _ieaiOPM.close();
                        _ieaiOPM = null;
                    }
                    break;
                case Constants.IEAI_LOOPHOLE_MANAGE :
                    if (null != _ieaiLoopholeManage)
                    {
                        key = type + "--" + _ieaiLoopholeManage.getUrl() + "--ieai--" + _ieaiLoopholeManage.getUsername();
                        _ieaiLoopholeManage.close();
                        _ieaiLoopholeManage = null;
                    }
                    break;
                case Constants.IEAI_IEAI:
                    if (null != _ieaiDataSource)
                    {
                        key = type + "--" + _ieaiDataSource.getUrl() + "--ieai--" + _ieaiDataSource.getUsername();
                        _ieaiDataSource.close();
                        _ieaiDataSource = null;
                    }
                    break;
                case Constants.IEAI_INFOCOLLECTION:
                    if (null != _infoCollectionDataSource)
                    {
                        key = type + "--" + _infoCollectionDataSource.getUrl() + "--ieai--"
                                + _infoCollectionDataSource.getUsername();
                        _infoCollectionDataSource.close();
                        _infoCollectionDataSource = null;
                    }
                    break;
                case Constants.IEAI_SUS:
                    if (null != _autoDataSource)
                    {
                        key = type + "--" + _autoDataSource.getUrl() + "--ieai--" + _autoDataSource.getUsername();
                        _autoDataSource.close();
                        _autoDataSource = null;
                    }
                    break;
                case Constants.IEAI_EMERGENCY_SWITCH:
                    if (null != _disRecDataSource)
                    {
                        key = type + "--" + _disRecDataSource.getUrl() + "--ieai--" + _disRecDataSource.getUsername();
                        _disRecDataSource.close();
                        _disRecDataSource = null;
                    }
                    break;
                case Constants.IEAI_TIMINGTASK:
                    if (null != _timeTaskDataSource)
                    {
                        key = type + "--" + _timeTaskDataSource.getUrl() + "--ieai--"
                                + _timeTaskDataSource.getUsername();
                        _timeTaskDataSource.close();
                        _timeTaskDataSource = null;
                    }
                    break;
                case Constants.IEAI_EMERGENCY_OPER:
                    if (null != _emergencyOperDataSource)
                    {
                        key = type + "--" + _emergencyOperDataSource.getUrl() + "--ieai--"
                                + _emergencyOperDataSource.getUsername();
                        _emergencyOperDataSource.close();
                        _emergencyOperDataSource = null;
                    }
                    break;
                case Constants.IEAI_INFO_COLLECT:
                    if (null != _icSource)
                    {
                        key = type + "--" + _icSource.getUrl() + "--ieai--" + _icSource.getUsername();
                        _icSource.close();
                        _icSource = null;
                    }
                    break;
                case Constants.IEAI_HEALTH_INSPECTION:
                    if (null != _healthInspectionDataSource)
                    {
                        key = type + "--" + _healthInspectionDataSource.getUrl() + "--ieai--"
                                + _healthInspectionDataSource.getUsername();
                        _healthInspectionDataSource.close();
                        _healthInspectionDataSource = null;
                    }
                    break;
                case Constants.IEAI_DAILY_OPERATIONS:
                    if (null != _dailyOperationsDataSource)
                    {
                        key = type + "--" + _dailyOperationsDataSource.getUrl() + "--ieai--"
                                + _dailyOperationsDataSource.getUsername();
                        _dailyOperationsDataSource.close();
                        _dailyOperationsDataSource = null;
                    }
                    break;
                case Constants.IEAI_SCRIPT_SERVICE:
                    if (null != _scriptServiceDataSource)
                    {
                        key = type + "--" + _scriptServiceDataSource.getUrl() + "--ieai--"
                                + _scriptServiceDataSource.getUsername();
                        _scriptServiceDataSource.close();
                        _scriptServiceDataSource = null;
                    }
                    break;
                // 添加应用启停初始化数据源
                case Constants.IEAI_SERVER_START_AND_STOP:
                    if (null != _disServiceDataSource)
                    {
                        key = type + "--" + _disServiceDataSource.getUrl() + "--ieai--"
                                + _disServiceDataSource.getUsername();
                        _disServiceDataSource.close();
                        _disServiceDataSource = null;
                    }
                    break;
                case Constants.IEAI_COMPARE:
                    if (null != _compareServiceDataSource)
                    {
                        key = type + "--" + _compareServiceDataSource.getUrl() + "--ieai--"
                                + _compareServiceDataSource.getUsername();
                        _compareServiceDataSource.close();
                        _compareServiceDataSource = null;
                    }
                    break;
                case Constants.IEAI_CMDB:
                    if (null != _cmdbServiceDataSource)
                    {
                        key = type + "--" + _cmdbServiceDataSource.getUrl() + "--ieai--"
                                + _cmdbServiceDataSource.getUsername();
                        _cmdbServiceDataSource.close();
                        _cmdbServiceDataSource = null;
                    }
                    break;
                case Constants.IEAI_APM:
                    if (null != _apmDataSource)
                    {
                        key = type + "--" + _apmDataSource.getUrl() + "--ieai--" + _apmDataSource.getUsername();
                        _apmDataSource.close();
                        _apmDataSource = null;
                    }
                    break;
                case Constants.IEAI_TOPO:
                    if (null != _topoServiceDataSource)
                    {
                        key = type + "--" + _topoServiceDataSource.getUrl() + "--ieai--"
                                + _topoServiceDataSource.getUsername();
                        _topoServiceDataSource.close();
                        _topoServiceDataSource = null;
                    }
                    break;
                case Constants.IEAI_DATA_COLLECT:
                    if (null != _dataCollectDataSource)
                    {
                        key = type + "--" + _dataCollectDataSource.getUrl() + "--ieai--"
                                + _dataCollectDataSource.getUsername();
                        _dataCollectDataSource.close();
                        _dataCollectDataSource = null;
                    }
                    break;
                case Constants.IEAI_PAAS:
                    if (null != _paasDataSource)
                    {
                        key = type + "--" + _paasDataSource.getUrl() + "--ieai--" + _paasDataSource.getUsername();
                        _paasDataSource.close();
                        _paasDataSource = null;
                    }
                    break;

                case Constants.IEAI_ROUTINE_TASKS:
                    if (null != _routineTasksDataSource)
                    {
                        key = type + "--" + _routineTasksDataSource.getUrl() + "--ieai--"
                                + _routineTasksDataSource.getUsername();
                        _routineTasksDataSource.close();
                        _routineTasksDataSource = null;
                    }
                    break;

                case Constants.IEAI_AZ:
                    if (null != _azDataSource)
                    {
                        key = type + "--" + _azDataSource.getUrl() + "--ieai--" + _azDataSource.getUsername();
                        _azDataSource.close();
                        _azDataSource = null;
                    }
                    break;
                case Constants.IEAI_SUS_GROUP:
                    if (null != _sus_group_DataSource)
                    {
                        key = type + "--" + _azDataSource.getUrl() + "--ieai--" + _azDataSource.getUsername();
                        _sus_group_DataSource.close();
                        _sus_group_DataSource = null;
                    }
                    break;
                case Constants.IEAI_AZ_GROUP:
                    if (null != _sus_group_DataSource)
                    {
                        key = type + "--" + _azDataSource.getUrl() + "--ieai--" + _azDataSource.getUsername();
                        _sus_group_DataSource.close();
                        _sus_group_DataSource = null;
                    }
                    break;
                case Constants.IEAI_TOOLS:
                    if (null != _toolsDataSource)
                    {
                        key = type + "--" + _toolsDataSource.getUrl() + "--ieai--" + _toolsDataSource.getUsername();
                        _toolsDataSource.close();
                        _toolsDataSource = null;
                    }
                    break;
                case Constants.IEAI_SYS_CHANGE:
                    if (null != _sysChangeDataSource)
                    {
                        key = type + "--" + _sysChangeDataSource.getUrl() + "--ieai--" + _sysChangeDataSource.getUsername();
                        _sysChangeDataSource.close();
                        _sysChangeDataSource = null;
                    }
                    break;
                case Constants.IEAI_EMS:
                    if (null != _emsDataSource)
                    {
                        key = type + "--" + _emsDataSource.getUrl() + "--ieai--" + _emsDataSource.getUsername();
                        _emsDataSource.close();
                        _emsDataSource = null;
                    }
                    break;
                case Constants.IEAI_BMI:
                    if (null != _bmiDataSource)
                    {
                        key = type + "--" + _bmiDataSource.getUrl() + "--ieai--" + _bmiDataSource.getUsername();
                        _bmiDataSource.close();
                        _bmiDataSource = null;
                    }
                    break;
                case Constants.IEAI_SCRIPT_DBAAS:
                    if (null != _dbaasDirDataSource)
                    {
                        key = type + "--" + _dbaasDirDataSource.getUrl() + "--ieai--"
                                + _dbaasDirDataSource.getUsername();
                        _dbaasDirDataSource.close();
                        _dbaasDirDataSource = null;
                    }
                    break;
                case Constants.IEAI_SHUTDOWN_MAINTAIN:
                    if (null != _shutdownMaintainDataSource)
                    {
                        key = type + "--" + _shutdownMaintainDataSource.getUrl() + "--ieai--" + _shutdownMaintainDataSource.getUsername();
                        _shutdownMaintainDataSource.close();
                        _shutdownMaintainDataSource = null;
                    }
                    break;
                case Constants.IEAI_FSH:
                    if (null != _fshDataSource)
                    {
                        key = type + "--" + _fshDataSource.getUrl() + "--ieai--" + _fshDataSource.getUsername();
                        _fshDataSource.close();
                        _fshDataSource = null;
                    }
                    break;
            }
            if (_dataSourceMap.containsKey(key))
            {
                _dataSourceMap.remove(key);
                _dataSourceFlag.remove(type);

            }
            flag = true;
        } catch (Exception e)
        {
            flag = false;
            throw new DBException(e.getMessage());
        }
        return flag;
    }

    /**
     * 点击 菜单<li>Description:</li>
     *
     * <AUTHOR> 2016年5月20日
     * @param type
     * @return
     * @throws DBException return Connection
     */
    public void getDbConnection ( int type ) throws DBException
    {
        // 通过连接获取resource表中对应的资源和用户名、密码
        try
        {
            switch (type)
            {
                case Constants.IEAI_IEAI_BASIC:
                    // con = _jdbcDataSource.getConnection();
                    break;
                case Constants.IEAI_OPM:
                    if (null == _ieaiOPM)
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                        dfc = getServerDbResource(type, 1);
                        if (null != dfc)
                        {
                            dbCfg.setDbPassword(dfc.get_dbPassword());
                            dbCfg.setDbUser(dfc.get_dbUser());
                            dbCfg.setUrl(dfc.get_url());
                        } else
                        {
                            _log.info("检测数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                        }
                        key = type + "--" + dbCfg.getUrl() + "--ieai--" + dbCfg.getDbUser();
                        if (_dataSourceMap.containsKey(key))
                        {
                            _ieaiOPM = _dataSourceMap.get(key);
                        } else
                        {
                            _dataSourceMap.remove(key);
                            initDBSource(dbCfg, key, type);
                        }
                    }
                    // con = _ieaiOPM.getConnection();
                    break;
                case Constants.IEAI_LOOPHOLE_MANAGE :
                    if (null == _ieaiLoopholeManage)
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                        dfc = getServerDbResource(type, 1);
                        if (null != dfc)
                        {
                            dbCfg.setDbPassword(dfc.get_dbPassword());
                            dbCfg.setDbUser(dfc.get_dbUser());
                            dbCfg.setUrl(dfc.get_url());
                        } else
                        {
                            _log.info("检测数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                        }
                        key = type + "--" + dbCfg.getUrl() + "--ieai--" + dbCfg.getDbUser();
                        if (_dataSourceMap.containsKey(key))
                        {
                            _ieaiLoopholeManage = _dataSourceMap.get(key);
                        } else
                        {
                            _dataSourceMap.remove(key);
                            initDBSource(dbCfg, key, type);
                        }
                    }
                    // con = _ieaiLoopholeManage.getConnection();
                    break;
                case Constants.IEAI_IEAI:
                    if (null == _ieaiDataSource)
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                        dfc = getServerDbResource(type, 1);
                        if (null != dfc)
                        {
                            dbCfg.setDbPassword(dfc.get_dbPassword());
                            dbCfg.setDbUser(dfc.get_dbUser());
                            dbCfg.setUrl(dfc.get_url());
                        } else
                        {
                            _log.info("检测数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                        }
                        key = type + "--" + dbCfg.getUrl() + "--ieai--" + dbCfg.getDbUser();
                        if (_dataSourceMap.containsKey(key))
                        {
                            _ieaiDataSource = _dataSourceMap.get(key);
                        } else
                        {
                            _dataSourceMap.remove(key);
                            initDBSource(dbCfg, key, type);
                        }
                    }
                    // con = _ieaiDataSource.getConnection();
                    break;
                case Constants.IEAI_INFOCOLLECTION:
                    if (null == _infoCollectionDataSource)
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                        dfc = getServerDbResource(type, 1);
                        if (null != dfc)
                        {
                            dbCfg.setDbPassword(dfc.get_dbPassword());
                            dbCfg.setDbUser(dfc.get_dbUser());
                            dbCfg.setUrl(dfc.get_url());
                        } else
                        {
                            _log.info("检测数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                        }
                        key = type + "--" + dbCfg.getUrl() + "--ieai--" + dbCfg.getDbUser();
                        if (_dataSourceMap.containsKey(key))
                        {
                            _infoCollectionDataSource = _dataSourceMap.get(key);
                        } else
                        {
                            _dataSourceMap.remove(key);
                            initDBSource(dbCfg, key, type);
                        }
                    }
                    // con = _infoCollectionDataSource.getConnection();
                    break;
                case Constants.IEAI_SUS:
                    if (null == _autoDataSource)
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                        dfc = getServerDbResource(type, 1);
                        if (null != dfc)
                        {
                            dbCfg.setDbPassword(dfc.get_dbPassword());
                            dbCfg.setDbUser(dfc.get_dbUser());
                            dbCfg.setUrl(dfc.get_url());
                        } else
                        {
                            _log.info("检测数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                        }
                        key = type + "--" + dbCfg.getUrl() + "--ieai--" + dbCfg.getDbUser();
                        if (_dataSourceMap.containsKey(key))
                        {
                            _autoDataSource = _dataSourceMap.get(key);
                        } else
                        {
                            _dataSourceMap.remove(key);
                            initDBSource(dbCfg, key, type);
                        }
                    }
                    // con = _autoDataSource.getConnection();
                    break;
                case Constants.IEAI_EMERGENCY_SWITCH:
                    if (null == _disRecDataSource)
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                        dfc = getServerDbResource(type, 1);
                        if (null != dfc)
                        {
                            dbCfg.setDbPassword(dfc.get_dbPassword());
                            dbCfg.setDbUser(dfc.get_dbUser());
                            dbCfg.setUrl(dfc.get_url());
                        } else
                        {
                            _log.info("检测数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                        }
                        key = type + "--" + dbCfg.getUrl() + "--ieai--" + dbCfg.getDbUser();
                        if (_dataSourceMap.containsKey(key))
                        {
                            _disRecDataSource = _dataSourceMap.get(key);
                        } else
                        {
                            _dataSourceMap.remove(key);
                            initDBSource(dbCfg, key, type);
                        }
                    }
                    // con = _disRecDataSource.getConnection();
                    break;
                case Constants.IEAI_TIMINGTASK:
                    if (null == _timeTaskDataSource)
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                        dfc = getServerDbResource(type, 1);
                        if (null != dfc)
                        {
                            dbCfg.setDbPassword(dfc.get_dbPassword());
                            dbCfg.setDbUser(dfc.get_dbUser());
                            dbCfg.setUrl(dfc.get_url());
                        } else
                        {
                            _log.info("检测数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                        }
                        key = type + "--" + dbCfg.getUrl() + "--ieai--" + dbCfg.getDbUser();
                        if (_dataSourceMap.containsKey(key))
                        {
                            _timeTaskDataSource = _dataSourceMap.get(key);
                        } else
                        {
                            _dataSourceMap.remove(key);
                            initDBSource(dbCfg, key, type);
                        }
                    }
                    // con = _timeTaskDataSource.getConnection();
                    break;
                case Constants.IEAI_EMERGENCY_OPER:
                    if (null == _emergencyOperDataSource)
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                        dfc = getServerDbResource(type, 1);
                        if (null != dfc)
                        {
                            dbCfg.setDbPassword(dfc.get_dbPassword());
                            dbCfg.setDbUser(dfc.get_dbUser());
                            dbCfg.setUrl(dfc.get_url());
                        } else
                        {
                            _log.info("检测数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                        }
                        key = type + "--" + dbCfg.getUrl() + "--ieai--" + dbCfg.getDbUser();
                        if (_dataSourceMap.containsKey(key))
                        {
                            _emergencyOperDataSource = _dataSourceMap.get(key);
                        } else
                        {
                            _dataSourceMap.remove(key);
                            initDBSource(dbCfg, key, type);
                        }
                    }
                    break;
                case Constants.IEAI_PAAS:
                    if (null == _paasDataSource)
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                        dfc = getServerDbResource(type, 1);
                        if (null != dfc)
                        {
                            dbCfg.setDbPassword(dfc.get_dbPassword());
                            dbCfg.setDbUser(dfc.get_dbUser());
                            dbCfg.setUrl(dfc.get_url());
                        } else
                        {
                            _log.info("检测数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                        }
                        key = type + "--" + dbCfg.getUrl() + "--ieai--" + dbCfg.getDbUser();
                        if (_dataSourceMap.containsKey(key))
                        {
                            _paasDataSource = _dataSourceMap.get(key);
                        } else
                        {
                            _dataSourceMap.remove(key);
                            initDBSource(dbCfg, key, type);
                        }
                    }
                    break;

                case Constants.IEAI_ROUTINE_TASKS:
                    if (null == _routineTasksDataSource)
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                        dfc = getServerDbResource(type, 1);
                        if (null != dfc)
                        {
                            dbCfg.setDbPassword(dfc.get_dbPassword());
                            dbCfg.setDbUser(dfc.get_dbUser());
                            dbCfg.setUrl(dfc.get_url());
                        } else
                        {
                            _log.info("检测数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                        }
                        key = type + "--" + dbCfg.getUrl() + "--ieai--" + dbCfg.getDbUser();
                        if (_dataSourceMap.containsKey(key))
                        {
                            _routineTasksDataSource = _dataSourceMap.get(key);
                        } else
                        {
                            _dataSourceMap.remove(key);
                            initDBSource(dbCfg, key, type);
                        }
                    }
                    break;

                case Constants.IEAI_HEALTH_INSPECTION:
                    if (null == _healthInspectionDataSource)
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                        dfc = getServerDbResource(type, 1);
                        if (null != dfc)
                        {
                            dbCfg.setDbPassword(dfc.get_dbPassword());
                            dbCfg.setDbUser(dfc.get_dbUser());
                            dbCfg.setUrl(dfc.get_url());
                            key = type + "--" + dbCfg.getUrl() + "--ieai--" + dbCfg.getDbUser();
                        } else
                        {
                            dfc = getServerDbResource(Constants.IEAI_INFOCOLLECTION, 1);
                            if (null != dfc)
                            {
                                dbCfg.setDbPassword(dfc.get_dbPassword());
                                dbCfg.setDbUser(dfc.get_dbUser());
                                dbCfg.setUrl(dfc.get_url());
                                key = type + "--" + dfc.get_url() + "--ieai--" + dfc.get_dbUser();
                            } else
                            {
                                _log.info("检测数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                                key = type + "--" + dbCfg.getUrl() + "--ieai--" + dbCfg.getDbUser();
                            }
                        }

                        if (_dataSourceMap.containsKey(key))
                        {
                            _healthInspectionDataSource = _dataSourceMap.get(key);
                        } else
                        {
                            _dataSourceMap.remove(key);
                            initDBSource(dbCfg, key, type);
                        }
                    }
                    break;
                case Constants.IEAI_DAILY_OPERATIONS:
                    if (null == _dailyOperationsDataSource)
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                        dfc = getServerDbResource(type, 1);
                        if (null != dfc)
                        {
                            dbCfg.setDbPassword(dfc.get_dbPassword());
                            dbCfg.setDbUser(dfc.get_dbUser());
                            dbCfg.setUrl(dfc.get_url());
                            key = type + "--" + dbCfg.getUrl() + "--ieai--" + dbCfg.getDbUser();
                        } else
                        {
                            dfc = getServerDbResource(Constants.IEAI_INFOCOLLECTION, 1);
                            if (null != dfc)
                            {
                                dbCfg.setDbPassword(dfc.get_dbPassword());
                                dbCfg.setDbUser(dfc.get_dbUser());
                                dbCfg.setUrl(dfc.get_url());
                                key = type + "--" + dfc.get_url() + "--ieai--" + dfc.get_dbUser();
                            } else
                            {
                                _log.info("检测数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                                key = type + "--" + dbCfg.getUrl() + "--ieai--" + dbCfg.getDbUser();
                            }
                        }

                        if (_dataSourceMap.containsKey(key))
                        {
                            _dailyOperationsDataSource = _dataSourceMap.get(key);
                        } else
                        {
                            _dataSourceMap.remove(key);
                            initDBSource(dbCfg, key, type);
                        }
                    }
                    break;
                case Constants.IEAI_SCRIPT_SERVICE:
                    if (null == _scriptServiceDataSource)
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                        dfc = getServerDbResource(type, 1);
                        if (null != dfc)
                        {
                            dbCfg.setDbPassword(dfc.get_dbPassword());
                            dbCfg.setDbUser(dfc.get_dbUser());
                            dbCfg.setUrl(dfc.get_url());
                        } else
                        {
                            _log.info("检测数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                        }
                        key = type + "--" + dbCfg.getUrl() + "--ieai--" + dbCfg.getDbUser();
                        if (_dataSourceMap.containsKey(key))
                        {
                            _scriptServiceDataSource = _dataSourceMap.get(key);
                        } else
                        {
                            _dataSourceMap.remove(key);
                            initDBSource(dbCfg, key, type);
                        }
                    }
                    break;
                case Constants.IEAI_SERVER_START_AND_STOP:
                    if (null == _disServiceDataSource)
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                        dfc = getServerDbResource(type, 1);
                        if (null != dfc)
                        {
                            dbCfg.setDbPassword(dfc.get_dbPassword());
                            dbCfg.setDbUser(dfc.get_dbUser());
                            dbCfg.setUrl(dfc.get_url());
                        } else
                        {
                            _log.info("检测数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                        }
                        key = type + "--" + dbCfg.getUrl() + "--ieai--" + dbCfg.getDbUser();
                        if (_dataSourceMap.containsKey(key))
                        {
                            _disServiceDataSource = _dataSourceMap.get(key);
                        } else
                        {
                            _dataSourceMap.remove(key);
                            initDBSource(dbCfg, key, type);
                        }
                    }
                    // con = _disRecDataSource.getConnection();
                    break;
                case Constants.IEAI_COMPARE:
                    if (null == _compareServiceDataSource)
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                        dfc = getServerDbResource(type, 1);
                        if (null != dfc)
                        {
                            dbCfg.setDbPassword(dfc.get_dbPassword());
                            dbCfg.setDbUser(dfc.get_dbUser());
                            dbCfg.setUrl(dfc.get_url());
                        } else
                        {
                            _log.info("检测数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                        }
                        key = type + "--" + dbCfg.getUrl() + "--ieai--" + dbCfg.getDbUser();
                        if (_dataSourceMap.containsKey(key))
                        {
                            _compareServiceDataSource = _dataSourceMap.get(key);
                        } else
                        {
                            _dataSourceMap.remove(key);
                            initDBSource(dbCfg, key, type);
                        }
                    }
                    // con = _disRecDataSource.getConnection();
                    break;
                case Constants.IEAI_CMDB:
                    if (null == _cmdbServiceDataSource)
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                        dfc = getServerDbResource(type, 1);
                        if (null != dfc)
                        {
                            dbCfg.setDbPassword(dfc.get_dbPassword());
                            dbCfg.setDbUser(dfc.get_dbUser());
                            dbCfg.setUrl(dfc.get_url());
                        } else
                        {
                            _log.info("检测数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                        }
                        key = type + "--" + dbCfg.getUrl() + "--ieai--" + dbCfg.getDbUser();
                        if (_dataSourceMap.containsKey(key))
                        {
                            _cmdbServiceDataSource = _dataSourceMap.get(key);
                        } else
                        {
                            _dataSourceMap.remove(key);
                            initDBSource(dbCfg, key, type);
                        }
                    }
                    // con = _disRecDataSource.getConnection();
                    break;
                case Constants.IEAI_APM:
                    if (null == _apmDataSource)
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                        dfc = getServerDbResource(type, 1);
                        if (null != dfc)
                        {
                            dbCfg.setDbPassword(dfc.get_dbPassword());
                            dbCfg.setDbUser(dfc.get_dbUser());
                            dbCfg.setUrl(dfc.get_url());
                        } else
                        {
                            _log.info("检测数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                        }
                        key = type + "--" + dbCfg.getUrl() + "--ieai--" + dbCfg.getDbUser();
                        if (_dataSourceMap.containsKey(key))
                        {
                            _apmDataSource = _dataSourceMap.get(key);
                        } else
                        {
                            _dataSourceMap.remove(key);
                            initDBSource(dbCfg, key, type);
                        }
                    }
                    break;

                case Constants.IEAI_INFO_COLLECT:
                    if (null == _icSource)
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                        dfc = getServerDbResource(type, 1);
                        if (null != dfc)
                        {
                            dbCfg.setDbPassword(dfc.get_dbPassword());
                            dbCfg.setDbUser(dfc.get_dbUser());
                            dbCfg.setUrl(dfc.get_url());
                        } else
                        {
                            _log.info("检测数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                        }
                        key = type + "--" + dbCfg.getUrl() + "--ieai--" + dbCfg.getDbUser();
                        if (_dataSourceMap.containsKey(key))
                        {
                            _icSource = _dataSourceMap.get(key);
                        } else
                        {
                            _dataSourceMap.remove(key);
                            initDBSource(dbCfg, key, type);
                        }
                    }
                    // con = _disRecDataSource.getConnection();
                    break;

                case Constants.IEAI_TOPO:
                    if (null == _topoServiceDataSource)
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                        dfc = getServerDbResource(type, 1);
                        if (null != dfc)
                        {
                            dbCfg.setDbPassword(dfc.get_dbPassword());
                            dbCfg.setDbUser(dfc.get_dbUser());
                            dbCfg.setUrl(dfc.get_url());
                        } else
                        {
                            _log.info("检测数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                        }
                        key = type + "--" + dbCfg.getUrl() + "--ieai--" + dbCfg.getDbUser();
                        if (_dataSourceMap.containsKey(key))
                        {
                            _topoServiceDataSource = _dataSourceMap.get(key);
                        } else
                        {
                            _dataSourceMap.remove(key);
                            initDBSource(dbCfg, key, type);
                        }
                    }
                    break;
                case Constants.IEAI_DATA_COLLECT:
                    if (null == _dataCollectDataSource)
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                        dfc = getServerDbResource(type, 1);
                        if (null != dfc)
                        {
                            dbCfg.setDbPassword(dfc.get_dbPassword());
                            dbCfg.setDbUser(dfc.get_dbUser());
                            dbCfg.setUrl(dfc.get_url());
                        } else
                        {
                            _log.info("检测数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                        }
                        key = type + "--" + dbCfg.getUrl() + "--ieai--" + dbCfg.getDbUser();
                        if (_dataSourceMap.containsKey(key))
                        {
                            _dataCollectDataSource = _dataSourceMap.get(key);
                        } else
                        {
                            _dataSourceMap.remove(key);
                            initDBSource(dbCfg, key, type);
                        }
                    }
                    // con = _disRecDataSource.getConnection();
                    break;

                case Constants.IEAI_AZ:
                    if (null == _azDataSource)
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                        dfc = getServerDbResource(type, 1);
                        if (null != dfc)
                        {
                            dbCfg.setDbPassword(dfc.get_dbPassword());
                            dbCfg.setDbUser(dfc.get_dbUser());
                            dbCfg.setUrl(dfc.get_url());
                        } else
                        {
                            _log.info("检测数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                        }
                        key = type + "--" + dbCfg.getUrl() + "--ieai--" + dbCfg.getDbUser();
                        if (_dataSourceMap.containsKey(key))
                        {
                            _azDataSource = _dataSourceMap.get(key);
                        } else
                        {
                            _dataSourceMap.remove(key);
                            initDBSource(dbCfg, key, type);
                        }
                    }
                    break;
                case Constants.IEAI_SUS_GROUP:
                    if (null == _sus_group_DataSource)
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                        dfc = getServerDbResource(type, 1);
                        if (null != dfc)
                        {
                            dbCfg.setDbPassword(dfc.get_dbPassword());
                            dbCfg.setDbUser(dfc.get_dbUser());
                            dbCfg.setUrl(dfc.get_url());
                        } else
                        {
                            _log.info("检测数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                        }
                        key = type + "--" + dbCfg.getUrl() + "--ieai--" + dbCfg.getDbUser();
                        if (_dataSourceMap.containsKey(key))
                        {
                            _dataCollectDataSource = _dataSourceMap.get(key);
                        } else
                        {
                            _dataSourceMap.remove(key);
                            initDBSource(dbCfg, key, type);
                        }
                    }
                    break;
                case Constants.IEAI_AZ_GROUP:
                    if (null == _az_group_DataSource)
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                        dfc = getServerDbResource(type, 1);
                        if (null != dfc)
                        {
                            dbCfg.setDbPassword(dfc.get_dbPassword());
                            dbCfg.setDbUser(dfc.get_dbUser());
                            dbCfg.setUrl(dfc.get_url());
                        } else
                        {
                            _log.info("检测数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                        }
                        key = type + "--" + dbCfg.getUrl() + "--ieai--" + dbCfg.getDbUser();
                        if (_dataSourceMap.containsKey(key))
                        {
                            _dataCollectDataSource = _dataSourceMap.get(key);
                        } else
                        {
                            _dataSourceMap.remove(key);
                            initDBSource(dbCfg, key, type);
                        }
                    }
                    break;
                case Constants.IEAI_TOOLS:
                    if (null == _toolsDataSource)
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                        dfc = getServerDbResource(type, 1);
                        if (null != dfc)
                        {
                            dbCfg.setDbPassword(dfc.get_dbPassword());
                            dbCfg.setDbUser(dfc.get_dbUser());
                            dbCfg.setUrl(dfc.get_url());
                        } else
                        {
                            _log.info("检测数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                        }
                        key = type + "--" + dbCfg.getUrl() + "--ieai--" + dbCfg.getDbUser();
                        if (_dataSourceMap.containsKey(key))
                        {
                            _toolsDataSource = _dataSourceMap.get(key);
                        } else
                        {
                            _dataSourceMap.remove(key);
                            initDBSource(dbCfg, key, type);
                        }
                    }
                    break;
                case Constants.IEAI_SYS_CHANGE:
                    if (null == _sysChangeDataSource)
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                        dfc = getServerDbResource(type, 1);
                        if (null != dfc)
                        {
                            dbCfg.setDbPassword(dfc.get_dbPassword());
                            dbCfg.setDbUser(dfc.get_dbUser());
                            dbCfg.setUrl(dfc.get_url());
                        } else
                        {
                            _log.info("检测数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                        }
                        key = type + "--" + dbCfg.getUrl() + "--ieai--" + dbCfg.getDbUser();
                        if (_dataSourceMap.containsKey(key))
                        {
                            _sysChangeDataSource = _dataSourceMap.get(key);
                        } else
                        {
                            _dataSourceMap.remove(key);
                            initDBSource(dbCfg, key, type);
                        }
                    }
                    break;
                case Constants.IEAI_EMS:
                    if (null == _emsDataSource)
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                        dfc = getServerDbResource(type, 1);
                        if (null != dfc)
                        {
                            dbCfg.setDbPassword(dfc.get_dbPassword());
                            dbCfg.setDbUser(dfc.get_dbUser());
                            dbCfg.setUrl(dfc.get_url());
                        } else
                        {
                            _log.info("检测数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                        }
                        key = type + "--" + dbCfg.getUrl() + "--ieai--" + dbCfg.getDbUser();
                        if (_dataSourceMap.containsKey(key))
                        {
                            _emsDataSource = _dataSourceMap.get(key);
                        } else
                        {
                            _dataSourceMap.remove(key);
                            initDBSource(dbCfg, key, type);
                        }
                    }
                    break;
                case Constants.IEAI_BMI:
                    if (null == _bmiDataSource)
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                        dfc = getServerDbResource(type, 1);
                        if (null != dfc)
                        {
                            dbCfg.setDbPassword(dfc.get_dbPassword());
                            dbCfg.setDbUser(dfc.get_dbUser());
                            dbCfg.setUrl(dfc.get_url());
                        } else
                        {
                            _log.info("检测数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                        }
                        key = type + "--" + dbCfg.getUrl() + "--ieai--" + dbCfg.getDbUser();
                        if (_dataSourceMap.containsKey(key))
                        {
                            _bmiDataSource = _dataSourceMap.get(key);
                        } else
                        {
                            _dataSourceMap.remove(key);
                            initDBSource(dbCfg, key, type);
                        }
                    }
                    break;
                case Constants.IEAI_SCRIPT_DBAAS:
                    if (null == _dbaasDirDataSource)
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                        dfc = getServerDbResource(type, 1);
                        if (null != dfc)
                        {
                            dbCfg.setDbPassword(dfc.get_dbPassword());
                            dbCfg.setDbUser(dfc.get_dbUser());
                            dbCfg.setUrl(dfc.get_url());
                        } else
                        {
                            _log.info("检测数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                        }
                        key = type + "--" + dbCfg.getUrl() + "--ieai--" + dbCfg.getDbUser();
                        if (_dataSourceMap.containsKey(key))
                        {
                            _dbaasDirDataSource = _dataSourceMap.get(key);
                        } else
                        {
                            _dataSourceMap.remove(key);
                            initDBSource(dbCfg, key, type);
                        }
                    }
                    break;
                case Constants.IEAI_SHUTDOWN_MAINTAIN:
                    if (null == _shutdownMaintainDataSource)
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                        dfc = getServerDbResource(type, 1);
                        if (null != dfc)
                        {
                            dbCfg.setDbPassword(dfc.get_dbPassword());
                            dbCfg.setDbUser(dfc.get_dbUser());
                            dbCfg.setUrl(dfc.get_url());
                        } else
                        {
                            _log.info("检测数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                        }
                        key = type + "--" + dbCfg.getUrl() + "--ieai--" + dbCfg.getDbUser();
                        if (_dataSourceMap.containsKey(key))
                        {
                            _shutdownMaintainDataSource = _dataSourceMap.get(key);
                        } else
                        {
                            _dataSourceMap.remove(key);
                            initDBSource(dbCfg, key, type);
                        }
                    }
                    break;
                case Constants.IEAI_FSH:
                    if (null == _fshDataSource)
                    {
                        String key = "";
                        DBSourceMonitor dfc = null;
                        DBConfig dbCfg = null;
                        dbCfg = DBUtil.getDBConfig(ServerEnv.getServerEnv());
                        dfc = getServerDbResource(type, 1);
                        if (null != dfc)
                        {
                            dbCfg.setDbPassword(dfc.get_dbPassword());
                            dbCfg.setDbUser(dfc.get_dbUser());
                            dbCfg.setUrl(dfc.get_url());
                        } else
                        {
                            _log.info("检测数据库资源表中没有获取对应的数据源，无法进行获取连接：" + type);
                        }
                        key = type + "--" + dbCfg.getUrl() + "--ieai--" + dbCfg.getDbUser();
                        if (_dataSourceMap.containsKey(key))
                        {
                            _fshDataSource = _dataSourceMap.get(key);
                        } else
                        {
                            _dataSourceMap.remove(key);
                            initDBSource(dbCfg, key, type);
                        }
                    }
                    break;
                default:
                    _log.info(Thread.currentThread().getStackTrace()[1].getMethodName() + " getdefault dbsource! type:"
                            + type);
                    break;
            }
        } catch (Exception e)
        {
            throw new DBException(e.getMessage());
        }
    }

    private static BasicDataSource _secondDataSource;
    static public String           CLASSNAME   = "oracle.jdbc.driver.OracleDriver";
    static public String           HISUSER     = null;
    static public String           HISPASSWORD = null;
    static public String           HISURL      = null;

    public static void initSecondConnectionPool () throws Exception
    {

        BasicDataSource dataSource = new BasicDataSource();
        DBManager.getInstance().HISUSER = Environment.getInstance().getConfig("db.hisusername");
        DBManager.getInstance().HISPASSWORD = Environment.getInstance().getConfig("db.hispassword");
        DBManager.getInstance().HISURL = Environment.getInstance().getConfig("db.hisurl");
        dataSource.setDriverClassName(CLASSNAME);
        dataSource.setUrl(HISURL);
        dataSource.setUsername(HISUSER);
        // DesUtils des = new DesUtils("ideal");
        // dataSource.setPassword(des.decrypt(PASSWORD));
        dataSource.setPassword(HISPASSWORD);
        dataSource.setInitialSize(50);
        dataSource.setMaxActive(10);
        dataSource.setMaxIdle(3);
        dataSource.setMinIdle(3);
        dataSource.setMaxWait(-1);

        DBPoolConfig dbpool = new DBPoolConfig();
        dbpool.setTestOnBorrow(ServerEnv.getInstance().getSysConfig(ServerEnv.DB_POOL_TESTONBORROW),
                DBPoolConfig.DEFAULT_TEST_ONBORROW);
        dbpool.setValidationQuery(ServerEnv.getInstance().getSysConfig(ServerEnv.DB_POOL_VALIDATIONQUER),
                DBPoolConfig.DEFAULT_VALIDATIONQUERY);
        dataSource.setTestOnBorrow(dbpool.getTestOnBorrow());
        if (dbpool.getTestOnBorrow())
        {
            dataSource.setValidationQuery(dbpool.getValidationQuery());
        }
        _secondDataSource = dataSource;
        _log.info("Second DB Pool Init successfully!");
    }

    public Connection getAvgConnection () throws DBException
    {

        Connection conn = null;
        try
        {
            conn = _secondDataSource.getConnection();
            conn.setAutoCommit(false);
        } catch (SQLException e)
        {
            e.printStackTrace();
        }
        return conn;
    }

    // 數據庫服对应的数据库连接
    private static BasicDataSource _dbaasDataSource;

    /**
     * 启动初始化连接池數據庫服務
     * <li>Description:</li>
     * <AUTHOR>
     * 2019年1月14日
     * @throws Exception
     * return void
     */
    public static void initDbaasConnectionPool () throws Exception
    {
        BasicDataSource dataSource = new BasicDataSource();

        String dbaasClassname = ServerEnv.getInstance().getSysConfig("db.dbaas.driver_class",
                "oracle.jdbc.driver.OracleDriver");
        String dbaasUsr = ServerEnv.getInstance().getSysConfig("db.dbaas.username", "");
        String dbaasPwd = ServerEnv.getInstance().getSysConfig("db.dbaas.password", "");
        String dbaasUrl = ServerEnv.getInstance().getSysConfig("db.dbaas.url", "");
        int initSize = ServerEnv.getInstance().getIntConfig("db.dbaas.initSize", 10);
        int maxActive = ServerEnv.getInstance().getIntConfig("db.dbaas.maxActive", 100);
        int maxIdle = ServerEnv.getInstance().getIntConfig("db.dbaas.maxIdle", 10);
        int minIdle = ServerEnv.getInstance().getIntConfig("db.dbaas.minIdle", 5);
        boolean pwdSecurite = ServerEnv.getInstance().getBooleanConfig("db.dbaas.pwd.securite.switch",
                Environment.FALSE_BOOLEAN);
        dataSource.setDriverClassName(dbaasClassname);
        dataSource.setUrl(dbaasUrl);
        dataSource.setUsername(dbaasUsr);
        if (pwdSecurite)
        {
            dbaasPwd = Jcrypt.decrypt(dbaasPwd);
        }
        dataSource.setPassword(dbaasPwd);
        dataSource.setInitialSize(initSize);
        dataSource.setMaxActive(maxActive);
        dataSource.setMaxIdle(maxIdle);
        dataSource.setMinIdle(minIdle);
        dataSource.setMaxWait(30000);
        DBPoolConfig dbpool = new DBPoolConfig();
        dbpool.setTestOnBorrow(ServerEnv.getInstance().getSysConfig(ServerEnv.DB_POOL_TESTONBORROW),
                DBPoolConfig.DEFAULT_TEST_ONBORROW);
        dbpool.setValidationQuery(ServerEnv.getInstance().getSysConfig(ServerEnv.DB_POOL_VALIDATIONQUER),
                DBPoolConfig.DEFAULT_VALIDATIONQUERY);
        dataSource.setTestOnBorrow(dbpool.getTestOnBorrow());
        if (dbpool.getTestOnBorrow())
        {
            dataSource.setValidationQuery(dbpool.getValidationQuery());
        }
        _dbaasDataSource = dataSource;
        _log.info("dbaasDataSource DB Pool Init successfully!");
    }

    /**
     * 初始化連接池，連接驗證
     * <li>Description:</li>
     * <AUTHOR>
     * 2019年1月14日
     * @throws DBException
     * return void
     */
    public static void initDbaasConnectionPoolCheck () throws DBException
    {
        Connection conn = null;
        try
        {
            if (null == _dbaasDataSource)
            {
                throw new DBException("_dbaasDataSource is null ");
            }
            conn = _dbaasDataSource.getConnection();
            conn.setAutoCommit(false);
        } catch (SQLException e)
        {
            _log.error("initDbaasConnectionPoolCheck is error ", e);
            throw new DBException("initDbaasConnectionPoolCheck is null ", e);
        } finally
        {
            try
            {
                if (null != conn)
                {
                    conn.close();
                }
            } catch (SQLException e)
            {
                _log.error("initDbaasConnectionPoolCheck close is error ", e);
            }
        }
    }

    /**
     * 获取连接
     * <li>Description:</li>
     * <AUTHOR>
     * 2019年1月14日
     * @return
     * @throws DBException
     * return Connection
     */
    public Connection getDbaasJdbcConnection () throws DBException
    {

        Connection conn = null;
        try
        {
            if (null == _dbaasDataSource)
            {
                initDbaasJdbcConnection();
            }
            conn = _dbaasDataSource.getConnection();
            conn.setAutoCommit(false);
        } catch (SQLException e)
        {
            _log.error("getDbaasJdbcConnection is error ", e);
        }
        return conn;
    }

    /**
     * 初始化连接池
     * <li>Description:</li>
     * <AUTHOR>
     * 2019年1月14日
     * @throws DBException
     * return void
     */
    public void initDbaasJdbcConnection () throws DBException
    {
        try
        {
            initDbaasConnectionPool();
        } catch (Exception e)
        {
            _log.error("_dbaasDataSource is null , getDbaasJdbcConnection init is error ", e);
        }
    }

    public static final String IEAI_TEXT = "--ieai--";

    /**
     * dbaas对象中心
     * <li>Description:</li>
     * <AUTHOR>
     * 2020年6月9日
     * @throws Exception
     * return void
     */
    public static void initDbaasObjectConnectionPool () throws Exception
    {
        BasicDataSource dataSource = new BasicDataSource();
        Map<String, String> map = DbaasDbManager.getInstance().getDbServerInfo();
        String dbaasClassname = map.get("DRIVER");
        String dbaasUsr = map.get("DBUSR");
        String dbaasPwd = map.get("DBPAWD");
        String dbaasUrl = map.get("URL");
        int initSize = ServerEnv.getInstance().getIntConfig("db.dbaas.initSize", 10);
        int maxActive = ServerEnv.getInstance().getIntConfig("db.dbaas.maxActive", 100);
        int maxIdle = ServerEnv.getInstance().getIntConfig("db.dbaas.maxIdle", 10);
        int minIdle = ServerEnv.getInstance().getIntConfig("db.dbaas.minIdle", 5);
        dataSource.setDriverClassName(dbaasClassname);
        dataSource.setUrl(dbaasUrl);
        dataSource.setUsername(dbaasUsr);
        dbaasPwd = Jcrypt.decrypt(dbaasPwd);
        dataSource.setPassword(dbaasPwd);
        dataSource.setInitialSize(initSize);
        dataSource.setMaxActive(maxActive);
        dataSource.setMaxIdle(maxIdle);
        dataSource.setMinIdle(minIdle);
        dataSource.setMaxWait(30000);
        DBPoolConfig dbpool = new DBPoolConfig();
        dbpool.setTestOnBorrow(ServerEnv.getInstance().getSysConfig(ServerEnv.DB_POOL_TESTONBORROW),
                DBPoolConfig.DEFAULT_TEST_ONBORROW);
        dbpool.setValidationQuery(ServerEnv.getInstance().getSysConfig(ServerEnv.DB_POOL_VALIDATIONQUER),
                DBPoolConfig.DEFAULT_VALIDATIONQUERY);
        dataSource.setTestOnBorrow(dbpool.getTestOnBorrow());
        if (dbpool.getTestOnBorrow())
        {
            dataSource.setValidationQuery(dbpool.getValidationQuery());
        }
        _dbaasObjDataSource = dataSource;
        _log.info("_dbaasObjDataSource DB Pool Init successfully!");
    }

    private static BasicDataSource _dbaasObjDataSource = null;

    /**
     * dbaas对象中心获取连接
     * <li>Description:</li>
     * <AUTHOR>
     * 2019年1月14日
     * @return
     * @throws DBException
     * return Connection
     */
    public Connection getDbaasObjJdbcConnection () throws DBException
    {

        Connection conn = null;
        try
        {
            if (null == _dbaasObjDataSource)
            {
                initDbaasObjJdbcConnection();
            }
            conn = _dbaasObjDataSource.getConnection();
            conn.setAutoCommit(false);
        } catch (SQLException e)
        {
            if (null != conn)
            {
                try
                {
                    conn.close();
                } catch (SQLException e1)
                {
                    e1.printStackTrace();
                }
            }
            try
            {
                initDbaasObjJdbcConnection();
                conn = _dbaasObjDataSource.getConnection();
            } catch (SQLException e1)
            {
                _log.error("getDbaasObjJdbcConnection is error ", e1);
            }
            _log.error("getDbaasObjJdbcConnection is error ", e);
        }
        return conn;
    }

    /**
     * dbaas对象中心初始化连接池
     * <li>Description:</li>
     * <AUTHOR>
     * 2019年1月14日
     * @throws DBException
     * return void
     */
    public void initDbaasObjJdbcConnection () throws DBException
    {
        try
        {
            initDbaasObjectConnectionPool();
        } catch (Exception e)
        {
            _log.error("_dbaasDataSource is null , initDbaasObjectConnectionPool init is error ", e);
        }
    }

    public static void initDBConfigOrg () throws DBException
    {
        try
        {
            String initSizes = ServerEnv.getInstance().getSysConfig(Environment.DB_CUSTOM_INIT_INITSIZE,  String.valueOf(DB_INIT_SIZE));
            String maxActives = ServerEnv.getInstance().getSysConfig(Environment.DB_CUSTOM_INIT_MAXACTIVER,  String.valueOf(DB_INIT_MAXSIZE));
            String maxIdles = ServerEnv.getInstance().getSysConfig(Environment.DB_CUSTOM_INIT_MAXIDLE,
                    String.valueOf(DB_INIT_MAXIDLE));
            String minIdles = ServerEnv.getInstance().getSysConfig(Environment.DB_CUSTOM_INIT_MINIDLE,
                    String.valueOf(DB_INIT_MINIDLE));
            if (DBManager.requestFrom == 1)
            {
                initSizes = ServerEnv.getInstance().getSysConfig(Environment.DB_CUSTOM_P_INIT_INITSIZE,  String.valueOf(DB_INIT_SIZE));
                maxActives = ServerEnv.getInstance().getSysConfig(Environment.DB_CUSTOM_P_INIT_MAXACTIVER,  String.valueOf(DB_INIT_MAXSIZE));
                maxIdles = ServerEnv.getInstance().getSysConfig(Environment.DB_CUSTOM_P_INIT_MAXIDLE,
                        String.valueOf(DB_INIT_MAXIDLE));
                minIdles = ServerEnv.getInstance().getSysConfig(Environment.DB_CUSTOM_P_INIT_MINIDLE,
                        String.valueOf(DB_INIT_MINIDLE));
            }
            String[] initSize = initSizes.split(",");
            DBConfig dbc = null;
            for (String str : initSize)
            {
                if (null != str && !"".equals(str))
                {
                    String[] data = str.split(":");
                    dbc = new DBConfig();
                    if (data.length == 2)
                    {
                        String key = data[0];
                        String value = data[1];
                        DBPoolConfig dbpf = null;
                        if (dbCfgMap.containsKey(key))
                        {
                            dbc = dbCfgMap.get(key);
                            dbpf = dbc.getPoolConfig();
                            dbpf.setInitSize(Integer.parseInt(value));
                        } else
                        {
                            dbpf = new DBPoolConfig();
                            dbpf.setInitSize(Integer.parseInt(value));
                        }
                        dbc.setPoolConfig(dbpf);
                        dbCfgMap.put(key, dbc);
                    }
                }
            }
            String[] maxActive = maxActives.split(",");
            for (String str : maxActive)
            {
                if (null != str && !"".equals(str))
                {
                    String[] data = str.split(":");
                    dbc = new DBConfig();
                    if (data.length == 2)
                    {
                        String key = data[0];
                        String value = data[1];
                        DBPoolConfig dbpf = null;
                        if (dbCfgMap.containsKey(key))
                        {
                            dbc = dbCfgMap.get(key);
                            dbpf = dbc.getPoolConfig();
                            dbpf.setMaxActive(Integer.parseInt(value));
                        } else
                        {
                            dbpf = new DBPoolConfig();
                            dbpf.setMaxActive(Integer.parseInt(value));
                        }
                        dbc.setPoolConfig(dbpf);
                        dbCfgMap.put(key, dbc);
                    }
                }
            }
            String[] maxIdle = maxIdles.split(",");
            for (String str : maxIdle)
            {
                if (null != str && !"".equals(str))
                {
                    String[] data = str.split(":");
                    dbc = new DBConfig();
                    if (data.length == 2)
                    {
                        String key = data[0];
                        String value = data[1];
                        DBPoolConfig dbpf = null;
                        if (dbCfgMap.containsKey(key))
                        {
                            dbc = dbCfgMap.get(key);
                            dbpf = dbc.getPoolConfig();
                            dbpf.setMaxIdle(Integer.parseInt(value));
                        } else
                        {
                            dbpf = new DBPoolConfig();
                            dbpf.setMaxIdle(Integer.parseInt(value));
                        }
                        dbc.setPoolConfig(dbpf);
                        dbCfgMap.put(key, dbc);
                    }
                }
            }
            String[] minIdle = minIdles.split(",");
            for (String str : minIdle)
            {
                if (null != str && !"".equals(str))
                {
                    String[] data = str.split(":");
                    dbc = new DBConfig();
                    if (data.length == 2)
                    {
                        String key = data[0];
                        String value = data[1];
                        DBPoolConfig dbpf = null;
                        if (dbCfgMap.containsKey(key))
                        {
                            dbc = dbCfgMap.get(key);
                            dbpf = dbc.getPoolConfig();
                            dbpf.setMinIdle(Integer.parseInt(value));
                        } else
                        {
                            dbpf = new DBPoolConfig();
                            dbpf.setMinIdle(Integer.parseInt(value));
                        }
                        dbc.setPoolConfig(dbpf);
                        dbCfgMap.put(key, dbc);
                    }
                }
            }
        } catch (Exception e)
        {
            _log.error("_dbaasDataSource is null , initDBConfigOrg init is error ", e);
        }
    }

    /**
     * @Description: 判断是oracle 或 kingbase 数据库
     * @return: boolean
     * @Author: zuochao_wang
     * @Date: 2023/2/10 8:44
     */
    public static boolean Orcl_Faimily(){
        return  (JudgeDB.IEAI_DB_TYPE == 1 ||JudgeDB.IEAI_DB_TYPE == 5 || JudgeDB.IEAI_DB_TYPE == 4);
    }
    public static boolean DB2_Faimily(){
        return  (JudgeDB.IEAI_DB_TYPE == 2);
    }
    /**
     * @Description: 判断是Mysql 数据库
     * @return: boolean
     * @Author: zuochao_wang
     * @Date: 2023/2/10 8:44
     */
    public static boolean Mysql_Faimily(){
        return  (JudgeDB.IEAI_DB_TYPE == 3);
    }

    private static Map<String, DBConfig> dbCfgMap        = new ConcurrentHashMap<String, DBConfig>();

    private static int                   DB_INIT_SIZE    = 50;
    private static int                   DB_INIT_MAXSIZE = 300;
    private static int                   DB_INIT_MAXIDLE = 10;
    private static int                   DB_INIT_MINIDLE = 8;
    private static int                   requestFrom     = 0;
}
