package com.ideal.ieai.server.queue.election;


import com.ideal.ieai.server.queue.dao.LeaderElectionDAO;
import com.ideal.ieai.server.queue.worker.QueuedWorkflowProcessor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.net.UnknownHostException;
import java.sql.SQLException;
import java.util.Enumeration;
import java.util.UUID;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 表示一个 Leader/Follower 模式中的节点。
 * 节点通过与数据库交互来选举领导者并维持状态。
 * 注意：不再包含 initializeDatabase 方法调用。
 */
public class LeaderFollowerNode {
    private static final Logger logger = LoggerFactory.getLogger(LeaderFollowerNode.class);

    // --- 选举和心跳配置 ---
    private static final long HEARTBEAT_INTERVAL_MS = 3000; // Leader 心跳间隔 (毫秒)
    private static final long CHECK_INTERVAL_MS = 1000;     // Follower 检查间隔 (毫秒)
    private static final long LEADER_TIMEOUT_MS = 30000;     // 领导者超时时间 (毫秒)

    private final String nodeId;
    private final String serverIp;
    private final AtomicBoolean isLeader = new AtomicBoolean(false); // 使用 AtomicBoolean 保证线程安全
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);
    private final LeaderElectionDAO dao;

    public LeaderFollowerNode() throws UnknownHostException {
        this.nodeId = UUID.randomUUID().toString();
        this.serverIp = getMyIpAddress();
        this.dao = new LeaderElectionDAO();
        logger.info("Node created with ID: {} and IP: {}", this.nodeId, this.serverIp);
        this.start();
    }

    /**
     * 获取本机 IP 地址。
     * 注意：在复杂的网络环境中，可能需要更精确的逻辑来获取正确的 IP。
     *
     * @return 本机 IP 地址字符串。
     * @throws UnknownHostException 如果无法确定 IP 地址。
     */
    private String getMyIpAddress() throws UnknownHostException {
        try {
            Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
            while (networkInterfaces.hasMoreElements()) {
                NetworkInterface networkInterface = networkInterfaces.nextElement();
                Enumeration<InetAddress> addresses = networkInterface.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    InetAddress address = addresses.nextElement();
                    // 检查是否为 IPv4 地址且不是回环地址
                    if (!address.isLoopbackAddress() && address.getHostAddress().indexOf(':') == -1) {
                        return address.getHostAddress();
                    }
                }
            }
        } catch (SocketException e) {
            logger.warn("获取网络接口时发生异常，回退到 InetAddress.getLocalHost()", e);
        }
        // 如果没有找到合适的地址，回退到原来的实现
        return InetAddress.getLocalHost().getHostAddress();
    }

    /**
     * 尝试进行领导者选举。
     */
    private void attemptToBecomeLeader() {
        try {
            if (dao.tryBecomeLeader(nodeId, serverIp, LEADER_TIMEOUT_MS)) {
                if (isLeader.compareAndSet(false, true)) {
                    logger.info("+++ Node {} ({}) has become the LEADER +++", nodeId, serverIp);
                }
            } else {
                isLeader.set(false);
                logger.info("--- Node {} is now a FOLLOWER (Lost leadership or failed to acquire) ---", nodeId);
            }
        } catch (SQLException e) {
            logger.error("Database error during leader election attempt for node " + nodeId + ": " + e.getMessage(), e);
            isLeader.set(false); // 遇到错误，安全地回退到 Follower 状态
        }
    }


    /**
     * 作为领导者执行的任务。
     */
    private void performLeaderTask() {
        if (isLeader.get()) {
            QueuedWorkflowProcessor.process();
        }
    }

    /**
     * 启动节点，开始选举和心跳/检查循环。
     * 注意：不再调用 initializeDatabase。
     */
    public void start() {
        logger.info("Node {} is starting...", nodeId);

//        // 定期尝试成为领导者,如果已经为leader，更新心跳
//        scheduler.scheduleAtFixedRate(this::attemptToBecomeLeader, 0, CHECK_INTERVAL_MS, TimeUnit.MILLISECONDS);
//
//        // 定期执行领导者任务（如果成为领导者）
//        scheduler.scheduleWithFixedDelay(this::performLeaderTask, HEARTBEAT_INTERVAL_MS + 500, HEARTBEAT_INTERVAL_MS, TimeUnit.MILLISECONDS);
    }

    /**
     * 停止节点，关闭调度器。
     */
    public void stop() {
        logger.info("Node {} is stopping...", nodeId);
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
        logger.info("Node {} has stopped.", nodeId);
    }

    public boolean isLeader() {
        return isLeader.get();
    }

    public String getNodeId() {
        return nodeId;
    }

    public String getServerIp() {
        return serverIp;
    }
}