package com.ideal.ieai.server.util;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.server.dm.bean.TaskHistoryBean;
import com.ideal.ieai.server.dm.bean.TaskHistoryQueryBean;
import com.ideal.ieai.server.dm.repository.standardinterface.StandardOprationInterfaceServiceImpl;
import com.ideal.ieai.server.dm.repository.task.IcTaskManager;
import com.ideal.ieai.server.ieaikernel.PersonalityEnv;
import com.ideal.ieai.server.ieaikernel.ServerEnv;
import com.ideal.ieai.server.jobscheduling.repository.activity.ActivityReDoManager;
import com.ideal.ieai.server.jobscheduling.thread.DmWarnThread;
import com.ideal.ieai.server.jobscheduling.thread.JobSchedulingWarnThread;
import com.ideal.ieai.server.jobscheduling.util.DateUtil;
import com.ideal.ieai.server.platform.warnmanage.IeaiWarnModel;
import com.ideal.ieai.server.platform.warnmanage.UserModel;
import com.ideal.ieai.server.platform.warnmanage.WarningManage;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.db.DBRetryUtil;
import com.ideal.util.StringUtil;
import org.apache.log4j.Logger;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

/**
 * @ClassName: WarningInterfaceUtilsIEAI
 * @Description:作业调度告警信息工具类
 * @author: hongji_shang
 * @date: 2020年3月10日 下午2:58:18
 * @Copyright: 2020-2027 www.idealinfo.com Inc. All rights reserved.
 */
public class WarningInterfaceUtilsIEAI {
    private static Logger log = Logger.getLogger(WarningInterfaceUtilsIEAI.class);
    private static boolean ieaiArrangeFlowswitch = false;

    static {
        try {
            // 获取作业调度存告警信息开关
            ieaiArrangeFlowswitch = ServerEnv.getInstance().getBooleanConfigNew2(Environment.IEAI_SAVEWARNING_SWITCH,
                    false);
            log.info("ieai.savewarning.switch=" + ieaiArrangeFlowswitch);
        } catch (Exception e) {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        }
    }

    private WarningInterfaceUtilsIEAI() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * @param moduleCode
     * @param typeCode
     * @param levelCode
     * @param ip
     * @param message
     * @param happenTime
     * @return
     * @Title: callWarning
     * @Description: 记录告警信息
     * @author: hongji_shang
     * @date: 2020年3月10日 下午2:58:53
     */
    public static Long callWarning(String moduleCode, String typeCode, String levelCode, String ip, String message,
                                   Date happenTime, String projectName, String flowName, String actName, Long flowId, Long nodeWarnId, String agentIp) {

        Long warnId = 0L;
        if (ieaiArrangeFlowswitch) {
            try {
                log.info("ieai.callWarning is start");
                if(message.contains("业务异常")){
                Boolean redoEstimate=true;
                WarningManage warningInterface = new WarningManage();
                redoEstimate=   warningInterface.excRedoEstimate(flowId,actName,"发送告警");//判断是否重试告警
                if(!redoEstimate){
                    return warnId;
                }
                }
                //WarningInterface warningInterface = new WarningInterface();
//                warnId = warningInterface.warning(moduleCode, typeCode, levelCode, ip, message, happenTime,projectName);
                IeaiWarnModel wm = new IeaiWarnModel();
                wm.setImodulecode(moduleCode);
                wm.setItypecode(typeCode);
                wm.setIlevelcode(levelCode);
                wm.setIip(ip);
                wm.setIwarnmsg(message);
                wm.setIhappentime(happenTime);
                wm.setIhappentimeStr(DateUtil.formatDateTime(happenTime));
                wm.setIprojectName(projectName);
                wm.setIbusinessName(projectName);
                wm.setIsystemname(projectName);
                wm.setIworkFlowName(flowName);
                wm.setiErrorStepName(actName);
                wm.setIflowid(flowId);
                wm.setImodulename("作业调度");
                /** add by yuxh 20191227 中银富登银行报警发平台 */
                boolean zyfdWarnSwitch = PersonalityEnv.isZyfdWarnSwitchValue();
                if (zyfdWarnSwitch) {
                    long endTimeAction = System.currentTimeMillis();
                    if (nodeWarnId == null) {
                        wm.setAlertId("all-A_Effective_" + endTimeAction);
                    } else {
                        wm.setAlertId("all-A_Effective_" + String.valueOf(nodeWarnId));
                    }
                } else {
                    if (nodeWarnId == null) {
                        wm.setAlertId("");
                    } else {
                        wm.setAlertId(String.valueOf(nodeWarnId));
                    }
                }
                //判断是否将agentip存到iip中
                boolean transportSwitch = ServerEnv.getInstance().getBooleanConfigNew2(Environment.IEAI_WARNING_TRANSPORT_AGENTIP_SWITCH, false);
                if (null != agentIp && !"".equals(agentIp)) {
                    wm.setIagentip(agentIp);
                    if (transportSwitch) {
                        wm.setIip(agentIp);
                    }
                }

                /* 上海shellcmd接口异常自动调用Server服务器上的指定脚本并传参开始 */
                // 作业调度上海银行告警信息开关
                Boolean warningSHKG = ServerEnv.getInstance().getBooleanConfig(ServerEnv.IEAI_WARNING_MONITOR_SHANGHAI_SWITCH, false);
                if (warningSHKG) { // 组织上海银行告警信息
                    wm.setItypecode("IEAI_BUSINESSEXCEPTION");
                    wm.setIwarnmsg("作业调度业务异常");
                    wm.setEventName(typeCode);
                    wm.setEventLevel(levelCode);
                    wm.setEventInfo(message);
                    wm.setObjectName(projectName);
                    wm.setIpAddress(agentIp);
                    wm.setEventGroup(moduleCode); // 打开开关传告警分组
                    wm.setImodulecode("IEAI");
                }
                /* 上海shellcmd接口异常自动调用Server服务器上的指定脚本并传参结束 */

                /** add by xinglili 20220910 西安-调度平台与行内报警平台对接 */
                Boolean warningXiAn = ServerEnv.getInstance().getBooleanConfig(ServerEnv.IEAI_WARNING_MONITOR_XIAN_SWITCH, false);
                if (warningXiAn) { // 西安-组织银行告警信息
                    wm.setEventInfo(moduleCode);// 最后一行返回值
                    wm.setImodulecode("IEAI"); //模块名称
                    wm.setIbusinessName("统一调度管理平台"); // 平台名称
                    wm.setEventGroup(levelCode); //告警触发/告警恢复 1:告警触发 0:告警恢复
                    wm.setIlevelcode("three"); // 默认三级
                }
                boolean isBhSwitch = Environment.getInstance().isBhAlarmSendWarnSwitch();

                if(isBhSwitch&&message.contains("业务异常")){
                    String method = Thread.currentThread().getStackTrace()[1].getMethodName();

                    ActivityReDoManager actRedoManger = new ActivityReDoManager();
                    boolean isNotRedo = true; // 该活动默认不需要重试
                    // gg重试次数查询预处理
                    Map<String, String> reDoMap = new HashMap<String, String>();
                    reDoMap.put("reDoNum", "0");

                    isNotRedo = actRedoManger.isNotRedoActor(reDoMap, actName, flowId, isNotRedo);

                    if(isNotRedo){
                        JobSchedulingWarnThread wt = new JobSchedulingWarnThread(wm);
                        wt.start();
                        log.info(method+":"+flowId+"非重试工作流！");
                    }else
                    {

                        Connection conn = null;
                        try
                        {
                            WarningManage warningInterface = new WarningManage();

                            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);
                            //查询实际重试次数
                            int numRedoAct = warningInterface.getExcRedoAct(flowId, actName, conn);
                            //查询工程中作业重试次数
                            int numExcelModel = warningInterface.getExcelModelNum(flowId, actName, conn);
                            if (numExcelModel == 0 || numRedoAct == numExcelModel)
                            {
                                JobSchedulingWarnThread wt = new JobSchedulingWarnThread(wm);
                                wt.start();
                                log.info(flowId + "当前重试次数已达上限！发送告警！");
                            } else
                            {

                                log.info("当前重试告警拦截次数：" + numRedoAct);

                            }
                        } catch (Exception e)
                        {

                            log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);

                        } finally
                        {
                            DBResource.closeConnection(conn, method, log);
                        }
                    }
                }else {
                    JobSchedulingWarnThread wt = new JobSchedulingWarnThread(wm);
                    wt.start();
                }
                //warnId = warningInterface.warning(new IeaiWarnModel());
            } catch (Exception e) {
                log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        } else {
            log.info("ieai.savewarning.switch=false,Don't save Warning");
        }
        return warnId;

    }

    /***
     * 标准运维调用报警平台接口 ymx
     * @param moduleCode
     * @param typeCode
     * @param levelCode
     * @param ip
     * @param message
     * @param happenTime
     * @param projectName
     * @param flowName
     * @param actName
     * @param flowId
     * @param nodeWarnId
     * @param agentIp
     * @return
     */
    public static Long callWarningForDM(String moduleCode, String typeCode, String levelCode, String ip, String message,
                                        Date happenTime, String projectName, String flowName, String actName, Long flowId, Long nodeWarnId, String agentIp) {

        Long warnId = 0L;

        try {
            log.info("dm.callWarning is start");
            Map map = new HashMap();
            TaskHistoryQueryBean bean = new TaskHistoryQueryBean();
            bean.setTaskId(String.valueOf(flowId));
            bean.setLimit(30);
            bean.setStart(0);
            String userName = getUserName(String.valueOf(flowId));
            long sa = StandardOprationInterfaceServiceImpl.getInstance().getUserid(userName, Constants.IEAI_DAILY_OPERATIONS);
            bean.setUserId(sa);
            map = IcTaskManager.getInstance().getDmRunning(bean, Constants.IEAI_DAILY_OPERATIONS);
            IeaiWarnModel wm = new IeaiWarnModel();
            wm.setUserid(String.valueOf(sa));
            wm.setImodulecode(moduleCode);
            wm.setItypecode(typeCode);
            wm.setIlevelcode(levelCode);
            wm.setIip(ip);
            wm.setIwarnmsg(message);
            wm.setIhappentime(happenTime);
            wm.setIhappentimeStr(DateUtil.formatDateTime(happenTime));
            wm.setIprojectName(projectName);
            wm.setIbusinessName(projectName);
            wm.setIsystemname(projectName);
            wm.setIworkFlowName(flowName);
            wm.setiErrorStepName(actName);
            wm.setIflowid(flowId);
            wm.setImodulename("标准运维");
            if (nodeWarnId == null) {
                wm.setAlertId("");
            } else {
                wm.setAlertId(String.valueOf(nodeWarnId));
            }
            if(!StringUtil.isEmptyStr(agentIp)){
                wm.setIip(agentIp);
            }


            /**组织标准运维需要参数*/
            List<TaskHistoryBean> resultList = new ArrayList<TaskHistoryBean>();
            resultList = (List<TaskHistoryBean>) map.put("dataList", resultList);
            for(TaskHistoryBean model : resultList)
            {
                wm.setItaskName(model.getTaskName());
                wm.setStatus(model.getStatus());
                wm.setInitUser(model.getStartUser());
                wm.setExecUser(model.getPerformUser());
                if("1".equals(String.valueOf(model.getExecStrategy())))
                {
                    wm.setExecStrategy("触发执行");
                    wm.setExpression("");
                }else if("2".equals(String.valueOf(model.getExecStrategy())))
                {
                    wm.setExecStrategy("定时执行");
                    wm.setExpression(getCronTime(String.valueOf(flowId)));
                }else if("3".equals(String.valueOf(model.getExecStrategy())))
                {
                    wm.setExecStrategy("周期执行");
                    wm.setExpression(getCronTime(String.valueOf(flowId)));
                }else if("4".equals(String.valueOf(model.getExecStrategy())))
                {
                    wm.setExecStrategy("选择执行");
                }
                log.info("model.getExecStrategy"+String.valueOf(model.getExecStrategy()));
                log.info("model.getAuditPassTime() :"+model.getAuditPassTime());
                wm.setAuditPassTime(model.getAuditPassTime());
                wm.setStartTime(model.getStartTime());
                if("DM_END_NOTICE".equals(typeCode) || "DM_EXCEPTION_NOTICE".equals(typeCode))
                {
                    wm.setEndTime(model.getEndTime());
                    wm.setStatus(model.getStatus());
                    wm.setElapsedTime(model.getRunTime());
                    if("DM_EXCEPTION_NOTICE".equals(typeCode)){
                        initExceptionIp(wm,flowId);
                    }
                }
            }
            DmWarnThread wt = new DmWarnThread(wm);
            wt.start();
        } catch (Exception e) {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        }
        return warnId;
    }

    private static void initExceptionIp(IeaiWarnModel wm,Long taskId) {
        Connection conn=null;
        PreparedStatement ps =null;
        ResultSet rs = null;
        try
        {
            conn=DBManager.getInstance().getJdbcConnection(Constants.IEAI_DAILY_OPERATIONS);
            String sql="select a.iip,c.istate from ieai_standard_task_relation a,IEAI_ACTRUNTIME c  where a.itaskid=? and  a.iflowid=c.iflowid and c.istate in ('Fail','Fail:Business','Timeout')";
            ps=conn.prepareStatement(sql);
            ps.setLong(1,taskId);
            rs=ps.executeQuery();
            while (rs.next()) {
                String iip=rs.getString("iip");
                if(!StringUtil.isEmptyTrimmedStr(iip)){
                    wm.setIip(iip);
                    break;
                }
            }
        }catch (Exception e){
            log.error("获取异常信息ip时候发生异常",e);
        }finally{
            DBResource.closeConn(conn,rs,ps,"initExceptionIp",log);
        }

    }

    public static Long callWarningForStartEndFlow(String moduleCode, String typeCode, String levelCode, String ip, String message,
                                   Date happenTime, String projectName, String flowName, String actName, Long flowId, Long nodeWarnId, String agentIp, List<UserModel> userList) {

        Long warnId = 0L;
        if (ieaiArrangeFlowswitch) {
            try {
                log.info("ieai.callWarningStartEndFlow is start");

                IeaiWarnModel wm = new IeaiWarnModel();
                wm.setImodulecode(moduleCode);
                wm.setItypecode(typeCode);
                wm.setIlevelcode(levelCode);
                wm.setIip(ip);
                wm.setIwarnmsg(message);
                wm.setIhappentime(happenTime);
                wm.setIhappentimeStr(DateUtil.formatDateTime(happenTime));
                wm.setIprojectName(projectName);
                wm.setIbusinessName(projectName);
                wm.setIsystemname(projectName);
                wm.setIworkFlowName(flowName);
                wm.setiErrorStepName(actName);
                wm.setIflowid(flowId);
                wm.setImodulename("作业调度");
                if (nodeWarnId == null) {
                    wm.setAlertId("");
                } else {
                    wm.setAlertId(String.valueOf(nodeWarnId));
                }
                wm.setUserList(userList);
                //判断是否将agentip存到iip中
                boolean transportSwitch = ServerEnv.getInstance().getBooleanConfigNew2(Environment.IEAI_WARNING_TRANSPORT_AGENTIP_SWITCH, false);
                if (null != agentIp && !"".equals(agentIp)) {
                    wm.setIagentip(agentIp);
                    if (transportSwitch) {
                        wm.setIip(agentIp);
                    }
                }
                JobSchedulingWarnThread wt = new JobSchedulingWarnThread(wm);
                wt.start();

            } catch (Exception e) {
                log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            }
        } else {
            log.info("ieai.savewarning.switch=false,Don't save Warning");
        }
        return warnId;

    }



    public static String getCronTime(String taskid) throws RepositoryException {
        String cron = "";
        String sql = "select cron from IEAI_EXECUTION_STRATEGY where STANDARDIID = ?";

        for (int i = 0; i < 10; i++)
        {
            try
            {
                Connection conn = null;
                PreparedStatement ps = null;
                ResultSet rs = null;
                try
                {
                    conn = DBResource.getConnection("getCronTime", log, Constants.IEAI_DAILY_OPERATIONS);
                    ps = conn.prepareStatement(sql);
                    ps.setString(1, taskid);
                    rs = ps.executeQuery();
                    while (rs.next())
                    {
                        cron = rs.getString("cron");
                    }
                } catch (SQLException e)
                {
                    log.error("getCronTime is error ! " ,e);
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, rs, ps, "getCronTime", log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
        return cron;
    }

    public static String getUserName(String taskid) throws RepositoryException {
        String userName = "";
        String sql = "select istartuser from IEAI_DOUBLECHECK_WORKITEM_HIS where iflowid = ?";
        try {
            Connection conn = null;
            PreparedStatement ps = null;
            ResultSet rs = null;
            try {
                conn = DBResource.getConnection("getUserName", log, Constants.IEAI_DAILY_OPERATIONS);
                ps = conn.prepareStatement(sql);
                ps.setString(1, taskid);
                rs = ps.executeQuery();
                while (rs.next()) {
                    userName = rs.getString("istartuser");
                }
            } catch (SQLException e) {
                log.error("getUserName is error ! ", e);
                throw new RepositoryException(ServerError.ERR_DB_QUERY);
            } finally {
                DBResource.closeConn(conn, rs, ps, "getUserName", log);
            }

        } catch (RepositoryException ex) {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), ex);
        }
        return userName;
    }

}
