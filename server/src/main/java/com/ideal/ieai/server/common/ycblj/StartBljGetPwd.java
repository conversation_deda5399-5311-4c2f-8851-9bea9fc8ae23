package com.ideal.ieai.server.common.ycblj;

import com.ideal.ieai.core.Environment;
import com.ideal.ieai.server.ieaikernel.ServerEnv;
import com.simp.toolkit.security.Des;
import org.apache.log4j.Logger;
import org.codehaus.xfire.XFireFactory;
import org.codehaus.xfire.client.Client;
import org.codehaus.xfire.client.XFireProxy;
import org.codehaus.xfire.client.XFireProxyFactory;
import org.codehaus.xfire.service.Service;
import org.codehaus.xfire.service.binding.ObjectServiceFactory;
import org.codehaus.xfire.transport.http.CommonsHttpMessageSender;

import java.io.*;
import java.lang.reflect.Proxy;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

public class StartBljGetPwd
{
    private static Logger         log      = Logger.getLogger(StartBljGetPwd.class);
    private static StartBljGetPwd iintance = new StartBljGetPwd();

    public static StartBljGetPwd getInstance ()
    {
        if (iintance == null)
        {
            iintance = new StartBljGetPwd();
        }
        return iintance;
    }

    public String getPwds ( String appname, String sftpIp, String sftpAcc, String resType, String resIp,
            String filePath, int type, String osUser, String trans )
    {
        String address = ServerEnv.getInstance().getSysConfig(Environment.YC_BLJ_HTTP_INFO,
            "http://************:40000/");
        // 这里是创建一个service，需要传入一个接口类，因为我们后面必须调用相应的接口方法
        Service srcModel = new ObjectServiceFactory().create(IWebService.class);
        // 代理工厂，这里是为了后面创建相应的接口类
        XFireProxyFactory factory = new XFireProxyFactory(XFireFactory.newInstance().getXFire());
        // webservice地址，不需要加wsdl
        String readerServiceUrl = address;

        // 认证信息
        Map<String, String> httpHeaders = new HashMap<String, String>();
        String user = ServerEnv.getInstance().getSysConfig(Environment.YC_BLJ_HTTP_USR, "itsmapp");
        String pwd = ServerEnv.getInstance().getSysConfig(Environment.YC_BLJ_HTTP_PWD,
            "aa8dedf1e599d3d578c3b008fe03facf5b513767f8fff239823159d8212565b92");
        String time = Des.enc_time(String.valueOf(System.currentTimeMillis()));
        httpHeaders.put("auth.usr", user);
        httpHeaders.put("auth.pwd", pwd);
        httpHeaders.put("random", time);
        String ppd = "";
        try
        {
            // 利用工厂返回相应的接口类
            IWebService readerService = (IWebService) factory.create(srcModel, readerServiceUrl);
            XFireProxy proxy = (XFireProxy) Proxy.getInvocationHandler(readerService);
            Client client = proxy.getClient();

            // 安全性认证部分待升级后使用
            client.setTimeout(3 * 1000);
            client.setProperty(CommonsHttpMessageSender.HTTP_TIMEOUT, String.valueOf(10 * 1000));// 设置发送的超时限制,单位是毫秒
            client.setProperty(CommonsHttpMessageSender.DISABLE_KEEP_ALIVE, "true");
            client.setProperty(CommonsHttpMessageSender.DISABLE_EXPECT_CONTINUE, "true");
            client.setProperty(CommonsHttpMessageSender.HTTP_HEADERS, httpHeaders);

            String[] rv = readerService.get_ress(appname, sftpIp, sftpAcc, resType, resIp, filePath);

            if (rv != null && rv.length > 0 && rv[3] != null)
            {
                String path = String.valueOf(rv[2]);
                File filea = new File(path);
                if (!filea.exists())
                {
                    log.info("未接收到文件");
                }
                // 调用第一个接口获取文件
                String[] resList = getFile(resIp, path, osUser);
                String[] getPwdList = readerService.get_pwd_lists(appname, type, null, null, resList);
                // 返回结果： rv[0] (1 为成功， 其他为错误) rv[1] - rv [n] 数据列表 (返回结果在原来数据行添加 放回结果（1为成功）， 下一个数据为密文口令)
                if (getPwdList != null && getPwdList.length > 0)
                {
                    String[] array = getPwdList[1].split(",");// 正确用法
                    if ("_tr".equals(trans))
                    {
                        // 明文密码
                        try
                        {
                            ppd = Des.dec(array[8]);
//                            ppd = SM.dec(array[8]);
                        } catch (Exception e)
                        {
                            log.error(" ppd dec error " + array[8], e);
                        }
                    } else
                    {
                        // 密文密码
                        log.info(array[8]);
                    }
                } else
                {
                    log.info("get_pwd_lists未拿到对应数据");
                }

                File file = new File(path);

                if (file.exists())
                {
                    file.delete();
                }
            } else if (rv == null)
            {
                log.info("get_ress未获取到数据");
            } else if (rv[3] == null)
            {
                log.info("get_ress[3]未获取到数据");
            } else
            {
                log.info("get_ress异常");
            }
        } catch (Exception e)
        {
            log.error("getPwds error ", e);
        }
        return "'"+ppd+"'";
    }

    public static String[] getFile ( String res_ip, String filePath, String osUser ) throws IOException
    {

        String[] res_list = new String[1];

        BufferedReader br = new BufferedReader(new InputStreamReader(new FileInputStream(filePath), "UTF-8"));
        // 构造一个BufferedReader类来读取文件
        String s = null;
        while ((s = br.readLine()) != null)
        {// 使用readLine方法，一次读一行
            String[] array = s.split("\\|");// 正确用法

            res_list[0] = array[0] + "," + res_ip + "," + osUser + "," + array[1] + ", , ,";
        }

        br.close();

        return res_list;

    }

    public String setStartPwd ( String cmd )
    {
        try
        {
            String str = cmd;
            String repStr = "";
            String password = "";
            for (String param : str.split(" "))
            {
                if (param.contains("PASS:"))
                {
                    repStr = param;
                    param = param.substring(param.indexOf("PASS:") + 5, param.length());
                    String[] passParam = param.split("\\|\\|");
                    log.info("setStartPwd:" + Arrays.toString(passParam));
                    String pwd = this.getPwds(passParam[0], Environment.getInstance().getServerIP(), passParam[1],
                        passParam[2], passParam[3], passParam[4], Integer.valueOf(passParam[5]), passParam[6], "_tr");
                    cmd = cmd.replace(repStr, pwd);
                    log.info("setStartPwd:pwd:" + pwd);
                    log.info("setStartPwd：script:" + cmd);
                }
            }
        } catch (Exception e)
        {
            log.error("setStartPwd error:" + e + " cmd:" + cmd);
        }
        return cmd;
    }

    public static void main ( String[] args )
    {
        String appname = "";
        String sftp_ip = "";
        String sftp_acc = "";
        String res_type = "";
        String res_ip = "";
        String file_path = "";
        int type = -1;
        String osUser = "";
        String trans = "";

        appname = "ITSM";
        sftp_ip = "***********";
        sftp_acc = "ideal";
        res_type = "*_db";
        res_ip = "************";
        file_path = "/app/ideal/";
        type = 2;
        osUser = "ideal";
        trans = "_tr";
        StartBljGetPwd a = new StartBljGetPwd();
        a.getPwds(appname, sftp_ip, sftp_acc, res_type, res_ip, file_path, type, osUser, trans);
    }

}
