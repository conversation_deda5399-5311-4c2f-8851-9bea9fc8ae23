package com.ideal.ieai.server;

import com.ideal.ieai.adaptors.commadaptors.dbconnection.ConnectionPoolManage;
import com.ideal.ieai.cbb.strategy.manager.StrategyBean;
import com.ideal.ieai.cbb.strategy.service.ExecutionStrategyHandler;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.MonitorLogConfig;
import com.ideal.ieai.commons.ServerLogConfig;
import com.ideal.ieai.core.EntegorInfo;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.core.JudgApp;
import com.ideal.ieai.core.JudgeDB;
import com.ideal.ieai.core.activity.monitor.SyslogDefs;
import com.ideal.ieai.server.alert.AlertWatcher;
import com.ideal.ieai.server.config.consume.ConsumeTimeConfig;
import com.ideal.ieai.server.engine.Engine;
import com.ideal.ieai.server.engine.core.PerformanceWatching_Server;
import com.ideal.ieai.server.engine.util.CircleAvgTimer;
import com.ideal.ieai.server.ieaikernel.IEAIServer;
import com.ideal.ieai.server.ieaikernel.ServerEnv;
import com.ideal.ieai.server.jobscheduling.report.thread.AvgTimeThreadForJobscheduling;
import com.ideal.ieai.server.jobscheduling.report.thread.ClearDataThreadForJobscheduling;
import com.ideal.ieai.server.jobscheduling.report.thread.MonthReportThread;
import com.ideal.ieai.server.jobscheduling.report.thread.TimeAreaActCountThread;
import com.ideal.ieai.server.quartz.DbaasQuartZManageExecThread;
import com.ideal.ieai.server.quartz.RegClealUpAgentDateTime;
import com.ideal.ieai.server.repository.db.DBRetryUtil;
import com.ideal.ieai.server.repository.monitor.MonitorSyslog;
import com.ideal.ieai.server.webservice.proxy.ProxyOpImpl;
import com.ideal.util.StringUtil;
import org.apache.log4j.Logger;

import javax.xml.ws.Endpoint;
import java.io.File;
import java.io.IOException;
import java.net.MalformedURLException;
import java.util.Timer;
public class Bootstrap
{
    /**
     * Logger for this class
     */
    private static final Logger _log = Logger.getLogger(Bootstrap.class);

    private Bootstrap()
    {
    }

    // update by yan_wang 光大版本耗时
    private static Timer alertTimer = new Timer();

    public static void main ( String[] args )
    {
        // to setup handler when user forbid stop ieai server
        BootstrapStart.ISBOOTSTRAPSTART = true;
        // 初始化连接池管理对象
        ConnectionPoolManage.getConnectionPoolManage();
        try
        {
            // init server config
            ServerEnv.init();
            JudgeDB._init();
            JudgApp._init();

            // 根据配置文件决定是否打开异常开关
            MonitorSyslog monitorSyslog = new MonitorSyslog();
            SyslogDefs syslogDefs = monitorSyslog.getSyslogInfo(ServerEnv.getServerEnv());
            SystemConfig.setSystemexp(syslogDefs.isSyslogswitch());
            SystemConfig.setShellexp(syslogDefs.isShellexp());
            SystemConfig.setJavafunexp(syslogDefs.isJavafunexp());
            SystemConfig.setUsertaskexp(syslogDefs.isUsertaskexp());

            SystemConfig.setSarningPlayRingSwitch(syslogDefs.isWarningPlayRingSwitch());
            // add by yuxh 20180427 福建报警开关参数初始
            SystemConfig.setMonitorSwitchFJ(syslogDefs.isMonitorSwitchFJ());
            SystemConfig.setMonitorIpFJ(syslogDefs.getMonitorIpFJ());
            SystemConfig.setMonitorPortFJ(syslogDefs.getMonitorPortFJ());
            // add by yuxh 20180427 报警insert node_warning
            SystemConfig.setAddWarningSwitch(syslogDefs.isAddWarningSwitch());
            // add by yuxh 20180427 报警开关
            SystemConfig.setAlarmLevel(syslogDefs.getAlarmLevel());
            // add by yuxh 20180509 基础数据收集工作流名称标识
            SystemConfig.setTasknamePrefix(syslogDefs.getTasknamePrefix());

            /**add by yuxh 20180816 顺德开关*/
            SystemConfig.setWebservicewarnSDIp(syslogDefs.getWebservicewarnSDIp());
            SystemConfig.setWebservicewarnSDPort(syslogDefs.getWebservicewarnSDPort());

            SystemConfig.setMethodRunTimeLog(ServerEnv.getServerEnv().getMethodRunTimeLog());


            initSystemLogger();
            initMonitorLogger();
            initDbsourceRollbackExpLogger();
            initWarningLogger();
            // add gang_guo by topo start
            initTopoLogger();
            initDbRetrySet();

            EntegorInfo info = new EntegorInfo();
            info.info();

            _log.info("Entegor logger ");
            _log.info("initialization succeeded!");

            // first start iEAI Server
            if (!startIEAIServer())
            {
                _log.info("Initializing build-in Server Manager ...");
                startTomcat();
                startWebServices();
                String serverType = Environment.getInstance().getServerType();

                // 改造作业调度模块判断 by xinglin_tian 20200714
                for (String subType : serverType.split(","))
                {
                    if (ServerEnv.getServerEnv().hasServerType(Constants.IEAI_IEAI, serverType)||ServerEnv.getServerEnv().hasServerType(Constants.IEAI_DATA_COLLECT, serverType))
                    {
                        Engine.getInstance().startSendRemoteActThread();
                        // add start by wangnan for 启动报表相关计算线程
                        startReportThread();
                        // add end by wangnan for 启动报表相关计算线程
                        break;
                    }
                }
                boolean falg = Environment.getInstance().getStandardCardingDatasSwitch();
                if(falg) {
                    long workItemid = 0;
                    long standardiid = 0;
                    String cron = Environment.getInstance().getStandardCardingDatas();
                    int icron=10;
                    String jobClass = "com.ideal.ieai.server.dm.repository.task.thread.ICTaskHistoryJob";
                    icron = Integer.valueOf(cron);
                    if(icron>59) {
                        icron=10;
                    }
                    else if(icron<10) 
                    {
                        icron=10;
                    }
                    ExecutionStrategyHandler.getInstance().stopExecutionStrategyTaskJobClass(Constants.IEAI_DAILY_OPERATIONS, jobClass);
                    StrategyBean strategyBean = new StrategyBean();
                    strategyBean.setWorkItemid(workItemid);
                    strategyBean.setStandardiid(standardiid);
                    strategyBean.setCron("0/"+icron+" * * * * ? *");
                    strategyBean.setJobClass("com.ideal.ieai.server.dm.repository.task.thread.ICTaskHistoryJob");
                    ExecutionStrategyHandler.getInstance().addExecutionStrategyTask(Constants.IEAI_DAILY_OPERATIONS,strategyBean);
                }
                _log.info("Entegor Server has been started successfully!");
            } else
            {
                Object lock = new Object();
                synchronized (lock)
                {
                    lock.wait();
                }
            }
            PerformanceWatching_Server.initMonitor();

            String historySwitch = ServerEnv.getServerEnv().getSysConfig("HISTORY_SWITCH",
                String.valueOf(ServerEnv.HISTORY_SWITCH_DEFAULT));
            if ("true".equals(historySwitch))
            {
                alertTimer.scheduleAtFixedRate(new AlertWatcher(), 60000, Long.parseLong(ServerEnv.getInstance()
                        .getSysConfig(ServerEnv.ROUND_CYCLE, String.valueOf(ServerEnv.ROUND_CYCLE_DEFAULT))) * 60000);
            }
            String project = Environment.getInstance().getSysConfig(Environment.SS_SERVICESTART_ADDJOBPROJECT,
                "providers");
            if ("server".equals(project))
            {
                DbaasQuartZManageExecThread dbaasQuartZManageExecThread = new DbaasQuartZManageExecThread();
                dbaasQuartZManageExecThread.start();
            }
             //创建定时任务，定时清理异常的容器agent数据，逻辑与一键下线保持一致 jiaMing 2022-5-20 start
            String reV =  ServerEnv.getServerEnv().getSysConfig("reg.clUp.agentData.time", "flase");
            if(!"flase".equals(reV)){
                RegClealUpAgentDateTime regClealUpAgentDateTime = new RegClealUpAgentDateTime(Integer.valueOf(reV));
                regClealUpAgentDateTime.start();
            }
            //创建定时任务，定时清理异常的容器agent数据，逻辑与一键下线保持一致 jiaMing 2022-5-20 end
            //agent状态上送 线程 agent agent 生产数据发主题 monitor-topic  proxy 消费主题monitor-topic 后 ，生产数据主题rdp提供主题，server 消费nrdp提供的主题 start
            //Status submission
//            boolean  statusB = ServerEnv.getServerEnv().getBooleanConfig("agent.timed.task.switch",false);
//            if(statusB){
//                Thread kafkaproxy = new Thread(new KafkaServerStatusSumissThread(),"serverStatusSumiss");
//                kafkaproxy.start(); // 启动生产者消费者
//            }
            //agent状态上送 线程 agent agent 生产数据发主题 monitor-topic  proxy 消费主题monitor-topic 后 ，生产数据主题rdp提供主题，server 消费nrdp提供的主题 end

            //分配置任务批量操作记录信息 jiaMing 2023-5-22 start
//            boolean  assignB = ServerEnv.getServerEnv().getBooleanConfig("assignServerTaskSwitch",false);
//            if(assignB){
//                Thread assignThread = new Thread(new AssignTaskDetailsThread(),"assignThread");
//                assignThread.start(); //分配置任务批量操作记录信息
//            }
            //分配置任务批量操作记录信息 jiaMing 2023-5-22 end

            //批量执行操作任务 jiaMing 2023-5-23 start
//            boolean  terStartCollTask = ServerEnv.getServerEnv().getBooleanConfig("batchTerStartCollTaskSwitch",false);
//            if(terStartCollTask){
//                Thread batchTaskThread = new Thread(new BatchTerStartCollTaskThread(),"batchTaskThread");
//                batchTaskThread.start();
//            }
            //批量执行操作任务 jiaMing 2023-5-23 end

            //消费者线程监控获取消费者状态信息 jiaMing 2023-7-6 start
//            boolean  consumerThreadIsRun = ServerEnv.getServerEnv().getBooleanConfig("proxyTopicThreadIsRun",false);
//            if(consumerThreadIsRun){
//                Thread consumerThread = new Thread(new ProxyTopicStateThread(),"proxyTopicStateThread");
//                consumerThread.start();
//            }
            //消费者线程监控获取消费者状态信息 jiaMing 2023-7-6 end

        } catch (Exception e)
        {
            // server启动异常监控 add by tao_ding 2010-7-21
            if (SystemConfig.isSystemexp())
            {
                MonitorSyslog monitor = new MonitorSyslog();
                monitor.getSysStartExc(5, System.currentTimeMillis(), e.getMessage());
            }

            _log.error("Server start fail: " + e,e);
        }
    }

    private static void initWarnSysLogger () throws IOException
    {
        WarnLog.getInstance().initWarnSysLogger();
    }

    /**
     * @throws IOException
     */
    private static void initSystemLogger () throws IOException
    {

        ServerLogConfig config = ServerEnv.getServerEnv().getServerLogConfig();
        config.init();

    }

    private static void initMonitorLogger () throws IOException
    {

        if (ServerEnv.getServerEnv().isMonitorLogger())
        {
            MonitorLogConfig config = ServerEnv.getServerEnv().getMonitroLogConfig();
            config.init();
        }

    }

    /**
     * Start iEAI Server
     * 
     * @return true if server already started or server is started as idle. false if server is
     *         started as not idle
     * @throws Exception If any error occured when starting server
     */
    public static boolean startIEAIServer ()
    {
        boolean isIdle = false;
        if (iEAIServerStarted)
        {
            return true;
        }
        _log.info("Starting Entegor Server...");
        _log.info("Using Entegor_HOME at:" + System.getProperty("IEAI_HOME", "NOT DEFINED!!!"));
        // init log and repository when start iEAIServer
        isIdle = IEAIServer.start();
        if (!isIdle)
        {
            repositoryInited = true;
            iEAIServerStarted = true;
            _log.info("Entegor engine has been started sucessfully!");
        }
        return isIdle;
    }

    public static void startTomcat () throws Exception
    {
        startTomcatServer();
        if (1 == JudgApp.START_APP_TYPE)
        {
            startHD();
            startWeb("MX_HOME", "editor", "editor");
            
            //浦发合肥开发web服务注册
            startWeb("API_HOME","aomsapi","api");
        } else if (2 == JudgApp.START_APP_TYPE)
        {
            startIeaiCm();
        }
    }

    public static synchronized void startWeb ( String startHome, String configHome, String webRegName ) throws Exception
    {
        String ieaiHome = ServerEnv.getServerEnv().getIEAIHome();
        EmbeddedTomcat tomcat = EmbeddedTomcat.getInstance();
        String home = System.getProperty(startHome);
        if (null != home)
        {
            home = StringUtil.strReplace(home, IEAIHOME, ieaiHome);
        } else
        {
            home = ieaiHome + "/tomcat/webapps/" + configHome;
        }
        File file = new File(home);
        if (!file.exists())
        {
            return;
        }
        tomcat.registerWAR("/" + webRegName, home);
        _log.info("Registering " + webRegName + " succeeded!");
    }

    /**
     * 健康检查工程加载
     * 
     * @throws Exception
     * @throws MalformedURLException
     */
    public static synchronized void startHD () throws Exception
    {
        checkHCNecessary();
        if (isHDStarted())
        {
            return;
        }
        _log.info("Registering aoms...");
        String ieaiHome = ServerEnv.getServerEnv().getIEAIHome();
        EmbeddedTomcat tomcat = EmbeddedTomcat.getInstance();
        String servername = ServerEnv.getServerEnv().getSysConfig(Environment.IEAI_SERVER_HOME, "/aoms");
        // now get path to server manager
        // first check if SM_HOME is specified in startup prameter
        String hdHome = System.getProperty("HD_HOME");
        if (null != hdHome)
        {
            // if set, replace {IEAI_HOME} with
            // ServerEnv.getServerEnv().getIEAIHome
            hdHome = StringUtil.strReplace(hdHome, IEAIHOME, ieaiHome);
        } else
        {
            // if not set, use IEAI_HOME/tomcat/webapps/sm as default
            hdHome = ieaiHome + "/tomcat/webapps/hd";
        }
        tomcat.registerWAR(servername, hdHome);
        hdStarted = true;
        _log.info("Registering aoms succeeded!");
    }

    /**
     * ieaicm工程加载
     * 
     * @throws Exception
     * @throws MalformedURLException
     */
    public static synchronized void startIeaiCm () throws Exception
    {
        _log.info("Registering CM...");
        String ieaiHome = ServerEnv.getServerEnv().getIEAIHome();
        EmbeddedTomcat tomcat = EmbeddedTomcat.getInstance();
        // now get path to server manager
        // first check if SM_HOME is specified in startup prameter
        String cmHome = System.getProperty("CM_HOME");
        if (null != cmHome)
        {
            // if set, replace {IEAI_HOME} with
            // ServerEnv.getServerEnv().getIEAIHome
            cmHome = StringUtil.strReplace(cmHome, IEAIHOME, ieaiHome);
        } else
        {
            // if not set, use IEAI_HOME/tomcat/webapps/sm as default
            cmHome = ieaiHome + "/tomcat/webapps/cm";
        }
        tomcat.registerWAR("/CM", cmHome);
        ieaisusStarted = true;
        _log.info("Registering cm succeeded!");
    }

    /**
     * 统一登录工程加载
     * 
     * @throws Exception
     * @throws MalformedURLException
     */
    public static synchronized void startIeaiLogin () throws Exception
    {
        checkHCNecessary();
        if (isSelStarted())
        {
            return;
        }
        _log.info("Registering Login...");
        String ieaiHome = ServerEnv.getServerEnv().getIEAIHome();
        EmbeddedTomcat tomcat = EmbeddedTomcat.getInstance();
        // now get path to server manager
        // first check if SM_HOME is specified in startup prameter
        String hcHome = System.getProperty("LOG_HOME");
        if (null != hcHome)
        {
            // if set, replace {IEAI_HOME} with
            // ServerEnv.getServerEnv().getIEAIHome
            hcHome = StringUtil.strReplace(hcHome, IEAIHOME, ieaiHome);
        } else
        {
            // if not set, use IEAI_HOME/tomcat/webapps/sm as default
            hcHome = ieaiHome + "/tomcat/webapps/sel";
        }
        tomcat.registerWAR("/LOG", hcHome);
        ieaiSelStarted = true;
        _log.info("Registering LOG succeeded!");
    }

    // update by yan_wang 增加健康检查工程
    public static boolean issuscheckStarted ()
    {
        return ieaisusStarted;
    }

    public static boolean isSelStarted ()
    {
        return ieaiSelStarted;
    }

    public static boolean isHDStarted ()
    {
        return hdStarted;
    }

    public static boolean isPMStarted ()
    {
        return pMStarted;
    }

    public static boolean isMessageBrokerStarted ()
    {
        return messageBrokerStarted;
    }

    public static boolean isServermanagerStarted ()
    {
        return servermanagerStarted;
    }

    public static boolean isTomcatServerStarted ()
    {
        return tomcatServerStarted;
    }

    public static boolean isTomcatCMStarted ()
    {
        return tomcatCmStarted;
    }

    public static boolean isUsertaskStarted ()
    {
        return usertaskStarted;
    }

    public static boolean isWebserviceStarted ()
    {
        return webserviceStarted;
    }

    public static boolean isIEAIServerStarted ()
    {
        return iEAIServerStarted;
    }

    public static boolean isRepositoryInited ()
    {
        return repositoryInited;
    }

    public static boolean isServerEnvInited ()
    {
        return serverEnvInited;
    }

    /**
     * @throws Exception
     */
    private static synchronized void startTomcatServer () throws Exception
    {
        if (1 == JudgApp.START_APP_TYPE)
        {
            checkTomcatNecessary();
            if (isTomcatServerStarted())
            {
                return;
            }
            _log.info("Starting up embedded web server...");
            String ieaiHome = ServerEnv.getServerEnv().getIEAIHome();
            EmbeddedTomcat tomcat = EmbeddedTomcat.getInstance();
            tomcat.setPath(ieaiHome + "/tomcat");
            tomcat.startTomcat();
            tomcatServerStarted = true;
        } else if (2 == JudgApp.START_APP_TYPE)
        {
            if (isTomcatCMStarted())
            {
                return;
            }
            _log.info("Starting up embedded cm web server...");
            EmbeddedTomcat tomcat = EmbeddedTomcat.getInstance();
            tomcat.setPath(ServerEnv.getServerEnv().getIEAIHome() + "/tomcat");
            tomcat.startTomcat();
            tomcatCmStarted = true;
        }
    }

    /**
     * enableWebService
     * 
     * @throws Exception
     */
    public static synchronized void startWebServices () throws Exception
    {
        checkWebservicesNecessary();
        if (webserviceStarted)
        {
            return;
        }
        String ieaiHome = ServerEnv.getServerEnv().getIEAIHome();
        // Expose IEAIService as axis webservice
        EmbeddedTomcat tomcat = EmbeddedTomcat.getInstance();

        // now get path to server manager
        // first check if SM_HOME is specified in startup prameter
        String wsWebapps = System.getProperty("WS_WEBAPPS");
        if (null != wsWebapps)
        {
            // if set, replace {IEAI_HOME} with
            // ServerEnv.getServerEnv().getIEAIHome
            wsWebapps = StringUtil.strReplace(wsWebapps, IEAIHOME, ieaiHome);
        } else
        {
            // if not set, use IEAI_HOME/tomcat/webapps/sm as default
            wsWebapps = ieaiHome + "/tomcat/webapps";
        }
        try
        {
            tomcat.registerWAR("/iEAIWebService", wsWebapps + File.separator + "iEAIWebService");
            tomcat.registerWAR("/agentWebService", (wsWebapps + File.separator + "agentWebService"));

            if (ServerEnv.getServerEnv().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
                Environment.FALSE_BOOLEAN))
            {
                // startProxyWebServices();
            }

        } catch (Exception ex)
        {
            _log.error(ex);
        }
        webserviceStarted = true;
    }

    /**
     * check whether webservices necessary components started
     * 
     * @throws Exception
     */
    private static void checkWebservicesNecessary ()
    {
        startIEAIServer();
    }

    /**
     * check whether tmocat necessary components started
     * 
     * @throws Exception
     */
    private static void checkTomcatNecessary ()
    {
        startIEAIServer();
    }

    /**
     * check whether usertask components started
     * 
     * @throws Exception
     */
    static CircleAvgTimer    timer;
    static ConsumeTimeConfig timeconfig;

    public static void startCircleAvgTimer ()
    {
        timer = new CircleAvgTimer();

        timer.start();
    }

    /**
     * 启动耗时周期计算
     * 
     */
    public static void startConsumeTimer ()
    {
        timeconfig = new ConsumeTimeConfig();
        timeconfig.cycleCount();
    }

    public static void stopCircleAvgTimer ()
    {
        timer.stop();
    }

    // update by yan_wang 增加健康工程
    /**
     * check whether healthcheck components started
     * 
     * @throws Exception
     */
    private static void checkHCNecessary () throws Exception
    {
        startTomcatServer();
    }

    private static void initDbsourceRollbackExpLogger () throws IOException
    {
        WarnLog.getInstance().initDbsourceRollbackExpLogger();
    }

    private static void initWarningLogger () throws IOException
    {
        WarnLog.getInstance().initWarningLogger();
    }

    /**
     * 拓扑图日志文件初始化 <li>Description:</li>
     * 
     * <AUTHOR> 2018年1月22日
     * @throws IOException return void
     */
    private static void initTopoLogger () throws IOException
    {
        if (ServerEnv.getServerEnv().isTopoLogger())
        {
            TopoServerLog tsl = new TopoServerLog();
            tsl.initTopoServerLogger();
        }
    }

    private static boolean      messageBrokerStarted = false;
    private static boolean      tomcatServerStarted  = false;
    private static boolean      tomcatCmStarted      = false;
    private static boolean      servermanagerStarted = false;
    private static boolean      usertaskStarted      = false;
    private static boolean      webserviceStarted    = false;
    private static boolean      iEAIServerStarted    = false;
    private static boolean      serverEnvInited      = false;
    private static boolean      repositoryInited     = false;
    private static boolean      pMStarted            = false;

    private static final String IEAIHOME             = "{IEAI_HOME}";
    // update by yan_wang 增加健康检查工程
    private static boolean      hdStarted            = false;

    private static boolean      ieaisusStarted       = false;

    private static boolean      ieaiSelStarted       = false;

    // update by yan_wang 光大版本耗时
    public static Timer getAlertTimer ()
    {
        return alertTimer;
    }

    public static void startReportThread ()
    {
        MonthReportThread monthThread = new MonthReportThread();
        TimeAreaActCountThread actCountThread = new TimeAreaActCountThread();
        ClearDataThreadForJobscheduling clearThread = new ClearDataThreadForJobscheduling();
        AvgTimeThreadForJobscheduling avgThread = new AvgTimeThreadForJobscheduling();
        monthThread.start();
        _log.info("MonthReportThread start successfully");
        actCountThread.start();
        _log.info("TimeAreaActCountThread start successfully");
        // runningBatchThread.start();
        // _log.info("RunningBatchReportThread start successfully");
        clearThread.start();
        _log.info("ClearDataThreadForJobscheduling start successfully");
        avgThread.start();
        _log.info("AvgTimeThreadForJobscheduling start successfully");

    }

    public static void initDbRetrySet ()
    {
        DBRetryUtil.set_numRetries(ServerEnv.getServerEnv().getIntConfig(ServerEnv.RETRY_CONNECTION_TIME,
            Environment.RETRY_CONNECTION_TIME_DEFAULT));
        DBRetryUtil.set_waitTimes(ServerEnv.getServerEnv().getLongConfig(ServerEnv.RETRY_CONNECTION_TIMES,
            ServerEnv.RETRY_CONNECTION_TIMES_DEFAULT));
    }

    public static synchronized void startProxyWebServices ()
    {
        try
        {
            int startPort = ServerEnv.getServerEnv().getIntConfig(Environment.PROXY_SERVICE_PORT,
                Environment.PROXY_SERVICE_PORT_DEFAULT);
            String address = "http://" + ServerEnv.getServerEnv().getServerIP() + ":" + startPort + "/proxyOpService";
            _log.info(address);
            _log.info("start publish proxy webservice!");
            Endpoint.publish(address, new ProxyOpImpl());
            _log.info("publish proxy webservice successfully!");
        } catch (Exception ex)
        {
            _log.info(ex);
        }
    }

}
