package com.ideal.ieai.server.jobscheduling.repository.flowquery;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.commons.keynodealarm.AlarmCfgInfo;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.core.JudgeDB;
import com.ideal.ieai.server.ieaikernel.ConfigReader;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.log4j.Logger;

import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <ul>
 * <li>Title: FlowTimeQueryManager.java</li>
 * <li>Description:查询报警信息页面Manager</li>
 * <li>Copyright: Copyright 2003</li>
 * <li>Company: ideal</li>
 * </ul>
 *
 * <AUTHOR>
 * 2017年7月9日
 */
public class FlowTimeQueryManager {
    private static final Logger _log = Logger.getLogger(FlowTimeQueryManager.class);
    private static final String YYYYMMDDHHMMSS = "yyyy-MM-dd HH:mm:ss";
    private static final String YYYYMMDD = "yyyy-MM-dd";
    private static final String WARNTIME = "warnTime";
    private static final String IFLOWID = "iflowid";

    private static FlowTimeQueryManager intance = new FlowTimeQueryManager();

    public static FlowTimeQueryManager getInstance() {
        if (intance == null) {
            intance = new FlowTimeQueryManager();
        }
        return intance;
    }

    /**
     * <li>Description:查询超时信息</li>
     *
     * @param flowQueryBean
     * @return
     * @throws RepositoryException
     * @throws Exception           return List
     * <AUTHOR>
     * 2017年7月9日
     */
    public List getFlowTimeOutWarningList(FlowTimeOutQueryBean flowQueryBean) throws RepositoryException {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        List result = new ArrayList();
        String sql = "";
        flowQueryBean.setQueryTable("IEAI_NODE_WARNING");
        String sqlCur = getFlowTimeOutWarningSql(flowQueryBean);
        flowQueryBean.setQueryTable("IEAI_NODE_WARNING_HISTORY");
        String sqlHis = getFlowTimeOutWarningSql(flowQueryBean);
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        // 当前时间戳ms
        long currentTimeL = System.currentTimeMillis();
        for (int i = 0; ; i++) {
            try {
                try {
                    conn = DBResource.getConnection(method, _log, Constants.IEAI_IEAI);
                    int page = flowQueryBean.getPage();
                    int limit = flowQueryBean.getPageSize();
                    int fromNum = 0;
                    int toNum = 0;
                    fromNum = ((page - 1) * limit) + 1;
                    toNum = page * limit;
                    sql = getFlowTimeOutWarningAllSql(flowQueryBean, sqlCur, sqlHis, fromNum, toNum);
                    actStat = conn.prepareStatement(sql);
                    actRS = actStat.executeQuery();

                    while (actRS.next()) {
                        Map<String, Object> nodeInfo = new HashMap<>();
                        nodeInfo.put("prjName", actRS.getString("IPRJNAME"));
                        nodeInfo.put("flowName", actRS.getString("IFLOWNAME"));
                        nodeInfo.put("flowId", actRS.getLong("IFLOWID"));
                        nodeInfo.put("warnId", actRS.getLong("IWARNID"));
                        nodeInfo.put("actName", actRS.getString("IACTNAME") == null ? "": actRS.getString("IACTNAME") );
                        Long startL = actRS.getLong("ISTARTTIME");
                        Long endL = actRS.getLong("IENDTIME");
                        Long runL = actRS.getLong("IRUNTIME");
                        Object[] obj = getRunTimeObj(runL, startL, currentTimeL);
                        runL = (Long) obj[0];
                        nodeInfo.put("startTime", transDateLongToStr(startL, YYYYMMDDHHMMSS));
                        nodeInfo.put("endTime", transDateLongToStr(endL, YYYYMMDDHHMMSS));
                        nodeInfo.put("startTimeDay", transDateLongToStr(startL, YYYYMMDD));
                        nodeInfo.put("endTimeDay", transDateLongToStr(endL, YYYYMMDD));
                        nodeInfo.put("runTime", obj[1]);
                        /*nodeInfo.put("overTime", actRS.getInt("OVERTIME"));
                        nodeInfo.put("overTime1", actRS.getInt("OVERTIME1"));*/
                        nodeInfo.put("runTimeLong", runL);
                        result.add(nodeInfo);
                    }
                } catch (Exception e) {
                    _log.error("getNodeWarningList method of QueryAlarmInfoManager.class SQLException:"
                            + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally {
                    DBResource.closeConn(conn, actRS, actStat, method, _log);
                }
                break;
            } catch (RepositoryException ex) {
                _log.error("getNodeWarningList method of QueryAlarmInfoManager.class RepositoryException:"
                        + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return result;
    }

    // 查询默认耗时
    public AlarmCfgInfo getOverTimeInfo(FlowTimeOutQueryBean queryBean,
                                         Map<String, Object> info) throws RepositoryException {
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        AlarmCfgInfo alarmCfgForReView = new AlarmCfgInfo();
        try {
            conn = DBResource.getConnection("getOverTimeInfo", _log, Constants.IEAI_IEAI);
            String sql = getAlarmCfgOneRecordSql(queryBean, info);
            actStat = conn.prepareStatement(sql);
            actRS = actStat.executeQuery();
            while (actRS.next()) {
                setAlarmCfgInfo(alarmCfgForReView, actRS);
            }
        } catch (SQLException e) {
            _log.error("getOverTimeInfo method of FlowTimeQueryManager.class SQLException:", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally {
            DBResource.closeConn(conn, actRS, actStat, "getOverTimeInfo", _log);
        }
        return alarmCfgForReView;
    }

    private void setAlarmCfgInfo(AlarmCfgInfo alarmCfgForReView, ResultSet actRS) throws SQLException {

        alarmCfgForReView.setJailTimeType(actRS.getString("ijailtimetype"));
        if (alarmCfgForReView.getJailTimeType() == null) {
            alarmCfgForReView.setProjectName(actRS.getString("projectname"));
            alarmCfgForReView.setWorkFlowName(actRS.getString("workflowname"));
            alarmCfgForReView.setActName(actRS.getString("actname"));
            alarmCfgForReView.setRunTimeOverTime(actRS.getString("overtime"));
            alarmCfgForReView.setRunStatisticDays(actRS.getString("istaticdays"));
            alarmCfgForReView.setISetTime(actRS.getString("settime"));
            alarmCfgForReView.setIJailTime(actRS.getString("IBEGINTIME"));
            alarmCfgForReView.setJailType(actRS.getString("ITYPEDAY"));
            alarmCfgForReView.setMonth(actRS.getString("ITYPEMONTH"));
            alarmCfgForReView.setWeek(actRS.getString("ITYPEWEEK"));
            alarmCfgForReView.setOffsetTime(actRS.getString("IOFFSETTIME"));
        } else {
            if ("0".equals(alarmCfgForReView.getJailTimeType()))
            {
                alarmCfgForReView.setJailType(actRS.getString("ijailtype"));
                alarmCfgForReView.setMonth(actRS.getString("ijailmonth"));
                alarmCfgForReView.setWeek(actRS.getString("ijailweek"));
                alarmCfgForReView.setHour(actRS.getString("ijailtimehou"));
                alarmCfgForReView.setMinute(actRS.getString("ijailtimemin"));
                alarmCfgForReView.setStartStatisticDays(actRS.getString("istaticdays"));
            } else if ("1".equals(alarmCfgForReView.getJailTimeType()))
            {
                alarmCfgForReView.setEndJailType(actRS.getString("ijailtype"));
                alarmCfgForReView.setEndMonth(actRS.getString("ijailmonth"));
                alarmCfgForReView.setEndWeek(actRS.getString("ijailweek"));
                alarmCfgForReView.setEndHour(actRS.getString("ijailtimehou"));
                alarmCfgForReView.setEndMinute(actRS.getString("ijailtimemin"));
                alarmCfgForReView.setEndStatisticDays(actRS.getString("istaticdays"));
            }

        }

    }

    private static final String SAWORKFLOWNAME          = "' and workflowname='";
    private static final String SAIWORKFLOWNAME         = "' and iworkflowname='";
    private static final String SAIACTNAME              = "' and iactname='";
    private static final String SAACTNAME               = "' and actname = '";

    private String getAlarmCfgOneRecordSql ( FlowTimeOutQueryBean queryBean,
                                             Map<String, Object> info ) {


        StringBuilder sql = new StringBuilder();
        String ruleName = "";
        if (!info.get("actName").equals("") && !info.get("actName").equals(null))
        {

            String activityName = info.get("actName").toString();
            String prjName = info.get("prjName").toString();
            String flowName = info.get("flowName").toString();
            sql.append(
                    "select ijailtime,iprojectname,iworkflowname,iactname,null as projectname,null as workflowname,null as actname,iisactjail,ijailtimetype,ijailtype,ijailweek,ijailmonth,ijailtimehou,ijailtimemin,ijailstatus,ijailflag,iischeck,isetuser,isettime,null as overtime,null as ioffsettime,null as settime,null as setuser,iid,null as id," +
                            "istaticdays,NULL AS IBEGINTIME,NULL AS ITYPEDAY ,NULL AS ITYPEWEEK,NULL AS ITYPEMONTH ,NULL AS IDAYSET ,NULL AS IWEEKSET ,NULL AS IMONTHSET  FROM IEAI_APP_JAIL where IISCHECK = ISNODATAFIRST and iprojectname='"
                            + prjName + SAIWORKFLOWNAME + flowName + SAIACTNAME + activityName + "' UNION ALL select distinct null,null,null,null,projectname,workflowname,actname,null,null,null,null,null,null,null,null,null,null,null,null,overtime,ioffsettime,settime,setuser,null,id," +
                            "istaticdays,r2.IBEGINTIME,r2.ITYPEDAY ,r2.ITYPEWEEK ,r2.ITYPEMONTH ,r2.IDAYSET ,r2.IWEEKSET ,r2.IMONTHSET from ieai_hand_rule r1 LEFT JOIN IEAI_HAND_RULE_CFG r2 ON r2.IHANDRULEID = r1.ID where ISCHECK = ISNODATAFIRST and projectname='"
                            + prjName + SAWORKFLOWNAME + flowName + SAACTNAME + activityName + "' " +
                            "and id=(select max(id) from ieai_hand_rule where PROJECTNAME = '"+prjName+"' and WORKFLOWNAME = '"+flowName+"' and ACTNAME = '"+activityName+"')"
            );
        } else if ((!info.get("prjName").equals("") && !info.get("prjName").equals(null))
                || (!info.get("flowName").equals("") && !info.get("flowName").equals(null))) {
            String prjName = info.get("prjName").toString();
            String flowName = info.get("flowName").toString();
            sql.append(
                    "select ijailtime,iprojectname,iworkflowname,iactname,null as projectname,null as workflowname,null as actname,iisactjail,ijailtimetype,ijailtype,ijailweek,ijailmonth,ijailtimehou,ijailtimemin,ijailstatus,ijailflag,iischeck,isetuser,isettime,null as overtime,null as ioffsettime,null as settime,null as setuser,iid,null as id," +
                            "istaticdays,NULL AS IBEGINTIME,NULL AS ITYPEDAY ,NULL AS ITYPEWEEK,NULL AS ITYPEMONTH ,NULL AS IDAYSET  FROM IEAI_APP_JAIL where IISCHECK = ISNODATAFIRST and iprojectname='"
                            + prjName + SAIWORKFLOWNAME + flowName + "' UNION ALL select distinct null,null,null,null,projectname,workflowname,actname,null,null,null,null,null,null,null,null,null,null,null,null,overtime,ioffsettime,settime,setuser,null,id," +
                            "istaticdays,r2.IBEGINTIME,r2.ITYPEDAY ,r2.ITYPEWEEK ,r2.ITYPEMONTH ,r2.IDAYSET  from ieai_hand_rule r1 LEFT JOIN IEAI_HAND_RULE_CFG r2 ON r2.IHANDRULEID = r1.ID where ISCHECK = ISNODATAFIRST and projectname='"
                            + prjName + SAWORKFLOWNAME + flowName + "'  " +
                            "and id=(select max(id) from ieai_hand_rule where PROJECTNAME = '"+prjName+"' and WORKFLOWNAME = '"+flowName+"' and (ACTNAME is null or ACTNAME = '') )"
                            + " order by ijailtime desc");
        }

        return sql.toString();

    }

    /**
     * <li>Description:查询平均耗时</li>
     *
     * @param queryBean
     * @return
     * @throws RepositoryException
     * <AUTHOR>
     * 2017年7月9日
     */
    public long getAvgTimeInfo(FlowTimeOutQueryBean queryBean,
                               Map<String, Object> info) throws RepositoryException {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        long result = 0;
        String sql = "";
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        for (int i = 0; ; i++) {
            try {
                try {

                    sql = getAvgTimeInfoSql(queryBean, info);
                    conn = DBResource.getConnection(method, _log, Constants.IEAI_IEAI);
                    actStat = conn.prepareStatement(sql);
                    actRS = actStat.executeQuery();
                    while (actRS.next()) {
                        result = actRS.getLong("IAVGTIME");
                    }
                } catch (Exception e) {
                    _log.error(method + " method of FlowTimeQueryManager.class SQLException:"
                            + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally {
                    DBResource.closeConn(conn, actRS, actStat, method, _log);
                }
                break;
            } catch (RepositoryException ex) {
                _log.error(method + " method of FlowTimeQueryManager.class RepositoryException:"
                        + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return result;
    }

    // 获取列表sql
    private static String getAvgTimeInfoSql(FlowTimeOutQueryBean queryBean,
                                            Map<String, Object> info) {
        String sql = "SELECT (sum(t.IENDTIME) - sum(t.ISTARTTIME))/count(*) IAVGTIME " +
                "FROM IEAI_WORKFLOWINSTANCE t,IEAI_ACTRUNTIME t1  " +
                "WHERE 1 = 1 AND t.IFLOWID = t1.IFLOWID AND t.IENDTIME > 0";
        if (StringUtils.isNotEmpty(queryBean.getPrjName())) {
            sql = sql + " AND t.IPROJECTNAME = '" + queryBean.getPrjName() + "' ";
        } else if (StringUtils.isNotEmpty(queryBean.getFlowName())) {
            sql = sql + " AND t.IFLOWNAME = '" + queryBean.getFlowName() + "' ";
        } else if (StringUtils.isNotEmpty(queryBean.getStartTime1())) { // 启动时间的开始时间
            sql = sql + " AND t.ISTARTTIME >= " + transDateStrToNum(queryBean.getStartTime1()) + " ";
        } else if (StringUtils.isNotEmpty(queryBean.getEndTime1())) { // 启动时间的结束时间
            sql = sql + " AND t.ISTARTTIME < " + transDateStrToNum(queryBean.getEndTime1()) + " ";
        } else if (StringUtils.isNotEmpty(queryBean.getStartTime2())) { // 结束时间的开始时间
            sql = sql + " AND t.IENDTIME >= " + transDateStrToNum(queryBean.getStartTime2()) + " ";
        } else if (StringUtils.isNotEmpty(queryBean.getEndTime2())) { //结束时间的结束时间
            sql = sql + " AND t.IENDTIME < " + transDateStrToNum(queryBean.getEndTime2()) + " ";
        }
        if (info.get("actName") != null) {
            sql = sql + " AND t1.IACTNAME like '%" + info.get("actName").toString() + "%' ";
            sql = sql + " GROUP BY t.IPROJECTNAME,t.IFLOWNAME,t1.IACTNAME";
        } else {
            sql = sql + " GROUP BY t.IPROJECTNAME,t.IFLOWNAME";
        }
        return sql;
    }

    // 获取耗时
    private Object[] getRunTimeObj(Long runTime,
                                   Long startTime,
                                   Long currentTime) {
        Object[] obj = new Object[2];
        if (runTime < 0) {
            runTime = currentTime - startTime;
            obj[0] = runTime;
        } else {
            obj[0] = runTime;
        }
        obj[1] = transMsSec2HMS(runTime);
        return obj;
    }

    // 获取列表sql
    private static String getFlowTimeOutWarningSql(FlowTimeOutQueryBean queryBean) {
        String sql = "SELECT  " +
                "t.IPRJNAME,t.IFLOWNAME,t.IACTNAME, t.ISTARTTIME,t.IENDTIME,t.IRUNTIME,t.IFLOWID,t.IWARNID " +
                " FROM ( " +
                " SELECT T1.PRJNAME IPRJNAME,T1.FLOWNAME IFLOWNAME,T1.ACTNAME IACTNAME, " +
                "  T2.ISTARTTIME,T2.IENDTIME,T2.IENDTIME - T2.ISTARTTIME IRUNTIME,T1.IFLOWID,T1.IWARNID " +
                "FROM (  " +
                "  SELECT T.PRJNAME,T.FLOWNAME,T.ACTNAME,T.IFLOWID,MAX(T.ID) IWARNID " +
                "  FROM " + queryBean.getQueryTable() + " T  " +
                "  WHERE T.TYPE = 2  " +
                "  GROUP BY T.PRJNAME,T.FLOWNAME,T.ACTNAME,T.IFLOWID ORDER BY T.IFLOWID DESC  " +
                ") T1 LEFT JOIN IEAI_WORKFLOWINSTANCE T2  ON T1.IFLOWID = T2.IFLOWID  " +
                "WHERE 1 = 1 AND T2.ISTARTTIME > 0 " +
                ") T " +
                "WHERE 1 = 1 ";
        if (StringUtils.isNotEmpty(queryBean.getPrjName())) {
            sql = sql + "AND t.IPRJNAME = '" + queryBean.getPrjName() + "' ";
        }
        if (StringUtils.isNotEmpty(queryBean.getFlowName())) {
            sql = sql + "AND t.IFLOWNAME = '" + queryBean.getFlowName() + "' ";
        }
        if (StringUtils.isNotEmpty(queryBean.getActName())) {
            sql = sql + "AND t.IACTNAME like  '%" + queryBean.getActName() + "%' ";
        }
        if (StringUtils.isNotEmpty(queryBean.getStartTime1())) { // 启动时间的开始时间
            sql = sql + "AND t.ISTARTTIME >= " + transDateStrToNum(queryBean.getStartTime1()) + " ";
        }
        if (StringUtils.isNotEmpty(queryBean.getEndTime1())) { // 启动时间的结束时间
            sql = sql + "AND t.ISTARTTIME < " + transDateStrToNum(queryBean.getEndTime1()) + " ";
        }
        if (StringUtils.isNotEmpty(queryBean.getStartTime2())) { // 结束时间的开始时间
            sql = sql + "AND t.IENDTIME >= " + transDateStrToNum(queryBean.getStartTime2()) + " ";
        }
        if (StringUtils.isNotEmpty(queryBean.getEndTime2())) { //结束时间的结束时间
            sql = sql + "AND t.IENDTIME < " + transDateStrToNum(queryBean.getEndTime2()) + " ";
        }
        return sql;
    }

    // 获取列表总sql
    private static String getFlowTimeOutWarningAllSql(FlowTimeOutQueryBean queryBean,
                                                      String curSql,
                                                      String hisSql,
                                                      int fromNum,
                                                      int toNum) {

        String orderBy = "";
        if (null != queryBean.getSortMap()) {
            Map sortMap = queryBean.getSortMap();
            if (!sortMap.get("property").equals("avgTime") &&
                    !sortMap.get("property").equals("overTime") &&
                    !sortMap.get("property").equals("status")) {

                if ((null != sortMap.get("property") && !sortMap.get("property").equals("")) &&
                        (null != sortMap.get("direction") && !sortMap.get("direction").equals(""))) {
                    orderBy = " ORDER BY ";
                }
                if (null != sortMap.get("property") && !sortMap.get("property").equals("")) {
                    if (sortMap.get("property").equals("overTime")) {
                        orderBy = orderBy + sortMap.get("property").toString().toUpperCase();
                    } else {
                        orderBy = orderBy + "I" + sortMap.get("property").toString().toUpperCase();
                    }

                }
                if (null != sortMap.get("direction") && !sortMap.get("direction").equals("")) {
                    orderBy = orderBy + " " + sortMap.get("direction").toString().toUpperCase();
                }
            }
        }

        String sql = curSql + "UNION ALL " + hisSql + orderBy;

        if (DBManager.Orcl_Faimily()) {
            sql = "SELECT * FROM  ( SELECT  rownum AS RN ,  A.* FROM ( " + sql + " )  A "
                    + " ) WHERE RN BETWEEN  " + fromNum + " AND  " + toNum;
        } else if (JudgeDB.IEAI_DB_TYPE == 2) {
            sql = "SELECT * FROM  ( SELECT  ROW_NUMBER() OVER() AS RN ,  A.* FROM ( " + sql
                    + ")  A ) WHERE RN BETWEEN  " + fromNum + "  AND " + toNum;
        } else if (JudgeDB.IEAI_DB_TYPE == 3) {
            // 计算 mysql 分页最后一页显示条数不足时，取总数的余数为最后一页数量
            long start = queryBean.getPage();
            long tolimit = queryBean.getPageSize();
            long curpage = ((start - 1) * tolimit);
            sql += " limit " + curpage + "," + tolimit;
        }
        return sql;
    }

    /**
     * <li>Description:查询报警信息</li>
     *
     * @param flowQueryBean
     * @return
     * @throws RepositoryException
     * @throws Exception           return List
     * <AUTHOR>
     * 2017年7月9日
     */
    public int getNodeWarningListCount(FlowTimeOutQueryBean flowQueryBean) throws RepositoryException {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        int result = 0;
        String sql = "";
        flowQueryBean.setQueryTable("IEAI_NODE_WARNING");
        String sqlCur = getFlowTimeOutWarningSql(flowQueryBean);
        flowQueryBean.setQueryTable("IEAI_NODE_WARNING_HISTORY");
        String sqlHis = getFlowTimeOutWarningSql(flowQueryBean);
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        for (int i = 0; ; i++) {
            try {
                try {
                    conn = DBResource.getConnection(method, _log, Constants.IEAI_IEAI);
                    sql = getNodeWarningListCountAllSql(sqlCur, sqlHis);

                    actStat = conn.prepareStatement(sql);
                    actRS = actStat.executeQuery();

                    while (actRS.next()) {
                        result = actRS.getInt("count");
                    }
                } catch (Exception e) {
                    _log.error(method + " method of FlowTimeQueryManager.class SQLException:"
                            + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally {
                    DBResource.closeConn(conn, actRS, actStat, method, _log);
                }
                break;
            } catch (RepositoryException ex) {
                _log.error(method + " method of FlowTimeQueryManager.class RepositoryException:"
                        + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return result;
    }


    // 获取列表countsql
    private String getNodeWarningListCountAllSql(String curSql,
                                                 String hisSql) {
        String sql = curSql + "UNION ALL " + hisSql;
        sql = "select count(*) as count from ( " + sql + ") t";
        return sql;
    }

    /**
     * <li>Description:查询超时信息</li>
     *
     * @param flowQueryBean
     * @return
     * @throws RepositoryException
     * @throws Exception           return List
     * <AUTHOR>
     * 2017年7月9日
     */
    public List getFlowTimeOutWarningFinishList(FlowTimeOutQueryBean flowQueryBean,Map<String, Object> info) throws RepositoryException {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        List result = new ArrayList();
        String sql = "";
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        for (int i = 0; ; i++) {
            try {
                try {
                    conn = DBResource.getConnection(method, _log, Constants.IEAI_IEAI);
                    sql = getFlowTimeOutWarningAvgTimeSql(flowQueryBean,info);
                    actStat = conn.prepareStatement(sql);
                    actRS = actStat.executeQuery();

                    while (actRS.next()) {
                        Map<String, Object> nodeInfo = new HashMap<>();
                        nodeInfo.put("prjName", actRS.getString("IPROJECTNAME"));
                        nodeInfo.put("flowName", actRS.getString("IFLOWNAME"));
                        nodeInfo.put("actName", actRS.getString("IACTNAME") == null ? "": actRS.getString("IACTNAME") );
                        Long avgTime = 0L;
                        String avgTimeS = actRS.getString("avgtime");
                        if (avgTimeS.length() > 0) {
                            avgTime = getTimeMsLong(new BigDecimal(avgTimeS));
                        }
                        nodeInfo.put("avgTime", transMsSec2HMS(avgTime));
                        nodeInfo.put("avgTimeL",avgTime);
                        result.add(nodeInfo);
                    }
                } catch (Exception e) {
                    _log.error("getFlowTimeOutWarningFinishList method of FlowTimeQueryManager.class SQLException:"
                            + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally {
                    DBResource.closeConn(conn, actRS, actStat, method, _log);
                }
                break;
            } catch (RepositoryException ex) {
                _log.error("getFlowTimeOutWarningFinishList method of FlowTimeQueryManager.class RepositoryException:"
                        + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return result;
    }

    // 获取列表sql
    private static String getFlowTimeOutWarningFinishSql(FlowTimeOutQueryBean queryBean,
                                                         Map<String, Object> info ) {
        String sql = "SELECT  " +
                "t.IPRJNAME,t.IFLOWNAME,t.IACTNAME, t.ISTARTTIME,t.IENDTIME,t.IRUNTIME " +
                " FROM ( " +
                " SELECT T1.PRJNAME IPRJNAME,T1.FLOWNAME IFLOWNAME,T1.ACTNAME IACTNAME, " +
                "  T2.ISTARTTIME,T2.IENDTIME,T2.IENDTIME - T2.ISTARTTIME IRUNTIME " +
                "FROM (  " +
                "  SELECT T.PRJNAME,T.FLOWNAME,T.ACTNAME,T.IFLOWID " +
                "  FROM " + queryBean.getQueryTable() + " T  " +
                "  WHERE T.TYPE = 2  " +
                "  GROUP BY T.PRJNAME,T.FLOWNAME,T.ACTNAME,T.IFLOWID ORDER BY T.IFLOWID DESC  " +
                ") T1 LEFT JOIN IEAI_WORKFLOWINSTANCE T2  ON T1.IFLOWID = T2.IFLOWID  " +
                "WHERE 1 = 1 AND T2.ISTARTTIME > 0 AND T2.IENDTIME > 0" +
                ") T " +
                "WHERE 1 = 1 ";


        if (!info.get("actName").equals("") && !info.get("actName").equals(null)) {
            sql = sql + "AND t.IPRJNAME = '" + info.get("prjName").toString()+ "' ";
            sql = sql + "AND t.IFLOWNAME = '" + info.get("flowName").toString() + "' ";
            sql = sql + "AND t.IACTNAME = '" + info.get("actName").toString() + "' ";
        } else {
            sql = sql + "AND t.IPRJNAME = '" + info.get("prjName").toString()+ "' ";
            sql = sql + "AND t.IFLOWNAME = '" + info.get("flowName").toString() + "' ";
        }
        if (StringUtils.isNotEmpty(queryBean.getStartTime1())) { // 启动时间的开始时间
            sql = sql + "AND t.ISTARTTIME >= " + transDateStrToNum(queryBean.getStartTime1()) + " ";
        }
        if (StringUtils.isNotEmpty(queryBean.getEndTime1())) { // 启动时间的结束时间
            sql = sql + "AND t.ISTARTTIME < " + transDateStrToNum(queryBean.getEndTime1()) + " ";
        }
        if (StringUtils.isNotEmpty(queryBean.getStartTime2())) { // 结束时间的开始时间
            sql = sql + "AND t.IENDTIME >= " + transDateStrToNum(queryBean.getStartTime2()) + " ";
        }
        if (StringUtils.isNotEmpty(queryBean.getEndTime2())) { //结束时间的结束时间
            sql = sql + "AND t.IENDTIME < " + transDateStrToNum(queryBean.getEndTime2()) + " ";
        }
        return sql;
    }


    /**
     * dateStringToLongNum
     *
     * @param datetime
     * @return
     */
    private static long transDateStrToNum(String datetime) {
        long time = -1L;
        if (StringUtils.isBlank(datetime)) {
            return -1L;
        }
        SimpleDateFormat f = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            Date parse = f.parse(datetime);
            time = parse.getTime();
        } catch (java.text.ParseException e) {
            _log.error("日期格式错误！", e);
            return -1L;
        }
        return time;
    }

    /**
     * @param time
     * @Title: dateLongToString
     * @Description: 时间戳转化为指定格式的日期字符串（线程安全）
     * @date: 2019年11月4日 下午2:49:40
     */
    public static String transDateLongToStr(Long time, String pattern) {
        if (time == null || time == 0) {
            return "";
        }
        return DateFormatUtils.format(new Date(time), pattern);
    }

    /**
     * 传入时间ms转为 **天**小时**分**秒**毫秒
     *
     * @param ms
     * @return
     */
    public String transMsSec2HMS(Long ms) {
        Integer ss = 1000;
        Integer mi = ss * 60;
        Integer hh = mi * 60;
        Integer dd = hh * 24;

        Long day = ms / dd;
        Long hour = (ms - day * dd) / hh;
        Long minute = (ms - day * dd - hour * hh) / mi;
        Long second = (ms - day * dd - hour * hh - minute * mi) / ss;
        Long milliSecond = ms - day * dd - hour * hh - minute * mi - second * ss;

        StringBuffer sb = new StringBuffer();
        if (day > 0) {
            sb.append(day + "天");
        }
        if (hour > 0) {
            sb.append(hour + "小时");
        }
        if (minute > 0) {
            sb.append(minute + "分");
        }
        if (second > 0) {
            sb.append(second + "秒");
        }
        /*if (milliSecond > 0) {
            sb.append(milliSecond + "毫秒");
        }*/
        return sb.toString();
    }

    /**
     * 传入分钟转ms
     *
     * @param minute
     * @return
     */
    public static Long transIntToLongMs(Integer minute) {
        Integer ss = 1000;
        Integer mi = 60;
        return Long.valueOf((minute * mi * ss));
    }

    // 获取列表sql
    private static String getFlowTimeOutWarningAvgTimeSql(FlowTimeOutQueryBean queryBean,
                                                         Map<String, Object> info ) {
        StringBuilder sql = new StringBuilder();
        if (!info.get("actName").equals("") && !info.get("actName").equals(null)) { // 活动级
            sql.append("SELECT " +
                    "t1.IPROJECTNAME," +
                    "t1.IFLOWNAME, " +
                    "t.IACTNAME,  " +
                    "ROUND(SUM(t.IENDTIME - t.IBEGINEXCTIME)/COUNT(*), 2) AS avgtime " +
                    "FROM IEAI_ACTRUNTIME t " +
                    "LEFT JOIN IEAI_WORKFLOWINSTANCE t1 ON t.IFLOWID = t1.IFLOWID " +
                    "WHERE 1 = 1 " +
                    "AND t.IENDTIME > 0 " +
                    "AND t.IACTTYPE = 'Common' " +
                    "AND (t1.IPROJECTNAME is not null or t1.IPROJECTNAME != '') ");
            sql.append(" AND t1.IPROJECTNAME = '"+info.get("prjName").toString()+"' ");
            sql.append(" AND t1.IFLOWNAME = '"+info.get("flowName").toString()+"' ");
            sql.append(" AND t.IACTNAME = '"+info.get("actName").toString()+"' ");
        } else {// 流程级
            sql.append(
                    "SELECT " +
                    "t.IPROJECTNAME,t.IFLOWNAME,'' as IACTNAME ," +
                    "ROUND(sum(t.IENDTIME -t.ISTARTTIME)/count(*), 2) as avgtime " +
                    "FROM IEAI_WORKFLOWINSTANCE t " +
                    "WHERE 1 = 1 " +
                    "AND t.IENDTIME > 0 ");
            sql.append(" AND t.IPROJECTNAME = '"+info.get("prjName").toString()+"' ");
            sql.append(" AND t.IFLOWNAME = '"+info.get("flowName").toString()+"' ");

        }
        // 启动时间的开始时间
        if (StringUtils.isNotEmpty(queryBean.getStartTime1()) ) { // 启动时间的开始时间
            if (!info.get("actName").equals("") && !info.get("actName").equals(null)){
                sql.append("AND t.IBEGINEXCTIME >= " + transDateStrToNum(queryBean.getStartTime1()) + " ");
            } else {
                sql.append("AND t.ISTARTTIME >= " + transDateStrToNum(queryBean.getStartTime1()) + " ");
            }
        }
        if (StringUtils.isNotEmpty(queryBean.getEndTime1()) ) { // 启动时间的结束时间
            if (!info.get("actName").equals("") && !info.get("actName").equals(null)){
                sql.append("AND t.IBEGINEXCTIME < " + transDateStrToNum(queryBean.getEndTime1()) + " ");
            } else {
                sql.append("AND t.ISTARTTIME < " + transDateStrToNum(queryBean.getEndTime1()) + " ");
            }
        }

        if (StringUtils.isNotEmpty(queryBean.getStartTime2())) { // 结束时间的开始时间
            sql.append("AND t.IENDTIME >= " + transDateStrToNum(queryBean.getStartTime2()) + " ");
        }
        if (StringUtils.isNotEmpty(queryBean.getEndTime2())) { //结束时间的结束时间
            sql.append("AND t.IENDTIME < " + transDateStrToNum(queryBean.getEndTime2()) + " ");
        }
        if (!info.get("actName").equals("") && !info.get("actName").equals(null)) { // 活动级
            sql.append(" GROUP BY t1.IPROJECTNAME,t1.IFLOWNAME,t.IACTNAME");
        } else {
            sql.append(" GROUP BY t.IPROJECTNAME,t.IFLOWNAME");
        }

        return sql.toString();
    }




    // 获取列表sql  内层sql
    private static String getProjectTimeQueryListSql(FlowQueryBean queryBean) {
        StringBuilder sql = new StringBuilder();
        long current = System.currentTimeMillis();
        sql.append("SELECT t.IPROJECTNAME,t.IFLOWNAME,t.IFLOWINSNAME,MIN(t.ISTARTTIME) as ISTARTTIME,");
        sql.append("CASE max(t.IENDTIME) WHEN 0 THEN ").append(current).append(" ELSE max(t.IENDTIME) END IENDTIME,");
        sql.append("max(t.IENDTIME) as IENDTIMEY,t.ISTATUS,max(t.IFLOWID) as IFLOWID,");
        sql.append("FUN_GET_DATE_STRING(MIN(t.ISTARTTIME), 8,  'YYYYMMDD') as STARTDATEYMD,COUNT(*) AS RUN_NUM ");
        sql.append("FROM IEAI_WORKFLOWINSTANCE t WHERE 1=1 AND t.ISTARTTIME > 0 ");
        if (StringUtils.isNotEmpty(queryBean.getPrjName())) {
            sql.append("AND t.IPROJECTNAME = '" + queryBean.getPrjName() + "' ") ;
        }
        if (StringUtils.isNotEmpty(queryBean.getFlowInsName())){ // 启动时间的开始时间
            sql.append("AND FUN_GET_DATE_STRING ( t.ISTARTTIME, 8, 'YYYYMMDD' ) like '%" + queryBean.getFlowInsName() + "%' ") ;
        }
        if (StringUtils.isNotEmpty(queryBean.getStartTime1())) { // 启动时间的开始时间
            sql.append("AND t.ISTARTTIME >= " + transDateStrToNum(queryBean.getStartTime1()) + " ") ;
        }
        if (StringUtils.isNotEmpty(queryBean.getStartTime2())) { // 启动时间的结束时间
            sql.append("AND t.ISTARTTIME < " + transDateStrToNum(queryBean.getStartTime2()) + " ") ;
        }
        if (StringUtils.isNotEmpty(queryBean.getEndTime1())) { // 结束时间的开始时间
            sql.append("AND t.IENDTIME >= " + transDateStrToNum(queryBean.getEndTime1()) + " ") ;
        }
        if (StringUtils.isNotEmpty(queryBean.getEndTime2())) { //结束时间的结束时间
            sql.append("AND t.IENDTIME < " + transDateStrToNum(queryBean.getEndTime2()) + " ") ;
        }
        if (queryBean.getStatus() > 0) {
            sql.append("AND t.ISTATUS = " + queryBean.getStatus() + " ") ;
        }
        sql.append(" GROUP BY t.IPROJECTNAME,t.IFLOWNAME,t.IFLOWINSNAME,t.ISTATUS") ;
        return sql.toString();
    }
    // 获取列表sql
    private static String getProjectTimeQueryListSql1(FlowQueryBean queryBean,String querySql) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM ( ");
        sql.append(querySql);
        sql.append(") t ");
        return sql.toString();
    }

    // 获取列表总sql
    private static String getProjectAllSql(FlowQueryBean queryBean,
                                                      String querySql,
                                                      int fromNum,
                                                      int toNum) {

        StringBuilder orderBy = new StringBuilder();
        if (null != queryBean.getSortMap()){
            Map sortMap = queryBean.getSortMap();
            if ((null != sortMap.get("property") && !sortMap.get("property").equals(""))
            && (null != sortMap.get("direction") && !sortMap.get("direction").equals(""))) {
                orderBy.append(" ORDER BY ");
                if (sortMap.get("property").equals("prjName")) {
                    orderBy.append(" IPROJECTNAME ");
                } else if (sortMap.get("property").equals("upTime")) { // upTime
                    orderBy.append(" ").append("A.IRUNTIME").append(" ");
                } else if (sortMap.get("property").equals("statusConcat")) {
                    orderBy.append("STATUS_CONCAT ");
                } else {
                    String property = "I" + sortMap.get("property").toString().toUpperCase();
                    orderBy.append(" ").append(property).append(" ");
                }
                orderBy.append(" ").append(sortMap.get("direction").toString().toUpperCase()).append("");
            }else {
                orderBy.append(" ").append("ORDER BY A.ISTARTTIME desc");
            }
        }

        String sql = querySql;

        if (DBManager.Orcl_Faimily()) {
            sql = "SELECT * FROM  ( SELECT  rownum AS RN ,  A.* FROM ( " + sql + " )  A " + orderBy + " ) WHERE RN BETWEEN  " + fromNum + " AND  " + toNum;
        } else if (JudgeDB.IEAI_DB_TYPE == 2) {
            sql = "SELECT * FROM  ( SELECT  ROW_NUMBER() OVER() AS RN ,  A.* FROM ( " + sql + ")  A " + orderBy + ") WHERE RN BETWEEN  " + fromNum + "  AND " + toNum;
        } else if (JudgeDB.IEAI_DB_TYPE == 3) {
            // 计算 mysql 分页最后一页显示条数不足时，取总数的余数为最后一页数量
            long start = queryBean.getPage();
            long tolimit = queryBean.getPageSize();
            long curpage = ((start - 1) * tolimit);
            sql = "SELECT * FROM  (" + sql + ")  A " + orderBy + ") ";
            sql += " limit " + curpage + "," + tolimit;
        }
        return sql;
    }


    /*public int getProjectTimeQueryListCount(FlowQueryBean queryBean) throws RepositoryException {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        int result = 0;
        String sql = "";
        String projectSql = getProjectTimeQueryListSql(queryBean);
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        for (int i = 0; ; i++) {
            try {
                try {
                    conn = DBResource.getConnection(method, _log, Constants.IEAI_IEAI);
                    sql = getProjectTimeQueryListCountSql(projectSql);

                    actStat = conn.prepareStatement(sql);
                    actRS = actStat.executeQuery();

                    while (actRS.next()) {
                        result = actRS.getInt("count");
                    }
                } catch (Exception e) {
                    _log.error(method + " method of FlowTimeQueryManager.class SQLException:"
                            + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally {
                    DBResource.closeConn(conn, actRS, actStat, method, _log);
                }
                break;
            } catch (RepositoryException ex) {
                _log.error(method + " method of FlowTimeQueryManager.class RepositoryException:"
                        + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return result;
    }*/

    // 获取列表countsql
    private String getProjectTimeQueryListCountSql(String querySql) {
        String sql = "";
        sql = "select count(*) as count from ( " + querySql + ") t";
        return sql;
    }

    // 获取列表sql
    private static String getProjectQueryListSql(FlowQueryBean queryBean,String querySql) {
        String concatFunc = "";
        if (DBManager.Orcl_Faimily())
        { // oracle
            concatFunc = " LISTAGG( t.ISTATUS, ',') within group( order by t.ISTATUS) ";
        } else {
            concatFunc = " GROUP_CONCAT(t.ISTATUS) ";
        }
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT t.IPROJECTNAME,t.STARTDATEYMD,MIN(t.ISTARTTIME) ISTARTTIME,max(t.IENDTIME) IENDTIME,");
        sql.append("(max(t.IENDTIME) - MIN(t.ISTARTTIME) ) as IRUNTIME,max(t.IFLOWID) as IFLOWID,");
        sql.append(concatFunc).append("as status_concat,");
        sql.append("SUM(RUN_NUM) as RUN_NUM ");
        sql.append("FROM ( ");
        sql.append(querySql);
        sql.append(") t GROUP BY t.IPROJECTNAME,t.STARTDATEYMD");
        return sql.toString();
    }

    /**
     * <li>Description:查询超时信息</li>
     *
     * @param flowQueryBean
     * @return
     * @throws RepositoryException
     * @throws Exception           return List
     * <AUTHOR>
     * 2017年7月9日
     */
    public List getProjectQueryList(FlowQueryBean flowQueryBean) throws RepositoryException {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        List result = new ArrayList();
        String sql = "";
        String projectTimeSql = getProjectTimeQueryListSql(flowQueryBean);// 内层sql
        String projectSql = getProjectQueryListSql(flowQueryBean,projectTimeSql);// 外层sql
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        // 当前时间戳ms
        long currentTimeL = System.currentTimeMillis();
        /*for (int i = 0; ; i++) {
            try {
                try {
                    conn = DBResource.getConnection(method, _log, Constants.IEAI_IEAI);
                    int page = flowQueryBean.getPage();
                    int limit = flowQueryBean.getPageSize();
                    int fromNum = 0;
                    int toNum = 0;
                    fromNum = ((page - 1) * limit) + 1;
                    toNum = page * limit;
                    sql = getProjectAllSql(flowQueryBean, projectSql, fromNum, toNum);
                    actStat = conn.prepareStatement(sql);
                    actRS = actStat.executeQuery();
                    while (actRS.next()) {
                        FlowQueryBean bean = new FlowQueryBean();
                        bean.setPrjName(actRS.getString("IPROJECTNAME"));// 工程名称
                        bean.setFlowInsName(actRS.getString("STARTDATEYMD"));// 工作流名称
                        String ISTATUS_CONCAT = actRS.getString("STATUS_CONCAT");
                        if (ISTATUS_CONCAT.contains("25")) {
                            bean.setStatus(25);// 工作流状态
                        }
                        bean.setStatusConcat(ISTATUS_CONCAT);
                        Long startL = actRS.getLong("ISTARTTIME");
                        Long endL = actRS.getLong("IENDTIME");
                        Long runL = actRS.getLong("IRUNTIME");
                        Object[] obj = getRunTimeObj(runL, startL, currentTimeL);
                        runL = (Long) obj[0];
                        bean.setUpTime(obj[1].toString()); // 已运行时间
                        bean.setStartTime(transDateLongToStr(startL, YYYYMMDDHHMMSS));// 结束时间的开始时间
                        bean.setEndTime(transDateLongToStr(endL, YYYYMMDDHHMMSS));// 启动时间的结束时间
                        bean.setId(startL);
                        result.add(bean);
                    }
                } catch (Exception e) {
                    _log.error(method + " method of FlowTimeQueryManager.class SQLException:"
                            + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally {
                    DBResource.closeConn(conn, actRS, actStat, method, _log);
                }
                break;
            } catch (RepositoryException ex) {
                _log.error(method + " method of FlowTimeQueryManager.class RepositoryException:"
                        + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }*/
        return result;
    }

    /**
     * 获取list 集合重复元素
     *
     * @param list
     * @param <E>
     * @return
     */
    private static <E> List<E> getDuplicateElements(List<E> list) {
        return list.stream().filter(i -> i != "")                           // list 对应的 Stream 并过滤""
                .collect(Collectors.toMap(e -> e, e -> 1, Integer::sum)) // 获得元素出现频率的 Map，键为元素，值为元素出现的次数
                .entrySet()
                .stream()                       // 所有 entry 对应的 Stream
                .filter(e -> e.getValue() > 1)         // 过滤出元素出现次数大于 1 (重复元素）的 entry
                .map(Map.Entry::getKey)                // 获得 entry 的键（重复元素）对应的 Stream
                .collect(Collectors.toList());         // 转化为 List
    }

    public int getProjectQueryListCount(FlowQueryBean queryBean) throws RepositoryException {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        int result = 0;
        String sql = "";
        String projectTimeSql = getProjectTimeQueryListSql(queryBean);
        String projectSql = getProjectQueryListSql(queryBean,projectTimeSql);
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        for (int i = 0; ; i++) {
            try {
                try {
                    conn = DBResource.getConnection(method, _log, Constants.IEAI_IEAI);
                    sql = getProjectTimeQueryListCountSql(projectSql);

                    actStat = conn.prepareStatement(sql);
                    actRS = actStat.executeQuery();

                    while (actRS.next()) {
                        result = actRS.getInt("count");
                    }
                } catch (Exception e) {
                    _log.error(method + " method of FlowTimeQueryManager.class SQLException:"
                            + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally {
                    DBResource.closeConn(conn, actRS, actStat, method, _log);
                }
                break;
            } catch (RepositoryException ex) {
                _log.error(method + " method of FlowTimeQueryManager.class RepositoryException:"
                        + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return result;
    }


    public Map<String,Object> isManualRuleTrueCfgForMap(String ruleName) throws RepositoryException {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Map<String, Object> map = new HashMap<>();
        Connection conn = null;
        for (int i = 0; ; i++) {
            try {
                try {
                    conn = DBResource.getConnection(method, _log, Constants.IEAI_IEAI);
                    map = this.isManualRuleTrueCfgForMap(ruleName,conn);
                } catch (Exception e) {
                    _log.error(method + " method of FlowTimeQueryManager.class SQLException:"
                            + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally {
                    DBResource.closeConnection(conn, method, _log);
                }
                break;
            } catch (RepositoryException ex) {
                _log.error(method + " method of FlowTimeQueryManager.class RepositoryException:"
                        + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return map;
    }

    /**
     * <li齐鲁银行增加开关查询map:</li>
     *
     * @param ruleName
     * @param con
     * @return
     * @throws DBException return Map<String,Long>
     * @throws IOException
     * <AUTHOR>
     * 2020年12月18日
     */
    private Map<String, Object> isManualRuleTrueCfgForMap(String ruleName, Connection con) throws DBException, IOException {
        boolean maxIdSwitch = false;
        double overtimeF = 0;
        long overtime = 0L;
        long offsettime = 0L;
        int dbtype = JudgeDB.IEAI_DB_TYPE; // ORACLE=1;DB2=2;MYSQL=3;
        String sqlTime = "r.ioffsettime/100*r.overtime + r.overtime + NVL(R.IDELAYTIME, 0)";
        if (dbtype == 3) {
            sqlTime = "r.ioffsettime/100*r.overtime + r.overtime + IFNULL(R.IDELAYTIME, 0)";
        }
        ConfigReader cf = ConfigReader.getInstance();
        maxIdSwitch = cf.getBooleanProperties(Environment.QL_MAX_ID_SWITCH, false);
        _log.info("#########isManualRuleTrueCfgForMap.maxIdSwitch=" + maxIdSwitch + "#######");
        String sql = "select r.id,r.overtime,r.ioffsettime," + sqlTime + " as overtimeF,s.ihandruleid,s.ibegintime,s.itypeday,s.itypeweek,s.itypemonth,s.idayset,s.iweekset,s.imonthset from ieai_hand_rule r LEFT JOIN ieai_hand_rule_cfg s ON r.id=s.ihandruleid  "
                + "where (r.ischeck = 0 or r.isedit = 1) and  r.overtime<>0 AND (S.IBEGINTIME is null OR S.IBEGINTIME = '' ) and r.rulename=? ";
        if (maxIdSwitch) {
            sql = sql + " and id=(SELECT max(r.id) " +
                    "FROM ieai_hand_rule r LEFT JOIN ieai_hand_rule_cfg s ON r.id = s.ihandruleid " +
                    "WHERE (S.IBEGINTIME is null OR S.IBEGINTIME = '' ) AND  r.rulename=?) ";
        }
        PreparedStatement pstm = null;
        ResultSet reSet = null;
        Map<String, Object> map = new HashMap<>();

        try {
            Calendar cal = Calendar.getInstance();
            pstm = con.prepareStatement(sql);
            pstm.setString(1, ruleName);
            if (maxIdSwitch) {
                pstm.setString(2, ruleName);
            }
            /*_log.info("###########isManualRuleTrueCfgForMap.sql:select r.id,r.overtime,s.ihandruleid,s.ibegintime,s.itypeday,s.itypeweek,s.itypemonth,s.idayset,s.iweekset,s.imonthset from ieai_hand_rule r LEFT JOIN ieai_hand_rule_cfg s ON r.id=s.ihandruleid  "
                    + "where (r.ischeck = 0 or r.isedit = 1) and  r.overtime<>0 AND (S.IBEGINTIME is null OR S.IBEGINTIME = '' ) and r.rulename='" + ruleName + "' and id=(select max(id) from ieai_hand_rule where rulename= '" + ruleName + "' ##################");*/
            reSet = pstm.executeQuery();
            while (reSet.next()) {
                // 存在日
                if (StringUtils.isNotEmpty(reSet.getString("itypeday")) && reSet.getString("idayset").equals("1")) {
                    if (cal.get(Calendar.DAY_OF_MONTH) != Integer.parseInt(reSet.getString("itypeday"))) {
                        continue;
                    }
                } else if (StringUtils.isNotEmpty(reSet.getString("itypeday"))
                        && !reSet.getString("idayset").equals("1")) {
                    // 存在日取反
                    if (cal.get(Calendar.DAY_OF_MONTH) == Integer.parseInt(reSet.getString("itypeday"))) {
                        continue;
                    }
                }
                // 存在周
                if (StringUtils.isNotEmpty(reSet.getString("itypeweek")) && reSet.getString("iweekset").equals("1")) {
                    String week = weekConvert(reSet.getString("itypeweek"));
                    if ((cal.get(Calendar.DAY_OF_WEEK) - 1) != Integer.parseInt(week)) {
                        continue;
                    }
                } else if (StringUtils.isNotEmpty(reSet.getString("itypeweek"))
                        && !reSet.getString("iweekset").equals("1")) {
                    String week = weekConvert(reSet.getString("itypeweek"));
                    // 存在周取反
                    if (cal.get(Calendar.DAY_OF_MONTH) == Integer.parseInt(week)) {
                        continue;
                    }
                }
                // 存在日
                if (StringUtils.isNotEmpty(reSet.getString("itypemonth")) && reSet.getString("imonthset").equals("1")) {
                    if (cal.get(Calendar.MONTH) != Integer.parseInt(reSet.getString("itypemonth"))) {
                        continue;
                    }
                } else if (StringUtils.isNotEmpty(reSet.getString("itypemonth"))
                        && !reSet.getString("imonthset").equals("1")) {
                    // 存在日取反
                    if (cal.get(Calendar.MONTH) == Integer.parseInt(reSet.getString("itypemonth"))) {
                        continue;
                    }
                }
                overtime = reSet.getLong("overtime");
                offsettime = reSet.getLong("ioffsettime");
            }

            overtimeF = getOverTimeLong(overtime, offsettime);
            map.put("overtime", overtimeF);
        } catch (SQLException e) {
            _log.error("error when exec sql in TakeTimeDBManager isManualRuleTrue", e);
        } finally {
            try {
                if (pstm != null)
                    pstm.close();
                if (reSet != null)
                    reSet.close();
            } catch (SQLException ex) {
                _log.info("error when close resource in TakeTimeDBManager isManualRuleTrue!", ex);
            }

        }
        return map;
    }

    private static String weekConvert(String week) {
        if (week.equals("周一")) {
            return "1";
        } else if (week.equals("周二")) {
            return "2";
        } else if (week.equals("周三")) {
            return "3";
        } else if (week.equals("周四")) {
            return "4";
        } else if (week.equals("周五")) {
            return "5";
        } else if (week.equals("周六")) {
            return "6";
        } else {
            return "7";
        }
    }

    private static double getOverTimeLong(long overTime,long offsetTime){
        double result = overTime * (offsetTime/100) + overTime;
        return result;
    }

    private static long getTimeMsLong(BigDecimal timeY) {
        long timeYY = timeY.longValue();
        return timeYY;
    }
}
