package com.ideal.ieai.server.jobscheduling.repository.taskdownload;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ideal.ieai.commons.Conscommon;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ProjectInfo;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.core.JudgeDB;
import com.ideal.ieai.server.SQLUtil;
import com.ideal.ieai.server.jobscheduling.util.taskdownloadbean.ActRelationInfo;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.db.DBRetryUtil;
import com.ideal.ieai.server.repository.lob.BinaryEntity;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.Map.Entry;

/**   
 * @ClassName:  TaskDownloadManager   
 * @Description: 作业依赖关系导出
 * @author: yue_sun 
 * @date:   2018年2月26日 上午8:51:27   
 *     
 * @Copyright: 2018-2027 www.idealinfo.com Inc. All rights reserved. 
 * 
 */
public class TaskDownloadManager
{
    private static final Logger              _log         = Logger.getLogger(TaskDownloadManager.class);
    private static  final TaskDownloadManager _instProcess = new TaskDownloadManager();

    private static  final String  IBRANCHCONDITION = "IBRANCHCONDITION";
    
    private static  final String  ICALENDNAME = "ICALENDNAME";
    private static  final String  IDELAYTIME = "IDELAYTIME";
    private static  final String  INAME = "INAME";
    private static  final String  IOPERATIONID = "IOPERATIONID";
    private static  final String  IRETRYNUM = "IRETRYNUM";
    private static  final String  IOPERATIONID1 = "Ioperationid";
    private static  final String  PREACT = "preact";
    private static  final String  PROJECTNAME = "projectname";
    private static  final String  SUCCACT = "succact";
    private static  final String  TOTAL = "total";
    
    
    public static  TaskDownloadManager getInstance ()
    {
        return _instProcess;
    }

    private TaskDownloadManager()
    {
    }

    /**   
     * @Title: getTaskProject   
     * @Description: 获取所有任务工程信息
     * @param map
     * @param projectname
     * @param start
     * @param page
     * @param limit
     * @param sort
     * @param conn
     * @param result
     * @return
     * @throws RepositoryException      
     * @author: yue_sun 
     * @date:   2018年2月26日 上午8:52:25   
     */
    public List getTaskProject ( Map map, String projectname,  String page, String limit, String sort,
            Connection conn, List result ) throws RepositoryException
    {

        PreparedStatement ps = null;
        ResultSet rs = null;
        String sql = "";
        String sqlWhere = "";
        String sqlCount = "";
        int total = 0;
        int totalCount = 0;
        List list = new ArrayList();
        if (StringUtils.isNotBlank(projectname))
        {
            if (JudgeDB.IEAI_DB_TYPE .equals( JudgeDB.MYSQL))
            {
                sqlWhere = " and lower(a.INAME) LIKE concat('%',lower('" + projectname + "'),'%')";
            } else
            {
                sqlWhere = " and lower(a.INAME) LIKE '%'||lower('" + projectname + "')||'%'";
            }
        }
        try
        {
            int fromNum = 0;
            int toNum = 0;
            fromNum = ((Integer.parseInt(page) - 1) * Integer.parseInt(limit)) + 1;
            toNum = Integer.parseInt(page) * Integer.parseInt(limit);
            String orderBy = " ORDER BY INAME NULLS LAST ";
            if (JudgeDB.IEAI_DB_TYPE .equals( JudgeDB.MYSQL))
            {
                orderBy = " ORDER BY INAME";
            }
            try
            {
                if (StringUtils.isNotBlank(sort) )
                {
                    JSONArray jsonList = JSONArray.parseArray(sort);
                    JSONObject jsonObj = (JSONObject) jsonList.get(0);
                    if (PROJECTNAME.equals(jsonObj.getString("property")))
                    {
                        if (JudgeDB.IEAI_DB_TYPE .equals( JudgeDB.MYSQL))
                        {
                            orderBy = " order by INAME " + jsonObj.getString("direction");
                        } else
                        {
                            orderBy = " order by INAME " + jsonObj.getString("direction") + " NULLS LAST";
                        }

                    }
                }
            } catch (Exception e)
            {
                if (JudgeDB.IEAI_DB_TYPE .equals( JudgeDB.MYSQL))
                {
                    orderBy = " ORDER BY INAME ";
                } else
                {
                    orderBy = " ORDER BY INAME NULLS LAST ";
                }
            }

            sql = "select count(*)  from ieai_project a where a.iuploadnum = (select max(c.iuploadnum) from ieai_project c where a.iname = c.iname) "
                    + sqlWhere;
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next())
            {
                total = rs.getInt(1);
            }

            String iids = "";
            if (JudgeDB.IEAI_DB_TYPE .equals( JudgeDB.MYSQL))
            {
                // 计算 mysql 分页最后一页显示条数不足时，取总数的余数为最后一页数量
                long start1 = Long.parseLong(page);
                long tolimit = Long.parseLong(limit);
                long curpage = ((start1 - 1) * tolimit);
                String sqlPage = " limit " + curpage + "," + tolimit;

                if (result.size() == total)
                {
                    sqlCount = "select count(1) as count from (select a.iid, a.iname  from ieai_project a where a.iuploadnum = (select max(c.iuploadnum) from ieai_project c where a.iname = c.iname) "
                            + " and ( a.iname in (SELECT   t.imainproname  FROM ieai_excelmodel t ) "
                            + " or a.iname in (SELECT t.imainlinename   FROM ieai_excelmodel t ) or "
                            + " a.iname in (SELECT t.ichildproname   FROM ieai_excelmodel t ))  " + sqlWhere + "  ) TT";
                    ps = conn.prepareStatement(sqlCount);
                    rs = ps.executeQuery();
                    while (rs.next())
                    {
                        totalCount = rs.getInt(1);
                    }

                    sql = "select * from (select a.iid, a.iname  from ieai_project a where a.iuploadnum = (select max(c.iuploadnum) from ieai_project c where a.iname = c.iname) "
                            + " and ( a.iname in (SELECT t.imainproname FROM   ieai_excelmodel t ) "
                            + " or a.iname in (SELECT t.imainlinename   FROM ieai_excelmodel t ) or "
                            + " a.iname in (SELECT t.ichildproname FROM ieai_excelmodel   t )) "
                            + sqlWhere + orderBy + " ) tt " + sqlPage;
                    ps = conn.prepareStatement(sql);
                    rs = ps.executeQuery();
                    while (rs.next())
                    {
                        Map maps = new HashMap();
                        maps.put("iid", rs.getString("IID"));
                        maps.put(PROJECTNAME, rs.getString(INAME));
                        list.add(maps);
                    }
                    map.put(TOTAL, totalCount);
                } else
                {
                    for (int i = 0; i < result.size(); i++)
                    {
                        ProjectInfo project = (ProjectInfo) result.get(i);
                        if (i == 0)
                        {
                            iids = String.valueOf(project.getId());
                        } else
                        {
                            iids = iids + "," + project.getId();
                        }
                    }

                    sqlCount = "select count(1) from (select a.iid, a.iname from ieai_project a where "
                            + " a.iuploadnum = (select max(c.iuploadnum) from ieai_project c where a.iname = "
                            + " c.iname) and a.iid in ( " + iids + ") "
                            + " and ( a.iname in (SELECT t.imainproname   FROM ieai_excelmodel  t ) "
                            + " or a.iname in (SELECT t.imainlinename FROM  ieai_excelmodel  t ) or "
                            + " a.iname in (SELECT t.ichildproname  FROM  ieai_excelmodel t )) " + sqlWhere + "  )  TT";
                    ps = conn.prepareStatement(sqlCount);
                    rs = ps.executeQuery();
                    while (rs.next())
                    {
                        totalCount = rs.getInt(1);
                    }

                    sql = "select * from (select a.iid, a.iname  from ieai_project a where a.iuploadnum = (select max(c.iuploadnum) from ieai_project c where a.iname = c.iname) and a.iid in ("
                            + iids + ") " + " and (   a.iname in (SELECT t.imainproname FROM ieai_excelmodel t ) "
                            + " or a.iname in (SELECT   t.imainlinename FROM ieai_excelmodel t ) or "
                            + " a.iname in ( SELECT  t.ichildproname FROM ieai_excelmodel t )) " + sqlWhere + orderBy
                            + " ) tt" + sqlPage;
                    ps = conn.prepareStatement(sql);
                    rs = ps.executeQuery();
                    while (rs.next())
                    {
                        Map maps = new HashMap();
                        maps.put("iid", rs.getString("IID"));
                        maps.put(PROJECTNAME, rs.getString(INAME));
                        list.add(maps);
                    }
                    map.put(TOTAL, totalCount);
                }
            } else
            {
                 if (result.size() == total)
                 {
                    sqlCount = "select count(1) as count from (select a.iid, a.iname  from ieai_project a where a.iuploadnum = (select max(c.iuploadnum) from ieai_project c where a.iname = c.iname) "
                            + " and ( a.iname in (SELECT t.imainproname FROM ieai_excelmodel t ) "
                            + " or   a.iname in (SELECT t.imainlinename FROM ieai_excelmodel t ) or "
                            + " a.iname  in  (SELECT  t.ichildproname FROM ieai_excelmodel t )) " + sqlWhere
                            + " ) TT";
                    ps = conn.prepareStatement(sqlCount);
                    rs = ps.executeQuery();
                    while (rs.next())
                    {
                        totalCount = rs.getInt(1);
                    }

                    sql = "select * from (select ROW_NUMBER() OVER("
                            + orderBy
                            + ") AS RN ,a.iid, a.iname  from ieai_project a where a.iuploadnum = (select max(c.iuploadnum) from ieai_project c where a.iname = c.iname) "
                            + " and ( a.iname in (SELECT t.imainproname FROM ieai_excelmodel t ) "
                            + " or a.iname in (SELECT t.imainlinename  FROM    ieai_excelmodel t ) or "
                            + " a.iname in (SELECT t.ichildproname    FROM ieai_excelmodel t )) "
                            + sqlWhere + " ) where RN BETWEEN " + fromNum + " and " + toNum;
                    ps = conn.prepareStatement(sql);
                    rs = ps.executeQuery();
                    while (rs.next())
                    {
                        Map maps = new HashMap();
                        maps.put("iid", rs.getString("IID"));
                        maps.put(PROJECTNAME, rs.getString(INAME));
                        list.add(maps);
                    }
                    map.put(TOTAL, totalCount);
                 } else
                 {
                     List<Long> idList = new ArrayList<>();

                    for (int i = 0; i < result.size(); i++)
                    {
                        ProjectInfo project = (ProjectInfo) result.get(i);
                        idList.add(project.getId());
                    }
                     Long[] eIds = idList.toArray(new Long[idList.size()]);
                    sqlCount = "select count(1) from (select a.iid, a.iname from ieai_project a where "
                            + " a.iuploadnum = (select max(c.iuploadnum) from ieai_project c where a.iname = "
                            + " c.iname) and ( " + Conscommon.getInSql(eIds, "a.iid", 1000)
                            + ") and ( a.iname in (SELECT t.imainproname FROM ieai_excelmodel t ) "
                            + " or a.iname in (SELECT t.imainlinename FROM ieai_excelmodel t ) or "
                            + " a.iname in (SELECT t.ichildproname FROM ieai_excelmodel t )) " + sqlWhere + " ) TT";
                    ps = conn.prepareStatement(sqlCount);
                    rs = ps.executeQuery();
                    while (rs.next())
                    {
                        totalCount = rs.getInt(1);
                    }

                    sql = "select * from (select ROW_NUMBER() OVER(" + orderBy
                            + ") AS RN ,a.iid, a.iname from ieai_project a where a.iuploadnum = (select max(c.iuploadnum) from ieai_project c where a.iname = c.iname) and ( "
                            +  Conscommon.getInSql(eIds, "a.iid", 1000) + " ) and ( a.iname in (SELECT t.imainproname FROM ieai_excelmodel t ) "
                            + " or a.iname in (SELECT t.imainlinename FROM ieai_excelmodel t ) or "
                            + " a.iname in (SELECT t.ichildproname FROM ieai_excelmodel t )) " + " " + sqlWhere
                            + " ) where RN BETWEEN " + fromNum + " and " + toNum;
                    ps = conn.prepareStatement(sql);
                    rs = ps.executeQuery();
                    while (rs.next())
                    {
                        Map maps = new HashMap();
                        maps.put("iid", rs.getString("IID"));
                        maps.put(PROJECTNAME, rs.getString(INAME));
                        list.add(maps);
                    }
                    map.put(TOTAL, totalCount);
                 }
            }

        } catch (Exception e)
        {
            _log.error("getTaskProject method of TaskDownloadManager.class SQLException:" , e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, "TaskDownloadManager.getTaskProject", _log);
        }
        return list;
    }

    private void perListMap(ResultSet rs,List preList,Map preMap) throws SQLException{
        ActRelationInfo workflows = null;
        String tmpId = "";
        while (rs.next())
        {
            if (null != workflows)
            {
                if ((rs.getString(1)).equals(workflows.getId()))
                {
                    if(rs.getString(5)!=null&&!"".equals(workflows.getPreAct())){
                    workflows.setPreAct(workflows.getPreAct() + "," + rs.getString(5));
                    continue;
                    }
                } else
                {
                    preList.add(workflows);
                    preMap.put(tmpId, workflows);
                }
            }
            workflows = new ActRelationInfo();
            workflows.setId(rs.getString(1));
            workflows.setProjectname(rs.getString(2));
            workflows.setMainLineName(rs.getString(3));
            workflows.setActName(rs.getString(4));
            workflows.setPreAct(StringUtils.isNotEmpty(rs.getString(5)) ? rs.getString(5) : "");
            workflows.setHeadFlag(rs.getString(6));
            workflows.setOkFilePath(rs.getString(7));
            workflows.setChildproName(rs.getString(8));
            workflows.setActDesc(rs.getString(9));
            workflows.setOutPara(rs.getString(10));
            workflows.setShellHouse(rs.getString(11));
            workflows.setShellName(rs.getString(12));
            workflows.setLastLine(rs.getString(13));
            workflows.setOkFileWeek(rs.getString(14));
            workflows.setFlag(rs.getString(15));
            workflows.setAgentGroup(rs.getString(16));
            workflows.setAgentCheckGroup(rs.getString(17));
            workflows.setAptGroupName(rs.getString(18));
            workflows.setAptFileName(rs.getString(19));
            workflows.setIsdb2(rs.getString(20));
            workflows.setDb2ip(rs.getString(21));
            workflows.setAptResGroupName(rs.getString(22));
            workflows.setSystem(rs.getString(23));
            workflows.setRedo(rs.getString(24));
            workflows.setWeights(rs.getString(25));
            workflows.setPriority(rs.getString(26));
            workflows.setIsDisabled(rs.getString(27));
            workflows.setIsAgentGroup(rs.getInt(28) == 0 );
            workflows.setDays(rs.getString("DAYS"));
            workflows.setLogic(rs.getString("LOGIC"));
            workflows.setWdays(rs.getString("WDAYS"));
            workflows.setMonth(rs.getString("IMONTH"));
            workflows.setPerformUser(rs.getString("PERFORMUSER"));
            workflows.setVirtualName(rs.getString("VIRTUALNAME"));
            workflows.setJoblist(rs.getString("JOBLIST"));
            workflows.setSequenceNumber(rs.getString("SEQUENCENUMBER"));
            workflows.setJobtype(rs.getString("IJOBTYPE"));
            workflows.setEndTag(rs.getInt("IENDTAG")==1?"是":"否");
        /*    workflows.setOkFileName(rs.getString("OKFILENAME"));*/
            workflows.setUserTask(rs.getInt("IUSERTASK"));
            if(Environment.getInstance().getGYBankSwitch()){
                workflows.setActNo(rs.getInt("IACTNO")+"");
            }else if(Environment.getInstance().getDGBankSwitch()){
                workflows.setActParams(rs.getString("IACTPARAMS"));
            }
            setWorkflows( rs, workflows);
            tmpId = rs.getString(1);
        }
        if (workflows!=null){
            preList.add(workflows);
            preMap.put(tmpId, workflows);
        }

    }
    
    private void setWorkflows(ResultSet rs,ActRelationInfo workflows) throws SQLException{
        workflows.setDelayTime(rs.getString(29) == null ? "" : rs.getString(29));
        workflows.setBranchCondition(rs.getString(30) == null ? "" : rs.getString(30));
        workflows.setReTryCount(rs.getInt(31) == 0 ? "" : rs.getString(31));
        workflows.setCalendName(rs.getString(32) == null ? "" : rs.getString(32));
        workflows.setReTryTime(rs.getInt(33));
        workflows.setReTryEndTime(rs.getString(34) == null ? "" : rs.getString(34));
        workflows.setSkip(rs.getString(35) == null ? "" : rs.getString(35));
        workflows.setDelayWarnning(rs.getString(36) == null ? "" : rs.getString(36));
    }
    //处理导出依赖及工作流数据
    private Map getRelationInfoForOracleOrMysqlList(String prjName, int dbType) throws RepositoryException{
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        List preList = new ArrayList();
        List succList = new ArrayList();
        List list = new ArrayList();
        Map preMap = new HashMap();
        Map succMap = new HashMap();
        Map returnMap = new HashMap();
        Map  VirtualkeyMap= new HashMap();
        Connection con = null;
        PreparedStatement pres = null;
        ResultSet rs = null;
        String presql ="";
                String succsql ="";
        if(Environment.getInstance().isBhODSEXPORTRELYONNAME()){
            presql = "SELECT distinct AE.IOPERATIONID, AE.IMAINPRONAME, AE.IMAINLINENAME, AE.IACTNAME,AE.preact,AE.IHEADTAILFLAG,AE.IOKFILEABSOLUTEPATH,AE.ICHILDPRONAME,AE.IACTDESCRIPTION,AE.IOUTPUTPARAM,AE.ISHELLHOUSE,AE.ISHELLABSOLUTEPATH, AE.ILASTLINE,AE.IOKFILEFINDWEEK,AE.IFLAG,AE.IAGENTSOURCEGROUP, AE.ICHECKAGENTGROUP ,AE.APTGROUPNAME, AE.APTFILENAME, AE.ISDB2, AE.DB2IP, AE.APTRESGROUPNAME, AE.ISYSTEM,AE.IREDO,AE.IWEIGHTS,AE.IPRIORITY,case when exists (select iid from IEAI_TASK_DISABLE_CON C WHERE C.IPRONAME = AE.ICHILDPRONAME AND C.IFLOWNAME=AE.IACTNAME AND C.ISYSTEM=AE.ISYSTEM ) then  '是' ELSE  '无'  end  as IDISABLED ,AE.ISAGENTGROUP,AE.IDELAYTIME,AE.IBRANCHCONDITION,AE.IRETRYNUM, AE.ICALENDNAME, AE.IRETRYTIME, AE.IRETRYENDTIME ,AE.IAUTOSKIP, AE.IDELAYWARNNING,AE.DAYS,AE.LOGIC,AE.WDAYS,AE.IMONTH,AE.PERFORMUSER,AE.VIRTUALNAME,AE.JOBLIST,AE.SEQUENCENUMBER,AE.IENDTAG,AE.IUSERTASK,AE.IJOBTYPE  from (SELECT distinct E.Ioperationid, E.IMAINPRONAME, E.IMAINLINENAME, E.IACTNAME,C.IACTNAME as preact, E.IHEADTAILFLAG, E.IOKFILEABSOLUTEPATH, E.ICHILDPRONAME, E.IACTDESCRIPTION, E.IOUTPUTPARAM, E.ISHELLHOUSE, E.ISHELLABSOLUTEPATH, E.ILASTLINE, E.IOKFILEFINDWEEK, E.IFLAG,  E.IAGENTSOURCEGROUP, E.ICHECKAGENTGROUP, E.APTGROUPNAME, E.APTFILENAME, E.ISDB2, E.DB2IP, E.APTRESGROUPNAME ,E.ISYSTEM,E.IREDO,E.IWEIGHTS,E.IPRIORITY,E.ISAGENTGROUP,E.IDELAYTIME,E.IBRANCHCONDITION,E.IRETRYNUM, E.ICALENDNAME, E.IRETRYTIME,E.IRETRYENDTIME,E.IAUTOSKIP,E.IDELAYWARNNING,E.DAYS,E.LOGIC,E.WDAYS,E.IMONTH,E.PERFORMUSER,E.VIRTUALNAME,E.JOBLIST,E.SEQUENCENUMBER,E.IENDTAG,E.IUSERTASK,E.IJOBTYPE  FROM IEAI_EXCELMODEL E  left join IEAI_ACTPRE A on  A.IPREACTNAME = E.Iactname AND A.IPROJECTNAME=E.IMAINPRONAME left join IEAI_EXCELMODEL C  on A.IOPERATIONID=C.IOPERATIONID WHERE ( E.IMAINPRONAME=?  OR E.ICHILDPRONAME=? )) AE where    AE.preact in (select ia.IACTNAME from  ieai_excelmodel ia where  ia.IMAINPRONAME=? or ia.ICHILDPRONAME=? ) and  AE.preact is not null order by AE.IOPERATIONID";
            succsql = " SELECT distinct AE.IOPERATIONID, AE.IMAINPRONAME, AE.IMAINLINENAME, AE.IACTNAME,AE.succact,AE.IHEADTAILFLAG,AE.IOKFILEABSOLUTEPATH,AE.ICHILDPRONAME,AE.IACTDESCRIPTION,AE.IOUTPUTPARAM,AE.ISHELLHOUSE,AE.ISHELLABSOLUTEPATH, AE.ILASTLINE,AE.IOKFILEFINDWEEK,AE.IFLAG,AE.IAGENTSOURCEGROUP, AE.ICHECKAGENTGROUP , AE.APTGROUPNAME, AE.APTFILENAME, AE.ISDB2, AE.DB2IP, AE.APTRESGROUPNAME,AE.ISYSTEM,AE.IREDO,AE.IWEIGHTS,AE.IPRIORITY,case when exists (select iid from IEAI_TASK_DISABLE_CON C WHERE C.IPRONAME = AE.ICHILDPRONAME AND C.IFLOWNAME=AE.IACTNAME AND C.ISYSTEM=AE.ISYSTEM ) then  '是' ELSE  '无'  end  as IDISABLED ,AE.ISAGENTGROUP,AE.IDELAYTIME,AE.IBRANCHCONDITION,AE.IRETRYNUM, AE.ICALENDNAME, AE.IRETRYTIME, AE.IRETRYENDTIME ,AE.IAUTOSKIP, AE.IDELAYWARNNING,AE.DAYS,AE.LOGIC,AE.WDAYS,AE.IMONTH,AE.PERFORMUSER,AE.VIRTUALNAME,AE.JOBLIST,AE.SEQUENCENUMBER,AE.IENDTAG,AE.IUSERTASK ,AE.IJOBTYPE  from (SELECT distinct E.Ioperationid, E.IMAINPRONAME, E.IMAINLINENAME, E.IACTNAME,C.IACTNAME as succact, E.IHEADTAILFLAG, E.IOKFILEABSOLUTEPATH, E.ICHILDPRONAME, E.IACTDESCRIPTION, E.IOUTPUTPARAM, E.ISHELLHOUSE, E.ISHELLABSOLUTEPATH, E.ILASTLINE, E.IOKFILEFINDWEEK, E.IFLAG,  E.IAGENTSOURCEGROUP, E.ICHECKAGENTGROUP, E.APTGROUPNAME, E.APTFILENAME, E.ISDB2, E.DB2IP, E.APTRESGROUPNAME,E.ISYSTEM,E.IREDO,E.IWEIGHTS,E.IPRIORITY,E.ISAGENTGROUP,E.IDELAYTIME,E.IBRANCHCONDITION,E.IRETRYNUM,E.ICALENDNAME,E.IRETRYTIME,E.IRETRYENDTIME,E.IAUTOSKIP,E.IDELAYWARNNING,E.DAYS,E.LOGIC,E.WDAYS,E.IMONTH,E.PERFORMUSER,E.VIRTUALNAME,E.JOBLIST,E.SEQUENCENUMBER,E.IENDTAG,E.IUSERTASK,E.IJOBTYPE  FROM IEAI_EXCELMODEL E  left join IEAI_ACTSUCC A on  A.Isuccactname = E.Iactname AND A.IPROJECTNAME=E.IMAINPRONAME left join IEAI_EXCELMODEL C  on A.IOPERATIONID=C.IOPERATIONID WHERE ( E.IMAINPRONAME=?  OR E.ICHILDPRONAME=? ) ) AE where    AE.succact in (select ia.IACTNAME from  ieai_excelmodel ia where  ia.IMAINPRONAME=? or ia.ICHILDPRONAME=? ) and  AE.succact is not null order by AE.IOPERATIONID";
            // bld 8.14.0
//            presql = " SELECT distinct AE.IOPERATIONID, AE.IMAINPRONAME, AE.IMAINLINENAME, AE.IACTNAME,AE.preact,AE.IHEADTAILFLAG,AE.IOKFILEABSOLUTEPATH,AE.ICHILDPRONAME,AE.IACTDESCRIPTION,AE.IOUTPUTPARAM,AE.ISHELLHOUSE,AE.ISHELLABSOLUTEPATH, AE.ILASTLINE,AE.IOKFILEFINDWEEK,AE.IFLAG,AE.IAGENTSOURCEGROUP, AE.ICHECKAGENTGROUP ,AE.APTGROUPNAME, AE.APTFILENAME, AE.ISDB2, AE.DB2IP, AE.APTRESGROUPNAME, AE.ISYSTEM,AE.IREDO,AE.IWEIGHTS,AE.IPRIORITY,case when exists (select iid from IEAI_TASK_DISABLE_CON C WHERE C.IPRONAME = AE.ICHILDPRONAME AND C.IFLOWNAME=AE.IACTNAME AND C.ISYSTEM=AE.ISYSTEM ) then  '是' ELSE  '无'  end  as IDISABLED ,AE.ISAGENTGROUP,AE.IDELAYTIME,AE.IBRANCHCONDITION,AE.IRETRYNUM, AE.ICALENDNAME, AE.IRETRYTIME, AE.IRETRYENDTIME ,AE.IAUTOSKIP, AE.IDELAYWARNNING,AE.DAYS,AE.LOGIC,AE.WDAYS,AE.IACTNO  from (SELECT distinct E.Ioperationid, E.IMAINPRONAME, E.IMAINLINENAME, E.IACTNAME, C.IACTNAME as preact, E.IHEADTAILFLAG, E.IOKFILEABSOLUTEPATH, E.ICHILDPRONAME, E.IACTDESCRIPTION, E.IOUTPUTPARAM, E.ISHELLHOUSE, E.ISHELLABSOLUTEPATH, E.ILASTLINE, E.IOKFILEFINDWEEK, E.IFLAG,  E.IAGENTSOURCEGROUP, E.ICHECKAGENTGROUP, E.APTGROUPNAME, E.APTFILENAME, E.ISDB2, E.DB2IP, E.APTRESGROUPNAME ,E.ISYSTEM,E.IREDO,E.IWEIGHTS,E.IPRIORITY,E.ISAGENTGROUP,E.IDELAYTIME,E.IBRANCHCONDITION,E.IRETRYNUM, E.ICALENDNAME, E.IRETRYTIME,E.IRETRYENDTIME,E.IAUTOSKIP,E.IDELAYWARNNING,E.DAYS,E.LOGIC,E.WDAYS,E.IACTNO FROM IEAI_EXCELMODEL E  left join IEAI_ACTPRE A on  A.IPREACTNAME = E.Iactname AND A.IPROJECTNAME=E.IMAINPRONAME left join IEAI_EXCELMODEL C  on A.IOPERATIONID=C.IOPERATIONID) AE where   AE.IMAINPRONAME=?  OR AE.ICHILDPRONAME=?   order by AE.IOPERATIONID";
//            succsql = " SELECT distinct AE.IOPERATIONID, AE.IMAINPRONAME, AE.IMAINLINENAME, AE.IACTNAME,AE.succact,AE.IHEADTAILFLAG,AE.IOKFILEABSOLUTEPATH,AE.ICHILDPRONAME,AE.IACTDESCRIPTION,AE.IOUTPUTPARAM,AE.ISHELLHOUSE,AE.ISHELLABSOLUTEPATH, AE.ILASTLINE,AE.IOKFILEFINDWEEK,AE.IFLAG,AE.IAGENTSOURCEGROUP, AE.ICHECKAGENTGROUP , AE.APTGROUPNAME, AE.APTFILENAME, AE.ISDB2, AE.DB2IP, AE.APTRESGROUPNAME,AE.ISYSTEM,AE.IREDO,AE.IWEIGHTS,AE.IPRIORITY,case when exists (select iid from IEAI_TASK_DISABLE_CON C WHERE C.IPRONAME = AE.ICHILDPRONAME AND C.IFLOWNAME=AE.IACTNAME AND C.ISYSTEM=AE.ISYSTEM ) then  '是' ELSE  '无'  end  as IDISABLED ,AE.ISAGENTGROUP,AE.IDELAYTIME,AE.IBRANCHCONDITION,AE.IRETRYNUM, AE.ICALENDNAME, AE.IRETRYTIME, AE.IRETRYENDTIME ,AE.IAUTOSKIP, AE.IDELAYWARNNING,AE.DAYS,AE.LOGIC,AE.WDAYS,AE.IACTNO  from (SELECT distinct E.Ioperationid, E.IMAINPRONAME, E.IMAINLINENAME, E.IACTNAME,C.IACTNAME as succact, E.IHEADTAILFLAG, E.IOKFILEABSOLUTEPATH, E.ICHILDPRONAME, E.IACTDESCRIPTION, E.IOUTPUTPARAM, E.ISHELLHOUSE, E.ISHELLABSOLUTEPATH, E.ILASTLINE, E.IOKFILEFINDWEEK, E.IFLAG,  E.IAGENTSOURCEGROUP, E.ICHECKAGENTGROUP, E.APTGROUPNAME, E.APTFILENAME, E.ISDB2, E.DB2IP, E.APTRESGROUPNAME,E.ISYSTEM,E.IREDO,E.IWEIGHTS,E.IPRIORITY,E.ISAGENTGROUP,E.IDELAYTIME,E.IBRANCHCONDITION,E.IRETRYNUM,E.ICALENDNAME,E.IRETRYTIME,E.IRETRYENDTIME,E.IAUTOSKIP,E.IDELAYWARNNING,E.DAYS,E.LOGIC,E.WDAYS,E.IACTNO FROM IEAI_EXCELMODEL E  left join IEAI_ACTSUCC A on  A.Isuccactname = E.Iactname AND A.IPROJECTNAME=E.IMAINPRONAME left join IEAI_EXCELMODEL C  on A.IOPERATIONID=C.IOPERATIONID) AE where   AE.IMAINPRONAME=?  OR AE.ICHILDPRONAME=?   order by AE.IOPERATIONID";


        }else if(Environment.getInstance().getGYBankSwitch()){
            presql = " SELECT distinct AE.IOPERATIONID, AE.IMAINPRONAME, AE.IMAINLINENAME, AE.IACTNAME,AE.preact,AE.IHEADTAILFLAG,AE.IOKFILEABSOLUTEPATH,AE.ICHILDPRONAME,AE.IACTDESCRIPTION,AE.IOUTPUTPARAM,AE.ISHELLHOUSE,AE.ISHELLABSOLUTEPATH, AE.ILASTLINE,AE.IOKFILEFINDWEEK,AE.IFLAG,AE.IAGENTSOURCEGROUP, AE.ICHECKAGENTGROUP ,AE.APTGROUPNAME, AE.APTFILENAME, AE.ISDB2, AE.DB2IP, AE.APTRESGROUPNAME, AE.ISYSTEM,AE.IREDO,AE.IWEIGHTS,AE.IPRIORITY,case when exists (select iid from IEAI_TASK_DISABLE_CON C WHERE C.IPRONAME = AE.ICHILDPRONAME AND C.IFLOWNAME=AE.IACTNAME AND C.ISYSTEM=AE.ISYSTEM ) then  '是' ELSE  '无'  end  as IDISABLED ,AE.ISAGENTGROUP,AE.IDELAYTIME,AE.IBRANCHCONDITION,AE.IRETRYNUM, AE.ICALENDNAME, AE.IRETRYTIME, AE.IRETRYENDTIME ,AE.IAUTOSKIP, AE.IDELAYWARNNING,AE.DAYS,AE.LOGIC,AE.WDAYS,AE.IMONTH,AE.PERFORMUSER,AE.VIRTUALNAME,AE.JOBLIST,AE.SEQUENCENUMBER,AE.IENDTAG,AE.IUSERTASK,AE.IACTNO ,AE.IJOBTYPE from (SELECT distinct E.Ioperationid, E.IMAINPRONAME, E.IMAINLINENAME, E.IACTNAME, C.IACTNO as preact, E.IHEADTAILFLAG, E.IOKFILEABSOLUTEPATH, E.ICHILDPRONAME, E.IACTDESCRIPTION, E.IOUTPUTPARAM, E.ISHELLHOUSE, E.ISHELLABSOLUTEPATH, E.ILASTLINE, E.IOKFILEFINDWEEK, E.IFLAG,  E.IAGENTSOURCEGROUP, E.ICHECKAGENTGROUP, E.APTGROUPNAME, E.APTFILENAME, E.ISDB2, E.DB2IP, E.APTRESGROUPNAME ,E.ISYSTEM,E.IREDO,E.IWEIGHTS,E.IPRIORITY,E.ISAGENTGROUP,E.IDELAYTIME,E.IBRANCHCONDITION,E.IRETRYNUM, E.ICALENDNAME, E.IRETRYTIME,E.IRETRYENDTIME,E.IAUTOSKIP,E.IDELAYWARNNING,E.DAYS,E.LOGIC,E.WDAYS,E.IMONTH,E.PERFORMUSER,E.VIRTUALNAME,E.JOBLIST,E.SEQUENCENUMBER,E.IENDTAG,E.IUSERTASK,E.IACTNO ,E.IJOBTYPEFROM IEAI_EXCELMODEL E  left join IEAI_ACTPRE A on  A.IPREACTNAME = E.Iactname AND A.IPROJECTNAME=E.IMAINPRONAME left join IEAI_EXCELMODEL C  on A.IOPERATIONID=C.IOPERATIONID  WHERE ( E.IMAINPRONAME=?  OR E.ICHILDPRONAME=? ) and ( C.IMAINPRONAME=?  OR C.ICHILDPRONAME=? )) AE   order by AE.IOPERATIONID";
            succsql = " SELECT distinct AE.IOPERATIONID, AE.IMAINPRONAME, AE.IMAINLINENAME, AE.IACTNAME,AE.succact,AE.IHEADTAILFLAG,AE.IOKFILEABSOLUTEPATH,AE.ICHILDPRONAME,AE.IACTDESCRIPTION,AE.IOUTPUTPARAM,AE.ISHELLHOUSE,AE.ISHELLABSOLUTEPATH, AE.ILASTLINE,AE.IOKFILEFINDWEEK,AE.IFLAG,AE.IAGENTSOURCEGROUP, AE.ICHECKAGENTGROUP , AE.APTGROUPNAME, AE.APTFILENAME, AE.ISDB2, AE.DB2IP, AE.APTRESGROUPNAME,AE.ISYSTEM,AE.IREDO,AE.IWEIGHTS,AE.IPRIORITY,case when exists (select iid from IEAI_TASK_DISABLE_CON C WHERE C.IPRONAME = AE.ICHILDPRONAME AND C.IFLOWNAME=AE.IACTNAME AND C.ISYSTEM=AE.ISYSTEM ) then  '是' ELSE  '无'  end  as IDISABLED ,AE.ISAGENTGROUP,AE.IDELAYTIME,AE.IBRANCHCONDITION,AE.IRETRYNUM, AE.ICALENDNAME, AE.IRETRYTIME, AE.IRETRYENDTIME ,AE.IAUTOSKIP, AE.IDELAYWARNNING,AE.DAYS,AE.LOGIC,AE.WDAYS,AE.IMONTH,AE.PERFORMUSER,AE.VIRTUALNAME,AE.JOBLIST,AE.SEQUENCENUMBER,AE.IENDTAG,AE.IUSERTASK,AE.IACTNO ,AE.IJOBTYPE from (SELECT distinct E.Ioperationid, E.IMAINPRONAME, E.IMAINLINENAME, E.IACTNAME,C.IACTNO as succact, E.IHEADTAILFLAG, E.IOKFILEABSOLUTEPATH, E.ICHILDPRONAME, E.IACTDESCRIPTION, E.IOUTPUTPARAM, E.ISHELLHOUSE, E.ISHELLABSOLUTEPATH, E.ILASTLINE, E.IOKFILEFINDWEEK, E.IFLAG,  E.IAGENTSOURCEGROUP, E.ICHECKAGENTGROUP, E.APTGROUPNAME, E.APTFILENAME, E.ISDB2, E.DB2IP, E.APTRESGROUPNAME,E.ISYSTEM,E.IREDO,E.IWEIGHTS,E.IPRIORITY,E.ISAGENTGROUP,E.IDELAYTIME,E.IBRANCHCONDITION,E.IRETRYNUM,E.ICALENDNAME,E.IRETRYTIME,E.IRETRYENDTIME,E.IAUTOSKIP,E.IDELAYWARNNING,E.DAYS,E.LOGIC,E.WDAYS,E.IMONTH,E.PERFORMUSER,E.VIRTUALNAME,E.JOBLIST,E.SEQUENCENUMBER,E.IENDTAG,E.IUSERTASK,E.IACTNO,E.IJOBTYPE FROM IEAI_EXCELMODEL E  left join IEAI_ACTSUCC A on  A.Isuccactname = E.Iactname AND A.IPROJECTNAME=E.IMAINPRONAME left join IEAI_EXCELMODEL C  on A.IOPERATIONID=C.IOPERATIONID  WHERE ( E.IMAINPRONAME=?  OR E.ICHILDPRONAME=? ) and ( C.IMAINPRONAME=?  OR C.ICHILDPRONAME=? )) AE    order by AE.IOPERATIONID";

        }else if(Environment.getInstance().getDGBankSwitch()) {
            presql = " SELECT distinct AE.IOPERATIONID, AE.IMAINPRONAME, AE.IMAINLINENAME, AE.IACTNAME,AE.preact,AE.IHEADTAILFLAG,AE.IOKFILEABSOLUTEPATH,AE.ICHILDPRONAME,AE.IACTDESCRIPTION,AE.IOUTPUTPARAM,AE.ISHELLHOUSE,AE.ISHELLABSOLUTEPATH, AE.ILASTLINE,AE.IOKFILEFINDWEEK,AE.IFLAG,AE.IAGENTSOURCEGROUP, AE.ICHECKAGENTGROUP ,AE.APTGROUPNAME, AE.APTFILENAME, AE.ISDB2, AE.DB2IP, AE.APTRESGROUPNAME, AE.ISYSTEM,AE.IREDO,AE.IWEIGHTS,AE.IPRIORITY,case when exists (select iid from IEAI_TASK_DISABLE_CON C WHERE C.IPRONAME = AE.ICHILDPRONAME AND C.IFLOWNAME=AE.IACTNAME AND C.ISYSTEM=AE.ISYSTEM ) then  '是' ELSE  '无'  end  as IDISABLED ,AE.ISAGENTGROUP,AE.IDELAYTIME,AE.IBRANCHCONDITION,AE.IRETRYNUM, AE.ICALENDNAME, AE.IRETRYTIME, AE.IRETRYENDTIME ,AE.IAUTOSKIP, AE.IDELAYWARNNING,AE.DAYS,AE.LOGIC,AE.WDAYS,AE.IMONTH,AE.PERFORMUSER,AE.VIRTUALNAME,AE.JOBLIST,AE.SEQUENCENUMBER,AE.IENDTAG,AE.IUSERTASK,AE.IACTPARAMS ,AE.IJOBTYPE from (SELECT distinct E.Ioperationid, E.IMAINPRONAME, E.IMAINLINENAME, E.IACTNAME, A.Ioperationid as preact, E.IHEADTAILFLAG, E.IOKFILEABSOLUTEPATH, E.ICHILDPRONAME, E.IACTDESCRIPTION, E.IOUTPUTPARAM, E.ISHELLHOUSE, E.ISHELLABSOLUTEPATH, E.ILASTLINE, E.IOKFILEFINDWEEK, E.IFLAG,  E.IAGENTSOURCEGROUP, E.ICHECKAGENTGROUP, E.APTGROUPNAME, E.APTFILENAME, E.ISDB2, E.DB2IP, E.APTRESGROUPNAME ,E.ISYSTEM,E.IREDO,E.IWEIGHTS,E.IPRIORITY,E.ISAGENTGROUP,E.IDELAYTIME,E.IBRANCHCONDITION,E.IRETRYNUM, E.ICALENDNAME, E.IRETRYTIME,E.IRETRYENDTIME,E.IAUTOSKIP,E.IDELAYWARNNING,E.DAYS,E.LOGIC,E.WDAYS,E.IMONTH,E.PERFORMUSER,E.VIRTUALNAME,E.JOBLIST,E.SEQUENCENUMBER,E.IENDTAG,E.IUSERTASK,E.IACTPARAMS,E.IJOBTYPE  FROM IEAI_EXCELMODEL E  left join IEAI_ACTPRE A  on  A.IPREACTNAME = E.Iactname AND A.IPROJECTNAME=E.IMAINPRONAME WHERE ( E.IMAINPRONAME=?  OR E.ICHILDPRONAME=? ) ) AE where   AE.preact in (select ia.IOPERATIONID from  ieai_excelmodel ia where  ia.IMAINPRONAME=? or ia.ICHILDPRONAME=? )  order by AE.IOPERATIONID";
            succsql = " SELECT distinct AE.IOPERATIONID, AE.IMAINPRONAME, AE.IMAINLINENAME, AE.IACTNAME,AE.succact,AE.IHEADTAILFLAG,AE.IOKFILEABSOLUTEPATH,AE.ICHILDPRONAME,AE.IACTDESCRIPTION,AE.IOUTPUTPARAM,AE.ISHELLHOUSE,AE.ISHELLABSOLUTEPATH, AE.ILASTLINE,AE.IOKFILEFINDWEEK,AE.IFLAG,AE.IAGENTSOURCEGROUP, AE.ICHECKAGENTGROUP , AE.APTGROUPNAME, AE.APTFILENAME, AE.ISDB2, AE.DB2IP, AE.APTRESGROUPNAME,AE.ISYSTEM,AE.IREDO,AE.IWEIGHTS,AE.IPRIORITY,case when exists (select iid from IEAI_TASK_DISABLE_CON C WHERE C.IPRONAME = AE.ICHILDPRONAME AND C.IFLOWNAME=AE.IACTNAME AND C.ISYSTEM=AE.ISYSTEM ) then  '是' ELSE  '无'  end  as IDISABLED ,AE.ISAGENTGROUP,AE.IDELAYTIME,AE.IBRANCHCONDITION,AE.IRETRYNUM, AE.ICALENDNAME, AE.IRETRYTIME, AE.IRETRYENDTIME ,AE.IAUTOSKIP, AE.IDELAYWARNNING,AE.DAYS,AE.LOGIC,AE.WDAYS,AE.IMONTH,AE.PERFORMUSER,AE.VIRTUALNAME,AE.JOBLIST,AE.SEQUENCENUMBER,AE.IENDTAG,AE.IUSERTASK,AE.IACTPARAMS ,AE.IJOBTYPE from (SELECT distinct E.Ioperationid, E.IMAINPRONAME, E.IMAINLINENAME, E.IACTNAME, A.Ioperationid as succact, E.IHEADTAILFLAG, E.IOKFILEABSOLUTEPATH, E.ICHILDPRONAME, E.IACTDESCRIPTION, E.IOUTPUTPARAM, E.ISHELLHOUSE, E.ISHELLABSOLUTEPATH, E.ILASTLINE, E.IOKFILEFINDWEEK, E.IFLAG,  E.IAGENTSOURCEGROUP, E.ICHECKAGENTGROUP, E.APTGROUPNAME, E.APTFILENAME, E.ISDB2, E.DB2IP, E.APTRESGROUPNAME,E.ISYSTEM,E.IREDO,E.IWEIGHTS,E.IPRIORITY,E.ISAGENTGROUP,E.IDELAYTIME,E.IBRANCHCONDITION,E.IRETRYNUM,E.ICALENDNAME,E.IRETRYTIME,E.IRETRYENDTIME,E.IAUTOSKIP,E.IDELAYWARNNING,E.DAYS,E.LOGIC,E.WDAYS,E.IMONTH,E.PERFORMUSER,E.VIRTUALNAME,E.JOBLIST,E.SEQUENCENUMBER,E.IENDTAG,E.IUSERTASK,E.IACTPARAMS,E.IJOBTYPE  FROM IEAI_EXCELMODEL E  left join IEAI_ACTSUCC A  on  A.Isuccactname = E.Iactname AND A.IPROJECTNAME=E.IMAINPRONAME WHERE ( E.IMAINPRONAME=?  OR E.ICHILDPRONAME=? ) ) AE where   AE.succact in (select ia.IOPERATIONID from  ieai_excelmodel ia where  ia.IMAINPRONAME=? or ia.ICHILDPRONAME=? )    order by AE.IOPERATIONID";
        }else {
            presql = " SELECT distinct AE.IOPERATIONID, AE.IMAINPRONAME, AE.IMAINLINENAME, AE.IACTNAME,AE.preact,AE.IHEADTAILFLAG,AE.IOKFILEABSOLUTEPATH,AE.ICHILDPRONAME,AE.IACTDESCRIPTION,AE.IOUTPUTPARAM,AE.ISHELLHOUSE,AE.ISHELLABSOLUTEPATH, AE.ILASTLINE,AE.IOKFILEFINDWEEK,AE.IFLAG,AE.IAGENTSOURCEGROUP, AE.ICHECKAGENTGROUP ,AE.APTGROUPNAME, AE.APTFILENAME, AE.ISDB2, AE.DB2IP, AE.APTRESGROUPNAME, AE.ISYSTEM,AE.IREDO,AE.IWEIGHTS,AE.IPRIORITY,case when exists (select iid from IEAI_TASK_DISABLE_CON C WHERE C.IPRONAME = AE.ICHILDPRONAME AND C.IFLOWNAME=AE.IACTNAME AND C.ISYSTEM=AE.ISYSTEM ) then  '是' ELSE  '无'  end  as IDISABLED ,AE.ISAGENTGROUP,AE.IDELAYTIME,AE.IBRANCHCONDITION,AE.IRETRYNUM, AE.ICALENDNAME, AE.IRETRYTIME, AE.IRETRYENDTIME ,AE.IAUTOSKIP, AE.IDELAYWARNNING,AE.DAYS,AE.LOGIC,AE.WDAYS,AE.IMONTH,AE.PERFORMUSER,AE.VIRTUALNAME,AE.JOBLIST,AE.SEQUENCENUMBER,AE.IENDTAG,AE.IUSERTASK,AE.IJOBTYPE  from (SELECT distinct E.Ioperationid, E.IMAINPRONAME, E.IMAINLINENAME, E.IACTNAME, A.Ioperationid as preact, E.IHEADTAILFLAG, E.IOKFILEABSOLUTEPATH, E.ICHILDPRONAME, E.IACTDESCRIPTION, E.IOUTPUTPARAM, E.ISHELLHOUSE, E.ISHELLABSOLUTEPATH, E.ILASTLINE, E.IOKFILEFINDWEEK, E.IFLAG,  E.IAGENTSOURCEGROUP, E.ICHECKAGENTGROUP, E.APTGROUPNAME, E.APTFILENAME, E.ISDB2, E.DB2IP, E.APTRESGROUPNAME ,E.ISYSTEM,E.IREDO,E.IWEIGHTS,E.IPRIORITY,E.ISAGENTGROUP,E.IDELAYTIME,E.IBRANCHCONDITION,E.IRETRYNUM, E.ICALENDNAME, E.IRETRYTIME,E.IRETRYENDTIME,E.IAUTOSKIP,E.IDELAYWARNNING,E.DAYS,E.LOGIC,E.WDAYS,E.IMONTH,E.PERFORMUSER,E.VIRTUALNAME,E.JOBLIST,E.SEQUENCENUMBER,E.IENDTAG,E.IUSERTASK,E.IJOBTYPE  FROM IEAI_EXCELMODEL E  left join IEAI_ACTPRE A  on  A.IPREACTNAME = E.Iactname AND A.IPROJECTNAME=E.IMAINPRONAME WHERE ( E.IMAINPRONAME=?  OR E.ICHILDPRONAME=? ) ) AE where   AE.preact in (select ia.IOPERATIONID from  ieai_excelmodel ia where  ia.IMAINPRONAME=? or ia.ICHILDPRONAME=? )  order by AE.IOPERATIONID";
            succsql = " SELECT distinct AE.IOPERATIONID, AE.IMAINPRONAME, AE.IMAINLINENAME, AE.IACTNAME,AE.succact,AE.IHEADTAILFLAG,AE.IOKFILEABSOLUTEPATH,AE.ICHILDPRONAME,AE.IACTDESCRIPTION,AE.IOUTPUTPARAM,AE.ISHELLHOUSE,AE.ISHELLABSOLUTEPATH, AE.ILASTLINE,AE.IOKFILEFINDWEEK,AE.IFLAG,AE.IAGENTSOURCEGROUP, AE.ICHECKAGENTGROUP , AE.APTGROUPNAME, AE.APTFILENAME, AE.ISDB2, AE.DB2IP, AE.APTRESGROUPNAME,AE.ISYSTEM,AE.IREDO,AE.IWEIGHTS,AE.IPRIORITY,case when exists (select iid from IEAI_TASK_DISABLE_CON C WHERE C.IPRONAME = AE.ICHILDPRONAME AND C.IFLOWNAME=AE.IACTNAME AND C.ISYSTEM=AE.ISYSTEM ) then  '是' ELSE  '无'  end  as IDISABLED ,AE.ISAGENTGROUP,AE.IDELAYTIME,AE.IBRANCHCONDITION,AE.IRETRYNUM, AE.ICALENDNAME, AE.IRETRYTIME, AE.IRETRYENDTIME ,AE.IAUTOSKIP, AE.IDELAYWARNNING,AE.DAYS,AE.LOGIC,AE.WDAYS,AE.IMONTH,AE.PERFORMUSER,AE.VIRTUALNAME,AE.JOBLIST,AE.SEQUENCENUMBER,AE.IENDTAG,AE.IUSERTASK,AE.IJOBTYPE  from (SELECT distinct E.Ioperationid, E.IMAINPRONAME, E.IMAINLINENAME, E.IACTNAME, A.Ioperationid as succact, E.IHEADTAILFLAG, E.IOKFILEABSOLUTEPATH, E.ICHILDPRONAME, E.IACTDESCRIPTION, E.IOUTPUTPARAM, E.ISHELLHOUSE, E.ISHELLABSOLUTEPATH, E.ILASTLINE, E.IOKFILEFINDWEEK, E.IFLAG,  E.IAGENTSOURCEGROUP, E.ICHECKAGENTGROUP, E.APTGROUPNAME, E.APTFILENAME, E.ISDB2, E.DB2IP, E.APTRESGROUPNAME,E.ISYSTEM,E.IREDO,E.IWEIGHTS,E.IPRIORITY,E.ISAGENTGROUP,E.IDELAYTIME,E.IBRANCHCONDITION,E.IRETRYNUM,E.ICALENDNAME,E.IRETRYTIME,E.IRETRYENDTIME,E.IAUTOSKIP,E.IDELAYWARNNING,E.DAYS,E.LOGIC,E.WDAYS,E.IMONTH,E.PERFORMUSER,E.VIRTUALNAME,E.JOBLIST,E.SEQUENCENUMBER,E.IENDTAG,E.IUSERTASK,E.IJOBTYPE FROM IEAI_EXCELMODEL E  left join IEAI_ACTSUCC A  on  A.Isuccactname = E.Iactname AND A.IPROJECTNAME=E.IMAINPRONAME WHERE ( E.IMAINPRONAME=?  OR E.ICHILDPRONAME=? ) ) AE where   AE.succact in (select ia.IOPERATIONID from  ieai_excelmodel ia where  ia.IMAINPRONAME=? or ia.ICHILDPRONAME=? )    order by AE.IOPERATIONID";
        }
        try
        {
            con = DBManager.getInstance().getJdbcConnection(dbType);
            pres = con.prepareStatement(presql);

            pres.setString(1, prjName);
            pres.setString(2, prjName);
            pres.setString(3, prjName);
            pres.setString(4, prjName);

            rs = pres.executeQuery();
            ActRelationInfo workflows = null;
            String tmpId = "";
            perListMap( rs, preList, preMap);
            
            
            pres = null;
            rs = null;
            workflows = null;
            pres = con.prepareStatement(succsql);

            pres.setString(1, prjName);
            pres.setString(2, prjName);
            pres.setString(3, prjName);
            pres.setString(4, prjName);

            rs = pres.executeQuery();
            tmpId = "";
            while (rs.next())
            {
                if (null != workflows)
                {
                    if ((rs.getString(1)).equals(workflows.getId()))
                    {
                        if(rs.getString(5)!=null&&!"".equals(workflows.getSuccAct())){
                            workflows.setSuccAct(workflows.getSuccAct() + "," + rs.getString(5));
                            continue;
                        }

                    } else
                    {
                        succList.add(workflows);
                        succMap.put(tmpId, workflows);
                    }
                }
                workflows = new ActRelationInfo();
                workflows.setId(rs.getString(1));
                workflows.setProjectname(rs.getString(2));
                workflows.setMainLineName(rs.getString(3));
                workflows.setActName(rs.getString(4));
                workflows.setSuccAct(StringUtils.isNotEmpty(rs.getString(5)) ? rs.getString(5) : "");
                workflows.setHeadFlag(rs.getString(6));
                workflows.setOkFilePath(rs.getString(7));
                workflows.setChildproName(rs.getString(8));
                workflows.setActDesc(rs.getString(9));
                workflows.setOutPara(rs.getString(10));
                workflows.setShellHouse(rs.getString(11));
                workflows.setShellName(rs.getString(12));
                workflows.setLastLine(rs.getString(13));
                workflows.setOkFileWeek(rs.getString(14));
                workflows.setFlag(rs.getString(15));
                workflows.setAgentGroup(rs.getString(16));
                workflows.setAgentCheckGroup(rs.getString(17));
                workflows.setAptGroupName(rs.getString(18));
                workflows.setAptFileName(rs.getString(19));
                workflows.setIsdb2(rs.getString(20));
                workflows.setDb2ip(rs.getString(21));
                workflows.setAptResGroupName(rs.getString(22));
                workflows.setSystem(rs.getString(23));
                workflows.setRedo(rs.getString(24));
                workflows.setWeights(rs.getString(25));
                workflows.setPriority(rs.getString(26));
                workflows.setIsDisabled(rs.getString(27));
                workflows.setIsAgentGroup(rs.getInt(28) == 0 );
                workflows.setDelayTime(rs.getString(29));
                workflows.setBranchCondition(rs.getString(30));
                workflows.setReTryCount(rs.getInt(31) == 0 ? "" : rs.getString(31));
                workflows.setCalendName(rs.getString(32));
                workflows.setDays(rs.getString("DAYS"));
                workflows.setLogic(rs.getString("LOGIC"));
                workflows.setWdays(rs.getString("WDAYS"));
                workflows.setMonth(rs.getString("IMONTH"));
                workflows.setPerformUser(rs.getString("PERFORMUSER"));
                workflows.setVirtualName(rs.getString("VIRTUALNAME"));
                workflows.setJoblist(rs.getString("JOBLIST"));
                workflows.setSequenceNumber(rs.getString("SEQUENCENUMBER"));
                workflows.setJobtype(rs.getString("IJOBTYPE"));
                workflows.setEndTag(rs.getInt("IENDTAG")==1?"是":"否");
             /*   workflows.setOkFileName(rs.getString("OKFILENAME"));*/
                workflows.setUserTask(rs.getInt("IUSERTASK"));
                if(Environment.getInstance().getGYBankSwitch()){
                    workflows.setActNo(rs.getInt("IACTNO")+"");
                }else if(Environment.getInstance().getDGBankSwitch()){
                    workflows.setActParams(rs.getString("IACTPARAMS"));
                }

                workflows.setReTryTime(rs.getInt(33));
                workflows.setReTryEndTime(rs.getString(34) == null ? "" : rs.getString(34));
                workflows.setSkip(rs.getString(35) == null ? "" : rs.getString(35));
                workflows.setDelayWarnning(rs.getString(36) == null ? "" : rs.getString(36));

                if(Environment.getInstance().isBhODSUserSwitch()) {//渤海ODS作业执行用户开关
                    if(VirtualkeyMap.containsKey(workflows.getVirtualName())){//取所有虚拟组
                        String Virtualname= (String) VirtualkeyMap.get(workflows.getVirtualName());
                        if(workflows.getVirtualName()!=null){
                        VirtualkeyMap.put( workflows.getVirtualName(),Virtualname+","+workflows.getId());
                        }
                    }else {
                        if(workflows.getVirtualName()!=null){
                            VirtualkeyMap.put( workflows.getVirtualName(),workflows.getId());
                        }
                    }
                }
                tmpId = rs.getString(1);
            }
            if (workflows != null) {
                succList.add(workflows);
                succMap.put(tmpId, workflows);
            }
            successList( succList, preMap);
            Map headMap = new HashMap();
            if (preMap.size() == 0) {
                preMap = getMainHeatOnlyOne(prjName, dbType);
            } else {
                headMap = getMainHeatOnlyOne(prjName, dbType);
                Iterator iter = headMap.entrySet().iterator();
                while (iter.hasNext()) {
                    Entry entry = (Entry) iter.next();
                    if (!preMap.containsKey(entry.getKey())) {
                        ActRelationInfo bean = (ActRelationInfo) headMap.get(entry.getKey());
                        preMap.put(bean.getId(), bean);
                    }
                }
            }
            Iterator iter = preMap.entrySet().iterator();
            while (iter.hasNext())
            {
                Entry entry = (Entry) iter.next();
                list.add(entry.getValue());
            }
            if(Environment.getInstance().isBhODSUserSwitch()) {//渤海ODS作业执行用户开关
            returnMap.put("VirtualkeyMap", VirtualkeyMap);
            }
            returnMap.put("ActRelationInfo", list);
        } catch (SQLException e)
        {
            _log.error("Execution sql error at the method getRelationInfo of TaskDownloadManager ", e);
            throw new RepositoryException(ServerError.ERR_DB_EXEC);
        } catch (DBException e)
        {
            _log.error("Connect database error at the method getRelationInfo of TaskDownloadManager ", e);
            throw new RepositoryException(ServerError.ERR_DB_EXEC);
        } finally
        {
            DBResource.closeConn(con, rs, pres, method, _log);
        }
        return returnMap;
    }
    
    private void successList(List succList,Map preMap){
        if (null != succList && !succList.isEmpty())
        {
            for (int i = 0; i < succList.size(); i++)
            {
                ActRelationInfo bean = (ActRelationInfo) succList.get(i);
                if (bean != null) {
                    if (preMap.containsKey(bean.getId())) {
                        ActRelationInfo bean1 = (ActRelationInfo) preMap.get(bean.getId());
                        bean1.setSuccAct(bean.getSuccAct());
                        preMap.put(bean.getId(), bean1);
                    } else {
                        preMap.put(bean.getId(), bean);
                    }
                }
            }
        }
    }
    /**
     * 新末班强狂下获取活动前后继关系数据 ausor gang_guo 2014-04-10
     * 
     * @param prjName
     * @return
     * @throws RepositoryException
     */
    public Map getRelationInfoForOracleOrMysql ( String prjName, int dbType ) throws RepositoryException
    {
        Map returnMap = new HashMap();
        for (int m = 0;; m++)
        {
            try
            {
                 returnMap =   getRelationInfoForOracleOrMysqlList( prjName,  dbType);
               break;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(m, ex);
            }
        }
        return returnMap;
    }

    /**
     * 新末班强狂下获取活动前后继关系数据 ausor gang_guo 2014-04-10
     * 因db2数据库中查询字段太多，会超出页数限制，故改造方法。
     * @param prjName
     * <AUTHOR> by yue_sun on 2018-02-13 
     * @return
     * @throws RepositoryException
     */
    public List getRelationInfoForDb2 ( String prjName, int dbType ) throws RepositoryException
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        List preList = new ArrayList();
        List succList = new ArrayList();
        List list = new ArrayList();
        Map preMap = new HashMap();
        Map succMap = new HashMap();
        Connection con = null;
        PreparedStatement pres = null;
        ResultSet rs = null;
        String tmpId = "";
        ActRelationInfo workflows = null;
        for (int m = 0;; m++)
        {
            try
            {
                try
                {
                    con = DBManager.getInstance().getJdbcConnection(dbType);
                    List preIdsList = new ArrayList();
                    preIdsList = queryPreIdsForDb2(prjName, dbType);
                    for (int i = 0; i < preIdsList.size(); i++)
                    {
                        Map preIdsMap = (Map) preIdsList.get(i);

                        String presql = "SELECT distinct AE.IOPERATIONID,AE.IMAINPRONAME,AE.IMAINLINENAME,AE.IACTNAME,AE.IHEADTAILFLAG,AE.IOKFILEABSOLUTEPATH,AE.ICHILDPRONAME,AE.IACTDESCRIPTION,AE.IOUTPUTPARAM,AE.ISHELLHOUSE,AE.ISHELLABSOLUTEPATH,AE.ILASTLINE,AE.IOKFILEFINDWEEK,AE.IFLAG,AE.IAGENTSOURCEGROUP,AE.ICHECKAGENTGROUP,AE.APTGROUPNAME,AE.APTFILENAME,AE.ICALENDNAME,AE.ISDB2,AE.DB2IP,AE.APTRESGROUPNAME,AE.ISYSTEM,AE.IREDO,AE.IWEIGHTS,AE.IPRIORITY,AE.ISAGENTGROUP,AE.IDELAYTIME,AE.IBRANCHCONDITION,AE.IRETRYNUM,AE.IRETRYTIME,AE.IRETRYENDTIME,case when exists (select iid from IEAI_TASK_DISABLE_CON C WHERE C.IPRONAME = AE.ICHILDPRONAME AND C.IFLOWNAME = AE.IACTNAME AND C.ISYSTEM = AE.ISYSTEM) then '是' ELSE '无' end as IDISABLED,AE.IAUTOSKIP,AE.IDELAYWARNNING,AE.DAYS,AE.LOGIC,AE.WDAYS,AE.IMONTH,AE.IUSERTASK from IEAI_EXCELMODEL AE where AE.Ioperationid = ? ";

                        pres = null;
                        rs = null;
                        pres = con.prepareStatement(presql);
                        pres.setLong(1, Long.parseLong(preIdsMap.get(IOPERATIONID1).toString()));
                        rs = pres.executeQuery();
                        while (rs.next())
                        {
                            if (null != workflows)
                            {
                                if ((rs.getString(IOPERATIONID)).equals(workflows.getId()))
                                {
                                    workflows.setPreAct(workflows.getPreAct() + "," +preIdsMap.get("preact").toString());
                                    continue;
                                } else
                                {
                                    preList.add(workflows);
                                    preMap.put(tmpId, workflows);
                                }
                            }
                            workflows = new ActRelationInfo();
                            workflows.setId(rs.getString(IOPERATIONID));
                            workflows.setProjectname(rs.getString("IMAINPRONAME"));
                            workflows.setMainLineName(rs.getString("IMAINLINENAME"));
                            workflows.setActName(rs.getString("IACTNAME"));
                            workflows.setPreAct(preIdsMap.get(PREACT) == null ? "" : preIdsMap.get(PREACT)
                                    .toString());
                            workflows.setHeadFlag(rs.getString("IHEADTAILFLAG"));
                            workflows.setOkFilePath(rs.getString("IOKFILEABSOLUTEPATH"));
                            workflows.setChildproName(rs.getString("ICHILDPRONAME"));
                            workflows.setActDesc(rs.getString("IACTDESCRIPTION"));
                            workflows.setOutPara(rs.getString("IOUTPUTPARAM"));
                            workflows.setShellHouse(rs.getString("ISHELLHOUSE"));
                            workflows.setShellName(rs.getString("ISHELLABSOLUTEPATH"));
                            workflows.setLastLine(rs.getString("ILASTLINE"));
                            workflows.setOkFileWeek(rs.getString("IOKFILEFINDWEEK"));
                            workflows.setFlag(rs.getString("IFLAG"));
                            workflows.setAgentGroup(rs.getString("IAGENTSOURCEGROUP"));
                            workflows.setAgentCheckGroup(rs.getString("ICHECKAGENTGROUP"));
                            workflows.setAptGroupName(rs.getString("APTGROUPNAME"));
                            workflows.setAptFileName(rs.getString("APTFILENAME"));
                            workflows.setIsdb2(rs.getString("ISDB2"));
                            workflows.setDb2ip(rs.getString("DB2IP"));
                            workflows.setAptResGroupName(rs.getString("APTRESGROUPNAME"));
                            workflows.setSystem(rs.getString("ISYSTEM"));
                            workflows.setRedo(rs.getString("IREDO"));
                            workflows.setWeights(rs.getString("IWEIGHTS"));
                            workflows.setPriority(rs.getString("IPRIORITY"));
                            workflows.setIsDisabled(rs.getString("IDISABLED"));
                            workflows.setIsAgentGroup(rs.getInt("ISAGENTGROUP") == 0 );
                            workflows.setDays(rs.getString("DAYS")== null ? "" : rs.getString("DAYS"));
                            workflows.setLogic(rs.getString("LOGIC")== null ? "" : rs.getString("LOGIC"));
                            workflows.setWdays(rs.getString("WDAYS")== null ? "" : rs.getString("WDAYS"));
                            workflows.setMonth(rs.getString("IMONTH")== null ? "" : rs.getString("IMONTH"));
                            workflows.setUserTask(rs.getInt("IUSERTASK"));
                            workflows
                                    .setDelayTime(rs.getString(IDELAYTIME) == null ? "" : rs.getString(IDELAYTIME));
                            workflows.setBranchCondition(rs.getString(IBRANCHCONDITION) == null ? "" : rs
                                    .getString(IBRANCHCONDITION));
                            workflows.setReTryCount(rs.getInt(IRETRYNUM) == 0 ? "" : rs.getString(IRETRYNUM));
                            workflows.setCalendName(rs.getString(ICALENDNAME) == null ? "" : rs
                                    .getString(ICALENDNAME));
                            workflows.setReTryTime(rs.getInt("IRETRYTIME"));
                            workflows.setReTryEndTime(rs.getString("IRETRYENDTIME") == null ? "" : rs
                                    .getString("IRETRYENDTIME"));
                            workflows.setSkip(rs.getString("IAUTOSKIP") == null ? "" : rs.getString("IAUTOSKIP"));
                            workflows.setDelayWarnning(rs.getString("IDELAYWARNNING") == null ? "" : rs
                                    .getString("IDELAYWARNNING"));
                            tmpId = rs.getString(1);
                        }
                        preList.add(workflows);
                        preMap.put(tmpId, workflows);
                        DBResource.closePSRS(rs, pres, method, _log);
                    }

                    List succIdsList = new ArrayList();
                    succIdsList = querySuccIdsForDb2(prjName, dbType);
                    workflows = null;
                    tmpId = "";
                    for (int i = 0; i < succIdsList.size(); i++)
                    {
                        Map succIdsMap = (Map) succIdsList.get(i);
                        String succsql = " SELECT distinct AE.IOPERATIONID, AE.IMAINPRONAME, AE.IMAINLINENAME, AE.IACTNAME,AE.IHEADTAILFLAG,AE.IOKFILEABSOLUTEPATH,AE.ICHILDPRONAME,AE.IACTDESCRIPTION,AE.IOUTPUTPARAM,AE.ISHELLHOUSE,AE.ISHELLABSOLUTEPATH, AE.ILASTLINE,AE.IOKFILEFINDWEEK,AE.IFLAG,AE.IAGENTSOURCEGROUP, AE.ICHECKAGENTGROUP , AE.APTGROUPNAME, AE.APTFILENAME, AE.ISDB2, AE.DB2IP, AE.APTRESGROUPNAME,AE.ISYSTEM,AE.IREDO,AE.IWEIGHTS,AE.IPRIORITY,case when exists (select iid from IEAI_TASK_DISABLE_CON C WHERE C.IPRONAME = AE.ICHILDPRONAME AND C.IFLOWNAME=AE.IACTNAME AND C.ISYSTEM=AE.ISYSTEM ) then  '是' ELSE  '无'  end  as IDISABLED ,AE.ISAGENTGROUP,AE.IDELAYTIME,AE.IBRANCHCONDITION,AE.IRETRYNUM, AE.ICALENDNAME, AE.IRETRYTIME, AE.IRETRYENDTIME ,AE.IAUTOSKIP, AE.IDELAYWARNNING,AE.DAYS,AE.LOGIC,AE.WDAYS,AE.IMONTH ,AE.IUSERTASK from IEAI_EXCELMODEL AE  where AE.Ioperationid = ? ";
                        pres = null;
                        rs = null;
                        pres = con.prepareStatement(succsql);
                        pres.setLong(1, Long.parseLong(succIdsMap.get(IOPERATIONID1).toString()));
                        rs = pres.executeQuery();
                        while (rs.next())
                        {
                            if (null != workflows)
                            {
                                if ((rs.getString(IOPERATIONID)).equals(workflows.getId()))
                                {
                                    workflows.setSuccAct(workflows.getSuccAct() + "," + succIdsMap.get("succact") );
                                    continue;
                                } else
                                {
                                    succList.add(workflows);
                                    succMap.put(tmpId, workflows);
                                }
                            }
                            workflows = new ActRelationInfo();
                            workflows.setId(rs.getString(IOPERATIONID));
                            workflows.setProjectname(rs.getString("IMAINPRONAME"));
                            workflows.setMainLineName(rs.getString("IMAINLINENAME"));
                            workflows.setActName(rs.getString("IACTNAME"));
                            workflows.setSuccAct(succIdsMap.get(SUCCACT) == null ? "" : succIdsMap.get(SUCCACT)
                                    .toString());
                            workflows.setHeadFlag(rs.getString("IHEADTAILFLAG"));
                            workflows.setOkFilePath(rs.getString("IOKFILEABSOLUTEPATH"));
                            workflows.setChildproName(rs.getString("ICHILDPRONAME"));
                            workflows.setActDesc(rs.getString("IACTDESCRIPTION"));
                            workflows.setOutPara(rs.getString("IOUTPUTPARAM"));
                            workflows.setShellHouse(rs.getString("ISHELLHOUSE"));
                            workflows.setShellName(rs.getString("ISHELLABSOLUTEPATH"));
                            workflows.setLastLine(rs.getString("ILASTLINE"));
                            workflows.setOkFileWeek(rs.getString("IOKFILEFINDWEEK"));
                            workflows.setFlag(rs.getString("IFLAG"));
                            workflows.setAgentGroup(rs.getString("IAGENTSOURCEGROUP"));
                            workflows.setAgentCheckGroup(rs.getString("ICHECKAGENTGROUP"));
                            workflows.setAptGroupName(rs.getString("APTGROUPNAME"));
                            workflows.setAptFileName(rs.getString("APTFILENAME"));
                            workflows.setIsdb2(rs.getString("ISDB2"));
                            workflows.setDb2ip(rs.getString("DB2IP"));
                            workflows.setAptResGroupName(rs.getString("APTRESGROUPNAME"));
                            workflows.setSystem(rs.getString("ISYSTEM"));
                            workflows.setRedo(rs.getString("IREDO"));
                            workflows.setWeights(rs.getString("IWEIGHTS"));
                            workflows.setPriority(rs.getString("IPRIORITY"));
                            workflows.setIsDisabled(rs.getString("IDISABLED"));
                            workflows.setIsAgentGroup(rs.getInt("ISAGENTGROUP") == 0 );
                            workflows.setDays(rs.getString("DAYS")== null ? "" : rs.getString("DAYS"));
                            workflows.setLogic(rs.getString("LOGIC")== null ? "" : rs.getString("LOGIC"));
                            workflows.setWdays(rs.getString("WDAYS")== null ? "" : rs.getString("WDAYS"));
                            workflows.setMonth(rs.getString("IMONTH")== null ? "" : rs.getString("IMONTH"));
                            workflows.setUserTask(rs.getInt("IUSERTASK"));
                            workflows.setDelayTime(rs.getString(IDELAYTIME));
                            workflows.setBranchCondition(rs.getString(IBRANCHCONDITION));
                            workflows.setReTryCount(rs.getInt(IRETRYNUM) == 0 ? "" : rs.getString(IRETRYNUM));
                            workflows.setCalendName(rs.getString(ICALENDNAME));
                            tmpId = rs.getString(1);
                        }
                        succList.add(workflows);
                        succMap.put(tmpId, workflows);
                        DBResource.closePSRS(rs, pres, method, _log);
                    }

                    if (null != succList && !succList.isEmpty())
                    {
                        for (int i = 0; i < succList.size(); i++)
                        {
                            ActRelationInfo bean = (ActRelationInfo) succList.get(i);
                            if (preMap.containsKey(bean.getId()))
                            {
                                ActRelationInfo bean1 = (ActRelationInfo) preMap.get(bean.getId());
                                bean1.setSuccAct(bean.getSuccAct());
                                preMap.put(bean.getId(), bean1);
                            } else
                            {
                                preMap.put(bean.getId(), bean);
                            }
                        }
                    }
                    Iterator iter = preMap.entrySet().iterator();
                    while (iter.hasNext())
                    {
                        Entry entry = (Entry) iter.next();
                        list.add(entry.getValue());
                    }
                    break;
                } catch (SQLException e)
                {
                    _log.error("Execution sql error at the method getRelationInfo of TaskDownloadManager ", e);
                    throw new RepositoryException(ServerError.ERR_DB_EXEC);
                } catch (DBException e)
                {
                    _log.error("Connect database error at the method getRelationInfo of TaskDownloadManager ", e);
                    throw new RepositoryException(ServerError.ERR_DB_EXEC);
                } finally
                {
                    DBResource.closeConn(con, rs, pres, method, _log);
                }
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(m, ex);
            }
        }
        return list;
    }

    /**   
     * @Title: queryPreOrSuccIdsForDb2   
     * @Description: 特殊处理查询IEAI_ACTPRE表中的preID
     * @param tableName
     * @param prjName
     * @param dbType
     * @return
     * @throws RepositoryException      
     * @author: yue_sun 
     * @date:   2018年2月13日 上午9:32:18   
     */
    public List queryPreIdsForDb2 ( String prjName, int dbType ) throws RepositoryException
    {
        List reList = new ArrayList();
        Connection con = null;
        PreparedStatement pres = null;
        ResultSet rs = null;
        for (int m = 0;; m++)
        {
            try
            {
                String sql = "";
                sql = " SELECT distinct E.Ioperationid, A.Ioperationid as preact, row_number()over(partition by E.Ioperationid order by A.Ioperationid desc) as row "
                        + " FROM IEAI_EXCELMODEL E left join IEAI_ACTPRE A on A.IPREACTNAME = E.Iactname AND A.IPROJECTNAME = E.IMAINPRONAME WHERE E.IMAINPRONAME = ? OR E.ICHILDPRONAME = ? order by Ioperationid";
                try
                {
                    con = DBManager.getInstance().getJdbcConnection(dbType);
                    pres = con.prepareStatement(sql);
                    pres.setString(1, prjName);
                    pres.setString(2, prjName);
                    rs = pres.executeQuery();
                    while (rs.next())
                    {
                        Map map = new HashMap();
                        map.put(IOPERATIONID1, rs.getString(IOPERATIONID1));
                        map.put(PREACT, rs.getString(PREACT));
                        reList.add(map);
                    }
                    break;
                } catch (SQLException e)
                {
                    _log.error("Execution sql error at the method queryPreIdsForDb2 of TaskDownloadManager ", e);
                    throw new RepositoryException(ServerError.ERR_DB_EXEC);
                } catch (DBException e)
                {
                    _log.error("Connect database error at the method queryPreIdsForDb2 of TaskDownloadManager ", e);
                    throw new RepositoryException(ServerError.ERR_DB_EXEC);
                } finally
                {
                    DBResource.closeConn(con, rs, pres, "queryPreIdsForDb2", _log);
                }
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(m, ex);
            }
        }
        return reList;
    }

    /**   
     * @Title: queryPreOrSuccIdsForDb2   
     * @Description: 特殊处理查询IEAI_ACTSUCC表中的succID
     * @param tableName
     * @param prjName
     * @param dbType
     * @return
     * @throws RepositoryException      
     * @author: yue_sun 
     * @date:   2018年2月13日 上午9:32:18   
     */
    public List querySuccIdsForDb2 ( String prjName, int dbType ) throws RepositoryException
    {
        List reList = new ArrayList();
        Connection con = null;
        PreparedStatement pres = null;
        ResultSet rs = null;
        for (int m = 0;; m++)
        {
            try
            {
                String sql = "";
                sql = " SELECT distinct E.Ioperationid, A.Ioperationid as succact, row_number()over(partition by E.Ioperationid order by A.Ioperationid desc) as row "
                        + " FROM IEAI_EXCELMODEL E left join IEAI_ACTSUCC A on  A.Isuccactname = E.Iactname AND A.IPROJECTNAME = E.IMAINPRONAME WHERE E.IMAINPRONAME = ? OR E.ICHILDPRONAME = ? order by Ioperationid";
                try
                {
                    con = DBManager.getInstance().getJdbcConnection(dbType);
                    pres = con.prepareStatement(sql);
                    pres.setString(1, prjName);
                    pres.setString(2, prjName);
                    rs = pres.executeQuery();
                    while (rs.next())
                    {
                        Map map = new HashMap();
                        map.put(IOPERATIONID1, rs.getString(IOPERATIONID1));
                        map.put(SUCCACT, rs.getString(SUCCACT));
                        reList.add(map);
                    }
                    break;
                } catch (SQLException e)
                {
                    _log.error("Execution sql error at the method querySuccIdsForDb2 of TaskDownloadManager ", e);
                    throw new RepositoryException(ServerError.ERR_DB_EXEC);
                } catch (DBException e)
                {
                    _log.error("Connect database error at the method querySuccIdsForDb2 of TaskDownloadManager ", e);
                    throw new RepositoryException(ServerError.ERR_DB_EXEC);
                } finally
                {
                    DBResource.closeConn(con, rs, pres, "querySuccIdsForDb2", _log);
                }
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(m, ex);
            }
        }
        return reList;
    }

    /**
     * sm导出时，判断该工程是否为excel模板上传方式 by gang_guo 2014.4.11
     * 
     * @param flowId
     * @param flowInsName
     * @param flag
     * @return
     * @throws RepositoryException
     */
    public boolean isRelation ( String prjName, int dbType ) throws RepositoryException
    {
        boolean flag = false;
        Connection con = null;
        PreparedStatement pres = null;
        ResultSet rs = null;
        for (int m = 0;; m++)
        {
            try
            {
                String sql = " SELECT count(*) FROM IEAI_EXCELMODEL WHERE IMAINPRONAME = ? or ICHILDPRONAME = ?";
                try
                {
                    con = DBManager.getInstance().getJdbcConnection(dbType);
                    pres = con.prepareStatement(sql);
                    pres.setString(1, prjName);
                    pres.setString(2, prjName);
                    rs = pres.executeQuery();
                    while (rs.next())
                    {
                        if (rs.getInt(1) > 0)
                        {
                            flag = true;
                        }
                    }
                    break;
                } catch (SQLException e)
                {
                    _log.error("Execution sql error at the method isRelation of TaskDownloadManager ", e);
                    throw new RepositoryException(ServerError.ERR_DB_EXEC);
                } catch (DBException e)
                {
                    _log.error("Connect database error at the method isRelation of TaskDownloadManager ", e);
                    throw new RepositoryException(ServerError.ERR_DB_EXEC);
                } finally
                {
                    DBResource.closeConn(con, rs, pres, "isRelation", _log);
                }
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(m, ex);
            }
        }
        return flag;
    }







    /**
     * @title: getTaskVersionDownloadExcel
     * @description: TODO(【作业依赖关系】查询历史版本列表)
     * @author: lili_xing
     * @createDate: 2022/8/18
     * @param
     * @param prjName
     * @param page
     * @param limit
     * @throws
     */
    public Map<String, Object> getTaskVersionDownloadExcel(String prjName,
                                                           String page,
                                                           String limit) {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Map<String, Object> map = new HashMap<>();
        Connection conn = null;
        int count = 0;
        String sqlForDB="";
        String sqlForDB1="";
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);
            String sqlWhere = " where 1=1 ";
            /** 子系统名 */
            if (StringUtils.isNotBlank(prjName))
            {
                sqlWhere += " and IPRJNAME  = '" + prjName+"'";
            }

            long start = Long.parseLong(page);
            long tolimit = Long.parseLong(limit);
            long curpage = ((start - 1) * tolimit);
            long curpages = ((start - start) * tolimit);
            String sql = "SELECT * FROM IEAI_EXCELNODEL_VERSION "+sqlWhere+" ORDER BY IID DESC";
            count = SQLUtil.toCount(conn, sql);
            if (DBManager.Orcl_Faimily())
            {
                sqlForDB=SQLUtil.toORACLE(sql, String.valueOf(curpage), String.valueOf(tolimit));
                sqlForDB1=SQLUtil.toORACLE(sql, String.valueOf(curpages), String.valueOf(tolimit));
            }
            if (JudgeDB.IEAI_DB_TYPE == 2)
            {
                sqlForDB=SQLUtil.toDB2(sql, String.valueOf(curpage), String.valueOf(tolimit));
                sqlForDB1=SQLUtil.toDB2(sql, String.valueOf(curpages), String.valueOf(tolimit));
            }
            if (JudgeDB.IEAI_DB_TYPE == 3)
            {
                sqlForDB=SQLUtil.toMYSQLByLong(sql, curpage, tolimit);
                sqlForDB1=SQLUtil.toMYSQLByLong(sql, curpages, tolimit);
            }
            List<Map<String,Object>> list = getTaskVersionDownloadList(conn,sqlForDB);
            List<Map<String,Object>> lists = getTaskVersionDownloadList(conn,sqlForDB1);
            if(list.isEmpty() && !lists.isEmpty()){
                list = lists;
            }
            map.put(DATA_LIST, list);
            map.put(TOTAL, count);
            map.put(MESSAGE, RESULT_SUCCESS);
            map.put(SUCCESS, true);
        } catch (Exception e)
        {
            map.put(MESSAGE, RESULT_FAIL);
            map.put(SUCCESS, false);
            _log.error(method, e);
        } finally {
            DBResource.closeConnection(conn, method + " is error", _log);
        }
        return map;
    }



    /**
     * @title: getTaskVersionDownloadList
     * @description: TODO(【作业依赖关系】查询历史版本列表)
     * @author: lili_xing
     * @createDate: 2022/8/18
     * @param
     * @param con
     * @param sql
     * @throws
     */
    private List<Map<String,Object>> getTaskVersionDownloadList(Connection con ,String sql) throws RepositoryException {
        List<Map<String,Object>> dataList= new ArrayList<Map<String,Object>>();
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        for (int i = 0; ; i++) {
            try {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                try {
                    actStat = con.prepareStatement(sql);
                    actRS = actStat.executeQuery();
                    while (actRS.next()) {
                        Map<String,Object> objectMap = new HashMap<>();
                        objectMap.put("iid",actRS.getLong("IID"));
                        objectMap.put("prjName",actRS.getString("IPRJNAME"));
                        objectMap.put("version",actRS.getString("IVERSION"));
                        objectMap.put("shellCrust",actRS.getString("ISHELLCRUST"));
                        objectMap.put("IUPLOADUSER", actRS.getString("IUPLOADUSER"));
                        objectMap.put("IUPLOADTIME", actRS.getString("IUPLOADTIME"));
                        dataList.add(objectMap);
                    }
                } catch (SQLException e) {
                    _log.error("method: " + method, e);
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally {
                    DBResource.closePSRS(actRS, actStat, method, _log);
                }
                break;
            } catch (RepositoryException ex) {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
        return dataList;
    }


    /**
     * @title: getTaskVersionDownloadExcel
     * @description: TODO(【作业依赖关系】导出历史版本)
     * @author: lili_xing
     * @createDate: 2022/8/18
     * @param
     * @param key
     * @throws
     */
    public static byte[] getTaskVersionBinary ( Long key) throws RepositoryException {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        String sql = "SELECT IID,IVERSIONID,ICONTENT FROM IEAI_EXCELNODEL_VERSION_DETAIL WHERE IVERSIONID = ?";
        BinaryEntity blobEntity = null;
        ByteArrayOutputStream out = new ByteArrayOutputStream();

        PreparedStatement ps = null;
        ResultSet rs = null;
        Connection con = null;
        try
        {
            con = DBResource.getConnection(method, _log, Constants.IEAI_IEAI);
            ps = con.prepareStatement(sql);
            ps.setLong(1, key.longValue());
            rs = ps.executeQuery();

            if (rs.next())
            {
                blobEntity = new BinaryEntity();
                blobEntity.setId(new Long(rs.getLong("IID")));
                blobEntity.setNull(rs.getLong("IID") == 0 ? false : true);
                blobEntity.setContent(rs.getBlob("ICONTENT"));
                if (rs.getBlob("ICONTENT") == null)
                {
                    return new byte[0];
                }
            } else
            {
                return null;
            }

            InputStream in = null;
            try
            {
                in = blobEntity.getContent().getBinaryStream();
            } catch (SQLException e2)
            {
                _log.error("error while getting inputStream from a blob with it's id " + key, e2);
                throw new RepositoryException(ServerError.ERR_DB_ERROR);
            }
            byte buff[] = new byte[128];

            try
            {
                while (true)
                {
                    int c = in.read(buff);
                    if (c <= 0)
                    {
                        break;
                    }
                    out.write(buff, 0, c);
                }

            } catch (IOException e)
            {
                _log.error("error while reading from a blob with it's id " + key, e);
                throw new RepositoryException(ServerError.ERR_DB_ERROR);

            } finally
            {
                try
                {
                    in.close();
                    out.close();
                } catch (IOException e1)
                {
                    _log.error("error while closing stream", e1);
                    throw new RepositoryException(ServerError.ERR_DB_ERROR);
                }
            }
        } catch (SQLException e)
        {
            DBResource.rollback(con, ServerError.ERR_DB_INSERT, e,
                    Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        } finally
        {
            DBResource.closeConn(con,rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }

        byte[] result = out.toByteArray();
        return result;
    }
    private Map getMainHeatOnlyOne(String prjName, int dbType) throws RepositoryException{
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Map preMap = new HashMap();
        List preList = new ArrayList();
        Connection con = null;
        PreparedStatement pres = null;
        ResultSet rs = null;
        String presql="";
        if(Environment.getInstance().getGYBankSwitch()){
             presql = "SELECT distinct AE.IOPERATIONID, AE.IMAINPRONAME, AE.IMAINLINENAME, AE.IACTNAME,AE.preact,AE.IHEADTAILFLAG,AE.IOKFILEABSOLUTEPATH,AE.ICHILDPRONAME,AE.IACTDESCRIPTION,AE.IOUTPUTPARAM,AE.ISHELLHOUSE,AE.ISHELLABSOLUTEPATH, AE.ILASTLINE,AE.IOKFILEFINDWEEK,AE.IFLAG,AE.IAGENTSOURCEGROUP, AE.ICHECKAGENTGROUP ,AE.APTGROUPNAME, AE.APTFILENAME, AE.ISDB2, AE.DB2IP, AE.APTRESGROUPNAME, AE.ISYSTEM,AE.IREDO,AE.IWEIGHTS,AE.IPRIORITY,case when exists (select iid from IEAI_TASK_DISABLE_CON C WHERE C.IPRONAME = AE.ICHILDPRONAME AND C.IFLOWNAME=AE.IACTNAME AND C.ISYSTEM=AE.ISYSTEM ) then  '是' ELSE  '无'  end  as IDISABLED ,AE.ISAGENTGROUP,AE.IDELAYTIME,AE.IBRANCHCONDITION,AE.IRETRYNUM, AE.ICALENDNAME, AE.IRETRYTIME, AE.IRETRYENDTIME ,AE.IAUTOSKIP, AE.IDELAYWARNNING,AE.DAYS,AE.LOGIC,AE.WDAYS,AE.IMONTH,AE.PERFORMUSER,AE.VIRTUALNAME,AE.JOBLIST,AE.SEQUENCENUMBER,AE.IENDTAG,AE.IUSERTASK,AE.IACTNO,AE.IJOBTYPE   from (SELECT distinct E.Ioperationid, E.IMAINPRONAME, E.IMAINLINENAME, E.IACTNAME,C.IACTNAME as preact, E.IHEADTAILFLAG, E.IOKFILEABSOLUTEPATH, E.ICHILDPRONAME, E.IACTDESCRIPTION, E.IOUTPUTPARAM, E.ISHELLHOUSE, E.ISHELLABSOLUTEPATH, E.ILASTLINE, E.IOKFILEFINDWEEK, E.IFLAG,  E.IAGENTSOURCEGROUP, E.ICHECKAGENTGROUP, E.APTGROUPNAME, E.APTFILENAME, E.ISDB2, E.DB2IP, E.APTRESGROUPNAME ,E.ISYSTEM,E.IREDO,E.IWEIGHTS,E.IPRIORITY,E.ISAGENTGROUP,E.IDELAYTIME,E.IBRANCHCONDITION,E.IRETRYNUM, E.ICALENDNAME, E.IRETRYTIME,E.IRETRYENDTIME,E.IAUTOSKIP,E.IDELAYWARNNING,E.DAYS,E.LOGIC,E.WDAYS,E.IMONTH,E.PERFORMUSER,E.VIRTUALNAME,E.JOBLIST,E.SEQUENCENUMBER,E.IENDTAG,E.IUSERTASK,E.IACTNO,E.IJOBTYPE  FROM IEAI_EXCELMODEL E  left join IEAI_ACTPRE A on  A.IPREACTNAME = E.Iactname AND A.IPROJECTNAME=E.IMAINPRONAME left join IEAI_EXCELMODEL C  on A.IOPERATIONID=C.IOPERATIONID WHERE ( E.IMAINPRONAME=?  OR E.ICHILDPRONAME=? ) ) AE where  AE.IOPERATIONID in (select ia.IOPERATIONID from  ieai_excelmodel ia where  ia.IMAINPRONAME=? or ia.ICHILDPRONAME=? )  order by AE.IOPERATIONID";
        }else if(Environment.getInstance().getDGBankSwitch()){
            presql = "SELECT distinct AE.IOPERATIONID, AE.IMAINPRONAME, AE.IMAINLINENAME, AE.IACTNAME,AE.preact,AE.IHEADTAILFLAG,AE.IOKFILEABSOLUTEPATH,AE.ICHILDPRONAME,AE.IACTDESCRIPTION,AE.IOUTPUTPARAM,AE.ISHELLHOUSE,AE.ISHELLABSOLUTEPATH, AE.ILASTLINE,AE.IOKFILEFINDWEEK,AE.IFLAG,AE.IAGENTSOURCEGROUP, AE.ICHECKAGENTGROUP ,AE.APTGROUPNAME, AE.APTFILENAME, AE.ISDB2, AE.DB2IP, AE.APTRESGROUPNAME, AE.ISYSTEM,AE.IREDO,AE.IWEIGHTS,AE.IPRIORITY,case when exists (select iid from IEAI_TASK_DISABLE_CON C WHERE C.IPRONAME = AE.ICHILDPRONAME AND C.IFLOWNAME=AE.IACTNAME AND C.ISYSTEM=AE.ISYSTEM ) then  '是' ELSE  '无'  end  as IDISABLED ,AE.ISAGENTGROUP,AE.IDELAYTIME,AE.IBRANCHCONDITION,AE.IRETRYNUM, AE.ICALENDNAME, AE.IRETRYTIME, AE.IRETRYENDTIME ,AE.IAUTOSKIP, AE.IDELAYWARNNING,AE.DAYS,AE.LOGIC,AE.WDAYS,AE.IMONTH,AE.PERFORMUSER,AE.VIRTUALNAME,AE.JOBLIST,AE.SEQUENCENUMBER,AE.IENDTAG,AE.IUSERTASK,AE.IACTPARAMS,AE.IJOBTYPE   from (SELECT distinct E.Ioperationid, E.IMAINPRONAME, E.IMAINLINENAME, E.IACTNAME,C.IACTNAME as preact, E.IHEADTAILFLAG, E.IOKFILEABSOLUTEPATH, E.ICHILDPRONAME, E.IACTDESCRIPTION, E.IOUTPUTPARAM, E.ISHELLHOUSE, E.ISHELLABSOLUTEPATH, E.ILASTLINE, E.IOKFILEFINDWEEK, E.IFLAG,  E.IAGENTSOURCEGROUP, E.ICHECKAGENTGROUP, E.APTGROUPNAME, E.APTFILENAME, E.ISDB2, E.DB2IP, E.APTRESGROUPNAME ,E.ISYSTEM,E.IREDO,E.IWEIGHTS,E.IPRIORITY,E.ISAGENTGROUP,E.IDELAYTIME,E.IBRANCHCONDITION,E.IRETRYNUM, E.ICALENDNAME, E.IRETRYTIME,E.IRETRYENDTIME,E.IAUTOSKIP,E.IDELAYWARNNING,E.DAYS,E.LOGIC,E.WDAYS,E.IMONTH,E.PERFORMUSER,E.VIRTUALNAME,E.JOBLIST,E.SEQUENCENUMBER,E.IENDTAG,E.IUSERTASK,E.IACTPARAMS,E.IJOBTYPE,E.IJOBTYPE  FROM IEAI_EXCELMODEL E  left join IEAI_ACTPRE A on  A.IPREACTNAME = E.Iactname AND A.IPROJECTNAME=E.IMAINPRONAME left join IEAI_EXCELMODEL C  on A.IOPERATIONID=C.IOPERATIONID WHERE ( E.IMAINPRONAME=?  OR E.ICHILDPRONAME=? ) ) AE where  AE.IOPERATIONID in (select ia.IOPERATIONID from  ieai_excelmodel ia where  ia.IMAINPRONAME=? or ia.ICHILDPRONAME=? )  order by AE.IOPERATIONID";
        }else{
             presql = "SELECT distinct AE.IOPERATIONID, AE.IMAINPRONAME, AE.IMAINLINENAME, AE.IACTNAME,AE.preact,AE.IHEADTAILFLAG,AE.IOKFILEABSOLUTEPATH,AE.ICHILDPRONAME,AE.IACTDESCRIPTION,AE.IOUTPUTPARAM,AE.ISHELLHOUSE,AE.ISHELLABSOLUTEPATH, AE.ILASTLINE,AE.IOKFILEFINDWEEK,AE.IFLAG,AE.IAGENTSOURCEGROUP, AE.ICHECKAGENTGROUP ,AE.APTGROUPNAME, AE.APTFILENAME, AE.ISDB2, AE.DB2IP, AE.APTRESGROUPNAME, AE.ISYSTEM,AE.IREDO,AE.IWEIGHTS,AE.IPRIORITY,case when exists (select iid from IEAI_TASK_DISABLE_CON C WHERE C.IPRONAME = AE.ICHILDPRONAME AND C.IFLOWNAME=AE.IACTNAME AND C.ISYSTEM=AE.ISYSTEM ) then  '是' ELSE  '无'  end  as IDISABLED ,AE.ISAGENTGROUP,AE.IDELAYTIME,AE.IBRANCHCONDITION,AE.IRETRYNUM, AE.ICALENDNAME, AE.IRETRYTIME, AE.IRETRYENDTIME ,AE.IAUTOSKIP, AE.IDELAYWARNNING,AE.DAYS,AE.LOGIC,AE.WDAYS,AE.IMONTH,AE.PERFORMUSER,AE.VIRTUALNAME,AE.JOBLIST,AE.SEQUENCENUMBER,AE.IENDTAG,AE.IUSERTASK,AE.IJOBTYPE from (SELECT distinct E.Ioperationid, E.IMAINPRONAME, E.IMAINLINENAME, E.IACTNAME,C.IACTNAME as preact, E.IHEADTAILFLAG, E.IOKFILEABSOLUTEPATH, E.ICHILDPRONAME, E.IACTDESCRIPTION, E.IOUTPUTPARAM, E.ISHELLHOUSE, E.ISHELLABSOLUTEPATH, E.ILASTLINE, E.IOKFILEFINDWEEK, E.IFLAG,  E.IAGENTSOURCEGROUP, E.ICHECKAGENTGROUP, E.APTGROUPNAME, E.APTFILENAME, E.ISDB2, E.DB2IP, E.APTRESGROUPNAME ,E.ISYSTEM,E.IREDO,E.IWEIGHTS,E.IPRIORITY,E.ISAGENTGROUP,E.IDELAYTIME,E.IBRANCHCONDITION,E.IRETRYNUM, E.ICALENDNAME, E.IRETRYTIME,E.IRETRYENDTIME,E.IAUTOSKIP,E.IDELAYWARNNING,E.DAYS,E.LOGIC,E.WDAYS,E.IMONTH,E.PERFORMUSER,E.VIRTUALNAME,E.JOBLIST,E.SEQUENCENUMBER,E.IENDTAG,E.IUSERTASK,E.IJOBTYPE  FROM IEAI_EXCELMODEL E  left join IEAI_ACTPRE A on  A.IPREACTNAME = E.Iactname AND A.IPROJECTNAME=E.IMAINPRONAME left join IEAI_EXCELMODEL C  on A.IOPERATIONID=C.IOPERATIONID WHERE ( E.IMAINPRONAME=?  OR E.ICHILDPRONAME=? ) ) AE where  AE.IOPERATIONID in (select ia.IOPERATIONID from  ieai_excelmodel ia where  ia.IMAINPRONAME=? or ia.ICHILDPRONAME=? )  order by AE.IOPERATIONID";
        }
         try
        {
            con = DBManager.getInstance().getJdbcConnection(dbType);
            pres = con.prepareStatement(presql);
            pres.setString(1, prjName);
            pres.setString(2, prjName);
            pres.setString(3, prjName);
            pres.setString(4, prjName);
            rs = pres.executeQuery();
            ActRelationInfo workflows = null;
            String tmpId = "";
            perListMap( rs, preList, preMap);

        } catch (SQLException e)
        {
            _log.error("Execution sql error at the method getRelationInfo of TaskDownloadManager ", e);
            throw new RepositoryException(ServerError.ERR_DB_EXEC);
        } catch (DBException e)
        {
            _log.error("Connect database error at the method getRelationInfo of TaskDownloadManager ", e);
            throw new RepositoryException(ServerError.ERR_DB_EXEC);
        } finally
        {
            DBResource.closeConn(con, rs, pres, method, _log);
        }
        return preMap;
    }

    /**
     * 判断该工程是否为excel模板上传方式 是子系统还是工程名
     *
     * @param prjName
     * @param dbType
     * @return
     * @throws RepositoryException
     */
    public String isChildName(String prjName, int dbType) throws RepositoryException {

        Connection con = null;
        PreparedStatement pres = null;
        ResultSet rs = null;
        String proName = prjName;
        String sql = " SELECT DISTINCT IMAINPRONAME FROM IEAI_EXCELMODEL WHERE  ICHILDPRONAME = ?";
        try {
            con = DBManager.getInstance().getJdbcConnection(dbType);
            pres = con.prepareStatement(sql);
            pres.setString(1, prjName);
            rs = pres.executeQuery();
            while (rs.next()) {
                proName = rs.getString(1);
            }
        } catch (SQLException e) {
            _log.error("Execution sql error at the method isChildName of TaskDownloadManager ", e);
            throw new RepositoryException(ServerError.ERR_DB_EXEC);
        } catch (DBException e) {
            _log.error("Connect database error at the method isChildName of TaskDownloadManager ", e);
            throw new RepositoryException(ServerError.ERR_DB_EXEC);
        } finally {
            DBResource.closeConn(con, rs, pres, "isChildName", _log);
        }
        return proName;
    }

    public static final  String  MESSAGE               = "message";
    public static final  String  SUCCESS               = "success";
    public static final  String  DATA_LIST             = "dataList";
    public static final  String  RESULT_SUCCESS        = "查询成功！";
    public static final  String  RESULT_FAIL           = "查询失败！";
}
