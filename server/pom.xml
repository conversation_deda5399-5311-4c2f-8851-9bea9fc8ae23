<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.ideal.ieai</groupId>
	<artifactId>server</artifactId>
	<version>0.1-SNAPSHOT</version>
	<parent>
		<groupId>com.ideal</groupId>
		<artifactId>entegorboot-parent</artifactId>
		<version>0.0.1-SNAPSHOT</version>
		<relativePath />
	</parent>

	<properties>
		<project.build.sourceEncoding>GBK</project.build.sourceEncoding>
		<java.version>1.8</java.version>
	</properties>
	<dependencies>
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>javax.servlet.jsp</groupId>
			<artifactId>javax.servlet.jsp-api</artifactId>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.snmp4j</groupId>
			<artifactId>snmp4j</artifactId>
		</dependency>
		<dependency>
			<groupId>ideal</groupId>
			<artifactId>drcb-rsasm</artifactId>
			<version>2.0</version>
		</dependency>
		<dependency>
			<groupId>p6spy</groupId>
			<artifactId>p6spy</artifactId>
			<version>3.9.1</version>
		</dependency>
		<!--
		<dependency>
			<groupId>com.simp</groupId>
			<artifactId>com.simp</artifactId>
		</dependency> 
		-->
		<!-- for springboot add end -->
	
	
		<dependency>
			<groupId>com.github.ben-manes.caffeine</groupId>
			<artifactId>caffeine</artifactId>
		</dependency>
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
		</dependency>
		<!-- RocketMQ -->
		<!-- <dependency> <groupId>com.alibaba.rocketmq</groupId> <artifactId>rocketmq-client</artifactId> 
			<version>3.5.8</version> </dependency> -->
		<dependency>
            <groupId>com.rabbitmq</groupId>
            <artifactId>amqp-client</artifactId>
	 	</dependency>
		<dependency>
			<groupId>org.apache.rocketmq</groupId>
			<artifactId>rocketmq-client</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>fastjson</artifactId>
					<groupId>com.alibaba</groupId>
				</exclusion>
				<exclusion>
					<artifactId>netty-all</artifactId>
					<groupId>io.netty</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- svnkit 20200104 范铁军在主线上开发一个SVN下载功能所用到的jar start-->
		<!-- https://mvnrepository.com/artifact/org.antlr/antlr-runtime -->
		<dependency>
			<groupId>org.antlr</groupId>
			<artifactId>antlr-runtime</artifactId>
		</dependency>

		<!-- https://mvnrepository.com/artifact/net.java.dev.jna/jna -->
		<dependency>
			<groupId>net.java.dev.jna</groupId>
			<artifactId>jna</artifactId>
		</dependency>

		<!-- https://mvnrepository.com/artifact/de.regnis.q.sequence/sequence-library -->
		<dependency>
			<groupId>de.regnis.q.sequence</groupId>
			<artifactId>sequence-library</artifactId>
		</dependency>

		<!-- https://mvnrepository.com/artifact/org.tmatesoft.sqljet/sqljet -->
		<dependency>
			<groupId>org.tmatesoft.sqljet</groupId>
			<artifactId>sqljet</artifactId>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.tmatesoft.svnkit/svnkit-cli -->
		<dependency>
			<groupId>org.tmatesoft.svnkit</groupId>
			<artifactId>svnkit-cli</artifactId>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.tmatesoft.svnkit/svnkit -->
		<dependency>
			<groupId>org.tmatesoft.svnkit</groupId>
			<artifactId>svnkit</artifactId>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.tmatesoft.svnkit/svnkit-javahl -->
		<dependency>
			<groupId>org.tmatesoft.svnkit</groupId>
			<artifactId>svnkit-javahl</artifactId>
		</dependency>
		<!-- https://mvnrepository.com/artifact/com.trilead/trilead-ssh2 -->
		<dependency>
			<groupId>com.trilead</groupId>
			<artifactId>trilead-ssh2</artifactId>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.tmatesoft.svnkit/svnkit-cli -->
		<!-- svnkit 20200104 范铁军在主线上开发一个SVN下载功能所用到的jar end-->


		<dependency>
			<groupId>com.ideal.ieai</groupId>
			<artifactId>idealutils</artifactId>
			<version>0.1-SNAPSHOT</version>
			<scope>compile</scope>
			<exclusions>
				<exclusion>
					<artifactId>slf4j-api</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.ideal.ieai</groupId>
			<artifactId>communication</artifactId>
			<version>0.1-SNAPSHOT</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>com.ideal.ieai</groupId>
			<artifactId>core</artifactId>
			<version>0.1-SNAPSHOT</version>
			<scope>compile</scope>
			<exclusions>
				<exclusion>
					<artifactId>javassist</artifactId>
					<groupId>org.javassist</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.ideal.ieai</groupId>
			<artifactId>commonadaptor</artifactId>
			<version>0.1-SNAPSHOT</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>com.ideal.ieai</groupId>
			<artifactId>interface</artifactId>
			<version>0.1-SNAPSHOT</version>
			<scope>compile</scope>
		</dependency>
		<!--
		<dependency>
			<groupId>com.ideal.ieai</groupId>
			<artifactId>studio</artifactId>
			<version>0.1-SNAPSHOT</version>
			<scope>compile</scope>
		</dependency>
		-->
		<dependency>
			<groupId>com.ideal.ieai</groupId>
			<artifactId>webservice</artifactId>
			<version>0.1-SNAPSHOT</version>
			<scope>compile</scope>
		</dependency>

		<!-- jstl -->
		<dependency> 
			<groupId>javax.servlet</groupId>
			<artifactId>jstl</artifactId>
		</dependency>
		
		<!-- spring -->
		<dependency>
			<groupId>org.springframework.ldap</groupId>
			<artifactId>spring-ldap-core</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.springframework</groupId>
					<artifactId>spring-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.springframework</groupId>
					<artifactId>spring-asm</artifactId>
				</exclusion>
				<exclusion>
				      <groupId>org.slf4j</groupId>
		     		 <artifactId>slf4j-api</artifactId>
		      	</exclusion>
		     	<exclusion>
		     		<groupId>org.springframework</groupId>
                    <artifactId>spring-beans</artifactId>
                </exclusion>
		     	<exclusion>
		     		<groupId>org.springframework</groupId>
                    <artifactId>spring-tx</artifactId>
                </exclusion>
		       	<exclusion>
		       		<groupId>org.springframework</groupId>
                    <artifactId>spring-context</artifactId>
                </exclusion>
		      	<exclusion>
		      		<groupId>org.springframework</groupId>
                    <artifactId>spring-jdbc</artifactId>
                </exclusion>
		     	<exclusion>
		     		<groupId>org.springframework</groupId>
		     	 	<artifactId>spring-orm</artifactId>
		      	</exclusion>
		      	<exclusion> 
		     		<groupId>org.springframework</groupId>
		     	 	<artifactId>spring-aop</artifactId>
		      	</exclusion>
		      	<exclusion> 
		     		<groupId>org.springframework</groupId>
		     	 	<artifactId>spring-expression</artifactId>
		      	</exclusion>
		      	<exclusion> 
		     		<groupId>org.springframework</groupId>
		     	 	<artifactId>spring-jcl</artifactId>
		      	</exclusion>
		      	<exclusion> 
		     		<groupId>org.springframework</groupId>
		     	 	<artifactId>spring-messaging</artifactId>
		      	</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context</artifactId>
			</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context-support</artifactId>
			</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-webmvc</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-tx</artifactId>
		</dependency>
		
		<!-- for sringboot  add begin -->
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-aop</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-aspects</artifactId>
		</dependency>
		<!-- goldendb依赖 -->
		<dependency>
			<groupId>ideal</groupId>
			<artifactId>gdb_mysql-connector-java</artifactId>
			<version>5.1.46.30</version>
		</dependency>
		<!-- for sringboot  add end -->
		<!-- del
		<dependency>
			<groupId>commons-lang</groupId>
			<artifactId>commons-lang</artifactId>
			<version>2.6</version>
		</dependency>
		
		<dependency>
			<groupId>commons-beanutils</groupId>
			<artifactId>commons-beanutils</artifactId>
			<version>1.7.0</version>
			<exclusions>
				<exclusion>
					<groupId>*</groupId>
					<artifactId>*</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		
		<dependency>
			<groupId>commons-dbcp</groupId>
			<artifactId>commons-dbcp</artifactId>
			<version>1.2.1</version>
			<exclusions>
				<exclusion>
					<groupId>*</groupId>
					<artifactId>*</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		
		<dependency>
			<groupId>commons-pool</groupId>
			<artifactId>commons-pool</artifactId>
			<version>1.2</version>
			<exclusions>
				<exclusion>
					<groupId>*</groupId>
					<artifactId>*</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		
		<dependency>
			<groupId>commons-configuration</groupId>
			<artifactId>commons-configuration</artifactId>
			<version>1.0</version>
			<exclusions>
				<exclusion>
					<groupId>*</groupId>
					<artifactId>*</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		-->
<!--		<dependency>-->
<!--			<groupId>commons-codec</groupId>-->
<!--			<artifactId>commons-codec</artifactId>-->
<!--			<version>1.3</version>-->
<!--			<exclusions>-->
<!--				<exclusion>-->
<!--					<groupId>*</groupId>-->
<!--					<artifactId>*</artifactId>-->
<!--				</exclusion>-->
<!--			</exclusions>-->
<!--		</dependency>-->

<!-- 
		<dependency>
			<groupId>commons-net</groupId>
			<artifactId>commons-net</artifactId>
			<version>1.3.0</version>
			<exclusions>
				<exclusion>
					<groupId>*</groupId>
					<artifactId>*</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>1.1</version>
			<exclusions>
				<exclusion>
					<groupId>*</groupId>
					<artifactId>*</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		-->
		<!--
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
			<version>3.4</version>
			<exclusions>
				<exclusion>
					<groupId>*</groupId>
					<artifactId>*</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		-->
		<!-- <dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-compress</artifactId>
			<version>1.10</version>
			<exclusions>
				<exclusion>
					<groupId>*</groupId>
					<artifactId>*</artifactId>
				</exclusion>
			</exclusions>
		</dependency> -->
		<dependency>
			<groupId>commons-dbutils</groupId>
			<artifactId>commons-dbutils</artifactId>
			<exclusions>
				<exclusion>
					<groupId>*</groupId>
					<artifactId>*</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<!--
		<dependency>
			<groupId>commons-fileupload</groupId>
			<artifactId>commons-fileupload</artifactId>
			<version>1.3.3</version>
			<exclusions>
				<exclusion>
					<groupId>*</groupId>
					<artifactId>*</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		-->
		<!-- https://mvnrepository.com/artifact/commons-discovery/commons-discovery -->
		<dependency>
			<groupId>commons-discovery</groupId>
			<artifactId>commons-discovery</artifactId>
		</dependency>


		<!-- aop相关 -->
		<!-- https://mvnrepository.com/artifact/aopalliance/aopalliance -->
		<dependency>
			<groupId>aopalliance</groupId>
			<artifactId>aopalliance</artifactId>
			</dependency>
		<dependency>
			<groupId>org.aspectj</groupId>
			<artifactId>aspectjrt</artifactId>
		</dependency>
		<dependency>
			<groupId>org.aspectj</groupId>
			<artifactId>aspectjweaver</artifactId>
		</dependency>



		<!-- spring管理的 velocity thyemleaf 等模板 <dependency> <groupId>ideal</groupId>
			<artifactId>velocity</artifactId> <version>1.6.4</version> </dependency>
			<dependency> <groupId>ideal</groupId> <artifactId>velocity-tools</artifactId>
			<version>2.0</version> </dependency> -->
		<!-- https://mvnrepository.com/artifact/org.thymeleaf/thymeleaf -->
		<dependency>
			<groupId>org.thymeleaf</groupId>
			<artifactId>thymeleaf</artifactId>
			<version>3.0.15.RELEASE</version>
			<exclusions>
				<exclusion>
					<artifactId>javassist</artifactId>
					<groupId>org.javassist</groupId>
				</exclusion>
				<exclusion>
					<artifactId>slf4j-api</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
				<exclusion>
					 <groupId>ognl</groupId>
      				<artifactId>ognl</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.thymeleaf/thymeleaf-spring5 -->
		<dependency>
			<groupId>org.thymeleaf</groupId>
			<artifactId>thymeleaf-spring5</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>slf4j-api</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
			</exclusions>
		</dependency>


		<!-- https://mvnrepository.com/artifact/xmlrpc/xmlrpc -->
		<dependency>
			<groupId>xmlrpc</groupId>
			<artifactId>xmlrpc</artifactId>
		</dependency>
		<!-- https://mvnrepository.com/artifact/net.sf.hibernate/hibernate -->
		<dependency>
			<groupId>net.sf.hibernate</groupId>
			<artifactId>hibernate</artifactId>
		</dependency>

		<!-- log代理 -->
		<!-- https://mvnrepository.com/artifact/commons-logging/commons-logging -->
		<dependency>
			<groupId>commons-logging</groupId>
			<artifactId>commons-logging</artifactId>
		</dependency>
		
		<dependency>
	        <groupId>net.sourceforge.jexcelapi</groupId>
	        <artifactId>jxl</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>log4j</artifactId>
					<groupId>log4j</groupId>
				</exclusion>
			</exclusions>
		</dependency>

<!--
		<dependency>
			<groupId>ideal</groupId>
			<artifactId>jxl</artifactId>
			<version>1.0</version>
		</dependency>
		-->	
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
		</dependency>
		
		<!--
		<dependency>
			<groupId>ideal</groupId>
			<artifactId>slf4j-log4j12</artifactId>
			<version>1.7.21</version>
		</dependency>
	-->
		<dependency>
			<groupId>ideal</groupId>
			<artifactId>json</artifactId>
		</dependency>
		<dependency>
			<groupId>ideal</groupId>
			<artifactId>hsm_lm1_4</artifactId>
		</dependency>
		<dependency>
			<groupId>ideal</groupId>
			<artifactId>quartz-all</artifactId>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.apache.poi/poi-ooxml -->
<!--		<dependency>-->
<!--			<groupId>org.apache.poi</groupId>-->
<!--			<artifactId>poi-ooxml</artifactId>-->
<!--			<version>3.10-FINAL</version>-->
<!--		</dependency>-->
		<dependency>
			<groupId>ideal</groupId>
			<artifactId>mail</artifactId>
		</dependency>
		<dependency>
			<groupId>ideal</groupId>
			<artifactId>mailapi</artifactId>
		</dependency>
		<!-- jackson 1X + 2X -->
		<dependency>
			<groupId>org.codehaus.jackson</groupId>
			<artifactId>jackson-core-asl</artifactId>
		</dependency>
		<dependency>
			<groupId>org.codehaus.jackson</groupId>
			<artifactId>jackson-mapper-asl</artifactId>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-annotations</artifactId>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-core</artifactId>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
		</dependency>

		<!-- dubbo zk -->
		<!-- https://mvnrepository.com/artifact/org.apache.dubbo/dubbo -->
		<dependency>
			<groupId>org.apache.dubbo</groupId>
			<artifactId>dubbo</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>spring-context</artifactId>
					<groupId>org.springframework</groupId>
				</exclusion>
				<exclusion>
					<artifactId>fastjson</artifactId>
					<groupId>com.alibaba</groupId>
				</exclusion>
				<exclusion>
					<artifactId>snakeyaml</artifactId>
					<groupId>org.yaml</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.yaml</groupId>
			<artifactId>snakeyaml</artifactId>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.apache.curator/curator-client -->
		<dependency>
			<groupId>org.apache.curator</groupId>
			<artifactId>curator-client</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.apache.zookeeper</groupId>
					<artifactId>zookeeper</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>slf4j-api</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.apache.curator/curator-framework -->
		<dependency>
			<groupId>org.apache.curator</groupId>
			<artifactId>curator-framework</artifactId>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.apache.curator/curator-recipes -->
		<dependency>
			<groupId>org.apache.curator</groupId>
			<artifactId>curator-recipes</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.curator</groupId>
			<artifactId>curator-x-discovery</artifactId>
		</dependency>
		<!--<dependency> <groupId>ideal</groupId> <artifactId>zookeeper</artifactId> 
			<version>3.4.14</version> </dependency> -->
		<dependency>
			<groupId>org.apache.zookeeper</groupId>
			<artifactId>zookeeper</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>slf4j-log4j12</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>slf4j-api</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.101tec</groupId>
			<artifactId>zkclient</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>zookeeper</artifactId>
					<groupId>org.apache.zookeeper</groupId>
				</exclusion>
				<exclusion>
					<artifactId>slf4j-api</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>jaxen</groupId>
			<artifactId>jaxen</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>dom4j</artifactId>
					<groupId>dom4j</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>saxpath</groupId>
			<artifactId>saxpath</artifactId>
		</dependency>
		<dependency>
			<groupId>ideal</groupId>
			<artifactId>jcommon</artifactId>
		</dependency>
		<dependency>
			<groupId>ideal</groupId>
			<artifactId>notifyAoms</artifactId>
		</dependency>
		<dependency>
			<groupId>ideal</groupId>
			<artifactId>taobao-sdk-java-auto_1479188381469</artifactId>
		</dependency>

		<dependency>
			<groupId>ideal</groupId> 
			<artifactId>commons-excel</artifactId>
		</dependency>
		
		<dependency>
			<groupId>ideal</groupId>
			<artifactId>BatchShell</artifactId>
		</dependency>
		<dependency>
			<groupId>ideal</groupId>
			<artifactId>com.fjhx.encrypt</artifactId>
		</dependency>
		<!-- https://mvnrepository.com/artifact/net.sourceforge.javacsv/javacsv -->
		<dependency>
			<groupId>net.sourceforge.javacsv</groupId>
			<artifactId>javacsv</artifactId>
		</dependency>
		<!-- <dependency> <groupId>geronimo-spec</groupId> <artifactId>geronimo-spec-jms</artifactId> 
			<version>1.1-rc3</version> </dependency> -->
		<dependency>
			<groupId>ideal</groupId>
			<artifactId>geronimo-spec-jta</artifactId>
		</dependency>
		<dependency>
			<groupId>ideal</groupId>
			<artifactId>geronimo-spec-jms</artifactId>
		</dependency>
		<dependency>
			<groupId>ideal</groupId>
			<artifactId>geronimo-spec-j2ee-management</artifactId>
		</dependency>
		<dependency>
			<groupId>ideal</groupId>
			<artifactId>geronimo-spec-j2ee-jacc</artifactId>
			<classifier>rc3</classifier>
		</dependency>
		<dependency>
			<groupId>ideal</groupId>
			<artifactId>jfreechart</artifactId>
		</dependency>
		<dependency>
			<groupId>ideal</groupId>
			<artifactId>spc-webos</artifactId>
		</dependency>
		<!-- https://mvnrepository.com/artifact/com.jcraft/jsch -->
		<dependency>
			<groupId>ideal</groupId>
			<artifactId>jotm</artifactId>
		</dependency>
		<dependency>
			<groupId>ideal</groupId>
			<artifactId>jotm_iiop_stubs</artifactId>
		</dependency>
		<dependency>
			<groupId>ideal</groupId>
			<artifactId>jotm_jrmp_stubs</artifactId>
		</dependency>
		<dependency>
			<groupId>ideal</groupId>
			<artifactId>com.ibm.mq</artifactId>
		</dependency>
		<dependency>
			<groupId>ideal</groupId>
			<artifactId>com.ibm.mq.commonservices</artifactId>
		</dependency>
		<dependency>
			<groupId>ideal</groupId>
			<artifactId>com.ibm.mq.headers</artifactId>
		</dependency>
		<dependency>
			<groupId>ideal</groupId>
			<artifactId>com.ibm.mq.jmqi</artifactId>
		</dependency>
		<dependency>
			<groupId>ideal</groupId>
			<artifactId>com.ibm.mq.jms.Nojndi</artifactId>
		</dependency>
		<dependency>
			<groupId>ideal</groupId>
			<artifactId>com.ibm.mq.pcf</artifactId>
		</dependency>
		<dependency>
			<groupId>ideal</groupId>
			<artifactId>com.ibm.mq.soap</artifactId>
		</dependency>
		<dependency>
			<groupId>ideal</groupId>
			<artifactId>com.ibm.mqjms</artifactId>
		</dependency>

		<!-- 配置类jar 或 其他jar包 关联jar -->
		<dependency>
			<groupId>xml-apis</groupId>
			<artifactId>xml-apis</artifactId>
		</dependency>
		<dependency>
			<groupId>ideal</groupId>
			<artifactId>cglib-full-2.0.2</artifactId>
		</dependency>
		<dependency>
			<groupId>ideal</groupId>
			<artifactId>cglib-nodep-2.2</artifactId>
		</dependency>
		<dependency>
			<groupId>ideal</groupId>
			<artifactId>odmg</artifactId>
		</dependency>
		<dependency>
			<groupId>ideal</groupId>
			<artifactId>ognl</artifactId>
		</dependency>
		<dependency>
			<groupId>ideal</groupId>
			<artifactId>ldap</artifactId>
		</dependency>
		<dependency>
			<groupId>ideal</groupId>
			<artifactId>net.people2000.ikey.spdb</artifactId>
		</dependency>
		<dependency>
			<groupId>commons-fileupload</groupId>
			<artifactId>commons-fileupload</artifactId>
			<exclusions>
				<exclusion>
					<groupId>*</groupId>
					<artifactId>*</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
<!-- 		<dependency>
			<groupId>ideal</groupId>
			<artifactId>struts-taglib</artifactId>
			<version>1.3.10</version>
		</dependency> -->
		<dependency>
			<groupId>wsdl4j</groupId>
			<artifactId>wsdl4j</artifactId>
		</dependency>

		<!-- provider启动使用 -->
		<!-- https://mvnrepository.com/artifact/net.lingala.zip4j/zip4j -->
		<dependency>
			<groupId>net.lingala.zip4j</groupId>
			<artifactId>zip4j</artifactId>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.jboss.netty/netty -->
		<dependency>
			<groupId>org.jboss.netty</groupId>
			<artifactId>netty</artifactId>
		</dependency>
		<dependency>
			<groupId>com.googlecode.java-diff-utils</groupId>
			<artifactId>diffutils</artifactId>
		</dependency>
		<!-- <dependency> <groupId>org.apache.activemq</groupId> <artifactId>activemq-all</artifactId> 
			<version>5.11.2</version> </dependency> -->

		<dependency>
			<groupId>org.apache.activemq</groupId>
			<artifactId>activemq-client</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>slf4j-api</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		
		
		<dependency>
			<groupId>com.squareup.okhttp3</groupId>
			<artifactId>okhttp</artifactId>
		</dependency>
		
		<dependency>
			<groupId>wfbank</groupId>
			<artifactId>hxHSM-api</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-pool2</artifactId>
		</dependency>
		<dependency>
			<groupId>com.github.penggle</groupId>
			<artifactId>kaptcha</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>javax.servlet-api</artifactId>
					<groupId>javax.servlet</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.jhlabs</groupId>
			<artifactId>filters</artifactId>
		</dependency>

		<dependency>
			<groupId>psbc.ideal</groupId>
			<artifactId>yc_blj_transpwd-1.0.0</artifactId>
		</dependency>
		<dependency>
			<groupId>cebbank</groupId>
			<artifactId>caspclient</artifactId>
		</dependency>
		<dependency>
			<groupId>org.codehaus.xfire</groupId>
			<artifactId>xfire-core</artifactId>
			<exclusions>
				<exclusion>
						<groupId>commons-httpclient</groupId>
						<artifactId>commons-httpclient</artifactId>
				</exclusion>
				<exclusion>
						 <groupId>jdom</groupId>
      					 <artifactId>jdom</artifactId>
				</exclusion>
				<exclusion>
						 <groupId>javax.mail</groupId>
     					 <artifactId>mail</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.codehaus.xfire</groupId>
			<artifactId>xfire-aegis</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.kafka</groupId>
			<artifactId>kafka-clients</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>slf4j-api</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>CIB</groupId>
			<artifactId>cap_cas</artifactId>
		</dependency>
		<dependency>
			<groupId>CIB</groupId>
			<artifactId>cas_client</artifactId>
		</dependency>
		<dependency>
			<groupId>CIB</groupId>
			<artifactId>cas_clientcore</artifactId>
		</dependency>

		<dependency>
			<groupId>ideal.cqrcb</groupId>
			<artifactId>shortmsg</artifactId>
		</dependency>
		<dependency>
			 <groupId>org.fusesource.hawtbuf</groupId>
			  <artifactId>hawtbuf</artifactId>
		</dependency>
		<dependency>
		  <groupId>wfbank</groupId>
		  <artifactId>fpspConnPoolApi</artifactId>
		</dependency>
        <!--aliyun Ons rockectmq -->
		<dependency>
			<groupId>com.aliyun.openservices</groupId>
			<artifactId>ons-client</artifactId>
		</dependency>
		<dependency>
		  <groupId>org.jsoup</groupId>
		  <artifactId>jsoup</artifactId>
		</dependency>
		<dependency>
			<groupId>ideal</groupId>
			<artifactId>jodconverter</artifactId>
		</dependency>
		<dependency>
			<groupId>ideal</groupId>
			<artifactId>BHbankSMSclient</artifactId>
			<version>1.0</version>
		</dependency>
<!--		<dependency>-->
<!--			<groupId>ideal</groupId>-->
<!--			<artifactId>struts-core</artifactId>-->
<!--			<version>1.3.10</version>-->
<!--		</dependency>-->

	<!--		福建农信专用jar包增加-->
	        <dependency>
	            <groupId>ideal.fjnx</groupId>
	            <artifactId>security_tools</artifactId>
	            <version>1.0</version>
	        </dependency>
	        <dependency>
	            <groupId>ideal.fjnx</groupId>
	            <artifactId>x509</artifactId>
	            <version>1.0</version>
	        </dependency>
	        <dependency>
	            <groupId>ideal.fjnx</groupId>
	            <artifactId>cdpackage</artifactId>
	            <version>1.0</version>
	        </dependency>

	        <dependency>
	            <groupId>ideal</groupId>
	            <artifactId>fjnx-java-api-union</artifactId>
	            <version>v1.0</version>
	        </dependency>

	        <dependency>
	            <groupId>ideal</groupId>
	            <artifactId>fjnx-java-api-keyou</artifactId>
	            <version>v1.0</version>
	        </dependency>

	        <dependency>
	            <groupId>ideal</groupId>
	            <artifactId>fjnx-api-java</artifactId>
	            <version>4.0.6</version>
	        </dependency>

	        <dependency>
	            <groupId>ideal</groupId>
	            <artifactId>fjnx-java-api</artifactId>
	            <version>3.8.166</version>
	        </dependency>

	        <dependency>
	            <groupId>ideal</groupId>
	            <artifactId>fjnx-security-tls</artifactId>
	            <version>1.0.5</version>
	        </dependency>

	        <dependency>
	            <groupId>ideal</groupId>
	            <artifactId>fjnx-soft-algorithm-core</artifactId>
	            <version>1.0</version>
	        </dependency>
	        <!--        福建农信esb短信需要jar-->
	        <dependency>
	            <groupId>ideal.fjnx</groupId>
	            <artifactId>esb-client-api</artifactId>
	            <version>1.1</version>
	        </dependency>

	        <dependency>
	            <groupId>ideal.fjnx</groupId>
	            <artifactId>framework</artifactId>
	            <version>3.0</version>
	        </dependency>

	        <dependency>
	            <groupId>ideal.fjnx</groupId>
	            <artifactId>swagger-jaxrs</artifactId>
	            <version>1.6.2</version>
	        </dependency>

	        <dependency>
	            <groupId>ideal.fjnx</groupId>
	            <artifactId>swagger-models</artifactId>
	            <version>1.6.2</version>
	        </dependency>
			<dependency>
				<groupId>com.github.librepdf</groupId>
				<artifactId>openpdf</artifactId>
				<version>1.3.43</version>
			</dependency>
			<dependency>
				<groupId>ideal</groupId>
				<artifactId>mxgraph-all</artifactId>
				<version>1.0</version>
			</dependency>

		    <dependency>
				<groupId>io.kubernetes</groupId>
				<artifactId>client-java</artifactId>
				<version>17.0.2</version>
				<scope>compile</scope>
			</dependency>
			<dependency>
				<groupId>com.jayway.jsonpath</groupId>
				<artifactId>json-path</artifactId>
				<exclusions>
					<exclusion>
						<groupId>org.ow2.asm</groupId>
						<artifactId>asm</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.3.2</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
			<groupId>com.esotericsoftware</groupId>
			<artifactId>kryo</artifactId>
			<version>5.6.0</version>
		</dependency>
    </dependencies>
	
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<configuration>
					<skip>true</skip>
				</configuration>
			</plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
        </plugins>
		<resources>
			<resource>
				<directory>src/main/java</directory>
				<includes>
					<include>**/*.hbm.xml</include>
				</includes>
			</resource>
			<resource>
				<directory>src/main/resources</directory>
				<includes>
					<include>**/*.*</include>
				</includes>
			</resource>
			<resource>
				<directory>..</directory>
				<includes>
					<include>version.txt</include>
				</includes>
			</resource>
		</resources>
	</build>
</project>