bh.parameter.check.switch=true
script.denend.script.switch=true
script.edite.tab.show.switch=true
jlnx.script.work.dir=true
jlnx.script.class=true
script.shell.syntax.validate=true
#script.edit.book.switch=true
script.function.output.switch=true
script.edit.template.switch=true

#告警开关
alarmNanJingSwitch=true
#新增维护期URL
alarmSuppressionNanJingUrl=http://************:8080/idealeye-manager/monitor/api/maintain/create-maintain-strategy
#删除维护期URL
alarmClearingNanJingUrl=http://************:8080/idealeye-manager/monitor/api/maintain/delete-maintain-strategy/


#sd.ldap.domain.user.switch=true
script.edit.book.switch=true
script.batch.release.switch=true
#script.three.bstype.switch=true
unity.token.sgs.switch=true

openofficeip=127.0.0.1
openofficeport=8100


double.message.order.switch=true
templatecall.state.manage.thread.switch=true
pass.dm.switch=true
paas.eswitch.flag=true

#脚本服务-脚本下发功能
script.task.apply.attachment.params.switch=false
script.issued.checkmd5.switch=true
script.issuedbutton.switch=false

#CMDB变更记录开关
iscmdbgetdataneedconfirm=true

#流程编排批量参数

#shbank.batch.switch=true

script.danger.cmd.import.switch=true
ss.scriptMonitor.timeOutSwitch.show=true
fsh.isverify.message=false
fsh.superAdmin.loginname=ideal
double.message.fh.switch=true

##################################################

# entegor.type:ieai cm
# entegor.type=ieai
entegor.type=ieai
#patch_path=/program
patch_path=/

##################################################

# svninfo upload packeaget
svnuser=tao_ding
svnpass=********


##################################################

# resule send switch
send_packge=false
send_deploy=false


##################################################

# Server basic configurations
# port=Integer(6666,6667,...),default 6666 defines port that server listens to
# domain= name of the domain,service node which with the same name will at the same domain
port=6666
domain=Entegor_domain_001


##################################################

# SSL configurations, properties for SSL service.
# enable ssl connection,default is false
# ssl.enable=true/false
# ssl service port ,default is 6667
# ssl.port=6667
# ssl.mode=(no_certificate|not_verify_client|verify_client default=not_verify_client).
# ssl.keyStore=(key store file path).
# ssl.keyStorePassword=(key store password).
# ssl.trustStore=(trust store file path).
# ssl.trustStorePassword=(trust store password).
ssl.enable=false


##################################################

# System Database settings
# iEAI installer doesn't install JDBC driver. User must find coresponding JDBC driver
# for the system DB used to run iEAI server, and copy to $IEAI_HOME/libs.
# User also need to setup DB driver classs, DB source, user name, password information
#
# db.driver_class=JDBC driver class name
# db.url=Datasource of System database
# e.g.: ************************************
# db.username = User id to login to system database
# db.password = Password to login to system database
# db.type=oracle|oralce9|mssql|db2|sybase|mysql|
# db.charset = Characterset used to connect to database, default is iso_1
#
# Here're some examples of possible DB configs for some DBMS:
#
# For Sybase:
#   db.driver_class=com.sybase.jdbc2.jdbc.SybDriver
#   db.url=**********************************
#   db.username=username
#   db.password=password
#   db.charset=utf8
#    db.type=sybase
#
# For MS SQL Server:
#   db.driver_class=com.microsoft.jdbc.sqlserver.SQLServerDriver
#   db.url=******************************************************************************;
#   db.username=username
#   db.password=password
#   db.type=mssql
#
# For Oracle server:
#   db.driver_class=oracle.jdbc.driver.OracleDriver
#   db.url=************************************
#   db.username=username
#   db.password=password
#   db.type=oracle
#
# For DB2 server:
#   db.driver_class=com.ibm.db2.jcc.DB2Driver
#   db.url=******************************
#   db.username=username
#   db.password=password
#   db.type=db2
#
# For MySQL 4.1 or higher with MySQL Connector/J 3.1 or higher:
#db.driver_class=com.mysql.jdbc.Driver
#db.url=*******************************************************************************************************************************************************
#db.username=ideal
#db.password=ni9bVwZuEF2M/VxZvGO5CqFJi8BMRQPv
#db.type=mysql

#########压测环境
db.driver_class=com.mysql.jdbc.Driver
db.url=*****************************************************************************************************************************************************************************************************
db.username=root
db.password=ttuy18wyiZU1NOb4WL5vzJU4SgK/LGoV
db.type=mysql

#########山东
#db.driver_class=com.ibm.db2.jcc.DB2Driver
#db.url=jdbc:db2://***********14:58000/sdfz
#db.username=sdfz
#db.password=RPolF5j+PVhAC2e5uU4iwYSUKp1vvgEy
#db.type=db2


#db.driver_class=com.kingbase8.Driver
#db.url=*******************************************
#db.username=ideal
#db.password=ni9bVwZuEF2M/VxZvGO5CqFJi8BMRQPv
#db.type=kingbase8


#
#db.driver_class=oracle.jdbc.driver.OracleDriver
#db.url=********************************************
#db.username=entegor
#db.password=ni9bVwZuEF2M/VxZvGO5CqFJi8BMRQPv
#db.type=oracle9

##################################################

# DataBase Connection Pool setting
# It's supporsted to be edited by deployer , only when there is db connection problem.
# dbpool.initSize the init size of the connection pool
# dbpool.maxActive the max active connections in the connection pool
# dbpool.maxIdle the max idle connection  allow in the pool
# dbpool.minIdle the min idle connection allow in the pool
# dbpool.initSize=200
# dbpool.maxActive=200
# dbpool.maxIdle=10
# dbpool.minIdle=8
# dbpool.removeAbandoned=true
# dbpool.removeAbandonedTimeout=180
# dbpool.logAbandoned=true
# dbpool.testOnBorrow = false
# dbpool.validationQuery = SELECT 1 FROM IDUAL
dbpool.initSize=10
dbpool.maxActive=500
dbpool.maxIdle=10
dbpool.minIdle=8


##################################################

# Task timeout check interval and email setting
# check_interval=Integer, The time unit is second. Default is 30 seconds.
# email_country=(country flag)
# email_language=(language flag)


##################################################

# All none-standard system properties
# start_port=(default 8888) port of tomcat started if not config start at 8888
# notice.check.interval=30 second of notice state changed
# log.debug_mode=(true,false) default is false, enable this to use log4j.properties for log setting
# log.level=system log level, valid values are:OFF, FATAL, ERROR, WARN, INFO, ALL|DEBUG.
#          DEFAULT IS WARN
# log.filesize=maximum log file size in mega bytes, default is 10M
# log.filenum=maximum number of log file backup, default is 10
# log.toconsole=(true,false,t,f,y,n)is also dump log message to console, default is true
# log.consolepattern=Log4J compatible pattern.
#                   Default is:%d{ISO8601} %-5p %c{2}: %m%n
start_port=8888
cm_port=8887
log.debug_mode=false
log.level=ALL
log.filesize=10
log.filenum=10
log.toconsole=true
log.consolepattern=%d{ISO8601} %-5p %c{2}: %m%n


##################################################

# the below is log4j properties for system logger,user can custom the logger  of the 
# logger with properties below when the log.debug_mode is set to true
-- listing properties --


##################################################

# 用于指定server.log的输出目录。
# 默认使用entegor安装目录下的log文件夹。
# 填写该字段并赋值为目标文件夹的绝对路径后，需重启server服务，新日志输出目录生效。
# log.filepath =


##################################################

# For network recovery time
# When network is down, RExecAdaptor try to wait until network is recovered so that 
#  any executing remote command doesn't have to be killed or redone when network is backup
# The value is set to number of seconds to wait for network to be recovered
# Default is one hour
# network.recovery.wait.time = 3600


##################################################

#definition of password type and length
#user.password.pattern=[a-zA-Z_0-9]+
#user.password.length=8
#password.expired.day=(default 8888)day of time before the password will be expired 
#password.expired.day.switch=false
#pass.keep.num=3
#userpass.check=false
#pass.keep.cicle=30
user.password.length=1
password.expired.day=8888
pass.keep.num=2
userpass.check=false
pass.keep.cicle=8888


##################################################

#用户名密码长度及正则表达式验证
#user.loginname.lengthmax=20
#user.loginname.lengthmix=1
#user.loginname.pattern=[a-zA-Z_0-9]+
user.loginname.lengthmax=10
user.loginname.lengthmix=2


##################################################

#definition of disabling mutiple login, the default value is false
#if it's set to be true,then user can login in the server once


##################################################

#specify the IP of server
#server.ip=***********
#server.name=ICBC
server.ip=*********


##################################################

#################多服务器IP配置################
#server.iplist=***********,***********


##################################################

#################检查Serverip是否在本机的网卡中################
#isCheckServerIp=false


##################################################

#################变更任务结束弹窗提醒功能开关################
#pop.reminder.sus  开-ON 关-OFF


##################################################

# Database retry connection setting
# retry.connection.time = 10
# retry.connection.times = 10
retry.connection.time=10
retry.connection.times=10


##################################################

# Scheduler Thread Config
# safefirst.scheduler.thread.number = 300
safefirst.scheduler.thread.number=1000


##################################################

# Send mobile message when taks is timeout.Must implements com.ideal.ieai.core.activity.IMobileMsgSender
# Default is not mobile message.If you want send mobile message,need config like this:[mobile.msg.class=com.ideal.mobile.MobileMsgSender]


##################################################

#reset server's group 
#isGroupReset=false


##################################################

#check server time
#hbrotationtime=30


##################################################

#the time of server shutdown
#hbresponsetime＝30


##################################################

# studio open flows count
# default max count = 10
STUDIO_FLOWS_NUM=10


##################################################

#Tivoli Syslog . RemoteHost Addr and port
#syslogswitch=false
#syslogshell=false
#syslogjavafun=false
#syslogserver.ip=***********
#syslogserver.port=514
syslogswitch=false
syslogshell=false
syslogjavafun=false


##################################################

#数据库自身维护周期
#databasepollcycle=4
databasepollcycle=4


##################################################

#线程自身维护周期
#threadpollcycle=60


##################################################

#心跳自我检查周期
#selfchkpollcycle=5


##################################################

#数据库自我维护周期超过3分钟自杀
#suicidepollcycle=180
suicidepollcycle=180


##################################################

#数据库连接重试周期
#repeatpollcycle=60
repeatpollcycle=180


##################################################

#数据库连接重试次数
#repeatnum=6
repeatnum=6


##################################################

#心跳监控其他服务器是否健康周期
#moniterpollcycle=60


##################################################

#监控其他服务器数据库超过6分钟没有更新数据库状态周期自杀
#monitertimeoutpollcycle=360
monitertimeoutpollcycle=360


##################################################

#监控线程超过6分钟没有更新数据库状态周期自杀
#threadtimeoutpollcycle=360


##################################################

#迁移数据启动时间(0:00~23:59)
#transcheckresultpollcycle=2:00
transcheckresultpollcycle=14:53


##################################################

#同步总控用户启动时间(0:00~23:59)
#syncusergroupcycle=2:00


##################################################

#SAML SSO SWITCH
#saml.sso.switch=false
#健康巡检 是否发送报警邮件
#send.email.waring.switch=false
#健康巡检 是否发送事件单
#send.itil.incident.switch=false
#单点登录URL
#saml.url=http://90.17.0.4:8080/ldap/?appId=app020
#ITIL发送事件单接口地址
#itil.incident.url=http://90.17.0.4:8080/ldap/itil
#ITIL发送事件单用户名
#itil.incident.username=entegor
#ITIL发送事件单密码
#itil.incident.password=entegor
#同步AD域用户是否使用SpringLdap方式获取用户开关
#sync.user.springldap.switch=true
send.email.waring.switch=true
#发送巡检报警 socket方式
#send.socket.ic.warning=false
#发送巡检报警 socket方式的报文头设置
#health.check.oid=


##################################################

#JVM time warning .jvmpolltime is seccond;jvmoffersettime is minute
#jvmwarningswitch=false
#jvmpolltime=60
#jvmoffersettime=3


##################################################

# upload file implements com.ideal.ieai.servermanager.timeConstraint


##################################################

#更新状态，单位耗秒(updateExcelInsToFinish=500)默认500毫秒
updateExcelInsToFinish=7
# #流程结束多久移动数据到历史表 moveExcelDataTime=1000,单位:毫秒，movepollTime=1000,单位:毫秒多久轮询一次。
moveExcelDataTime=10
movepollTime=2
#变更管理超时报警轮询线程轮询时间，单位毫秒(flowTimeOutpollTime=30000)默认30000毫秒


##################################################

#bankswitch:开关轮询不同算法
#bankswitch=SHB


##################################################

#测试模式
#变更管理环境
#envname=生产环境
envname=演示环境


##################################################

#waitNum=900,单位:次,表示autoType-pub获取agent返回值轮询900次后退出，间隔2s
#waitNum=900


##################################################

#server modeltype
#1-作业调度；2-信息采集；3-变更管理；
#4-灾备切换；5-定时任务；6-应急操作；
#7-监控巡检；8-日常操作；9-应用启停
#10-脚本服务化；13-一致性比对;16-应用维护 ;20-拓扑图
#15-cmdb; 40-AZ切换 199-漏洞扫描
#serverType=1,2,3,4,5,6,7,8,9,10,13,15,16,20
serverType=1,2,3,4,5,6,7,8,9,10,13,15,16,19,20,22,23,25,27,30,40,300,400


##################################################

#自定义权限提示消息
secureFilterMessages=没有操作权限


##################################################

#测试模式
#testflag=false


##################################################

#domainconfig
#domainip=xxx.xxx.xxx.xxx
#domainport=389


##################################################

#dbresourceCicleTime=数据源监控轮询时间，默认：10，单位：分钟


##################################################

#DB最小空余连接数，默认：30，单位：个
#dbConFreeNum=30


##################################################

#fFiveMatchedShell:F5主备同步匹配的脚本名称
#fFiveAgentIp:F5主备同步agent IP地址
#fFiveAgentPort:F5主备同步agent端口号 默认1500
#fFiveSyncScript:F5主备同步所使用的脚本 需完整路径


##################################################

#EXCEL上传同包名校验开关 0-禁用 1-启用
#samePackageNameCheck=1
samePackageNameCheck=0


##################################################

#ITSM集成配置读取文件路径
#excelPath=/opt/


##################################################

#轮询周期配置信息
#sleep_time=100
sleep_time=5


##################################################

#线程开关
#工作流基础轮询线程
#isscheduler=true
#Excel实例驱动线程
#isexcelmodelexec=true
#监控自身数据库连接
#ischeckdbconnection=true
#数据库自写
#ismaintaindbselfstate=true
#线程池自写
#isrotationmaintainthreadstate=true
#心跳健康检查
#ishearbeatselfcheck=true
#失效备援
#isheartbeatmonitor=true
#数据清理线程
#istranscheckresult=true
#同步AD域用户开关
#issyncaduserswitch=true
#数据库连接池监控打印日志
#ischeckdbpoollog=true
#itsm集成轮询线程
#isexecitsmautodelaymonitor=true
#itsm集成轮询线程
#isexecsendmessageitsmautomonitor=true
#Excel实例完成跟新状态和Excel实例完成后转移到历史表
#isactexceldatahanle=true
#数据源监控
#isdbresourcemonitor=true
#延时器线程
#isexecdelaymonitor=false
#计算超时线程
#istimeguard=false
#招行超时计算线程
#istimeguardthreadzs=false
#优雅终止工作流线程
#ischeckstoppingflows=false
#监控接口数据库连接池
#ischeckdbconnectionpool=false
#数据库连接池监控报警
#ischeckdbpool=false
#监控sesstion是否超时
#ischecksessioninfo=false
#agent监控
#isagentcheck=false
#查询agent是否发生变化线程
#ispollagentchange=false
#变更管理超时报警轮询线程
#isflowTimeOutNodeInfo=true
#定时任务相关线程
#启停线程
#isTimetaskFailover=true
#计算定时任务监控图数据线程
#isCalculateChartData=true
#终止实例线程
#isTimetaskStopIns=true
#更新Agent状态线程
#isAgentUpdate=true
#定时任务超时统计线程
#isTimetaskTimeout=true
#Agent宕机监控
#agent.check=true
#Agent宕机监控轮训周期
#agent.check.cycle=10
#入站获取token失效时间(天) 默认30天
#token.overtime=30
#入站平台开关 默认false
#jobBatch.Monitor.Switch=true
#报警平台发送报文开关 默认false
#http.warn.switch=true
#报警平台发送报文 http 超时时间 默认1秒
#http.warn.wait.time=1
#报警平台发送报文地址
#http.warn.address=http://ip:port/portal/api/warning/extern/third_party/add.do
#报警阀值计算开关
#app.jail.warn.switch=true
#报警阀值规则更改开关
#app.jail.rule.change.switch=true
#报警阀值运行耗时计算开关
#app.jail.over.time.switch=true
isexcelmodelexec=true
ismaintaindbselfstate=true
isrotationmaintainthreadstate=true
ishearbeatselfcheck=true
isheartbeatmonitor=true
isexecitsmautodelaymonitor=true
isexecdelaymonitor=true
istimeguard=true
ischeckstoppingflows=true
agent.check=true
agent.check.cycle=1
app.jail.warn.switch=true
app.jail.rule.change.switch=true
app.jail.over.time.switch=true


##################################################

#设置数据连接池配置所需监控的动态数据源类型，参数为string类型，多数据源类型以逗号分隔，不填写默认为空
#monitor.servertype.switch=1,2,3


##################################################

#日常操作接口token失效时间(小时) 默认2小时
dailyoper.token.overtime=2


##################################################

# upload file implements com.ideal.ieai.servermanager.timeConstraint
#Int型，取值范围1分钟?永不超时(0或-1),默认值30分钟
#session.timeout=30


##################################################

# Default Properties file for use by StdSchedulerFactory
# to create a Quartz Scheduler Instance, if a different
# properties file is not explicitly specified.
#org.quartz.scheduler.instanceName = DefaultQuartzScheduler
#org.quartz.scheduler.rmi.export=false
#org.quartz.scheduler.rmi.proxy=false
#org.quartz.scheduler.wrapJobExecutionInUserTransaction=false

#org.quartz.threadPool.class=org.quartz.simpl.SimpleThreadPool
#org.quartz.threadPool.threadCount=10
#org.quartz.threadPool.threadPriority=5
#org.quartz.threadPool.threadsInheritContextClassLoaderOfInitializingThread=true

#org.quartz.jobStore.misfireThreshold=60000

#org.quartz.jobStore.class=org.quartz.simpl.RAMJobStore

org.quartz.scheduler.instanceName=DefaultQuartzScheduler
org.quartz.scheduler.rmi.export=false
org.quartz.scheduler.rmi.proxy=false
org.quartz.scheduler.wrapJobExecutionInUserTransaction=false

org.quartz.threadPool.class=org.quartz.simpl.SimpleThreadPool
org.quartz.threadPool.threadCount=10
org.quartz.threadPool.threadPriority=5
org.quartz.threadPool.threadsInheritContextClassLoaderOfInitializingThread=true

org.quartz.jobStore.misfireThreshold=60000

org.quartz.jobStore.class=org.quartz.simpl.RAMJobStore



##################################################

# 扫表启动、停止定时任务线程的扫表周期毫秒(默认1秒).
#timetask.failover.thread.monitor.cycle=1000

#timetask.stop.instance.thread.monitor.cycle=1000

#timetask.calculate.chartData.thread.monitor.cycle=1000

#timetask.timeout.thread.monitor.cycle=15000

timetask.failover.thread.monitor.cycle=1000
timetask.stop.instance.thread.monitor.cycle=1000
timetask.calculate.chartData.thread.monitor.cycle=1000
timetask.timeout.thread.monitor.cycle=15000


##################################################

#定时任务，日启时间，格式为:时（0-23）-分（0-59）-秒（0-59）
#5-7-5：表是每天5时7分5秒，执行日启
#若格式错误使用默认值每天2时执行日启
dailystart=8-50-1


##################################################

#定时任务最大任务数
#timetask.maxnum=99999
timetask.maxnum=99999


##################################################

#驱动类型,0:excel驱动;1:变更管理mxGraph驱动;2:变更mxGraph驱动并且灾备
poolswitch=2


##################################################

#是否是广发银行
isgdb=false


##################################################

#是否是中信银行
iscitic=false


##################################################

#pub服务器IP
pubAgentIP=*************
#pub服务器Port
pubAgentPort=15000
#pub服务器上执行脚本路径
pubExecShPath=/opt/EntegorAgent/baseScript/Graph/Analisys_Package.sh
#pub服务器上执行脚本时,获得ftp绝对根路径：例如 D:\ftp\sus (不要以\结尾)
ftpAbsoluteRootPath=/media/aoms
#比对流程,核对程序包版本取的shellPath值
confirmPkgPath=/media/aoms/confirm
#pub服务器操作系统类型1Win,2非win
pubOsType=2


##################################################

#版本管理推送FTP服务器
vmSendFtpHost=
#版本管理推送FTP端口
vmSendFtpPort=


##################################################

#当前机构标识
#D:数据中心 departmentFlag=D
#R:研发中心 departmentFlag=R


##################################################



##################################################

#运维管理平台数据库信息


##################################################

#SVN下载服务器IP
#SVN下载服务器Port
#SVN下载执行脚本路径


##################################################



##################################################

# cmdb_classname =com.microsoft.sqlserver.jdbc.SQLServerDriver
# cmdb_url=**********************************************************;
# cmdb_user=username
# cmdb_password=password
# cmdb_handtimes=12:00
# cmdb_flag=false



##################################################

#密码过期，不强制修改密码开关
#userloginexpirdforce.check=false


##################################################

# db.sqlserver.driver_class =com.microsoft.sqlserver.jdbc.SQLServerDriver
# db.sqlserver.url=******************************************************************************;
# db.sqlserver.username=username
# db.sqlserver.password=password
# ldap.switch=false 是否开通OA用户验证
# ldap.url= 
# ldap.port= 
# ldap.domain=
# ldap.dc=
# ldap.userDn=
# ldap.password=
# ldap.organizationBase=
# ldap.searchBase=
# ldap.ocOrganizationBase=
ldap.switch=false
ldap.url=*************
ldap.port=389
ldap.domain=@omr.bankofchina.com
ldap.searchBase=DC=omr,DC=bankofchina,DC=com
#ldap.displayName=st-app-zyjk-ldap

ldap.userDn=cn=root,dc=omr,dc=bankofchina,dc=com
ldap.password=123456
ldap.organizationBase=ou=Users
ldap.ocOrganizationBase=ou=People

#同步AD域用户是否使用SpringLdap方式获取用户开关
#sync.user.springldap.switch=true

##################################################

# isdbdataclean=false 变更管理_数据库数据_清理线程 开关


##################################################

#日常操作--双人复核--立即启动开关(默认存在)
double.message.ic.immediately.start.switch=false


##################################################

#应急处置--验证码失效时间(分钟) 默认10分钟
#emer.authcode.overtime=10


##################################################

#应急操作--预案发起-应急预案定时任务开关(默认不存在)
#emer.time.task.switch=false


##################################################

#应急操作--预案发起-立即执行开关(默认为false 执行双人复核操作)
#double.check.emer.planstart.immediate.switch=false


##################################################

#应急操作--应急处置-获取常用任务开关(默认为false，获取的是任务申请)
#em.getcommontask.switch=false


##################################################

#健康巡检--巡检监控--导出开关
#hc.inspect.monitor.export.switch=false
hc.inspect.monitor.export.switch=true


##################################################

#健康巡检--巡检健康-导出时间超时配置，超过配置时间最后巡检时间标黄(默认小时默认12小时)
#hc.over.last.inspect.time.switch=12


##################################################

#健康巡检--配置批量--设备样式同白名单开关
#health.inspection.computer.switch=false
health.inspection.computer.switch=true


##################################################

#用户管理--业务赋权--支持为用户配置菜单及业务系统权限功能开关
#user.permissions.all.config.items.switch=false


##################################################

#健康巡检--巡检监控--定时自动导出开关
#hc.inspect.monitor.export.thread.switch=false
hc.inspect.monitor.export.thread.switch=false


##################################################

#健康巡检巡检监控导出，默认早八点、晚五点
#hc.inspect.monitor.export.thread.cron=0 0 8,17 * * ?
hc.inspect.monitor.export.thread.cron=0 31,35,38 20 * * ?


##################################################

#健康巡检巡检监控交易量数据导出开关
#hc.inspect.monitor.export.trade.statistic=false


##################################################

#设备入组、业务系统下的所有定时任务绑定新入组的设备
#equipment.into.timetask.switch=false


##################################################

#新增 删除agent后，解绑agent关联的定时任务关系数据 开关(默认开启)
#tt.del.computer.of.agent.switch=true


##################################################

#应用启停--小信封--开关(默认关闭)
#double.message.sas.switch=true
#应急操作--小信封--开关(默认关闭)
#double.message.em.switch=true
#信息采集--小信封--开关(默认关闭)
#double.message.ic.switch=true
#定时任务--小信封--开关(默认关闭)
#double.message.tt.switch=true
#脚本服务化--小信封--开关(默认关闭)
#double.message.ss.switch=true
#脚本服务化--角色及权限去掉Agent分组--开关(默认关闭)
remove.agentGroup.switch=false
#变更管理--小信封--开关(默认关闭)
#double.message.sus.switch=true
#灾备切换--小信封--开关(默认关闭)
#double.message.sw.switch=true
#作业调度--小信封--开关(默认关闭)
#double.check.switch.jobscheduling=true
#集群报警--小信封--开关(默认关闭)
#double.message.ca.switch=true
#分析算法变更--小信封--开关(默认关闭)
#double.message.analyze.switch=true
#变更流程通知----开关(默认关闭)
#double.message.nt.switch=true
#流程执行完或异常发送邮件--开关(默认关闭)
#flow.mail.switch=true
#域用户导入--开关(默认关闭)
#areauserimport=true
#变更流程发起选择环境加上环境角色过滤--开关(默认关闭)
#envrolefilter=true
#账号管理平台地址
accountmanagementip=*************
#角色权限自动赋权列隐藏--开关(默认显示) false--显示 true--隐藏
#roleauthoritygrant=false
#资源组管理设备维护保存按钮隐藏--开关(默认显示) false--显示 true--隐藏
#equipmentmaintainsave=false
#用户管理角色及自动赋权列显示--开关(默认文字) false--文字 true--图标
#userrolepicture=false
#浦发待办事项标题显示--开关(默认不变) false--不变 true--修改:变更管理->应用变更,脚本服务化->自定义运维 日常操作->标准运维
#todolisttitle=false
#浦发变更发起同一系统执行过的变更单号不再显示--开关(默认false 提交任务后不需重新上传可显示) ,true提交任务后需重新上传后才可显示
#identicalversioncheck=false
#浦发用户登录默认类型显示配置,nomal代表普通用户(默认),adarea表示AD域用户,token表示AD域用户+token,hdqarea表示HDQ域用户,devarea表示DEV域用户
#pf.logintype=nomal
#浦发环境管理设置上线发起环境按钮和环境状态列隐藏--开关(默认false) false--显示 true--隐藏
#envmanagementbtnswitch=false
#浦发巡检发送邮件添加收件人和端口配置--开关(默认false) false--不变  true--添加
#mailpropertiesswitch=false
#浦发变更发起显示回退系统复选框显示--开关(默认false) false--显示  true--隐藏
#showbacksysswitch=false
#浦发信封中应用变更、自定义运维、标准运维任务清理按钮显示配置开关 false--隐藏  true--显示
#transfertohistoryswitch=false
#浦发cmdb mysql数据库连接信息配置
#pf.cmdb.mysql.user=
#pf.cmdb.mysql.password=
#pf.cmdb.mysql.url=
#浦发agent cmdb mysql数据库信息同步--开关 默认false false--无操作  true--同步
#pf.cmdb.mysql.sync.switch=false
#浦发agent cmdb mysql数据库信息同步线程启动--开关 默认false false--不启动  true--启动
#pf.cmdb.mysql.sync.start.switch=false
#浦发agent cmdb mysql数据库信息同步时间配置
#pf.cmdb.mysql.sync.start.time=00:00
#浦发agent管理页面显示cmdb相关列和按钮开关 默认false false隐藏 true 显示
#pf.cmdb.mysql.sync.show.switch=false
double.message.sas.switch=true
double.message.em.switch=true
double.message.ic.switch=true
double.message.ss.switch=true
double.message.sus.switch=true
double.message.sw.switch=true
double.message.ca.switch=true
double.message.ppa.switch=true
double.check.switch.jobscheduling=false
areauserimport=true
roleauthoritygrant=true
equipmentmaintainsave=false
userrolepicture=true
envmanagementbtnswitch=false
showbacksysswitch=true
transfertohistoryswitch=true
#登录操作的审计开关
#operlog.login.switch=false
operlog.login.switch=true


##################################################

# 通用操作审计开关。默认为false。开启需重启Server。
# audit.switch=false
audit.switch=false
# 审计时获取请求ip和mac地址的开关。默认为false。开启需重启Server。
# audit.getipmac.switch=false
audit.getipmac.switch=false
#设备分组开关
#equipment.group.switch=true
equipment.group.switch=true


##################################################

#任务查询点击菜单进入时是否加载数据
#taskquery.firstisloaddata.switch=true
#taskquery.loaddata.day=30


##################################################

#邮件服务器配置
#mail.smtp.auth=true
#mail.smtp.host=smtp.sina.com
#mail.user=<EMAIL>
#mail.password=12345678
#邮件发送人邮箱地址,该值为空或未配置时使用mail.user的值
#mail.from=<EMAIL>
#发送邮件轮询周期
#mailcycle=60
#用于控制自动发送邮件的线程是否开启
#mailswitch=true
time.window.start=00:05:00
time.window.end=00:10:00
mail.smtp.auth=true
mail.smtp.host=*************
mail.user=<EMAIL> 
mail.password=idealyjcs
mail.from=<EMAIL>
mailcycle=60
mailswitch=true


##################################################

#定时任务灾难处理开关
#need.recover.timetask=false
need.recover.timetask=false


##################################################

#兄弟节点异常执行开关
#exception.control=false
exception.control=true


##################################################

#获取agent出现异常时，是否发送消息开关
#sendAgentErrorMessFlag=false
#主页显示的url
#welcome.page.url=pandectnew.do
#巡检的重复报警信息是否写入报警日志的开关
#ic.repetitive.warning.write.log.switch=false


##################################################

#获取Agent状态信息开关
#isagentstatepool=false
#轮询Agent状态信息时间设置（分钟）
#agentStateMin=60
agentStateMin=1
#日切时间设置
#data.time.changetime=8:00
data.time.changetime=18:00
#24小时轮询线程设置
#jobnumswitch=true
jobnumswitch=true 
#监控活动接口轮询开关
#act.monitor.switch=true
act.monitor.switch=true
#用户校验开关
#user.len.switch=true
user.len.switch=false
#用户密码校验开关
#password.check
password.check=false


##################################################

#短信报警平台发送报文开关 默认false
#sms.send.switch=false
#短信报警平台发送报文ip
#sms.send.ip=
#短信报警平台发送报文port
#sms.send.port=


##################################################

#用户保存密码时是否触发校验保存功能
#checkSaveNewPassHisSwitch=true
checkSaveNewPassHisSwitch=false


##################################################

#用户登录失败时是否触发保存登录失败记录功能
#checkSaveLoginFailSwitch=true
checkSaveLoginFailSwitch=false


##################################################

#用户首次登录开关
#checkFirstLoginSwitch = true
checkFirstLoginSwitch=false
#变更分支条件开关
#ibranch.sus.switch=false
#变更双人复核启动时间开关
#startTime.sus.switch=false
startTime.sus.switch=true


##################################################

#工程上载审核配置
#jobscheduler.review=false
jobscheduler.review=false
#############################拓扑图相关功能开关S################################
save.encode=UTF-8
#save.encode=gbk
save.todb.encode=UTF-8

#tp数据转移线程开关
#topodatadump=false
topodatadump=true
#tp数据转移功能开关
#dataextract=false
dataextract=true
#tp工程级解析流程关键字
#analyCrit=_日启动,_月启动
#1流程级解析、2活动
#analyModel=2
analyModel=2
#tp数据清理
#clearswitch=false
#tp数据清理数据范围
#clearday=30
#tp数据清理执行时间
#cleartime=00:01
#tp平均耗时计算开关
#avgTimeClose=false
avgTimeClose=true
#tp平均耗时计算时间
#setTime=00:01
setTime=00:01
#tp预计时间计算开关
#actTimeCalculateThreadSwitch=false
actTimeCalculateThreadSwitch=true
#tp预计时间计算间隔时间
actTimeCalculateThreadInterval=120000
#tp预警报警开关开关
#actTimeWarn=false
actTimeWarn=true
#tp日切开关
#timeswitch=false
#tp解析显示对应层级工作流调用名称
#lastdeepcallactName=false
#tp显示报警消息开关
#topo.show.warn.mess=false
topo.show.warn.mess=true
#tp打印日志开关
#topologswitch=true
topologswitch=true
#tp DataMapping数据解析开关
#datamapp.analy=false
#tp analyExcelSwitch数据(ODS-EXCEL)解析开关
#analyExcelSwitch=true
#tp analyExcelSwitch数据special系统
#analyexcel.special.sysname=
#tp 展示特定日期拓扑图数据开关
#topo.datadate.switch=false
#tp 固定数据日期
#topo.datadate=2019-07-01
#tp jx-show里程碑数据
#topo.mile.data=sysname:actname,
#tp jx-show特定状态下活动
#topo.act.data.state=4,5,6,8,10,20
#tp 系统间大屏，页面刷新时间（秒）
#topo.systosys.refreshTime=120
#tp jz年决ut任务，是否有接管操作
#remind.task.manual.topo=false
#年终决算任务启动审核页面开关,默认值是false
# comm.yearendactask.runaudit.switch=false 
#年终决算预警线程开关,默认值是false
# comm.yearendactask.warnthread.switch=false 
#年终决算预警线程轮询时间间隔,默认值是2秒
# comm.yearendactask.warnthread.polltime=2 
#年终决算预警是否发送短信报警总开关
#comm.tp.sendwarn.msg.switch=true
#年终决算预警是否发送短信报警任务超时开关
#comm.tp.sendwarn.msg.timeout.switch=true
#年终决算预警是否发送短信报警到时未结束开关
#comm.tp.sendwarn.msg.noend.switch=true
#年终决算预警是否发送短信报警到时未开始开关
#comm.tp.sendwarn.msg.nostart.switch=true
#拓扑图-年决-数据导入中 补丁开关
#topo.sys.import.patch.switch=true
#拓扑图-年决-数据维护中 '复核人'、'超时时间'显示开关
#topo.hidden.ireviewer.itimeout=false
#拓扑图-年决-预计开始时间作为轮训运行开始时间处理判断开关
#topo.poll.exec.expdate.switch=false
#tp-年决-任务监控-是否显示复核人
#topo.reviewer.switch=false

#新增823拓扑图开关
topo.trigger.send.switch=true
topo.trigger.check.switch=true
topo.memory.interface.switch=true
#拓扑图新版样式
#topo.screen.new.page=true

#年终决算昆仑版topo超级用户配置
topo.perm.username=ideal,sa

#################拓扑图相关功能开关E################

##################################################

#活动监控背景色开关
#abnormal.switch=false
abnormal.switch=true


##################################################

#报警铃声开关
#warningPlayRingSwitch=false
warningPlayRingSwitch=true


##################################################



##################################################

#agent重试次数
#agentnum=10
#Agent集群功能开关，默认为关
#isagentcluster=false


##################################################

#福建集中监控报警开关
#fj.monitor.switch=false
#福建集中监控报警ip
#fj.monitor.ip=127.0.0.1
#福建集中监控报警端口
#fj.monitor.port=16200


##################################################

#保存报警信息到数据库开关
#add.warning.switch=false
add.warning.switch=true


##################################################

#报警级别
#alarmLevel=3
alarmLevel=3


##################################################

#基础数据收集工作流名称标识
#taskname.prefix=_E


##################################################

#################工作流操作相关功能开关E################
#快照中shell异常和UT异常处理时，开启操作说明列显示与否的开关
#opration.description.switch=false
opration.description.switch=true


##################################################

#相同工程名、相同工作流名、相同实例名的工作流不能重复启动的开关
#dateRepeatSwitch=false
dateRepeatSwitch=true


##################################################

#相同工程名、相同工作流名的工作流不能重复启动的开关
#workflow.repeat.switch=false
workflow.repeat.switch=false


##################################################

#作业调度-数据自清理线程启动开关
#clear.data.switch.ieai=false
clear.data.switch.ieai=true


##################################################

#工作流快照显示中文状态开关
#camera.cn.display.switch=false
camera.cn.display.switch=false


##################################################

#作业调度-时间区域报表计算开关
#time.area.switch.jobscheduling=false
time.area.switch.jobscheduling=true


##################################################

#作业调度-数据清理计算开关
#clear.data.switch.jobscheduling=false
clear.data.switch.jobscheduling=true


##################################################

#作业调度-数据清理计算时间
#clear.data.time.jobscheduling=00:01
clear.data.time.jobscheduling=20:00


##################################################

#作业调度-数据清理计算天数
#clear.data.day.jobscheduling=180
clear.data.day.jobscheduling=180


##################################################

#作业调度-活动平均耗时计算开关
#avgtime.switch.jobscheduling=false
avgtime.switch.jobscheduling=true


##################################################

#作业调度-活动平均耗时计算时间
#avgtime.setTime.jobscheduling=00:01
avgtime.setTime.jobscheduling=09:30


##################################################

#作业调度-活动平均耗时计算天数
#avgtime.actrun.day.jobscheduling=180
avgtime.actrun.day.jobscheduling=180


##################################################

#作业调度-平均耗时计算开关
#avgtime.everyday.switch.jobscheduling=false
avgtime.everyday.switch.jobscheduling=true


##################################################

#作业调度-计算每天平均耗时的标识
#avgtime.everyday.flag.jobscheduling=false
avgtime.everyday.flag.jobscheduling=true


##################################################

#作业调度-计算最大最小耗时的标识
#avgtime.maxmin.flag.jobscheduling=false
avgtime.maxmin.flag.jobscheduling=true


##################################################

#作业调度-基础数据收集标识
#base_data_switch_jobscheduling=false
base_data_switch_jobscheduling=true


##################################################

#作业调度-studio上载工程自动解析开关
#prj.auto.analy.switch=false
prj.auto.analy.switch=true


##################################################

#作业调度-添加UT异常任务配置
#uterrorname=异常*,error*
uterrorname=异常*,error*


##################################################

#作业调度-UT在活动监控页面显示的状态类型
#uterror.state.name=UT异常
uterror.state.name=UT_异常


##################################################

#活动描述包含字符备份类
#act.type.bak=bak
act.type.bak=备份


##################################################

#活动描述不包含字符备份类
#act.type.notbak=notbak,notbak1
act.type.notbak=处理


##################################################

#活动描述包含字符轮询类
#act.type.polling=polling
act.type.polling=轮询


##################################################

#活动描述不包含字符轮询类
#act.type.notpolling=notpolling,notpolling1
act.type.notpolling=aa


##################################################

#流程执行结束后基础数据保存 
#saveHistorySwitch=false
saveHistorySwitch=true


##################################################

#平均耗时和平均出错次数统计线线程执行开始时间 
#setConsumeTime=08:01
setConsumeTime=15:10


##################################################

#平均耗时线程控制开关 
#avgTimeFailNumSwitch=false
avgTimeFailNumSwitch=true


##################################################

#平均出错次数统计控制开关 
#failnumswitch=false
failnumswitch=true


##################################################

#平均耗时计算统计时间默认18月内 
#actRunMonth=18
actRunMonth=18


##################################################

#平均耗时计算统计月结 默认3
#monthnum=3
monthnum=3


##################################################

#平均耗时计算统计周结 默认3
#weeknum=3
weeknum=5


##################################################

#平均耗时计算统计日结 默认7
#daynum=7
daynum=7


##################################################

#平均耗时计算统计计算最少记录数 默认7
#defnum=7
defnum=1


##################################################

#日报邮件发送线程开关 
isplancheck=false


##################################################

#默认邮件发送检测时间，默认8:00
#dailychecktime=8:00
dailychecktime=15:15


##################################################

#studio活动时间超时设置开关 
#studio.timeout.switch=true
studio.timeout.switch=true


##################################################

#关键批量执行步骤统计线程开关 
#isdailycheck=false
isdailycheck=true


##################################################

#关键批量执行步骤统计数据保存开关 
#keyflowswitch=false
keyflowswitch=true


##################################################

#工作流标准输出信息保存开关 
#check.workflows.cycle=false
check.workflows.cycle=true
check.okfile.switch=true


##################################################

#增加活动基础数据存储功能开关
#act.basedata.save=false
act.basedata.save=true


##################################################

#增加业务异常自动重试开关
#actredo.thread.switch=false
actredo.thread.switch=true


##################################################



##################################################

#内蒙古集中监控报警开关
#webservicwarn.nmg.switch=false
#内蒙古集中监控报警ip
#webservicwarn.nmg.ip=**********
#内蒙古集中监控报警端口
#webservicwarn.nmg.port=8201


##################################################

#巡检设备与cmdb集成开关
#hc.computer.cmdb.integration.switch=false


##################################################

#巡检设备与cmdb集成,cmdb接口获取token地址
#hc.computer.cmdb.integration.service.get.token.url=
#巡检设备与cmdb集成,cmdb接口获取服务器数据地址
#hc.computer.cmdb.integration.service.get.data.url=
#巡检设备与cmdb集成,cmdb接口获取网络设备数据地址
#hc.computer.cmdb.integration.service.get.network.data.url=
#巡检设备与cmdb集成,cmdb接口用户名
#hc.computer.cmdb.integration.service.username=
#巡检设备与cmdb集成,cmdb接口密码
#hc.computer.cmdb.integration.service.pwd=
#巡检设备与cmdb集成,cmdb接口参数
#hc.computer.cmdb.integration.service.rememberMe=
#巡检设备与cmdb集成,请求超时时间
#hc.computer.cmdb.integration.request.timeout=3000
#巡检设备与cmdb集成,同步线程开关
#hc.computer.cmdb.integration.thread.switch=false
#巡检设备与cmdb集成,同步线程的执行频率，默认每天12点执行
#hc.computer.cmdb.integration.thread.cron=0 0 12 * * ?


##################################################

#健康巡检集中监控集成发送webservice发送报警开关
#send.webservice.ic.warning=true


##################################################

#Agent管理集成发送信息开关
#send.webservice.opm.agent=true
send.webservice.opm.agent=true


##################################################

#变更管理是否能查看启动过的流程
startbussrule.switch=false


##################################################

#主页是否开启Tab型菜单
#main.page.is.tab=false
#主页标题
#welcome.title=总览


##################################################

#发布proxywebservice开关 
#proxy.start.webservice.switch=false
proxy.start.webservice.switch=true


##################################################

#变更管理系统维护导出是否导出配对、禁用、高风险步骤列
exportColumn.switch=true


##################################################

#活动分支条件为空时触发并发或分支开关  默认为关闭时功能为分支  开关开启时功能为并发
#branch.parallelstruct.swith=false
branch.parallelstruct.swith=true


##################################################

#################自定义报表发送开关################
#自定义报表发送方案检测开关
#isreportdailcheck=true
#自定义报表发送方案检测时间 
#isreportdailchecktime=9:00
#自定义报表邮件发送开关 
#isreportdailysendcheck=true


##################################################

#江西模拟流程存储开关，配置后流程名称生效开关 
#flowname.error.switch=flow1,flow2
#江西银行ods工作流查询页面，默认首选展示只带“主流程”的关键字批量，无需前台输入 默认false 
#flowname.mainflow.switch=true
#江西银行ods工作流查询页面左侧照相机功能，流程信息列表展示页面 列表模式，运行状态所在行为绿色，异常为红色。 默认false 
#flowdetail.color.switch=true
#工作流调用，查看子流程中是否有异常，有异常则报异常，否则为运行。下差状态时，只有运行中的工作流调用需要下差。并且只有照相机的列表模式  
#flowdetail.flowcall.status.switch=true


##################################################

#菜单网段控制开关 
#net.menu.switch=false


##################################################

#判断是否浦发银行开关 
#spdb.switch=false


##################################################

#浦发银行巡检发送邮件控制开关 
#sendmail.spdb.switch=false


##################################################



##################################################

#同步测试环境excel到生产环境Ip
#susserver.ip=***********
susserver.ip=************


##################################################

#同步测试环境excel到生产环境Port
#susserver.port=8888
susserver.port=8888


##################################################

#ODS工作流查询Grid 执行UT行颜色开关
#odsFlowqueryColorsSwtich=false
odsFlowqueryColorsSwtich=true
#同步测试环境excel到生产环境出传输文件接口
#susserverupload.port=6666
susserverupload.port=6666


##################################################

#应用部署，window环境下执行bat脚本，是否使用SU.bat


##################################################

#应用部署，是否使用64编码进行加脚本关键参数
isLogKeyword64Encode=true


##################################################

#分片轮询空间对象状态的线程开关
burst_switch=true
#分片轮询日志输出开关
burst_log_swich=false


##################################################

#pub脚本环境名称参数开关
# sus.pub.param.envname.switch=false


##################################################

#应用部署，模板流程驱动线程池大小
#sus.model.poolsize=1


##################################################

#应用部署，强制终止流程是否判断开关
#sus.endCheck=false


##################################################

#变更监控图形版，活动全部未执行状态图标是否特殊显示
#sus.darkall.switch=false
sus.darkall.switch=true
################# webstudio ######################
#prj.auto.analy.switch=false
webstudio.default.project=日常维护模板_画2


##################################################

################# 采集配置 ######################
# 线程开关
# collect Thread Config
# collect.config.switch=false
collect.config.switch=true
# safefirst.collect.thread.number = 100
safefirst.collect.thread.number=2
# 按钮开关
# collect.config.switch.button=false
collect.config.switch.button=true
# 是否根据采集结果更新设备纳管时间
# collect.update.computer.icreate=false


##################################################

#中原银行报警发短信平台开关
zy.warn.switch=false
#zy.warn.switch=true 
#中原银行报警发短信平台ip
#zy.warn.ip=127.0.0.1
zy.warn.ip=***********
#中原银行报警发短信平台port
#zy.warn.port=5555
zy.warn.port=5555	


##################################################

#浦发银行AD域登录验证信息
#pf.ldap.host=127.0.0.1
#pf.ldap.port=1111
#pf.ldap.domain=domain
#浦发银行token登录验证信息
#pf.token.host=127.0.0.1
#浦发银行HDQ域登录验证信息
#pf.ldap.hdq.host=127.0.0.1
#pf.ldap.hdq.port=1111
#pf.ldap.hdq.domain=domain
#浦发银行DEV域登录验证信息
#pf.ldap.dev.host=127.0.0.1
#pf.ldap.dev.port=1111
#pf.ldap.dev.domain=domain
#浦发银行变更历史样式展示开关 (默认false) false--按原样式展示 true--按浦发样式展示
#pf.sus.flowhistory.show=false
pf.sus.flowhistory.show=false
#页面添加中文排序开关 false--排序不变 true--添加中文排序
#page.sort.zh.switch=false


##################################################

#浦发银行测试角色名称
#pf.test.role=rolename


##################################################

#浦发银行应用变更系统发布通知邮件发送开关
#pf.sus.message=false


##################################################

#浦发应用变更，系统部署失败时子流程运行中状态变为部署失败开关
#pf.sus.flowfail.state.show=false


##################################################

#浦发应用变更发起是否支持定时开关
#pf.sus.timingstart=false
pf.sus.timingstart=false


##################################################

#浦发银行butterfly查询数据库用户
#pf.getdbresults.user=username

##################################################

#浦发银行 standardtask线程数
#standardtask.threadnum=10
standardtask.threadnum=10


##################################################

#浦发银行日常操作审核新页面开启控制开关
#daily.operation.audit.spdb.switch=false
daily.operation.audit.spdb.switch=false


##################################################

#浦发银行日常操作审核新页面通过并且直接启动控制开关
#daily.operation.passandstart.spdb.switch=false
daily.operation.passandstart.spdb.switch=false


##################################################

#浦发银行日常操作执行人显示列表控制开关
#daily.operation.performuser.spdb.switch=false
daily.operation.performuser.spdb.switch=false


##################################################

#浦发银行监控日常维护任务状态监控开关
#daily.operation.passandstart.spdb.switch=false
daily.monitor.task.state.spdb.switch=true


##################################################

#浦发银行监控日常维护任务启动策略控制开关
#daily.execution.strategy.spdb.switch=false
daily.execution.strategy.spdb.switch=true


##################################################

#浦发银行 平台管理 业务系统维护，增加设备弹出窗口特殊展示（同白名单执行一致）
#opm.busisystem.maintain.equi.switch=false
#opm.busisystem.maintain.equi.switch=true


##################################################

#浦发银行日常维护审核通能，动态配置定时执行冗余时间（分），默认5分钟，且最小冗余时间为5分钟
#daily.task.autit.redundancy.num.spdb.switch=5


##################################################

#浦发银行业务系统维护绑定用户，默认不显示
#opm.buss.user.bind.switch=true


##################################################

#日常维护小信封功能的网段控制开关 true-根据网段的描述，包含生产两个字时才显示启动按钮，没有时不显示启动按钮 false-不控制 默认为false
#daily.segment.control.switch=false


##################################################

#小信封--文件下发--发双人复核的控制开关，开启后启动文件下发的双人复核功能
#double.check.fi.switch=false
double.check.fi.switch=true


##################################################

#平台管理--文件下发每日定时发送邮件给指定接收人线程的启动开关
#daily.file.everyday.mail.switch=false


#####################标准运维相关开关#############################

#标准运维，标准运维任务执行结果通知邮件控制开关
#flow.dm.mail.switch=false

#标准运维白名单取消获取用户token
standard.task.ccjj.ip.port.switch=true

##################################################

#业务系统绑定设备开开关
sys.binding.computer.flag=true


##################################################

#灾备切换预启开关
# isswitchtaskautostart=false
isswitchtaskautostart=true


##################################################

#Agent获取详情是否更新设备表中的设备名称
# isupdatecomputerlist=false


##################################################

#作业调度ODS轮训引擎开关，默认为false(ODS轮训引擎关闭)
# isOdsDrive=false


##################################################

#Agent获取详情是否调用采集方法
# isimmediatelycollection=false
#配置远程发布地址列表
#remoteiplist=**********,**********
#脚本调用轮询开关
#script.call.loop.switch = true
#异常脚本轮询次数
#exception.script.call.default.loop.count=10


##################################################

#审计功能、审计查询同时运行开关
# audit.newold.open.switch=false
audit.newold.open.switch=true


##################################################

#agent管理,略过reqid为空
# agent.manage.reqid.skip.switch=false


##################################################

#辽宁农信核心监控批处理发送ESB地址
# llnx.esbquery.host=127.0.0.1


##################################################

#辽宁农信核心监控批处理发送ESB地址端口
# llnx.esbquery.port=8080


##################################################

#辽宁农信核心监控批处理发送查询请求开关
# llnx.esbquery.switch=false


##################################################

#######脚本服务化，增加dbaas相关功能属性开关START#######
#脚本服务化--数据库云平台----项目开关--1数据库，默认0产品
#ss.project.flag=0
#用户组管理，是否显示角色配置列
#ss.usergrouprole.hidden=false
#服务管理，是否显示一级分类列
#ss.firstclass.show=true
#服务管理，是否显示二级分类列
#ss.secondclass.show=true
#服务管理，是否显示危险等级列
#ss.servicelevel.show=true
#服务管理，是否显示服务类型列
#ss.servicetype.show=false
#服务管理，数据库类型列
#ss.dbtype.show=false
#服务管理，发布人列
#ss.dbssuer.show=false
#服务维护，共享按钮
#ss.shareswitch.show=true
#服务维护，导入按钮
#ss.importswitch.show=true
#引入新样式
#bank.css.switch=blue
bank.css.switch=blue
#1:展示模版定义时服务来源我的订阅，2:显示所有服务
#ss.mysub.service.flag=1
#脚本服务化--小信封--开关(默认关闭)--资源申请
#double.message.dbaasapp.switch=false
#脚本服务化--小信封--开关(默认关闭)--采集任务周期修改
#double.message.dbaasmt.switch=false
#脚本服务化--prv启动时使用线程恢复dbaas.timetask
#dbaas.prv.recover.timetask.switch=false
#服务通道，服务定义，基本信息，是否显示启动用户
#ss.basicscript.suExecUser.show=false
#服务通道，服务定义，基本信息，是否自动订阅
#ss.basicscript.isAutoSub.show=false
#服务通道，服务定义，基本信息，是否支持手动发起
#ss.basicscript.manualStart.show=false
#服务维护，是否显示适用平台查询条件
#ss.usePlantFormswitch.show=false
#服务维护，是否显示查看下发结果按钮
#ss.queryIssueRecordswitch.show=false
#服务维护，是否显示下发按钮
#ss.sendScriptswitch.show=false
#脚本服务化--小信封--开关(默认关闭)--时间变更
#double.message.dbaasmt.switch=false
#服务维护，是否显示共享状态列
#ss.isshareswitch.show=false
#服务维护，是否显示使用次数列
#ss.useTimesswitch.show=false
#服务维护，是否显示成功率列
#ss.winTimesswitch.show=false
#服务维护，是否显示版本号列
#ss.versionswitch.show=true
#服务维护，是否显示脚本名称列
#ss.scriptNameswitch.show=false
#服务维护，是否显示服务类型列（显示服务类型就会隐藏脚本类型）
#ss.scriptTypeswitch.show=true
#服务维护，是否显示服务状态列（显示服务类型就会隐藏脚本状态）
#ss.statusswitch.show=true
#应用系统管理，是否显示脚本及作业
#ss.appSysManage.scriptInfoGridswitch.show=true
#应用系统管理，是否显示所属项目列
#ss.appSysManage.groupSswitch.show=false
#应用系统管理，是否显示同步按钮
#ss.appSysManage.syncUserGroupswitch.show=true
#应用系统管理，是否显示发布按钮
#ss.appSysManage.issueAppSysManageswitch.show=true
#应用系统管理，是否显示导入导出按钮
#ss.appSysManage.importswitch.show=false
#服务管理，是否显示表明列
#ss.tablenamecol.switch.show=false
#服务维护，是否显示服务ID号
#ss.serviceIdswitch.show=true
#服务维护，是否显示创建人
#ss.serviceIdswitch.show=true
#服务维护，是否显示修改人
#ss.serviceIdswitch.show=true
#资源纳管资源状态监控，默认false,间隔5分钟
#dbaas.resource.check.switch=false
#dbaas.resource.check.time=5
#服务化----审核job超过计划时间，是否立即执行开关 true:立即；flase:下一次
#dbaas.plantime.immediate.switch=flase
#服务化----执行是否关闭第三方连接，true关闭
#dbaas.three.conn.close.switch=flase
#服务化----采集结果入另外库开关及配置
#dbaas.conn.diff.database.switch=flase
#服务化---采集结果库开关开启后，采集结果是否存入当前库
#dbaas.conn.diff.database.basicconn.switch=flase
#db.dbaas.driver_class=oracle.jdbc.driver.OracleDriver
#db.dbaas.url=jdbc:oracle:thin:@127.0.0.1/orcl
#db.dbaas.username=testss
#db.dbaas.password=F/T1/Z+OrnpgWtKi1UFr0A==
#db.dbaas.pwd.securite.switch=true
#db.dbaas.initSize=10
#db.dbaas.maxActive=200
#db.dbaas.maxIdle=10
#db.dbaas.minIdle=8
#服务化---采集结果算法分析异常是否发送邮件开关
#dbaas.exec.automatic.analyse.abnormal.sendMail.switch=false
#dbaas.exec.manual.analyse.abnormal.sendMail.switch=false
#dbaas.execForPerson.analyse.abnormal.sendMail.switch=false
#服务化---采集结果是否调用分析算法开关
#dbaas.exec.automatic.analyse.switch=false
#dbaas.exec.manual.analyse.switch=false
#dbaas.execForPerson.analyse.switch=false
#服务化---资源保存，直接获取状态
#dbaas.resource.status.switch=false
#服务化----开关控制服务组发起只能发起一次
#dbaas.service.group.switch=false
#服务化---管理员用户ID
#dbaas.manage.userid=3
#服务化---#用户组管理，是否隐藏同步按钮
#usergroupsync=false
#服务化---#用户管理，是否隐藏部门列
#dbaas.user.department.switch=false
#服务化---#用户管理，是否隐藏证件号
#dbaas.user.identityID.switch=false
#服务化---#用户管理，是否隐藏业务赋权
#dbaas.user.grantBusiness.switch=false
#服务化---#用户管理，是否隐藏锁定按钮
#dbaas.user.lockuser.switch=false
#服务化---#用户管理，是否隐藏解锁按钮
#dbaas.user.unlockuser.switch=false
#服务化---#用户管理，是否隐藏导入导出按钮
#dbaas.user.importOrExport.switch=false
#服务化---#用户管理，是否隐藏修改时间列
#dbaas.user.modifyTime.switch=false
#服务化---#在线用户，是否隐藏批量踢出
#dbaas.onlineUsers.kikoutUsers.switch=false
#服务化---#在线用户，是否隐藏登录ID列
#dbaas.onlineUsers.loginname.switch=false
#服务化---#在线用户，是否隐藏登录ID查询条件
#dbaas.onlineUsers.loginnameSearch.switch=false
#服务化---#在线用户，是否隐藏查询按钮及用户名查询条件
#dbaas.onlineUsers.searchButton.switch=false
#服务化---#服务管理，是否只查询最高版本的数据
#dbaas.service.isSearchLastVersion.switch=false
#服务化---#服务定义--服务ID号格式
#dbaas.basicscript.serviceID.format=ID-SER-0000001
#服务化--是否隐藏服务维护中发布按钮
#ss.publishswitch.hide=false
#执行历史--是否隐藏任务名称
#ss.scriptMonitor.taskNameSwitch.show=true
#执行历史--是否隐藏计算机名称
#ss.scriptMonitor.hostNameSwitch.show=true
#服务版本查看--是否显示修改人信息
ss.scriptViewVersion.modifyUserSwitch=true
#服务版本查看--是否显示分析算法查看
#ss.scriptViewVersion.analyzeSwitch=true
#######脚本服务化，增加dbaas相关功能属性开关END#######


##################################################

#本开关控制判断是否已经登录，如果已经登录直接跳转到主页面
#login.check.session.auto.forword.switch=false


##################################################

#是否是银行备份平台
#jobscheduling.dbback.switch=false


##################################################

#备份平台执行脚本路径
#jobscheduling.dbback.script.path=/back


##################################################

#备份脚本名称
#jobscheduling.dbback.script.backname=back.sh


##################################################

#恢复脚本名称
#jobscheduling.dbback.script.recovername=restore.sh


##################################################

#恢复备份运行记录开关
#jobscheduling.switch.db.runrecord=false


##################################################

#恢复备份数据每天清理时间12:00
#dbback.cleardata.time=12:00


##################################################

#恢复备份数据清理间隔时间
#dbback.cleardata.intervaltime=35


##################################################

#数据对象扫描监控时间间隔单位（天）
#jobscheduling.dbback.dbinfo.dbscanday=7


##################################################

#数据对象扫描监控开关
#jjobscheduling.switch.dbback.dbscan=false
#######脚本服务化，相关开关START#######
################################


##################################################

#脚本服务化  我的脚本 下发功能 开关
#script.service.send.switch=false


##################################################

#脚本服务化  我的脚本 下发   线程池数量
#script.issue.threadnum=30


##################################################

#浦发需求 脚本服务化 任务执行 跳转展示最后一层结果 开关
#script.service.taskexec.show.switch=false


##################################################

#脚本发布同步开关
#ISRELEASETOPRODUCT=true


##################################################

#脚本发布同步服务器ip
#RELEASE_TO_PRODUCT_AGENT_IP=**********
#RELEASE_TO_PRODUCT_AGENT_PORT=15000
#脚本服务化登录地址配置 
#login.url=/page/loginScript.jsp


##################################################

#脚本服务化 自动同步应用系统数据开关
#app.sysmanage.autosync.switch=false
#app.sysmanage.autosync.cron=0 0 7 * * ?
#脚本服务化 发布应用系统 生产环境数据库配置
#app.sysmanage.issue.classname=com.ibm.db2.jcc.DB2Driver
#app.sysmanage.issue.url=jdbc:db2://***********:50000/entegor1
#app.sysmanage.issue.user=entegor1
#app.sysmanage.issue.password=idealinfo


##################################################

#脚本服务化 定时任务开关
#scriptservice.timer.task.switch=true
scriptservice.timer.task.switch=true
#脚本服务化 是否显示脚本组合相关内容
#scriptservice.script.flow.switch=false
scriptservice.script.flow.switch=true
#脚本服务化 全部脚本，隐藏小齿轮和附件开关
#scriptAllScriptSwitch=false
#脚本服务化 原子脚本不转换图开关
#scriptConvertFlowSwitch=false
#脚本服务化，任务申请，单号显示开关
#scriptOddNumberSwitch=false
#脚本服务化 定时任务取消开关
#scriptTimeTaskCancelSwitch=false
#脚本服务化 白名单脚本选择agent开关
#scriptWhiteAgentSwitch=false
#脚本服务化 浦发需求 普通脚本、白名单脚本只校验提醒级别的关键命令的开关
#scriptDangerCmdSwitch=false
#脚本服务化 任务申请是否显示任务来源
#scriptHideTaskFromSwitch=false
#脚本服务化 任务申请隐藏常用任务
#scriptHideCustomSwitch=false
#脚本服务化 脚本编写，预期结果默认值为空
#scriptExpectValueSwitch=false


##################################################

# For usergroup :
#   usergroup_classname=com.microsoft.jdbc.sqlserver.jdbc.SQLServerDriver
#   usergroup_url=************************************************************
#   usergroup_user=username
#   usergroup_password=password
#


##################################################

# 保留原来用户组配置信息开关 :
#   usergrouphold_flag=false


##################################################

#脚本服务化自愈开关
#scriptReviewSwitch=false


##################################################

#脚本服务化超时配置开关
script.timeout.switch=true


##################################################

#脚本服务化脚本执行超时线程开关
script.insatance.timeout.switch=false


##################################################

#脚本服务化任务申请短信验证码开关
#script.audi.code.switch=false


##################################################

#脚本服务化API校验token开关
#script.token.switch=false


##################################################

#脚本服务化执行任务中不展示白名单数据开关
#script.service.no.show.white.switch=false


##################################################

#脚本服务化校验脚本是否存在开关，默认五项唯一，否则服务名唯一
#scriptExistSwitch=false


##################################################

#脚本服务化常用任务原子脚本提交展示配置信息开关
#scriptShowConfigSwitch=false


##################################################

#脚本服务化追加执行结果开关
#scriptAddOutSwitch=false


##################################################

#脚本服务化双层复合开关
#scriptDoubleLevelSwitch=false


##################################################

#脚本服务化测试历史、执行历史是否展示脚本名称开关
#scriptNameSwitch=false


##################################################

#脚本服务化测试历史隐藏编辑开关
#scriptHideEditSwitch=false


##################################################

#脚本服务化任务申请执行人开关
#ss.double.execuser.switch=false


##################################################

#脚本服务化任务申请白名单脚本开关
#ss.whitelist.switch=false
ss.whitelist.switch=true


##################################################

#脚本服务化白名单脚本可以查看所有白名单脚本的开关，如果未开，查询不到非当前用户组的其他用户未共享的白名单脚本
#ss.whitelistScriptQuery.switch=false


##################################################

#浦发银行 脚本服务化??任务申请??增加服务器弹出窗口同白名单一致 开关
#script.task.apply.add.agent.switch=false


##################################################

#浦发银行 脚本服务化??任务执行完后发送邮件 开关
#script.taskExec.sendEmail.switch=false


##################################################

#浦发银行 脚本服务化??提交、发布、打回（任务、脚本、白名单命令）
#script.submit.sendEmail.switch=false


##################################################

#广发银行 脚本服务化-日志解析功能相关开关 执行历史 、任务申请 历史记录
#script.logAnalize.switch=false


##################################################

#脚本服务化-我的脚本，发布时，是否显示风险级别 true展示，false不展示
#script.scriptLevel.switch=false


##################################################

#用户组管理同步按钮显示--开关(默认显示) false--显示 true--隐藏
#usergroupsync=false


##################################################

#脚本服务化API启动原子脚本及常用任务时，是否等待执行结果的开关
#script.api.start.wait.result.switch=false


##################################################

#脚本服务化API启动原子脚本及常用任务时，等待执行结果时，查询结果的轮询时间，毫秒级别默认是3000，即3秒
#script.api.start.wait.result.sleep.time=3000


##################################################

# 脚本服务化-山东 执行历史，不显示活动名称开关 false：显示活动名称，true：不显示活动名称
#script.taskexec.History.showActNameswitch=false


##################################################

# 脚本服务化-广发关机维护，服务名称配置，后台会查询这两个服务名称的脚本进行任务申请
#scriptShutdownServicename=关机维护_关机检查
#scriptShutdownCheckServicename=关机维护_开机检查


##################################################

#广发脚本服务化dt集成，查看工单详情配置 sourceTag=YWZY  脚本服务化使用 为了第三方能区分请求是来自变更还是脚本服务化发起的
#dt.query.url=http://xxx.xxx.xxx.xxx:xxx/CGBBANK/zdhShowBG?prid=30631&jump=500&sourceTag=YWZY&


##################################################

#浦发银行 脚本服务化??执行历史??高权限用户配置 不配置为不启用，配置请按登录用户的用户名进行配置
#script.task.exec.history.highpermission.user=sa,ideal


##################################################

#广发银行 脚本服务化-任务申请,常用任务列表区是否隐藏一级分类，二级分类显示适用平台
#script.pageStyle.switch=false


##################################################

#广发银行 脚本服务化-常用任务，解析idata字段clob串按钮开关
#script.commonTask.analysisButton.switch=false


##################################################

#广发银行 脚本服务化-任务申请列表区是否显示使用次数
#script.usageTimes.switch=false
##########################################################脚本服务化，相关开关END###########################################################


##################################################

#巡检告警红铃铛中告警数据超过24时后自动转移到告警历史表中，开关（默认false不发送报警信息）
#hc.alarm.switch.thread.switch=false
#######钉钉集成开关BEGIN#######


##################################################

#与钉钉集成报警开关（默认false不发送报警信息）
#send.dingding.waring.switch=false


##################################################

#钉钉报警平台获取token地址


##################################################

#钉钉报警平台部门用户列表地址


##################################################

#钉钉接口发送信息地址


##################################################

#钉钉发送报警，联系人配置信息 控制开关（默认true不显示钉钉联系人配置信息）
#ding.ding.alarm.config.switch=true
#######钉钉集成相关开关END#######


##################################################

#联系人配置信息 控制开关（默认true不显示联系人配置信息）
#ic.alarm.config.switch=true
#######钉钉集成相关开关END#######
#######小铃铛配置信息 控制开关BEGIN#######


##################################################

#小铃铛配置信息 控制开关（默认false不显示新界面）
#small_bell_alarm.config.switch=false
#######小铃铛配置信息 控制开关END#######


##################################################

#巡检监控 业务系统导出 控制开关（默认true不显示导出按钮）
#hc.business.monitor.export.switch=true
#######巡检监控 业务系统导出 控制开关END#######


##################################################

#平台管理 设备维护字段控制显隐开关（默认检查项开关、应用详情、应用类型、设备描述（默认true显示配置信息）
#opm.computer.config.switch=true
#######平台管理 设备维护字段控制显隐开关 #######


##################################################

#发送短信开关
#send.message.waring.switch=false


##################################################

#联社编号
#sin.bank.mbrid = 
#接入方编号
#sin.bank.sysId = 
#机构网点编号
#sin.bank.opennode = 
#主机编号
#sin.bank.host = 
#端口号
#sin.bank.port = 


##################################################

#登录页面验证码开关 
#login.auth.code.validate.switch = true


##################################################

#应用维护-系统维护 相关操作功能是否隐藏开关 false：不隐藏，true：隐藏     默认true
#apm.config.hide.switch=true


##################################################

#角色授权给用户 false：不隐藏，true：隐藏     默认true
#user.roleauthgrant=false
#是否显示任务名称     默认true,显示
#dbaas.show.taskname=true


##################################################

#poc设备查询(false:获取agent设备,true:获取agent与纳管关联查询设备)
#poczy.equipment.config.swith=false
#日常操作启动时，选择文件true执行文件名模式，false执行ftp模式
#poczy.taskstart.select.file.swith=false


##################################################

#资源纳管日志文件列
#dbaas.res.col.hidden=true
#资源申请执行脚本路径
#dbaas.ss.exec.instance.path=/opt/
#dbaas.ss.exec.rac.path=/opt/
#dbaas.ss.exec.dataguard.path=/opt/
#dbaas.ss.exec.usr.path=/opt/
#dbaas.ss.exec.instance.path.win=/opt/
#dbaas.ss.exec.rac.path.win=/opt/
#dbaas.ss.exec.dataguard.path.win=/opt/
#dbaas.ss.exec.usr.path.win=/opt/
#资源申请实例级别，通过脚本返回连接串信息
#dbaas.ss.resapp.shell.result.switch=true
#申请记录，资源回收脚本路径
#dbaas.ss.recover.instance.path=
#dbaas.ss.recover.instance.path.win=
#dbaas.ss.recover.rac.path=
#dbaas.ss.recover.rac.path.win=
#dbaas.ss.recover.dataguard.path=
#dbaas.ss.recover.dataguard.path.win=
#dbaas.ss.recover.usr.path=
#dbaas.ss.recover.usr.path.win=


##################################################

#弹出窗口方式编辑用户管理
#user.editor.open=false


##################################################

#ASM管理功能调用线程开关
#asm.thread.swtich=false


##################################################

#ASM管理功能调用线程轮询周期
#asm.thread.polltime=60000
#ASM采集脚本路径
#dbaas.ss.asm.path=
#dbaas.ss.asm.path.win=
#服务启停-系统分类是否显示角色配置
#sas.class.hiddenrole.switch=true
#服务启停-发起页，折叠与展开开关，true关闭
#sas.start.openorclose.switch=false
#excel版变更监控操作权限控制--开关(默认显示) false--非发起人可以操作     true--非发起人不可以操作
#sus.ExcelMonitor.Control=false


##################################################

#健康巡检集中监控集成发送webservice发送报警开关
#send.webservice.ic.sd.warning=true
#集中监控报警ip
#webservicwarn.sd.ip=*************
#集中监控报警端口
#webservicwarn.sd.port=8281


##################################################

#监控巡检-巡检监控-业务系统监控 页面设备列转行开关 取值：0为原样式设备在列，1为设备在行，默认为0
#hc.monitor.business.computer.row.switch=0


##################################################

#小铃铛巡检告警超期未处理提醒邮件开关配置初始 取值：true 开启邮件提醒，false 关闭提醒
#send.hc.waring.timeout.email.switch=false


##################################################

#小铃铛巡检告警超期未处理提醒邮件轮询发送时间间隔配置初始 单位秒
#send.hc.waring.timeout.email.cycle=600
#excel版变更预启任务审核开关--开关(默认显示) false--不需要审核     true--需要审核
#sus.ExcelItsmAutoDisplay.Examine=false


##################################################

#应用维护启动页面，服务器选项卡中，步骤面板是否显示的开关(true显示;false隐藏;默认值false不显示)
#apm.show.step.swich=false


##################################################

#一致性比对启动灾备切换开关
# iscomparestartswitch=false
#变更管理判断依赖是否完成逻辑开关--开关(默认false) false--不查询历史表数据     true--查询开始表数据
#sus.validprojectandstep.flag=false


##################################################

#循环启动日常操作开关,默认值是false
# cycle.start.daily.oper.switch=false 
#每次轮训大小默认值 5
# cycle.start.daily.oper.size=5 
#轮询时间间隔 默认值 5000毫秒
#cycle.start.daily.oper.time=5000 


##################################################

#健康巡检-巡检监控-设备监控新页面展示开关配置初始 取值：true 开启新页面，false 原系统内设备监控页面
#hc.monitor.cpmonitor.new.page.switch=false


##################################################

#小铃铛巡检告警超期未处理提醒短信开关配置初始 取值：true 开启短信提醒，false 关闭短信
#send.hc.waring.timeout.sms.switch=false


##################################################

#小铃铛巡检告警超期未处理提醒短信轮询发送时间间隔配置初始 单位秒
#send.hc.waring.timeout.sms.cycle=600


##################################################

#浦发删除agent控制删除agent绑定的设备并将该设备移除所在系统开关，true为开启删除agent时删除绑定设备，false为保持原有处理逻辑。默认为true，开启删除。
#hc.del.computer.of.agent.switch=true


##################################################

#自动入组巡检设备自动克隆巡检模板及自动启动功能开关，true为开启可配置巡检系统绑定巡检模板入口，开启自动入组的巡检设备自动克隆组内模板的巡检项及启动巡检，false为不开放系统绑定巡检模板入口，不开启自动克隆和启动。默认为false，不开启本功能。

hc.businesssystem.computer.auto.cloneandstart.switch=true


##################################################

#浦发报表邮件查询是否包含五级告警开关控制，true为报表邮件中查询数据含5告警，false为不开启查询5告警，查询结果中不包含5级告警数据。默认为false，不开启。
#hc.report.mail.five.level.pf.switch=false


##################################################

##################OPM-MENU###############
#角色管理，角色有效时间配置，false:隐藏；true:显示
#role.validtime.cfg.show=false


##################################################

#锦州银行灾备系统录入，默认为false，不开启。
#switchcolumn.versiontwo=false


##################################################

#信息采集,一体化运维平台查询token所需的用户名信息
#info.collection.token.username=


##################################################

#信息采集,一体化运维平台查询token所需的密码信息
#info.collection.token.password=


##################################################

#信息采集,一体化运维平台登录用户所需的url地址
#info.collection.login.url=


##################################################

#信息采集,一体化运维平台登出用户所需的url地址
#info.collection.logout.url=


##################################################

#信息采集,信息采集向一体化运维平台发送信息采集消息记录启动开关
#info.collection.logout.url=


##################################################

#信息采集,向一体化运维平台返回上传文件执行结果信息所需的url地址
#info.collection.upload.res.url=


##################################################

#信息采集,向一体化运维平台返回信息采集后执行结果信息所需的url地址
#info.collection.upload.res.url=


##################################################

#变更管理资源组同步CMDB数据按钮显示开关,默认值是false
# sus.cmdb.sync.switch=false 


##################################################
#dbpool.removeAbandoned=true
#dbpool.removeAbandonedTimeout=180
#dbpool.logAbandoned=true
#dbpool.testOnBorrow=true
#dbpool.validationQuer=SELECT 1 FROM IDUAL

##################################################
#灾备切换-组合切换-签到通知开关
prologicSwitchSignFlag=true
excelPath=/opt


hc.check.model.agreement.zabbix.support.switch=true
hc.suppercheck.switch=true

#山东城商联盟组合方案增加添加原子步骤功能按钮，开关
entegor.step.instance.switch=true

#变更回退启动开关，省略了双人复核
direct.launch.task.sus.switch=true

#Agent获取详情是否调用采集方法-配置采集开关
isimmediatelycollection=true


#邮储自动化对接TISM开关
excelPath=/app/ideal/EntegorServer/excel/
send_deploy=true
yc.sus.send.message=true
script.psbc.itsm.switch=true
yc.sus.forbidden.version.switch=true

sus.cmdb.sync.switch=true

#proxy使用巡检开关
hc.proxy.start.webservice.switch=true
proxy.ip=*************


#############################光大采集0527版本增加开始#######################################################
# 控制前台页面显示内容，展示光大特有字样等
#product.name=ceb.unify.agent

# 紫色广发银行首页
#welcome.page.url=initCollectMonitorHome.do

# 光大主题颜色
#bank.css.switch=purple

#黑白名单控制开关 true为开启； false为关闭，默认关闭。*配置一个Server即可
#datacollect.autostoptask=false
datacollect.autostoptask=true

#数据清理控制开关 true为开启； false为关闭，默认关闭。*配置一个Server即可
datacollect.autoscleandata=true

#数据清理周期 单位毫秒 默认60000
datacollect.autoscleandata.period=3000
############################0527版本增加结束########################################################

##########################0723版本增加###########################
#当运行中的需要返回给server的任务，如果一定时间段内没有返回，则自动增加一条警告信息
datacollect.autostate=true
#datacollect.autostate=false
##监控时长,配置项为分钟数。线程会每隔该配置项配置的分钟数后，运行一次。进行数据返回校验。默认3分钟
datacollect.autostate.sleepmin=30

###################0803版本添加########################
#首页告警小铃铛开关
datacollect.alarm.switch=true

#可以通过守护进程启停agent
is.guard.opt=true

#守护线程开关
daemonsSwitch=true

#容量分析相关开关
#容量分析 系统信息采集脚本名称
shunde.capacity.cpuscript=/opt/scripts/ShunDe_Scripts/systemInfo.sh
#容量分析 系统存储采集脚本名称
shunde.capacity.storagescript=/opt/scripts/ShunDe_Scripts/checkStore.sh
#容量分析 TopN信息采集脚本名称
shunde.capacity.topscript=/opt/scripts/ShunDe_Scripts/checkTopN.sh
#容量分析 接收采集结果接口地址
shunde.capacity.returnurl=http://*************:8081/capacity/collectResult.do 
#存储采集间隔时间
shunde.capacity.storage.interval=0 0/1 * * * ?


#故障自愈-标准运维返回任务成功与失败状态，true代表开启
#故障自愈-开关返回自愈平台状态的开关，true开启
dm.selfheahing.switch=true

#故障自愈-任务超时计算线程开关，true开启，单台SERVER开启
fsh.timeout.thread.switch=false

#故障自愈-读取邮件线程开关，默认false关闭，单台SERVER开启
fsh.reademail.thread.switch=false

#故障自愈-小信封-展示开关，true开启
#double.message.fh.switch=false

#故障自愈-数据转移至历史表线程开关，单台SERVER开启
fas.datatransfer.switch=false

#故障自愈-数据转移运行时间
fas.datatransfer.runtime=08:00

#故障自愈-数据转移数据保留周期，单位：天
fas.datatransfer.retaintime=30
#使灾备切换"切换检查"变为"桌面演练"
#switch.desktop.flag=true


#关机维护-计划弹窗关闭
gfcheck.backlog.timing.switch=false

#应急模式切换
em.select.ip.model.switch=false
postal.em.showip.switch=false

#脚本标签
#sd.script.labeledit.switch=true

#制品库地址
product.warehouse.path = /media/ProLibeary

#制品库获取token地址
zpk.token.url=http://*************:8888/aoms/token/getToken.do
#制品库获取文件路径地址
zpk.platform.url=http://*************:8888/aoms/artifacts/platform.do
#制品库开关-excel页面显示补丁文件的开关
productwarehouseinfo=true

#吉林银行CI docker镜像查询地址
ci.docker.searchurl = http://$1:$2/images/json
#吉林银行CI docker镜像删除地址
ci.docker.deleteurl = http://$1:$2/images/
#吉林银行CI定时任务启动开关
ci.task.cicletiming.switch=true
#Agent增加cmdb同步开关
poc.tb.cmdb.agent=true
#查询资产信息接口
poc.assetinfo.url=http://*************:8888/aoms/cmdb/V1/queryCmdbInfoListPage.do
#查询资产信息接口类型(为模型主键)
poc.assetinfo.type=120

#巡检推送函数开关
hc.agent.funandvar.sendmessage.switch=true
#脚本任务执行历史是否显示超时
script.exec.his.timeout=true
#脚本服务化超时轮训开关
script.insatance.timeout.switch=true
#脚本任务超时后是否自动终止
script.kill.switch=false`

#灾备切换模块开关
eswitch.nmgexcel.xtlr.flag=true
nmg.managenotice.message.switch=true
#灾备场景库审核
double.message.cjk.switch=true
nmg.drillmanage.message.switch=true
nmg.screenshot.switch=true
double.message.emevent.switch=true
#em.tags.switch=true
component.manage.message.switch=true
nmg.socreplan.message.switch=true
nmg.orgmanagement.show.switch=true
#operlog.login.switch=true
double.message.emplan.switch=true
#脚本服务化最大允许并发数
each_num=1000

#新版菜单开关
new.menu.switch=false


#统一Agent采集量显示
ignoreResult=false

#开关说明entegor.config配置以下信息
#脚本服务化-脚本编写助手开关
script.edit.helper.switch=true
#脚本服务化-脚本助手服务器url地址信息
script.edit.helper.url=http://*************:5000



#应用变更图形化版本增加服务类型脚本 默认false 不显示服务脚本选择；true 显示脚本服务选择下拉选择
sus.call.script.service.switch=true


#API注册http接口调用
gd.poc.show.switch=true

#上海版本ITSM工单调用自动化显示开关
sh.paas.order.switch=true

#渤海银行统一登录配置开关
#cbhb.login.sso=true

#独立平台开关
#bh.sso.independen.operation=true 
#bh.sso.url=http://*************:8888 

#渤海日常操作-基线检查-JSON分割开关
bh.output.split.switch=true
bohaibank.batch.dm.switch=true
dm.double.check.switch=false

###############CMDB-POC-拓扑图 配置 ###############

# cmdb Poc ssh 资产关系发现类型关系名称
cmdb.ssh.discover.type.relation=TCP

# cmdb Poc ssh 资产关系发现 ，下级资产的（模型code:端口）
cmdb.ssh.discover.netstat.relation = MySQL:3306,Zookeeper:2181,Nacos:9848

# cmdb Poc 采集脚本名称
cmdb.discover.relation.script.name = netstat.sh

# cmdb Poc 采集的模型编码
cmdb.discover.relation.cmdb.type = ssh_app


################福建农信流水线CI侧开关###############
#bankswitch=FJNX
#project.order.poc.switch = true


zpk.token.url=http://*************:8888/token/getToken.do
zpk.platform.url=http://*************:8888/artifacts/platform.do
#晋级ci测配置
#CI docker镜像查询地址
#ci.docker.searchurl = http://$1:$2/images/json
#CI docker镜像删除地址
#ci.docker.deleteurl = http://$1:$2/images/
#配置CD机器ip和port
3rise.http.to.cd.url = http://*************:8888/
#cicd标识 0:CI,1:CD 空:CI
sus.cicd.flag=0

rise.task.baseurl = /opt/bePromoted

#福建农信制品库 CI侧上传文件存放到制品server路径
#product.warehouse.serverpath =/artifact/upload
#福建农信制品库 CI侧制品server地址
#product.warehouse.url = http://*************:8888/aoms
#福建农信制品库 CI侧临时存放制品路径
#product.warehouse.path =/artifact/temp
##福建农信制品库 CD侧制品Server文件存放路径
#product.warehouse.cd.serverpath =/opt/fjnx_download
##福建农信制品库 CD侧server地址
#product.warehouse.cd.url = http://*************:8888
##福建农信制品库 CD侧制品server地址
#product.warehouse.cdserver.url = http://*************:8888
#脚本服务化不用审核直接发布脚本开关
#script.cross.publish.pass=false

#fjnx spec
#fjnx.sync.system.switch=true

#############################################################

##############################系统相关配置####################################
#关闭开关解决PROC_BATCH_AVERAGE问题
sys.act.avgtime.switch=false
sys.act.avg.baseline.switch=false

##############################################################################



##############################漏洞扫描相关配置####################################

#漏扫平台赋予当前用户通过漏扫平台调用标准运维
#distributed.thread.monitor.user=ideal

#开启后漏扫和漏扫的任务才能执行，true开启
#distributed.thread.monitor.switch=false

#漏扫任务线程的循环间隔时间。单位毫秒
#distributed.thread.monitor.cycle=20000

#漏扫任务执行的serverIp及代理Ip
#distributed.thread.monitor.hostip=*************

#漏扫脚本路径 须包括执行脚本命令
#distributed.thread.monitor.python.patch=/usr/python3/bin/python3 /aoms/Nessus/autoNessus.py

# 异步任务开关，用于支持漏扫所需的简易线性任务驱动
#asynchrony.runtime.switch=true

##############################################################################

screenflag=true
eswitch.excel.nmnx.flag=true
em.systemin.scene.notnull.valid.switch=false

#作业调度日启动支持配置cron表达式
dg.project.param.switch=true




#server连接拓扑图ip

dg.unified.http_port.url=*************:3000

#server推送数据ip例:http://127.0.0.1:8888/

drcb.topologic.http.url=http://*************:8360/

#登录统一门户，是否需要追加id前缀

dg.unified.certification.login.addid.switch=false

#excel作业增加参数和是否循环开关
dgbank.batch.switch=false

#主线定时配置excel表支持crontab表达式功能
#daily.execution.strategy.spdb.switch=true
ql.overtime.formap.switch=false
avgTimeWarnningSwitch=true

bohaibank.cross.main.line=true
analyExcelSwitch=true


qlwarn.update.intertime=1
runtime.alarm.cfg.switch=true
cz.permission.switch=true

ieai.excel.save.version.switch=true
cz.promotion.ip=127.0.0.1
standard.code.validation.switch=true
ieai.savewarning.switch=true