
DELIMITER ;;
DROP PROCEDURE IF EXISTS UPPRDE;;
create PROCEDURE UPPRDE()
BEGIN

    IF NOT EXISTS ( SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'IEAI_EXCELMODEL_COPY' AND COLUMN_NAME = 'IJOBTYPE' ) THEN
        alter table IEAI_EXCELMODEL_COPY add column IJOBTYPE VARCHAR(255) default null;
    END IF;

    IF NOT EXISTS ( SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'IEAI_EXCELMODEL' AND COLUMN_NAME = 'IJOBTYPE' ) THEN
        alter table IEAI_EXCELMODEL add column IJOBTYPE VARCHAR(255) default null;
    END IF;

    IF NOT EXISTS ( SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'ieai_act_timeoutconfig' AND COLUMN_NAME = 'ITASK_TWOTIMEOUTNUM' ) THEN
        ALTER TABLE ieai_act_timeoutconfig ADD COLUMN ITASK_TWOTIMEOUTNUM DECIMAL(19,2) DEFAULT NULL;
    END IF;

    IF NOT EXISTS ( SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'ieai_act_timeoutconfig' AND COLUMN_NAME = 'ITASK_MINTIMEOUTNUM' ) THEN
        ALTER TABLE ieai_act_timeoutconfig ADD COLUMN ITASK_MINTIMEOUTNUM DECIMAL(19,2) DEFAULT NULL;
    END IF;

END;;
DELIMITER ;
CALL UPPRDE();

DELIMITER ;;
DROP PROCEDURE IF EXISTS UPPRDE;;
CREATE PROCEDURE UPPRDE()
BEGIN
CREATE TABLE IF NOT EXISTS IEAI_PRJACT_AGENTINFO (IID NUMERIC(19) NOT NULL,IPRJID NUMERIC(19),IPRJNAME VARCHAR(255),IWORKFLOWNAME VARCHAR(255),IACTNAME VARCHAR(255),IRESOURCENAME VARCHAR(255), IAGENTIP VARCHAR(255), IAGENTPORT NUMERIC(5), IGROUPNAME VARCHAR(255),CONSTRAINT PK_IEAI_PRJACT_AGENTINFO PRIMARY KEY(IID));

CREATE TABLE IF NOT EXISTS IEAI_JOB_CONFIG (IID NUMERIC(19) NOT NULL,IPARAMNAME VARCHAR(255),IPARAMCODE VARCHAR(255),IISENCRYPT NUMERIC(1),IPARAMVALUE VARCHAR(1000),IPRJNAME VARCHAR(255),CONSTRAINT PK_IEAI_JOB_CONFIG PRIMARY KEY (IID));

    IF NOT EXISTS ( SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'IEAI_DELAYER_ACTIVITY' AND COLUMN_NAME = 'IFLOWID' ) THEN
        ALTER TABLE IEAI_DELAYER_ACTIVITY MODIFY COLUMN IFLOWID NUMERIC(19) NOT NULL;
    END IF;

    IF NOT EXISTS ( SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'IEAI_ACTMONITOR' AND COLUMN_NAME = 'ITYPE' ) THEN
        ALTER TABLE IEAI_ACTMONITOR ADD COLUMN ITYPE DECIMAL(1) DEFAULT 0;
    END IF;

    IF NOT EXISTS ( SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'IEAI_ACTMONITOR_RUN' AND COLUMN_NAME = 'ITYPE' ) THEN
        ALTER TABLE IEAI_ACTMONITOR_RUN ADD COLUMN ITYPE DECIMAL(1) DEFAULT 0;
    END IF;

    IF NOT EXISTS ( SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'IEAI_ACTMONITOR_BAK' AND COLUMN_NAME = 'ITYPE' ) THEN
        ALTER TABLE IEAI_ACTMONITOR_BAK ADD COLUMN ITYPE DECIMAL(1) DEFAULT 0;
    END IF;

    IF NOT EXISTS ( SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'IEAI_CUSTOM_REPORT_DATASOURCE' AND COLUMN_NAME = 'IMAXCONNECT' ) THEN
        ALTER TABLE IEAI_CUSTOM_REPORT_DATASOURCE ADD COLUMN IMAXCONNECT INT;
    END IF;

CREATE TABLE IF NOT EXISTS IEAI_CRITICAL_PATH_NEW (
    IID BIGINT NOT NULL AUTO_INCREMENT,
    PATH_ID VARCHAR(64) NOT NULL,
    PATH_NAME VARCHAR(200) NOT NULL,
    PATH_DESCRIPTION VARCHAR(1000),
    START_PROJECT VARCHAR(100) NOT NULL,
    START_ACTIVITY VARCHAR(100) NOT NULL,
    END_PROJECT VARCHAR(100) NOT NULL,
    END_ACTIVITY VARCHAR(100) NOT NULL,
    TOTAL_TIME NUMERIC(10,2) DEFAULT 0.00,
    PATH_NODES LONGTEXT,
    QUERY_DATE VARCHAR(10) NOT NULL,
    CREATE_TIME NUMERIC(19),
    CONSTRAINT PK_IEAI_CRITICAL_PATH_NEW PRIMARY KEY (IID)
    );

CREATE TABLE IF NOT EXISTS IEAI_CRITICAL_PATH_STATUS (
    IID NUMERIC(19) NOT NULL,
    START_PROJECT VARCHAR(255) NOT NULL,
    START_ACTIVITY VARCHAR(255) NOT NULL,
    END_PROJECT VARCHAR(255) NOT NULL,
    END_ACTIVITY VARCHAR(255) NOT NULL,
    CURRENT_PATH_ID VARCHAR(255) NOT NULL,
    QUERY_DATE VARCHAR(20) NOT NULL,
    CREATE_TIME NUMERIC(19) NOT NULL,
    UPDATE_TIME NUMERIC(19) NOT NULL,
    CONSTRAINT PK_IEAI_CRITICAL_PATH_STATUS PRIMARY KEY (IID)
);

CREATE TABLE IF NOT EXISTS ieai_data_date_flop (
    IID decimal(19,0) NOT NULL,
    ISYSTEM_NAME varchar(255) DEFAULT NULL,
    IDATA_DATE decimal(8,0) DEFAULT NULL,
    IUPDATE_TIME decimal(19,0) DEFAULT NULL,
    ICALL_TIME decimal(19,0) DEFAULT NULL,
    IOFFSET varchar(50) DEFAULT NULL,
    IBUSINESS_DATE varchar(100)  DEFAULT NULL,
    CONSTRAINT PK_IEAI_DATA_DATE_FLOP PRIMARY KEY (IID)
    )

CREATE TABLE IF NOT EXISTS drill_results (
    ID VARCHAR(50) COMMENT '主键ID',
    PROJECT_NAME VARCHAR(100) NOT NULL COMMENT '工程名称',
    ACTIVITY_NAME VARCHAR(100) NOT NULL COMMENT '活动名称',
    TOPO_LEVEL VARCHAR(20) NOT NULL COMMENT 'topo显示层数',
    DATA_DATE DATE NOT NULL COMMENT '数据日期',
    SAVE_TIME NUMERIC(19) DEFAULT 0 COMMENT '保存时间',
    REMARK LONGTEXT COMMENT '备注信息',
    ORIGINAL_XML LONGTEXT NOT NULL COMMENT '完整XML数据',
    DRILL_CONFIG LONGTEXT NOT NULL COMMENT '演练配置JSON字符串',
    NODE_COUNT INT DEFAULT 0 COMMENT '节点数量',
    EXCEPTION_COUNT INT DEFAULT 0 COMMENT '异常节点数量',
    ALARM_COUNT INT DEFAULT 0 COMMENT '告警节点数量',
    CREATED_BY VARCHAR(50) COMMENT '创建人',
    CREATED_TIME NUMERIC(19) DEFAULT 0 COMMENT '创建时间',
    UPDATED_TIME NUMERIC(19) DEFAULT 0 COMMENT '更新时间',
    scheme_name VARCHAR(100) NOT NULL DEFAULT '未命名方案',
    CONSTRAINT PK_DRILL_RESULTS PRIMARY KEY (ID)
    );


CREATE TABLE IF NOT EXISTS ieai_leader_election (
    iid int(11) NOT NULL,
    ileader_id varchar(60)  NOT NULL,
    ilast_heartbeat bigint(20) NOT NULL,
    iversion bigint(20) NOT NULL DEFAULT '0',
    iserver_ip varchar(45) NOT NULL DEFAULT '',
    PRIMARY KEY (iid)
);

CREATE TABLE IF NOT EXISTS ieai_message_contents (
    iid bigint(20) NOT NULL AUTO_INCREMENT,
    icontent text NOT NULL,
    PRIMARY KEY (iid)
);

CREATE TABLE IF NOT EXISTS ieai_messages (
    iid bigint(20) NOT NULL AUTO_INCREMENT,
    istatus varchar(20)   NOT NULL DEFAULT 'PENDING',
    ibiz_types varchar(200)  NOT NULL,
    icreated_at bigint(20) NOT NULL,
    iprocessed_at bigint(20) DEFAULT NULL,
    PRIMARY KEY (iid),
    KEY idx_status (istatus),
    KEY idx_biz_type (ibiz_types),
    KEY icreated_at (icreated_at,istatus)
);

IF NOT EXISTS (SELECT * FROM ieai_leader_election D WHERE D.iid=1) THEN
    INSERT INTO ieai_leader_election VALUES (1,'e3188de1-0909-4a24-a4be-5efb2a6b07be',1755333171389,113,'*************');
END IF;

CREATE TABLE IF NOT EXISTS TEMP_LOOKMONITOR_CHECKFILE(pid int PRIMARY KEY  NOT NULL AUTO_INCREMENT,IID DECIMAL(19,0) ,IFLOWID DECIMAL(19),IFLOWOWNID DECIMAL(19),IFLOWUPPERID DECIMAL(19),IPRJID DECIMAL(19),IPRJUPPERID DECIMAL(19),IPRJNAME VARCHAR(255),IFLOWNAME VARCHAR(255),ISTARTTIME DECIMAL(19),IENDTIME DECIMAL(19),ISTATUS DECIMAL(19),IDATADATE VARCHAR(255),IHOUR DECIMAL(2),IEXCEPTION DECIMAL(1),IEXCEPTIONTIME DECIMAL(19),IRESTARTTIME DECIMAL(19),IOKFILEABSOLUTEPATH VARCHAR(255),IOKFILEFINDWEEK VARCHAR(255),IAGENTIP VARCHAR(255));

DROP TABLE TEMP_ACTAVGTIME_QUERY;
CREATE TABLE IF NOT EXISTS TEMP_ACTAVGTIME_QUERY(IID INT(19) NOT NULL AUTO_INCREMENT , opt_id DECIMAL(19),  acttype VARCHAR(255),  prjflowact_name VARCHAR(255),  countnum DECIMAL(19),CONSTRAINT PK_TEMP_ACTAVGTIME_QUERY PRIMARY KEY (IID)) ;

CREATE TABLE IF NOT EXISTS IEAI_JOB_LABEL (IID NUMERIC(19) NOT NULL,INAME VARCHAR(100),ICREATEUSER  VARCHAR(100), ICREATETIME NUMERIC(19),IMODIFYUSER VARCHAR(100),IMODIFYTIME NUMERIC(19),CONSTRAINT PK_IEAI_JOB_LABEL PRIMARY KEY (IID));

CREATE TABLE IF NOT EXISTS IEAI_STANDARD_CODE (
    IID BIGINT NOT NULL AUTO_INCREMENT,  -- 使用AUTO_INCREMENT自动生成ID
    ISTANDARDCODE VARCHAR(50) NOT NULL,   -- 贯标代码（英文唯一）
    BUSINESS_SYSTEM_NAME VARCHAR(255) NOT NULL, -- 业务系统中文名（唯一）
    ICREATETIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    ICREATEUSER VARCHAR(255),             -- 创建用户
    CONSTRAINT PK_IEAI_STANDARD_CODE PRIMARY KEY (IID),
    CONSTRAINT UK_IEAI_STANDARD_CODE UNIQUE (ISTANDARDCODE),
    CONSTRAINT UK_IEAI_STANDARD_CODE_NAME UNIQUE (BUSINESS_SYSTEM_NAME)
    );

IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_DATA_DATE_FLOP' AND COLUMN_NAME = 'ICUT_TIME') THEN
    ALTER TABLE ieai_data_date_flop ADD COLUMN icut_time VARCHAR(10) DEFAULT NULL;
END IF;

CREATE TABLE IF NOT EXISTS IEAI_DOMAIN_MANAGEMENT(IID NUMERIC(19) NOT NULL,INAME VARCHAR(255),IDESC VARCHAR(255),ICREATORID NUMERIC(19),ICREATORNAME VARCHAR(255),ICREATETIME NUMERIC(19),CONSTRAINT PK_IEAI_DOMAIN_MANAGEMENT PRIMARY KEY (IID));

IF NOT EXISTS ( SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'ieai_serverlist' AND COLUMN_NAME = 'IDOMAINID' ) THEN
    ALTER TABLE ieai_serverlist ADD IDOMAINID decimal(19,0) NULL COMMENT '域id';
END IF;

IF NOT EXISTS ( SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'ieai_project' AND COLUMN_NAME = 'IDOMAINID' ) THEN
    ALTER TABLE ieai_project ADD IDOMAINID decimal(19,0) NULL COMMENT '域id';
END IF;

IF NOT EXISTS (SELECT * FROM IEAI_MENU WHERE  IID=1000461) THEN
    INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(1000461, '执行域管理', 100, 'initDomainManagementPage.do', 9, '平台配置', 'images/info24.png');
END IF;

IF NOT EXISTS (SELECT * FROM IEAI_MENU WHERE  IID=1000461) THEN
    INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(1000461, '执行域管理', 100, 'initDomainManagementPage.do', 9, '平台配置', 'images/info24.png');
END IF;

IF NOT EXISTS (SELECT * FROM IEAI_MENU WHERE  IID=50007) THEN
    INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG, AUDITENBLE, IISAPP) VALUES(50007, '贯标管理', 1, 'standardCodeManage.do', 1, '', 'null', 0, 0);
END IF;

IF NOT EXISTS (SELECT * FROM IEAI_MENU WHERE  IID=50001) THEN
    INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG, AUDITENBLE, IISAPP) VALUES(50001, '翻牌', 1, 'dataDateFlop.do', 1, '', 'null', 0, 0);
END IF;

END;;
DELIMITER ;
CALL UPPRDE();


