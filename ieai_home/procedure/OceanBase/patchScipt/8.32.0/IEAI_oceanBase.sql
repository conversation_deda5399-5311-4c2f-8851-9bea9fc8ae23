DELIMITER ;;
DROP PROCEDURE IF EXISTS UPPRDE;;
CREATE PROCEDURE UPPRDE()
BEGIN
		IF NOT EXISTS (SELECT * FROM IEAI_MENU  WHERE IID=11117) THEN
INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(11117, '业务系统查询', 1, 'businessSystemQuery.do', '60', '', 'images/info165.png');
END IF;
END;;
DELIMITER ;
CALL UPPRDE();

DELIMITER ;;
DROP PROCEDURE IF EXISTS UPPRDE;;
CREATE PROCEDURE UPPRDE()
BEGIN
CREATE TABLE IF NOT EXISTS IEAI_SYSALARM_LEVEL_DG(IID NUMERIC(19) NOT NULL,IPRJNAME VARCHAR(255),IFLOWNAME VARCHAR(255),IACTNAME  VARCHAR(255),ILEVEL VARCHAR(10),CONSTRAINT PK_IEAI_SYSALARM_LEVEL_DG PRIMARY KEY (IID));
END;;
DELIMITER ;
CALL UPPRDE();

DELIMITER ;;
DROP PROCEDURE IF EXISTS UPPRDE;;
CREATE PROCEDURE UPPRDE()
BEGIN
		IF NOT EXISTS (SELECT * FROM IEAI_MENU  WHERE IID=11116) THEN
INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(11116, '报警级别配置', 1, 'sysAlarmLevelDg.do', '59', '配置', 'images/info165.png');
END IF;
END;;
DELIMITER ;
CALL UPPRDE();

DELIMITER ;;
DROP PROCEDURE IF EXISTS UPPRDE;;
CREATE PROCEDURE UPPRDE()
BEGIN
CREATE TABLE IF NOT EXISTS IEAI_EXCELMODEL_MUST_PARAM (IID INT (19) NOT NULL auto_increment primary key,IPROJECTNAME VARCHAR(255),IISREQUIRED VARCHAR(10));
END;;
DELIMITER ;
CALL UPPRDE();

DELIMITER ;;
DROP PROCEDURE IF EXISTS UPPRDE;;
CREATE PROCEDURE UPPRDE()
BEGIN

    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_PROJECT_INFO' AND COLUMN_NAME = 'ISHANDSTART') THEN
ALTER TABLE IEAI_PROJECT_INFO ADD ISHANDSTART VARCHAR(200) ;
END IF;
END;;
DELIMITER ;
CALL UPPRDE();

DELIMITER ;;
DROP PROCEDURE IF EXISTS UPPRDE;;
CREATE PROCEDURE UPPRDE()
BEGIN
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'ieai_excelnodel_version' AND COLUMN_NAME = 'IUPLOADTIME') THEN
ALTER TABLE ieai_excelnodel_version ADD IUPLOADTIME varchar(255);
END IF;
END;;
DELIMITER ;
CALL UPPRDE();

DELIMITER ;;
DROP PROCEDURE IF EXISTS UPPRDE;;
CREATE PROCEDURE UPPRDE()
BEGIN
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'ieai_excelnodel_version' AND COLUMN_NAME = 'IUPLOADUSER') THEN
ALTER TABLE ieai_excelnodel_version ADD IUPLOADUSER varchar(255);
END IF;
END;;
DELIMITER ;
CALL UPPRDE();

DELIMITER ;;
DROP PROCEDURE IF EXISTS UPPRDE;;
CREATE PROCEDURE UPPRDE()
BEGIN
    IF NOT EXISTS (SELECT * FROM IEAI_MENU  WHERE IID=1085) THEN
        INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(1085, 'ODS监控',1, 'excelActMonitorIndex.do', 6, '', 'images/info88.png');
END IF;

    IF NOT EXISTS (SELECT * FROM IEAI_HIGHOPER  WHERE IBUTTONID=1132) THEN
        INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(1132, '批量重试', 'queryHomeData.do', '作业调度-ODS监控-批量重试');
END IF;
    IF NOT EXISTS (SELECT * FROM IEAI_HIGHOPER  WHERE IBUTTONID=1133) THEN
        INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(1133, '批量略过', 'queryHomeData.do', '作业调度-ODS监控-批量略过');
END IF;
    IF NOT EXISTS (SELECT * FROM IEAI_HIGHOPER  WHERE IBUTTONID=1134) THEN
        INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(1134, '暂停', 'flowQuery/pauseProject.do', '作业调度-ODS监控-暂停');
END IF;
    IF NOT EXISTS (SELECT * FROM IEAI_HIGHOPER  WHERE IBUTTONID=1135) THEN
        INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(1135, '终止', 'flowQuery/killFlow.do', '作业调度-ODS监控-终止');
END IF;
    IF NOT EXISTS (SELECT * FROM IEAI_HIGHOPER  WHERE IBUTTONID=1136) THEN
        INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(1136, '恢复', 'flowQuery/cancelPauseProject.do', '作业调度-ODS监控-恢复');
END IF;
    IF NOT EXISTS (SELECT * FROM IEAI_HIGHOPER  WHERE IBUTTONID=1137) THEN
        INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(1137, '重试是否进行双人复核', '-', '作业调度-ODS监控-重试是否进行双人复核');
END IF;

    IF NOT EXISTS (SELECT * FROM IEAI_MENU_BUTTON  WHERE IMENUBUTTONID=1132) THEN
        INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(1132, 1085, 1132, '');
END IF;
    IF NOT EXISTS (SELECT * FROM IEAI_MENU_BUTTON  WHERE IMENUBUTTONID=1133) THEN
        INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(1133, 1085, 1133, '');
END IF;
    IF NOT EXISTS (SELECT * FROM IEAI_MENU_BUTTON  WHERE IMENUBUTTONID=1134) THEN
        INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(1134, 1085, 1134, '');
END IF;
    IF NOT EXISTS (SELECT * FROM IEAI_MENU_BUTTON  WHERE IMENUBUTTONID=1135) THEN
        INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(1135, 1085, 1135, '');
END IF;
    IF NOT EXISTS (SELECT * FROM IEAI_MENU_BUTTON  WHERE IMENUBUTTONID=1136) THEN
        INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(1136, 1085, 1136, '');
END IF;
    IF NOT EXISTS (SELECT * FROM IEAI_MENU_BUTTON  WHERE IMENUBUTTONID=1137) THEN
        INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(1137, 1085, 1137, '');
END IF;



END;;
DELIMITER ;
CALL UPPRDE();

DELIMITER ;;
DROP PROCEDURE IF EXISTS UPPRDE;;
CREATE PROCEDURE UPPRDE()
BEGIN
CREATE TABLE IF NOT EXISTS IEAI_SYSALARM_LEVEL_DG(IID NUMERIC(19) NOT NULL,IPRJNAME VARCHAR(255),IFLOWNAME VARCHAR(255),IACTNAME  VARCHAR(255),ILEVEL VARCHAR(10),CONSTRAINT PK_IEAI_SYSALARM_LEVEL_DG PRIMARY KEY (IID));
END;;
DELIMITER ;
CALL UPPRDE();

DELIMITER ;;
DROP PROCEDURE IF EXISTS UPPRDE;;
CREATE PROCEDURE UPPRDE()
BEGIN
		IF NOT EXISTS (SELECT * FROM IEAI_MENU  WHERE IID=11116) THEN
INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(11116, '报警级别配置', 1, 'sysAlarmLevelDg.do', '59', '配置', 'images/info165.png');
END IF;
END;;
DELIMITER ;
CALL UPPRDE();

DELIMITER ;;
DROP PROCEDURE IF EXISTS UPPRDE;;
CREATE PROCEDURE UPPRDE()
BEGIN
CREATE TABLE IF NOT EXISTS IEAI_EXCELMODEL_MUST_PARAM (IID INT (19) NOT NULL auto_increment primary key,IPROJECTNAME VARCHAR(255),IISREQUIRED VARCHAR(10));
END;;
DELIMITER ;
CALL UPPRDE();

DELIMITER ;;
DROP PROCEDURE IF EXISTS UPPRDE;;
CREATE PROCEDURE UPPRDE()
BEGIN

    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_PROJECT_INFO' AND COLUMN_NAME = 'ISHANDSTART') THEN
ALTER TABLE IEAI_PROJECT_INFO ADD ISHANDSTART VARCHAR(200) ;
END IF;
END;;
DELIMITER ;
CALL UPPRDE();

DELIMITER ;;
DROP PROCEDURE IF EXISTS UPPRDE;;
CREATE PROCEDURE UPPRDE()
BEGIN
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'ieai_excelnodel_version' AND COLUMN_NAME = 'IUPLOADTIME') THEN
ALTER TABLE ieai_excelnodel_version ADD IUPLOADTIME varchar(255);
END IF;
END;;
DELIMITER ;
CALL UPPRDE();

DELIMITER ;;
DROP PROCEDURE IF EXISTS UPPRDE;;
CREATE PROCEDURE UPPRDE()
BEGIN
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'ieai_excelnodel_version' AND COLUMN_NAME = 'IUPLOADUSER') THEN
ALTER TABLE ieai_excelnodel_version ADD IUPLOADUSER varchar(255);
END IF;

    IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'ieai_supdata' AND COLUMN_NAME = 'SYSTEM') THEN
        ALTER TABLE ieai_supdata CHANGE `SYSTEM` ISYSTEM VARCHAR(255);
END IF;
END;;
DELIMITER ;
CALL UPPRDE();

DELIMITER ;;
DROP PROCEDURE IF EXISTS UPPRDE;;
CREATE PROCEDURE UPPRDE()
BEGIN
    IF NOT EXISTS (SELECT * FROM IEAI_MENU  WHERE IID=1085) THEN
        INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(1085, 'ODS监控',1, 'excelActMonitorIndex.do', 6, '', 'images/info88.png');
END IF;

    IF NOT EXISTS (SELECT * FROM IEAI_HIGHOPER  WHERE IBUTTONID=1132) THEN
        INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(1132, '批量重试', 'queryHomeData.do', '作业调度-ODS监控-批量重试');
END IF;
    IF NOT EXISTS (SELECT * FROM IEAI_HIGHOPER  WHERE IBUTTONID=1133) THEN
        INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(1133, '批量略过', 'queryHomeData.do', '作业调度-ODS监控-批量略过');
END IF;
    IF NOT EXISTS (SELECT * FROM IEAI_HIGHOPER  WHERE IBUTTONID=1134) THEN
        INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(1134, '暂停', 'flowQuery/pauseProject.do', '作业调度-ODS监控-暂停');
END IF;
    IF NOT EXISTS (SELECT * FROM IEAI_HIGHOPER  WHERE IBUTTONID=1135) THEN
        INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(1135, '终止', 'flowQuery/killFlow.do', '作业调度-ODS监控-终止');
END IF;
    IF NOT EXISTS (SELECT * FROM IEAI_HIGHOPER  WHERE IBUTTONID=1136) THEN
        INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(1136, '恢复', 'flowQuery/cancelPauseProject.do', '作业调度-ODS监控-恢复');
END IF;
    IF NOT EXISTS (SELECT * FROM IEAI_HIGHOPER  WHERE IBUTTONID=1137) THEN
        INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(1137, '重试是否进行双人复核', '-', '作业调度-ODS监控-重试是否进行双人复核');
END IF;

    IF NOT EXISTS (SELECT * FROM IEAI_MENU_BUTTON  WHERE IMENUBUTTONID=1132) THEN
        INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(1132, 1085, 1132, '');
END IF;
    IF NOT EXISTS (SELECT * FROM IEAI_MENU_BUTTON  WHERE IMENUBUTTONID=1133) THEN
        INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(1133, 1085, 1133, '');
END IF;
    IF NOT EXISTS (SELECT * FROM IEAI_MENU_BUTTON  WHERE IMENUBUTTONID=1134) THEN
        INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(1134, 1085, 1134, '');
END IF;
    IF NOT EXISTS (SELECT * FROM IEAI_MENU_BUTTON  WHERE IMENUBUTTONID=1135) THEN
        INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(1135, 1085, 1135, '');
END IF;
    IF NOT EXISTS (SELECT * FROM IEAI_MENU_BUTTON  WHERE IMENUBUTTONID=1136) THEN
        INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(1136, 1085, 1136, '');
END IF;
    IF NOT EXISTS (SELECT * FROM IEAI_MENU_BUTTON  WHERE IMENUBUTTONID=1137) THEN
        INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(1137, 1085, 1137, '');
END IF;
END;;
DELIMITER ;
CALL UPPRDE();