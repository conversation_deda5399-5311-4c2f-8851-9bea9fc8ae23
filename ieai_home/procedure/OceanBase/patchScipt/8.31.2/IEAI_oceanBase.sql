
DELIMITER ;;
DROP PROCEDURE IF EXISTS UPPRDE;;
CREATE PROCEDURE UPPRDE()
BEGIN
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_EXCELMODEL_DAYSTART' AND COLUMN_NAME = 'ICRONTAB') THEN
ALTER TABLE IEAI_EXCELMODEL_DAYSTART ADD ICRONTAB VARCHAR(255)  ;
END IF;
END;;
DELIMITER ;
CALL UPPRDE();


DELIMITER ;;
DROP PROCEDURE IF EXISTS UPPRDE;;
CREATE PROCEDURE UPPRDE()
BEGIN
CREATE TABLE IF NOT EXISTS IEAI_EXCEl_CRON_JOB(IID NUMERIC(19) NOT NULL,IPRJNAME VARCHAR(255),IFLOWNAME VARCHAR(255),ICALLPRONAME  VARCHAR(255),ICALLMAINFLOW  VARCHAR(255),<PERSON>CR<PERSON><PERSON><PERSON> VARCHAR(255),IUSERNAME VARCHAR(255),<PERSON><PERSON><PERSON><PERSON> NUMERIC(19),IINSNAME VARCHAR(50),<PERSON><PERSON><PERSON><PERSON>ME NUMERIC(19),IIP VARCHAR(50) ,CONSTRAINT PK_IEAI_EXCEl_CRON_JOB PRIMARY KEY (IID));
END;;
DELIMITER ;
CALL UPPRDE();



